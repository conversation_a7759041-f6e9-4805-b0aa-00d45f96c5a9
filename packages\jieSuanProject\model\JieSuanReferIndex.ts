/**
 *
 * 参考指标设置
 */
export class JieSuanReferIndex{

    /**
     * 工程类型
     */
    public projectType: string;
    /**
     *  建筑分类:
     */
    public buildType: string;
    /**
     * 造价类别:
     */
    public costType: string;
    /**
     * 结构类型:
     */
    public strType: string;

    /**
     * 编制时间:
     */
    public madeTime: string;

    /**
     * 建设单位:
     */
    public buildUnit: string;

    /**
     * 计算口径:
     */
    public indexCountCaliber: string;

    /**
     * 建筑高度:
     */
    public buildHeight: string;

    constructor(projectType: string, buildType: string, costType: string, strType: string, madeTime: string, buildUnit: string, indexCountCaliber: string, buildHeight: string) {
        this.projectType = projectType;
        this.buildType = buildType;
        this.costType = costType;
        this.strType = strType;
        this.madeTime = madeTime;
        this.buildUnit = buildUnit;
        this.indexCountCaliber = indexCountCaliber;
        this.buildHeight = buildHeight;
    }
}