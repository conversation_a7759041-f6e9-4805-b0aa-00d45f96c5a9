<!--
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2024-03-05 16:53:07
 * @LastEditors: liuxia
 * @LastEditTime: 2024-09-18 11:35:45
-->
<template>
  <div class="risk-range">
    <common-modal
      className="dialog-comm resizeClass"
      title="风险幅度范围"
      width="700"
      height="200"
      v-model:modelValue="props.riskVisible"
      :mask="false"
      @close="cancel"
    >
      <div class="range-content">
        风险幅度范围：-<a-input
          v-model:value="formData.riskAmplitudeRangeMin"
          v-integer
        />
        % ~
        <a-input v-model:value="formData.riskAmplitudeRangeMax" v-integer />
        %
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button
          type="primary"
          :disabled="
            !formData.riskAmplitudeRangeMin || !formData.riskAmplitudeRangeMax
          "
          @click="sureHandle"
          >确定</a-button
        >
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import api from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail.js';
import { ref, watch } from 'vue';
import { useDecimalPoint } from '@/hooks/useDecimalPoint';
const { rateFormat } =
    useDecimalPoint();
const props = defineProps([
  'riskVisible',
  'selectData',
  'currentInfo',
  'isEditCurrentData',
]);
const emits = defineEmits([
  'update:riskVisible',
  'updateData',
  'updateCurrentInfo',
]);
const projectStore = projectDetailStore();
const formData = ref({
  riskAmplitudeRangeMax: projectStore.currentTreeInfo?.riskAmplitudeRangeMax,
  riskAmplitudeRangeMin: Math.abs(
    projectStore.currentTreeInfo?.riskAmplitudeRangeMin
  ),
});
const VInteger = {
  mounted(el, binding) {
    const { value } = binding;
    // el.addEventListener('input', () => {
    //   // 获取输入框的当前值
    //   let value = el.value;
    //   // 如果值不是正整数，则设置为之前的值，或 1 如果是空字符串
    //   if (!/^\+?[0-9]\d*$/.test(value)) {
    //     if (value !== '') {
    //       value = el.dataset.lastValue || 1;
    //     }
    //   }
    //   // 更新数据以反映更改
    //   el.value = value;
    //   el.dataset.lastValue = value;
    // });
    el.addEventListener('blur', () => {
      // 获取输入框的当前值
      let value = el.value;
      if (!/^(0|[1-9]\d*)(\.\d+)?$/.test(value)) {
        if (value !== '') {
          value = el.dataset.lastValue || 1;
        }
      }
      // 更新数据以反映更改
      el.value = rateFormat(value);// rateFormat 全局保留小数位数的方法
      el.dataset.lastValue = rateFormat(value);
      el.dispatchEvent(new Event('input'));
    });
  },
};
const cancel = () => {
  emits('update:riskVisible', false);
};

watch(
  () => props.riskVisible,
  () => {
    if (props.riskVisible) {
      if (props.isEditCurrentData) {
        formData.value.riskAmplitudeRangeMax =
          props.currentInfo.riskAmplitudeRangeMax;
        formData.value.riskAmplitudeRangeMin = Math.abs(
          props.currentInfo.riskAmplitudeRangeMin
        );
      } else {
        formData.value.riskAmplitudeRangeMax =
          projectStore.currentTreeInfo?.riskAmplitudeRangeMax;
        formData.value.riskAmplitudeRangeMin = Math.abs(
          projectStore.currentTreeInfo?.riskAmplitudeRangeMin
        );
      }
    }
  }
);

const sureHandle = () => {
  if ([1,2].includes(projectStore.currentTreeInfo.levelType)) {
    constructRiskAmplitudeRangeController();
  } else {
    riskAmplitudeRangeController();
  }
};

const riskAmplitudeRangeController = () => {
  if (props.isEditCurrentData) {
    formData.value.riskAmplitudeRangeMax = Number(
      formData.value.riskAmplitudeRangeMax
    );
    formData.value.riskAmplitudeRangeMin =
      Number(formData.value.riskAmplitudeRangeMin) * -1;
    emits('updateCurrentInfo', formData.value);
    return;
  }
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    tcType: Number(projectStore.asideMenuCurrentInfo?.key),
    list: JSON.parse(JSON.stringify(props.selectData)),
    max: Number(formData.value.riskAmplitudeRangeMax),
    min: Number(formData.value.riskAmplitudeRangeMin) * -1,
    levelType:projectStore.currentTreeInfo.levelType
  };
  console.log('apiData', apiData);
  api.riskAmplitudeRangeController(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('批量设置风险幅度范围成功');
      cancel();
      emits('updateData');
    }
  });
};

const constructRiskAmplitudeRangeController = () => {
  if (props.isEditCurrentData) {
    formData.value.riskAmplitudeRangeMax = Number(
      formData.value.riskAmplitudeRangeMax
    );
    formData.value.riskAmplitudeRangeMin =
      Number(formData.value.riskAmplitudeRangeMin) * -1;
      emits('updateCurrentInfo', formData.value);
      return;
    }
    emits('updateTreeVal', Number(formData.value.riskAmplitudeRangeMax),Number(formData.value.riskAmplitudeRangeMin) * -1);
};
</script>

<style lang="scss" scoped>
.range-content {
  margin-bottom: 15px;
}
:deep(.ant-input) {
  width: 100px;
  margin: 0 5px;
}
</style>
