const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const EE = require('../../../core/ee');
const {UPCContext} = require("../core/UPCContext");
const {AnalyzeCore, UPCTemplateVO} = require("../core/UPCTemplateVO");
const {ccodes, baseFn, kemap, zmdmArr, rcjArr} = require("./rules/chargecode");
const _ = require('lodash');
const {NumberUtil} = require("../../utils/NumberUtil");
const TaxCalculationMethodEnum = require("../../enum/TaxCalculationMethodEnum");
const ConstantUtil = require("../../enum/ConstantUtil");
const DePropertyTypeConstant = require("../../enum/DePropertyTypeConstant");
const {Snowflake} = require("../../utils/Snowflake");
const {deFillBaseRule, deFillRules, cupfilter} = require("./rules/deRule");
const {LifeFactory,Cell, Organ} = require("@valuation/rules-engine");

//获取人材机数据
class RcjContext {
    constructor(ctx) {
        this.ctx = ctx;
        this.attr = new Map();
        //需要特殊处理的人材机
        this.specialRcjCode = ConstantUtil.SPECIAL_RCJ;

    }

    //过滤特殊的人材机 并且单位是%
    rcjMiddleware(rcj) {
        let roleExists = this.specialRcjCode.includes(rcj.materialCode) && rcj.unit == '%';
        return roleExists;
    }

    //构建基础的人材机数据
    getRcjList() {
        let {service} = EE.app;
        let {de, constructId, singleId, unitId} = this.ctx;
        let rcjs = [];
        let specialRcjs = [];
        let fn = (item) => {
            if (this.rcjMiddleware(item)) {
                specialRcjs.push(item)
            } else {
                rcjs.push(item);
            }
        }
        //根据定额获取人材机列表
        let rcjlist = service.rcjProcess.queryRcjDataByDeId(de.sequenceNbr, constructId, singleId, unitId);
        rcjlist = _.cloneDeep(rcjlist);
        if (rcjlist && rcjlist.length > 0) {
            rcjlist.forEach(item => {
                //处理其他材料的计算逻辑
                if (item.rcjDetailsDTOs && item.rcjDetailsDTOs.length > 0) {
                    item.rcjDetailsDTOs.forEach(itemc => {
                        itemc.parent = item;
                        fn(itemc);
                    })
                } else {
                    fn(item);
                }
            });
            if (specialRcjs.length > 0) {
                let specialRcjsGroup = _.groupBy(specialRcjs, item => item.kind);
                this.attr.set("SPECIAL_RCJ", specialRcjsGroup);
            }
            let rcjGroup = _.groupBy(rcjs, item => item.kind);
            this.attr.set("rcjGroup", rcjGroup);
        }
    }

    /**
     * 获取人材机数据
     * @param kind 人材机类型
     * @param column 获取的字段
     * @param isSimple 是否简易计税
     * @param key 获取的对饮的key
     * @returns {number|*[]}
     */
    getRcjData({kind, column, isSimple}, key) {
        let rcjdata = this.attr.get(key);
        let de  = this.ctx.de;
        if (!rcjdata) return 0;
        let value = [];
        let getValue = (k, rcjGroup) => {
            let list = rcjGroup[k];
            if (list) {
                list.forEach(item => {
                    if (typeof column == "function") {
                        value.push(column(item.libraryCode && item.libraryCode.startsWith(ConstantUtil.YEAR_2022), isSimple, item,de));
                    } else {
                        value.push(item[column] || 0);
                    }
                });
            }
        };
        if (_.isArray(kind)) {
            for (let i = 0; i < kind.length; i++) {
                getValue(kind[i], rcjdata);
            }
        } else {
            getValue(kind, rcjdata);
        }
        return value;
    }

}

/*
* 单价构成定额 计算逻辑
* 原 UnitPriceService.caculataDEUnitPrice 方法核心逻辑
* */


class UPCCupmuteDe extends LifeFactory{
  constructor(quotaLine, constructId, singleId, unitId) {
      super({});
      this.de = quotaLine;
      this.constructId = constructId;
      this.singleId = singleId;
      this.unitId = unitId;
      this.unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
      this.is2022 = PricingFileFindUtils.is22UnitById(constructId,singleId,unitId);;
      //计税方式
      this.taxCalculationMethod = this.unit.projectTaxCalculation.taxCalculationMethod;
      this.isSimple = this.taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code
      this.unitFeeFile = {};
      this.upcTemplateList = [];
      this.item = {};
      this.rcjContext = new RcjContext(this);
      this.pointConfig={
          "rcjDetailAmount": 2,   //人材机明细区：消耗量、合计数量，数量类：小数点后4位、第5位小数四舍五入
          "rcjSummaryAmount": 2,   //人材机汇总：消耗量、合计数量，数量类：小数点后4位、第5位小数四舍五入
          "qDDeAmount": 2,  // 工程量，数量类：小数点后3位，第4位四舍五入
          "costPrice": 2, // 金额、合计，金额类：小数点后2位，第3位四舍五入
          "rate": 2,  // 费率、指数、比率(%)：小数点后2位，第3位四舍五入
      }
  }
    static getInstance({constructId, singleId, unitId, allData}, pointLine) {
        return new UPCCupmuteDe(pointLine, constructId, singleId, unitId, allData);
    }
    getFeeFile(useMainFile) {
        let costFileCode = this.de.costFileCode;
        if (UPCContext.qfCodeMap.has(this.constructId + this.de.costFileCode)) {
            costFileCode = UPCContext.qfCodeMap.get(this.constructId + costFileCode);
        }
        let unitFeeFile;
        if (useMainFile || !costFileCode|| "SYSTEM-SZGC"==costFileCode||"SUIZHUGONGCHENG"==costFileCode) {
            unitFeeFile = this.unit.feeFiles.filter(f => f.defaultFeeFlag && f.defaultFeeFlag === 1)[0];
        } else {
            unitFeeFile = this.unit.feeFiles.filter(f => f.feeFileCode === costFileCode)[0]
        }
        this.unitFeeFile = unitFeeFile;
    }

    //填充基础数据
    prepare(useMainFile) {
        let { service } = EE.app;
        this.pointConfig=service.globalConfigurationService.getDecimalPointConfig()
        //获取取费文件
        this.getFeeFile(useMainFile);
        //获取单价构成模板
        this.getUpcTemplateList();
        //获取人材机列表
        this.rcjContext.getRcjList();
        //填充费率
        this.prepareOther();
        super.initialize()
    }

    analyze() {
       let rules=ccodes;
        //加载核心公式
        for (let baseIten in baseFn){
           let itemFn = baseFn[baseIten];
            rules[baseIten]=()=>{
                let result = itemFn();
                let cell =   {...result, name: baseIten };
                return cell;
            };
        }
        this.addRulesAndInitialize(rules);
        //加载单价构模板公式
        this.analyzeOther();

    }
    transform(field, value) {
        if(field == "GR"||field == "CLF_ZC"||field == "JXF_ZC")return value;
        return NumberUtil.numberScale(value, this.pointConfig.costPrice);
    }

    //生成默认的合价规则
    convertRuleStrHj(item) {
        //单价乘以工程量
        return 'NumberUtil.multiplyParams(' + item.code + ', DEGCL)';
    }
    analyzeUpcTemplateRules() {
      let rules ={};
        if (this.upcTemplateList.length > 0) {
            this.upcTemplateList.forEach(item => {
                if(item.code && item.caculateBase){
                    //计算基数
                    let ruleCaculateBase = Organ.create({name:item.code + "_caculateBaseValue",description:item.code,gene:item.caculateBase});
                    rules[item.code + "_caculateBaseValue"]=ruleCaculateBase;
                    //单价逻辑
                    let ruleDj = Organ.create({name:item.code,description:item.code+"单价",gene:this.convertRuleStrDJ(item)});
                    rules[item.code]=ruleDj;
                    //合价计算公式
                    let ruleHJ = Organ.create({name:item.code + "_allPrice",description:item.code+"合价",gene:this.convertRuleStrHj(item)});
                    rules[item.code+ "_allPrice"]=ruleHJ;
                }
            });
            this.addRulesAndInitialize(rules);
        }

    }

    //处理和核心逻辑之外的特殊逻辑
    analyzeOther() {
        //加载单价构模板公式
        this.analyzeUpcTemplateRules();

    }

    prepareOther() {
        //如果是随主工程 取费文件的code和单价构成模板不一致 代表是增量模板
        if (this.unitFeeFile.defaultFeeFlag == 1 && "SYSTEM-SZGC"!=this.de.qfCode&"SUIZHUGONGCHENG"!=this.de.qfCode&&this.unitFeeFile.feeFileCode != this.de.qfCode) return;
        //直接对比取费文件code 和单价构成code  不一致  则是增量模板
        if (this.de.costFileCode&&"SYSTEM-SZGC"!=this.de.qfCode&"SUIZHUGONGCHENG"!=this.de.qfCode && this.de.costFileCode != this.de.qfCode) return;
        //填充费率如果不是系统模版 是用户手动添加并且应用的模版 则不需要填充费率
        this.upcTemplateList.forEach(item => {
            if (item.typeCode == "UPC_GLF") {//管理费
                item.rate = Number(this.unitFeeFile.managementFee);
            }
            if (item.typeCode == "UPC_LR") {//利润
                item.rate = Number(this.unitFeeFile.profit);
            }
            if (item.typeCode == "UPC_GF") {
                item.rate = Number(this.unitFeeFile.fees);
            }
            if (item.typeCode == "UPC_AWF") {//安全文明施工费
                item.rate = Number(this.unitFeeFile.anwenRateBase);
            }
        })
    }

    //获取当前需要计算的单价构成模板
    getUpcTemplateList() {
        //获取仿制模板getTemplateListbyPath
        let upcTemplateList = UPCContext.getImitationTemplateList(this.constructId, this.unitId, this.de.qfCode);
        //如果不是仿制模板则从系统模板中获取
        if (!upcTemplateList || upcTemplateList.length == 0) {
            //获取系统模板
            UPCContext.getSystemTemplateList(this.unitFeeFile.feeFileCode, this.is2022 ? "22" : "12").forEach((item) => {
                //处理规费 和安文费 费用的 锁定状态
                let isLock = false;
                if ("UPC_GF" == item.typeCode || "UPC_AWF" == item.typeCode) isLock = true;
                this.upcTemplateList.push(new UPCTemplateVO({
                    ...item,
                    isLock: isLock,
                    sequenceNbr: Snowflake.nextId()
                }));
            })
        } else {
            upcTemplateList.forEach((item) => {
                this.upcTemplateList.push(new UPCTemplateVO({...item, sequenceNbr: Snowflake.nextId()}));
            })
        }
    }

    //获取人材机列表

    /*
    * 执行计算逻辑
    * */
    cupmute() {
        //如果没有人材机数据直接跳过计算
        if (this.de.isCostDe == DePropertyTypeConstant.AWF_DE) {
            if (!this.rcjContext.attr.has("rcjGroup")) return;
        }
        this.analyze();
        this.realCupmute();
        this.fillData();
    }

    //处理特殊逻辑并合计

    fillData() {
        if (!this.unit.feeBuild) {
            this.unit.feeBuild = {};
        }
        //填充单价构构成计算结果
        this.unit.feeBuild[this.de.sequenceNbr] = this.upcTemplateList;
        //回填定额数据
        this.backFillData();
        //回填费用代码数据
        this.backFillChargeCodeData();

    }

    //处理费用代码的回填
    backFillChargeCodeData() {
        if (!this.unit.upcTemp) this.unit.upcTemp = {};  //zmdmArr , rcjArr
        let copezmdmArr = _.cloneDeep(zmdmArr);
        let copercjArr = _.cloneDeep(rcjArr);
        copezmdmArr.forEach(item => {
            item.unitPrice = this.create(item.code);
        });
        copercjArr.forEach(item => {
            item.unitPrice = this.create(item.code);
        });
        this.unit.upcTemp[this.de.sequenceNbr] = {
            zmdmArr: copezmdmArr, rcjArr: copercjArr
        }
    }

    /**
     * 获取单价构成计算结果
     * @param type
     * @param kind
     * @param column
     * @returns {[]}
     */
    getUPCData({from, kind, column}) {

        let value = [];
        let getValue = (k, rcjGroup) => {
            let list = rcjGroup[k];
            if (list) {
                list.forEach(item => {
                    if (typeof column == "function") {
                        value.push(item);
                    } else {
                        value.push(item[column] || 0);
                    }
                });
            }
        };
        if (_.isArray(kind)) {
            for (let i = 0; i < kind.length; i++) {
                getValue(kind[i], this.UPCGroup);
            }
        } else {
            getValue(kind, this.UPCGroup);
        }
        return value;
    }

    /**
     * 回填定额数据
     * 单价构成数据回填到定额中  部分直接从费用类别直接获取
     */
    backFillData() {
        //根据类型进行分组
        this.UPCGroup = _.groupBy(this.upcTemplateList, "typeCode");
        let deFillRulesRef= _.cloneDeep(deFillRules);
        if(this.de.rcjFlag==1){
            let zjfPriceRule =   deFillRulesRef["zjfPrice"] ;
            zjfPriceRule.gene="ZJFPRICE";
            deFillRulesRef["zjfPrice"] =zjfPriceRule;

            let zjfTotalRule =   deFillRulesRef["zjfTotal"];
            zjfTotalRule.gene="zjfPrice*DEGCL";
            deFillRulesRef["zjfTotal"] =zjfTotalRule;
        }

        this.addRulesAndInitialize({...deFillBaseRule,...deFillRulesRef});
        for (const key in deFillRulesRef) {

            this.de[key] = this.create(key);
        }
    }

    /**
     * 计算过滤器 有可能跳过某个计算 这里  默认是防寒子目的选择 coldResistantSuborder
     * @param param
     * @returns {boolean}
     */
    isSkip(param) {
        return !(cupfilter[param] ? cupfilter[param](this) : true);
    }

    //通用计算规则
    realCupmute() {
        //计算单价
        this.upcTemplateList.forEach(item => {
            this.item = item;
            if (item.code && item.caculateBase) {
                item.caculateBaseValue =this.create(item.code + "_caculateBaseValue") //this.parseParams(item.code + "_caculateBaseValue");
                item.unitPrice =this.create(item.code) //this.parseParams(item.code);
                item.displayUnitPrice = item.unitPrice;
            }
        });
        //计算合价
        this.upcTemplateList.forEach(item => {
            this.item = item;
            if (item.code && item.caculateBase) {
                item.allPrice =this.create(item.code + "_allPrice");// this.parseParams(item.code + "_allPrice");
                item.displayAllPrice = item.allPrice;
            }
        });
    }

    /**
     * 格式化计算规则 单价构成 单价计算规则 默认需要乘以费率
     * @param type
     * @param caculateBase
     * @returns {string}
     */
    convertRuleStrDJ({type, caculateBase}) {
        return 'NumberUtil.multiplyParams((' + caculateBase + '), JSRATE)';
    }

    //计算引擎运行时取值
    getRuntimeValue(cell, param) {
        let {from, kind, column}=cell
        let item = this.item;
        if (item.code !== param) {
            item = this.upcTemplateList.filter(item => item.code == param)[0];
        }
        let value = 0;
        if (typeof column == "function") {
            value = column(item);
        } else {
            value = item[column];
        }
        return value;
    }

    //计算引擎默认取值实现
    getCellValue(cell) {
       let {from, kind, column}=cell
        let value = 0;
        switch (from) {
            case "rcj": {
                //从人材机中取值
                value = this.rcjContext.getRcjData({kind, column, isSimple: this.isSimple}, "rcjGroup");
                if (value.length == 0) value = 0;
                break;
            }
            case "SPECIAL_RCJ": {
                //其他类型人材机
                value = this.rcjContext.getRcjData({kind, column, isSimple: this.isSimple}, "SPECIAL_RCJ");
                if (value.length == 0) value = 0;
                break;
            }
            case "de": {
                //从当前定额中取值
                if (typeof column == "function") {
                    value = column({is2022: this.is2022, de: this.de, kind});
                } else {
                    value = this.de[column] || 0;
                }
                break;
            }
            case "UPC": {
                //从单价构成中获取值 这里用作定额数据的回填 的值获取
                value = this.getUPCData({from, kind, column});
                if (value.length == 0) value = 0;
                break;
            }
            default: {
                //运行时取值
                value = cell;
                break;
            }
        }
        return value;
    }
}
module.exports = UPCCupmuteDe
