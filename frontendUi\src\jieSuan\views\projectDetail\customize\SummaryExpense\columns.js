/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2024-03-29 15:44:30
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-19 09:34:50
 */
import { ref } from 'vue';

export const tableColumns = ref([
  {
    title: '序号',
    field: 'dispNo',
    width: 60,
    align: 'center',
    editRender: { autofocus: '.vxe-input--inner' },
    slot: true,
    classType: 1,
    initialize: true,
  },
  {
    title: '费用代号',
    field: 'code',
    align: 'center',
    minWidth: 120,
    width: 'auto',
    editRender: { autofocus: '.vxe-input--inner' },
    slot: true,
    classType: 1,
    initialize: true,
  },
  {
    title: '名称',
    field: 'name',
    width: 'auto',
    minWidth: 220,
    align: 'center',
    editRender: { autofocus: '.vxe-input--inner' },
    slot: true,
    classType: 1,
    initialize: true,
  },
  {
    title: '计算基数',
    field: 'calculateFormula',
    width: 'auto',
    minWidth: 150,
    align: 'center',
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    initialize: true,
  },
  {
    title: '基数说明',
    field: 'instructions',
    minWidth: 200,
    width: 'auto',
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    initialize: true,
  },
  {
    title: '费率（%）',
    field: 'rate',
    width: 120,
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    initialize: true,
  },
  {
    title: '费率类别',
    field: 'type',
    width: 180,
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    initialize: true,
  },
  // {
  //   title: '合同金额',
  //   field: 'jieSuanPrice',
  //   // field: 'backPrice',
  //   width: 120,
  //   classType: 1,
  //   initialize: true,
  // },
  {
    title: '结算金额',
    // field: 'price',
    field: 'price',
    width: 120,
    classType: 1,
    initialize: true,
  },
  {
    title: '备注',
    field: 'remark',
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    initialize: true,
  },
  {
    title: '打印',
    field: 'whetherPrint',
    width: 80,
    slot: true,
    classType: 1,
    initialize: true,
  }
]);

export const orgTableColumns = ref([
  {
    title: '序号',
    field: 'dispNo',
    width: 60,
    align: 'center',
    editRender: { autofocus: '.vxe-input--inner' },
    slot: true,
    classType: 1,
    initialize: true,
  },
  {
    title: '费用代号',
    field: 'code',
    align: 'center',
    minWidth: 120,
    width: 'auto',
    editRender: { autofocus: '.vxe-input--inner' },
    slot: true,
    classType: 1,
    initialize: true,
  },
  {
    title: '名称',
    field: 'name',
    width: 'auto',
    minWidth: 220,
    align: 'center',
    editRender: { autofocus: '.vxe-input--inner' },
    slot: true,
    classType: 1,
    initialize: true,
  },
  {
    title: '计算基数',
    field: 'calculateFormula',
    width: 'auto',
    minWidth: 150,
    align: 'center',
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    initialize: true,
  },
  {
    title: '基数说明',
    field: 'instructions',
    minWidth: 200,
    width: 'auto',
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    initialize: true,
  },
  {
    title: '费率（%）',
    field: 'rate',
    width: 120,
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    initialize: true,
  },
  {
    title: '费率类别',
    field: 'type',
    width: 180,
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    initialize: true,
  },
  {
    title: '合同金额',
    field: 'jieSuanPrice',
    //field: 'backPrice',
    width: 120,
    classType: 1,
    initialize: true,
  },
  {
    title: '结算金额',
    // field: 'price',
    field: 'price',
    width: 120,
    classType: 1,
    initialize: true,
  },
  {
    title: '备注',
    field: 'remark',
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    initialize: true,
  },

  {
    title: '打印',
    field: 'whetherPrint',
    width: 80,
    slot: true,
    classType: 1,
    initialize: true,
  },
]);
