const {Controller} = require("../../core");
const {ResponseData} = require("../utils/ResponseData");
class ProjectOverviewController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 保存工程特征
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async saveBasicEngineeringInfoOrEngineeringFeature(args){
        const res =await this.service.projectOverviewService.saveList(args);
        return ResponseData.success(res);
    }

    /**
     * 删除工程特征
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async delBasicEngineeringInfoOrEngineeringFeature(args){
        const res =await this.service.projectOverviewService.delete(args);
        return ResponseData.success(res);
    }

    /**
     * 解锁工程基本信息 （0:未锁定 1：已锁定）
     * @param args
     * @returns {Promise<ResponseData>}
     */
     lockBasicEngineeringInfo(args){
        const res = this.service.projectOverviewService.lockBasicEngineeringInfo(args);
        return ResponseData.success(res);
    }

    /**
     * 查询工程特征和基本信息
     * @param args
     * @returns {Promise<ResponseData>}
     */
     getProjectOverview(args){
        const res = this.service.projectOverviewService.getProjectOverviewList(args);
        return ResponseData.success(res);
    }

    /**
     * 获取默认费用汇总
     * @returns {Promise<ResponseData>}
     */
    getFyhz(){
        const res = this.service.projectOverviewService.getFyhz();
        return ResponseData.success(res);
    }

    /**
     * 获取费用代码
     * @returns {Promise<ResponseData>}
     */
    getFydm(){
        const res = this.service.projectOverviewService.getFydm();
        return ResponseData.success(res);
    }

    async initializationProject(args){
        await this.service.projectOverviewService.initializationProject(args);
        return ResponseData.success();
    }
    async updateYsfFile(args){
        await this.service.projectOverviewService.updateYsfFile(args);
        return ResponseData.success();
    }


    /**
     * 列表:导出xml之前获取未填字段, 校验接口
     * @param args
     * @returns {Promise<ResponseData>}-+
     */
    async getNotFilledList(args){
        const res = await this.service.projectOverviewService.getNotFilledList(args);
        return ResponseData.success(res);
    }

    /**
     * 编辑保存:xml之前获取未填字段, 校验接口
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async saveNotFilledList(args){
        const res = await this.service.projectOverviewService.saveNotFilledList(args);
        return ResponseData.success(true);
    }


    /**
     * 不同厂家 xml导出前 校验方法
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async validateManufacturerXML(args){
        const res = await this.service.projectOverviewService.validateManufacturerXML(args);
        return ResponseData.success(res);
    }


}

ProjectOverviewController.toString = () => '[class ProjectOverviewController]';
module.exports = ProjectOverviewController;
