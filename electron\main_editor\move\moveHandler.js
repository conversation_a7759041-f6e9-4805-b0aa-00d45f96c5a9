const EE = require('../../../core/ee');
const _ = require("lodash");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const {traverseRecursive} = require("../util");
const BranchProjectLevelConstant = require("../../enum/BranchProjectLevelConstant");
const RcjDeleteStrategy = require("../../rcj_handle/remove/removeRcjStrategy");
const OptionMenuHandler = require("../optionMenuHandler");
const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const BranchProjectDisplayConstant = require("../../enum/BranchProjectDisplayConstant");
const { NumberUtil } = require("../../utils/NumberUtil");



class MoveCommonHandler {
    constructor(ctx) {
        this.ctx = ctx;
        this.pointLine = null;
        this.allData = ctx.allData;
    }

    prepare({selectId}) {
        this.pointLine = this.allData.getNodeById(selectId);
        //获取到当前需要删除的的父节点
        this.parent = this.pointLine.parent;

    }

   /* move() {
        this.allData.removeNode(this.pointLine.sequenceNbr);
        this.parent.refreshIndex();
        if (!this.parent.children.length) {
            this.parent.displaySign = BranchProjectDisplayConstant.noSign;
        }
    }*/
    clearExtendedData(){
           //子类实现 删除当前行关联数据
    }
}

/**
 * 如理插分部删除
 */
class FbMoveHandler extends MoveCommonHandler{
    constructor(ctx) {
        super(ctx);
    }
    move({selectId,operateAction}) {
        let self = this;
        this.prepare({selectId});
         //判断当前节点下挂的是不是分部
        this.allData.removeNode(this.pointLine.sequenceNbr);
        let id="";//需要倍重新计算的ID
        // 上移
        if (operateAction=="up"){
            let parentIndex = this.parent.index;
            //删除当前行
            //添加到 祖父节点下
            let granddadNode = this.allData.getNodeById(this.parent.parentId);
            if(granddadNode.kind==BranchProjectLevelConstant.top){
                this.pointLine.kind=BranchProjectLevelConstant.fb;
            }
            this.allData.addNodeAt(this.pointLine, granddadNode, parentIndex+1);
            OptionMenuHandler.fillUpDown(granddadNode);
            id = this.parent.sequenceNbr;
            this._fillArrow(this.parent);

        }else {
            // 获取左边的邻居节点
            let leftNode = this.parent.children[this.pointLine.index-1];
            this.pointLine.kind=BranchProjectLevelConstant.zfb;
            this.allData.addNode(this.pointLine, leftNode);
            this._fillArrow(leftNode);
            OptionMenuHandler.fillUpDown(leftNode);

            id=leftNode.sequenceNbr;
        }
        OptionMenuHandler.setOptionMenu(this.parent);
        this.clearExtendedData();
        return id;
    }
    _fillArrow(newNode){
        newNode.displaySign = ObjectUtils.isEmpty(newNode.children) ? BranchProjectDisplayConstant.noSign : BranchProjectDisplayConstant.open;
        //处理父节点箭头
        if(this.parent){
            this.parent.displaySign = BranchProjectDisplayConstant.open;
        }
    }
    clearExtendedData(){

    }
}
module.exports = {
    FbMoveHandler
}
