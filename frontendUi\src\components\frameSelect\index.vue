<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2023-08-09 16:04:45
 * @LastEditors: liuxia
 * @LastEditTime: 2024-07-11 17:36:55
-->

<template>
  <div class="box" ref="selectBox" @mousedown="handleMouseDown">
    <div
      class="box-mask"
      v-show="positionList.is_show_mask"
      :style="
        'width:' +
        mask_width +
        'left:' +
        mask_left +
        'height:' +
        mask_height +
        'top:' +
        mask_top
      "
    ></div>
    <!-- <MySlot ref="slotRef"/> -->
    <slot></slot>
  </div>
</template>
<script setup>
// 如果节点childen没有值 则是最底层节点
// 如果节点有childen，则选中当前节点以及所有自己点
// 如果所有子节点选中，则父节点选中
import { ref, computed, reactive, watch, onMounted } from 'vue';
import frameSelectJs from './index';
import copyRestrictions from './copyRestrictions';
const selectBox = ref();
const emit = defineEmits(['selectData', 'scrollTo']);
let add_list = [];
let del_list = [];
let isScroll = false;
let headerHeight = 0;
const props = defineProps({
  tableData: {
    type: Array,
    default: [],
  },
  eventDom: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'measure',
  },
});
let scrollTargetHeight = ref(54); //功能区高度
const selectedState = ref([]);
const positionList = reactive({
  is_show_mask: false,
  box_screen_left: 0,
  box_screen_top: 0,
  start_x: 0,
  start_y: 0,
  end_x: 0,
  end_y: 0,
});

const typeList = ref([{}]);
const mask_width = computed(() => {
  return `${Math.abs(positionList.end_x - positionList.start_x)}px;`;
});
const mask_height = computed(() => {
  return `${Math.abs(positionList.end_y - positionList.start_y)}px;`;
});
const mask_left = computed(() => {
  return `${
    Math.min(positionList.start_x, positionList.end_x) -
    positionList.box_screen_left
  }px;`;
});
const mask_top = computed(() => {
  return `${
    Math.min(positionList.start_y, positionList.end_y) -
    positionList.box_screen_top
  }px;`;
});
const getRowCurrent = () => {
  return props.tableData.find(
    item =>
      item.sequenceNbr ===
      selectBox.value.querySelector('.row--current').attributes['rowid'].value
  );
};
const setCurrentRow = () => {
  console.log(
    '+++++++++++++++++setCurrentRow-----------------------',
    selectBox.value,
    props.tableData
  );
  selectBox.value.querySelectorAll('.vxe-body--row').forEach((node, index) => {
    if (props.tableData[0].sequenceNbr === node.attributes['rowid'].value) {
      node.classList.add('multiple-check');
    }
  });
};
defineExpose({
  getRowCurrent,
  frameSelectJs,
  clearSelect,
  setCurrentRow,
  isBranchCopy,
  isMeasureCopy,
  isHumanCopy,
});

//鼠标按下事件
const handleMouseDown = event => {
  if (event.button === 2) return;
  if (event.target.className === 'vxe-context-menu--link-content') return;
  // console.log('handleMouseDown',event)
  // 行号列
  if (event.target.classList.contains(props.eventDom)) {
    // if (event.ctrlKey) {
    // console.log('Ctrl键被按住并且点击了鼠标左键');
    positionList.is_show_mask = true;
    positionList.start_x = event.clientX;
    positionList.start_y = event.clientY;
    positionList.end_x = event.clientX;
    positionList.end_y = event.clientY;
    //获取表格标题头高
    headerHeight =
      selectBox.value.querySelector('.vxe-header--row').clientHeight;
    document.body.addEventListener('mousemove', handleMouseMove); //监听鼠标移动事件
    document.body.addEventListener('mouseup', handleMouseUp); //监听鼠标抬起事件
  } else {
    //非行号列
    // console.log(event)
    // 鼠标左键点击时
    if (event.button === 0) {
      if (!event.ctrlKey) clearSelect();
      // console.log()
      // 获取当前单选rowid
      let rowId = frameSelectJs.findParentWithClassName(
        event.target,
        'vxe-body--row'
      )?.attributes['rowid']?.value;
      if (event.ctrlKey) {
        if (add_list.includes(rowId) && add_list.length > 1) {
          add_list = add_list.filter(a => a !== rowId);
        } else if (add_list.includes(rowId) && add_list.length === 1) {
          add_list = [rowId];
        } else {
          add_list = [rowId, ...add_list];
        }
      } else {
        if (rowId) add_list = [rowId];
      }
      console.log(add_list);
      checkRow(add_list);
      document.body.addEventListener('mouseup', handleMouseUpNotFirst); //监听鼠标抬起事件
    }
  }
};

onMounted(() => {
  // console.log(selectBox.value)
  let wrapper = selectBox.value.querySelector('.vxe-table--body-wrapper');
  let tar = document.querySelectorAll('.operate-scroll')[0].parentElement; //功能区域div元素
  scrollTargetHeight.value = tar.offsetHeight; //功能区域div元素的高度
  // console.log(selectBox.value.querySelector('.vxe-table--body-wrapper'))
  wrapper.addEventListener('scroll', e => {
    // 判断是否滚动到页面底部
    // console.log(e);
    // console.log('1',wrapper)
    // console.log('add_list', add_list);
    selectBox.value
      .querySelectorAll('.vxe-body--row')
      .forEach((node, index) => {
        if (add_list.includes(node.attributes['rowid'].value)) {
          node.classList.add('multiple-check');
        } else {
          node.classList.remove('multiple-check');
        }
      });
    updateClassesAndIdentifyStartEnd();

    // handleDomSelect();
    // 在这里执行你的操作
  });
  console.log('+++++++++++++++++setCurrentRow-----------------------');
  setCurrentRow();
});
function clearSelect() {
  add_list = [];
  del_list = [];
  resSetXY();
  isScroll = false;
  selectBox.value.querySelectorAll('.vxe-body--row').forEach((node, index) => {
    node.classList.remove('multiple-check');
  });
  updateClassesAndIdentifyStartEnd();
}
function handleMouseMove(event) {
  console.log(event);
  positionList.end_x = event.clientX;
  positionList.end_y = event.clientY;
  console.log(selectBox.value.querySelectorAll('.vxe-table'));
  let boxDom = selectBox.value.querySelector('.vxe-table');
  handleDomSelect(event);
  scrollDom(event.y, boxDom.offsetHeight, boxDom.offsetTop);
  updateClassesAndIdentifyStartEnd();
}

function handleMouseUp(event) {
  if (event.button === 2) return;
  if (event.target.className === 'vxe-context-menu--link-content') return;
  document.body.removeEventListener('mousemove', handleMouseMove);
  document.body.removeEventListener('mouseup', handleMouseUp);
  positionList.is_show_mask = false;
  handleDomSelect(event);
  // 抬起鼠标进行可复制校验
  if (props.type === 'measure') isMeasureCopy(add_list);
  if (props.type === 'branch') isBranchCopy(add_list);
  if (props.type === 'human') isHumanCopy(add_list);
  isScroll = false;
  resSetXY();
  updateClassesAndIdentifyStartEnd();
}
function handleMouseUpNotFirst(event) {
  document.body.removeEventListener('mouseup', handleMouseUpNotFirst);
  // 抬起鼠标进行可复制校验
  if (props.type === 'measure') isMeasureCopy(add_list);
  if (props.type === 'branch') isBranchCopy(add_list);
  if (props.type === 'human') isHumanCopy(add_list);
}
function scrollDom(mouseY, offsetHeight, offsetTop) {
  console.log(mouseY, offsetHeight, offsetTop);
  if (mouseY >= offsetHeight + offsetTop) {
    // 下滑
    emit('scrollTo', 0, mouseY - (offsetHeight + offsetTop));
    isScroll = true;
  }
  if (mouseY <= headerHeight + offsetTop) {
    console.log('上滑');
    emit('scrollTo', 0, mouseY - (headerHeight + offsetTop));
    isScroll = true;
  }
}

function handleDomSelect(event) {
  console.log('handleDomSelect', event);
  const dom_mask = window.document.querySelector('.box-mask');
  // console.log(box.offsetTop)
  //getClientRects()每一个盒子的边界矩形的矩形集合
  const rect_select = dom_mask.getClientRects()[0];
  // rect_select.y = rect_select.y - box.offsetTop
  // console.log(rect_select);
  if (!event.ctrlKey && !isScroll) {
    add_list = [];
    del_list = [];
  }
  // console.log(document.querySelectorAll(".vxe-body--row"))
  // console.log(selectBox.value.querySelectorAll(".vxe-body--row"))
  // console.log(mask_width.value,mask_height.value)
  try {
    selectBox.value
      .querySelector('.vxe-table--main-wrapper')
      .querySelectorAll('.vxe-body--row')
      .forEach((node, index, arr) => {
        // console.log('node1',node)
        // console.log(arr.length)
        const rects = node.getClientRects()[0];
        if (collide(rects, rect_select) === true) {
          // console.log(node)
          if (mask_width.value === '0px;' && mask_height.value === '0px;') {
            //点击
            del_list = [];
            if (node.classList.contains('row-unit')) {
              // 全选根节点
              selectBox.value
                .querySelectorAll('.vxe-body--row')
                .forEach(node => {
                  // add_list.push()
                  // console.log(node)
                });
              // console.log(props.tableData)
              if (add_list.length > 1) {
                add_list = [node.attributes['rowid'].value];
              } else {
                props.tableData.map(a => {
                  add_list.push(a.sequenceNbr);
                });
              }

              throw new Error('break');
            } else {
              // console.log(node)
              // if (add_list.includes(node.attributes['rowid'].value)&&!event.ctrlKey) {
              //   //如果有id，移除
              //   add_list = add_list.filter(
              //     a => a !== node.attributes['rowid'].value
              //   );
              //   // console.log('//如果有id，移除',node.attributes['rowid'].value,add_list)
              // } else {
              // console.log('点击')

              let nodeInfo = props.tableData.find(a => {
                return a.sequenceNbr === node.attributes['rowid'].value;
              });
              if (nodeInfo.children.length > 0) {
                if (event.ctrlKey) {
                  // 是否选中
                  add_list.includes(node.attributes['rowid'].value)
                    ? invertChildren(nodeInfo.children)
                    : setChildren(nodeInfo.children);
                } else {
                  setChildren(nodeInfo.children);
                }
              } else {
                if (event.ctrlKey) {
                  // 是否选中
                } else {
                  setChildren(nodeInfo.children);
                }
              }
              if (
                event.ctrlKey &&
                add_list.includes(node.attributes['rowid'].value)
              ) {
                if (add_list.length > 1) {
                  add_list = add_list.filter(
                    a => a !== node.attributes['rowid'].value
                  );
                }
              } else {
                add_list.push(nodeInfo.sequenceNbr);
              }
              // console.log(add_list);
            }
          } else {
            del_list.push(node.attributes['rowid'].value);
          }
        }
      });
  } catch (e) {
    console.log(e);
  }
  function setChildren(arr) {
    arr.map(child => {
      console.log('🚀 ~ setChildren ~ child:', child);
      add_list.push(child);
      // if (child.children.length > 0) {
      //   setChildren(child.children);
      // }
    });
    console.log('🚀 ~ setChildren ~ add_list:', add_list);
  }
  function invertChildren(arr) {
    arr.map(child => {
      add_list = add_list.filter(item => {
        return item !== child;
      });
      // if (child.children.length > 0) {
      //   invertChildren(child.children);
      // }
    });
  }
  if (del_list.length > 0) {
    add_list = del_list;
    // if(!isScroll){
    //   del_list = []
    // }
  }
  add_list = [...new Set(add_list)];
  // console.log(add_list)
  checkRow(add_list);
  // emit('selectData',add_list)
  // selectedState.value = selectedState.value.concat(add_list).filter((item) => !del_list.includes(item));
}
function copyRestrictionsCreate() {
  return new copyRestrictions(props.tableData);
}
// 分部分项可复制校验
function isBranchCopy(arr) {
  const copyRestrictions = copyRestrictionsCreate();
  let emitData = copyRestrictions.simpleCopy(arr);
  // let emitData = copyRestrictions.isBranchCopy(arr);
  emit('selectData', emitData);
}
// 措施项目可复制校验
function isMeasureCopy(arr) {
  const copyRestrictions = copyRestrictionsCreate();
  let emitData = copyRestrictions.simpleCopy(arr);
  // let emitData = copyRestrictions.isMeasureCopy(arr);
  emit('selectData', emitData);
}

// 人材机调整页面复制
function isHumanCopy(arr) {
  const copyRestrictions = copyRestrictionsCreate();
  let emitData = copyRestrictions.getData(arr);
  emit('selectData', emitData);
}

// 我有一个数结构数据，let arr = [{parentId:'1',id:'11',children:[{}]}]

function checkRow(add_list) {
  selectBox.value.querySelectorAll('.vxe-body--row').forEach((node, index) => {
    if (add_list.includes(node.attributes['rowid'].value)) {
      node.classList.add('multiple-check');
    } else {
      node.classList.remove('multiple-check');
    }
    if (
      node.classList.contains('row--current') &&
      (add_list.length === 0 || !add_list)
    ) {
      console.log(node);
      node.classList.add('multiple-check');
    }
  });
}
function updateClassesAndIdentifyStartEnd() {
  const elements = selectBox.value.querySelectorAll('.vxe-body--row');
  elements.forEach(element => {
    element.classList.remove('check-start', 'check-end');
  });
  // let currentMultipleCheckStart = null;

  for (let i = 0; i < elements.length; i++) {
    const element = elements[i];
    const nextElement = elements[i + 1];
    const prevelement = elements[i - 1];
    const isMultipleCheck = element.classList.contains('multiple-check'); //当前是否选中
    const isNextMultipleCheck =
      nextElement?.classList.contains('multiple-check'); //下一个是否选中
    const isPrevMultipleCheck =
      prevelement?.classList.contains('multiple-check'); //上一个是否选中
    if (prevelement && !isPrevMultipleCheck && isMultipleCheck) {
      element.classList.add('check-start');
    }
    if (
      prevelement &&
      isPrevMultipleCheck &&
      nextElement &&
      !isNextMultipleCheck &&
      isMultipleCheck
    ) {
      element.classList.add('check-end');
    }
    if (
      prevelement &&
      !isPrevMultipleCheck &&
      nextElement &&
      !isNextMultipleCheck &&
      isMultipleCheck
    ) {
      element.classList.add('check-end');
    }
    // console.log('nextElementisMultipleCheck',element,nextElement,isMultipleCheck,isNextMultipleCheck)
  }
}
//比较checkbox盒子边界和遮罩层边界最大最小值
function collide(rect1, rect2) {
  const box = window.document.querySelector('.box');
  const maxX = Math.max(rect1.x + rect1.width, rect2.x + rect2.width);
  const maxY = Math.max(
    rect1.y + rect1.height,
    rect2.y - scrollTargetHeight.value + rect2.height
  );
  const minX = Math.min(rect1.x, rect2.x);
  const minY = Math.min(rect1.y, rect2.y - scrollTargetHeight.value);
  return (
    maxX - minX <= rect1.width + rect2.width &&
    maxY - minY <= rect1.height + rect2.height
  );
}

//清除
function resSetXY() {
  positionList.start_x = 0;
  positionList.start_y = 0;
  positionList.end_x = 0;
  positionList.end_y = 0;
}
</script>
<style lang="scss" scoped>
.box {
  &-mask {
    position: absolute;
    background: transparent;
    opacity: 0.4;
    z-index: 100;
  }
  :deep(.vxe-table--body) {
    border-collapse: collapse;
  }
  // :deep(.check-start){
  //   border-top:2px solid #287CFA ;
  // }
  // :deep(.check-end){
  //   border-bottom:2px solid #287CFA ;
  // }
  :deep(.vxe-table) {
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .vxe-icon-caret-down,
    .vxe-icon-caret-right {
      width: 12px;
      height: 12px;
      display: inline-block;
      text-align: center;
      line-height: 8px;
      border-radius: 50%;
      position: relative;
      top: -1px;
      left: 2px;
      //border: 1px solid #87b2f2;
      //color: #87b2f2;
      font-size: 12px;
    }
    .vxe-icon-caret-down:before {
      content: '-';
    }
    .vxe-icon-caret-right:before {
      content: '+';
    }
    .multiple-check {
      background: #a6c3fa !important;
      // background-color: hsl(200, 50%, 50%);
      // border-bottom:1px solid white ;
    }
  }
}
</style>
