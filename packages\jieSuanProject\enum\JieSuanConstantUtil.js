const os = require("os");

/**
 * 结算项目常用的公共常量
 */
class JieSuanConstantUtil {

    static YUSUAN_FILE_SUFFIX =[`YSF`,`YSFZ`,`YSFD`];


    static jsProject = 3;




    //用户历史数据路径
    static USERHISTORY_PATH =`${os.homedir()}\\.xilidata\\userHistory.json`;

    //结算项目存储文件名
    static JIESUAN_FILENAME =`file.json`;

    //结算项目文件后缀名
    static JIESUAN_FILE_SUFFIX =`YJS`;

    //结算项目文件后缀名
    static JIESUAN_FILE_SUFFIX_XLSX =`xlsx`;




    //分期方式 1：按分期比例输入  2：按分期工程量输入
    static STAGE_RATIO = 1;//1：按分期比例输入
    static STAGE_QUANTITY = 2;//2：按分期工程量输入

    //动态列存储文件名
    static JIESUAN_COLUMNNAME =`column.json`;


    //动态字段
    static COLUMN =`column`;


    //（结算价-基期价）/基期价＞5% 中的5%
    static JIEQI_BILI1=0.05;
    static JIEQI_BILI2=-0.05;

    // 是否甲供  1:是甲供  0：不是甲供
    static IF_DONORMATERIAL_0 = 0;
    static IF_DONORMATERIAL_1 = 1;


    static TRRGET_VERSION="1.0.38";
    static ZHUCAI="94";
    static SHEBEI="95";

    static FB_ZHIBIAO=1;
    static CS_ZHIBIAO=2;
    static GL_ZHIBIAO=3;

    static QD_CODE4="清单前四位";




}
module.exports = JieSuanConstantUtil
