import {Column, Entity, PrimaryColumn} from 'typeorm';

import { BaseModel } from './BaseModel';

/**
 * 12定额父级定额关联表
 */
@Entity({name: "base_de_relation"})
export class BaseDeRelation {

    @PrimaryColumn({name:"sequence_nbr"})
    public sequenceNbr : string; // 'id'

    @Column({name: "list_code", nullable: true})
    public listCode: string; // '清单编码',

    @Column({name: "job_content", nullable: true})
    public jobContent: string; // '工作内容',

    @Column({name: "groupid", nullable: true})
    public groupid: string; // '规则组id',

    @Column({name: "library_code", nullable: true})
    public libraryCode: string; // '定额库编码',


    @Column({name: "library_name", nullable: true})
    public libraryName: string; // '定额库名称',

    @Column({name: "de_code_f", nullable: true})
    public deCodeF: string; // '父级定额编码',

    @Column({name: "de_name_f", nullable: true})
    public deNameF: string; // '父级定额名称',

    @Column({name: "unit_f", nullable: true})
    public unitF: string; // '父级单位',

    @Column({name: "library_name_relation", nullable: true})
    public libraryNameRelation: string; // '子级定额库名称',

    @Column({name: "relation_content", nullable: true})
    public relationContent: string; // '关联内容',

    @Column({name: "de_code_z", nullable: true})
    public deCodeZ: string; // '子级定额编码',

    @Column({name: "de_name_z", nullable: true})
    public deNameZ: string; // '子级定额名称',

    @Column({name: "unit_z", nullable: true})
    public unitZ: string; // '子级定额单位',

    @Column({name: "quantity", nullable: true})
    public quantity: string; // '子级定额含量',

    @Column({name: "library_code_z", nullable: true})
    public libraryCodeZ: string; // '子级定额册编码',









}



