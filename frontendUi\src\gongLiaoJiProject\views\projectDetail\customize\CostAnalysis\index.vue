<!--
 * @Author: wangru
 * @Date: 2023-05-29 09:34:55
 * @LastEditors: sunchen
 * @LastEditTime: 2025-04-08 10:17:06
-->
<template>
  <div class="common-flex-upAndDown">
    <!-- <div class="head">
      <img src="~@/assets/img/detailImg/export.png" alt="" /><a-button
        type="text"
        >导出表格</a-button
      >
    </div> -->
    <split
      horizontal
      ratio="4/3"
      :horizontalBottom="35"
      style="height: 100%"
      mode="vertical"
      @onDragHeight="dragHeight"
    >
      <template #one>
        <keep-alive>
          <div class="flex-auto">
            <!-- {{isUnit}} -->
            <item-analysis
              v-if="!isUnit"
              :stableHeight="stableHeight"
              :showColumns="showColumns"
            ></item-analysis>
            <unit-analysis
              class="item"
              :tableColumns="handlerColumns"
              v-if="isUnit"
              :stableHeight="stableHeight"
            ></unit-analysis>
          </div>
        </keep-alive>
      </template>
      <template #two>
        <div class="quota-info">
          <div class="head-action">
            <a-tabs
              v-model:activeKey="componentName"
              type="card"
              :hideAdd="true"
            >
              <a-tab-pane key="threeMaterials" tab="三材汇总表">
                <div class="standard-type-table">
                  <vxe-table
                    ref="vexTable"
                    class="standard-type-table"
                    border
                    height="auto"
                    :scroll-y="{ enabled: false }"
                    :data="tableData"
                    keep-source
                    :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
                    :column-config="{ resizable: true }"
                    :cell-class-name="selectedClassName"
                  >
                    <vxe-column :width="columnWidth(60)" title="序号">
                      <template #default="{ row, $rowIndex }">
                        {{ $rowIndex + 1 }}
                      </template>
                    </vxe-column>
                    <vxe-column field="name" :min-width="columnWidth(180)" title="名称"></vxe-column>
                    <vxe-column
                      field="unitCN"
                      :min-width="columnWidth(80)"
                      title="单位"
                    ></vxe-column>
                    <vxe-column field="scCount" :min-width="columnWidth(80)" title="数量">
                      <template #default="{ row }">
                        {{
                          decimalFormat(
                            row.scCount,
                            'COST_ANALYSIS_SCNUMBER_PATH'
                          )
                        }}
                      </template>
                    </vxe-column>
                  </vxe-table>
                </div>
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
      </template>
    </split>
  </div>
</template>

<script setup>
import { onMounted, ref, watch, getCurrentInstance } from 'vue';
import ItemAnalysis from './ItemAnalysis.vue';
import UnitAnalysis from './UnitAnalysis.vue';
import { useCellClick } from '@gongLiaoJi/hooks/useCellClick';
const { selectedClassName } = useCellClick();
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import { useFormatTableColumns } from '@gongLiaoJi/hooks/useGljFormatTableColumns.js';
import tableColumns from './tableColumns';
import feePro from '@/gongLiaoJiProject/api/feePro';
import { useDecimalPoint } from '@gongLiaoJi/hooks/useDecimalPoint.js';
const { decimalFormat } = useDecimalPoint();
import { columnWidth } from '@gongLiaoJi/hooks/useSystemConfig';
const stableHeight = ref(400);
const tableData = ref([]);
const componentName = ref('threeMaterials');
const dragHeight = h => {
  stableHeight.value = h - (window.innerWidth < 1370 ? 0 : 45);
};
window.addEventListener('resize', function () {
  let tableEl = document.querySelector('.table-content');
  stableHeight.value =
    tableEl.clientHeight - (window.innerWidth < 1370 ? 0 : 50);
});
const store = projectDetailStore();
let isUnit = ref(true);
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
bus.on('export-table', data => {
  if (store.componentId === 'CostAnalysis') exportTable();
});

watch(
  () => store.currentTreeInfo,
  () => {
    if (store.currentTreeInfo?.type === 3) {
      isUnit.value = true;
    } else {
      isUnit.value = false;
    }
    if (store.currentTreeInfo?.type === 2) {
      getScList();
    }
  }
);
watch([() => store.tabSelectName, () => tableColumns.value], () => {
  if (store.tabSelectName == '造价分析') {
    tableColumns.value.map(a => {
      if (
        ['其他措施费，其中：机械费', '零星工程包干费，其中：机械费'].includes(
          a.field
        )
      ) {
        a.visible = store.currentTreeInfo.constructMajorType !== '2018-AZGC-GS';
      }
    });
    initColumns({
      columns: tableColumns.value,
      pageName: 'zjfx',
    });
    console.log('初始化列头');
    getScList();
  }
});
const {
  showColumns,
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
} = useFormatTableColumns({
  type: 5,
  initColumnsCallback: () => {
    initColumns({
      columns: tableColumns.value,
      pageName: 'zjfx',
    });
  },
});

onMounted(() => {
  if (store.currentTreeInfo.constructMajorType === '2018-AZGC-GS') {
    tableColumns.value.map(a => {
      if (
        ['其他措施费，其中：机械费', '零星工程包干费，其中：机械费'].includes(
          a.field
        )
      ) {
        a.visible = false;
      }
    });
  }
  if (store.tabSelectName == '造价分析') {
    initColumns({
      columns: tableColumns.value,
      pageName: 'zjfx',
    });
    getScList();
  }
  if (store.currentTreeInfo?.type === 3) {
    isUnit.value = true;
  } else {
    isUnit.value = false;
  }
});
// 获取三材汇总表数据
const getScList = () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId:
      store.currentTreeInfo?.type == 2
        ? store.currentTreeInfo?.id
        : store.currentTreeInfo?.parentId, //单项ID
    unitId: store.currentTreeInfo?.type == 3 ? store.currentTreeInfo?.id : '', //单位ID
  };
  feePro.getScCountList(apiData).then(res => {
    console.log('取费表获取三材汇总表数据', res);
    if (res.status !== 200) {
      return message.error(res.message);
    }
    tableData.value = res.result;
  });
};
const exportTable = () => {
  console.log('导出表格', {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId:
      store.currentTreeInfo?.type == 2
        ? store.currentTreeInfo?.id
        : store.currentTreeInfo?.parentId, //单项ID
    unitId: store.currentTreeInfo?.type == 2 ? '' : store.currentTreeInfo?.id, //单位ID
    type: store.currentTreeInfo?.type,
  });
  feePro
    .exportCostAnalysis({
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId:
        store.currentTreeInfo?.type == 2
          ? store.currentTreeInfo?.id
          : store.currentTreeInfo?.parentId, //单项ID
      unitId: store.currentTreeInfo?.type == 2 ? '' : store.currentTreeInfo?.id, //单位ID
      type: store.currentTreeInfo?.type,
    })
    .then(res => {
      console.log(res);
      if (res.result) {
        message.success('导出成功');
      } else {
        message.error('导出失败');
      }
    });
};
defineExpose({
  handlerColumns,
  updateColumns,
  getDefaultColumns,
});
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 10px;
}
.quota-info {
  height: 100%;
  .head-action {
    margin-bottom: 5px;
    height: 35px;
    background: #e7e7e7;
    flex: 1;
    :deep(.ant-tabs-tab) {
      height: 35px;
      background: transparent;
      border: none;
      color: #7c7c7c;
    }
    :deep(.ant-tabs-tab-active) {
      background: #ffffff;
      border-top: 2px solid #4786ff;
      .ant-tabs-tab-btn {
        color: #000000;
      }
    }
    button {
      float: right;
      margin-right: 15px;
    }
  }
  .content {
    height: calc(100% - 40px);
  }
}
.standard-type-table {
  width: 500px;
  height: 100%;
  :deep(.vxe-table) {
    width: 97% !important;
    height: 100%;
    .vxe-table--render-wrapper {
      height: 100%;
      .vxe-table--main-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
        .vxe-table--body-wrapper {
          flex: 1;
          height: auto !important;
          min-height: auto !important;
        }
      }
    }
  }
  .table-edit-common {
    width: 100% !important;
  }
}
</style>
