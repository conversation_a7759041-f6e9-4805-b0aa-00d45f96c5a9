/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2024-06-14 19:33:42
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2025-03-25 11:24:09
 */
import { markRaw, defineAsyncComponent } from 'vue';
const jieSuanZbPrefix = '../../../../jieSuan/views/projectDetail/quota';
export const getComponents = (type = 'jieSuanZhiBiao') => {
  const paths = {
    jieSuanZhiBiao: [
      { name: 'keyInfo', path: './keyInfo/index.vue' },
      { name: 'economyQuota', path: './economyQuota/index.vue' },
    ],
  };
  let components = markRaw(new Map());
  const jieSuanZhiBiaoModules = import.meta.glob(
    '../../../../jieSuan/views/projectDetail/quota/*/*.vue'
  );
  const allModules = Object.assign(jieSuanZhiBiaoModules);
  for (let item of paths[type]) {
    components.set(item.name, defineAsyncComponent(allModules[item.path]));
  }
  return components;
};
