const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const _ = require('lodash');
const EE = require('../../../core/ee');
const {qdFillBaseRule, qdFillRules, qdUPCBaseRule, qdUPCRules, qdUPCRules2012} = require("./rules/qdRule");
const {qdUPCTemplate2022, qdUPCTemplate2012, otherList} = require("./template/qdUpcTemplate");
const {NumberUtil} = require("../../utils/NumberUtil");
const {LifeFactory, Organ} = require("@valuation/rules-engine");


class UPCCupmuteQd  extends LifeFactory{
    constructor(qdId, constructId, singleId, unitId, allData) {
        super();
        this.qdId = qdId;
        this.qd = {};
        this.des = [];
        // 安文费费用定额
        this.awfDes = [];
        this.constructId = constructId;
        this.singleId = singleId;
        this.unitId = unitId;
        this.unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        this.is2022 = PricingFileFindUtils.is22UnitById(constructId,singleId,unitId);
        this.allData = allData;
        this.upcTemplateList = [];
        this.upcTemplateGroupList = [];
        this.attr = new Map();
        this.pointConfig={
            "rcjDetailAmount": 2,   //人材机明细区：消耗量、合计数量，数量类：小数点后4位、第5位小数四舍五入
            "rcjSummaryAmount": 2,   //人材机汇总：消耗量、合计数量，数量类：小数点后4位、第5位小数四舍五入
            "qDDeAmount": 2,  // 工程量，数量类：小数点后3位，第4位四舍五入
            "costPrice": 2, // 金额、合计，金额类：小数点后2位，第3位四舍五入
            "rate": 2,  // 费率、指数、比率(%)：小数点后2位，第3位四舍五入
        }
    }

    static getInstance({constructId, singleId, unitId, allData}, pointLine) {
        return new UPCCupmuteQd(pointLine.sequenceNbr, constructId, singleId, unitId, allData);
    }

    prepare() {
        let { service } = EE.app;
        this.pointConfig=service.globalConfigurationService.getDecimalPointConfig()
        this.projectConfig=service.globalConfigurationService.getProjectConfig(this.constructId)

        this.getBaseData();
        this.getUpcTemplateList();
        // 构建规则
        let rules={...qdFillBaseRule,...qdFillRules,...qdUPCBaseRule};
        if (this.upcTemplateList.length > 0) {
            this.upcTemplateList.forEach(item => {
                //合价计算公式
                let rule = Organ.create({name:item.typeCode + "_allPrice",description:item.typeCode+"合价",gene:this.convertRuleStrHj(item)});
                rules[item.typeCode + "_allPrice"]=rule;
                //单价逻辑
                let rule1 = Organ.create({name:item.typeCode,description:item.typeCode+"单价",gene:this.convertRuleStrDJ(item)});
                rules[item.typeCode]=rule1;
            });
        }
        for (const otherKey in otherList) {
            let item = otherList[otherKey];
            //合价计算公式
            let rule = Organ.create({name:item.typeCode + "_allPrice",description:item.typeCode+"合价",gene:this.convertRuleStrHj(item)});
            rules[item.typeCode + "_allPrice"]=rule;
            //单价逻辑
            let rule1 = Organ.create({name:item.typeCode,description:item.typeCode+"单价",gene:this.convertRuleStrDJ(item)});
            rules[item.typeCode]=rule1;
        }
        this.addRulesAndInitialize(rules);
    }



    //单价计算公式
    convertRuleStrDJ({typeCode}) {
        return 'quantity>0?(NumberUtil.divide(' + typeCode + '_allPrice,quantity)):0';
    }

    //合价计算公式
    convertRuleStrHj(item) {
        return this.is2022 ? qdUPCRules[item.typeCode + "_allPrice"].gene : qdUPCRules2012[item.typeCode + "_allPrice"].gene
    }
    getBaseData() {
        this.qd = this.allData.getNodeById(this.qdId);
        this.qd.children.forEach(item => {
            this.des.push(item);
        });
        this.upcTemplateList = this.is2022 ? _.cloneDeep(qdUPCTemplate2022) : _.cloneDeep(qdUPCTemplate2012);
    }

    getUpcTemplateList() {
        //合并单价构成数据
        let arr = [];
        this.des.forEach(item => {
            let list = this.unit.feeBuild[item.sequenceNbr];
            if (list && list.length > 0) {
                list = list.filter(item => !_.isUndefined(item));
                arr = arr.concat(list)
            }
        });
        //按类型分组
        this.upcTemplateGroupList = _.groupBy(arr, item => item.typeCode);
    }

    cupmute() {
        this.realCupmute();
        this.afterExecute();
        this.clean();
    }

    clean() {
        this.attr.clear();
    }

    getData({kind, column}) {
        let value = [];
        let list = this.upcTemplateGroupList[kind];
        if (list && list.length > 0) {
            list.forEach(item => {
                if (typeof column == "function") {
                    value.push(item);
                } else {
                    value.push(item[column] || 0);
                }
            });
        }
        return value.length > 0 ? value : 0;
    }
    /**
     * 获取定额数据
     * @param column
     * @returns {[]}
     */
    getDeData({column}) {
        let value = [];
        if (this.des.length > 0) {
            this.des.forEach(item => {
                if (typeof column == "function") {
                    value.push(item);
                } else {
                    value.push(item[column] || 0);
                }
            });
        }
        return value.length > 0 ? value : 0;
    }
    transform(field, value) {
        return NumberUtil.numberScale(value,  this.pointConfig.costPrice);
    }

    getCellValue(cell) {
        let {from, kind, column}=cell;
        let value = 0;
        switch (from) {
            case "DE": {
                value = this.getDeData({from, kind, column});
                break;
            }
            case "UPC": {
                value = this.getData({from, kind, column});
                break;
            }
            case "qd": {
                if (typeof column == "function") {
                    value = column({qd: this.qd});
                } else {
                    value = this.qd[column] || 0;
                }
                break;
            }
            case "runtime": {
                value = {from, kind, column};
                break;
            }
            case "context": {
                value = this;
                break;
            }
            default: {
                value = column(this);
            }
        }
        return value;
    }

    realCupmute() {
        //计算合价
        this.upcTemplateList.forEach(item => {
            this.item = item;
            try {
                item.allPrice = this.create(item.typeCode + "_allPrice");
                item.displayAllPrice = item.allPrice;
            } catch (e) {
                console.log(item.typeCode);
            }
        });
        //计算单价
        this.upcTemplateList.forEach(item => {
            this.item = item;
            item.unitPrice = this.create(item.typeCode);
            item.displayUnitPrice = item.unitPrice;

        });
        //默认隐藏的行
        for (const otherKey in otherList) {
            let item = otherList[otherKey];
            if (this.upcTemplateGroupList[otherKey]) {
                item = _.cloneDeep(item);
                item.allPrice = this.create(item.typeCode + "_allPrice");
                item.displayAllPrice = item.allPrice;
                item.unitPrice = this.create(item.typeCode);
                item.displayUnitPrice = item.unitPrice;
                this.upcTemplateList.push(item);
            }
        }

    }


    afterExecute() {
        if (!this.unit.feeBuild) {
            this.unit.feeBuild = {};
        }
        //填充单价构成
        this.unit.feeBuild[this.qd.sequenceNbr] = this.upcTemplateList;
        this.backFillData();
    }

    /**
     * 回填清单数据
     */
    backFillData() {

        for (const key in qdFillRules) {
            //如果计算清单的综合单价
            if(key=="price" && this.qd.lockPriceFlag && !this.qd.tempDeleteFlag){
                this.qd[key] =this.qd.lockPriceBack;
            }else if(key=="total" && this.qd.lockPriceFlag && !this.qd.tempDeleteFlag){
                this.qd[key]=NumberUtil.numberScale(NumberUtil.multiply(this.qd.quantity,this.qd.lockPriceBack), 2)
            } else {
                this.qd[key] = NumberUtil.numberScale(this.create(key), 2);
            }
        }
    }

}

module.exports = UPCCupmuteQd
