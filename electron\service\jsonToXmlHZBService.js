const {ObjectUtils} = require("../utils/ObjectUtils");
const fs = require('fs');
const _ = require("lodash");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {NumberUtil} = require("../utils/NumberUtil");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {Service} = require('../../core');
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
let npm = require('getmac');
const UnitPriceConstant = require("../enum/UnitPriceConstant");
const RcjLevelMarkConstant = require("../enum/RcjLevelMarkConstant");
const OtherProjectCalculationBaseConstant = require("../enum/OtherProjectCalculationBaseConstant");
const CalculateBaseType = require("../enum/CalculateBaseType");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const CostTypeJrgEnum = require("../enum/CostTypeJrgEnum");
const CostTypeSbLbEnum = require("../enum/CostTypeSbLbEnum");
const Fixs = require("../fixs");
const {RCJKind} = require("../enum/ConversionSourceEnum");





//获取mac地址
class JsonToXmlHZBService extends Service {
    constructor(ctx) {

        super(ctx);
        let rcjArray=[];//人材机汇总接口返回值
        let RcjMap ={};
    }
    getFeeTypeByKind(kind) {
        switch (kind) {
            case 0:
                return "其他费";
            case 1:
                return "人工费";
            case 2:
                return "材料费";
            case 3:
                return "机械费";
            case 4:
                return "设备费";
            case 5:
                return "主材费";
            case 6:
                return "商砼";
            case 7:
                return "砼";
            case 8:
                return "浆";
            case 9:
                return "商浆";
            case 10:
                return "配比";
        }
    }
     createSubItem  (rcj, index, outputTaxRate)  {
        return {
            序号: index + 1,
            编码: rcj.materialCode,
            类型: this.getFeeTypeByKind (rcj.kind),
            名称: rcj.materialName,
            计量单位: rcj.unit,
            数量: this.dualZBType( rcj.totalNumber),
            预算价: this.dualZBType(rcj.dePrice),
            除税系数: this.dualZBType(rcj.taxRemoval),
            市场价: this.dualZBType(rcj.marketPrice),
            除税市场价: this.dualZBType(NumberUtil.costPriceAmountFormat(NumberUtil.subtract(rcj.marketPrice, NumberUtil.multiply(rcj.marketPrice, NumberUtil.divide100( rcj.taxRemoval))))),
            结算价: this.dualZBType(rcj.marketPrice),
            暂估标志: rcj.ifProvisionalEstimate == 1 ? 1 : 0,
            主要材料标志: rcj.ifProvisionalEstimate == 1 ? 1 : 0,
            规格型号: rcj.specification,
            质量等级: rcj.qualityGrade,
            厂家: rcj.manufactor,
            产地: rcj.producer,
            含税合价: rcj.total,
            除税合价: NumberUtil.subtract(rcj.total, rcj.jxTotal),
            销项税额合计: NumberUtil.costPriceAmountFormat(NumberUtil.multiply(rcj.total, NumberUtil.divide100(outputTaxRate))),
            进项税额合计: rcj.jxTotal,
            供应时间: '',
            甲供数量: rcj.donorMaterialNumber,
            送达地点: rcj.deliveryLocation,
            备注: rcj.description
        };
    };

    /**
     * 处理 招标文件时固定为 0
     * type ==0 招标
     */
    dualZBType(value){
        if( this.type ==0){
            return 0
        }else {
            return ObjectUtils.isEmpty(value)?0:value
        }

    }

    async generateXml(args) {
        let resObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
        //改为从文件读取
        let projectObj = await PricingFileFindUtils.getProjectObjByPath(resObj.path);
        await  new Fixs(projectObj,projectObj.version).fix();
        this.res = "";
        let json = await this.convertXMLData(projectObj,args.type);
        let xmlString = '<?xml version="1.0" encoding="UTF-8"?>' + "\n"  + await this.printObj(json, "JingJiBiao", "");


        let path = projectObj.path;
        let pathArray = path.split("\\");
        let fileName = pathArray[pathArray.length - 1];
        let suffix = 'xml';
        if(args.vender === 0){
            suffix ='xml'
        }
        let newFileName = projectObj.constructName+"."+suffix;
        let newPath = path.replace(fileName, newFileName);

        //获取线上项目
        // let defaultStoragePath = PricingFileFindUtils.getDefaultStoragePath(projectObj.constructName);
        let result = dialog.showSaveDialogSync(null, {
            title: '保存文件',
            defaultPath: newPath, // 默认保存路径
            filters: [
                {name: '云算房', extensions: [suffix]} // 可选的文件类型
            ]
        });

        if (!ObjectUtils.isEmpty(result)) {

            let filePath = result;
            try {
                fs.writeFileSync(filePath, xmlString);
                console.log('XML写入文件成功！');
            } catch (err) {
                console.log('XML写入文件失败：', err);
            }
            return true;
        }else {

            return false;

        }


    }

    /**
     * ysf格式转为河北标准格式XML对应JSON
     * @param projectObj
     */
    async convertXMLData(projectObj,type){


        this.type = type
        let taxCalculation = await this.service.baseFeeFileService.taxCalculationParam(projectObj);

        let json = {};
        json.文件版本 = '1.0'
        json.接口标准 = '河北建设工程工程量清单惠招标接口2019'
        json.造价规范 = ''
        if(type === 0){
            json.文件类别 = '招标文件'
        }else if(type === 1){
            json.文件类别 = '投标文件'
        }else if(type === 2){
            json.文件类别 = '招标控制价文件'
        }
        json.文件校验码 = '' //todo 无法确定如何取值

        let 工程数据 ={}

        let 招投标信息 = {}

        招投标信息.招投标项目编码 = projectObj.constructCode;
        招投标信息.招投标项目名称 = projectObj.constructName;
        招投标信息.招投标单位编码 = '';
        招投标信息.招投标单位名称 = '';
        招投标信息.发包人 =type==1?'': projectObj.constructProjectJBXX.find(item=>item.name =='招标人(发包人)').remark //取 工程项目项目层级-工程基本信息-招标人(发包人)
        招投标信息.发包人法定代表人 = type==1?'':projectObj.constructProjectJBXX.find(item=>item.name =='招标人(发包人)法人或其授权人').remark
        招投标信息.承包人 = type ==1? projectObj.constructProjectJBXX.find(item=>item.name =='投标人(承包人)').remark:''
        招投标信息.承包人法定代表人 = type ==1?projectObj.constructProjectJBXX.find(item=>item.name =='投标人(承包人)法人或其授权人').remark:''
        招投标信息.工程造价咨询人 = type==1?'':projectObj.constructProjectJBXX.find(item=>item.name =='招标人(发包人)法人或其授权人').remark
        招投标信息.编制人 = type==1?'':projectObj.constructProjectJBXX.find(item=>item.name =='编制人').remark
        招投标信息.核对人 = type==1?'':projectObj.constructProjectJBXX.find(item=>item.name =='核对人(复核人)').remark
        招投标信息.编制日期 =type==1?'': projectObj.constructProjectJBXX.find(item=>item.name =='编制时间').remark
        招投标信息.委托人 =type==1?'': projectObj.constructProjectJBXX.find(item=>item.name =='招标人(发包人)法人或其授权人').remark
        招投标信息.计价软件服务商 = '云算房智能造价平台（服务商：河北宏盛建通信息技术有限公司）';
        招投标信息.计价软件产品版本 = projectObj.version;
        招投标信息.网卡地址 = this.service.commonService.getMacAddress();
        工程数据.招投标信息 = 招投标信息
        if(ObjectUtils.isEmpty(global.microDog)){
            招投标信息.计价软件加密锁号 = ''
        }else {
            招投标信息.计价软件加密锁号 = global.microDog.serial
        }


        let 工程项目 = {}
        工程项目.项目编码 = projectObj.constructCode
        工程项目.项目名称 = projectObj.constructName
        工程项目.编制说明 =await this.keepSpecificTags(projectObj.organizationInstructions.context, ['br']);
        工程项目.填表须知 ='1  工程量清单与计价格式中所有要求签字、盖章的地方，必须由规定的单位和人员签字、盖章。\n' +
            '2  工程量清单与计价格式中除另有规定外，任何内容不得修改。\n' +
            '3  工程量清单与计价格式中要求填报的单价和合价，投标人均应填报，未填报的单价与合价，视为此项费用已包含在工程量清单的其他单价与合价中。\n' +
            '4  金额（价格）以招标文件规定的币种表示。'
        工程项目.报价说明 = await this.keepSpecificTags(projectObj.organizationInstructions.context, ['br']);
        let zjfx = await this.countZjfx({constructId:projectObj.sequenceNbr,levelType:1,type:this.type})
        工程项目.金额 =zjfx.gczj
        工程项目.其中安全生产文明施工费 =zjfx.safeFee
        工程项目.其中规费 =zjfx.gfee
        工程项目.其中设备费及相关费用 =zjfx.sbfsj
        工程项目.其中工程费 =zjfx.qzgcf
        工程项目.其中措施费 =zjfx.qzcsf
        工程项目.其中单价措施费 =zjfx.djcsxhj
        工程项目.其中总价措施费 =zjfx.zjcsxhj
        工程项目.计税方式 =taxCalculation.taxCalculationMethod == '1'?'增值税方式_一般计税':'增值税方式_简易计税';
        工程项目.地区标准 =''


        //单项工程

        //工程项目 工程属性
        await this.convertGcxmGcsx(工程项目,projectObj);


         //对应不同的新建预算项目
        if(projectObj.biddingType!== 2){
            await this.convertDxgcxx(工程项目,projectObj.singleProjects)
        }else {
            await this.convertDWGXDwgcxx(工程项目,projectObj.unitProject);
        }



        this.rcjArray =[];
        工程数据.工程项目 = 工程项目
        json.工程数据 = 工程数据
        return json;
    }

    keepSpecificTags(html, allowedTags = []) {
        if(ObjectUtils.isEmpty(html)) return ''
        if (!allowedTags.length) return html.replace(/<[^>]+>/g, ''); // 不保留任何标签时清空

        const regex = new RegExp(`<(?!\/?(${allowedTags.join('|')})\\b)[^>]+>`, 'gi');
        return html.replace(regex, '');
    }
    //造价分析
    async countZjfx(args){
        let zjfxArray = await this.service.unitProjectService.getCostAnalysisData(args);
        if (ObjectUtils.isEmpty(zjfxArray.costAnalysisConstructVOList) && ObjectUtils.isEmpty(zjfxArray.costAnalysisUnitVOList)&& ObjectUtils.isEmpty(zjfxArray.costAnalysisSingleVOList)){
            return {};
        }else {
            let gczj = 0;
            let safeFee = 0;
            let gfee = 0;
            let sbfsj = 0;
            let qzgcf = 0;//其中工程费 工程项目-造价分析-合计行-金额列 减去 工程项目-造价分析-合计行-设备费及其税金列
            let qzcsf = 0; //其中措施费 取 工程项目-造价分析-合计行-措施项目列 加上 工程项目-造价分析-合计行-安全生产、文明施工费列
            let djcsxhj = 0;
            let zjcsxhj = 0;
            if(args.type !==0){
                //招标文件时固定为 0所以不進來
                if(args.levelType == 1){
                    for (let i = 0; i < zjfxArray.costAnalysisConstructVOList.length; i++) {
                        let costAnalysisConstructVO = zjfxArray.costAnalysisConstructVOList[i];
                        gczj = NumberUtil.add(gczj,costAnalysisConstructVO.gczj)
                        safeFee = NumberUtil.add(safeFee,costAnalysisConstructVO.safeFee)
                        gfee = NumberUtil.add(gfee,costAnalysisConstructVO.gfee)
                        sbfsj = NumberUtil.add(sbfsj,costAnalysisConstructVO.sbfsj)
                        qzgcf = NumberUtil.add(qzgcf,NumberUtil.subtract(costAnalysisConstructVO.gczj, costAnalysisConstructVO.sbfsj))
                        qzcsf =NumberUtil.addParams(qzcsf,costAnalysisConstructVO.djcsxhj,costAnalysisConstructVO.zjcsxhj,costAnalysisConstructVO.safeFee)
                        djcsxhj = NumberUtil.add(djcsxhj,costAnalysisConstructVO.djcsxhj)
                        zjcsxhj = NumberUtil.add(zjcsxhj,costAnalysisConstructVO.zjcsxhj)
                    }
                }else if(args.levelType == 2){
                    if(zjfxArray.costAnalysisSingleVOList){
                        let costAnalysisSingleVO = zjfxArray.costAnalysisSingleVOList
                        gczj = NumberUtil.add(gczj,costAnalysisSingleVO.gczj)
                        safeFee = NumberUtil.add(safeFee,costAnalysisSingleVO.safeFee)
                        gfee = NumberUtil.add(gfee,costAnalysisSingleVO.gfee)
                        sbfsj = NumberUtil.add(sbfsj,costAnalysisSingleVO.sbfsj)
                        qzgcf = NumberUtil.add(qzgcf,NumberUtil.subtract(costAnalysisSingleVO.gczj, costAnalysisSingleVO.sbfsj))
                        qzcsf =NumberUtil.addParams(qzcsf,costAnalysisSingleVO.djcsxhj,costAnalysisSingleVO.zjcsxhj,costAnalysisSingleVO.safeFee)
                        djcsxhj = NumberUtil.add(djcsxhj,costAnalysisSingleVO.djcsxhj)
                        zjcsxhj = NumberUtil.add(zjcsxhj,costAnalysisSingleVO.zjcsxhj)
                    }

                }else {
                    args.levelType= 2
                    zjfxArray = await this.service.unitProjectService.getCostAnalysisData(args)
                    let unitZjfx =zjfxArray.costAnalysisSingleVOList.childrenList.find(item =>item.sequenceNbr == args.unitId)
                    gczj    = unitZjfx.gczj??gczj
                    safeFee = unitZjfx.safeFee??safeFee
                    gfee    = unitZjfx.gfee??gfee
                    sbfsj   = unitZjfx.sbfsj??sbfsj
                    qzgcf   = NumberUtil.subtract(unitZjfx.gczj, unitZjfx.sbfsj)
                    qzcsf   = NumberUtil.addParams(unitZjfx.djcsxhj,unitZjfx.zjcsxhj,unitZjfx.safeFee)
                    djcsxhj = unitZjfx.djcsxhj??djcsxhj
                    zjcsxhj = unitZjfx.zjcsxhj??djcsxhj
                }
            }

            let res ={
                gczj : gczj,
                safeFee : safeFee,
                gfee : gfee,
                sbfsj : sbfsj,
                qzgcf :qzgcf,
                qzcsf : qzcsf,
                djcsxhj : djcsxhj,
                zjcsxhj :zjcsxhj
            }
            return res;
        }

    }



    //单位工程xx
    async convertDxgcxx(json, singleProjects) {
        if (ObjectUtils.isEmpty(singleProjects)) {
            return;
        }

        let dxgcxxArray = new Array();

        for (let singleProject of singleProjects) {
            const zjfx = await this.countZjfx({
                constructId: singleProject.constructId,
                singleId: singleProject.sequenceNbr,
                levelType: 2,
                type: this.type
            });

            let 当前单项工程 = {
                编码: singleProject.projectCode,
                名称: singleProject.projectName,
                金额: zjfx.gczj,
                其中安全生产文明施工费: zjfx.safeFee,
                其中规费: zjfx.gfee,
                其中设备费及相关费用: zjfx.sbfsj,
                其中工程费: zjfx.qzgcf,
                其中措施费: zjfx.qzcsf,
                其中单价措施费: zjfx.djcsxhj,
                其中总价措施费:NumberUtil.add( zjfx.zjcsxhj,zjfx.safeFee)
            };

            if (!ObjectUtils.isEmpty(singleProject.subSingleProjects)) {
                // 递归处理子项目
                let 子项容器 = {};
                await this.convertDxgcxx(子项容器, singleProject.subSingleProjects);
                当前单项工程.单项工程 = 子项容器.单项工程;
            } else {
                // 处理单位工程
                await this.convertDwgcxx(当前单项工程, singleProject.unitProjects);
            }

            dxgcxxArray.push(当前单项工程);
        }

        json.单项工程 = dxgcxxArray;
    }

    /**
     * 工程项目 基本属性
     * @param 工程项目
     * @param projectObjById
     * @returns {Promise<void>}
     */
    async convertGcxmGcsx(工程项目,projectObjById){
        if (ObjectUtils.isEmpty(projectObjById)){
            return;
        }
        let gcsx ={};
        工程项目.工程属性 = {};
        if (!ObjectUtils.isEmpty(projectObjById.constructProjectJBXX)){
            let projectOverview = projectObjById.constructProjectJBXX;
            let zm = new Array();
            for (let projectOverviewElement of projectOverview) {
                let gcsxzm = {};
                gcsxzm.序号 = projectOverviewElement.dispNo;
                gcsxzm.编码 = "";
                gcsxzm.名称 = projectOverviewElement.name;
                gcsxzm.属性值 = projectOverviewElement.remark;
                zm.push(gcsxzm);
            }
            工程项目.工程属性.工程属性子目 = zm;
        }
    }

    // async  convertDxgcxx(singleProject, json = { 单项工程: [] }) {
    //     // 创建 单项工程 对象并填充数据
    //     const 单项工程 = {
    //         Dxgcbh: singleProject.projectCode,
    //         Dxgcmc: singleProject.projectName,
    //         Je: singleProject.total,
    //         Gf: singleProject.gfee,
    //         Aqwmf: singleProject.safeFee,
    //         SbfSJ: singleProject.sbfTax
    //     };
    //
    //     // 如果存在子项目，递归调用转换函数
    //     if (!ObjectUtils.isEmpty(singleProject.subSingleProjects)) {
    //         const subDxgcxxArray = await Promise.all(singleProject.subSingleProjects.map(async (subProject) =>
    //             await this.convertDxgcxx(subProject)
    //         ));
    //         单项工程.subDxgcxxArray = subDxgcxxArray;
    //     }
    //
    //     // 如果有关联的项目，处理它们
    //     if (!ObjectUtils.isEmpty(singleProject.unitProjects)) {
    //         await this.convertDwgcxx(单项工程, singleProject.unitProjects);
    //     }
    //
    //     // 将当前项目的 单项工程 对象添加到结果数组中
    //     json.单项工程.push(单项工程);
    //
    //     // 返回更新后的 json 对象
    //     return json;
    // }

    /**
     * 转换专业类别
     * 映射规则
     12:
     单位工程清单专业 为“建筑工程” 取 1
     清单专业 为“装饰工程” 取 2
     清单专业 为“安装工程” 取 3
     清单专业 为“市政工程” 且定额册选择为“2012-SZGC-DEK” 取 4
     清单专业 为“园林绿化工程” 且定额册选择为“2013-YLLH-DEK” 取 5
     清单专业 为 “市政工程” 且定额册选择为“2013-SZSS-DEX” 取 8
     清单专业 为“仿古建筑工程” 且定额册选择“2013-FGJZ-DEG” 取 9
     清单专业 为“园林绿化工程” 且定额册选择为“2014-YLLHYH-DEY” 取10
     清单专业 为“仿古建筑工程” 且定额册选择为“2014-GJXSGC-DEG” 取 11
     其他取 15

     22：
     单位工程清单专业 为 “建筑工程” 取 1
     清单专业为 “装饰装修工程” 取 2
     清单专业为“安装工程” 取 3
     清单专业为“市政工程” 取 4
     其他取 15  后续定额册更新后 再次补充映射规则
     */
    convertZylb(constructMajorType,mainDeLibrary){

        switch (constructMajorType) {
            case '建筑工程':{
                return 1
                break;
            }
            case '装饰工程':{
                return 2
                break;
            }
            case '安装工程':{
                return 3
                break;
            }
            case '市政工程':{
                if("2013-SZSS-DEX"==mainDeLibrary){
                    return 8
                }else {
                    return 4
                }
                break;
            }
            case '园林绿化工程':{
                if("2013-YLLH-DEK"==mainDeLibrary){
                    return 5
                }else  if("2014-YLLHYH-DEY"==mainDeLibrary){
                    return 10
                }else {
                    return 15
                }
                break;
            }
            case '仿古建筑工程':{
                if("2013-FGJZ-DEG"==mainDeLibrary){
                    return 9
                }else  if("2014-GJXSGC-DEG"==mainDeLibrary){
                    return 11
                }else {
                    return 15
                }
                break;
            }
        }
    }



    /**
     * 单位工程
     * @param 单项工程
     * @param unitProjects
     * @returns {Promise<void>}
     */
    async convertDwgcxx(单项工程,unitProjects){
        if(ObjectUtils.isEmpty(unitProjects)){
            return;
        }
        let dwgcxxArray = new Array();
        for (let i = 0; i < unitProjects.length; i++) {
            let unitProject = unitProjects[i];
            //人材机汇总查询
            this.rcjArray =await this.service.rcjProcess.queryConstructRcjByDeIdNew(3, 0, unitProject.constructId, unitProject.spId,unitProject.sequenceNbr);
            // 获取单位下人材机list
            let rcjList =await PricingFileFindUtils.getRcjList(unitProject.constructId, unitProject.spId,unitProject.sequenceNbr);

            //人材机汇总：主要材料、设备表
            this.zyclsbZysbclArray = await this.service.rcjProcess.queryConstructRcjByDeIdNew(3, 7, unitProject.constructId, unitProject.spId,unitProject.sequenceNbr,null);

            this.RcjMap =await this.convertRcjMap(rcjList);
            let zjfx= await this.countZjfx({constructId:unitProject.constructId,singleId:unitProject.spId,unitId:unitProject.sequenceNbr,levelType:3,type: this.type})

            let 单位工程 = {};
            单位工程.编码 = unitProject.upCode ;
            单位工程.名称 = unitProject.upName ;
            单位工程.金额 = zjfx.gczj;
            单位工程.其中安全文明费 =zjfx.safeFee ;
            单位工程.其中规费 = zjfx.gfee;
            单位工程.其中设备费及相关费用 = zjfx.sbfsj;
            单位工程.其中工程费 =zjfx.qzgcf
            单位工程.其中措施费 =zjfx.qzcsf
            单位工程.其中单价措施费 =zjfx.djcsxhj
            单位工程.其中总价措施费 =NumberUtil.add(zjfx.zjcsxhj,zjfx.safeFee)


            //分部分项工程
            await this.convertQdxm(单位工程,unitProject);
            //
            //
            //措施项目
            await this.convertCsxm(单位工程,unitProject);

            //工程属性
            await this.convertGcsx(单位工程,unitProject);
            //
            //
            // //Qtxm
            await this.convertQtxm(单位工程,unitProject,unitProject.feeFiles);

            //招标人供应材料设备明细表
            await this.convertZbrClSb(单位工程,unitProject);

            //主要材料设备明细表
            await this.convertZyClSb(单位工程,unitProject);
            //增值税进项税额计算汇总表
            await this.convertZzsjxshzb(单位工程,unitProject.inputTaxDetails)
            //单位工程费汇总表
            await this.convertFywj(单位工程,unitProject.unitCostSummarys,unitProject.unitCostCodePrices)

            //人材机汇总表
            await this.convertRcjhz(单位工程,unitProject);
            //Aqwmsgf
            await this.convertAqwmsgf(单位工程,unitProject);

            //Gf
            await this.convertGf(单位工程,unitProject);




            dwgcxxArray.push(单位工程)

        }
        单项工程.单位工程 = dwgcxxArray;

    }

    //xxx单位工程信息
    async convertDWGXDwgcxx(单项工程,unitProject){

        //人材机汇总查询
        this.rcjArray =await this.service.rcjProcess.queryConstructRcjByDeIdNew(3, 0, unitProject.constructId, unitProject.spId,unitProject.sequenceNbr);
        // 获取单位下人材机list
        let rcjList =await PricingFileFindUtils.getRcjList(unitProject.constructId, unitProject.spId,unitProject.sequenceNbr);

        //人材机汇总：主要材料、设备表
        this.zyclsbZysbclArray = await this.service.rcjProcess.queryConstructRcjByDeIdNew(3, 7, unitProject.constructId, unitProject.spId,unitProject.sequenceNbr,null);

        this.RcjMap =await this.convertRcjMap(rcjList);
        let zjfx= await this.countZjfx({constructId:unitProject.constructId,singleId:unitProject.spId,unitId:unitProject.sequenceNbr,levelType:3,type: this.type})

        let 单位工程 = {};
        单位工程.编码 = unitProject.upCode ;
        单位工程.名称 = unitProject.upName ;
        单位工程.金额 = zjfx.gczj;
        单位工程.其中安全文明费 =zjfx.safeFee ;
        单位工程.其中规费 = zjfx.gfee;
        单位工程.其中设备费及相关费用 = zjfx.sbfsj;
        单位工程.其中工程费 =zjfx.qzgcf
        单位工程.其中措施费 =zjfx.qzcsf
        单位工程.其中单价措施费 =zjfx.djcsxhj
        单位工程.其中总价措施费 =zjfx.zjcsxhj


        //分部分项工程
        await this.convertQdxm(单位工程,unitProject);
        //
        //
        //措施项目
        await this.convertCsxm(单位工程,unitProject);

        //工程属性
        await this.convertGcsx(单位工程,unitProject);
        //
        //
        // //Qtxm
        await this.convertQtxm(单位工程,unitProject,unitProject.feeFiles);

        //招标人供应材料设备明细表
        await this.convertZbrClSb(单位工程,unitProject);

        //主要材料设备明细表
        await this.convertZyClSb(单位工程,unitProject);
        //增值税进项税额计算汇总表
        await this.convertZzsjxshzb(单位工程,unitProject.inputTaxDetails)
        //单位工程费汇总表
        await this.convertFywj(单位工程,unitProject.unitCostSummarys,unitProject.unitCostCodePrices)

        //人材机汇总表
        await this.convertRcjhz(单位工程,unitProject);
        //Aqwmsgf
        await this.convertAqwmsgf(单位工程,unitProject);

        //Gf
        await this.convertGf(单位工程,unitProject);


        单项工程.单位工程 = 单位工程;

    }

    //单位工程费用汇总
    async convertFywj(单位工程,unitCostSummarys,unitCostCodePrices){

        if(ObjectUtils.isEmpty(unitCostSummarys)){
            return;
        }
        let 单位工程费汇总表 ={};
        let 单位工程费用子目Array = new Array();
        for (let i = 0; i < unitCostSummarys.length; i++) {

            let unitCostSummary = unitCostSummarys[i];
            let 单位工程费用子目 ={};

            单位工程费用子目.序号 = unitCostSummary.dispNo  ;
            单位工程费用子目.费用编码 = unitCostSummary.code  ;
            单位工程费用子目.费用名称 = unitCostSummary.name  ;
            单位工程费用子目.计算基础 = unitCostSummary.calculateFormula  ;
            单位工程费用子目.计算基础说明 = unitCostSummary.instructions  ;
            单位工程费用子目.费率 = ObjectUtils.isEmpty(unitCostSummary.rate)?'100':unitCostSummary.rate  ;
            单位工程费用子目.金额 = await this.dualZBType(unitCostSummary.price)
            单位工程费用子目.其中人工费 = '0'  ;
            单位工程费用子目.其中材料费 = '0' ;
            单位工程费用子目.其中机械费 = '0'  ;
            if('分部分项工程量清单计价合计' ==unitCostSummary.name){
                单位工程费用子目.其中人工费 = await this.dualZBType(unitCostCodePrices.find(item => item.code == 'RGF').price)
                单位工程费用子目.其中材料费 = await this.dualZBType(unitCostCodePrices.find(item => item.code == 'CLF').price)
                单位工程费用子目.其中机械费 = await this.dualZBType(unitCostCodePrices.find(item => item.code == 'JXF').price)
            }
            if('措施项目清单计价合计' ==unitCostSummary.name){
                单位工程费用子目.其中人工费 = await this.dualZBType(NumberUtil.add(unitCostCodePrices.find(item => item.code == 'DJCS_RGF').price,unitCostCodePrices.find(item => item.code == 'QTZJCS_RGF').price));
                单位工程费用子目.其中材料费 = await this.dualZBType(NumberUtil.add(unitCostCodePrices.find(item => item.code == 'DJCS_CLF').price,unitCostCodePrices.find(item => item.code == 'QTZJCS_CLF').price));
                单位工程费用子目.其中机械费 = await this.dualZBType(NumberUtil.add(unitCostCodePrices.find(item => item.code == 'DJCS_JXF').price,unitCostCodePrices.find(item => item.code == 'QTZJCS_JXF').price));
            }
            if('单价措施项目工程量清单计价合计' ==unitCostSummary.name){
                单位工程费用子目.其中人工费 = await this.dualZBType(unitCostCodePrices.find(item => item.code == 'DJCS_RGF').price)
                单位工程费用子目.其中材料费 = await this.dualZBType(unitCostCodePrices.find(item => item.code == 'DJCS_CLF').price)
                单位工程费用子目.其中机械费 = await this.dualZBType(unitCostCodePrices.find(item => item.code == 'DJCS_JXF').price)
            }
            if('其他总价措施项目清单计价合计' ==unitCostSummary.name){
                单位工程费用子目.其中人工费 = await this.dualZBType(unitCostCodePrices.find(item => item.code == 'QTZJCS_RGF').price)
                单位工程费用子目.其中材料费 = await this.dualZBType(unitCostCodePrices.find(item => item.code == 'QTZJCS_CLF').price)
                单位工程费用子目.其中机械费 = await this.dualZBType(unitCostCodePrices.find(item => item.code == 'QTZJCS_JXF').price)
            }
            单位工程费用子目.费用类别 =unitCostSummary.type;
            单位工程费用子目.备注 = unitCostSummary.remark


            单位工程费用子目Array.push(单位工程费用子目)
        }
        单位工程费汇总表.单位工程费用子目 = 单位工程费用子目Array;

        单位工程.单位工程费汇总表 = 单位工程费汇总表;

    }



    //分部分项清单计价表
    async convertQdxm(单位工程, unitProject) {
        //获取单位工程所有人材机
        let itemBillProjects = unitProject.itemBillProjects;
        let rcjArray = this.rcjArray;
        let 分部分项工程 = {}
        分部分项工程.金额 = 0
        if(ObjectUtils.isEmpty(itemBillProjects)){
            单位工程.分部分项工程 =分部分项工程;
            return
        }

        let dwgc = itemBillProjects.find(item => item.kind === '0');
        let bt = null;
        if (ObjectUtils.isNotEmpty(dwgc)) {
            //获取dwgc的所有标题(可能会出现没有标题只有清单的情况)
            分部分项工程.金额 = await this.dualZBType(dwgc.total??0)
            bt = itemBillProjects.filter(item => item.parentId === dwgc.sequenceNbr);
        }

        if(ObjectUtils.isEmpty(bt)){
            单位工程.分部分项工程 =分部分项工程;
            return
        }

        const createItemBillProjects = async (QdBtArray,bt) =>{

            if(bt[0].kind === '03'){
                //(需要考虑没有分布的情况)此处就是清单
                await this.convertFbFxQdDeRcj(itemBillProjects,rcjArray,bt,分部分项工程,unitProject)

            }else {
                //有分布
                for (let i = 0; i < bt.length; i++) {
                    let 分部子目 ={};
                    let btElement = bt[i];

                    分部子目.编码 = btElement.bdCode;
                    分部子目.名称 = btElement.name;
                    分部子目.金额 = await this.dualZBType(btElement.total) ;

                    // 标题下的第一层
                    let filter1 = itemBillProjects.filter(item => item.parentId === btElement.sequenceNbr);

                    if (!ObjectUtils.isEmpty(filter1)){
                        //判断当前层为标题还是清单
                        let filter2 = filter1.filter(item =>{

                            return (item.kind === '01' ||  item.kind === '02')
                        } );
                        if(!ObjectUtils.isEmpty(filter2)){
                            //标题
                            let  QdBtArray = new Array()
                            let QdBtArrayS = await createItemBillProjects(QdBtArray,filter2);
                            分部子目.分部子目 = QdBtArrayS;

                        }else {
                            await this.convertFbFxQdDeRcj(itemBillProjects,rcjArray,filter1,分部子目,unitProject)
                        }

                    }
                    QdBtArray.push(分部子目)
                }

                return QdBtArray;
            }

        }

        let QdBtArray = new Array()
        分部分项工程.分部子目 = await createItemBillProjects(QdBtArray,bt);
        if (ObjectUtils.isEmpty(分部分项工程.分部子目)){
            delete 分部分项工程.分部子目;
        }
        单位工程.分部分项工程 =分部分项工程;
    }


    //措施项目
    async convertCsxm(单位工程, unitProject) {
        //获取单位工程所有人材机
        let rcjArray =this.rcjArray;

        let measureProjectTables = unitProject.measureProjectTables;
        let 措施项目 = {}
        措施项目.金额 =await this.dualZBType(NumberUtil.add( unitProject.csxhj,unitProject.safeFee))
        措施项目.其中单价措施费 = await this.dualZBType(unitProject.djcsxhj)
        措施项目.其中总价措施费 = await this.dualZBType(NumberUtil.add(unitProject.zjcsxhj,unitProject.safeFee))
        if(ObjectUtils.isEmpty(measureProjectTables)){
            单位工程.措施项目 =措施项目;
            return
        }
        let csxm = measureProjectTables.find(item => item.kind === '0');
        //获取csxm下的所有标题（）
        let bt = measureProjectTables.filter(item => item.parentId === csxm.sequenceNbr);
        if(ObjectUtils.isEmpty(bt)){
            return
        }



        let zjCsBtArray = new Array();

        let awfBtArray =[]



        let djCsBtArray = new Array();



        //存放其他总价措施清单
        let QtZjCsQd = new Array();


        for (let i = 0; i < bt.length; i++) {
            let btElement = bt[i];
            let qdArray = measureProjectTables.filter(item => item.parentId === btElement.sequenceNbr);
            if(  btElement.constructionMeasureType === 3 ) {// 1单价措施 2安文费 3其他总价措施

                //3其他总价措施
                let 措施标题 = {};

                措施标题.序号 = '';
                措施标题.编码 = btElement.fxCode;
                措施标题.名称 = '其他总价措施';
                措施标题.金额 =  await this.dualZBType(btElement.total);
                if(!ObjectUtils.isEmpty(qdArray)){
                    if(qdArray[0].kind ==='02'){

                        for (let j = 0; j < qdArray.length; j++) {
                            let ZjCsMxArray =[]

                            let fb = qdArray[j];

                            //查询单价措施项目下的清单
                            let qdS = measureProjectTables.filter(item => item.parentId === fb.sequenceNbr);
                            if (!ObjectUtils.isEmpty(qdS)){

                                await this.convertQTZJCsQdDeRcj(qdS, unitProject, measureProjectTables, ZjCsMxArray, 措施标题,false,false);

                            }
                        }
                    }else {
                        let ZjCsMxArray =[]
                        await this.convertQTZJCsQdDeRcj(qdArray, unitProject, measureProjectTables, ZjCsMxArray, 措施标题,false,false);

                    }
                }
                zjCsBtArray.push(措施标题)
            }else if(btElement.constructionMeasureType === 2){

                //2安文费
                let 措施标题 = {};
                措施标题.序号 = '';
                措施标题.编码 = btElement.fxCode;
                措施标题.名称 = btElement.name;
                措施标题.金额 = await this.dualZBType(btElement.total);

                let 措施子目清单Array = []
                if(!ObjectUtils.isEmpty(qdArray)){
                    if(qdArray[0].kind ==='02'){
                        //分布

                        let awfQd = new Array();
                        for (let j = 0; j < qdArray.length; j++) {

                            let fb = qdArray[j];

                            //查询单价措施项目下的清单
                            let qdS = measureProjectTables.filter(item => item.parentId === fb.sequenceNbr);
                            if (!ObjectUtils.isEmpty(qdS)){
                                for (let k = 0; k < qdS.length; k++) {
                                    awfQd.push(qdS[k]);
                                }
                            }

                        }
                        await this.convertQTZJCsQdDeRcj(awfQd, unitProject, measureProjectTables, 措施子目清单Array, 措施标题,true,false);
                    }else {
                        //清单
                        await this.convertQTZJCsQdDeRcj(qdArray, unitProject, measureProjectTables, 措施子目清单Array, 措施标题,true,false);
                    }
                }
                awfBtArray.push(措施标题);
            }else if(btElement.constructionMeasureType === 1){
                //单价措施
                let 措施标题 ={}
                措施标题.序号 = btElement.dispNo;
                措施标题.编码 = btElement.fxCode;
                措施标题.名称 = btElement.name;
                措施标题.金额 =  await this.dualZBType(btElement.total);
                djCsBtArray.push(措施标题)
                let 措施子目清单Array = []
                if(!ObjectUtils.isEmpty(qdArray)){
                    if(qdArray[0].kind ==='02'){
                        //分布


                        let dcjsSonBtArray = []
                        for (let j = 0; j < qdArray.length; j++) {
                            措施子目清单Array = []
                            let 措施标题1 = {};
                            let fb = qdArray[j];
                            措施标题1.序号 = fb.dispNo;
                            措施标题1.编码 = fb.fxCode;
                            措施标题1.名称 = fb.name;
                            措施标题1.金额 = await this.dualZBType(ObjectUtils.isEmpty(fb.total)?'0':fb.total);

                            //查询单价措施项目下的清单
                            let qdS = measureProjectTables.filter(item => item.parentId === fb.sequenceNbr);
                            if (!ObjectUtils.isEmpty(qdS)){
                                // await this.convertDJCsQdDeRcj(measureProjectTables,rcjArray,qdS,措施标题1,unitProject)

                                await this.convertQTZJCsQdDeRcj(qdS, unitProject, measureProjectTables, 措施子目清单Array, 措施标题1,false,true);
                            }

                            dcjsSonBtArray.push(措施标题1);
                        }
                        措施标题.措施标题 = dcjsSonBtArray
                    }else {
                        //清单
                        // await this.convertDJCsQdDeRcj(measureProjectTables,rcjArray,qdArray,措施标题,unitProject)
                        await this.convertQTZJCsQdDeRcj(qdArray, unitProject, measureProjectTables, 措施子目清单Array, 措施标题,false,true);
                    }
                }



            }

        }

        // if (!ObjectUtils.isEmpty(QtZjCsQd)){
        //     let ZjCsMxArray = new Array();
        //     await this.convertQTZJCsQdDeRcj(QtZjCsQd, unitProject, measureProjectTables, ZjCsMxArray, 措施标题,false,false);
        // }

        let awfZjcsBtArray = [...awfBtArray,...zjCsBtArray];//合并安文费清单和总价措施清单

        //新增 总价措施标题层级
        let zjcsbt = {
            序号: "",
            编码: "",
            名称: "总价措施项目",
            金额: 措施项目.其中总价措施费
        };

        zjcsbt.措施标题 = awfZjcsBtArray;

        措施项目.措施标题 = [zjcsbt,...djCsBtArray];

        单位工程.措施项目 =措施项目;


    }


    /**
     * 工程属性
     * @param 单位工程
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertGcsx(单位工程,unitProject){
        let args ={
            levelType: 3,
            constructId: unitProject.constructId,
            singleId: unitProject.spId,
            unitId: unitProject.sequenceNbr,
            type: this.type
        }
        //造价分析
        let zjfx= await this.service.unitProjectService.getCostAnalysisData({constructId:unitProject.constructId,singleId:unitProject.spId,unitId:unitProject.sequenceNbr,levelType:3,type: this.type})
        let res = await this.service.baseFeeFileService.feeCollectionData(args);
        let unitFeeDescriptionList = res.unitFeeDescriptionList
        let 基本属性 ={}
        基本属性.工程类别 = this.dualDropdown( unitFeeDescriptionList.find(item=>item.name =='工程类别').optionList,  unitFeeDescriptionList.find(item=>item.name =='工程类别').context)
        if(   (this.dualDropdown( unitFeeDescriptionList.find(item=>item.name =='工程所在地').optionList,  unitFeeDescriptionList.find(item=>item.name =='工程所在地').context))   !='非市区、县城、镇'){
            基本属性.工程所在地区 ='市区县城区域'
        }else {
            基本属性.工程所在地区 ='非市区县城区域'
        }

        //1. 取单位工程造价分析中的建筑面积值
        // 2. 若单位工程中建筑面积值为空或0，需要根据取费表中建筑面积映射处理：
        // 2.1 选择10000㎡以上，展示为11000；
        // 2.2 选择10000㎡以下，展示为9000
        let zjfxJzmj =zjfx.costAnalysisUnitVOList.find(item =>item.name =='建筑面积(m、㎡)').context
        if(ObjectUtils.isNotEmpty(zjfxJzmj) && zjfxJzmj !='0'){
            基本属性.建筑面积 =zjfxJzmj
        }else {
            let  qfbJzmj=  this.dualDropdown( unitFeeDescriptionList.find(item=>item.name =='建筑面积').optionList,  unitFeeDescriptionList.find(item=>item.name =='建筑面积').context)
            if(qfbJzmj == '10000㎡以上'){
                基本属性.建筑面积 = 11000
            }else {
                基本属性.建筑面积 = 9000
            }
        }
        // 基本属性.工程所在地区 = this.dualDropdown( unitFeeDescriptionList.find(item=>item.name =='工程所在地').optionList,  unitFeeDescriptionList.find(item=>item.name =='工程所在地').context)
        // 基本属性.建筑面积 = this.dualDropdown( unitFeeDescriptionList.find(item=>item.name =='建筑面积').optionList,  unitFeeDescriptionList.find(item=>item.name =='建筑面积').context)
        基本属性.工程造价 = unitFeeDescriptionList.find(item=>item.name =='市政工程造价')? this.dualDropdown( unitFeeDescriptionList.find(item=>item.name =='市政工程造价').optionList,  unitFeeDescriptionList.find(item=>item.name =='市政工程造价').context):'5000万元以下'
        let ljms = this.dualDropdown( unitFeeDescriptionList.find(item=>item.name =='临路面数').optionList,  unitFeeDescriptionList.find(item=>item.name =='临路面数').context)
        基本属性.临路面数 = ljms  =='不临路'?0:ljms.replace('面','')
        基本属性.施工地点 = ''
        if(unitProject.projectTaxCalculation.taxPayingRegion == '市区'){
            基本属性.纳税地区 = '市区区域'
        }else if(unitProject.projectTaxCalculation.taxPayingRegion == '县城' ||unitProject.projectTaxCalculation.taxPayingRegion == '镇'){
            基本属性.纳税地区 = '县城镇区域'
        }else if(unitProject.projectTaxCalculation.taxPayingRegion == '不在市区、县、城镇'){
            基本属性.纳税地区 = '其他区域'
        }

        基本属性.清单计价规则 = '建设工程工程量清单计价规范_2013'
        基本属性.定额计价规则 = this.getConsumptionStandard(unitProject.deStandardReleaseYear,unitProject.constructMajorType)
        let 工程属性 = {}
        工程属性.基本属性 = 基本属性
        工程属性.自定义属性列表 = {}
        单位工程.工程属性 = 工程属性
    }


     getConsumptionStandard(year, specialty) {
        const standards = {
            "22": {
                "建筑工程": {
                    "code": "61",
                    "name": "河北省建筑工程消耗量标准及计算规则(2022)"
                },
                "装饰装修工程": {
                    "code": "62",
                    "name": "河北省建筑工程消耗量标准及计算规则(2022)"
                },
                "安装工程": {
                    "code": "63",
                    "name": "河北省建筑工程消耗量标准及计算规则(2022)"
                },
                "市政工程": {
                    "code": "64",
                    "name": "河北省市政工程消耗量标准及计算规则(2022)"
                },
                "园林绿化工程": {
                    "code": "0",
                    "name": "其他"
                }
            },
            "12": {
                "建筑工程": {
                    "code": "51",
                    "name": "河北省建筑工程消耗量定额(2012)"
                },
                "装饰工程": {
                    "code": "52",
                    "name": "河北省建筑工程消耗量定额(2012)"
                },
                "仿古工程": {
                    "code": "55",
                    "name": "河北省仿古建筑工程消耗量定额(2013)"
                },
                "安装工程": {
                    "code": "53",
                    "name": "河北省建筑工程消耗量定额(2012)"
                },
                "市政工程": {
                    "code": "54",
                    "name": "河北省市政工程消耗量定额(2012)"
                },
                "园林绿化工程": {
                    "code": "56",
                    "name": "河北省园林绿化工程消耗量定额(2013)"
                }
            }
        };

        // 检查年份是否存在
        if (!standards[year]) {
            return "其他";
        }

        // 检查专业是否存在，如果不存在则返回"其他"
        const specialtyData = standards[year][specialty];
        return specialtyData ? specialtyData.name : "其他";
    }

    /**
     * 处理下拉
     * @param dataArray
     * @param targetKey
     */
    dualDropdown(dataArray,targetKey){
        let result = null;
        for (const item of dataArray) {
            if (item.hasOwnProperty(targetKey)) {
                result = item[targetKey];
                break;
            }
        }
        return result
    }

    //总价措施基数
    async connertZjcsJs(value){
        if(CalculateBaseType.RGFSCJ_JXFSCJ == value){
            return '人工费市场价+机械费市场价'
        }

        if(CalculateBaseType.RGFDEJ_JXFDEJ == value){
            return '人工费定额价+机械费定额价'
        }
        return '';
    }

    //其他总价措施清单的人材机
    async convertQTZJCsQdDeRcj(qdArray, unitProject, measureProjectTables,  措施子目清单Array, 措施标题,awfMark,djcsMark) {
        let memUnit = await PricingFileFindUtils.getUnit(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr);
        for (let j = 0; j < qdArray.length; j++) {
            let 措施子目清单 = {}
            let qd = qdArray[j];
            措施子目清单.序号 = j+1;
            措施子目清单.编码 = qd.fxCode;
            措施子目清单.名称 = qd.name;
            措施子目清单.工作内容 = '';
            措施子目清单.项目特征 = qd.projectAttr;
            措施子目清单.计量单位 = qd.unit;
            措施子目清单.工程量 = qd.quantity;
            措施子目清单.人工费单价 = await this.dualZBType(qd.rfee)
            措施子目清单.材料费单价 = await this.dualZBType(qd.cfee)
            措施子目清单.机械费单价 = await this.dualZBType(qd.jfee)
            措施子目清单.未计价材料单价 = await this.dualZBType(qd.zcfee);
            措施子目清单.设备费单价 = await this.dualZBType(qd.sbfPrice);
            措施子目清单.管理费单价 = await this.dualZBType(qd.managerFee);
            措施子目清单.利润单价 = await this.dualZBType(qd.profitFee);
            措施子目清单.综合单价 = await this.dualZBType(qd.price);
            措施子目清单.综合合价 = await this.dualZBType(qd.total);
            措施子目清单.措施子目类别 = djcsMark?'单价项目': "总价项目"
            措施子目清单.措施子目费用类别 = '无';
            措施子目清单.子目类型 =awfMark?'措施子目':'清单子目'
            措施子目清单.是否可竞争措施子目 = awfMark?1:0;;
            措施子目清单.主要标志 = qd.ifMainQd?1:0


            let deArrayFilter = measureProjectTables.filter(item => item.parentId === qd.sequenceNbr);

            if (this.type !=0) {
                await this.deRcj(deArrayFilter, unitProject, 措施子目清单)
            }

            if(qd.fxCode && qd.bdCode){//空清单不导出xml
                措施子目清单Array.push(措施子目清单);
            }else {
                //空清单需要判断下方有没有定额 如果有定额 保存为和清单同级别
                if(deArrayFilter && this.type !=0){
                    if(ObjectUtils.isNotEmpty(措施子目清单.定额组价)){
                        for (let i = 0; i <措施子目清单.定额组价.定额子目.length ; i++) {
                            let  定额子目= 措施子目清单.定额组价.定额子目[i]
                            定额子目.标签名称 = '定额子目'
                            措施子目清单Array.push(定额子目)
                        }
                    }
                }
            }



        }
        if(!ObjectUtils.isEmpty(措施标题.措施子目清单)){
            措施标题.措施子目清单 = [...措施标题.措施子目清单,...措施子目清单Array] ;
        }else {
            措施标题.措施子目清单 = 措施子目清单Array;
        }
    }


    // 工日单价
    async dualGRDJ(deRcjArray){
        if( this.type == 0){
            return 0
        }

        let rcjs = deRcjArray || [];
        // 过滤 类型人工，单位工日
        let rgs = rcjs.filter(item => item.kind === RCJKind.人工 && item.unit === '工日');

        if(ObjectUtils.isEmpty(rgs)){
            return null;
        }

        let rgKind2 = rgs.find(item => item.materialName === '综合用工二类');
        if(ObjectUtils.isNotEmpty(rgKind2)){
            return rgKind2.marketPrice;
        }

        return rgs[0].marketPrice;
    }



    /**
     * 分布分项清单的人材机
     * 类型
     * @param itemBillProjects
     * @param rcjArray
     * @param filter1
     * @param 分部分项工程
     * @returns {Promise<void>}
     */
    async convertFbFxQdDeRcj(itemBillProjects,rcjArray,filter1,分部分项工程,unitProject){
        let QdmxArray = new Array();
        let filterQd = filter1.filter(item =>item .kind === '03');
        if(!ObjectUtils.isEmpty(filterQd)){
            // let unitTmp = await PricingFileFindUtils.getUnit(unitProject.constructId, unitProject.spId,unitProject.sequenceNbr);
            //清单
            for (let j = 0; j < filterQd.length; j++) {
                let filter1Element = filterQd[j];
                let 分部分项工程量清单 = {};
                // 分部分项工程量清单.Xh = filter1Element.dispNo;
                分部分项工程量清单.项目编码 = filter1Element.bdCode;
                分部分项工程量清单.项目名称 = filter1Element.name;
                // 分部分项工程量清单.Xmtz = filter1Element.projectAttr;
                // 分部分项工程量清单.Gcnr = '';//工作内容
                // 分部分项工程量清单.Jsgz = '';//计算规则
                分部分项工程量清单.计量单位 = filter1Element.unit;
                分部分项工程量清单.工程量 = filter1Element.quantity;
                分部分项工程量清单.人工费单价 =   await this.dualZBType(filter1Element.rfee)
                分部分项工程量清单.材料费单价 =   await this.dualZBType(filter1Element.cfee)
                分部分项工程量清单.机械费单价 =   await this.dualZBType(filter1Element.jfee)
                分部分项工程量清单.未计价材费单价=  await this.dualZBType(filter1Element.zcfee)
                分部分项工程量清单.设备费单价 =   await this.dualZBType(filter1Element.sbfPrice)
                分部分项工程量清单.管理费单价 =  await this.dualZBType(filter1Element.managerFee)
                分部分项工程量清单.利润单价 =   await this.dualZBType(filter1Element.profitFee)
                分部分项工程量清单.综合单价 =   await this.dualZBType(filter1Element.price)
                分部分项工程量清单.综合合价 =   await this.dualZBType(filter1Element.total)
                分部分项工程量清单.工作内容 =   ''
                分部分项工程量清单.项目特征 =   filter1Element.projectAttr
                分部分项工程量清单.主要标志 =   filter1Element.ifMainQd?1:0




                //定额
                let deFilter = itemBillProjects.filter(item => item.parentId === filter1Element.sequenceNbr);

                if (this.type !=0) {
                    await this.deRcj(deFilter, unitProject, 分部分项工程量清单);
                }

                if(filter1Element.fxCode && filter1Element.bdCode){//空清单不导出xml
                    QdmxArray.push(分部分项工程量清单);
                }else {
                    //空清单需要判断下方有没有定额 如果有定额 保存为和清单同级别
                    if(deFilter && this.type !=0){
                        if(ObjectUtils.isNotEmpty(分部分项工程量清单.定额组价)){
                            for (let i = 0; i <分部分项工程量清单.定额组价.定额子目.length ; i++) {
                                let  定额子目= 分部分项工程量清单.定额组价.定额子目[i]
                                定额子目.标签名称 = '定额子目'
                                QdmxArray.push(定额子目)
                            }
                        }
                    }
                }

            }
            分部分项工程.分部分项工程量清单 = QdmxArray;
        }
    }

    async deRcj(deFilter, unitProject, 清单) {
        if (!ObjectUtils.isEmpty(deFilter)) {
            let 定额组价 = {};
            let deArray = new Array();
            //存放当前清单下所有人才机
            let qdRcjArray = new Array();
            for (let k = 0; k < deFilter.length; k++) {
                let deFilterElement = deFilter[k];
                let 定额子目 = {};
                定额子目.序号 = deFilterElement.dispNo;
                定额子目.定额编号 = deFilterElement.bdCode;
                定额子目.名称 = deFilterElement.name;
                定额子目.计量单位 = deFilterElement.unit;
                定额子目.工程量 = deFilterElement.quantity;
                定额子目.取费基数 = 0;//需要分情况处理，新奔腾中相关费用定额 如 其他总价措施、安文费 均展示对应的计算基数值，gld中展示为0，如何处理？ 考虑简单处理直接参考gld
                定额子目.费率 = 0;//需要分情况处理，新奔腾中相关费用定额 如 其他总价措施、安文费 均展示对应的计算基数值，gld中展示为0，如何处理？ 考虑简单处理直接参考gld
                定额子目.人工费单价 = await this.dualZBType(deFilterElement.rfee)
                定额子目.材料费单价 = await this.dualZBType(deFilterElement.cfee)
                定额子目.机械费单价 = await this.dualZBType(deFilterElement.jfee)
                定额子目.未计价材费单价 = await this.dualZBType(deFilterElement.zcfee)
                定额子目.设备费单价 = await this.dualZBType(deFilterElement.sbfPrice)
                定额子目.管理费单价 = await this.dualZBType(deFilterElement.managerFee)
                定额子目.利润单价 = await this.dualZBType(deFilterElement.profitFee)
                定额子目.综合单价 = await this.dualZBType(deFilterElement.price)
                定额子目.综合合价 = await this.dualZBType(deFilterElement.total)

                //查询定额下的人材机
                let deRcjArry = this.RcjMap.get(deFilterElement.sequenceNbr)
                定额子目.工日单价 = await this.dualGRDJ(deRcjArry) //取 定额中综合用工二类的市场价，若不含综合用工二类数据或存在多条人工费取第一条类别为人工单位为工日的数据市场价

                if (!ObjectUtils.isEmpty(deRcjArry)) {
                    let 消耗量表 = {}

                    let 消耗量子目Array = new Array();
                    for (let l = 0; l < deRcjArry.length; l++) {
                        let deRcjElement = deRcjArry[l];
                        let 消耗量子目 = {}
                        消耗量子目.序号 = l + 1
                        消耗量子目.编码 = deRcjElement.materialCode
                        消耗量子目.类型 = await this.getFeeTypeByKind( deRcjElement.kind)
                        消耗量子目.名称 = deRcjElement.materialName
                        消耗量子目.计量单位 = deRcjElement.unit
                        消耗量子目.数量 = deRcjElement.totalNumber
                        消耗量子目.预算价 = deRcjElement.dePrice
                        消耗量子目.除税系数 = ObjectUtils.isEmpty(deRcjElement.taxRemoval)?0:deRcjElement.taxRemoval
                        消耗量子目.市场价 = deRcjElement.marketPrice
                        消耗量子目.除税市场价 = NumberUtil.costPriceAmountFormat( NumberUtil.subtract(消耗量子目.市场价,NumberUtil.multiply(消耗量子目.市场价,NumberUtil.divide100(deRcjElement.taxRemoval))));//取 人材机汇总对应人材机 市场价列 - 进项税额列
                        消耗量子目.结算价 = deRcjElement.marketPrice
                        消耗量子目.暂估标志 = deRcjElement.ifProvisionalEstimate==1?1:0
                        消耗量子目.主要材料标志 = this.zyclbzBy主要材料设备表(deRcjElement);
                        消耗量子目.规格型号 = deRcjElement.specification
                        消耗量子目.质量等级 = deRcjElement.qualityGrade
                        消耗量子目.厂家 = deRcjElement.manufactor
                        消耗量子目.产地 = deRcjElement.producer
                        消耗量子目.含税合价 = deRcjElement.total
                        消耗量子目.除税合价 = NumberUtil.subtract(deRcjElement.total,deRcjElement.jxTotal);
                        消耗量子目.销项税额合计 = NumberUtil.costPriceAmountFormat( NumberUtil.multiply( deRcjElement.total,NumberUtil.divide100(unitProject.projectTaxCalculation.outputTaxRate)));
                        消耗量子目.进项税额合计 = deRcjElement.jxTotal
                        消耗量子目.供应时间 = ''
                        消耗量子目.甲供数量 = ObjectUtils.isEmpty(deRcjElement.donorMaterialNumber)?0:deRcjElement.donorMaterialNumber
                        消耗量子目.送达地点 = deRcjElement.deliveryLocation
                        消耗量子目.备注 = ''

                        消耗量子目Array.push(消耗量子目);
                        qdRcjArray.push(deRcjElement);
                    }
                    消耗量表.消耗量子目 = 消耗量子目Array;

                    定额子目.消耗量表 = 消耗量表

                }
                deArray.push(定额子目);

                let feeFiles = unitProject.feeFiles.find(item => item.feeFileId == deFilterElement.feeFileId)
                let 子目费用表 = {}
                子目费用表.费用ID = 1 //gld应是按自己业务数据处理，新奔腾固定展示为1，考虑使用新奔腾形式处理
                子目费用表.费用代码 = feeFiles.feeCode //取 当前定额对应的取费文件的取费编码
                子目费用表.费用表名称 = feeFiles.feeFileName //取 当前定额对应的取费文件名称

                let feeBuildElement = await this.service.unitPriceService.getPriceBuild(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr, deFilterElement.sequenceNbr)
                if (feeBuildElement) {

                    let 费用子目Array = []
                    for (let i = 0; i < feeBuildElement.length; i++) {
                        let 费用子目 = {}
                        费用子目.序号 = feeBuildElement[i].sort
                        费用子目.费用编码 = feeBuildElement[i].code
                        费用子目.费用名称 = feeBuildElement[i].name
                        费用子目.计算基础 = feeBuildElement[i].caculateBase
                        费用子目.计算基础说明 = feeBuildElement[i].desc
                        费用子目.费率 = ObjectUtils.isNotEmpty(feeBuildElement[i].rate) ? feeBuildElement[i].rate : 100
                        费用子目.单价 = feeBuildElement[i].unitPrice
                        费用子目.金额 = feeBuildElement[i].allPrice
                        费用子目.费用类别 = feeBuildElement[i].type
                        费用子目.备注 = feeBuildElement[i].isLock?'不计入综合单价':''
                        费用子目Array.push(费用子目)
                    }
                    子目费用表.费用子目 = 费用子目Array
                    定额子目.子目费用表 = 子目费用表
                }


            }
            定额组价.定额子目 = deArray;
            清单.定额组价 = 定额组价;
        }

    }

    /**
     * 主要材料标志，通过人材机汇总中主要材料、设备表判断
     */
    zyclbzBy主要材料设备表(currentRcj){
        let zyclsbArray = this.zyclsbZysbclArray;
        let result = 0;
        for(let r of zyclsbArray) {
            if(r.materialCode == currentRcj.materialCode){
                result = 1;
                break;
            }
        }
        return result;
    }

//单价措施清单的人材机
    async convertDJCsQdDeRcj(measureProjectTables,rcjArray,filter1,DjCsBt,unitProject){
        let memUnit = await PricingFileFindUtils.getUnit(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr);
        let djCsMxArray = new Array();
        let filterQd = filter1.filter(item =>item .kind === '03');
        if(!ObjectUtils.isEmpty(filterQd)){
            //清单
            for (let j = 0; j < filterQd.length; j++) {
                let filter1Element = filterQd[j];
                let DjCsMx = {};
                DjCsMx.Xh = filter1Element.dispNo;
                DjCsMx.Qdbm = filter1Element.bdCode;
                DjCsMx.Mc = filter1Element.bdName;
                DjCsMx.Xmtz = filter1Element.projectAttr;
                DjCsMx.Gcnr = '';//工作内容
                DjCsMx.Jsgz = '';//计算规则
                DjCsMx.Dw = filter1Element.unit;
                DjCsMx.Sl = filter1Element.quantity;
                DjCsMx.Zhdj = filter1Element.price;
                DjCsMx.Rgf = filter1Element.rfee;
                DjCsMx.Zcf = ObjectUtils.isEmpty(filter1Element.zcfee)?'0':filter1Element.zcfee;
                DjCsMx.Sbf = '0';
                DjCsMx.Fcf = ObjectUtils.isEmpty(filter1Element.zcfee)?'0':filter1Element.cfee;//辅材费用
                DjCsMx.Clf = NumberUtil.numberScale2(NumberUtil.add(filter1Element.zcfee,filter1Element.cfee));
                DjCsMx.Jxf = ObjectUtils.isEmpty(filter1Element.jfee)?'0':filter1Element.jfee;
                DjCsMx.Rgjj = '0';
                DjCsMx.Zcjj = '0';
                DjCsMx.Fcjj = '0';
                DjCsMx.Sbjj = '0';
                DjCsMx.Cljj = '0';
                DjCsMx.Jxjj = '0';
                DjCsMx.Glf = filter1Element.managerFee;
                DjCsMx.Lr = filter1Element.profitFee;


                let djgc = ObjectUtils.isEmpty(memUnit.feeBuild) ? null : memUnit.feeBuild[filter1Element.sequenceNbr];
                if (!ObjectUtils.isEmpty(djgc)) {


                    let glf =djgc.find(f=>f.type==="管理费")
                    if(ObjectUtils.isNotEmpty(glf)){
                        DjCsMx.Glf = NumberUtil.numberScale2( glf.allPrice);
                    }else {
                        DjCsMx.Glf = '0';
                    }

                    let lr =djgc.find(f=>f.type==="利润")
                    if(ObjectUtils.isNotEmpty(lr)){
                        DjCsMx.Lr = NumberUtil.numberScale2( lr.allPrice);
                    }else {
                        DjCsMx.Lr = '0';
                    }

                    let gf = djgc.find(f => f && f.type === "规费")
                    if(ObjectUtils.isNotEmpty(gf)){
                        DjCsMx.Gf =    NumberUtil.numberScale2( gf.allPrice);
                    }else {
                        DjCsMx.Gf = '';
                    }

                    let aqwmf = djgc.find(f => f && f.type === "安全文明施工费");
                    if(ObjectUtils.isNotEmpty(aqwmf)){
                        DjCsMx.Aqwmf = NumberUtil.numberScale2( aqwmf.allPrice);
                    }else {
                        DjCsMx.Aqwmf = '';
                    }

                }else {
                    DjCsMx.Glf='0'
                    DjCsMx.Lr='0'
                    DjCsMx.Gf='0'
                    DjCsMx.Aqwmf='0'
                }
                DjCsMx.Zgj =   '';
                DjCsMx.Zhhj = filter1Element.total;
                DjCsMx.Rgdj = filter1Element.rfeePrice;
                DjCsMx.Bz = filter1Element.description;

                //定额
                let deFilter = measureProjectTables.filter(item => item.parentId === filter1Element.sequenceNbr);

                if(!ObjectUtils.isEmpty(deFilter)){
                    let Csxdezj = {};
                    let deArray = new Array();
                    //存放当前清单下所有人才机
                    let qdRcjArray = new Array();
                    for (let k = 0; k < deFilter.length; k++) {
                        let deFilterElement = deFilter[k];
                        let CsxdezjMx ={};
                        CsxdezjMx.Debm = deFilterElement.bdCode;
                        CsxdezjMx.DeGuid = "";
                        CsxdezjMx.YsDebm = "";
                        CsxdezjMx.Dekbz  =await this.convertDekbz(deFilterElement.description);
                        CsxdezjMx.Mc = deFilterElement.bdName;
                        CsxdezjMx.Dw = deFilterElement.unit;
                        CsxdezjMx.Sl = deFilterElement.quantity;
                        CsxdezjMx.Dj = deFilterElement.price;
                        CsxdezjMx.Hj = deFilterElement.total;
                        CsxdezjMx.Rgf = deFilterElement.rfee;
                        CsxdezjMx.Zcf = ObjectUtils.isEmpty(deFilterElement.zcfee)?'0':deFilterElement.zcfee;
                        CsxdezjMx.Sbf = '0';
                        CsxdezjMx.Fcf = ObjectUtils.isEmpty(deFilterElement.cfee)?'0':deFilterElement.cfee;
                        CsxdezjMx.Clf = NumberUtil.numberScale2(NumberUtil.add(deFilterElement.zcfee,deFilterElement.cfee));
                        CsxdezjMx.Jxf = ObjectUtils.isEmpty(deFilterElement.zcfee)?'0':deFilterElement.jfee;
                        CsxdezjMx.Rgjj =  '0';
                        CsxdezjMx.Zcjj =  '0';
                        CsxdezjMx.Fcjj =  '0';
                        CsxdezjMx.Sbjj =  '0';
                        CsxdezjMx.Cljj =  '0';
                        CsxdezjMx.Jxjj =  '0';

                        let deFeeBuild = ObjectUtils.isEmpty(memUnit.feeBuild) ? null : memUnit.feeBuild[deFilterElement.sequenceNbr];
                        if(ObjectUtils.isNotEmpty(deFeeBuild)){

                            let glf =deFeeBuild.find(f=>f.type==="管理费")
                            if(ObjectUtils.isNotEmpty(glf)){
                                CsxdezjMx.Glf = NumberUtil.numberScale2( glf.allPrice);
                                CsxdezjMx.Glffl = glf.rate;
                            }else {
                                CsxdezjMx.Glf = '0';
                                CsxdezjMx.Glffl = '0';
                            }

                            let lr =deFeeBuild.find(f=>f.type==="利润")
                            if(ObjectUtils.isNotEmpty(lr)){
                                CsxdezjMx.Lr = NumberUtil.numberScale2( lr.allPrice);
                                CsxdezjMx.Lrfl =  lr.rate;
                            }else {
                                CsxdezjMx.Lr = '0';
                                CsxdezjMx.Lrfl = '0';
                            }
                        }else {
                            CsxdezjMx.Glf = '0';
                            CsxdezjMx.Glffl = '0';
                            CsxdezjMx.Lr = '0';
                            CsxdezjMx.Lrfl = '0';
                        }
                        let deRcjArry = this.RcjMap.get(deFilterElement.sequenceNbr)
                        if(!ObjectUtils.isEmpty(deRcjArry)){
                            let rgfRcj = deRcjArry.filter(item => (item.deId ==deFilterElement.sequenceNbr && item.type =='人工费' ))
                            if(ObjectUtils.isNotEmpty(rgfRcj) &&   ObjectUtils.isNotEmpty(rgfRcj[0].marketPrice)){
                                CsxdezjMx.Rgdj = rgfRcj[0].marketPrice
                            }else {
                                CsxdezjMx.Rgdj = '0'
                            }
                        }else {
                            CsxdezjMx.Rgdj = '0';
                        }

                        //查询定额下的人材机
                        // let deRcjArry = rcjArray.filter(item =>item.deId ===deFilterElement.sequenceNbr );
                        if(!ObjectUtils.isEmpty(deRcjArry)){
                            let Csxrcjhl = {};
                            let CsxrcjhlMxArray = new Array();
                            for (let l = 0; l < deRcjArry.length; l++) {
                                let deRcjElement = deRcjArry[l];
                                let CsxdercjhlMx = {};
                                CsxdercjhlMx.RcjId =deRcjElement.standardId ;
                                CsxdercjhlMx.Rcjhl =ObjectUtils.isEmpty(deRcjElement.resQty)?0:deRcjElement.resQty;
                                CsxdercjhlMx.RcjDehj =NumberUtil.multiplyToString(deRcjElement.dePrice,deRcjElement.totalNumber,2);//单位定额现合价 Decimal 必填）
                                CsxdercjhlMx.Rcjhj   =NumberUtil.multiplyToString(deRcjElement.marketPrice,deRcjElement.totalNumber,2);
                                CsxrcjhlMxArray.push(CsxdercjhlMx);
                                qdRcjArray.push(deRcjElement);
                            }
                            Csxrcjhl.CsxdercjhlMx  =CsxrcjhlMxArray;

                            CsxdezjMx.Csxrcjhl = Csxrcjhl;
                        }
                        deArray.push(CsxdezjMx);
                    }
                    Csxdezj.CsxdezjMx = deArray;
                    DjCsMx.Csxdezj =Csxdezj;

                    //Csxrcjhl 此处是查询当前清单下所有定额下的人材机并根据编码分组求和
                    let Csxrcjhl = {};
                    if(!ObjectUtils.isEmpty(qdRcjArray)){
                        let group = ArrayUtil.group(qdRcjArray,'materialCode');
                        let CsxdercjhlMxS = new Array();
                        for ( let [key, value] of group.entries()) {
                            let CsxdercjhlMx = {};
                            CsxdercjhlMx.RcjId =value[0].standardId ;
                            CsxdercjhlMx.Rcjhl =value.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add( accumulator , constructProjectRcj.resQty);
                            }, 0);
                            CsxdercjhlMx.RcjDehj =value.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add( accumulator , NumberUtil.multiply(constructProjectRcj.dePrice,constructProjectRcj.totalNumber) );
                            }, 0);
                            CsxdercjhlMx.Rcjhj =value.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add( accumulator , NumberUtil.multiply(constructProjectRcj.marketPrice,constructProjectRcj.totalNumber) );
                            }, 0);
                            CsxdercjhlMxS.push(CsxdercjhlMx);
                        }
                        Csxrcjhl.CsxdercjhlMx = CsxdercjhlMxS;
                    }
                    DjCsMx.Csxrcjhl = Csxrcjhl;
                }
                djCsMxArray.push(DjCsMx)
            }
            DjCsBt.DjCsMx = djCsMxArray;
        }
    }
    /**
     * 其他项目
     * @param 单位工程
     * @param otherProjects
     * @returns {Promise<void>}
     */
    async convertQtxm(单位工程, unit,feeFiles) {
        let Qtxm ={};
        let cs = {};
        cs.constructId = unit.constructId;
        cs.singleId = unit.spId;
        cs.unitId = unit.sequenceNbr;

        let otherProjectList = await this.service.otherProjectService.getOtherProjectList(cs);
        if(ObjectUtils.isEmpty(otherProjectList)){
            单位工程.其他项目 =Qtxm;
            return ;
        }
        //暂列金额行
        let findZlje = otherProjectList.find(i=>i.type == "暂列金额");
        if (!ObjectUtils.isEmpty(findZlje)){
            let zlje ={};
            zlje.序号 =findZlje.dispNo;
            zlje.计量单位 =findZlje.unit;
            zlje.金额 =findZlje.total;
            zlje.备注 =findZlje.description;
            Qtxm.暂列金额 = zlje;

            //暂列金额列表
            let otherProjectProvisionals = unit.otherProjectProvisionals;
            if (!ObjectUtils.isEmpty(otherProjectProvisionals)){
                let zljList = new Array();
                for (let otherProjectProvisional of otherProjectProvisionals) {
                    let zljZm = {};
                    zljZm.序号 = otherProjectProvisional.dispNo ;
                    zljZm.项目名称 = otherProjectProvisional.name ;
                    zljZm.计量单位 = otherProjectProvisional.unit ;
                    zljZm.暂定金额 = otherProjectProvisional.provisionalSum ;
                    zljZm.备注 = otherProjectProvisional.description;
                    zljList.push(zljZm);
                }
                Qtxm.暂列金额.暂列金额子目 = zljList;
            }
        }

        //暂估价行
        let findZgj = otherProjectList.find(i=>i.type == "暂估价");
        if (!ObjectUtils.isEmpty(findZgj)){
            let zgj ={};
            zgj.序号 =findZgj.dispNo;
            zgj.计量单位 =findZgj.unit;
            zgj.金额 =findZgj.total;
            zgj.备注 =findZgj.description;
            Qtxm.暂估价 = zgj;

            let findClzgj = otherProjectList.find(i=>i.type == "材料暂估价");
            let zgjRcjList  =await this.service.rcjProcess.queryConstructRcjByDeIdNew(3, 8, unit.constructId, unit.spId,unit.sequenceNbr);

            //材料暂估价
            if (!ObjectUtils.isEmpty(findClzgj)){
                let clzgj ={};
                clzgj.序号 =findClzgj.dispNo;
                clzgj.计量单位 =findClzgj.unit;
                //clzgj.金额 =findClzgj.total;
                clzgj.备注 =findClzgj.description;
                Qtxm.暂估价.材料暂估价 = clzgj;

                //人材机汇总查询
                if (!ObjectUtils.isEmpty(zgjRcjList)){
                    //材料暂估
                    let clZgList = zgjRcjList.filter(i=>i.kind != 4);
                    if (!ObjectUtils.isEmpty(clZgList)){
                        let clzg = await this.rcjZgjJx(clZgList);
                        Qtxm.暂估价.材料暂估价.暂估材料子目 = clzg;
                    }
                }
            }
            let findSbzgj = otherProjectList.find(i=>i.type == "设备暂估价");
            //设备暂估价
            if (!ObjectUtils.isEmpty(findSbzgj)){
                let sbzgj ={};
                sbzgj.序号 =findSbzgj.dispNo;
                sbzgj.计量单位 =findSbzgj.unit;
                //sbzgj.金额 =findSbzgj.total;
                sbzgj.备注 =findSbzgj.description;
                Qtxm.暂估价.设备暂估价 = sbzgj;
                if(ObjectUtils.isNotEmpty(zgjRcjList)){
                    //材料暂估
                    let sbZgList = zgjRcjList.filter(i=>i.kind == 4);
                    if (!ObjectUtils.isEmpty(sbZgList)){
                        let sbzg = await this.rcjZgjJx(sbZgList);
                        Qtxm.暂估价.设备暂估价.暂估材料子目 = sbzg;
                    }
                }

            }

            let findZygcZgj = otherProjectList.find(i=>i.type == "专业工程暂估价");
            //专业工程暂估价
            if (!ObjectUtils.isEmpty(findZygcZgj)){
                let zygcZgj ={};
                zygcZgj.序号 =findZygcZgj.dispNo;
                zygcZgj.计量单位 =findZygcZgj.unit;
                zygcZgj.金额 =findZygcZgj.total;
                zygcZgj.备注 =findZygcZgj.description;
                Qtxm.暂估价.专业工程暂估价 = zygcZgj;

                let otherProjectZygcZgjs = unit.otherProjectZygcZgjs;

                if (!ObjectUtils.isEmpty(otherProjectZygcZgjs)){
                    let array = new Array();
                    for (let otherProjectZygcZgj of otherProjectZygcZgjs) {
                        let zygc = {};
                        zygc.序号  = otherProjectZygcZgj.dispNo;
                        zygc.工程名称  = otherProjectZygcZgj.name;
                        zygc.工程内容  = otherProjectZygcZgj.content;
                        zygc.单位  = otherProjectZygcZgj.unit;
                        zygc.金额  = otherProjectZygcZgj.total;
                        zygc.备注  = otherProjectZygcZgj.description;
                        array.push(zygc);
                    }
                    Qtxm.暂估价.专业工程暂估价.暂估工程子目 = array;
                }
            }
        }
        //总承包服务费
        let findZcbfwf = otherProjectList.find(i=>i.type == "总承包服务费");
        if (!ObjectUtils.isEmpty(findZcbfwf)){
            let zcbfwf ={};
            zcbfwf.序号 =findZcbfwf.dispNo;
            zcbfwf.计量单位 =findZcbfwf.unit;
            zcbfwf.金额 =findZcbfwf.total;
            zcbfwf.备注 =findZcbfwf.description;
            Qtxm.总承包服务费 = zcbfwf;

            let otherProjectServiceCosts = unit.otherProjectServiceCosts;
            let filterSJH = [];
            if (!ObjectUtils.isEmpty(otherProjectServiceCosts)){
                filterSJH = otherProjectServiceCosts.filter(i=>i.dataType == 2);
            }
            if (!ObjectUtils.isEmpty(filterSJH)){
                let array1 = new Array();
                for (let otherProjectServiceCost of filterSJH) {
                    let zcb = {};
                    zcb.序号  = otherProjectServiceCost.dispNo;
                    zcb.项目名称  = otherProjectServiceCost.fxName;
                    zcb.项目价值  = otherProjectServiceCost.xmje;
                    zcb.计算基础  = otherProjectServiceCost.xmje;
                    zcb.服务内容  = otherProjectServiceCost.serviceContent;
                    zcb.费率  = !ObjectUtils.isEmpty(otherProjectServiceCost.rate)?otherProjectServiceCost.rate:100;
                    zcb.金额  = await this.dualZBType(otherProjectServiceCost.fwje);

                    if (!ObjectUtils.isEmpty(otherProjectServiceCost.parentId)){
                        let find = otherProjectServiceCosts.find(i=>i.sequenceNbr ==otherProjectServiceCost.parentId );
                        if (!ObjectUtils.isEmpty(find)){
                            zcb.类型  = find.fxName;
                        }
                    }
                    array1.push(zcb);
                }
                Qtxm.总承包服务费.总承包服务费子目 = array1;
            }
        }

        //计日工
        let findJrg = otherProjectList.find(i=>i.type == "计日工");
        if (!ObjectUtils.isEmpty(findZcbfwf)){
            let jrg ={};
            jrg.序号 =findJrg.dispNo;
            jrg.计量单位 =findJrg.unit;
            jrg.金额 =findJrg.total;
            jrg.备注 =findJrg.description;
            Qtxm.计日工 = jrg;

            let otherProjectDayWorks = unit.otherProjectDayWorks;
            if (!ObjectUtils.isEmpty(otherProjectDayWorks)){

                //人工
                let findRg = otherProjectDayWorks.find(i=>i.worksName == "人工");
                if (!ObjectUtils.isEmpty(findRg)){
                    let rg = {};
                    rg.编码 = findRg.dispNo;
                    rg.金额 = await this.dualZBType(!ObjectUtils.isEmpty(findRg.total)?findRg.total : 0);
                    Qtxm.计日工.人工 = rg;
                    let rgList = otherProjectDayWorks.filter(i=>i.parentId == findRg.sequenceNbr);
                    if (!ObjectUtils.isEmpty(rgList)){
                        let array2 = new Array();
                        let i = 0
                        for (let rgListKey in rgList) {
                            i = i+1;
                            let rgzm = {};
                            rgzm.序号 = i;
                            rgzm.编码 = rgList[rgListKey].dispNo;
                            rgzm.类型 = "人工" ;
                            rgzm.名称 = rgList[rgListKey].worksName;
                            rgzm.计量单位 = rgList[rgListKey].unit;
                            rgzm.数量 = !ObjectUtils.isEmpty(rgList[rgListKey].tentativeQuantity)?rgList[rgListKey].tentativeQuantity:0;
                            rgzm.预算价 = 0;
                            rgzm.除税系数 = rgList[rgListKey].taxRemoval;
                            rgzm.市场价 = await this.dualZBType(rgList[rgListKey].price);
                            let csscj = rgzm.市场价;
                            if (rgzm.除税系数 != 0){
                                let subtract = NumberUtil.subtract(100,rgList[rgListKey].taxRemoval);
                                csscj = NumberUtil.costPriceAmountFormat(NumberUtil.multiplyParams(rgList[rgListKey].price,subtract,0.01));
                            }
                            rgzm.除税市场价 = await this.dualZBType(csscj);
                            rgzm.结算价 = 0;
                            rgzm.暂估标志 = 0;
                            rgzm.主要材料标志 = 0;
                            rgzm.规格型号 = rgList[rgListKey].specification;
                            rgzm.质量等级 = "" ;
                            rgzm.厂家 = "";
                            rgzm.产地 = "";
                            rgzm.含税合价 = await this.dualZBType(rgList[rgListKey].total);
                            rgzm.除税合价 = await this.dualZBType(NumberUtil.costPriceAmountFormat(NumberUtil.multiplyParams(rgzm.除税市场价,rgList[rgListKey].tentativeQuantity)));
                            let is22 = PricingFileFindUtils.is22Unit(unit);
                            if (is22){
                                rgzm.销项税额合计 = 0;
                            }else {
                                rgzm.销项税额合计 = NumberUtil.costPriceAmountFormat( NumberUtil.multiply( rgList[rgListKey].total,unit.projectTaxCalculation.outputTaxRate));
                            }

                            rgzm.进项税额合计 = await this.dualZBType(rgList[rgListKey].csPrice);
                            rgzm.供应时间 = "";
                            rgzm.甲供数量 = "";
                            rgzm.送达地点 = "";
                            rgzm.备注 = "";
                            array2.push(rgzm);
                        }
                        Qtxm.计日工.人工.人工子目 = array2;
                    }

                }

                //材料
                let findCl = otherProjectDayWorks.find(i=>i.worksName == "材料");
                if (!ObjectUtils.isEmpty(findCl)){
                    let cl = {};
                    cl.编码 = findCl.dispNo;
                    cl.金额 = await this.dualZBType(!ObjectUtils.isEmpty(findCl.total)?findCl.total : 0);
                    Qtxm.计日工.材料 = cl;
                    let clList = otherProjectDayWorks.filter(i=>i.parentId == findCl.sequenceNbr);
                    if (!ObjectUtils.isEmpty(clList)){
                        let arrayCl = new Array();
                        let i = 0;
                        for (let clListKey in clList) {
                            i = i+1;
                            let clzm = {};
                            clzm.序号 = i;
                            clzm.编码 = clList[clListKey].dispNo;
                            clzm.类型 = "材料" ;
                            clzm.名称 = clList[clListKey].worksName;
                            clzm.计量单位 = clList[clListKey].unit;
                            clzm.数量 = clList[clListKey].tentativeQuantity;
                            clzm.预算价 = 0;
                            clzm.除税系数 = clList[clListKey].taxRemoval;
                            clzm.市场价 = await this.dualZBType(clList[clListKey].price);
                            let csscj = clzm.市场价;
                            if (clzm.除税系数 != 0){
                                let subtract = NumberUtil.subtract(100,clList[clListKey].taxRemoval);
                                csscj = NumberUtil.costPriceAmountFormat(NumberUtil.multiplyParams(clList[clListKey].price,subtract,0.01));
                            }
                            clzm.除税市场价 = await this.dualZBType(csscj);
                            clzm.结算价 = 0;
                            clzm.暂估标志 = 0;
                            clzm.主要材料标志 = 0;
                            clzm.规格型号 = clList[clListKey].specification;
                            clzm.质量等级 = "" ;
                            clzm.厂家 = "";
                            clzm.产地 = "";
                            clzm.含税合价 = await this.dualZBType(clList[clListKey].total);
                            clzm.除税合价 = NumberUtil.costPriceAmountFormat(NumberUtil.multiplyParams(clzm.除税市场价,clList[clListKey].tentativeQuantity));

                            let is22 = PricingFileFindUtils.is22Unit(unit);
                            if (is22){
                                clzm.销项税额合计 = 0;
                            }else {
                                clzm.销项税额合计 = NumberUtil.costPriceAmountFormat( NumberUtil.multiply( clList[clListKey].total,unit.projectTaxCalculation.outputTaxRate));
                            }
                            clzm.进项税额合计 = clList[clListKey].csPrice;
                            clzm.供应时间 = "";
                            clzm.甲供数量 = "";
                            clzm.送达地点 = "";
                            clzm.备注 = "";
                            arrayCl.push(clzm);
                        }
                        Qtxm.计日工.材料.材料子目 = arrayCl;
                    }
                }


                //机械
                let findJx = otherProjectDayWorks.find(i=>i.worksName == "机械");
                if (!ObjectUtils.isEmpty(findJx)){
                    let jx = {};
                    jx.编码 = findJx.dispNo;
                    jx.金额 = await this.dualZBType(!ObjectUtils.isEmpty(findJx.total)?findJx.total : 0);
                    Qtxm.计日工.机械 = jx;
                    let jxList = otherProjectDayWorks.filter(i=>i.parentId == findJx.sequenceNbr);
                    if (!ObjectUtils.isEmpty(jxList)){
                        let arrayJx = new Array();
                        let i = 0;
                        for (let jxListKey in jxList) {
                            i = i+1;
                            let jxzm = {};
                            jxzm.序号 = i;
                            jxzm.编码 = jxList[jxListKey].dispNo;
                            jxzm.类型 = "材料" ;
                            jxzm.名称 = jxList[jxListKey].worksName;
                            jxzm.计量单位 = jxList[jxListKey].unit;
                            jxzm.数量 = jxList[jxListKey].tentativeQuantity;
                            jxzm.预算价 = 0;
                            jxzm.除税系数 = jxList[jxListKey].taxRemoval;
                            jxzm.市场价 = await this.dualZBType(jxList[jxListKey].price);
                            let csscj = jxzm.市场价;
                            if (jxzm.除税系数 != 0){
                                let subtract = NumberUtil.subtract(100,jxList[jxListKey].taxRemoval);
                                csscj = NumberUtil.costPriceAmountFormat(NumberUtil.multiplyParams(jxList[jxListKey].price,subtract,0.01));
                            }
                            jxzm.除税市场价 = await this.dualZBType(csscj);
                            jxzm.结算价 = 0;
                            jxzm.暂估标志 = 0;
                            jxzm.主要材料标志 = 0;
                            jxzm.规格型号 = jxList[jxListKey].specification;
                            jxzm.质量等级 = "" ;
                            jxzm.厂家 = "";
                            jxzm.产地 = "";
                            jxzm.含税合价 = await this.dualZBType(jxList[jxListKey].total);
                            jxzm.除税合价 = NumberUtil.costPriceAmountFormat(NumberUtil.multiplyParams(jxzm.除税市场价,jxList[jxListKey].tentativeQuantity));

                            let is22 = PricingFileFindUtils.is22Unit(unit);
                            if (is22){
                                jxzm.销项税额合计 = 0;
                            }else {
                                jxzm.销项税额合计 = NumberUtil.costPriceAmountFormat( NumberUtil.multiply( jxList[jxListKey].total,unit.projectTaxCalculation.outputTaxRate));
                            }
                            jxzm.进项税额合计 = jxList[jxListKey].csPrice;
                            jxzm.供应时间 = "";
                            jxzm.甲供数量 = "";
                            jxzm.送达地点 = "";
                            jxzm.备注 = "";
                            arrayJx.push(jxzm);
                        }
                        Qtxm.计日工.机械.机械子目 = arrayJx;
                    }
                }
            }

        }

        单位工程.其他项目 = Qtxm;
    }

    async rcjZgjJx(zgjRcjList){
        let clZgArray = new Array();
        for (let clZgListElement of zgjRcjList) {
            let kind = clZgListElement.kind;
            let kindString = "";
            switch (kind) {
                case -1:
                    kindString = "无";
                    break;
                case 4:
                    kindString = "设备费";
                    break;
                case 5:
                    kindString = "主材费";
                    break;
                default:
                    kindString = "材料费";
                    break;
            }
            let clzgzm = {};
            clzgzm.序号 = clZgListElement.dispNo;
            clzgzm.编码 = clZgListElement.materialCode;
            clzgzm.类型 = kindString;
            clzgzm.名称 = clZgListElement.materialName;
            clzgzm.计量单位 = clZgListElement.unit;
            clzgzm.数量 = clZgListElement.totalNumber;
            clzgzm.预算价 = 0;
            clzgzm.除税系数 = 0;
            clzgzm.市场价 = clZgListElement.marketPrice;
            clzgzm.除税市场价 = clZgListElement.marketPrice;
            clzgzm.结算价 = 0;
            clzgzm.暂估标志 = 1;
            clzgzm.主要材料标志 = 0;
            clzgzm.规格型号 = clZgListElement.specification;
            clzgzm.质量等级 = null;
            clzgzm.厂家 = clZgListElement.manufactor;
            clzgzm.产地 = clZgListElement.producer;
            clzgzm.含税合价 = clZgListElement.total;
            clzgzm.除税合价 = clZgListElement.total;
            clzgzm.销项税额合计 = 0;
            clzgzm.进项税额合计 = 0;
            clzgzm.供应时间 = null;
            clzgzm.甲供数量 = 0;
            clzgzm.送达地点 = null;
            clzgzm.备注 = clZgListElement.remark;
            clZgArray.push(clzgzm);
        }
        return clZgArray;
    }

    /**
     * 其他项目项目类别转换
     * @returns {Promise<string>}
     */
    async handleQtxmXmlb(type){

        let res =''
        switch (type) {
            case OtherProjectCalculationBaseConstant.zljr :{
                res = '1'
                break;
            }
            case OtherProjectCalculationBaseConstant.zgj :{
                res = '2'
                break;
            }
            case OtherProjectCalculationBaseConstant.clzgj :{
                res = '2.1'
                break;
            }
            case OtherProjectCalculationBaseConstant.sbzgj :{
                res = '2.2'
                break;
            }
            case OtherProjectCalculationBaseConstant.zygczgj :{
                res = '2.3'
                break;
            }
            case OtherProjectCalculationBaseConstant.jrg :{
                res = '3'
                break;
            }
            case OtherProjectCalculationBaseConstant.zcbfwf :{
                res = '4'
                break;
            }


        }
        return res
    }

    //暂列金额
    async convertZlje(单位工程, otherProjectProvisionals) {
        let zljeMxArray = new Array();
        let Zlje ={};
        if(ObjectUtils.isEmpty(otherProjectProvisionals)){
            let ZljeMx = {};
            ZljeMx.Xh = '';
            ZljeMx.Mc = '';
            ZljeMx.Dw = '';
            ZljeMx.Zdje = '0';
            ZljeMx.Csxs = '0';
            ZljeMx.Jxse = '0';
            ZljeMx.Bz = '';
            zljeMxArray.push(ZljeMx)
            单位工程.Zlje = zljeMxArray;
            return
        }
        for (let i = 0; i < otherProjectProvisionals.length; i++) {
            let otherProjectProvisional = otherProjectProvisionals[i];
            let ZljeMx = {};
            ZljeMx.Xh = otherProjectProvisional.dispNo;
            ZljeMx.Mc = otherProjectProvisional.name;
            ZljeMx.Dw = otherProjectProvisional.unit;
            ZljeMx.Zdje = otherProjectProvisional.provisionalSum;
            ZljeMx.Csxs = NumberUtil.getDefault(otherProjectProvisional.taxRemoval);
            ZljeMx.Jxse = NumberUtil.getDefault(otherProjectProvisional.jxTotal);
            ZljeMx.Bz = otherProjectProvisional.description;
            zljeMxArray.push(ZljeMx)
        }
        Zlje.ZljeMx =zljeMxArray;
        单位工程.Zlje = Zlje;
    }

    /**
     * 材料暂估价
     * @param 单位工程
     * @param otherProjectClZgjs
     * @returns {Promise<void>}
     */
    async convertClzg(单位工程, otherProjectClZgjs) {
        let clzgMxArray = new Array();
        let Clzg ={};
        if(ObjectUtils.isEmpty(otherProjectClZgjs)){
            let ClzgMx = {};
            ClzgMx.Xh = '';
            ClzgMx.RcjId = '';
            ClzgMx.Clbh  = '';
            ClzgMx.Mc = '';
            ClzgMx.Ggxh = '';
            ClzgMx.Dw  = '';
            ClzgMx.Sl  = '0';


            ClzgMx.Dj  = '0';
            clzgMxArray.push(ClzgMx)
            单位工程.Clzg =clzgMxArray;
            return
        }

        for (let i = 0; i < otherProjectClZgjs.length; i++) {
            let otherProjectZgj = otherProjectClZgjs[i];
            let ClzgMx = {};

            ClzgMx.Xh = otherProjectZgj.dispNo;
            ClzgMx.RcjId = '';
            ClzgMx.Clbh  = otherProjectZgj.unit;
            ClzgMx.Mc = otherProjectZgj.name;
            ClzgMx.Ggxh = otherProjectZgj.attr;
            ClzgMx.Dw  = otherProjectZgj.unit;
            ClzgMx.Sl  = '';
            ClzgMx.Dj  = otherProjectZgj.price;
            clzgMxArray.push(ClzgMx)
        }
        Clzg.ZljeMx =clzgMxArray;
        单位工程.Clzg = Clzg;
    }

    //设备暂估价
    async convertSbzg(单位工程, otherProjectSbZgjs) {
        let SbzgMxArray = new Array();
        let Sbzg ={};
        if(ObjectUtils.isEmpty(otherProjectSbZgjs)){
            let SbzgMx = {};
            SbzgMx.Xh = '';
            SbzgMx.Rcjld = '';
            SbzgMx.Sbbh = '';
            SbzgMx.Mc  = '';
            SbzgMx.Ggxh  = '';
            SbzgMx.Dw = '';
            SbzgMx.Sl = '0';
            SbzgMx.Dj = '0';
            SbzgMx.Hj = '0';
            SbzgMx.Bz = '';
            SbzgMxArray.push(SbzgMx);
            单位工程.Sbzg = SbzgMxArray;
            return
        }
    }

    /**
     * 专用工程暂估价
     * @param 单位工程
     * @param otherProjectZygcZgjs
     * @returns {Promise<void>}
     */
    async convertZygczg(单位工程, otherProjectZygcZgjs) {
        let Zygczg = {}
        let zygczgMxArray =new Array();
        if(ObjectUtils.isEmpty(otherProjectZygcZgjs)){
            let ZygczgMx = {};
            ZygczgMx.Xh = '';
            ZygczgMx.Mc = '';
            ZygczgMx.Gcnr = '';
            ZygczgMx.Dw  = '';
            ZygczgMx.Je = '0';
            ZygczgMx.Csxs  = '0';
            ZygczgMx.Jxse = '0';
            ZygczgMx.Bz = '';
            zygczgMxArray.push(ZygczgMx);
            单位工程.Zygczg =zygczgMxArray;
            return
        }

        for (let i = 0; i < otherProjectZygcZgjs.length; i++) {
            let otherProjectZygcZgj = otherProjectZygcZgjs[i];
            let ZygczgMx = {};
            ZygczgMx.Xh = otherProjectZygcZgj.dispNo ;
            ZygczgMx.Gcmc = otherProjectZygcZgj.name;
            ZygczgMx.Gcnr = otherProjectZygcZgj.content;
            ZygczgMx.Dw  = otherProjectZygcZgj.unit ;
            ZygczgMx.Je = otherProjectZygcZgj.total ;
            ZygczgMx.Csxs  = otherProjectZygcZgj.taxRemoval ;
            ZygczgMx.Jxse = otherProjectZygcZgj.jxTotal ;
            ZygczgMx.Bz = otherProjectZygcZgj.description ;
            zygczgMxArray.push(ZygczgMx);
        }
        Zygczg.ZygczgMx = zygczgMxArray;
        单位工程.Zygczg =Zygczg;
    }

    /**
     * 总承包服务费
     * @param 单位工程
     * @param otherProjectServiceCosts
     * @returns {Promise<void>}
     */
    async convertZcbfwf(单位工程, otherProjectServiceCosts) {
        let Zcbfwf ={};
        if(ObjectUtils.isEmpty(otherProjectServiceCosts)){
            单位工程.Zcbfwf = Zcbfwf;
            return
        }
        let parentList = otherProjectServiceCosts.filter(item =>item.parentId === null);
        let sonList = otherProjectServiceCosts.filter(item =>item.parentId !== null);

        let  zcbfwfMxS = new Array()

        if(!ObjectUtils.isEmpty(sonList)){
            for (let i = 0; i < sonList.length; i++) {
                let filterElement = sonList[i];
                let ZcbfwfMx = {};
                ZcbfwfMx.Xh = filterElement.dispNo;
                ZcbfwfMx.Mc = filterElement.fxName;
                ZcbfwfMx.Xmjz = filterElement.xmje;
                ZcbfwfMx.Fl = filterElement.rate;
                ZcbfwfMx.Je = filterElement.fwje;
                zcbfwfMxS.push(ZcbfwfMx);
            }
        }


        if(ObjectUtils.isEmpty(parentList)){
            //如果没有标题只生成明细数据
            if(!ObjectUtils.isEmpty(sonList)){

                Zcbfwf.ZcbfwfMx =zcbfwfMxS;
                单位工程.Zcbfwf = Zcbfwf;
                return ;
            }
        }



        let ZcbfwfBtArray = new Array();

        for (let i = 0; i < parentList.length; i++) {

            let element = parentList[i];

            let ZcbfwfBt = {};
            ZcbfwfBt.Xh = element.dispNo;
            ZcbfwfBt.Mc = element.fxName;
            ZcbfwfBt.Je = element.fwje;

            let zcbfwfMxArray = new Array();

            //获取当前标题下的子项
            let filter = sonList.filter(item => item.parentId === element.sequenceNbr );
            if(!ObjectUtils.isEmpty(filter)){
                for (let j = 0; j < filter.length; j++) {
                    let filterElement = filter[j];
                    let ZcbfwfMx = {};
                    ZcbfwfMx.Xh = filterElement.dispNo;
                    ZcbfwfMx.Mc = filterElement.fxName;
                    ZcbfwfMx.Xmjz = filterElement.xmje;
                    ZcbfwfMx.Fl = filterElement.rate;
                    ZcbfwfMx.Je = filterElement.fwje;
                    zcbfwfMxArray.push(ZcbfwfMx);
                }
                ZcbfwfBt.ZcbfwfMx = zcbfwfMxArray;

            }
            ZcbfwfBtArray.push(ZcbfwfBt)

        }
        Zcbfwf.ZcbfwfBt =ZcbfwfBtArray;
        Zcbfwf.ZcbfwfMx =zcbfwfMxS;
        单位工程.Zcbfwf = Zcbfwf;

    }

    /**
     * 签证与索赔
     * @param 单位工程
     * @param otherProjectVisaAndClaim
     * @returns {Promise<void>}
     */
    async convertVisaAndClaim(单位工程, otherProjectVisaAndClaim) {
        let VisaAndClaimArray = new Array();
        let VisaAndClaim ={};
        if(ObjectUtils.isEmpty(otherProjectVisaAndClaim)){
            let VisaAndClaimMx = {};
            VisaAndClaimMx.Xh = '';
            VisaAndClaimMx.Mc = '';
            VisaAndClaimMx.Dw = '';
            VisaAndClaimMx.Zdje = '0';
            VisaAndClaimMx.Csxs = '0';
            VisaAndClaimMx.Jxse = '0';
            VisaAndClaimMx.Bz = '';
            VisaAndClaimArray.push(VisaAndClaimMx)
            单位工程.VisaAndClaim = VisaAndClaimArray;
            return
        }
        for (let i = 0; i < otherProjectVisaAndClaim.length; i++) {
            let VisaAndClaim = otherProjectVisaAndClaim[i];
            let VisaAndClaimMx = {};
            VisaAndClaimMx.Xh = VisaAndClaim.dispNo;
            VisaAndClaimMx.Mc = VisaAndClaim.project;
            VisaAndClaimMx.Dw = VisaAndClaim.type;
            VisaAndClaimMx.Zdje = VisaAndClaim.total;
            VisaAndClaimMx.Csxs = NumberUtil.getDefault(VisaAndClaim.price);
            VisaAndClaimMx.Jxse = NumberUtil.getDefault(VisaAndClaim.amount);
            VisaAndClaimMx.Bz = VisaAndClaim.zhPrice;
            VisaAndClaimArray.push(VisaAndClaimMx)
        }
        VisaAndClaim.VisaAndClaimMx = VisaAndClaimArray;
        单位工程.VisaAndClaim = VisaAndClaim;
    }

    //计日工
    async convertJrg(单位工程, otherProjectDayWorks) {
        let Jrg ={}
        if(ObjectUtils.isEmpty(otherProjectDayWorks)){
            单位工程.jrg =Jrg;
            return;
        }
        let parentList = otherProjectDayWorks.filter(item =>item.parentId === null);
        let sonList = otherProjectDayWorks.filter(item =>item.parentId !== null);

        let JrgMxS  = new Array();
        if(ObjectUtils.isEmpty(parentList)){
            //如果没有标题只生成明细数据
            if(!ObjectUtils.isEmpty(sonList)){

                for (let i = 0; i < sonList.length; i++) {
                    let sonListElement = sonList[i];
                    let  JrgMx = {};
                    JrgMx.Xh = sonListElement.dispNo;
                    JrgMx.Mc = sonListElement.worksName;
                    JrgMx.Dw = sonListElement.unit;
                    JrgMx.Zdsl = sonListElement.tentativeQuantity;
                    JrgMx.Zhdj = sonListElement.price;
                    JrgMx.Zhhj = sonListElement.total;
                    JrgMx.Csxs = sonListElement.taxRemoval;
                    JrgMx.Jxse = sonListElement.jxTotal;
                    JrgMxS.push(JrgMx)
                }
                单位工程.JrgMx = JrgMxS;
                return ;
            }
        }else {
            let JrgBtArray = new Array();
            for (let i = 0; i < parentList.length; i++) {
                let parentListElement = parentList[i];
                let JrgBt = {};
                JrgBt.Xh = parentListElement.dispNo;
                JrgBt.Mc = parentListElement.worksName;
                JrgBt.Je = parentListElement.total;
                JrgBt.LB = ''
                if(JrgBt.Mc!== undefined){
                    //遍历枚举
                    for(let key in CostTypeJrgEnum){
                        if(JrgBt.Mc === CostTypeJrgEnum[key].desc){
                            JrgBt.LB = CostTypeJrgEnum[key].code;
                        }
                    }
                }
                let JrgMxArray  = new Array();
                //获取当前标题下的子项
                let filter = sonList.filter(item => item.parentId === parentListElement.sequenceNbr );

                if(!ObjectUtils.isEmpty(filter)){
                    for (let j = 0; j < filter.length; j++) {
                        let filterElement = filter[j];
                        let  JrgMx = {};
                        JrgMx.Xh   = filterElement.dispNo;
                        JrgMx.Mc   = filterElement.worksName;
                        //规格型号
                        JrgMx.Ggxh = filterElement.specification;
                        JrgMx.Dw   = filterElement.unit;
                        JrgMx.Zdsl = filterElement.tentativeQuantity;
                        JrgMx.Zhdj = filterElement.price;
                        JrgMx.Zhhj = filterElement.total;
                        JrgMx.Csxs = filterElement.taxRemoval;
                        JrgMx.Jxse = filterElement.jxTotal;
                        JrgMxArray.push(JrgMx)

                    }
                    JrgBt.JrgMx = JrgMxArray;
                }
                JrgBtArray.push(JrgBt)
            }
            Jrg.JrgBt =JrgBtArray;
            单位工程.Jrg =Jrg;
        }

    }



    //人材机汇总
    async convertRcjhz(单位工程, unitProject) {

        let 人材机汇总表 ={};

        let rcjArray =this.rcjArray;

        if(ObjectUtils.isEmpty(rcjArray)){
            单位工程.人材机汇总表 =人材机汇总表;
            return
        }
        //将二次解析的父级过滤掉
        let rcjArrayFilter = rcjArray.filter(i=>!(i.markSum ===1 && (i.levelMark ===RcjLevelMarkConstant.SINK_JX ||  i.levelMark ===RcjLevelMarkConstant.SINK_PB)));

        if(ObjectUtils.isEmpty(rcjArrayFilter)){
            单位工程.人材机汇总表 =人材机汇总表;
            return
        }
        let 人材机子目Array = new Array();
        for (let i = 0; i < rcjArrayFilter.length; i++) {
            let rcj = rcjArrayFilter[i];
            let 人材机子目 = {};
            人材机子目.序号 = i+1;
            人材机子目.编码 = rcj.materialCode;
            人材机子目.类型 = await this.getFeeTypeByKind(rcj.kind);
            人材机子目.名称 = rcj.materialName;
            人材机子目.计量单位 = rcj.unit;
            人材机子目.数量 = await this.dualZBType( rcj.totalNumber);
            人材机子目.预算价 =await this.dualZBType( rcj.dePrice);
            人材机子目.除税系数 =ObjectUtils.isEmpty( rcj.taxRemoval)?0:rcj.taxRemoval
            人材机子目.市场价 = await this.dualZBType(rcj.marketPrice);
            人材机子目.除税市场价 = await this.dualZBType(NumberUtil.costPriceAmountFormat( NumberUtil.subtract(人材机子目.市场价,NumberUtil.multiply(人材机子目.市场价,NumberUtil.divide100(rcj.taxRemoval)))));//取 人材机汇总对应人材机 市场价列 - 进项税额列
            人材机子目.结算价 = await this.dualZBType(rcj.marketPrice); //取 人材机汇总对应人材机 市场价列
            人材机子目.暂估标志 = rcj.ifProvisionalEstimate==1?1:0  //取 人材机汇总对应人材机暂估状态，勾选展示为1，未勾选展示为0
            人材机子目.主要材料标志 = this.zyclbzBy主要材料设备表(rcj)
            人材机子目.规格型号 = rcj.specification;
            人材机子目.质量等级 = rcj.qualityGrade;
            人材机子目.厂家 = rcj.manufactor;
            人材机子目.产地 = rcj.producer;
            人材机子目.含税合价 = await this.dualZBType(rcj.total);
            人材机子目.除税合价 =  await this.dualZBType(NumberUtil.subtract(rcj.total,rcj.jxTotal));
            人材机子目.销项税额合计 = NumberUtil.costPriceAmountFormat( NumberUtil.multiply( rcj.total,NumberUtil.divide100(unitProject.projectTaxCalculation.outputTaxRate)));
            人材机子目.进项税额合计 = await this.dualZBType(rcj.jxTotal);
            人材机子目.供应时间 = '';
            人材机子目.甲供数量 =ObjectUtils.isEmpty( rcj.donorMaterialNumber)?0:rcj.donorMaterialNumber;
            人材机子目.送达地点 = rcj.deliveryLocation;
            人材机子目.备注 = rcj.description;
           

            人材机子目Array.push(人材机子目)
        }
        人材机汇总表.人材机子目 = 人材机子目Array;
        单位工程.人材机汇总表 = 人材机汇总表;
    }


    //增值税进项税汇总表
    async convertZzsjxshzb(单位工程, inputTaxDetails) {
        let 增值税进项税额计算汇总表 ={};
        //查询 增值税进项税额计算汇总数据
        let 进项税额费用子目Array = new Array();
        if(inputTaxDetails){
            for (let i = 0; i < inputTaxDetails.length; i++) {
                let findElement = inputTaxDetails[i];
                let 进项税额费用子目 = {};
                进项税额费用子目.序号 =i+1+"";
                进项税额费用子目.名称 =findElement.name;
                进项税额费用子目.计算基础 =findElement.calculateFormula;
                进项税额费用子目.除税系数 =ObjectUtils.isEmpty(findElement.rate)?100:findElement.rate;
                进项税额费用子目.金额 =findElement.price
                进项税额费用子目.备注 =findElement.remark;

                进项税额费用子目Array.push(进项税额费用子目)
            }
            增值税进项税额计算汇总表.进项税额费用子目 = 进项税额费用子目Array;
        }
        单位工程.增值税进项税额计算汇总表 = 增值税进项税额计算汇总表;
    }

    /**
     * 主要材料设备
     * 红成  如果勾选了
     * @param 单位工程
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertZyClSb(单位工程, unitProject) {
        let 主要材料设备明细表 = {};
        if (ObjectUtils.isEmpty(this.rcjArray)) {
            单位工程.主要材料设备明细表 = 主要材料设备明细表;
            return;
        }

        let res = this.rcjArray.filter(i => i.mainMaterial == 1);
        if (ObjectUtils.isEmpty(res)) {
            单位工程.主要材料设备明细表 = 主要材料设备明细表;
            return;
        }

        // 将二次解析的父级过滤掉
        let rcjArrayFilter = res.filter(i => !(i.markSum === 1 && (i.levelMark === RcjLevelMarkConstant.SINK_PB || i.levelMark === RcjLevelMarkConstant.SINK_JX)));
        let clRcj = rcjArrayFilter.filter(item => item.kind != 4);//设备
        let sbRcj = rcjArrayFilter.filter(item => item.kind == 4);//材料



        // 处理材料
        主要材料设备明细表.主要材料 = {
            材料子目: clRcj.map((rcj, index) =>this. createSubItem(rcj, index, unitProject.projectTaxCalculation.outputTaxRate))
        };

        // 处理设备
        主要材料设备明细表.主要设备 = {
            设备子目: sbRcj.map((rcj, index) => this.createSubItem(rcj, index, unitProject.projectTaxCalculation.outputTaxRate))
        };

        单位工程.主要材料设备明细表 = 主要材料设备明细表;
    }



    //招标人供应材料设备明细表
    async convertZbrClSb(单位工程, unitProject) {
        let 招标人供应材料设备明细表 = {}
        let res =  this.rcjArray;
        if (ObjectUtils.isEmpty(res)){
            单位工程.招标人供应材料设备明细表 = 招标人供应材料设备明细表;
            return;
        }
        let jgRcj = res.filter(item => item.ifDonorMaterial === 1);
        if (ObjectUtils.isEmpty(jgRcj)){
            单位工程.招标人供应材料设备明细表 = 招标人供应材料设备明细表;
            return;
        }else {

            let clRcj = jgRcj.filter(item => item.kind != 4);//设备
            let sbRcj = jgRcj.filter(item => item.kind == 4);//材料

            // 处理材料
            招标人供应材料设备明细表.甲供材料 = {
                材料子目: clRcj.map((rcj, index) =>this.createSubItem(rcj, index, unitProject.projectTaxCalculation.outputTaxRate))
            };

            // 处理设备
            招标人供应材料设备明细表.甲供设备 = {
                设备子目: sbRcj.map((rcj, index) => this.createSubItem(rcj, index, unitProject.projectTaxCalculation.outputTaxRate))
            };



            单位工程.招标人供应材料设备明细表 = 招标人供应材料设备明细表;
        }


    }

    //安全文明施工费
    async convertAqwmsgf(单位工程, unitProject) {
        let 安全文明施工费列表 ={};

        let args ={};
        args.constructId = unitProject.constructId;
        args.singleId = unitProject.spId;
        args.unitId = unitProject.sequenceNbr;
        let safeFee = await this.service.safeFeeService.getSafeFee(args);
        if(ObjectUtils.isEmpty(safeFee)){
            单位工程.安全文明施工费列表 = 安全文明施工费列表;
            return
        }

        let 安全文明施工费用子目Array = new Array();
        for (let i = 0; i < safeFee.length; i++) {
            let safeFeeElement = safeFee[i];
            let 安全文明施工费用子目 ={};

            安全文明施工费用子目.序号 = i+1+"";
            安全文明施工费用子目.取费专业编码 = safeFeeElement.feeCode;
            安全文明施工费用子目.取费专业名称 = safeFeeElement.costMajorName;
            安全文明施工费用子目.取费基数 =   safeFeeElement.caculateBaseCode;
            安全文明施工费用子目.取费基数金额 =await this.dualZBType(safeFeeElement.costFeeBase) ;
            安全文明施工费用子目.费率 = safeFeeElement.basicRate;
            安全文明施工费用子目.金额 = await this.dualZBType(safeFeeElement.feeAmount);

            安全文明施工费用子目Array.push(安全文明施工费用子目);
        }
        安全文明施工费列表.安全文明施工费用子目 = 安全文明施工费用子目Array;
        单位工程.安全文明施工费列表 = 安全文明施工费列表;
    }

    //规费
    async convertGf(单位工程, unitProject) {
        let 规费列表 ={};
        let args ={};
        args.constructId = unitProject.constructId;
        args.singleId = unitProject.spId;
        args.unitId = unitProject.sequenceNbr;
        let gfeeFee = await this.service.gfeeService.getGfeeFee(args);

        if(ObjectUtils.isEmpty(gfeeFee) || unitProject.deStandardReleaseYear=='22'){
            单位工程.规费列表 = 规费列表;
            return;
        }
        let 规费子目Array = new Array();
        for (let i = 0; i < gfeeFee.length; i++) {
            let 规费子目 = {};
            let gfeeFeeElement = gfeeFee[i];
            规费子目.序号 = i+1+"";
            规费子目.取费专业编码 =gfeeFeeElement.feeCode;
            规费子目.取费专业名称 = gfeeFeeElement.costMajorName;
            规费子目.取费基数 = ObjectUtils.isEmpty(unitProject.feeCalculateBaseList.find(item=>item.type =='gf'))?'RGF_DEJ+JXF_DEJ':unitProject.feeCalculateBaseList.find(item=>item.type =='gf').code;
            规费子目.取费基数金额 = gfeeFeeElement.costFeeBase;
            规费子目.费率 = gfeeFeeElement.gfeeRate;
            规费子目.金额 = gfeeFeeElement.feeAmount;
            规费子目Array.push(规费子目)
        }
        规费列表.规费子目 = 规费子目Array;
        单位工程.规费列表 = 规费列表;

    }

    /**
     * 转换Dekbz
     * @returns {Promise<void>}
     */
    async convertDekbz(dekbz){

        if(ObjectUtils.isEmpty(dekbz)){
            return '';
        }else {
            let res = '';
            switch (dekbz) {
                case '河北省建筑工程消耗量定额（2012）' :
                    res= '12jz';
                    break;
                case '河北省装饰装修工程消耗量定额（2012）' :
                    res= '12zs';
                    break;
                case '河北省安装工程消耗量定额（2012）' :
                    res= '12az';
                    break;
                case '河北省市政工程消耗量定额（2012）' :
                    res= '12sz';
                    break;
                case '河北省仿古建筑工程消耗量定额（2013）' :
                    res= '13fg';
                    break;
                case '河北省园林绿化工程消耗量定额（2013）' :
                    res= '13yl';
                    break;
                case '河北省房屋修缮工程消耗量定额（土建分册）（2013）' :
                    res= '13xsjz';
                    break;
                case '河北省房屋修缮工程消耗量定额（安装分册）（2013）' :
                    res= '13xsaz';
                    break;
                case '河北省市政设施维修养护工程消耗量定额（2013）' :
                    res= '13szyh';
                    break;
                case '河北省城市园林绿化养护管理定额（2014）' :
                    res= '13ylyh';
                    break;
                case '古建（明清）修缮工程消耗量定额（2014）' :
                    res= '14gjxs';
                    break;
                case '京津翼城市地下综合管廊工程消耗量定额（2018）' :
                    res= '18gl';
                    break;
                case '城市轨道交通工程预算定额河北省消耗量定额（2015）' :
                    res= '15gd';
                    break;
                case '河北省人防工程预算定额（2015）' :
                    res= '15rf';
                    break;
                case '河北省装配式建筑工程定额（试行）（2018）' :
                    res= '18zp';
                    break;
                case '独立费' :
                    res= '独';
                    break;
                case '自编定额及企业定额等' :
                    res= '其他';
                    break;
                case '2022年《河北省建设工程消耗量标准》-建筑工程' :
                    res= '22jz';
                    break;
                case '2022年《河北省建设工程消耗量标准》-装饰装修工程' :
                    res= '22zs';
                    break;
                case '2022年《河北省建设工程消耗量标准》-安装工程' :
                    res= '22az';
                    break;
                case '2022年《河北省建设工程消耗量标准》-市政工程' :
                    res= '22sz';
                    break;
                default :
                    res = '';
            }
            return  res;
        }
    }

    /**
     * 用所有人材机构成
     * @param rcjList
     * @param rcjDetailList
     * @returns {Promise<void>}
     */
    async convertRcjMap(rcjList) {



        if(ObjectUtils.isEmpty(rcjList)){
            return new Map();
        }
        return  ArrayUtil.group(rcjList,'deId');


    }



}

JsonToXmlHZBService.toString = () => '[class JsonToXmlHZBService]';
module.exports = JsonToXmlHZBService;
