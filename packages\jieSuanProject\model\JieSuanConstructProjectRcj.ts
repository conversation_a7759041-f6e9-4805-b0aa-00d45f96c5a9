import {ConstructProjectRcj} from "../../../electron/model/ConstructProjectRcj";


export class JieSuanConstructProjectRcj extends ConstructProjectRcj {


    //结算除税系数
    public jieSuanTaxRemoval: number;

    //结算合计数量
    public jieSuanTotalNumber: number;

    //结算合价
    public jieSuanTotal: number;

    //调差工程量
    public jieSuanStageDifferenceQuantity: number;

    //结算差额进项税
    public settlementPriceDifferencInputTax: number;

    //价差合计
    public jieSuanPriceDifferencSum: number;

    //结算进项税额
    public jieSunJxTotal: number;

    public riskAmplitudeRangeMax: string;//涨幅
    public riskAmplitudeRangeMin: string;//跌幅

    //基期价数据来源
    public baseJournalsourcePrice: string;

    /**
     * 指标名称
     */
    public indicatorName: string;

    /**
     * 指标名称对应的专业
     */
    public indicatorNameOfMajor: string;

    /**
     * 指标单位
     */
    public indicatorUnit: string;

    /**
     * 指标转换系数
     */
    public  conversionCoefficient:number








}
