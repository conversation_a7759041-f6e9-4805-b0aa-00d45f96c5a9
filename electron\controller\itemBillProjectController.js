const {Controller} = require("../../core");
const {ResponseData} = require("../utils/ResponseData");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const _ = require("lodash");
const RcjReplaceStrategy = require("../rcj_handle/replace/replaceRcjStrategy");
const {ObjectUtils} = require("../utils/ObjectUtils");
class ItemBillProjectController extends Controller {

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    unitPriceService = this.service.unitPriceService;
    itemBillProjectService = this.service.itemBillProjectService;
    itemBillProjectOptionService = this.service.itemBillProjectOptionService;

    /**
     * 初始化分部分项
     * @param constructId
     * @param singleId
     * @param unitId 单位id
     * @returns {Promise<void>}
     */
    async initUnitFb(args) {
        let {constructId, singleId, unitId} = args;
        const result = await itemBillProjectService.initUnitFb(constructId, singleId, unitId);
        return ResponseData.success(result);
    }

    /**
     * 分部分项目录树
     * @param constructId
     * @param singleId
     * @param unitId 单位id
     * @returns {Promise<void>}
     */
    async queryUnitBranchTree(args) {
        let {constructId, singleId, unitId} = args;

        const result = await itemBillProjectService.queryUnitBranchTree(args);
        return ResponseData.success(result);
    }

    /**
     * 查分部分项
     * @param sequenceNbr id
     * @param kind 0查所有 ; 01 04 查分部子分部及其下的清单定额
     * @see ItemBillProject#kind
     * @returns {Promise<ItemBillProject[]>}
     * @deprecated
     */
    async queryItemBillList(args) {

        let {sequenceNbr, kind} = args;

        const result = await itemBillProjectService.selectByKind(sequenceNbr, kind);
        return ResponseData.success(result);
    }

    /**
     * 展开
     */
    open(args) {
        let {constructId, singleId, unitId, pointLine} = args;
        const result = this.itemBillProjectOptionService.openLine(constructId, singleId, unitId, pointLine);
        return ResponseData.success(true);
    }

    /**
     * 折叠
     */
    close(args) {
        let {constructId, singleId, unitId, pointLine} = args;
        const result = this.itemBillProjectOptionService.closeLine(constructId, singleId, unitId, pointLine);
        return ResponseData.success(true);
    }

    /**
     * 锁定解锁
     * @param args
     * @return {ResponseData}
     */
    lockQd(args) {
        let {constructId, singleId, unitId, pointLine} = args;
        this.service.itemBillProjectOptionService.lockQd(constructId, singleId, unitId, pointLine.sequenceNbr);

        return ResponseData.success(true);
    }
    unLockQd(args) {
        let {constructId, singleId, unitId, pointLine} = args;
        this.service.itemBillProjectOptionService.unLockQd(constructId, singleId, unitId, pointLine.sequenceNbr);

        return ResponseData.success(true);
    }
    lockAll(args) {
        let {constructId, singleId, unitId} = args;
        let all = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);

        if (!all || all.length == 0) {
            return ResponseData.success(true);
        } else {
            for (let i = 0 ; i < all.length ; ++i) {
                if (all[i].kind === "03") {
                    all[i].isLocked = 1;
                    this.service.baseBranchProjectOptionService.handleDeAddQdStatus(all,all[i],1);
                }
            }
        }
        return ResponseData.success(true);
    }
    unLockAll(args) {
        let {constructId, singleId, unitId} = args;
        let all = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);

        if (!all || all.length == 0) {
            return ResponseData.success(true);
        } else {
            for (let i = 0 ; i < all.length ; ++i) {
                if (all[i].kind === "03") {
                    delete all[i].isLocked;
                    this.service.baseBranchProjectOptionService.handleDeAddQdStatus(all,all[i],2);
                }
            }
        }

        return ResponseData.success(true);
    }

    /**
     * 复制行
     * @param args
     * @returns {ResponseData}
     */
    copy(args) {
        let {sequenceNbrs} = args;
        if(_.isEmpty(sequenceNbrs)){
            return ResponseData.fail("请选择要复制的行");
        }
        const result = this.itemBillProjectOptionService.copyLine(sequenceNbrs);
        return ResponseData.success(result);
    }

   /*单位工程全局展开*/
    spread(args){
        const result = this.itemBillProjectOptionService.spread(args);
        return ResponseData.success(result);
    }
    /*获取分部深度*/
    getFbDeep(args){
        const result = this.itemBillProjectOptionService.getFbDeep(args);
        return ResponseData.success(result);
    }

    cut(args){
        let {sequenceNbrs} = args;
        if(_.isEmpty(sequenceNbrs)){
            return ResponseData.fail("请选择要剪切的行");
        }
        const result = this.itemBillProjectOptionService.cut(sequenceNbrs);
        return ResponseData.success(result);
    }
    //展开收起
    expansionLevel(){

    }

    /**
     * 粘贴
     * @param pointLine 前端鼠标点击的行
     * @param menuType
     */
    async pasteLine(args){
        let {constructId, singleId, unitId,pointLine} = args;
        if(_.isEmpty(pointLine)){
            return ResponseData.fail("请选择要粘贴的行");
        }
        const result = await this.itemBillProjectOptionService.pasteLine(constructId, singleId, unitId,pointLine);

        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");
        return ResponseData.success(result);
    }


    /**
     * 复制 编辑区的主材设备
     * @param args
     * @returns {ResponseData}
     */
    copyZcSb(args) {
        let {sequenceNbrs} = args;
        if(_.isEmpty(sequenceNbrs)){
            return ResponseData.fail("请选择要复制的行");
        }
        const result = this.itemBillProjectOptionService.copyLinezcsb(sequenceNbrs);
        return ResponseData.success(result);
    }

    /**
     * 粘贴  编辑区的主材设备
     * @param pointLine 前端鼠标点击的行
     * @param type   csxm fbfx
     */
    async pasteLineZcSb(args){
        let {constructId, singleId, unitId,pointLine,type} = args;
        if(_.isEmpty(pointLine)){
            return ResponseData.fail("请选择要粘贴的行");
        }
        const result = await this.itemBillProjectOptionService.pasteLineZcsb(constructId, singleId, unitId,pointLine,type);
        return ResponseData.success(result);
    }

    /**
     * 批量删除
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async batchDelete(args) {
        let {constructId, singleId, unitId, sequenceNbrs} = args;
        if(!_.isArray(sequenceNbrs)){
            return ResponseData.fail("参数异常sequenceNbrs 应为数组");
        }
        try {
            const result = await this.itemBillProjectOptionService.batchDelete(constructId, singleId, unitId, sequenceNbrs);
            await this.service.management.sycnTrigger("unitDeChange");
            await this.service.management.trigger("itemChange");
            await this.service.autoCostMathService.autoCostMath({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                countCostCodeFlag: true
            });
            return ResponseData.success(result);
        } catch (e) {
            console.log(e);
            return ResponseData.fail(e.message);
        }
    }

    /**
     * 结构新增
     * @param args
     * @returns {ResponseData}
     */
    async save(args) {
        let {constructId, singleId, unitId, pointLine, newLine, rootLineId} = args;
        const result = await this.itemBillProjectOptionService.insertLine(constructId, singleId, unitId, pointLine, newLine, rootLineId);
         //处理升降级状态
        this.service.baseBranchProjectOptionService.setItemDtaStatus(PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes());
        return ResponseData.success(result);
    }

    searchPonitAndChild(args) {
        return ResponseData.success(this.itemBillProjectOptionService.searchPonitAndChild(args));
    }
    //获取单位工程或分部分项清单数据v1
    queryBranchDataByFbIdV1(args) {
        let {constructId, singleId, unitId, pageNum, pageSize, sequenceNbr,isAllFlag,colorList} = args;
        let result = this.itemBillProjectOptionService.getFbFx(constructId, singleId, unitId, sequenceNbr, pageNum, pageSize,isAllFlag,colorList);
        result.data.forEach(e => this.itemBillProjectOptionService.dataDeal(e,1));
        return ResponseData.success(result);
    }
    searchForsequenceNbr(args) {
        let {constructId, singleId, unitId,sequenceNbr} = args;
        const result = this.itemBillProjectOptionService.searchForsequenceNbr(constructId, singleId, unitId, sequenceNbr);
        return ResponseData.success(result);
    }


    // /**
    //  * 从清单定额索引中点击插入
    //  */
    // async replaceFromIndexPage(args) {
    //     let {constructId, singleId, unitId, pointLine, kind, indexId, unit, rcjFlag} = args;
    //     let res = await this.service.stepItemCostService.fillDataFromIndexPage(constructId, singleId, unitId, pointLine, kind, indexId, unit, rcjFlag);
    //
    //     await this.service.management.sycnTrigger("unitDeChange");
    //
    //     await this.service.management.trigger("itemChange");
    //     return ResponseData.success(res);

    // }

    /**
     * 从索引界面添加数据
     * @param constructId
     * @param singleId
     * @param unitWorkId
     * @param pointLine
     * @param createType
     * @param indexId
     * @param unit
     * @returns {ResponseData}
     */
    async fillFromIndexPage(args) {
        let {constructId, singleId, unitId, pointLine, kind, indexId, unit, rootLineId, rcjFlag,libraryCode} = args;
        //标记前端  传来修改
        let res = await this.itemBillProjectOptionService.fillDataFromIndexPage(constructId, singleId, unitId, pointLine, kind, indexId, unit, rootLineId, rcjFlag,null,true,libraryCode);
        //处理升降级状态
        this.service.baseBranchProjectOptionService.setItemDtaStatus(PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes());
        /*        this.service.management.trigger("controllerItemChange");
                await this.service.management.sycnTrigger("unitDeChange");

                await this.service.management.trigger("itemChange");*/
        return ResponseData.success(res);
    }

    /**
     * 从清单定额索引中点替换
     */
    async replaceFromIndexPage(args) {
        let {constructId, singleId, unitId, unitWorkId, selectId, replaceId, type, conversionCoefficient,unit, kind,libraryCode} = args;
        if (!unitId) {
            unitId = unitWorkId;
        }
        //true 标记前端 传来修改
        let res = await this.itemBillProjectOptionService.replaceFromIndexPage(constructId, singleId, unitId, selectId, replaceId, type, conversionCoefficient,unit, kind,false,libraryCode);
        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");
        return ResponseData.success(res);
    }

    /**
     * 明细区人材机替换
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async retailAreaRcjReplace(args){
        let {constructId, singleId, unitId, selectLine, replaceLine, de} = args;
        if (ObjectUtils.isEmpty(args.conversionCoefficient)){
            args.conversionCoefficient = 1;
        }
        let rcjReplaceStrategy = new RcjReplaceStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId)});
        let result =await rcjReplaceStrategy.execute({selectLine, replaceLine, de, conversionCoefficient:args.conversionCoefficient,rcjSource:1,conversionFlag:true});
        let constructProjectRcj = PricingFileFindUtils.getUnit(constructId, singleId, unitId).constructProjectRcjs.find(rcj=>rcj.sequenceNbr==result.sequenceNbr);
        //处理人材机换算信息
        let conversionInfo = this.service.rcjProcess.updateRcjSyncDeConversionInfo(constructId, singleId, unitId,de.sequenceNbr,selectLine,"replace",constructProjectRcj,null,1);
        let {line:deReal} = this.service.baseBranchProjectOptionService.findLineOnlyById(de.sequenceNbr);
        deReal.name = `${deReal.name} ${conversionInfo.conversionNameExplain}`;
        deReal.nameSuffixHistory = deReal.nameSuffixHistory || [];
        deReal.nameSuffixHistory.push(conversionInfo.conversionNameExplain)
        return ResponseData.success(result);
    }

    /**
     * 清单修改 后加的注释 目前看 修改工程量走这里
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async update(args) {
        let {constructId, singleId, unitWorkId, pointLineId, params, column, value} = args;
        let res;
        if (params) {
            for (const key in params) {
                res = await this.itemBillProjectOptionService.updateByList(constructId, singleId, unitWorkId, pointLineId, {column:key, value:params[key]});
            }
        } else {
            res = await this.itemBillProjectOptionService.updateByList(constructId, singleId, unitWorkId, pointLineId, {
                column, value
            });
        }
        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");
        return ResponseData.success(res);
    }

    /**
     * 特征及项目编辑
     */
    updateQdFeature(args) {
        let {constructId, singleId, unitId, pointLine, updateStr} = args;
        this.itemBillProjectOptionService.updateQdFeature(constructId, singleId, unitId, pointLine, updateStr);
    }

    /**
     * 删除
     * @param args
     * @returns {ResponseData}
     */
    async remove(args) {
        let {constructId, singleId, unitWorkId, pointLine, isBlock} = args;
        try {
            const result = await this.itemBillProjectOptionService.removeLine(constructId, singleId, unitWorkId, pointLine, isBlock);
            await this.service.management.sycnTrigger("unitDeChange");
            await this.service.management.trigger("itemChange");
            return ResponseData.success(result);
        } catch (e) {
            return ResponseData.fail(e.message);
        }

    }

    /**
     * 查询单价构成
     */
    getFeeBuild(args) {
        let {constructId, singleId, unitWorkId, pointLineId} = args;
        let res = this.unitPriceService.getPriceBuild(constructId, singleId, unitWorkId, pointLineId);
        return ResponseData.success(res);
    }


    /**
     * 临时删除数据
     * @param args
     * @return {ResponseData}
     */
    async updateDelTempStatusColl(args) {
        await this.service.itemBillProjectOptionService.updateDelTempStatus(args);
        // await this.service.management.sycnTrigger("unitDeChange");
        // await this.service.management.trigger("itemChange");
        return ResponseData.success(true);
    }



    /**
     * 根据类别批量删除
     * @param args
     * @return {ResponseData}
     */
   async batchDelByTypeOfColl(args) {
        let res = await this.service.itemBillProjectOptionService.batchDelByTypeOf(args);
        // await this.service.management.sycnTrigger("unitDeChange");
        // await this.service.management.trigger("itemChange");
        return ResponseData.success(res);
    }


    /**
     * 分部分项数据行颜色设置  批量设置
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async updateDataColorColl(args) {
        let {constructId, singleId, unitWorkId, idList, column,value} = args;
        for(const pointLineId of idList){
            let upDateInfo={
                column,value
            }
            let res = await this.itemBillProjectOptionService.updateByList(constructId, singleId, unitWorkId, pointLineId, upDateInfo);
        }
        await this.itemBillProjectOptionService.updateUnitColorList(constructId, singleId, unitWorkId,"fbfx");
        return ResponseData.success(true);
    }


    /**
     * 查询要删除的定额数据中是否含有关联定额数据
     */
    async queryExistsGlDeColl(args){
        let {constructId, singleId, unitId, idList,type} = args;
        let b = await this.itemBillProjectOptionService.queryExistsGlDe(constructId, singleId, unitId,idList,type);
        return ResponseData.success(b);

    }

    /**
     * 批量删除子目-查询列表
     */
    async batchDelBySeachList(args){
        let {constructId, singleId, unitId, idList,type} = args;
        let result = await this.itemBillProjectOptionService.batchDelBySeachList(args);
        return ResponseData.success(result);

    }

    /**
     * 批量删除子目
     */
    async batchDelDeItem(args){
        let {deList,constructId} = args;
        try {
            let unitList = PricingFileFindUtils.getUnitList(constructId);
            for (let de of deList) {
                let {unitId} = de;
                let unit = unitList.find(k =>k.sequenceNbr == unitId);
                const result = await this.itemBillProjectOptionService.removeLine(constructId, unit.spId, unitId, de, false);
            }
            return ResponseData.success();
        } catch (e) {
            return ResponseData.fail(e.message);
        }

    }


}

ItemBillProjectController.toString = () => '[class ItemBillProjectController]';
module.exports = ItemBillProjectController;




