
const DePropertyTypeConstant = require("../../../electron/enum/DePropertyTypeConstant");
const TaxCalculationMethodEnum = require("../../../electron/enum/TaxCalculationMethodEnum");
const JieSuanRcjDifferenceEnum = require("../enum/JieSuanRcjDifferenceEnum");
const JieSuanConstantUtil = require("../enum/JieSuanConstantUtil");
const {ResponseData} = require("../../../common/ResponseData");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {RcjHandleFactory} = require("../rcj_handle/RcjHandleFactory");
const {RcjDataUtils} = require("../../../electron/rcj_handle/RcjDataUtils");
const {Service} = require("../../../core");
const {JieSuanRcjStageUtils} = require("../utils/JieSuanRcjStageUtils");
const BranchProjectLevelConstant = require("../../../electron/enum/BranchProjectLevelConstant");
const FindRcjStrategy = require("../../../electron/rcj_handle/find/findRcjStrategy");
const {JieSuanUpdateRcjStrategy} = require("../rcj_handle/update/jieSuanUpdateRcjStrategy");
const RcjLevelMarkConstant = require("../../../electron/enum/RcjLevelMarkConstant");
const {UnitRcjCacheUtil} = require("../../../electron/rcj_handle/cache/UnitRcjCacheUtil");
const ConstantUtil = require("../../../electron/enum/ConstantUtil");
const fs = require("fs");
const {ExcelOperateUtil} = require("../../../electron/utils/ExcelOperateUtil");
const {ExcelUtil} = require("../../../electron/utils/ExcelUtil");


/**
 * 人材机 process
 */
class JieSuanRcjProcess extends Service {

    constructor(ctx) {
        super(ctx);
        //“其他材料”、“其他材料费”、“其他机械费”、“其他机械“ 特殊编码
        this.specialRcjCode = ConstantUtil.SPECIAL_RCJ;
    }

    //1：人工费；2：材料费；3：机械费；4：设备费；5：主材费；6：商砼；7：砼；8：浆；9：商浆；10：配比
    getUnitRcjType(arg) {
        let {constructId, singleId, unitId, levelType} = arg;
        let treeListVO = {};
        let itemList = new Array();
        if (levelType === 1) {
            let unitList = PricingFileFindUtils.getUnitList(constructId);
            let rcj = {"0": "合同内所有人材机", type: 1};
            itemList.push(rcj);
            //查找合同内的单位是否有一个选择了分期  ifParticipateInAdjustment
            let tempUnitList = unitList.filter(k => k.originalFlag && JieSuanRcjStageUtils.isStage(k));
            //let list = unitList.filter(k => !k.originalFlag && k.isDifference);
            if (!ObjectUtil.isNotEmpty(tempUnitList)) {
                let unit = unitList.filter(k => k.originalFlag)[0];
                //调差信息
                let rcjDifference = unit.adjustMethodList;
                let rengong = {"1": "人工调差"}
                rengong.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.RENGONG.code);
                itemList.push(rengong);
                let cailiao = {"2": "材料调差"}
                cailiao.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.CAILIAO.code);
                itemList.push(cailiao);
                let zangujia = {"8": "暂估价材料调差"}
                zangujia.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.ZANGUJIA.code);
                itemList.push(zangujia);

                let jixie = {"3": "机械调差"}
                jixie.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.JIXIE.code);
                itemList.push(jixie);
            }
            itemList.push({"21": "合同外所有人材机", type: 2});
            let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
            if (ObjectUtils.isNotEmpty(projectObjById.isDifference) && projectObjById.isDifference == true) {
                itemList.push({"20": "价差"});
            }
        }


        if (levelType === 2) {
            let unitListBySingle = PricingFileFindUtils.getUnitListBySingle(constructId, singleId);

            let originalFlagUnit = unitListBySingle.find(k =>k.originalFlag == true);

            let isStageUnit = unitListBySingle.find(k =>JieSuanRcjStageUtils.isStage(k));

            if (ObjectUtils.isNotEmpty(originalFlagUnit)) {
                itemList.push({"0": "所有人材机", type: 1});
                //调差信息
                let rcjDifference = unitListBySingle[0].adjustMethodList;
                if (!isStageUnit) {
                    let rengong = {"1": "人工调差"}
                    rengong.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.RENGONG.code);
                    itemList.push(rengong);
                    let cailiao = {"2": "材料调差"}
                    cailiao.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.CAILIAO.code);
                    itemList.push(cailiao);

                    let zangujia = {"8": "暂估价材料调差"}
                    zangujia.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.ZANGUJIA.code);
                    itemList.push(zangujia);

                    let jixie = {"3": "机械调差"}
                    jixie.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.JIXIE.code);
                    itemList.push(jixie);
                }
            } else {
                itemList.push({"0": "所有人材机", type: 2});
                itemList.push({"1": "人工"});
                itemList.push({"2": "材料"});
                itemList.push({"3": "机械"});
                itemList.push({"4": "设备"});
                itemList.push({"5": "主材"});
                itemList.push({"6": "预拌混凝土"});
                //let list = unitListBySingle.filter(k => !k.originalFlag && k.isDifference);
                let obj = PricingFileFindUtils.getSingleProject(constructId, singleId)
                if (ObjectUtils.isNotEmpty(obj.isDifference) && obj.isDifference == true) {
                    itemList.push({"20": "价差"});
                }
            }


        }


        if (levelType === 3) {
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            if (unit.originalFlag) {
                itemList.push({"0": "所有人材机", type: 1});
                let stage = JieSuanRcjStageUtils.isStage(unit);
                //调差信息
                let rcjDifference = unit.adjustMethodList;
                if (!ObjectUtils.isEmpty(rcjDifference)) {
                    let rengong = {"1": "人工调差"}
                    let cailiao = {"2": "材料调差"}
                    let zangujia = {"8": "暂估价材料调差"}
                    let jixie = {"3": "机械调差"}
                    rengong.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.RENGONG.code);
                    cailiao.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.CAILIAO.code);
                    zangujia.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.ZANGUJIA.code);
                    jixie.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.JIXIE.code);
                    //batchType
                    if (!stage){
                        rengong.info.frequencyList =[];
                        cailiao.info.frequencyList =[];
                        zangujia.info.frequencyList =[];
                        jixie.info.frequencyList =[];
                    }
                    itemList.push(rengong);
                    itemList.push(cailiao);
                    itemList.push(zangujia);
                    itemList.push(jixie);
                }
            } else {
                itemList.push({"0": "所有人材机", type: 2});
                itemList.push({"1": "人工"});
                itemList.push({"2": "材料"});
                itemList.push({"3": "机械"});
                itemList.push({"4": "设备"});
                itemList.push({"5": "主材"});
                itemList.push({"6": "预拌混凝土"});
                if (ObjectUtils.isNotEmpty(unit.isDifference) && unit.isDifference == true) {
                    itemList.push({"20": "价差"});
                }
            }


        }
        treeListVO.itemList = itemList;
        return treeListVO;
    }


    /**
     * 单位下人材机汇总查询
     */
    async unitRcjQuery(args) {

        let {constructId, singleId, unitId, kind, num} = args;
        args.levelType = 3;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let rcjAdjustMethodList = JieSuanRcjStageUtils.getRcjAdjustMethodList(unit);
        let rcjDifferenceInfos = rcjAdjustMethodList.find(k =>k.kind == kind);
        if (ObjectUtil.isNotEmpty(rcjDifferenceInfos)){
            args.adjustMethod = rcjDifferenceInfos.adjustMethod;
        }else {
            args.adjustMethod = 1;
        }
        let instance = RcjHandleFactory.getInstance(constructId, singleId, unitId,args);
        return instance.cupmute();


        //获取当前单位
        //let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        // //获取费用定额的ID
        // let costDeIdList = this._getCostDeIdList(unit);
        //
        // //材料父级
        // let copyRcjs = ObjectUtil.cloneDeep(unit.constructProjectRcjs);
        // if (ObjectUtil.isEmpty(copyRcjs)) {
        //     return copyRcjs;
        // }
        //
        // //材料子级
        // let childDetails = ObjectUtil.cloneDeep(unit.rcjDetailList);
        // childDetails.forEach(k => {
        //     //费用定额人材机以及二次解析的父级材料
        //     if (k.ifProvisionalEstimate ==1) {
        //         //置灰标识
        //         k.isGray = true;
        //     }
        // });
        // copyRcjs.forEach(k => {
        //     //费用定额人材机以及二次解析的父级材料
        //     //if (costDeIdList.includes(k.deId) || (k.markSum === 1 && (k.levelMark === 1 || k.levelMark === 2)) || k.ifProvisionalEstimate ==1) {
        //     if (costDeIdList.includes(k.deId) || (k.markSum === 1 && (k.levelMark == 1 || k.levelMark == 2)) ) {
        //         //置灰标识
        //         k.isGray = true;
        //     }
        //     ;
        //     //是否二次解析（如果二次解析需要将子级材料也填充进来）
        //     if (k.markSum === 1 && ObjectUtil.isNotEmpty(childDetails)) {
        //
        //
        //         let childRcjs = childDetails.filter(i => i.rcjId === k.sequenceNbr);
        //         copyRcjs.push(...childRcjs);
        //     }
        // });
        //
        // //根据类型筛选
        // if (kind !== 0) {
        //     //   【材料调差】：所有类型为“材料”、“主材”、“设备”、“商砼”、“浆”、“商浆”、“配比”且是否调差为"是"、是否暂估为"否"且是否甲供为"否"的所有人材机数据行
        //     if (JieSuanRcjDifferenceEnum.CAILIAO.code == kind) {
        //         //人材机类型
        //         let rcjType = [2, 4, 5, 6, 8, 9, 10];
        //         copyRcjs = copyRcjs.filter(i => rcjType.includes(i.kind));
        //     } else if (JieSuanRcjDifferenceEnum.ZANGUJIA.code == kind) {
        //         // 【暂估价调差】所有是否暂估为"是"的人材机数据行 （包含暂估甲供材）；
        //         copyRcjs = copyRcjs.filter(i => i.ifProvisionalEstimate == 1 || i.ifDonorMaterial ==1);
        //     } else if (20 == kind) {
        //         //   【价差】
        //         copyRcjs = copyRcjs.filter(i => i.isDifference);
        //     } else {
        //         copyRcjs = copyRcjs.filter(i => i.kind == kind);
        //     }
        //
        //     if (unit.originalFlag) {
        //         if (JieSuanRcjDifferenceEnum.CAILIAO.code == kind) {
        //             //人材机类型
        //             copyRcjs = copyRcjs.filter(i => i.isDifference && i.ifProvisionalEstimate != 1 && i.ifDonorMaterial != 1);
        //         }
        //         //   【人工调差】：所有类型 为“人工”且是否调差为"是"、是否暂估为"否"、是否甲供为"否"的所有人材机数据行
        //         if (JieSuanRcjDifferenceEnum.RENGONG.code == kind) {
        //             //copyRcjs = copyRcjs.filter(i => i.isDifference && i.ifProvisionalEstimate != 1 && i.ifDonorMaterial != 1);
        //             copyRcjs = copyRcjs.filter(i=>!costDeIdList.includes(i.deId));
        //         }
        //         //    【机械调差】所有类型为“机械”且是否调差为"是"、是否暂估为"否"的所有人材机数据行；
        //         if (JieSuanRcjDifferenceEnum.JIXIE.code == kind) {
        //             copyRcjs = copyRcjs.filter(i => i.isDifference && i.ifProvisionalEstimate != 1);
        //         }
        //     }
        // }
        //
        // //根据 材料编码、类型、名称、规格型号、单位、合同预算价去重
        // //分组
        // let rcjGroup = this._getRcjGroup(copyRcjs);
        //
        // let newRcjList = new Array();
        // //人材机数据封装
        // this._getRcjListDataHandler(newRcjList, rcjGroup, constructId, singleId, unitId, num);
        //
        // /**
        //  * 人材机排序规则：
        //  * 1. 整体【非置灰数据】排在【置灰数据】前面
        //  * 2.【 非置灰数据】内排序顺序：人工、材料、主材、机械
        //  * 3.【 置灰数据】内排序顺序：人工、材料、主材、机械、二次解析父级材料
        //  */
        // let sortRcjList = new Array();
        // //非置灰
        // let noGray = newRcjList.filter(i => !i.isGray).sort((a, b) => {
        //     if (a.kind === b.kind) {
        //         return a.materialCode.localeCompare(b.materialCode)
        //     }
        //     return a.kind - b.kind;
        // });
        //
        // //置灰
        // let ercijiexi = newRcjList.filter(i => i.isGray).filter(i => i.markSum === 1 && (i.levelMark === 1 || i.levelMark === 2));//二次解析
        // let feiercijiexi = newRcjList.filter(i => i.isGray).filter(i => !(i.markSum === 1 && (i.levelMark === 1 || i.levelMark === 2))).sort((a, b) => {
        //     return a.kind - b.kind;
        // });//非二次解析
        // sortRcjList.push(...noGray);
        // sortRcjList.push(...feiercijiexi);
        // sortRcjList.push(...ercijiexi);
        //
        //
        // if(!ObjectUtils.isEmpty(sortRcjList)){
        //     //暂估置灰
        //     for (let sortRcjListElement of sortRcjList) {
        //         if (sortRcjListElement.ifProvisionalEstimate ==1 || sortRcjListElement.ifDonorMaterial  ==1){
        //             sortRcjListElement.isDifference = true;
        //         }
        //
        //         if (sortRcjListElement.outputToken != 1){
        //             sortRcjListElement.outputToken =2;
        //         }
        //     }
        // }
        //
        // //过滤 临时删除
        // sortRcjList = sortRcjList.filter(i=>{
        //    if( !(!ObjectUtils.isEmpty(i.tempDeleteFlag) && i.tempDeleteFlag)){
        //        if (ObjectUtils.isEmpty(i.ifDonorMaterial)) {
        //            i.ifDonorMaterial = 0;
        //        }
        //        return true;
        //    }
        // });
        //
        // let parm ={};
        // parm.constructId =constructId;
        // parm.singleId    =singleId ;
        // parm.unitId      =unitId;
        // parm.rcjList      =sortRcjList;
        // parm.clType = kind;
        // parm.fqNum = num;

        //统一处理人材机 调差
        await this.service.jieSuanProject.jieSuanRcjStageService.jieSuanRcjListTongYiTiaoCha(parm);

        return sortRcjList;
    }

    /**
     * 人材机根据几要素分组
     * @private
     */
    _getRcjGroup(rcjs) {
        //拼接相同材料
        for (let arrayElement of rcjs) {
            arrayElement.tempcol =this._indexJoint(3,arrayElement);
        }
        //分组
        return rcjs.reduce((accumulator, currentValue) => {
            // 将分组作为对象的 key，相同分组的项放入同一个数组
            (accumulator[currentValue.tempcol] = accumulator[currentValue.tempcol] || []).push(currentValue);
            return accumulator;
        }, {});
    }


    /**
     * 获取费用定额的ID
     * @private
     */
    _getCostDeIdList(unit) {
        //获取当前单位的费用定额ID
        let costDeIdList = [];
        let costDeList = [DePropertyTypeConstant.AWF_DE, DePropertyTypeConstant.ZJCS_DE, DePropertyTypeConstant.CG_DE, DePropertyTypeConstant.AZ_DE];
        if (ObjectUtil.isNotEmpty(unit.itemBillProjects)) {
            let itemBillProjectsIds = unit.itemBillProjects.filter(k => costDeList.includes(k.isCostDe)).map(k => k.sequenceNbr);
            costDeIdList.push(...itemBillProjectsIds);
        }
        if (ObjectUtil.isNotEmpty(unit.measureProjectTables)) {
            let measureProjectTablesIds = unit.measureProjectTables.filter(k => costDeList.includes(k.isCostDe)).map(k => k.sequenceNbr);
            costDeIdList.push(...measureProjectTablesIds);
        }
        return costDeIdList;
    }


    /**
     * 工程项目级别的人材机列表数据
     */
    async projectRcjList(args) {

        let {constructId, singleId, unitId, kind, num,type,adjustMethod} = args;
        //let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        if (ObjectUtil.isNotEmpty(adjustMethod)){
            args.adjustMethod = adjustMethod;
        }else {
            args.adjustMethod = 1;
        }
        let instance = RcjHandleFactory.getInstance(constructId, singleId, unitId,args);
        return instance.cupmute();
    }

    /**
     * 子父集材料条件合并
     * @param tempRcjArray
     * @param unitList
     */
    _rcjMerge(tempRcjArray, unitList) {
        //所有单位的材料集合
        unitList.forEach(k => {
            //获取费用定额的ID
            let costDeIdList = this._getCostDeIdList(k);

            //材料父级
            let copyRcjs = k.constructProjectRcjs;
            if (ObjectUtil.isNotEmpty(copyRcjs)) {
                //过滤临时删除
                copyRcjs = copyRcjs.filter(i=>!(!ObjectUtils.isEmpty(i.tempDeleteFlag) && i.tempDeleteFlag));
                for (let i = 0; i < copyRcjs.length; i++) {
                    let copyRcj = copyRcjs[i];
                    //二次解析的父级材料
                    if (copyRcj.markSum == 1 && (copyRcj.levelMark == 1 || copyRcj.levelMark == 2)) {
                        //材料子级
                        let childRcjs = k.rcjDetailList.filter(i => i.rcjId === copyRcj.sequenceNbr && !(!ObjectUtils.isEmpty(i.tempDeleteFlag) && i.tempDeleteFlag));
                        //置灰标识
                        copyRcj.isGray = true;


                        tempRcjArray.push(...childRcjs);
                        //continue;
                    }
                    //置灰
                    /*if (copyRcj.markSum != 1) {
                        copyRcj.isGray = true;
                    }*/
                    tempRcjArray.push(copyRcj);
                }

                for (let tempRcjArrayElement of tempRcjArray) {
                    if (costDeIdList.includes(tempRcjArrayElement.deId) ){
                        tempRcjArrayElement.isGray = true;
                    }
                }
            }
        });

    }

    /**
     * 结算 获取 主要材料设备和工程设备一览表数据
     */
    getJieSuanRcjBBzyclgc(args){
        let {constructId, singleId, unitId, methodType} = args;
        args.kind =2;
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        if (ObjectUtil.isEmpty(projectObjById)){
            return null;
        }

        let clType = 2
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        if (unit.rcjDifference){
            let rcjDifference = unit.rcjDifference;
            for (const obj of rcjDifference) {
                if (obj.kind == clType) {
                    if (obj.rcjDifferenceType !=methodType){
                        return null;
                    }
                }
            }
        }
        return this.unitRcjQuery(args);

    }


    /**+
     * 高亮处理
     * @param tempRcjArray
     */
    highlightHandle(tempRcjArray) {
        for (const tempRcj of tempRcjArray  ){
            if (ObjectUtil.isNotEmpty(tempRcj.marketPrice)&& ObjectUtil.isNotEmpty(tempRcj.jieSuanPrice) && parseFloat(tempRcj.jieSuanPrice) !== tempRcj.marketPrice) {
                tempRcj.highlight = true;
            } else {
                tempRcj.highlight = false;
            }
        }

    }


    /**
     * 结算 计算单位工程 分期人材机合计数量
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<null|Map<any, any>>}
     */
     jieSuanFenQiRcjTotalNumber(constructId, singleId, unitId){
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);

        if (ObjectUtils.isEmpty(unit)){
            return null;
        }

        //如果不分期 则不需要计算
        if (ObjectUtils.isEmpty(unit.rcjStageSet) && unit.rcjStageSet.isStage ==false){
            return null;
        }

        //分部分项
        let itemBillProjects = unit.itemBillProjects.getAllNodes();
        //措施项目
        let measureProjectTables = unit.measureProjectTables.getAllNodes();

        //一级人材机
        let constructProjectRcjs = unit.constructProjectRcjs;
        //二级人材机
        let rcjDetailList = unit.rcjDetailList;

        let map = new Map();

        if (!ObjectUtils.isEmpty(itemBillProjects)){
            //循环 清单
            for (let itemBillProject of itemBillProjects) {
                //清单
                if (itemBillProject.kind =="03"){

                    //人材机占比比例记录
                    let blMap = new Map;
                    if (!ObjectUtil.isEmpty(itemBillProject.stageQuantitiesList)){
                        //计算清单分期占比

                        let stageQuantitiesList = itemBillProject.stageQuantitiesList;
                        //按比例分期
                        if (itemBillProject.stageType ==1){
                            for (let stageQuan of stageQuantitiesList) {
                                //比例
                                let bl  = NumberUtil.numberScale6(NumberUtil.divide(stageQuan.stageQuantity,itemBillProject.quantity));
                                blMap.set(stageQuan.stage,bl);
                            }
                        }else {
                            //按工程量分期
                            for (let stageQuan of stageQuantitiesList) {
                                //比例
                                let bl  = NumberUtil.numberScale6(NumberUtil.divide(stageQuan.stageQuantity,itemBillProject.quantity));
                                blMap.set(stageQuan.stage,bl);
                            }
                        }


                        let filter = itemBillProjects.filter(i=>i.parentId ==itemBillProject.sequenceNbr);

                        let array = new Array();
                        //清单id
                        array.push(itemBillProject.sequenceNbr);
                        for (let filterElement of filter) {
                            //定额id
                            array.push(filterElement.sequenceNbr);
                        }

                        let constructProjectRcjs1 = constructProjectRcjs.filter(i=>array.includes(i.deId));
                        let rcjDetailList1 = rcjDetailList.filter(i=>array.includes(i.deId));

                        //一级人材机
                        for (let constructProjectRcj of constructProjectRcjs1) {
                            let materialCode = constructProjectRcj.materialCode;
                            let totalNumber = constructProjectRcj.totalNumber;
                            for (let stageQuan of stageQuantitiesList) {
                                //获取比例
                                let v = blMap.get(stageQuan.stage);

                                //计算工程量
                                let number = NumberUtil.numberScale6(NumberUtil.multiply(totalNumber,v));

                                let rcjZongTotalNumber = map.get(materialCode+stageQuan.stage);

                                if (ObjectUtil.isEmpty(rcjZongTotalNumber)){
                                    map.set(materialCode+stageQuan.stage,number);
                                }else {
                                    map.set(materialCode+stageQuan.stage,NumberUtil.add(rcjZongTotalNumber,number));
                                }
                            }
                        }

                        //二级
                        for (let constructProjectRcj of rcjDetailList1) {
                            let materialCode = constructProjectRcj.materialCode;
                            let totalNumber = constructProjectRcj.totalNumber;
                            for (let stageQuan of stageQuantitiesList) {
                                //获取比例
                                let v = blMap.get(stageQuan.stage);

                                //计算工程量
                                let number = NumberUtil.numberScale6(NumberUtil.multiply(totalNumber,v));

                                let rcjZongTotalNumber = map.get(materialCode+stageQuan.stage);

                                if (ObjectUtil.isEmpty(rcjZongTotalNumber)){
                                    map.set(materialCode+stageQuan.stage,number);
                                }else {
                                    map.set(materialCode+stageQuan.stage,NumberUtil.add(rcjZongTotalNumber,number));
                                }
                            }
                        }
                    }
                }
            }
        }

        //措施项目
        if (!ObjectUtils.isEmpty(measureProjectTables)){
            //循环 清单
            for (let itemBillProject of measureProjectTables) {
                //清单
                if (itemBillProject.kind =="03"){

                    //人材机占比比例记录
                    let blMap = new Map;
                    if (!ObjectUtil.isEmpty(itemBillProject.stageQuantitiesList)){
                        //计算清单分期占比

                        let stageQuantitiesList = itemBillProject.stageQuantitiesList;
                        //按比例分期
                        if (itemBillProject.stageType ==1){
                            for (let stageQuan of stageQuantitiesList) {
                                //比例
                                let bl  = NumberUtil.numberScale6(NumberUtil.divide(stageQuan.stageQuantity,itemBillProject.quantity));
                                blMap.set(stageQuan.stage,bl);
                            }
                        }else {
                            //按工程量分期
                            for (let stageQuan of stageQuantitiesList) {
                                //比例
                                let bl  = NumberUtil.numberScale6(NumberUtil.divide(stageQuan.stageRatio,100));
                                blMap.set(stageQuan.stage,bl);
                            }
                        }


                        let filter = itemBillProjects.filter(i=>i.parentId ==itemBillProject.sequenceNbr);

                        let array = new Array();
                        //清单id
                        array.push(itemBillProject.sequenceNbr);
                        for (let filterElement of filter) {
                            //定额id
                            array.push(filterElement.sequenceNbr);
                        }

                        let constructProjectRcjs1 = constructProjectRcjs.filter(i=>array.includes(i.deId));
                        let rcjDetailList1 = rcjDetailList.filter(i=>array.includes(i.deId));

                        //一级人材机
                        for (let constructProjectRcj of constructProjectRcjs1) {
                            let materialCode = constructProjectRcj.materialCode;
                            let totalNumber = constructProjectRcj.totalNumber;
                            for (let stageQuan of stageQuantitiesList) {
                                //获取比例
                                let v = blMap.get(stageQuan.stage);

                                //计算工程量
                                let number = NumberUtil.numberScale6(NumberUtil.multiply(totalNumber,v));

                                let rcjZongTotalNumber = map.get(materialCode+stageQuan.stage);

                                if (ObjectUtil.isEmpty(rcjZongTotalNumber)){
                                    map.set(materialCode+stageQuan.stage,number);
                                }else {
                                    map.set(materialCode+stageQuan.stage,NumberUtil.add(rcjZongTotalNumber,number));
                                }
                            }
                        }

                        //二级
                        for (let constructProjectRcj of rcjDetailList1) {
                            let materialCode = constructProjectRcj.materialCode;
                            let totalNumber = constructProjectRcj.totalNumber;
                            for (let stageQuan of stageQuantitiesList) {
                                //获取比例
                                let v = blMap.get(stageQuan.stage);

                                //计算工程量
                                let number = NumberUtil.numberScale6(NumberUtil.multiply(totalNumber,v));

                                let rcjZongTotalNumber = map.get(materialCode+stageQuan.stage);

                                if (ObjectUtil.isEmpty(rcjZongTotalNumber)){
                                    map.set(materialCode+stageQuan.stage,number);
                                }else {
                                    map.set(materialCode+stageQuan.stage,NumberUtil.add(rcjZongTotalNumber,number));
                                }
                            }
                        }
                    }
                }
            }
        }

        return map;
    }




    /**
     * 人材机汇总选择
     * @param args
     */
    async unitRcjCollectSelect(args) {
        let {constructId, singleId, unitId, levelType, kind, name, type} = args;

        let param = {};
        param.constructId = constructId;
        param.singleId = singleId;
        param.unitId = unitId;
        param.kind = 0;
        param.type = 1;
        param.levelType = levelType;
        //条件处理
        if (kind.includes(2)) {
            kind.push(...[4, 6, 8, 9, 10])
        }

        let unitRcjQuery = [];
        if (levelType == 3) {
            //获取单位级别人材机数据
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            unitRcjQuery = await this.unitRcjQuery(param);
            let costDeIdList = JieSuanRcjStageUtils.getCostDeIdList([unit]);
            unitRcjQuery = unitRcjQuery.filter(k => !costDeIdList.includes(k.deId) || k.isGray != true);
        }
        if (levelType == 2) {
            unitRcjQuery = await this.projectRcjList(param);
        }
        if (levelType == 1) {
            //获取项目级别人材机数据
            unitRcjQuery = await this.projectRcjList(param);
        }


        //根据类型筛选
        unitRcjQuery = unitRcjQuery.filter(k => kind.includes(k.kind) && k.ifProvisionalEstimate != 1 && k.ifDonorMaterial != 1 && !k.isGray);
        //根据名称筛选
        if (ObjectUtil.isNotEmpty(name)) {
            unitRcjQuery = unitRcjQuery.filter(k => k.materialName.includes(name) || k.materialCode.includes(name))
        }
        return ResponseData.success(unitRcjQuery);

    }


    /**
     * 自动过滤调差材料
     */
    async filterDifferenceRcj(args) {
        let {constructId, singleId, unitId, type, value,levelType} = args;

        //获取当前单位
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        if (ObjectUtils.isEmpty(unit)){
            return ;
        }

        let param = {};
        param.constructId = constructId;
        param.singleId = singleId;
        param.unitId = unitId;
        param.kind = 0;
        param.levelType = levelType;
        param.type = 1;

        let rcjQuery = [];
        if (levelType == 1 || levelType == 2) {
            rcjQuery = await this.projectRcjList(param)
        }
        if (levelType == 3) {
            rcjQuery = await this.unitRcjQuery(param);
        }


        //获取费用定额的ID
        let unitList = [];
        if (levelType == 1){
            unitList = PricingFileFindUtils.getUnitList(constructId);
        }
        if (levelType == 2){
            unitList = PricingFileFindUtils.getUnitListBySingle(constructId,singleId);
        }
        if (levelType == 3){
            let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
            unitList.push(unit);
        }
        let costDeIdList = JieSuanRcjStageUtils.getCostDeIdList(unitList);



        //过滤掉 暂估 甲供 费用定额材料
        rcjQuery = rcjQuery.filter(i => i.ifProvisionalEstimate!=1 && (i.ifDonorMaterial == 0 || ObjectUtils.isEmpty(i.ifDonorMaterial)));
        rcjQuery = rcjQuery.filter(i => !(i.markSum == 1 && (i.levelMark == 1 || i.levelMark == 2)));

        rcjQuery = rcjQuery.filter(i => !costDeIdList.includes(i.deId));
        rcjQuery = rcjQuery.filter(i => [2,4,5, 6,7,8, 9, 10].includes(i.kind));


        let isDifferenceTrue =[];

        let noZcSheBeiFalse = [];

        if (type == 1) {
            //合同计价文件中主要材料、设备 设置调差
            isDifferenceTrue = rcjQuery.filter(k => (k.kind == 4 || k.kind == 5));

            //非主材设备 设置不调差
            noZcSheBeiFalse = rcjQuery.filter(i => [2, 6,7,8, 9, 10].includes(i.kind));


        }
        if (type == 2) {
            //过滤掉 暂估 甲供 费用定额材料
            // unitRcjQuery = unitRcjQuery.filter(i => i.ifProvisionalEstimate!=1 && (i.ifDonorMaterial == 0 || ObjectUtils.isEmpty(i.ifDonorMaterial)));
            // unitRcjQuery = unitRcjQuery.filter(i => i.markSum == 0 || ObjectUtils.isEmpty(i.markSum));
            // unitRcjQuery = unitRcjQuery.filter(i => !costDeIdList.includes(i.deId));
            // unitRcjQuery = unitRcjQuery.filter(i => [2,4,5, 6,7,8, 9, 10].includes(i.kind));

            //根据材料在单位工程内的合同市场价合计的数值进行排序

            if (value>rcjQuery.length){
                isDifferenceTrue = rcjQuery;
            }else {
                rcjQuery.sort((a, b) => b.total - a.total);

                //
                isDifferenceTrue = rcjQuery.slice(0, Number(value));

                let idList = [];
                isDifferenceTrue.forEach(i=>idList.push(i.sequenceNbr));

                noZcSheBeiFalse = rcjQuery.filter(i=>!idList.includes(i.sequenceNbr));

            }

        }
        if (type == 3) {
            //过滤掉 暂估 甲供 费用定额材料
            //unitRcjQuery = unitRcjQuery.filter(i=>!(i.ifProvisionalEstimate==1 || i.ifDonorMaterial == 1 || costDeIdList.includes(i.deId))  && [2, 6,7,8, 9, 10].includes(i.kind));
            //根据材料在单位工程内的合同市场价合计的数值占比进行排序
            rcjQuery.sort((a, b) => b.total - a.total);
            let sum = rcjQuery.reduce((accumulator, currentValue) => accumulator + Number(currentValue.total), 0);
            let valueSum = NumberUtil.multiply(sum, NumberUtil.divide100(Number(value)));
            let tempArray = new Array();
            let temp = 0;
            for (let i = 0; i < rcjQuery.length; i++) {
                temp = temp + Number(rcjQuery[i].total);
                tempArray.push(rcjQuery[i]);
                if (temp >= valueSum) {
                    tempArray.push(rcjQuery[i]);
                    break;
                }
            }
            //unitRcjQuery = tempArray;
            isDifferenceTrue = tempArray;
            let idList = [];
            isDifferenceTrue.forEach(i=>idList.push(i.sequenceNbr));
            noZcSheBeiFalse = rcjQuery.filter(i=>!idList.includes(i.sequenceNbr));

        }

        let rcjcs ={
            type:1,
            kind:0,
            levelType:levelType,
            constructId:constructId,
            singleId:singleId,
            unitId:unitId
        };

        if (!ObjectUtils.isEmpty(isDifferenceTrue)){
            for (let item of isDifferenceTrue) {
                let constructProjectRcj ={
                    isDifference:true
                }
                let jieSuanUpdateRcjStrategy = new JieSuanUpdateRcjStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId),arg:rcjcs});
                await jieSuanUpdateRcjStrategy.execute({pointLine:item,upDateInfo:constructProjectRcj});
            }
        }
        if (!ObjectUtils.isEmpty(noZcSheBeiFalse)){
            for (let item of noZcSheBeiFalse) {
                let constructProjectRcj ={
                    isDifference:false
                }
                let jieSuanUpdateRcjStrategy = new JieSuanUpdateRcjStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId),arg:rcjcs});
                await jieSuanUpdateRcjStrategy.execute({pointLine:item,upDateInfo:constructProjectRcj});
            }
        }
        return ResponseData.success(true);
    }


    /**
     * 元素拼接
     */
    _indexJoint(levelType,k){
        let tempcol = "";
        if (levelType == 3) {
            tempcol = (ObjectUtils.isNotEmpty(k.materialCode) ? k.materialCode : "")+
                (ObjectUtils.isNotEmpty(k.kind) ? k.kind : "")+
                (ObjectUtils.isNotEmpty(k.materialName) ? k.materialName : "")+
                (ObjectUtils.isNotEmpty(k.specification) ? k.specification : "")+
                (ObjectUtils.isNotEmpty(k.unit) ? k.unit : "")+
                (ObjectUtils.isNotEmpty(k.dePrice) ? k.dePrice : "");
        }
        if (levelType == 1) {
            tempcol = (ObjectUtils.isNotEmpty(k.materialCode) ? k.materialCode : "")+
                (ObjectUtils.isNotEmpty(k.kind) ? k.kind : "")+
                (ObjectUtils.isNotEmpty(k.materialName) ? k.materialName : "")+
                (ObjectUtils.isNotEmpty(k.specification) ? k.specification : "")+
                (ObjectUtils.isNotEmpty(k.unit) ? k.unit : "")+
                (ObjectUtils.isNotEmpty(k.dePrice) ? k.dePrice : "")+
                (ObjectUtils.isNotEmpty(k.marketPrice) ? k.marketPrice : "")+
                (ObjectUtils.isNotEmpty(k.ifDonorMaterial) ? k.ifDonorMaterial : "")+
                (ObjectUtils.isNotEmpty(k.jieSuanAdminRate) ? k.jieSuanAdminRate : "")+
                (ObjectUtils.isNotEmpty(k.isDifference) ? k.isDifference : "")+
                (ObjectUtils.isNotEmpty(k.markSum) ? k.markSum : "")+
                (ObjectUtils.isNotEmpty(k.ifProvisionalEstimate) ? k.ifProvisionalEstimate : "");
        }
        return tempcol;

    }


    /**
     * 获取两个数字之间的所有数字
     * @param num1
     * @param num2
     * @return {[]}
     */
    findNumbersBetween(num1, num2) {
        let start = Math.min(num1, num2);
        let end = Math.max(num1, num2);
        let numbers = [];
        for (let i = start; i <= end; i++) {
            numbers.push(i);
        }
        return numbers;
    }


    /**
     * 人材机汇总选择后的确认
     */
    unitRcjCollectSelectNotarize(args) {
        let {constructId, singleId, unitId, levelType, selectList} = args;
        //六要素集合
        for (const item of selectList) {
            if (levelType == 1) {
                let unitList = PricingFileFindUtils.getUnitList(constructId);
                unitList.forEach(k => {
                    this._onlyValueRcjUpdate(k.constructProjectRcjs, k.rcjDetailList, levelType, item,k);
                })
            }
            if (levelType == 2) {
                let unitList = PricingFileFindUtils.getUnitListBySingle(constructId,singleId);
                unitList.forEach(k => {
                    this._onlyValueRcjUpdate(k.constructProjectRcjs, k.rcjDetailList, levelType, item,k);
                })
            }
            if (levelType == 3) {
                let rcjList = PricingFileFindUtils.getRcjList(constructId, singleId, unitId);
                let rcjDetailList = PricingFileFindUtils.getRcjDetailList(constructId, singleId, unitId);
                let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
                this._onlyValueRcjUpdate(rcjList, rcjDetailList, levelType, item,unit);
            }
        }
        return ResponseData.success(true);
    }


    /**
     * 唯一要素人材机修改
     */
    _onlyValueRcjUpdate(rcjList, rcjDetailList, levelType, item,unit) {
        let num = JieSuanRcjStageUtils.periods(unit);
        if (ObjectUtil.isNotEmpty(rcjList)) {
            rcjList.forEach(k => {
                if (JieSuanRcjStageUtils.indexJoint(levelType,k,null,1) ==item.tempcol) {
                    k.isDifference = item.type;
                    if (JieSuanRcjStageUtils.isStage(unit)){
                        if (k.isDifference){
                            let rcjAdjustMethod = JieSuanRcjStageUtils.getRcjAdjustMethodByKind(unit,k.kind);
                            num = rcjAdjustMethod.frequencyList.length;
                        }
                        JieSuanRcjStageUtils.stageSetRcjInit(k,num);
                    }
                }
            })
        }
        if (ObjectUtil.isNotEmpty(rcjDetailList)) {
            rcjDetailList.forEach(k => {
                if (JieSuanRcjStageUtils.indexJoint(levelType,k,null,1) == item.tempcol) {
                    k.isDifference = item.type;
                    if (JieSuanRcjStageUtils.isStage(unit)){
                        if (k.isDifference){
                            let rcjAdjustMethod = JieSuanRcjStageUtils.getRcjAdjustMethodByKind(unit,k.kind);
                            num = rcjAdjustMethod.frequencyList.length;
                        }
                        JieSuanRcjStageUtils.stageSetRcjInit(k,num);
                    }
                }
            })
        }
    }


    editableHtnNotOriginalPrice(constructId, singleId, unitId, rcjList){
        for(const rcj of rcjList){
            //合同内原始数据不允许修改
            if(rcj.originalFlag){
                rcj.ifChangePrice=false;
            }else {
                //获取当前单位的所有人材机
                let rcjList = PricingFileFindUtils.getRcjList(constructId, singleId, unitId);
                if(ObjectUtils.isEmpty(rcjList) || rcjList.length==1){
                    rcj.ifChangePrice=true;
                }else {
                    let constructProjectRcjs = rcjList.filter(r=>r.sequenceNbr!=rcj.sequenceNbr && r.originalFlag);
                    let ts = constructProjectRcjs.filter(sixrcj=>RcjDataUtils.rcjKeyByFiveElementsAddCode(sixrcj)==RcjDataUtils.rcjKeyByFiveElementsAddCode(rcj));
                    if(ObjectUtils.isEmpty(ts)){
                        rcj.ifChangePrice=true;
                    }else {
                        rcj.ifChangePrice=false;
                    }
                }
            }
        }
    }


    /**
     * 分部分项/措施项目 人材机明细列表
     * @param id 分部分项/措施项目id
     * @param branchType 1分部分项、2措施项目
     * @param constructId
     * @param singleId
     * @param unitId
     * @return {ConstructProjectRcj[]}
     */
    jieSuanQueryRcjDataByDeId(args) {
        let {branchType, id, constructId, singleId, unitId} = args;
        let allData = PricingFileFindUtils.getUnit(constructId, singleId, unitId)[branchType == 1 ? "itemBillProjects" : "measureProjectTables"];
        if (!allData.getNodeById(id)) {
            return [];
        }
        let item=allData.getNodeById(id);
        let findRcjStrategy = new FindRcjStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId)});
        args.pageType = branchType == 1?"fbfx":"csxm"
        let res = findRcjStrategy.execute(args);
        res.forEach(e => this.service.itemBillProjectOptionService.dataDeal(e,2));
        if(item.kind==BranchProjectLevelConstant.de){
            this.editableHtnNotOriginalPrice(constructId, singleId, unitId, res);
        }
        return ResponseData.success(res);

    }


    async getjieSuanConstructIdTree(args) {
        let {constructId,type} = args;
        const result = await this.service.loadPriceSetService.getConstructIdTree(args);
        let tempOriginalFlag = true;//合同内
        if (ObjectUtils.isEmpty(type) || type == 20 || type == 21){
            tempOriginalFlag = false;
        }
         let array = [];
         if(ObjectUtils.isNotEmpty(result)){
             let unitList= PricingFileFindUtils.getUnitListByConstructObj(PricingFileFindUtils.getProjectObjById(constructId));
             let singleList = PricingFileFindUtils.getSingleProjectList(constructId);
             result.forEach(k =>{
                 if (k.levelType == 1){
                     array.push(k);
                 }
                 if (k.levelType == 2){
                     let single = singleList.find(a =>a.sequenceNbr == k.id);

                     if (tempOriginalFlag){
                         if (ObjectUtils.isNotEmpty(single.originalFlag) || single.originalFlag==true){
                             array.push(k);
                         }
                     }else {
                         if (ObjectUtils.isEmpty(single.originalFlag) || single.originalFlag==false){
                             array.push(k);
                         }
                     }
                 }
                 if (k.levelType == 3){
                     let unit = unitList.find(a =>a.sequenceNbr == k.id);
                     if (tempOriginalFlag){
                         if (ObjectUtils.isNotEmpty(unit.originalFlag) || unit.originalFlag==true){
                             array.push(k);
                         }
                     }else {
                         if (ObjectUtils.isEmpty(unit.originalFlag) || unit.originalFlag==false){
                             array.push(k);
                         }
                     }

                 }
             })
         }
        return array;
    }

    /**
     * 修改市场价调整系数
     */
    async adjustmentCoefficient(args){
        let { constructId, singleId, unitId, coefficient, rcjList ,levelType} = args;
        //数据校验
        if (ObjectUtils.isEmpty(coefficient) ||  coefficient == 1 || ObjectUtils.isEmpty(rcjList)){
            return ;
        }
        for (let rcj of rcjList) {
            let jieSuanUpdateRcjStrategy = new JieSuanUpdateRcjStrategy({constructId,singleId:rcj.singleId,unitId:rcj.unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId),arg:args});

            jieSuanUpdateRcjStrategy.handleRcjList = jieSuanUpdateRcjStrategy.getHandleRcjList(rcj);
            for (let item of jieSuanUpdateRcjStrategy.handleRcjList) {
                //费用人材机 判断
                if (item.isFyrcj == 0){
                    continue;
                }
                //父级人材机判断 不解析
                if (item.markSum == 1 && (item.levelMark == item.SINK_PB
                    || rcj.levelMark == item.SINK_JX)){
                    continue;
                }
                //其他材料 判断
                if (this.specialRcjCode.includes(item.materialCode) && item.unit =="%"){
                    continue;
                }
                let constructProjectRcj ={
                    marketPrice:NumberUtil.multiply(coefficient,item.marketPrice)
                }
                let handler = jieSuanUpdateRcjStrategy.getHandler(constructProjectRcj);
                await handler.updateByRcj(item);
            }
        }
        return true;
    }


    /**
     * 来源分析
     * @param args
     * @return {Promise<void>}
     */
    async rcjFrom(args){
        let {constructId,singleId,unitId,rcj,levelType,kind,type} = args;
        if (ObjectUtils.isEmpty(rcj)){
            return [];
        }
        let jieSuanUpdateRcjStrategy = new JieSuanUpdateRcjStrategy({constructId:rcj.constructId,singleId:rcj.singleId,unitId:rcj.unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId),arg:args});
        let handleRcjList = jieSuanUpdateRcjStrategy.getHandleRcjList(rcj);
        if (type == 2){
            handleRcjList = handleRcjList.filter(k =>!k.originalFlag);
        }else {
            handleRcjList = handleRcjList.filter(k =>k.originalFlag);
        }
        let instance = RcjHandleFactory.getInstance(constructId, singleId, unitId,args);
        let result = instance.freeCupmute(handleRcjList);
        let cloneDeep = ObjectUtils.cloneDeep(result); 
        for (let item of cloneDeep) {
            let unit = PricingFileFindUtils.getUnit(rcj.constructId,rcj.singleId,rcj.unitId);
            let name =await this.getsingleProjectPathName(constructId,item.singleId);
            item.singleName = name;
            item.unitName = unit.upName;
        }
        return cloneDeep;
    }


    async getsingleProjectPathName(constructId,singleId){
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        if (ObjectUtils.isEmpty(projectObjById.singleProjects)){
            return "";
        }
        let find = projectObjById.singleProjects.find(i=>i.sequenceNbr == singleId);
        if (!ObjectUtils.isEmpty(find)){
            return find.projectName;
        }else {

            return await this.findIdPath(projectObjById.singleProjects,singleId);
        }
    }
    async findIdPath(singleProjects, singleId) {
        function recursiveSearch(node, path) {
            // 检查是否找到了目标 ID
            if (node.sequenceNbr == singleId) {
                return [...path, node.projectName];
            }

            // 遍历子节点
            for (const child of node.subSingleProjects || []) {
                const result = recursiveSearch(child, [...path, node.projectName]);
                if (result) {
                    return result;
                }
            }
            return null;
        }

        for (const topLevelNode of singleProjects) {
            const path = recursiveSearch(topLevelNode, []);
            if (path) {
                return path.join('/');
            }
        }

        // 如果没有找到目标 ID，则返回 null 或空字符串
        return '';
    }
    async getExcelRcjList(path){
        // 检查文件是否存在
        if (!fs.existsSync(path)) {
            //console.error('本地文件不存在');
            return null;
        }

        //let workbook = new ExcelJS.Workbook();
        let sheetNames;
        let options = {};
        try {
            //await workbook.xlsx.readFile(path);
            if (path.endsWith("xls")){
                options.version = 2003;
                let workbook =await ExcelOperateUtil.readToWorkBook(path,options)
                sheetNames = workbook.SheetNames[0];
            }else {
                options.version = 2007;
                let workbook =await ExcelOperateUtil.readToWorkBook(path,options)
                sheetNames = workbook._worksheets[1].name;
            }

        } catch (e) {
            console.log(e);
            return false;
        }

        //获取第一个 sheet 名称


        //解析
        let readMapList =await ExcelUtil.readSheetContent(path,sheetNames,options);

        //获得表头
        let readMapListElement = readMapList[0];

        let excelMap = new Map();

        //解析
        for (const [key, value] of readMapListElement) {
            //console.log(`Key: ${key}, Value: ${value}`);
            switch (value) {
                case '材料编码':{
                    excelMap.set("材料编码",key);
                    break;
                }
                case '编码':{
                    excelMap.set("编码",key);
                    break;
                }
                case '名称':{
                    excelMap.set("名称",key);
                    break;
                }
                case '规格型号':{
                    excelMap.set("规格型号",key);
                    break;
                }
                case '类型':{
                    excelMap.set("类型",key);
                    break;
                }
                case '单位':{
                    excelMap.set("单位",key);
                    break;
                }
                case '合同/确认不含税单价':{
                    excelMap.set("合同/确认不含税单价",key);
                    break;
                }
                case '合同/确认含税单价':{
                    excelMap.set("合同/确认含税单价",key);
                    break;
                }
                case '市场价':{
                    excelMap.set("市场价",key);
                    break;
                }
                case '合同/确认单价':{
                    excelMap.set("合同/确认单价",key);
                    break;
                }
            }
        }

        if (!((excelMap.has("材料编码")||excelMap.has("编码"))&&excelMap.has("名称")&&excelMap.has("规格型号")&&excelMap.has("类型")&&excelMap.has("单位"))){
            return false;
        }

        let excelRcjlist = new Array();
        for (let i = 1; i < readMapList.length; i++) {
            let rcj ={};
            rcj.materialCode = !ObjectUtils.isEmpty(readMapList[i].get(excelMap.get("材料编码")))?readMapList[i].get(excelMap.get("材料编码")):readMapList[i].get(excelMap.get("编码"));
            rcj.materialName = readMapList[i].get(excelMap.get("名称"));
            rcj.specification = readMapList[i].get(excelMap.get("规格型号"));
            rcj.kind = readMapList[i].get(excelMap.get("类型"));
            rcj.unit = readMapList[i].get(excelMap.get("单位"));

            rcj.marketPrice = readMapList[i].get(excelMap.get("市场价"));
            rcj.priceMarket = readMapList[i].get(excelMap.get("合同/确认不含税单价"));
            rcj.priceMarketTax = readMapList[i].get(excelMap.get("合同/确认含税单价"));
            rcj.marketPrice = readMapList[i].get(excelMap.get("合同/确认单价"));
            excelRcjlist.push(rcj);
        }

        return excelRcjlist;
    }

    /**
     * 人材机汇总 导入 Excel 修改人材机汇总 市场价
     * @param args
     * @returns {Promise<void>}
     */
    async useUnitExcelRcjPrice(args){
        let { constructId, singleId, unitId, path} = args;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        if (ObjectUtils.isEmpty(unit)){
            return null;
        }

        //excel 人材机
        let excelRcjList = await this.getExcelRcjList(path);
        if (excelRcjList == false){
            return null;
        }
        if (ObjectUtils.isEmpty(excelRcjList)){
            return null;
        }

        //单位工程人材机
        //let unitRcjList = this.unitConstructProjectRcjQuery(constructId, singleId, unitId, 0,null);
        let unitRcjList = PricingFileFindUtils.getRcjList(constructId, singleId, unitId)

        if (ObjectUtils.isEmpty(unitRcjList)){
            return null;
        }
        let is22De = PricingFileFindUtils.is22Unit(unit);

        //工程项目
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);

        //计税对象
        let projectTaxCalculation = projectObjById.projectTaxCalculation;

        //计税方式
        let taxCalculationMethod = projectTaxCalculation.taxCalculationMethod;
        let simple = false;
        //简易计税
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            simple = true;
        }

        let excelDbMap = new Map();
        excelRcjList.forEach(obj=>{
            let identifier = String(obj.materialName)
                .concat(!ObjectUtils.isEmpty(obj.materialCode) ? obj.materialCode : '')
                .concat(!ObjectUtils.isEmpty(obj.specification) ? obj.specification : '')
                .concat(!ObjectUtils.isEmpty(obj.kind) ? obj.kind : '')
                .concat(!ObjectUtils.isEmpty(obj.unit) ? obj.unit : '');
            if (!excelDbMap.has(identifier)) {
                excelDbMap.set(identifier, []);
            }
            excelDbMap.get(identifier).push(obj);
        });

        let updateRcjList = new Array();

        let updatePrice = "marketPrice";
        let updateVuale = null;



        let jieSuanUpdateRcjStrategy = new JieSuanUpdateRcjStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId),arg:args});

        for (let obj of unitRcjList) {
            let identifier = String(obj.materialName)
                .concat(!ObjectUtils.isEmpty(obj.materialCode) ? obj.materialCode : '')
                .concat(!ObjectUtils.isEmpty(obj.specification) ? obj.specification : '')
                .concat(!ObjectUtils.isEmpty(obj.type) ? obj.type : '')
                .concat(!ObjectUtils.isEmpty(obj.unit) ? obj.unit : '');

            if (excelDbMap.has(identifier) && excelDbMap.get(identifier).length > 0) {
                let excelRcj = excelDbMap.get(identifier)[0];

                if (is22De){
                    //22定额
                    if (simple){
                        if (!ObjectUtils.isEmpty(excelRcj.priceMarketTax) && excelRcj.priceMarketTax != obj.priceMarketTax){
                            updatePrice = "priceMarketTax";
                            updateVuale = excelRcj.priceMarketTax;
                            updateRcjList.push(obj);
                        }
                    }else {
                        if (!ObjectUtils.isEmpty(excelRcj.priceMarket) && excelRcj.priceMarket != obj.priceMarket){
                            updatePrice = "priceMarket";
                            updateVuale = excelRcj.priceMarket;
                            updateRcjList.push(obj);
                        }

                    }
                }else {
                    //12定额
                    if (!ObjectUtils.isEmpty(excelRcj.marketPrice) && excelRcj.marketPrice != obj.marketPrice){
                        updatePrice = "marketPrice";
                        updateVuale = excelRcj.marketPrice;
                        updateRcjList.push(obj);
                    }
                }

                if (ObjectUtils.isNotEmpty(updateVuale)){
                    let constructProjectRcj = {};
                    constructProjectRcj[updatePrice] = updateVuale;
                    await jieSuanUpdateRcjStrategy.execute({pointLine:obj,upDateInfo:constructProjectRcj});
                    updateVuale = null;
                    obj.sourcePrice = "线下导入数据";
                }
            }
        }
        return updateRcjList.length;

    }


    /**
     * 单项工程 人材机汇总 导入 Excel 修改人材机汇总 市场价
     * @param args
     * @returns {Promise<void>}
     */
    async useSingleExcelRcjPrice(args){
        let { constructId, singleId,path} = args;
        //工程项目
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        if (ObjectUtils.isEmpty(projectObjById) || ObjectUtils.isEmpty(singleId)){
            return null;
        }

        let projectDe22 = projectObjById.deStandardReleaseYear =="22"?true:false;
        //计税对象
        let projectTaxCalculation = projectObjById.projectTaxCalculation;

        //计税方式
        let taxCalculationMethod = projectTaxCalculation.taxCalculationMethod;
        let simple = false;
        //简易计税
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            simple = true;
        }
        let rcjList = [];
        let unitListBySingle = PricingFileFindUtils.getUnitListBySingle(constructId, singleId);

        unitListBySingle.forEach(k =>{
            let unit = PricingFileFindUtils.getUnit(constructId, singleId,k.sequenceNbr);
            rcjList.push(...unit.constructProjectRcjs);
        })
        PricingFileFindUtils
        if (ObjectUtils.isEmpty(rcjList)){
            return null;
        }

        //excel 人材机
        let excelRcjList = await this.getExcelRcjList(path);

        if (excelRcjList == false){
            return null;
        }

        if (ObjectUtils.isEmpty(excelRcjList)){
            return null;
        }

        let excelDbMap = new Map();
        excelRcjList.forEach(obj=>{
            let identifier = String(obj.materialName)
                .concat(!ObjectUtils.isEmpty(obj.materialCode) ? obj.materialCode : '')
                .concat(!ObjectUtils.isEmpty(obj.specification) ? obj.specification : '')
                .concat(!ObjectUtils.isEmpty(obj.kind) ? obj.kind : '')
                .concat(!ObjectUtils.isEmpty(obj.unit) ? obj.unit : '');
            if (!excelDbMap.has(identifier)) {
                excelDbMap.set(identifier, []);
            }
            excelDbMap.get(identifier).push(obj);
        });


        let updateRcjList = new Array();
        let updatePrice = "marketPrice";
        let updateVuale = null;

        for (let obj of rcjList) {
            let identifier = String(obj.materialName)
                .concat(!ObjectUtils.isEmpty(obj.materialCode) ? obj.materialCode : '')
                .concat(!ObjectUtils.isEmpty(obj.specification) ? obj.specification : '')
                .concat(!ObjectUtils.isEmpty(obj.type) ? obj.type : '')
                .concat(!ObjectUtils.isEmpty(obj.unit) ? obj.unit : '');

            if (excelDbMap.has(identifier) && excelDbMap.get(identifier).length > 0) {
                let excelRcj = excelDbMap.get(identifier)[0];

                //22人材机
                if (obj.deStandardReleaseYear == "22"){
                    //22定额
                    if (simple){
                        if (!ObjectUtils.isEmpty(excelRcj.priceMarketTax) && excelRcj.priceMarketTax != obj.priceMarketTax){
                            updateVuale = excelRcj.priceMarketTax;
                            updatePrice = "priceMarketTax";
                            updateRcjList.push(obj);
                        }
                    }else {
                        if (!ObjectUtils.isEmpty(excelRcj.priceMarket) && excelRcj.priceMarket != obj.priceMarket){
                            updateVuale = excelRcj.priceMarket;
                            updatePrice = "priceMarket";
                            updateRcjList.push(obj);
                        }

                    }
                }else {
                    //12人材机 放22项目
                    if (projectDe22){
                        //简易
                        if (simple){
                            if (!ObjectUtils.isEmpty(excelRcj.priceMarketTax) && excelRcj.priceMarketTax != obj.marketPrice){
                                updateVuale = excelRcj.priceMarketTax;
                                updatePrice = "marketPrice";
                                updateRcjList.push(obj);
                            }
                        }else {
                            //一般
                            if (!ObjectUtils.isEmpty(excelRcj.priceMarket) && excelRcj.priceMarket != obj.marketPrice){
                                updateVuale = excelRcj.priceMarket;
                                updatePrice = "marketPrice";
                                updateRcjList.push(obj);
                            }
                        }
                    }else {
                        //12人材机 放12项目
                        if (!ObjectUtils.isEmpty(excelRcj.marketPrice) && excelRcj.marketPrice != obj.marketPrice){
                            updateVuale = excelRcj.marketPrice;
                            updatePrice = "marketPrice";
                            updateRcjList.push(obj);
                        }
                    }
                }
            }
            if (ObjectUtils.isNotEmpty(updateVuale)){
                let jieSuanUpdateRcjStrategy = new JieSuanUpdateRcjStrategy({constructId, singleId:obj.singleId, unitId:obj.unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId),arg:args});
                let constructProjectRcj = {};
                constructProjectRcj[updatePrice] = updateVuale;
                await jieSuanUpdateRcjStrategy.execute({pointLine:obj,upDateInfo:constructProjectRcj});
                updateVuale = null;
                obj.sourcePrice = "线下导入数据";
            }

        }

        let result ={}
        result.size = updateRcjList.length;
        //
        // let updateRcj = [];
        //
        // if (!ObjectUtils.isEmpty(updateRcjList)){
        //     for (let updateRcjListElement of updateRcjList) {
        //         let rcj = {};
        //         rcj.sequenceNbr = updateRcjListElement.sequenceNbr;
        //         rcj.updatePrice = updateRcjListElement.updatePrice;
        //         rcj[updateRcjListElement.updatePrice] = updateRcjListElement[updateRcjListElement.updatePrice];
        //         updateRcj.push(rcj);
        //     }
        // }
        //result.updateRcj = updateRcj;
        result.updateRcj = updateRcjList;
        return result;
    }
}

JieSuanRcjProcess.toString = () => '[class JieSuanRcjProcess]';
module.exports = JieSuanRcjProcess;