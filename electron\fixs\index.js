const { app } = require('electron');
const FbfxFix = require("./fbfx");
const {ObjectUtils} = require("../utils/ObjectUtils");
const EE = require("../../core/ee");
const OtherFix = require("./other");
const GlobalConfigurationFix = require("./globalConfiguration");
const Helper = require("../../core/utils/helper");
/**
 *用户历史版本的 ysf 文件导入，补丁程序
 * 比如：新版本 1.0.1 引入了新功能，但是用户之前已经使用了旧版本，导致用户使用新版本时，无法正常计算。
 * 需要补丁进行对旧版本数据的补丁
 * */
class Fixs{
  constructor(project,tarVersion) {
      this.service = EE.app.service;
      //当前程序版本
      this.version = app.getVersion();
      //当前导入的文件版本号
      this.tarVersion = tarVersion||"1.0.1";
      //工程项目
      this.project = project;
      //案例默认添加 分部分项的补丁
      this.fixInstance=[new FbfxFix(this),new OtherFix(this),new GlobalConfigurationFix(this)]
  }
  /**
  * 比较当前程序版本号与导入文件版本号，返回结果
   * 1：表示当前版本比打开的文件版本大，需要补丁
   * 0：表示当前版本与打开的文件版本一致，不需要补丁
   * -1：表示当前版本比打开的文件版本小，不需要补丁，有可能当前版本的文件不能在 当前版本中打开
  * */
  compareVersions(){
      const parts1 = this.version.split('.').map(Number);
      const parts2 = this.tarVersion.split('.').map(Number);
      for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
          const num1 = parts1[i] || 0;
          const num2 = parts2[i] || 0;
          if (num1 > num2) return 1;
          if (num1 < num2) return -1;
      }

      return 0; // 相等
  }
  /**
   * 执行修补函数
   * 执行前判断当前版本是否需要补丁
   * 如果当文件版本号 小于当前版本，则不执行补丁 可能需要提示用户 使用对应的软件进行打开
   * */
  async fix() {
      if(ObjectUtils.isEmpty(this.tarVersion))return;

      let result = this.compareVersions();
      if(result<1)return;
      for (let fix of this.fixInstance) {
          try {
            await  fix.fix()
          }catch (e) {
              console.log("修补程序出错",e.message)
          }
      }
      try {
          await this.service.azCostMathService.clearAzCacheByFwxsVersion(this.project);
      }catch (e) {
          console.log("修补程序出错",e.message)
      }

  }
}
module.exports = Fixs


