const os = require("os");
const AdmZip = require("adm-zip");
const crypto = require("crypto");
const {writeFile} = require("fs");
const {ConstructProject} = require("../model/ConstructProject");
const {Snowflake} = require("../utils/Snowflake");
const {getRepository} = require("typeorm");
const password = 'myPassword';
const fs = require('fs');
const {el} = require("date-fns/locale");
const ConstructBiddingTypeConstant = require("../enum/ConstructBiddingTypeConstant");
const {ObjectUtils} = require("./ObjectUtils");
const {ItemBillProject} = require("../model/ItemBillProject");
const {RcjDetails} = require("../model/RcjDetails");
const {ConversionInfo} = require("../model/ConversionInfo");
const {ArrayUtil} = require("./ArrayUtil");
const ConstructionMeasureTypeConstant = require("../enum/ConstructionMeasureTypeConstant");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const JSZip = require('jszip');
const _ = require("lodash");
const path = require ('path');
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const CostListEnum = require("../enum/CostListEnum");
const ConstantUtil = require('../enum/ConstantUtil');
const LogUtil = require("../../packages/PreliminaryEstimate/core/tools/logUtil");
const TaxCalculationMethodEnum = require("../enum/TaxCalculationMethodEnum");
const {CryptoUtils} = require("./CrypUtils");
const Helper = require('../../core/utils/helper');
const {UnitProject} = require("../model/UnitProject");
const EE = require("../../core/ee");
const {OtherProjectQzAndSuoPei} = require("../model/OtherProjectQzAndSuoPei");
const OtherProjectCalculationBaseConstant = require("../enum/OtherProjectCalculationBaseConstant");
const {OtherProjectDayWork} = require("../model/OtherProjectDayWork");

/**
 * ysf文件查询操作工具类
 */
class PricingFileFindUtils{

    //装饰垂运、超高、安装费用、其他总价措施、安文费清单、补充清单
    filterList(list){
        if (ObjectUtils.isEmpty(list)){
            return list;
        }
        //装饰垂运  超高
        let filter = list.filter(item => !ObjectUtils.isEmpty(item.standardId)).filter(k => !CostListEnum.list.includes(k.standardId))
            //其他总价措施 安文费清单
            .filter(k => ObjectUtils.isEmpty(k.zjcsClassCode));
        return filter;
    }

    /**
     * 根据清单获取定额
     */
    getDeByQdId(constructId,singleId, unitId,sequenceNbr) {
        let {measureProjectTables, itemBillProjects} = this.getUnit(constructId, singleId, unitId);
        let qd = itemBillProjects.getNodeById(sequenceNbr);
        if (ObjectUtils.isEmpty(qd)) {
            qd = measureProjectTables.getNodeById(sequenceNbr);
        }

        if(qd.children.length>0){
            return qd.children.filter(i=>(ObjectUtils.isNotEmpty(i.fxCode) || ObjectUtils.isNotEmpty(i.bdCode)))
        }
        return new Array();
    }


    /**
     * 获取单价措施定额
     */
    getDeByDjcs(constructId,singleId, unitId){
        let {measureProjectTables} = this.getUnit(constructId,singleId, unitId);
        //获取措施项目中的单价措施标题下的定额数据
        let measureDJCS = measureProjectTables.getAllNodes().filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.DJCS).map(i => i.sequenceNbr);
        let djcsDe = this.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.de, measureDJCS);
        return djcsDe;
    }
    /**
     * 获取总价定额
     */
    getDeByZjcs(constructId,singleId, unitId){
        let {measureProjectTables} = this.getUnit(constructId,singleId, unitId);
        //获取其他总价措施标题下的定额数据
        let measureZJCS = measureProjectTables.getAllNodes().filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.ZJCS).map(i => i.sequenceNbr);
        let zjcsDe = this.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.de, measureZJCS);
        return zjcsDe;


    }

    /**
     * 获取安文费定额
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {lineData[]}
     */
    getDeByAwf(constructId,singleId, unitId){
        let {measureProjectTables} = this.getUnit(constructId,singleId, unitId);
        //获取安文费标题下的定额数据
        let measureAwf = measureProjectTables.getAllNodes().filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.AWF).map(i => i.sequenceNbr);

        let awfDe = this.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.de, measureAwf);
        return awfDe;
    }

    getQdByAwf(constructId, singleId, unitId) {
        let { measureProjectTables } = this.getUnit(constructId, singleId, unitId);
        //获取安文费标题下的定额数据
        let measureAwf = measureProjectTables.getAllNodes().filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.AWF).map(i => i.sequenceNbr);

        let awfDe = this.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.qd, measureAwf);
        return awfDe;
    }


    /**
     * 获取分部分项
     */
    getDeByfbfx(constructId,singleId, unitId){
        let {itemBillProjects} = this.getUnit(constructId,singleId, unitId);
        //获取分部分项标题下的定额数据
        let fbfxDe = itemBillProjects.getAllNodes().filter(k => k.kind === BranchProjectLevelConstant.de);
        return fbfxDe;
    }

    /**
     * 获取安文费
     */
    getDeByAwf(constructId,singleId, unitId){
        let {measureProjectTables} = this.getUnit(constructId,singleId, unitId);
        //获取其他总价措施标题下的定额数据
        let measureZJCS = measureProjectTables.getAllNodes().filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.AWF).map(i => i.sequenceNbr);
        let zjcsDe = this.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.de, measureZJCS);
        return zjcsDe;
    }


    /**
     * 获取分部分项清单数据
     */
    getQdByfbfx(constructId,singleId, unitId){
        let {itemBillProjects} = this.getUnit(constructId,singleId, unitId);
        //获取分部分项标题下的定额数据
        let fbfxDe = itemBillProjects.filter(k => k.kind === BranchProjectLevelConstant.qd);
        return fbfxDe;
    }


    /**
     * 获取总价定额清单数据
     */
    getQdByZjcs(constructId,singleId, unitId) {
        let {measureProjectTables} = this.getUnit(constructId, singleId, unitId);
        //获取其他总价措施标题下的数据
        let measureZJCS = measureProjectTables.getAllNodes().filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.ZJCS).map(i => i.sequenceNbr);
        let zjcsDe = this.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.qd, measureZJCS);
        return zjcsDe;


    }

    /**
     * 获取单价措施清单
     */
    getQdByDjcs(constructId,singleId, unitId) {
        let {measureProjectTables} = this.getUnit(constructId, singleId, unitId);
        //获取其他总价措施标题下的数据
        let measureZJCS = measureProjectTables.getAllNodes().filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.DJCS).map(i => i.sequenceNbr);
        let djcsDe = this.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.qd, measureZJCS);
        return djcsDe;
    }

    /**
     * 获取单价措施的子项和清单list  包含可能含有多个标题的情况，将其汇总到一个list下
     */
    async getQdFbList(constructId,singleId, unitId) {
        let {measureProjectTables} = this.getUnit(constructId, singleId, unitId);
        //获取其他总价措施标题下的数据
        let measureZJCS = measureProjectTables.getAllNodes().filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.DJCS).map(i => i.sequenceNbr);

        let fbZfbQdList = [];
        await this.existInMeasureProjectTables(measureZJCS,measureProjectTables.getAllNodes(),fbZfbQdList);
        return fbZfbQdList;
    }

    //递归定义  如果集合上存在父节点的子节点，那么就将该子节点加入list
    async existInMeasureProjectTables(parentIds,measureProjectTables,fbZfbQdList) {
        for (let m = 0; m < parentIds.length; m++) {
            for (let i = 0; i < measureProjectTables.length; i++) {
                if (measureProjectTables[i].parentId == parentIds[m] && measureProjectTables[i].kind!="04") {
                    fbZfbQdList.push(measureProjectTables[i]);
                    await this.existInMeasureProjectTables([measureProjectTables[i].sequenceNbr],measureProjectTables,fbZfbQdList);
                }
            }
        }
    }


    /**
     * 获取安文费
     */
    getQdByAwf(constructId,singleId, unitId) {
        let {measureProjectTables} = this.getUnit(constructId, singleId, unitId);
        //获取其他总价措施标题下的数据
        let measureAWF = measureProjectTables.filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.AWF).map(i => i.sequenceNbr);
        let djcsDe = this.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.qd, measureAWF);
        return djcsDe;


    }

    /**
     * 根据一级人材机获取到二级人材机数据
     * @param unit
     */
    getRcjDetailListByRcjId(unit,rcj){
        let {rcjDetailList} = unit;
        let list = [];
        if(!ObjectUtils.isEmpty(rcjDetailList)) {
            //获取二级材料
           list = rcjDetailList.filter(k => k.rcjId === rcj.sequenceNbr);
        }

        return list;
    }




    /**
     * 根据一级人材机获取到二级人材机数据并且在分组
     * @param unit
     */
    getRcjDetailGroup(unit,rcj){
        let list = this.getRcjDetailListByRcjId(unit,rcj);
        if (ObjectUtils.isEmpty(list)){
            return {"rDetail":null,"cDetail":null,"jDetail":null};
        }
        //筛选人明细
        let RList = list.filter(k => k.kind === 1 );
        //筛选机明细
        let JList = list.filter(k => k.kind === 3);
        // 筛选材料明细
        let CList = list.filter(k => ![1,3,4].includes(k.kind));
        return {"rDetail":RList,"cDetail":CList,"jDetail":JList};
    }

    getUnitDatas(allDatas, searchKind, parentIds) {
        let resArray = [];
        if(!parentIds){
            parentIds=[allDatas.root.sequenceNbr];
        }
        if(parentIds&&typeof parentIds =="string"){
            parentIds = [parentIds];
        }
        for (let i = 0; i < parentIds.length; i++) {
           let id = parentIds[i];
           let node = allDatas.getNodeById(id);
           let flist = allDatas.filterAllSubsets(node,item=>{
               return item.kind==searchKind;
           });
            resArray = resArray.concat(flist);
        }
        return resArray;
    }

    /**
     * 查找分部分项或措施项目下的数据
     * @param allDatas 措施项目 或 分部分项全量数据
     * @param searchKind 要找的数据的类型，kind = 清单 | kind = 定额
     * @param parentIds  要在什么地方找 如果找全部，则不传参 可传单个可传数组
     *
     * @return [lineData]
     */
    getUnitDatasOld(allDatas, searchKind, parentIds) {
        // 处理入参
        let numSearchKind = Number.parseInt(searchKind);
        if (!parentIds) {
            parentIds = allDatas[0].sequenceNbr;
        }
        if (!ObjectUtils.isArray(parentIds)) {
            let buffer = parentIds;
            parentIds = [];
            parentIds.push(buffer);
        }

        // 定义搜索变量 例如 部/部/部/清1 清2 清3 入参为第一个部 按照pid匹配清单时需要知道第三个部
        let pidMap = {};
        // 定义结果变量
        let resArray = [];
        for (let i =0 ; i < parentIds.length ; ++i) {
            pidMap[parentIds[i]] = 1;
        }
        for (let index = 0 ; index < allDatas.length ; ++ index) {
            let lineData = allDatas[index];
            let numLineKind = Number.parseInt(lineData.kind);
            if (!lineData.sequenceNbr || lineData.sequenceNbr ==="") {
                continue;
            }
            if (numLineKind < numSearchKind) {
                if (pidMap[lineData.parentId]) {
                    pidMap[lineData.sequenceNbr] = 1;
                }
            }
            if (numLineKind === numSearchKind) {
                if (pidMap[lineData.parentId]) {
                    resArray.push(lineData);
                }
            }
        }

        return resArray;
    }

    /**
     * 获取默认存储路径
     */
     getDefaultStoragePath(fileName) {
        let baseDataDir = ObjectUtils.isEmpty(fileName)
            ? `${os.homedir()}\\.xilidata\\`
            : `${os.homedir()}\\.xilidata\\${fileName}` + ConstantUtil.YUSUAN_FILE_DOT_SUFFIX;
        return baseDataDir;
    }


    /**
     * 获取文件下载路径
     */
    getFileDownloadPath(){

        let sql = "select content FROM sys_params WHERE params_tag =?";
        let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).all("FILE_DOWNLOAD_PATH");
        let content = sqlRes[0].content;

        let falg = /^\/|^([A-Za-z]:\\)/.test(content);

        if (falg){
            return content;
        }else {
            //获取到软件安装目录
            let homeDir =  path.dirname(app.getPath('exe'));
            return homeDir;
        }
    }





    /**
     * 获取工程项目或者单位工程的编制说明
     * @param fileName
     * @return {Promise<string>}
     */
     getOrganizationInstructions(levelType, constructId,singleId, unitId) {
        let fileObj =  this.getProjectObjById(constructId);
        if (1 == levelType) {
            //工程项目
            return fileObj.organizationInstructions;
        }
        if (3 == levelType) {
            let unit = this.getUnit(constructId,singleId,unitId);
            return unit.organizationInstructions;
        }
    }







    /**
     * 获取单位工程
     * @param fileName
     * @return {UnitProject}
     */
     getUnit(constructId,singleId, unitId) {
        let proJectData = this.getProjectObjById(constructId);
        if(ObjectUtils.isNotEmpty(proJectData.bzUnitProject)){
            if(unitId === proJectData.bzUnitProject.sequenceNbr){
                return  proJectData.bzUnitProject;
            }
        }
        if (2 == proJectData.biddingType){
            //单位工程
            let unitProject = proJectData.unitProject;
            return unitProject;
        }else {
            //单项
            let singleProject = this.getSingleProject(constructId,singleId);
            if (ObjectUtils.isEmpty(singleProject)){
                if (!ObjectUtils.isEmpty(proJectData.unitProjectArray)){
                    let unitProject = proJectData.unitProjectArray.find((item ) => item.sequenceNbr === unitId);
                    return unitProject;
                }
            }else {
                let array = new Array();
                array = this.getSubSingleUnits(singleProject,array);
                let unitProject = array.find((item ) => item.sequenceNbr === unitId);
                return unitProject;
            }
        }

    }

    getUnitByConstructObj(proJectData,singleId,unitId) {
        if(ObjectUtils.isNotEmpty(proJectData.bzUnitProject)){
            if(unitId === proJectData.bzUnitProject.sequenceNbr){
                return  proJectData.bzUnitProject;
            }
        }
        if (2 == proJectData.biddingType){
            //单位工程
            let unitProject = proJectData.unitProject;
            return unitProject;
        }else {
            //单项
            let singleProject = this.getOneFromSingleProjects(proJectData.singleProjects, singleId);
            if (ObjectUtils.isEmpty(singleProject)){
                if (!ObjectUtils.isEmpty(proJectData.unitProjectArray)){
                    let unitProject = proJectData.unitProjectArray.find((item ) => item.sequenceNbr === unitId);
                    return unitProject;
                }
            }else {
                let array = new Array();
                array = this.getSubSingleUnits(singleProject,array);
                let unitProject = array.find((item ) => item.sequenceNbr === unitId);
                return unitProject;
            }
        }
    }

    async execClearCostDe(deArray, costTypeArray, unit, changeDeIds, removeStrategy) {
        const allNodes = deArray.getAllNodes();
        for (let i = allNodes.length - 1; i >= 0; i--) {
            let item = allNodes[i];
            if (item.kind == BranchProjectLevelConstant.de && !ObjectUtils.isEmpty(item.isCostDe) && costTypeArray.includes(item.isCostDe)) {
                if (!ObjectUtils.isEmpty(unit.constructProjectRcjs)) {
                    unit.constructProjectRcjs = unit.constructProjectRcjs.filter(k => k.deId != item.sequenceNbr);
                }
                // item 删除
                // allNodes.splice(i, 1);
                // deArray.removeNode(item.sequenceNbr);
                await removeStrategy.execute({
                    pointLine: item,
                    isBlock: item.kind !== BranchProjectLevelConstant.de
                });
                // 删除时这个定额会被删除  导致后续无法查到定额  也就没法继续做后续   此处拿定额的父级清单id   用于处理定额删除后清单的汇总
                changeDeIds.add(item.parentId);
            }
        }
    }

    /**
     * 获取项目下所有的单位
     * @param fileName
     * @return {UnitProject}
     */
    getUnitList(constructId) {


        let proJectData = this.getProjectObjById(constructId);
        return this.getUnitListByConstructObj(proJectData);

    }


    /**
     * 根据单位Id 获取项目下的单位
     * @param fileName
     * @return {UnitProject}
     */
    getUnitById(constructId,unitId) {
       let unitList = this.getUnitList(constructId);
       return unitList.find(unit=>unit.sequenceNbr==unitId);
    }

    /**
     * 获取某个单项下的所有单位工程
     * 支持子单项
     */
    getUnitListBySingle(constructId, singleId) {
        let unitArr = [];
        const singleProject = this.getSingleProject(constructId, singleId);
        this._findSingleUnit(singleProject, unitArr);
        return unitArr;
    }

    _findSingleUnit(singleProject, unitArr) {
        if (ObjectUtils.isEmpty(singleProject)) {
            return;
        }
        if (ObjectUtils.isEmpty(singleProject.subSingleProjects)) {
            if (ObjectUtils.isNotEmpty(singleProject.unitProjects)) {
                singleProject.unitProjects.map(item => unitArr.push(item));
            }
            return;
        }
        for (const subSingle of singleProject.subSingleProjects) {
            this._findSingleUnit(subSingle, unitArr);
        }
    }

    /**
     * 获取项目下所有的单位(根据对象)
     * @param proJectData
     * @returns {any[]}
     */
    getUnitListByConstructObj(proJectData) {
        let array = new Array();
        if(ObjectUtils.isNotEmpty(proJectData.bzUnitProject)){
            array.push(proJectData.bzUnitProject);
            return  array;
        }
        if (2 == proJectData.biddingType) {
            //单位工程
            let unitProject = proJectData.unitProject;
            array.push(unitProject);
            return array;
        } else {
            //单项
            let singleProjectList = proJectData.singleProjects;
            if (ObjectUtils.isEmpty(singleProjectList)) {
                if (!ObjectUtils.isEmpty(proJectData.unitProjectArray)) {
                    let unitProject = proJectData.unitProjectArray;
                    array = array.concat(unitProject);
                    return array;
                }
            } else {
                for (const single of singleProjectList) {
                    array = this.getSubSingleUnits(single, array);
                }
                return array;
            }
        }

        return array;
    }

    /**
     * 在标准组价弹窗下拿到工作台的所有单位
     * @param proJectData
     * @returns {any[]}
     */
    getUnitListByStandardMerge(constructId) {
        let proJectData = this.getProjectObjById(constructId);
        let array = new Array();
        if (2 == proJectData.biddingType) {
            //单位工程
            let unitProject = proJectData.unitProject;
            array.push(unitProject);
            return array;
        } else {
            //单项
            let singleProjectList = proJectData.singleProjects;
            if (ObjectUtils.isEmpty(singleProjectList)) {
                if (!ObjectUtils.isEmpty(proJectData.unitProjectArray)) {
                    let unitProject = proJectData.unitProjectArray;
                    array = array.concat(unitProject);
                    return array;
                }
            } else {
                for (const single of singleProjectList) {
                    array = this.getSubSingleUnits(single, array);
                }
                return array;
            }
        }

        return array;
    }

//得到子单项的所有单位
    getSubSingleUnits(singleProject,array) {

        if (!ObjectUtils.isEmpty(singleProject.unitProjects)) {  //达到最后一层包含单位的子单项
            array = array.concat(singleProject.unitProjects);
        }else {
            if(!ObjectUtils.isEmpty(singleProject.subSingleProjects)){
                for (let i = 0; i < singleProject.subSingleProjects.length; i++) {
                    array = this.getSubSingleUnits(singleProject.subSingleProjects[i],array);
                }
            }
        }
        return array;
    }





    /**
     * 获取单项工程
     * @param fileName
     * @return {Promise<string>}
     */
    getSingleProjectList(constructId) {
        let proJectData = this.getProjectObjById(constructId);
        if (ObjectUtils.isEmpty(proJectData.singleProjects)){
            return null;
        }
        //单项
        let singleProject = proJectData.singleProjects;
        return singleProject;

    }

    getOneFromSingleProjects(singleProjects, singleId){
        if(ObjectUtils.isEmpty(singleProjects)){
            return null;
        }

        for(let i = 0; i < singleProjects.length; i++){
            if(singleProjects[i].sequenceNbr == singleId){
                return singleProjects[i];
            }

            if(!ObjectUtils.isEmpty(singleProjects[i].subSingleProjects)){
                let result = this.getOneFromSingleProjects(singleProjects[i].subSingleProjects, singleId);
                if (result != null) {
                    return result;
                }
            }
        }
    }


    /**
     * 获取项目下指定的单项
     * @param fileName
     * @return {Promise<string>}
     */
    getSingleProject(constructId,singleId) {

        if(ObjectUtils.isEmpty(singleId)){
            return null;
        }

         let proJectData = this.getProjectObjById(constructId);
        // if (ObjectUtils.isEmpty(proJectData.singleProjects)){
        //     return null;
        // }
        // //单项
        // let singleProject = proJectData.singleProjects.find((item ) => item.sequenceNbr === singleId);
        // return singleProject;

        return this.getOneFromSingleProjects(proJectData.singleProjects, singleId)

    }

    /**
     * 获取单项工程的父实体
     * @param constructId
     * @param singleId
     * @return {parent:xxx, isSingle: false|true}
     */
    getParentBySingleId(constructId,singleId){
        let constructData = this.getProjectObjById(constructId);
        if (ObjectUtils.isEmpty(constructData.singleProjects)){
            return {parent: null, isSingle: false};
        }

        let singleProject = constructData.singleProjects.find((item) => item.sequenceNbr === singleId);
        if(ObjectUtils.isNotEmpty(singleProject)){
            return {parent:constructData, isSingle: false};
        }

        let obj = this.getParentSingleByChildId(constructData.singleProjects, singleId);
        if(ObjectUtils.isEmpty(obj)){
            return {parent: null, isSingle: false};
        }else{
            return {parent: obj, isSingle: true};
        }
    }

    /**
     * 根据单项id，查找父实体
     * @param singleProjects
     * @param childId
     * @returns {*|null}
     */
    getParentSingleByChildId(singleProjects, childId){
        if(ObjectUtils.isEmpty(singleProjects)){
            return null;
        }

        for(let i = 0; i < singleProjects.length; i++){
            if(this._ifChileSingle(singleProjects[i], childId)){
                return singleProjects[i];
            }else{
                let ps = this.getParentSingleByChildId(singleProjects[i].subSingleProjects, childId);
                if(ObjectUtils.isNotEmpty(ps)){
                    return ps;
                }
            }
        }
    }

    /**
     * 判断childId是否是parentSingleProject子单项id
     * @param parentSingleProject
     * @param childId
     * @returns {boolean|*}
     * @private
     */
    _ifChileSingle(parentSingleProject, childId){
        if (ObjectUtils.isEmpty(parentSingleProject.subSingleProjects)){
            return false;
        }

        let singleProject = parentSingleProject.subSingleProjects.find((item) => item.sequenceNbr === childId);
        return ObjectUtils.isNotEmpty(singleProject);
    }



    /**
     * 获取单位工程费用代码
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {*}
     */
    getUnitCostCodePrice(constructId,singleId, unitId){
         let unit = this.getUnit(constructId,singleId, unitId);
         return unit.unitCostCodePrices;
    }

    /**
     * 获取单位工程费用汇总
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {*}
     */
    getUnitCostSummary(constructId,singleId, unitId){
        let unit = this.getUnit(constructId,singleId, unitId);
        return unit.unitCostSummarys;
    }

    /**
     * 获取费用汇总下进项税明细
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Array<UnitCostSummary>}
     */
    getInputTaxDetails(constructId,singleId, unitId){
        let unit = this.getUnit(constructId,singleId, unitId);
        return unit.inputTaxDetails;
    }

    /**
     * 获取单位工程总承包服务费
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {*}
     */
    getOtherProjectServiceCost(constructId,singleId, unitId){
        let unit = this.getUnit(constructId,singleId, unitId);
        return unit.otherProjectServiceCosts;
    }

    /**
     * 获取单位工程计日工
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {*}
     */
    getOtherProjectDayWork(constructId,singleId, unitId){
        let unit = this.getUnit(constructId,singleId, unitId);
        return unit.otherProjectDayWorks;
    }

    /**
     * 获取单位工程其他项目
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {*}
     */
    getOtherProject(constructId,singleId, unitId){
        let unit = this.getUnit(constructId,singleId, unitId);
        return unit.otherProjects;
    }

    /**
     * 获取暂列金
     */
    getOtherProjectProvisional(constructId, singleId, unitId) {
        let unit = this.getUnit(constructId, singleId, unitId);
        return unit.otherProjectProvisionals;
    }

    /**
     * 获取专业工程暂估价
     */
    getOtherProjectZygcZgjList(constructId, singleId, unitId) {
        let unit = this.getUnit(constructId, singleId, unitId);
        return unit.otherProjectZygcZgjs;
    }

    /**
     * 获取工程项目基本信息
     * @param constructId
     * @returns {Promise<*>}
     */
     getConstructProjectJBXX(constructId){
        let constructProject = this.getProjectObjById(constructId);
        return constructProject.constructProjectJBXX;
    }

    /**
     * 获取工程项目定额标准
     * @param constructId
     * @returns {ConstantUtil.DE_STANDARD_12 | ConstantUtil.DE_STANDARD_22 }
     */
    getConstructDeStandard(constructId){
        let constructProject = this.getProjectObjById(constructId);
        return constructProject.deStandardReleaseYear;
    }

    is22Unit(unit) {
        if(ObjectUtils.isEmpty(unit)){
            return null;
        }
        return unit.deStandardReleaseYear == ConstantUtil.DE_STANDARD_22
    }

    is22UnitById(constructId, singleId, unitId) {
        return this.is22Unit(this.getUnit(constructId, singleId, unitId));
    }

    /**
     * 判断是否22定额标准
     * @param constructId
     * @returns {boolean}
     */
    is22De(constructId)
    {
        return this.getConstructDeStandard(constructId) == ConstantUtil.DE_STANDARD_22
    }

  is22DeItem(de) {
      return ObjectUtils.isNotEmpty(de.libraryCode) && (de.libraryCode.includes(ConstantUtil.DE_STANDARD_22) || de.libraryCode.includes(ConstantUtil.DE_STANDARD_23));
  }

    /**
     * 获取单位项目基本信息/工程特征
     * @param constructId
     * @param singleId
     * @param unitId
     * @param type （0：基本信息1：工程特征）
     * @returns {Promise<*>}
     */
     getUnitProjectJBXXOrXMTZ(constructId,singleId,unitId,type){
        let unitProject = this.getUnit(constructId,singleId,unitId);
        if(type===0){
            return unitProject.unitJBXX ;
        }else {
            return unitProject.unitGCTZ ;
        }
    }

    /**
     * 根据ID获取项目数据并且返回项目数据对象
     * 从内存里面取
     * @param path
     */
    getProjectObjById(id) {
        if (ObjectUtils.isEmpty(id) || ObjectUtils.isEmpty(global.constructProject) || ObjectUtils.isEmpty(global.constructProject[id])) {
            return null;
        }
        return global.constructProject[id].proJectData;
    }

    getProjectBuffer() {
        return global.projectBuffer||null;
    }

    setProjectBuffer(projectBuffer){
        global.projectBuffer=projectBuffer;
    }
    setProjectBeforeOption(copy=true){
        global.projectBeforeOption=copy;
    }
    getProjectBeforeOption() {
        return global.projectBeforeOption||null;
    }

    getInsertQdDe(){
        return global.insertQdDe||null;
    }

    //设置需要新增的定额（复用组价用）
    setInsertQdDe(insertQdDe){
        global.insertQdDe=insertQdDe;
    }
    /**
     * 根据路径读取本地文件加载
     * @param path
     * @return {*}
     */
    async getProjectObjByPath(path) {
        try {
            // // 读取压缩包文件
            const zipFileContent = fs.readFileSync(path);
            // 创建一个新的JSZip实例
            const zip = new JSZip();
            // 读取压缩包
            const zipObject = await zip.loadAsync(zipFileContent);
            // 读取指定文件的内容
            let fileContent = await zipObject.file("file.json").async("string");
            try {
                // 历史文件是未加密文件，解密会报错，捕捉异常使得可以打开历史文件
                fileContent = CryptoUtils.decryptAESData(fileContent);
            }catch (e) {
                console.log(e)
            }

            let object = JSON.parse(fileContent);
            //兼容历史版本,其他项目改动

            //this.setHistory(object);

            return object;
        } catch (error) {
            throw error;
        }
    }

    async setHistory(object) {
        this.service = EE.app.service;
        console.log(object.version);
        //判断版本号, 小于1代表版本号小于等于 v1.0.11
        if(!ObjectUtils.isEmpty(object.version)){
            let conut = Helper.compareVersion(object.version, '1.0.11')
            if(conut < 1){
                //拿到所有单位
                let unitProjects = this.getUnitListByConstructObj(object);
                for (let i = 0; i < unitProjects.length; i++) {
                    let unit = unitProjects[i];
                    //初始化措施项目调整系数  以及人材机的数据
                    this.service.constructProjectService.initUnitCsxmCoefficientAndRcjQty(unit);
                    //初始化历史项目中人材机缓存数据
                    await this.service.constructProjectService.initUnitRcjCache(unit);
                    if(ObjectUtils.isEmpty(unit.otherProjectProvisionals)){
                        //暂列金初始化
                        let otherProjectZljeList = this.service.otherProjectService.getInitOtherProjectZljeList(unit);
                        unit.otherProjectProvisionals = otherProjectZljeList;

                    }
                    if(ObjectUtils.isEmpty(unit.otherProjectZygcZgjs)){
                        //专业工程暂估价初始化
                        let otherProjectZygcZgjList = this.service.otherProjectService.getInitOtherProjectZygcZgjList(unit);
                        unit.otherProjectZygcZgjs = otherProjectZygcZgjList;
                    }
                    if(ObjectUtils.isEmpty(unit.otherProjectQzAndSuoPeis)){
                        //签证索赔初始化
                        let otherProjectQzSpJjbList = this.service.otherProjectService.getInitOtherProjectQzSpJjb(unit);
                        unit.otherProjectQzAndSuoPeis = otherProjectQzSpJjbList;
                    }

                    let otherProjects = unit.otherProjects
                    for (let j = 0; j < otherProjects.length; j++) {
                        if('暂列金额' === otherProjects[j].extraName || '暂估价' === otherProjects[j].extraName ||
                            '总承包服务费' === otherProjects[j].extraName || '计日工' === otherProjects[j].extraName){
                            otherProjects[j].putOntotalFlag = true;
                        }
                    }
                    let result = unit.otherProjectDayWorks.filter(item =>item.worksName === '计日工费用汇总');
                    if(result.length===0){
                        //计日工汇总
                        let newArr = new Array();
                        let otherProject0 = new OtherProjectDayWork();
                        otherProject0.sequenceNbr =Snowflake.nextId();
                        otherProject0.sortNo = 0;
                        otherProject0.worksName = '计日工费用汇总';
                        otherProject0.jinzhiFlag=true;
                        otherProject0.dataType = 0;
                        otherProject0.csTotal = 0;
                        otherProject0.jxTaxAmount = 0;
                        otherProject0.total = 0;
                        newArr.push(otherProject0);
                        unit.otherProjectDayWorks.forEach((item) => {
                            newArr.push(item);
                        });
                        unit.otherProjectDayWorks = newArr;
                        this.service.otherProjectDayWorkService.updateBiaotiTotal(unit);
                        this.service.otherProjectDayWorkService.updateHuiZongTotal(unit);

                    }
                    this.service.otherProjectServiceCostService.updateBiaotiTotal(unit);
                }
            }


            // 处理安装工程【房修需求】历史版本兼容问题
            await this.service.azCostMathService.clearAzCacheByFwxsVersion(object);
        }

    }

    //加密数据
    encryptData(data) {
        const cipher = crypto.createCipher('aes-256-cbc', password);
        let encryptedData = cipher.update(data, 'utf8', 'hex');
        encryptedData += cipher.final('hex');
        return encryptedData;
    }

    // 解密数据
    decryptData(encryptedData) {
        const decipher = crypto.createDecipher('aes-256-cbc', password);
        let decryptedData = decipher.update(encryptedData, 'hex', 'utf8');
        decryptedData += decipher.final('utf8');
        return decryptedData;
    }


    /**
     * 获取分部分项
     * @param constructId
     * @param singleId
     * @param unitId
     * @return {ItemBillProject[]}
     */
    getFbFx(constructId, singleId, unitId) {
        let unitProject = this.getUnit(constructId, singleId, unitId);
        return unitProject.itemBillProjects;
    }

    getCSXM(constructId, singleId, unitId) {
        let unitProject = this.getUnit(constructId, singleId, unitId);
        return unitProject.measureProjectTables;
    }

    getMainFeeFile(constructId, singleId, unitId) {
        let unitProject = this.getUnit(constructId, singleId, unitId);
        return this.getMainFeeFileByUnitObj(unitProject);
    }

    getMainFeeFileByUnitObj(unitProject) {
        return unitProject.feeFiles.filter(f=>f.defaultFeeFlag && f.defaultFeeFlag === 1)[0];
    }

    /**
     * 获取单位下 换算规则操作记录
     * @param constructId
     * @param singleId
     * @param unitId
     * @return {ConversionRuleOperationRecord[]}
     */
    getConversionRuleOperationRecord(constructId, singleId, unitId) {
        let unitProject = this.getUnit(constructId, singleId, unitId);
        return unitProject.conversionRuleOperationRecordList;
    }

    /**
     * 获取单位下 换算信息
     * @param constructId
     * @param singleId
     * @param unitId
     * @return {ConversionInfo[]}
     */
    getConversionInfo(constructId, singleId, unitId) {
        let unitProject = this.getUnit(constructId, singleId, unitId);
        return unitProject.conversionInfoList;
    }

    /**
     * 获取单位下 清单特征
     * @param constructId
     * @param singleId
     * @param unitId
     * @return {ListFeature[]}
     */
    getQdFeatureList(constructId, singleId, unitId) {
        let unitProject = this.getUnit(constructId, singleId, unitId);
        return unitProject.listFeatureList;
    }

    /**
     * 获取单位下 人材机数据
     * @param constructId
     * @param singleId
     * @param unitId
     * @return {ConstructProjectRcj[]}
     */
    getRcjList(constructId, singleId, unitId) {
        let unitProject = this.getUnit(constructId, singleId, unitId);
        return unitProject.constructProjectRcjs;
    }

    /**
     * 获取某一个定额的人材机数据
     */
    getDeRcjList(constructId, singleId, unitId, deId, unitObj) {
        const rcjList = ObjectUtils.isEmpty(unitObj) ? this.getRcjList(constructId, singleId, unitId) : unitObj.constructProjectRcjs;
        if (ObjectUtils.isEmpty(rcjList)) {
            return [];
        }
        return rcjList.filter(k => k.deId === deId);
    }

    /**
     * 获取某一批定额的人材机数据
     */
    batchGetDeRcjList(constructId, singleId, unitId, deIds, unitObj) {
        const rcjList = ObjectUtils.isEmpty(unitObj) ? this.getRcjList(constructId, singleId, unitId) : unitObj.constructProjectRcjs;
        if (ObjectUtils.isEmpty(rcjList)) {
            return [];
        }
        return rcjList.filter(k => deIds.includes(k.deId));
    }

    /**
     * 获取单位下 人材机配比明细
     * @param constructId
     * @param singleId
     * @param unitId
     * @return {RcjDetails[]}
     */
    getRcjDetailList(constructId, singleId, unitId) {
        let unitProject = this.getUnit(constructId, singleId, unitId);
        return unitProject.rcjDetailList;
    }


    /**
     * 判断传入的数据在分部分项下还是措施项目下返回该数据
     */
    getModuleData(constructId, singleId, unitId,sequenceNbr){
        let unitProject = this.getUnit(constructId, singleId, unitId);

        let itemBillProjects = unitProject.itemBillProjects;
        let measureProjectTables = unitProject.measureProjectTables;

        if (!ObjectUtils.isEmpty(itemBillProjects.find(k =>k.sequenceNbr === sequenceNbr))){
            return itemBillProjects;
        }
        if (!ObjectUtils.isEmpty(measureProjectTables.find(k =>k.sequenceNbr === sequenceNbr))){
            return measureProjectTables;
        }


    }

    getDeByIdAndBeLong(unitProject, deId, deBeLong){
        if(deBeLong == "fbfx"){
            return unitProject.itemBillProjects.find(k => k.sequenceNbr == deId);
        }else{
            return unitProject.measureProjectTables.find(k => k.sequenceNbr == deId);
        }
    }

    /**
     * 通过定额id 获取清单
     * @param constructId
     * @param singleId
     * @param unitId
     * @param sequenceNbr 定额id
     */
    getQdByDeId(constructId, singleId, unitId,sequenceNbr){
        let unitProject = this.getUnit(constructId, singleId, unitId);
        let itemBillProjects = unitProject.itemBillProjects;


        let t = itemBillProjects.find(k =>k.sequenceNbr === sequenceNbr);
        if (!ObjectUtils.isEmpty(t)){
            let t1 = itemBillProjects.find(i=>i.sequenceNbr == t.parentId);
            return t1;
        }

        let measureProjectTables = unitProject.measureProjectTables;
        let t2 = measureProjectTables.find(k =>k.sequenceNbr === sequenceNbr);
        if (!ObjectUtils.isEmpty(t2)){
            let t3 = measureProjectTables.find(i=>i.sequenceNbr == t2.parentId);
            return t3;
        }

        return null;
    }

    /**
     * 根据路径获取xml
     * @param path
     * @returns {Promise<any>}
     */
    async readXml(path){
        try {
            // // 读取压缩包文件
            const zipFileContent = fs.readFileSync(path);
            // 创建一个新的JSZip实例
            const zip = new JSZip();
            // 读取压缩包
            const zipObject = await zip.loadAsync(zipFileContent);
            // 读取指定文件的内容
            const fileContent = await zipObject.file("file.xml").async("string");
            return JSON.parse(fileContent);
        } catch (error) {
            throw error;
        }
    }


    userHistoryDataPath(){
        const baseDataDir = `${os.homedir()}\\.xilidata`;
        //历史记录路径
        let userHistoryWay = baseDataDir+'\\userHistory.json';
        return userHistoryWay;
    }


    /**
     * 获取用户整体文件数据
     * @return {any}
     */
     userHistoryData() {
        let userHistoryWay = this.userHistoryDataPath();
        //如果不存在创建文件
        if (!fs.existsSync(userHistoryWay)) {
            let obj ={
                key :'key1为用户主键_身份 存历史信息  key2 为主键 存对应身份 '
            }
            let data = ObjectUtils.toJsonString(obj);
            try {
                fs.writeFileSync(userHistoryWay, data);
                console.log('创建文件后写入文件成功！');
            } catch (err) {
                console.error('写入文件时发生错误：', err);
            }
        }
        //读取数据
        const data = fs.readFileSync(userHistoryWay, 'utf8');
        const userHistoryData = JSON.parse(data);
        return userHistoryData;
    }

    /**
     * 获取用户身份标识信息
     * @return {Promise<any>}
     */
     userInfoData() {
        //查询当前用户记录
        let idKey;
        try
        {
            let sequenceNbr  = global.idInformation.sequenceNbr;
            let identity  = global.idInformation.identity;
            //生成身份key
            idKey = sequenceNbr+'_'+identity;
        }
        catch (e)
        {
            LogUtil.renderLogger('获取用户身份标识信息失败');
        }

        return idKey;
    }




    /**
     * 用户历史文件列表数据
     * @return {*}
     */
    userHistoryFileListData() {
        let userHistoryData = this.userHistoryData();
        //生成身份key
        let idKey = this.userInfoData()
        return userHistoryData[idKey];
    }

    /**
     * 获取单位工程的所有定额
     * @param constructId
     * @param singleId
     * @param unitId
     */
    getAllDe(constructId, singleId, unitId) {
        let deArr = [];
        // 分部分项
        const fbFxDeArr = this.getDeByfbfx(constructId, singleId, unitId);
        //措施项目定额
        let deMeasureProjectTables = this.getUnitDatas(this.getUnit(constructId, singleId, unitId).measureProjectTables, BranchProjectLevelConstant.de);
        deArr = deArr.concat(fbFxDeArr).concat(deMeasureProjectTables);
        return deArr;
    }


    getDeById(constructId, singleId, unitId, deId) {
        const unit = this.getUnit(constructId, singleId, unitId);
        let deData = unit.itemBillProjects.getNodeById(deId);
        if (ObjectUtils.isNotEmpty(deData)) {
            return deData;
        }
        deData = unit.measureProjectTables.getNodeById(deId);
        if (ObjectUtils.isNotEmpty(deData)) {
            return deData;
        }
        return null;
    }


    /**
     * 获取工程项目的计税方式
     * @param constructId
     * @returns {boolean} 返回true 为 简易计税 false 为一般
     */
    getConstructProjectTaxCalculationMethod(constructId){

        let projectObjById = this.getProjectObjById(constructId);

        //计税对象
        let projectTaxCalculation = projectObjById.projectTaxCalculation;

        //计税方式
        let taxCalculationMethod = projectTaxCalculation.taxCalculationMethod;
        let simple = false;
        //简易计税
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            simple = true;
        }

        return simple;
    }

}

module.exports = {
    PricingFileFindUtils: new PricingFileFindUtils()
};
