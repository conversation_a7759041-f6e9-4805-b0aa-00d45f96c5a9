const { Controller } = require('../../../core');
const { ResponseData } = require('../../../common/ResponseData');
const JieSuanInputTaxDetailsService = require('../../../packages/jieSuanProject/service/jieSuanInputTaxDetailsService');

class jieSuanInputTaxDetailsController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
        this.jieSuanInputTaxDetailsService = new JieSuanInputTaxDetailsService(ctx);
    }

    /**
     * 获取进项税明细
     * @param args
     * @returns {ResponseData}
     */
    getInputTaxDetails(args){
        const res = this.service.inputTaxDetailsService.getInputTaxDetails(args);
        return ResponseData.success(res);
    }

    /**
     * 修改进项税明细（除税系数、备注）
     * @param args
     * @returns {ResponseData}
     */
    async updateInputTaxDetails(args){
        const res = await this.service.jieSuanProject.jieSuanInputTaxDetailsService.updateInputTaxDetails(args);
        return ResponseData.success(res);
    }
}

jieSuanInputTaxDetailsController.toString = () => '[class jieSuanInputTaxDetailsController]';
module.exports = jieSuanInputTaxDetailsController;