const {Controller} = require("../../../core");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");


/**
 * 人材分期调整
 */
class JieSuanRcjStageController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    jieSuanRcjStageService=this.service.jieSuanProject.jieSuanRcjStageService;
    jieSuanRcjProcess=this.service.jieSuanProject.jieSuanRcjProcess;

    /**
     * 人材机分期设置
     * @return {*}
     */
    rcjStageSet(args) {
        return  this.service.jieSuanProject.jieSuanRcjStageService.rcjStageSet(args);

    }

    /**
     * 获取人材机分期设置
     * @param args
     * @return {*}
     */
    getRcjStageSet(args) {
        return  this.service.jieSuanProject.jieSuanRcjStageService.getRcjStageSet(args);

    }


    /**
     * 清单级别人材机分期方式切换
     * @param args
     * @return {*}
     */
    rcjStageSwitch(args) {
        return  this.service.jieSuanProject.jieSuanRcjStageService.rcjStageSwitch(args);

    }


    /**
     * 分期比例批量应用
     */
    stageRatioBatchUse(args){
        return  this.service.jieSuanProject.jieSuanRcjStageService.stageRatioBatchUse(args);
    }

    /**
     * 清单级别的分期修改
     * @param args
     */
    qdRcjStageUpdate(args){
        return  this.service.jieSuanProject.jieSuanRcjStageService.qdRcjStageUpdate(args);
    }


    /**
     * 批量调整风险幅度范围设置
     * @param constructId
     * @param singleId
     * @param unitId
     * @param tcType  调差类型  1 人工费  2 材料费 3 机械费  4 暂估材料调差
     * @param list 所选调差数据集合 如果是空表示修改的是整个单位
     * @param max 最大值
     * @param min 最小值
     */
    async riskAmplitudeRangeController(args){
        //修改数据
       await this.jieSuanRcjStageService.riskAmplitudeRange(args);
       return ResponseData.success(true);

    }

    /**
     * 批量调整风险幅度范围设置  ----工程项目
     * @param constructId
     * @param tcType  调差类型  1 人工费  2 材料费 3 机械费  4 暂估材料调差
     * @param list 所选调差数据集合 如果是空表示修改的是整个单位
     * @param max 最大值
     * @param min 最小值
     */
    async constructRiskAmplitudeRangeController(args){
        //修改数据
        await this.jieSuanRcjStageService.constructRiskAmplitudeRange(args);
        return ResponseData.success(true);

    }


    /**
     * 批量应用价差取费设置
     * @param constructId
     * @param singleId
     * @param unitId
     * @param map  {"key1":"value1","key2":"value2","key3":"value3"}  key是类型：1（人工）  2（材料） 3（机械），  value：具体指（计税金）
     */
    async priceDifferenceDeeSettingController(args) {
        await this.jieSuanRcjStageService.priceDifferenceDeeSetting(args);
        return ResponseData.success(true);
    }

    /**
     * 工程项目---批量应用价差取费设置
     * @param constructId
     * @param map  {"key1":"value1","key2":"value2","key3":"value3"}  key是类型：1（人工）  2（材料） 3（机械），  value：具体指（计税金）
     */
    async constructPriceDifferenceDeeSettingController(args) {
        await this.jieSuanRcjStageService.constructPriceDifferenceDeeSetting(args);
        return ResponseData.success(true);
    }

    //获取取费设置
    getFeeSet(args) {

        return ResponseData.success(this.jieSuanRcjStageService.getFeeSet(args));
    }


    /**
     * 四种结算人材机调整法
     * @param constructId
     * @param singleId
     * @param unitId
     * @param clType 材料分类   1 人工  2 材料  3 机械
     * @param methodType  价差调整方式  1 2  3  4
     * @param
     */
    async priceDifferenceAdjustmentMethodController(args) {
        await this.jieSuanRcjStageService.priceDifferenceAdjustmentMethod(args);
        //await this.service.jieSuanProject.jieSuanUnitCostCodePriceService.countCostCodePrice(args)
        return ResponseData.success(true);
    }

    /**
     * 四种结算人材机调整法---工程项目
     * @param constructId
     * @param clType 材料分类   1 人工  2 材料  3 机械
     * @param methodType  价差调整方式  1 2  3  4
     * @param
     */
    async constructPriceDifferenceAdjustmentMethodController(args) {
        await this.jieSuanRcjStageService.priceDifferenceAdjustmentMethod(args);
        return ResponseData.success(true);
    }


    /**
     * 人材机分期查看
     * @param args
     */
    async getRcjStageList(args){
        let rcjStageList1 = await this.jieSuanRcjStageService.getRcjStageList1(args);
        return ResponseData.success(rcjStageList1);
    }

    /**
     * 人材机分期查看导出Excel
     * @param args
     */
    async exportRcjStageListExcel(args) {
        await this.jieSuanRcjStageService.exportRcjStageListExcel(args);
        return ResponseData.success(true);
    }


    /**
     * 判断 结算项目中是否有分期单位工程
     * @returns {Promise<ResponseData>}
     */
    async jieSuanConstructProjectFq(args){

        let result =await this.jieSuanRcjStageService.jieSuanConstructProjectFq(args);
        return ResponseData.success(result);
    }

    /**
     * 人材机参与调差
     * @param args
     */
    rcjParticipateInAdjustment(args){
        this.jieSuanRcjStageService.rcjParticipateInAdjustment(args);
        this.service.jieSuanProject.jieSuanUnitCostCodePriceService.countCostCodePrice(args)
        return ResponseData.success(true);
    }

    /**
     * 取消人材机参与调差
     * @param args
     * @returns {ResponseData}
     */
    cancelRcjParticipateInAdjustment(args){
        this.jieSuanRcjStageService.cancelRcjParticipateInAdjustment(args);
        this.service.jieSuanProject.jieSuanUnitCostCodePriceService.countCostCodePrice(args)
        return ResponseData.success(true);
    }

    /**
     * 获取单位的分期数
     * @param args
     */
    getUnitStage(args){
        return ResponseData.success(this.jieSuanRcjStageService.getUnitStage(args));
    }



    /**
     * 批量调整结算除税系数
     * @param constructId
     * @param singleId
     * @param unitId
     * @param tcType  调差类型  1 人工费  2 材料费 3 机械费  4 暂估材料调差
     * @param list 所选调差数据集合
     */
    async batchUpdateTaxRemovalController(args){
        //修改数据
        await this.jieSuanRcjStageService.batchUpdateTaxRemoval(args);
        return ResponseData.success(true);

    }

    /**
     * 批量调整结算除税系数---工程项目
     * @param constructId
     * @param tcType  调差类型  1 人工费  2 材料费 3 机械费  4 暂估材料调差
     * @param list 所选调差数据集合
     */
    async constructBatchUpdateTaxRemovalController(args){
        //修改数据
        await this.jieSuanRcjStageService.constructBatchUpdateTaxRemoval(args);
        return ResponseData.success(true);

    }

    //查询单位
    getUnitProjectById(args) {
        const result = this.jieSuanRcjStageService.getUnitProjectById(args);
        return ResponseData.success(result.isDifference);
    }
    //备份
    proJectBackups(args) {
        this.jieSuanRcjStageService.proJectBackups(args);
    }

    //统一应用取消按钮
    cancel(args) {
        this.jieSuanRcjStageService.cancel(args);
    }

    getIsStage(args){
        const result = this.jieSuanRcjStageService.getIsStage(args);
        return ResponseData.success(result);
    }



    /**
     * 合同外清单工程量修改
     * @param args
     * @return {*}
     */
    htwQdQuantity(args) {
        let {constructId,singleId,unitId,qd} = args;
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        let itemBillProjects = unit.itemBillProjects.find(k =>k.kind == "03" && k.sequenceNbr == qd.sequenceNbr);
        if (ObjectUtils.isNotEmpty(itemBillProjects)){
            return  this.service.jieSuanProject.jieSuanRcjStageService.htwQdQuantity(itemBillProjects);
        }
        let measureProjectTables = unit.measureProjectTables.find(k =>k.kind == "03" && k.sequenceNbr == qd.sequenceNbr);
        if (ObjectUtils.isNotEmpty(measureProjectTables)){
            return  this.service.jieSuanProject.jieSuanRcjStageService.htwQdQuantity(measureProjectTables);
        }

    }

}

JieSuanRcjStageController.toString = () => '[class JieSuanRcjStageController]';
module.exports = JieSuanRcjStageController;
