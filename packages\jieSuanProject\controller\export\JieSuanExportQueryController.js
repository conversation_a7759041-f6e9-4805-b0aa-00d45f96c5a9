const {Controller} = require("../../../../core");
const {ResponseData} = require("../../../../electron/utils/ResponseData");
const fs = require('fs');
/**
 * 报表查询controller
 */
class JieSuanExportQueryController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 点击报表名称  展示对应的报表页面
     * @param arg
     * @returns {Promise<*>}
     */
    async jieSuanShowSheetStyle (args) {

        const {itemLevel,lanMuName,sheetName,constructObj} = args
        // //首先判断计税方式
        const result = await this.service.jieSuanProject.jieSuanExportQueryService.jieSuanShowSheetStyle(itemLevel,lanMuName,sheetName,constructObj);
        return ResponseData.success(result);
    }

    /**
     * 点击左侧工程项目层级树  展示对应的报表结构目录
    * @param arg
        * @returns {Promise<*>}
    */
        async jieSuanShowExportHeadLineColl (args) {
        //首先判断计税方式
        const {itemLevel,constructObj} = args;
        //首先判断计税方式
        const result = await this.service.jieSuanProject.jieSuanExportQueryService.jieSuanShowExportHeadLine(itemLevel,constructObj);
        return ResponseData.success(result);
    }

    // //导出excel返回某一栏目下的数据
    async queryLanMuData(args) {
        let {lanMuName,constructId} = args;
        let queryLanMuData =await this.service.jieSuanProject.jieSuanExportQueryService.queryLanMuData(constructId,lanMuName);
        return ResponseData.success(queryLanMuData);
    }
    //
    // //导出勾选的excel zip 包
    async exportExcelZip(args) {
        let {lanMuName,params} = args;
        let result = await this.service.jieSuanProject.jieSuanExportQueryService.exportExcel(params,lanMuName);
        return ResponseData.success(result);
    }
    //
    // //导出勾选的pdf
    async exportPdfFile(args) {
        let {lanMuName,params} = args;
        let result = await this.service.jieSuanProject.pdfService.excelToPdf(lanMuName,params);
        return ResponseData.success(result);
    }
    //
    // /**
    //  * 返回一个sheet表的格式
    //  * @param arg
    //  * @returns {Promise<*>}
    //  */
    // async showSheetStyleSample () {
    //     const result = await this.service.jieSuanProject.exportQueryService.showSheetStyleSample();
    //     return ResponseData.success(result);
    // }


}

JieSuanExportQueryController.toString = () => '[class JieSuanExportQueryController]';
module.exports = JieSuanExportQueryController;
