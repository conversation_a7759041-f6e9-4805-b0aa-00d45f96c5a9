'use strict';


const { PricingFileFindUtils } = require("../../../electron/utils/PricingFileFindUtils");
const ConstructionMeasureTypeConstant = require("../../../electron/enum/ConstructionMeasureTypeConstant");
const { ObjectUtils } = require("../../../electron/utils/ObjectUtils");
const InsertStrategy = require("../../../electron/main_editor/insert/insertStrategy");
const AzCostMathService = require("../../../electron/service/azCostMathService");
const JieSuanConstructCostMathService = require("./jieSuanConstructCostMathService");

/**
 * 安装费用记取
 * @class
 */
class JieSuanAZCostMathService extends AzCostMathService {
    constructor(ctx) {
        super(ctx);
        this.jieSuanConstructCostMathService =
            ObjectUtils.isNotEmpty(this.service.jieSuanConstructCostMathService)?this.service.jieSuanConstructCostMathService :new JieSuanConstructCostMathService(ctx);
    }

    /**
     * 清单下挂定额,下挂人材机数据
     */
    async confirmQdAddCostDe(unit, qd, costDe, type) {
        let { constructId, spId, sequenceNbr } = unit;
        //插入的定额数据
        let de = null;
        //单位标题数据
        let titleData = null;
        //指定分部分项清单 || 对应分部分项
        if (type === ConstructionMeasureTypeConstant.FBFX || type === ConstructionMeasureTypeConstant.DYFBFX) {
            // 检查要添加的定额所属清单是不是临时删除的  如果是临时删除的就需要恢复这个清单和清单下的所有数据
            this.service.itemBillProjectOptionService.cleanQdDelTempStatus({
                constructId: constructId,
                singleId: spId,
                unitId: sequenceNbr,
                id: qd.sequenceNbr,
                modelType: 1,
                tempDeleteFlag: false
            });
            //给清单下新增定额数据
            let insertStrategy = new InsertStrategy({
                constructId, singleId: spId, unitId: sequenceNbr, pageType: 'fbfx'
            });
            de = await insertStrategy.execute({
                pointLine: qd,
                newLine: costDe,
                indexId: costDe.standardId,
                libraryCode: costDe.libraryCode,
                option: 'insert',
                skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
                overwriteColumn: false
            });
            titleData = PricingFileFindUtils.getUnit(constructId, spId, sequenceNbr).itemBillProjects;
        }
        //指定措施清单
        if (type === ConstructionMeasureTypeConstant.ZJCS) {
            // 检查要添加的定额所属清单是不是临时删除的  如果是临时删除的就需要恢复这个清单和清单下的所有数据
            this.service.itemBillProjectOptionService.cleanQdDelTempStatus({
                constructId: constructId,
                singleId: spId,
                unitId: sequenceNbr,
                id: qd.sequenceNbr,
                modelType: 2,
                tempDeleteFlag: false
            });
            let insertStrategy = new InsertStrategy({
                constructId, singleId: spId, unitId: sequenceNbr, pageType: 'csxm'
            });
            de = await insertStrategy.execute({
                pointLine: qd,
                newLine: costDe,
                indexId: costDe.standardId,
                libraryCode: costDe.libraryCode,
                option: 'insert',
                skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
                overwriteColumn: false
            });
            titleData = PricingFileFindUtils.getUnit(constructId, spId, sequenceNbr).measureProjectTables;
        }
        //初始化人材机数据
        await this.jieSuanConstructCostMathService.initRcj(unit, de)
        return {
            de: de, titleData: titleData
        };
    }

}

JieSuanAZCostMathService.toString = () => '[class JieSuanAZCostMathService]';
module.exports = JieSuanAZCostMathService;