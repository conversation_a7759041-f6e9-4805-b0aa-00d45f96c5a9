// const {ResponseData} = require("../../../electron/utils/ResponseData");
// const {Controller} = require("../../../core");
// const JieSuanOtherProjectDayWorkService = require("../../../packages/jieSuanProject/service/jieSuanOtherProjectDayWorkService");
// class JieSuanOtherProjectDayWorkController extends Controller{
//
//
//     /**
//      * 构造函数
//      * @param ctx
//      */
//     constructor(ctx) {
//         super(ctx);
//         // 实例化服务类并注入 ctx 参数
//         this.jieSuanOtherProjectDayWorkService = new JieSuanOtherProjectDayWorkService(ctx);
//     }
//
//
//     /**
//      * 获取默认计日工数据
//      */
//     getDefaultOtherProjectDayWork(){
//         const res = this.jieSuanOtherProjectDayWorkService.getDefaultOtherProjectDayWork();
//         return ResponseData.success(res);
//     }
//
//     getOtherProjectDayWork(){
//         const res = this.jieSuanOtherProjectDayWorkService.getOtherProjectDayWork();
//         return ResponseData.success(res);
//     }
//
//     /**
//      * 计日工操作
//      * @param arg
//      */
//     async otherProjectDayWork(arg){
//         const res = await this.jieSuanOtherProjectDayWorkService.otherProjectDayWork(arg);
//
//         if (arg.operateType !==1){
//
//             await this.service.management.sycnTrigger("unitDeChange");
//             await this.service.management.trigger("itemChange");
//         }
//         return ResponseData.success(res);
//     }
//
//
// }
//
// JieSuanOtherProjectDayWorkController.toString = () => '[class JieSuanOtherProjectDayWorkController]';
// module.exports = JieSuanOtherProjectDayWorkController;