const {Controller} = require("../../core");
const {ResponseData} = require("../utils/ResponseData");

class GlobalConfigurationImplementationController extends  Controller{
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 根据配置修改需要修改的数据
     * @param args
     * @returns {Promise<ResponseData>}
     */
    // async updateGlobalConfiguration(args) {
    //     const res = await this.service.globalConfigurationImplementationService.updateGlobalConfiguration(args);
    //     return ResponseData.success(res);
    // }
}
GlobalConfigurationImplementationController.toString = () => '[class ConfigurationController]';
module.exports = GlobalConfigurationImplementationController;