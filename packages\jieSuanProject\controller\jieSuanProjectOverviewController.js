const { Controller } = require('../../../core');
const { ResponseData } = require('../../../common/ResponseData');
class JieSuanProjectOverviewController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 查询工程特征和基本信息
     * @param args
     * @returns {Promise<ResponseData>}
     */
    getProjectOverview(args){
        const res = this.service.jieSuanProject.jieSuanProjectOverviewService.getProjectOverviewList(args);
        return ResponseData.success(res);
    }

    /**
     * 保存工程特征
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async saveBasicEngineeringInfoOrEngineeringFeature(args){
        const res = await this.service.jieSuanProject.jieSuanProjectOverviewService.saveList(args);
        return ResponseData.success(res);
    }


}

JieSuanProjectOverviewController.toString = () => '[class JieSuanProjectOverviewController]';
module.exports = JieSuanProjectOverviewController;
