<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2023-08-22 10:44:38
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-04-30 15:51:18
-->
<template>
  <div
    class="login"
    v-if="show"
  >
    <div class="login-banner">
      <a-carousel
        autoplay
        dotsClass="dots"
      >
        <div>
          <img
            class="img"
            :src="getUrl('newCsProject/banner-1.png')"
            alt=""
          />
        </div>
        <div>
          <img
            class="img"
            :src="getUrl('newCsProject/banner-1.png')"
            alt=""
          />
        </div>
        <div>
          <img
            class="img"
            :src="getUrl('newCsProject/banner-1.png')"
            alt=""
          />
        </div>
      </a-carousel>
    </div>
    <div class="login-form">
      <div class="form">
        <h3>{{ loginTypeList.find(item => item.type === loginType).name }}</h3>
        <div
          class="form-content"
          v-if="loginType === 'login'"
        >
          <a-form
            ref="LoginForm"
            :model="formState"
            name="basic"
            :label-col="{ span: 0 }"
            :wrapper-col="{ span: 24 }"
            autocomplete="off"
            @finish="onFinish"
            @finishFailed="onFinishFailed"
            validateTrigger="blur"
          >
            <a-tabs
              v-model:activeKey="activeKey"
              @change="changeActiveKey"
            >
              <a-tab-pane
                key="1"
                tab="账号登录"
              >
                <a-form-item
                  name="username"
                  :rules="[{ required: true, message: '请输入账号' }]"
                >
                  <a-dropdown :trigger="['click']">
                    <a-input
                      v-model:value="formState.username"
                      placeholder="请输入账号"
                      ref="userNameIpt"
                    />
                    <template
                      #overlay
                      v-if="passwordList?.length > 0"
                    >
                      <a-menu>
                        <a-menu-item v-for="(item, index) in passwordList">
                          <p
                            class="passList"
                            @click="setPass(item)"
                          >
                            <span class="passList-name">{{
                              item.username
                            }}</span>
                            <span>*******</span>
                            <icon-font
                              class="passList-del"
                              type="icon-guanbi"
                              @click="delUser(item, index)"
                            />
                          </p>
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-form-item>

                <a-form-item
                  name="password"
                  v-if="activeKey === '1'"
                  :rules="[{ required: true, message: '请输入密码' }]"
                >
                  <a-input-password
                    v-model:value="formState.password"
                    placeholder="请输入密码"
                  />
                </a-form-item>
                <a-form-item
                  name="captcha"
                  :rules="[{ required: true, message: '请输入验证码' }]"
                >
                  <a-input
                    v-model:value="formState.captcha"
                    placeholder="请输入验证码"
                  >
                    <template #addonAfter>
                      <img
                        @click="captchaCode"
                        :src="imgSrc"
                        style="height: 30px"
                      />
                    </template>
                  </a-input>
                </a-form-item>

                <div style="
                    display: flex;
                    justify-content: space-between;
                    padding-bottom: 10px;
                  ">
                  <a-checkbox v-model:checked="formState.rememberPas">记住密码</a-checkbox>
                  <a-checkbox v-model:checked="formState.autoLogin">自动登录</a-checkbox>
                  <!-- <a-button class="btnlink" @click="checkType('changePassword')" type="link">忘记密码</a-button> -->
                </div>
              </a-tab-pane>
              <a-tab-pane
                key="2"
                tab="手机号登录"
                force-render
              >
                <a-form-item
                  name="username"
                  v-if="activeKey === '2'"
                  :rules="[
                    { required: true, message: '请输入手机号' },
                    {
                      pattern: /^1\d{10}$/,
                      message: '手机号码格式有误!',
                    },
                  ]"
                >
                  <a-input
                    v-model:value="formState.username"
                    placeholder="请输入手机号"
                  />
                </a-form-item>
                <a-form-item
                  name="captcha"
                  :rules="[{ required: true, message: '请输入验证码' }]"
                >
                  <a-input
                    v-model:value="formState.captcha"
                    placeholder="请输入验证码"
                  >
                    <template #addonAfter>
                      <a-button
                        class="btnlink"
                        @click="getSms()"
                        :disabled="CountdownData.isCountingDown"
                        type="link"
                      >{{ CountdownData.buttonText }}</a-button>
                    </template>
                  </a-input>
                </a-form-item>
              </a-tab-pane>
            </a-tabs>
            <a-form-item :wrapper-col="{ offset: 0, span: 24 }">
              <a-button
                type="primary"
                html-type="submit"
                block
              >登录</a-button>
            </a-form-item>
            <div style="
                display: flex;
                justify-content: space-between;
                padding-bottom: 10px;
                margin-top: -15px;
                color: #1890ff;
              ">
              <span>
                <a-button
                  class="btnlink"
                  @click="checkType('register')"
                  type="link"
                >免费注册</a-button><span style="margin: 0 5px">|</span><a-button
                  class="btnlink"
                  @click="checkType('changePassword')"
                  type="link"
                >忘记密码</a-button>
              </span>
              <!-- <a-button
                class="btnlink"
                @click="checkType('offlineLogin')"
                type="link"
                >离线登录
                <a-tooltip placement="top">
                  <template #title>
                    <span>选择离线登录时，系统会自动检测本地加密锁驱动，如未检测到加密锁时，软件无法启用；当检测到加密锁时，用户可不用登录账号即可使用计价软件</span>
                  </template>
                  <icon-font
                    class="btnlink tishi"
                    type="icon-tishi2"
                  />
                </a-tooltip>
              </a-button> -->
            </div>
          </a-form>
        </div>
        <changePassword
          @checkType="checkType"
          v-if="loginType === 'changePassword'"
        />
        <register
          @checkType="checkType"
          v-if="loginType === 'register'"
        />
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { getUrl } from '@/utils/index';
import changePassword from './change-password.vue';
import register from './register.vue';
import { v4 } from 'uuid';
import Countdown, { CountdownData } from './Countdown';
import csProject from '../../../api/csProject';
import {
  getCaptcha,
  login,
  getEnterprise,
  checkNormalAgency,
  userInfo,
  checkMobile,
  getSmsCode,
  registerInfo,
  accountPermission,
  checkUserIsAvailable,
} from '@/api/auth';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
const store = projectDetailStore();
import { proModelStore } from '@/store/proModel.js';
const proStore = proModelStore();
const LoginForm = ref();
const props = defineProps({
  visible: {
    type: Boolean,
  },
});

const emit = defineEmits(['visible', 'offlineLoginOprate', 'loginSuccess']);

const show = computed({
  get: () => props.visible,
  set: val => {
    store.SET_LOGIN_VISIBLE(val);
  },
});
let formState = ref({
  username: '',
  password: '',
  agree: true,
  rememberPas: true,
  autoLogin: false,
});
const changeActiveKey = () => {
  LoginForm.value.resetFields();
  LoginForm.value.clearValidate();
  // if (activeKey.value === '2') {
  //   formState.username = '***********';
  //   formState.captcha = '123123';
  // } else {
  //   formState.username = 'wxf123';
  //   formState.password = 'wxf123@123';
  // }
};
const loginType = ref('login');
const loginTypeList = ref([
  {
    type: 'login',
    name: '云算房账号登录',
  },
  {
    type: 'changePassword',
    name: '云算房忘记密码',
  },
  {
    type: 'register',
    name: '注册云算房账号',
  },
]);
let passwordList = ref([]);
onMounted(() => {
  LoginForm.value.resetFields();
  LoginForm.value.clearValidate();
  captchaCode();
  passwordList.value = JSON.parse(localStorage.getItem('savedPasList'));
  setDefaultInfo();
});
const setDefaultInfo = () => {
  //如果设置了记住密码，登陆框打开默认展示账号密码
  if (localStorage.getItem('savedPasList')) {
    let list = JSON.parse(localStorage.getItem('savedPasList')) || [];
    if (list?.length > 0 && list[0].rememberPas && activeKey.value === '1') {
      formState.value.username = list[0].username;
      formState.value.password = list[0].password;
      // formState.value.rememberPas = list[0].rememberPas;
      // formState.value.autoLogin = list[0].autoLogin;
    }
  }
};
const delUser = (item, idx) => {
  passwordList.value.splice(idx, 1);
  localStorage.setItem('savedPasList', JSON.stringify(passwordList.value));
};
let userNameIpt = ref(); //聚焦失焦可重新进行验证
const setPass = item => {
  userNameIpt.value.focus();
  formState.value.username = item.username;
  formState.value.password = item.password;
  userNameIpt.value.blur();
};
let imgSrc = ref('');
const toURL = data => {
  const blob = new Blob([data]);
  return window.URL.createObjectURL(blob);
};
let uid = ref();
const captchaCode = () => {
  //获取登录图片验证码
  uid.value = v4();
  getCaptcha(uid.value).then(res => {
    console.log('验证码', res);
    imgSrc.value = toURL(res);
  });
};

const getSms = async () => {
  //获取手机验证码
  let SMS_CODE_TYPE = '';
  switch (loginType.value) {
    case 'login':
      SMS_CODE_TYPE = 1;
      break;
    case 'register':
      SMS_CODE_TYPE = 2;
      break;
    case 'changePassword':
      SMS_CODE_TYPE = 3;
  }
  checkMobile(formState.value.username, SMS_CODE_TYPE).then(res => {
    if (res.status === 200) {
      getSmsCode(formState.value.username, SMS_CODE_TYPE).then(res => {
        if (res.status === 200) {
          message.success('发送成功');
          Countdown.startCountdown();
        } else {
          message.error(res.message);
        }
      });
    } else {
      message.error(res.message);
    }
  });
};
const checkType = type => {
  if (type === 'offlineLogin') {
    const obj = {
      loginType: 'offlineLogin',
      userList: [],
    };
    localStorage.setItem('loginType', 'offlineLogin');
    store.SET_IS_USER_INFO_LIST(obj);
    store.SET_IS_LOGIN_USER_INFO({ loginType: 'offlineLogin' }); //存储当前登录的用户信息
    store.SET_LOGIN_PHONE(null);
    localStorage.removeItem('token');
    //离线登录后更新最近打开项目列表
    proStore.SET_Refresh(!proStore.isRefresh);
    emit('offlineLoginOprate');
    store.SET_LOGIN_VISIBLE(false); //离线登录以一种另外的身份登录
  } else {
    loginType.value = type;
  }
  // console.log(loginType.value, 'loginType.value');
  // Countdown.reset()
};

const checkAccountPermission = async () => {
  const availableRes = await checkUserIsAvailable();
  // console.log('availableRes', availableRes);
  if (availableRes.status == 200) {
    store.SET_DAYS_REMAINING(availableRes.result);
  }
};

const onFinish = values => {
  let payload = {};
  if (loginType.value === 'login') console.log('Success:', values, loginType.value);
  switch (loginType.value) {
    case 'login':
      localStorage.setItem('loginType', 'inlineLogin');
      if (activeKey.value === '2') {
        //手机号登录
        payload = {
          mobile: values.username,
          sms_code: values.captcha,
          loginChannel: 'PLATFORM_DEFAULT',
          grant_type: 'sms',
          scope: 'all',
        };
      } else {
        payload = {
          username: values.username,
          password: values.password,
          captcha: values.captcha,
          uuid: uid.value,
          loginChannel: 'PLATFORM_DEFAULT',
          scope: 'all',
          grant_type: 'password',
        };
      }
      login(payload).then(async res => {
        console.log(res, '登陆', formState.value);
        if (res.hasOwnProperty('access_token')) {
          if (store.winId !== 1) {
            let mainId = await $ipc.invoke(ipcApiRoute.getWCid, 'main');
            $ipc.sendTo(
              mainId,
              specialIpcRoute.window2ToWindow1,
              `updateLogin,${JSON.stringify({
                value: res,
              })}`
            );
          }
          if (activeKey.value === '2') {
            store.SET_LOGIN_PHONE(payload.mobile);
          }
          const token = res.token_type + ' ' + res.access_token;
          localStorage.setItem('token', token);
          //定时器调用检查账号权限接口
          await checkAccountPermission();
          emit('loginSuccess');
          setInterval(() => {
            checkAccountPermission();
          }, 5000);
          // const accountPermissionRes = await accountPermission();
          // let hasValidResource;
          // if (accountPermissionRes.status == 200) {
          //   if (
          //     Array.isArray(accountPermissionRes.result) &&
          //     accountPermissionRes.result.length > 0
          //   ) {
          //     const currentTime = Date.now();
          //     // 资源未过期且是计价软甲
          //     hasValidResource = accountPermissionRes.result.some(item => {
          //       const isJJRJ = item.key?.find(k => k.type == 'YSF-JJRJ')
          //       if(isJJRJ) {
          //         if (!item.end_time) return true;
          //         const et = Number(item.end_time);
          //         return et > currentTime;
          //       }
          //     });
          //   } else {
          //     hasValidResource = false;
          //   }
          //   store.SET_ACCOUNT_HAS_VALID_RESOURCE(hasValidResource);
          //   if (!hasValidResource) {
          //     store.SET_LOGIN_VISIBLE(false);
          //   }
          // } else {
          //   store.SET_LOGIN_VISIBLE(false);
          //   store.SET_ACCOUNT_HAS_VALID_RESOURCE(false);
          // }
          // // 锁中无有效资源，账号中也无有效资源
          // if(!hasValidResource && !store.lockHasValidResource) {
          //   store.SET_OVERALL_PERMISSION(false);
          // }
          savePasList(); //登录保存密码信息
          getUserInfo(res); //获取用户身份信息
        } else {
          formState.value.captcha = null;
          captchaCode();
          message.error(res.message);
        }
      });
      break;
    case 'register':
      break;
    case 'camel':
    default:
  }
};
const savePasList = () => {
  //记住密码，存储列表---还需要这是记住密码个数不超过10

  let list = localStorage.getItem('savedPasList');
  if ((!formState.value.rememberPas && !formState.value.autoLogin) || activeKey.value === '2') {
    list = JSON.parse(list);
    if (list?.length > 0) {
      list[0].autoLogin = false;
      localStorage.setItem('savedPasList', JSON.stringify(list));
    }
    return;
  }
  let tar = {
    username: formState.value.username,
    password: formState.value.password,
    autoLogin: formState.value.autoLogin, //自动登录
    rememberPas: formState.value.rememberPas, //记住密码
    loginTime: Date.parse(new Date()) / 1000, //设置登录时间限制
  };
  if (list) {
    //获取storage存储的密码列表信息
    list = JSON.parse(list);
    if (list?.length > 10) {
      //密码列表不超过10
      message.error('记住密码个数超出限制');
      return;
    }
    if (list?.find(a => a.username === formState.value.username)) {
      //存储列表有这次登录信息，将该信息移动至最前面
      let idx = list.findIndex(a => a.username === formState.value.username);
      list.splice(idx, 1);
    }
    list.unshift(tar);
  } else {
    list = [tar];
  }
  localStorage.setItem('savedPasList', JSON.stringify(list));
  console.log(localStorage.getItem('savedPasList'));
};
const getUserInfo = async tokens => {
  const token = tokens.token_type + ' ' + tokens.access_token;
  const expires = +(tokens.expires_in / 86400).toFixed(2);
  const isNormalAgency = await checkNormalAgency(token); //登录后检查是否存在普通用户身份，不存在则添加普通身份
  let userDetatil = {};
  if (isNormalAgency.status === 200) {
    userDetatil = await userInfo(token); //设置的默认身份-获取到登录用户sequenceNbr
    console.log(userDetatil, '用户信息userDetatil');
    if (userDetatil.status === 200 && userDetatil.result?.mobile) {
      store.SET_LOGIN_PHONE(userDetatil.result?.mobile);
    }
    // emit(
    //   'getLastUser',
    //   userDetatil && userDetatil.result && userDetatil.result.sequenceNbr
    // );
    await getLastUser(userDetatil && userDetatil.result && userDetatil.result.sequenceNbr);
    let userInfoList = await getEnterprise(token); //获取所有身份列表
    getUserDetail(userInfoList.result, userDetatil);
    store.SET_LOGIN_VISIBLE(false);
  } else {
    message.error(isNormalAgency.message);
    return;
  }
};
const getLastUser = async sequenceNbr => {
  await csProject
    .getLastIdInformation({
      sequenceNbr: sequenceNbr,
    })
    .then(res => {
      console.log('获取上次登录身份返回信息', res);
      if (res.status === 200 && res.result) {
        store.SET_LAST_INFO({ agencyCode: res.result });
      } else {
        store.SET_LAST_INFO({ agencyCode: null });
      }
    });
};
const getUserDetail = (data, user) => {
  data && delete data.delAgency;
  const keys = Object.keys(data);
  const values = Object.values(data);
  let infoList = [];
  for (let i = 0; i < keys.length; i++) {
    values[i] &&
      values[i].map(item => {
        let type = keys[i];
        item.type = type;
        item.sequenceNbr = user && user.result && user.result.sequenceNbr;
        item.openId = user && user.result && user.result.openId;

        if (type === 'normalAgency') {
          item.showName = item.userName;
          item.imgSrc =
            'https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/YYFUPT/Not%20certified.png';
        } else if (type === 'personalAgency') {
          item.showName = item.realName;
          item.imgSrc =
            'https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/YYFUPT/attestation.png';
        } else if (type === 'enterpriseAgency') {
          item.showName = item.enterpriseName;
          item.imgSrc =
            'https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/YYFUPT/attestation.png';
        }
        !item.logo ? (item.logo = item.imgSrc) : '';
      });
    infoList.push(...values[i]);
  }
  infoList = infoList.filter(item => Number(item.extend2) === 0);
  // infoList.forEach((item, index) => (item.sortNo = index));
  const obj = {
    userList: infoList,
    loginType: loginType.value,
  };
  store.SET_IS_USER_INFO_LIST(obj);
  console.log('getUserDetail', obj);
  store.SET_LOGIN_VISIBLE(false);
};
const onFinishFailed = errorInfo => {
  console.log('Failed:', errorInfo);
};
const activeKey = ref('1');
</script>
<style lang="scss" scoped>
.login {
  width: 1300px;
  height: 530px;
  display: flex;
  flex-direction: row;
  &-banner {
    width: 859px;
    height: 530px;
    .img {
      width: 859px;
      height: 530px;
    }
  }
  &-form {
    padding: 45px 68px;
    width: 450px;
    h3 {
      font-size: 24px;
      line-height: 30px;
      color: #2d2d2d;
      text-align: center;
      margin-bottom: 24px;
    }
  }
}
.form {
  &-content {
    :deep(.ant-tabs .ant-tabs-nav .ant-tabs-nav-operations) {
      display: none;
      .ant-tabs-nav-more {
        display: none;
      }
    }
    :deep(.ant-tabs-tab) {
      width: 50%;
    }
    :deep(.ant-tabs-nav-list) {
      width: 100%;
    }
    :deep(.ant-tabs-tab-btn) {
      margin: 0 auto;
    }
    :deep(.ant-tabs-tab-active) {
    }
  }
}

.dots {
  margin-right: -80%;
}
.btnlink {
  padding: 0;
  height: auto;
  &.tishi {
    margin-left: 4px;
  }
}
.passList {
  width: 100%;
  margin: 0;
  // padding-bottom: 5px;
  // border-bottom: 1px solid #cccccc;
  &-name {
    width: 90px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &-del {
    position: absolute;
    top: 10px;
    right: 20px;
  }
}
@media (max-width: 1365px) {
  .login {
    &-banner {
      width: 0;
      display: none;
    }
  }
}
</style>
