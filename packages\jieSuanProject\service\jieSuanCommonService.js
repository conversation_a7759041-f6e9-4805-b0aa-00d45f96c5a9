const JieSuanMenuBarEnum = require("../enum/JieSuanMenuBarEnum");
const {ResponseData} = require("../../../common/ResponseData");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {Service} = require("../../../core");
const { PricingFileFindUtils } = require('../../../electron/utils/PricingFileFindUtils');


class JieSuanCommonService extends Service{


    constructor(ctx) {
        super(ctx);
        let {baseFeeFileService,projectOverviewService,itemBillProjectService,otherProjectService} = this.service;
        let {jieSuanRcjProcess} = this.service.jieSuanProject;


        this.map = new Map();
        this.map.set(JieSuanMenuBarEnum.PROJECT_OVERVIEW,{obj:projectOverviewService,fun:projectOverviewService.getProjectOverviewCollectionData});//項目概況-项目
        this.map.set(JieSuanMenuBarEnum.UNIT_PROJECT_OVERVIEW,{obj:projectOverviewService,fun:projectOverviewService.getProjectOverviewCollectionData});//項目概況-单位
        this.map.set(JieSuanMenuBarEnum.FEE_COLLECTION_FORM,{obj:baseFeeFileService,fun:baseFeeFileService.getFeeCollectionTreeList});//取费文件列表-工程项目
        this.map.set(JieSuanMenuBarEnum.UNIT_FEE_COLLECTION_FORM,{obj:baseFeeFileService,fun:baseFeeFileService.getFeeCollectionTreeList});//取费文件列表-单位
        this.map.set(JieSuanMenuBarEnum.UNIT_ITEM_BILL,{obj:itemBillProjectService,fun:itemBillProjectService.queryUnitBranchTree});//分部分項-左側目錄書
        this.map.set(JieSuanMenuBarEnum.UNIT_OTHER_PROJECT,{obj:otherProjectService,fun:otherProjectService.menuData});//分部分項-左側目錄書

        this.map.set(JieSuanMenuBarEnum.UNIT_RCJ_SUMMARY,{obj:jieSuanRcjProcess,fun:jieSuanRcjProcess.getUnitRcjType});//人材机调整左侧树
        this.map.set(JieSuanMenuBarEnum.RCJ_SUMMARY,{obj:jieSuanRcjProcess,fun:jieSuanRcjProcess.getUnitRcjType});//人材机调整左侧树
        this.map.set(JieSuanMenuBarEnum.SINGLE_RCJ_SUMMARY,{obj:jieSuanRcjProcess,fun:jieSuanRcjProcess.getUnitRcjType});//人材机调整左侧树
    }


    /**
     * 获取菜单栏数据
     * @param args
     * @return {ResponseData}
     */
    getMenuList(args){
        let type = args.type;
        let levelType = args.levelType;
        let values = Object.values(JieSuanMenuBarEnum);
        let result = values.filter(item =>item.type==type && item.levelType==levelType);
        return  result;
    }

    /**
     * 获取菜单栏数据
     * @param args
     * @return {Promise<*|ResponseData>}
     */
    async getMenuData(args){
        let type = args.type;
        let levelType = args.levelType;
        let code = args.code;
        let menuBarEnum = this.getMenuBarEnum(type,levelType,code);
        if (ObjectUtil.isEmpty(menuBarEnum)){
            return ResponseData.fail("参数错误");
        }
        let objDefinition = this.map.get(menuBarEnum);
        if (ObjectUtil.isEmpty(objDefinition)){
            return ;
        }
        let result =await objDefinition.fun.call(objDefinition.obj,args);
        if(code=="7"){
            let unit = PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
            if(unit.originalFlag){
               let newResult = result.treeModel.childTreeModel.filter(qt=>qt.bdName!="签证与索赔计价表");
               result.treeModel.childTreeModel=newResult;
            }
        }
        return result;
    }


    /**
     * 获取枚举值
     * @param type
     * @param levelType
     * @param code
     * @return {object | string | bigint | number | boolean | symbol}
     */
    getMenuBarEnum(type,levelType,code){
        for (let menuBarEnumKey in JieSuanMenuBarEnum) {
            let Bartype = JieSuanMenuBarEnum[menuBarEnumKey].type;
            let BarlevelType = JieSuanMenuBarEnum[menuBarEnumKey].levelType;
            let Barcode = JieSuanMenuBarEnum[menuBarEnumKey].code;
            if (Bartype == type && BarlevelType ==levelType && Barcode== code){
                return JieSuanMenuBarEnum[menuBarEnumKey];
            }
        }
    }



}


JieSuanCommonService.toString = () => '[class JieSuanCommonService]';
module.exports = JieSuanCommonService;