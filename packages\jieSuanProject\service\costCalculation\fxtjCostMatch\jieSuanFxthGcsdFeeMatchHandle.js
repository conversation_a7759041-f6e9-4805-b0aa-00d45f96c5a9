'use strict';

const {PricingFileFindUtils} = require("../../../../../electron/utils/PricingFileFindUtils");
const FxtjCostConstants = require("../../../../../electron/service/costCalculation/fxtjCostMatch/FxtjCostConstants");
const {JieSuanCostMatchUtil} = require("../jieSuanCostMatchUtil");
const EE = require("../../../../../core/ee");
const DePropertyTypeConstant = require("../../../../../electron/enum/DePropertyTypeConstant");
const { BaseDe2022, BaseDe } = require('../../../../../electron/model/BaseDe');
const {BaseDeFwxsCgRelation2022} = require("../../../../../electron/model/BaseDeFwxsCgRelation2022");
const {BaseDeFwxsCgRelation} = require("../../../../../electron/model/BaseDeFwxsCgRelation");
const FxthGcsdFeeMatchHandle = require("../../../../../electron/service/costCalculation/fxtjCostMatch/fxthGcsdFeeMatchHandle");

/**
 * 房修土建  工程水电费 记取
 */
class JieSuanFxthGcsdFeeMatchHandle extends FxthGcsdFeeMatchHandle {

  constructor() {
    super();
  }

  async customizeConfirmCostDe(unit, baseDeArr, args, qdNode) {
    const is22Unit = PricingFileFindUtils.is22Unit(unit);
    let result = {
      'costDeArr': [],
      'costDeBaseDe': {}
    };
    let costDeArr = [];
    let costDeBaseDe = {};
    const baseDeFwxsCgRelationList = await EE.app.appDataSource.manager.getRepository(is22Unit ? BaseDeFwxsCgRelation2022 : BaseDeFwxsCgRelation).find({
      where: { value: FxtjCostConstants.GCSDF }
    });

    if (is22Unit) {
      // 没有现浇和预拌的区分
      const baseDe = await EE.app.appDataSource.manager.getRepository(is22Unit ? BaseDe2022 : BaseDe).findOne({
        where: {
          libraryCode: baseDeFwxsCgRelationList[0].libraryCode,
          deCode: baseDeFwxsCgRelationList[0].deCode,
          deName: baseDeFwxsCgRelationList[0].deName
        }
      });
      let costDe = JieSuanCostMatchUtil.getFxtjCostDeByBaseDe(baseDe, DePropertyTypeConstant.FXTJ_GCSDF);
      const newCostDe = await JieSuanCostMatchUtil.confirmQdAddCostDe(unit, qdNode, costDe, args.constructionMeasureType);
      costDeArr.push(newCostDe);
      costDeBaseDe[newCostDe.sequenceNbr] = baseDeArr;
      result.costDeArr = costDeArr;
      result.costDeBaseDe = costDeBaseDe;
    } else {
      // 12定额需要区分现浇和预拌
      const baseDeGroupByXjybFlag = baseDeArr.reduce((result, current) => {
        if (!result[current.xjybFlag]) {
          result[current.xjybFlag] = [];
        }
        result[current.xjybFlag].push(current);
        return result;
      }, {});
      for (const xjybFlag of Object.keys(baseDeGroupByXjybFlag)) {
        // 【现浇】或者【预拌】 对应的基数定额
        const baseDeArr = baseDeGroupByXjybFlag[xjybFlag];
        const baseDeFwxs = baseDeFwxsCgRelationList.find(item => item.isXjYb === xjybFlag);
        const baseDe = await EE.app.appDataSource.manager.getRepository(is22Unit ? BaseDe2022 : BaseDe).findOne({
          where: {
            libraryCode: baseDeFwxs.libraryCode,
            deCode: baseDeFwxs.deCode,
            deName: baseDeFwxs.deName
          }
        });
        let costDe = JieSuanCostMatchUtil.getFxtjCostDeByBaseDe(baseDe, DePropertyTypeConstant.FXTJ_GCSDF);
        const newCostDe = await JieSuanCostMatchUtil.confirmQdAddCostDe(unit, qdNode, costDe, args.constructionMeasureType);
        costDeArr.push(newCostDe);
        costDeBaseDe[newCostDe.sequenceNbr] = baseDeArr;
      }
      result.costDeArr = costDeArr;
      result.costDeBaseDe = costDeBaseDe;
    }
    return result;
  }


}

JieSuanFxthGcsdFeeMatchHandle.toString = () => '[class JieSuanFxthGcsdFeeMatchHandle]';
module.exports = JieSuanFxthGcsdFeeMatchHandle;
