import { BaseModel} from './BaseModel';

/**
 * 项目/单位/工程特征
 * 基本信息
 */
export class JieSuanProjectOverview extends BaseModel{

    
    public name :string;
    public context :string;
    public remark :string; //内容存在这个字段
    public type :number;
    public lockFlag :number;
    public unitId :number;
    public constructId: number;
    public sortNo :number;
    public jsonStr :string;
    public parentId :number;
    public groupCode :number;
    public addFlag :number;
    public levelType :number;
    public dispNo:number;
    public requiredFlag:number;


    constructor(sequenceNbr:string, name: string, context: string, remark: string, parentId: number, type: number, lockFlag: number, unitId: number, constructId: number, sortNo: number, jsonStr: string, groupCode: number, addFlag: number, levelType: number, dispNo: number, requiredFlag: number) {
        super(sequenceNbr);
        this.name = name;
        this.context = context;
        this.remark = remark;
        this.type = type;
        this.lockFlag = lockFlag;
        this.unitId = unitId;
        this.constructId = constructId;
        this.sortNo = sortNo;
        this.jsonStr = jsonStr;
        this.parentId = parentId;
        this.groupCode = groupCode;
        this.addFlag = addFlag;
        this.levelType = levelType;
        this.dispNo = dispNo;
        this.requiredFlag = requiredFlag;
    }
}