/*
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-08-24 15:23:17
 * @LastEditors: wangru
 * @LastEditTime: 2025-05-14 14:12:51
 */
import { ref } from 'vue';
import { getUrl } from '@/utils/index';
const operateList = ref([
  {
    label: '匹配指标',
    name: 'matching-indicators',
    iconType: 'icon-pipeizhibiao',
    components: ['keyInfo', 'economyQuota'],
  },
  {
    label: '设置查看范围',
    name: 'set-viewing-range',
    disabled: false,
    iconType: 'icon-shezhichakanfanwei',
    components: ['keyInfo', 'economyQuota'],
  },
  {
    label: '参考指标设置',
    name: 'reference-indicator-setting',
    iconType: 'icon-cankaozhibiaoshezhi',
    components: ['keyInfo', 'economyQuota'],
  },
  {
    label: '分析方式',
    name: 'analysis-method',
    windows: ['parentPage'],
    type: 'selectRadio',
    value: 1,
    options: [
      {
        type: 1,
        name: '按工程分析',
        kind: 1,
        isValid: true,
      },
      {
        type: 2,
        name: '按专业分析',
        kind: 2,
        isValid: true,
      },
      {
        type: 3,
        name: '按费用分析',
        kind: 3,
        isValid: true,
      },
    ],
    iconType: 'icon-fenxifangshi',
    components: ['economyQuota'],
    showProjectType: ['jieSuan'],
  },
]);

export const updateOperateByName = (name, callback) => {
  const info = operateList.value.find(item => item.name === name);
  callback(info);
};

export default operateList;
