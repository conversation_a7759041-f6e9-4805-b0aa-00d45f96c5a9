const {Service} = require("../../core");
const ybjs_fyhz_1_12 = require("../jsonData/fyhz/12/一般计税/河北标准13清单规范_费用汇总模板.FYMB");
const jyjs_fyhz_1_12 = require("../jsonData/fyhz/12/简易计税/河北标准13清单规范_费用汇总模板.FYMB");
const jyjs_fyhz_1_22 = require("../jsonData/fyhz/22/简易计税/标准13清单规范_费用汇总模板.FYMB");
const ybjs_fyhz_1_22 = require("../jsonData/fyhz/22/一般计税/标准13清单规范_费用汇总模板.FYMB");
const TaxCalculationMethodEnum = require("../enum/TaxCalculationMethodEnum");
const ConstantUtil = require("../enum/ConstantUtil");
const {NumberUtil} = require("../utils/NumberUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ResponseData} = require("../utils/ResponseData");
const {Snowflake} = require("../utils/Snowflake");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {UnitCostSummary} = require("../model/UnitCostSummary");
const {TAX_MODE_1} = require("../enum/ProjectLevelConstant");
const fs = require('fs');
const path = require('path');
const os = require('os');
const {dialog} = require("electron");
const {writeFileSync, readFileSync, readdirSync, statSync} = require("fs");

function getFiles(currentPath, filter = item => true) {
    let itemsInfo = [];
    const items = readdirSync(currentPath);
    for (const item of items) {
        const itemPath = path.join(currentPath, item);
        const stat = statSync(itemPath);

        const itemInfo = {
            name: item,
            path: itemPath,
            size: stat.size,
            createdAt: stat.ctime,
            modifiedAt: stat.mtime,
            isDirectory: stat.isDirectory(),
            children: []
        };
        if (filter(itemInfo)) {
            itemsInfo.push(itemInfo);
        }
    }
    return itemsInfo;
}

function getAllFiles(dirPath, filter= item=>true) {
    function traverseDirectory(currentPath) {
        const items = readdirSync(currentPath);
        const itemsInfo = [];

        for (const item of items) {
            const itemPath = path.join(currentPath, item);
            const stat = statSync(itemPath);

            const itemInfo = {
                name: item,
                path: itemPath,
                size: stat.size,
                createdAt: stat.ctime,
                modifiedAt: stat.mtime,
                isDirectory: stat.isDirectory(),
                children: []
            };

            if (itemInfo.isDirectory) {
                itemInfo.children = traverseDirectory(itemPath);
            }
            if (filter(itemInfo)) {
                itemsInfo.push(itemInfo);
            }

        }

        return itemsInfo;
    }

    return traverseDirectory(dirPath);
}
class UnitCostSummaryService extends Service {

    constructor(ctx) {
        super(ctx);
        this.decimalPointConfig = this.service.globalConfigurationService.getDecimalPointConfig()
    }


    /**
     * 计算费用汇总
     * @param unitCostCodePriceArray
     * @param unitCostSummaryArray
     * @returns {*}
     */
    countUnitCostSummary(unitCostCodePriceArray, unitCostSummaryArray, unit) {
        //费用代码<费用代码,price>
        let priceMap = new Map();
        //计算基数 <费用汇总费用代号,calculateFormula>
        let codeFormulaMap = new Map();
        //费用汇总费率
        let codeRateMap = new Map();
        //费用代码
        for (let i = 0; i < unitCostCodePriceArray.length; i++) {
            let unitCostCodePrice = unitCostCodePriceArray[i];
            priceMap.set(unitCostCodePrice.code, unitCostCodePrice.price)
        }
        //费用汇总
        for (let i = 0; i < unitCostSummaryArray.length; i++) {
            let unitCostSummary=unitCostSummaryArray[i];
            codeFormulaMap.set(unitCostSummary.code,unitCostSummary.calculateFormula)
            codeRateMap.set(unitCostSummary.code,ObjectUtils.isEmpty(unitCostSummary.rate)?100:unitCostSummary.rate)
            if('增值税应纳税额'==unitCostSummary.type){
                if(unitCostSummary.price<0){
                    unitCostSummary.price =0
                    priceMap.set(unitCostSummary.code,unitCostSummary.price)
                }
            }

        }
        let constructDeStandard = PricingFileFindUtils.getConstructDeStandard(unit.constructId);
        for (let i = 0; i < unitCostSummaryArray.length; i++) {
            let unitCostSummary = unitCostSummaryArray[i];
            //计算基数
            let calculateFormula = unitCostSummary.calculateFormula;
            if (ObjectUtils.isEmpty(calculateFormula)) {
                continue
            }
            // 分解字符串成表达式和变量名
            const variablesToReplace = calculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
            //存放替换后的计算公式
            let afterCalculateFormula = calculateFormula;

            //递归计算费用汇总
            afterCalculateFormula = this.recursionSummary(calculateFormula, afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace, codeRateMap)

            let result;
            if (ObjectUtils.isNotEmpty(unitCostSummary.rate)) {
                result = parseFloat((eval(afterCalculateFormula) * unitCostSummary.rate / 100).toFixed(this.decimalPointConfig.costPrice));
            } else {
                result = NumberUtil.numberScale(eval(afterCalculateFormula),this.decimalPointConfig.costPrice) ;
            }
            if('增值税应纳税额'==unitCostSummary.type){
                //费用汇总中费用类别为“增值税应纳税额”，若费用汇总行对应该费用类别时，其计算结果小于0时，按0计算展示
                if(result<0){
                    result =0;
                    priceMap.set(unitCostSummary.code,result)
                }
            }

            unitCostSummary.price = result;
        }
        //更新造价分析
        this.setCostAnalysisData(unit, unitCostSummaryArray, unitCostCodePriceArray)
        return unitCostSummaryArray
    }

    /**
     * 递归计算费用汇总
     * @param calculateFormula 计算基数
     * @param afterCalculateFormula  存放替换后的计算公式
     * @param codeFormulaMap 计算基数 <费用汇总费用代号,calculateFormula>
     * @param priceMap 费用代码<费用代码,price>
     * @param variablesToReplace 拆分计算基数后的数组
     * @returns {*}
     */
    recursionSummary(calculateFormula, afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace, codeRateMap) {
        for (let variable of variablesToReplace) {
            if (priceMap.has(variable)) {
                if (priceMap.get(variable) < 0) {
                    afterCalculateFormula = afterCalculateFormula.replace(variable, '(' + priceMap.get(variable) + ')');
                } else {
                    afterCalculateFormula = afterCalculateFormula.replace(variable, priceMap.get(variable));
                }

            } else {
                if (isNaN(Number(variable))) {
                    if (ObjectUtils.isEmpty(codeFormulaMap.get(variable))){
                        return 0;
                    }
                    //此处认为是数字不进来
                    let variablesToReplace1 = codeFormulaMap.get(variable).replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
                    //说明当前行引用了费用代号 此处需要加上费率
                    afterCalculateFormula = afterCalculateFormula.replace(variable, '(' + variable + ')/100*' + codeRateMap.get(variable));
                    afterCalculateFormula = afterCalculateFormula.replace(variable, codeFormulaMap.get(variable));
                    //afterCalculateFormula = afterCalculateFormula.replace(variablesToReplace1[0], variablesToReplace1[0]+'/100*'+codeRateMap.get(variable));
                    afterCalculateFormula = this.recursionSummary(codeFormulaMap.get(variable), afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace1, codeRateMap)
                }

            }
        }
        return afterCalculateFormula;
    }

    /**
     * 新增粘贴费用汇总
     * @param args
     * @returns {Promise<void>}
     */
    async addCostSummary(args) {
        let unitCostSummarys = await this.getUnitCostSummary(args);

        let addUnitCostSummary = new Array();
        args.unitCostSummary.sequenceNbr = Snowflake.nextId();
        args.unitCostSummary.unitId = args.unitId;
        addUnitCostSummary.push(args.unitCostSummary);

        unitCostSummarys.splice(args.lineNumber - 1, 0, ...addUnitCostSummary);

        await PricingFileWriteUtils.updateUnitCostSummary(unitCostSummarys, args.constructId, args.singleId, args.unitId);
    }

    /**
     * 删除费用汇总(需要判断)
     * @param args
     * @returns {Promise<void>}
     */
    async deleteCostSummary(args) {
        let unitCostSummarys = await this.getUnitCostSummary(args);
        //判断需要删除的费用汇总中的费用代号是否被引用
        let deleteItem = unitCostSummarys.find(item => item.sequenceNbr === args.sequenceNbr);
        for (let i = 0; i < unitCostSummarys.length; i++) {
            if (!ObjectUtils.isEmpty(unitCostSummarys[i].calculateFormula)) {
                let codeList = unitCostSummarys[i].calculateFormula.split(/[+\-*/]/);
                if (!ObjectUtils.isEmpty(codeList) && !ObjectUtils.isEmpty(deleteItem.code)) {
                    if (this.stringInArray(codeList, deleteItem.code)) {
                        return ResponseData.fail('该行已被引用，不可删除');
                    }
                }
            }
        }
        let filter = unitCostSummarys.filter(item => item.sequenceNbr !== args.sequenceNbr);
        await PricingFileWriteUtils.updateUnitCostSummary(filter, args.constructId, args.singleId, args.unitId);
    }

    /**
     * 计税方式修改费率后修改费用汇总费率
     */
    updateUnitCostSummaryRate(unitObj) {

        let unitIs2022 = PricingFileFindUtils.is22Unit(unitObj);
        let projectTaxCalculation = unitObj.projectTaxCalculation
        //费用代码
        let unitCostCodePriceArray = unitObj.unitCostCodePrices


        let unitCostSummarys = unitObj.unitCostSummarys;

        for (let i = 0; i < unitCostSummarys.length; i++) {
            let obj = unitCostSummarys[i];
            if ('销项税额' === obj.type) {
                obj.rate = projectTaxCalculation.outputTaxRate
                obj.whetherTax = 1;
            } else if ('附加税费' === obj.type) {
                obj.rate = projectTaxCalculation.additionalTaxRate
                obj.whetherTax = 1;
            } else if ('税金' === obj.type) {

                if (unitIs2022) {
                    obj.rate = projectTaxCalculation.taxRate
                } else {
                    if (ObjectUtils.isEmpty(projectTaxCalculation.simpleRate)) {
                        //12 一般
                        obj.rate = null
                    } else {
                        obj.rate = projectTaxCalculation.simpleRate
                    }

                }
                obj.whetherTax = 1;
            }
        }

        let resArray = this.countUnitCostSummary(unitCostCodePriceArray, unitCostSummarys, unitObj);
        unitObj.unitCostSummarys = resArray;
        // 根据已初始化完成的单位费用代码数据进行单位的费用汇总数据计算
        // this.setCostAnalysisData(unitObj, resArray, unitCostCodePriceArray);
    }

    getDefaultUnitCostSummary(unitObj) {

        let unitIs2022 = PricingFileFindUtils.is22Unit(unitObj);
        let projectTaxCalculation = unitObj.projectTaxCalculation
        //费用代码
        let unitCostCodePriceArray = unitObj.unitCostCodePrices

        let args = {
            constructId: unitObj.constructId,
            singleId: unitObj.spId,
            unitId: unitObj.sequenceNbr
        }
        let template;
        //一般计税
        if (projectTaxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.GENERAL.code) {
            if (unitIs2022) {
                template = path.join(__dirname, '..', 'jsonData','fyhz', '22', '一般计税', '标准13清单规范_费用汇总模板.FYMB')
            } else {
                template = path.join(__dirname, '..', 'jsonData','fyhz', '12', '一般计税', '河北标准13清单规范_费用汇总模板.FYMB')
                //仅12定额标准、一般计税情况下需要增加进项税明细
                this.service.inputTaxDetailsService.initInputTaxDetails(unitObj);
            }
        } else {
            if (unitIs2022) {
                template = path.join(__dirname, '..', 'jsonData','fyhz', '22', '简易计税', '标准13清单规范_费用汇总模板.FYMB')
            } else {
                template = path.join(__dirname, '..', 'jsonData','fyhz', '12', '简易计税', '河北标准13清单规范_费用汇总模板.FYMB')
            }
        }
        args.path = template;
        let countUnitCostSummaryArray = this.getTemplateData(args)
        let resArray = this.countUnitCostSummary(unitCostCodePriceArray, countUnitCostSummaryArray, unitObj);
        unitObj.unitCostSummarys = resArray;
        // 根据已初始化完成的单位费用代码数据进行单位的费用汇总数据计算
        this.setCostAnalysisData(unitObj, resArray, unitCostCodePriceArray);

    }

    getUnitCostSummary(args) {
        let unitCostSummary = PricingFileFindUtils.getUnitCostSummary(args.constructId, args.singleId, args.unitId);
        return unitCostSummary;
    }

    async saveCostSummary(args) {
        let param = args.unitCostSummary;
        if (ObjectUtils.isEmpty(param) || ObjectUtils.isEmpty(param.unitId)) {
            return ResponseData.fail('参数错误');
        }
        let {unitCostCodePriceService} = this.service;
        let unitCostCodePriceArray = unitCostCodePriceService.getUnitCostCodePrice(args);
        for (let i = 0; i < unitCostCodePriceArray.length; i++) {
            if (param.code === unitCostCodePriceArray[i].code) {
                return ResponseData.fail('当前费用代号与费用代码重复，请修改');
            }
        }
        let unitCostSummaryArray = await this.getUnitCostSummary(args);
        for (let i = 0; i < unitCostSummaryArray.length; i++) {
            if (param.sequenceNbr !== unitCostSummaryArray[i].sequenceNbr && !ObjectUtils.isEmpty(param.code) && !ObjectUtils.isEmpty(unitCostSummaryArray[i].code) && param.code.toLowerCase() === unitCostSummaryArray[i].code.toLowerCase()) {
                return ResponseData.fail('当前费用代码已被使用');
            }
        }
        let codePriceMap = new Map();//1费用代号、代码存放<费用代号、代码,price>
        let codeFormulaMap = new Map();//计算基数
        let codeInstructionsMap = new Map();//基数说明、费用名称<费用代号、代码,instructions>
        let codeRateMap = new Map();
        let currentId = null;
        let priceChangeArray = new Array();
        let unit = await PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);
        if (!ObjectUtils.isEmpty(param.sequenceNbr)) {
            // 修改
            let unitCostSummary = unitCostSummaryArray.find(item => item.sequenceNbr === param.sequenceNbr);
            if (unitCostSummary == null) {
                return ResponseData.fail('参数错误');
            }
            currentId = param.sequenceNbr;

            await this.getAllCodeFormulaPriceMap(unitCostCodePriceArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, unitCostSummaryArray);
            //判断code
            if (!ObjectUtils.isEmpty(unitCostSummary.code) && param.code !== unitCostSummary.code) {
                // 如果修改了code  那么就要去看这个老的code是不有地方引用了他  如果有引用  那么不能修改这个code  后面产品又说改了code其他引用了code的地方也要同步变更...
                await this.inspectionCode(param.code, unitCostSummary.code, codeFormulaMap);
            }
            //判断 计算基数和费率修改
            if (param.calculateFormula !== unitCostSummary.calculateFormula || (param.rate !== unitCostSummary.rate && !ObjectUtils.isEmpty(param.calculateFormula))) {
                // 如果参数传来的计算公式或者费率不一样  那么说明修改了计算公式或者费率   就需要对计算公式进行验证并计算结果
                let responseData = await this.handleUpdateCalculateFormula(param, codePriceMap, codeFormulaMap, codeInstructionsMap);
                if (!ObjectUtils.isEmpty(responseData)) {
                    return ResponseData.fail(responseData.message);
                }
                // 把本次计算的结果存入map  留待后续计算使用
                codePriceMap.set(param.code, param.price);
                codeFormulaMap.set(param.code, param.calculateFormula);
                // 如果公式进行了修改  那么需要对引用了这个条公式对应的code的所有公式重新计算，并且对扩散影响的所有公式都需要重新计算
                await this.handleCodePriceChange(param.code, codePriceMap, codeFormulaMap, codeRateMap);
                if (param.rate != unitCostSummary.rate && param.whetherTax == 1) {
                    let projectTaxCalculation = unit.projectTaxCalculation;
                    if ('销项税额' === param.name) {
                        projectTaxCalculation.outputTaxRate = param.rate;
                    } else if ('附加税费' === param.name) {
                        projectTaxCalculation.additionalTaxRate = param.rate;
                    } else if ('税金' === param.name) {
                        projectTaxCalculation.simpleRate = param.rate;
                        projectTaxCalculation.taxRate = param.rate;
                    }
                }
            }
            // 根据上面这步的计算 得出有哪些数据需要更新
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let item = unitCostSummaryArray[i];
                let price = codePriceMap.get(item.code);
                let formula = codeFormulaMap.get(item.code)
                let updateUnitCostSummary = new UnitCostSummary();
                updateUnitCostSummary.sequenceNbr = item.sequenceNbr;
                let flag = false;
                if (!ObjectUtils.isEmpty(item.code) && price !== item.price) {
                    updateUnitCostSummary.price = price;
                    flag = true;
                }
                if (!ObjectUtils.isEmpty(item.code) && !ObjectUtils.isEmpty(formula) && formula !== item.calculateFormula) {
                    updateUnitCostSummary.calculateFormula = formula;
                    flag = true;
                }
                if (flag === true) {
                    priceChangeArray.push(updateUnitCostSummary)
                }
            }

        } else {
            // 新增
            if (ObjectUtils.isEmpty(args.unitId)) {
                return ResponseData.fail('参数错误');
            }
            let find = unitCostSummaryArray.find(item => item.code === param.code);
            if (!ObjectUtils.isEmpty(find)) {
                return ResponseData.fail('当前code已存在');
            }
            // 获取统一的map数据
            await this.getAllCodeFormulaPriceMap(unitCostCodePriceArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, unitCostSummaryArray);
            // 处理公式校验和计算
            await this.handleUpdateCalculateFormula(param, codePriceMap, codeFormulaMap, codeInstructionsMap);
        }

        // 进行数据更新
        await this.setUnitCostSummaryData(args, unitCostSummaryArray, priceChangeArray);

        //调用计算费用汇总
        await this.countUnitCostSummary(unitCostCodePriceArray, unitCostSummaryArray, unit);

        // await this.setCostAnalysisData(unit,unitCostSummaryArray,unitCostCodePriceArray)
        let returnFlag = await PricingFileWriteUtils.updateUnitCostSummary(unitCostSummaryArray, args.constructId, args.singleId, args.unitId).then(function () {
            return true;
        }).catch(function () {
            return false;
        });
        return returnFlag;
    }

    // inspectionCode(newCode, oldCode, codeFormulaMap) {
    //     for(let [key, value] of codeFormulaMap.entries()) {
    //         // 对公式进行分解
    //         let codeList = value.split(/[\+\-\*\/\(\)]+/)
    //         if ( !ObjectUtils.isEmpty(codeList) && codeList.includes(oldCode)) {
    //             let replace = value.replace(oldCode,newCode);
    //             codeFormulaMap.put(key,replace)
    //         }
    //     }
    // }

    /**
     * 判断 target中是否包含 arr
     * @param arr
     * @param target
     * @returns {boolean}
     */
    stringInArray(arr, target) {
        for (let item of arr) {
            if (item.includes(target)) {
                return true;
            }
        }
        return false;
    }

    inspectionCode(newCode, oldCode, codeFormulaMap) {
        for (let [key, value] of codeFormulaMap) {
            let codeList = value.split(/[+\-*/]/);
            if (!ObjectUtils.isEmpty(codeList) && this.stringInArray(codeList, oldCode)) {
                let reg = new RegExp("\\b" + oldCode + "\\b", "gi")
                let replace = value.replaceAll(reg, newCode);
                codeFormulaMap.set(key, replace);
            }
        }
    }


    /**
     * 获取单位工程的所有已有的费用代码基数和对应的价格
     * @param unitId
     * @param currentId
     * @param codePriceMap
     * @param codeFormulaMap
     * @param codeInstructionsMap
     * @param codeRateMap
     * @param unitCostSummaryDTOList
     */
    getAllCodeFormulaPriceMap(unitCostCodePriceArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, unitCostSummaryArray) {

        if (unitCostCodePriceArray) {
            for (let i = 0; i < unitCostCodePriceArray.length; i++) {
                let unitCostCodePrice = unitCostCodePriceArray[i];
                codePriceMap.set(unitCostCodePrice.code, unitCostCodePrice.price);
                codeFormulaMap.set(unitCostCodePrice.code, unitCostCodePrice.code);
                codeInstructionsMap.set(unitCostCodePrice.code, unitCostCodePrice.name);
            }
        }
        if (unitCostSummaryArray) {

            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let unitCostSummary = unitCostSummaryArray[i];
                if (!ObjectUtils.isEmpty(currentId) && currentId === unitCostSummary.sequenceNbr) {
                    continue;
                }
                if (ObjectUtils.isEmpty(unitCostSummary.calculateFormula)) {
                    continue;
                }
                codePriceMap.set(unitCostSummary.code, unitCostSummary.price);
                codeFormulaMap.set(unitCostSummary.code, unitCostSummary.calculateFormula);
                codeInstructionsMap.set(unitCostSummary.code, unitCostSummary.name);
                if (!ObjectUtils.isEmpty(unitCostSummary.rate)) {
                    codeRateMap.set(unitCostSummary.code, unitCostSummary.rate);
                }
            }

        }
    }


    updateUnitCostSummary(args) {
        for (let i = 0; i < args.unitCostSummarys.length; i++) {
            args.unitCostSummarys[i].sequenceNbr = Snowflake.nextId();
        }
        return PricingFileWriteUtils.updateUnitCostSummary(args.unitCostSummarys, args.constructId, args.singleId, args.unitId);
    }


    doCalculator(calculateFormula, codePriceMap) {
        // 分解字符串成表达式和变量名
        const variablesToReplace = calculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
        //存放替换后的计算公式
        let afterCalculateFormula = calculateFormula;
        //替换费用代码和费用代号
        for (let variable of variablesToReplace) {
            if (codePriceMap.has(variable)) {
                afterCalculateFormula = afterCalculateFormula.replace(variable, codePriceMap.get(variable));
            }
        }
        return eval(afterCalculateFormula);
    }

    setCostAnalysisData(unitProject, unitCostSummaryArray, unitCostCodePriceArray) {

        //工程总造价含设备及其税金(不含甲供 小写)
        // 取费用汇总中：
        // 工程造价     加上以下金额
        //
        //
        // 取费用代码中：
        // SBF-分部分项设备费  +
        // DJCS_SBF-单价措施设备费 +
        // QTZJCS_SBF-其他总价措施设备费
        // ZGJSBHJ-暂估设备费   减去
        // （JGRGF 甲供人工费 +
        // JGCLF 甲供材料费 +
        // JGJXF 甲供机械费 +
        // JGZCF 甲供主材费 +
        // JGSBF-甲供设备费）


        //工程总造价含设备及其税金(含甲供 小写)
        //取费用汇总中：
        // 工程造价      加上以下金额
        //
        //
        // 取费用代码中：
        // SBF-分部分项设备费  +
        // DJCS_SBF-单价措施设备费 +
        // QTZJCS_SBF-其他总价措施设备费 +
        // ZGJSBHJ-暂估设备费

        //工程总造价含设备及其税金(不含甲供) //gczj +SBF+DJCS_SBF+QTZJCS_SBF+ZGJSBHJ -JGRGF-JGCLF-JGJXF -JGZCF -JGSBF
        let gczjsbsj = 0;
        //工程总造价含设备及其税金(含甲供)   //gczj +SBF+DJCS_SBF+QTZJCS_SBF+JGSBF+ZGJSBHJ
        let gczjsbsjjg = 0;


        //设备费及其税金(不含甲供)
        // SBF-分部分项设备费  +
        // DJCS_SBF-单价措施设备费 +
        // QTZJCS_SBF-其他总价措施设备费
        //  减去
        // （JGSBF-甲供设备费）
        let sbfsj = 0; // SBF+DJCS_SBF+QTZJCS_SBF -JGSBF

        //设备费及其税金(含甲供)
        // SBF-分部分项设备费  +
        // DJCS_SBF-单价措施设备费 +
        // QTZJCS_SBF-其他总价措施设备费 +
        //


        let sbfsjjg = 0; //SBF+DJCS_SBF+QTZJCS_SBF+JGSBF

        //费用代码
        for (let i = 0; i < unitCostCodePriceArray.length; i++) {
            let unitCostCodePrice = unitCostCodePriceArray[i];
            switch (unitCostCodePrice.code) {
                case "SBF":
                    //工程总造价含设备及其税金计算需要
                    gczjsbsj = NumberUtil.add(gczjsbsj, unitCostCodePrice.price);
                    gczjsbsjjg = NumberUtil.add(gczjsbsjjg, unitCostCodePrice.price);
                    sbfsj = NumberUtil.add(sbfsj, unitCostCodePrice.price);
                    sbfsjjg = NumberUtil.add(sbfsjjg, unitCostCodePrice.price);
                    break;
                case "DJCS_SBF":
                    //工程总造价含设备及其税金
                    gczjsbsj = NumberUtil.add(gczjsbsj, unitCostCodePrice.price);
                    gczjsbsjjg = NumberUtil.add(gczjsbsjjg, unitCostCodePrice.price);
                    sbfsj = NumberUtil.add(sbfsj, unitCostCodePrice.price);
                    sbfsjjg = NumberUtil.add(sbfsjjg, unitCostCodePrice.price);
                    break;
                case "QTZJCS_SBF":
                    //工程总造价含设备及其税金
                    gczjsbsj = NumberUtil.add(gczjsbsj, unitCostCodePrice.price);
                    gczjsbsjjg = NumberUtil.add(gczjsbsjjg, unitCostCodePrice.price);
                    sbfsj = NumberUtil.add(sbfsj, unitCostCodePrice.price);
                    sbfsjjg = NumberUtil.add(sbfsjjg, unitCostCodePrice.price);
                    break;
                case "ZGJSBHJ":
                    //工程总造价含设备及其税金计算需要
                    gczjsbsj = NumberUtil.add(gczjsbsj, unitCostCodePrice.price);
                    gczjsbsjjg = NumberUtil.add(gczjsbsjjg, unitCostCodePrice.price);
                    // sbfsj = NumberUtil.add(sbfsj, unitCostCodePrice.price);
                    // sbfsjjg = NumberUtil.add(sbfsjjg, unitCostCodePrice.price);
                    break;
                case "JGRGF":
                    //工程总造价含设备及其税金计算需要
                    gczjsbsj = NumberUtil.subtract(gczjsbsj, unitCostCodePrice.price);

                    // sbfsj = NumberUtil.subtract(sbfsj, unitCostCodePrice.price);
                    break;
                case "JGCLF":
                    //工程总造价含设备及其税金计算需要
                    gczjsbsj = NumberUtil.subtract(gczjsbsj, unitCostCodePrice.price);
                    // sbfsj = NumberUtil.subtract(sbfsj, unitCostCodePrice.price);
                    break;
                case "JGJXF":
                    //工程总造价含设备及其税金计算需要
                    gczjsbsj = NumberUtil.subtract(gczjsbsj, unitCostCodePrice.price);
                    // sbfsj = NumberUtil.subtract(sbfsj, unitCostCodePrice.price);
                    break;
                case "JGZCF":
                    //工程总造价含设备及其税金计算需要
                    gczjsbsj = NumberUtil.subtract(gczjsbsj, unitCostCodePrice.price);
                    // sbfsj = NumberUtil.subtract(sbfsj, unitCostCodePrice.price);
                    break;

                case "JGSBF":

                    gczjsbsj = NumberUtil.subtract(gczjsbsj, unitCostCodePrice.price);
                    //工程总造价含设备及其税金计算需要
                    sbfsj = NumberUtil.subtract(sbfsj, unitCostCodePrice.price);

                    break;
                case "FBFXHJ":
                    unitProject.fbfxhj = unitCostCodePrice.price;
                    break;
                case "RGF":
                    unitProject.fbfxrgf = unitCostCodePrice.price;
                    break;
                case "CLF":
                    unitProject.fbfxclf = unitCostCodePrice.price;
                    break;
                case "JXF":
                    unitProject.fbfxjxf = unitCostCodePrice.price;
                    break;
                case "ZCF":
                    unitProject.fbfxzcf = unitCostCodePrice.price;
                    break;

                case "FBFX_GLF":
                    unitProject.fbfxglf = unitCostCodePrice.price;
                    break;
                case "FBFX_LR":
                    unitProject.fbfxlr = unitCostCodePrice.price;
                    break;
                case "CSXMHJ":
                    unitProject.csxhj = unitCostCodePrice.price;
                    break;
                case "DJCSF":
                    unitProject.djcsxhj = unitCostCodePrice.price;
                    break;
                case "DJCS_RGF":
                    unitProject.djcsxrgf = unitCostCodePrice.price;
                    break;
                case "DJCS_CLF":
                    unitProject.djcsxclf = unitCostCodePrice.price;
                    break;
                case "DJCS_JXF":
                    unitProject.djcsxjxf = unitCostCodePrice.price;
                    break;
                case "DJCS_ZCF":
                    unitProject.djcsxzcf = unitCostCodePrice.price;
                    break;

                case "DJCS_GLF":
                    unitProject.djcsxglf = unitCostCodePrice.price;
                    break;
                case "DJCS_LR":
                    unitProject.djcsxlr = unitCostCodePrice.price;
                    break;
                case "QTZJCSF":
                    unitProject.zjcsxhj = unitCostCodePrice.price;
                    break;
                case "QTZJCS_RGF":
                    unitProject.zjcsxrgf = unitCostCodePrice.price;
                    break;
                case "QTZJCS_CLF":
                    unitProject.zjcsxclf = unitCostCodePrice.price;
                    break;
                case "QTZJCS_JXF":
                    unitProject.zjcsxjxf = unitCostCodePrice.price;
                    break;
                case "QTZJCS_ZCF":
                    unitProject.zjcsxzcf = unitCostCodePrice.price;
                    break;
                case "QTZJCS_GLF":
                    unitProject.zjcsxglf = unitCostCodePrice.price;
                    break;
                case "QTZJCS_LR":
                    unitProject.zjcsxlr = unitCostCodePrice.price;
                    break;
                case "QTXMHJ":
                    unitProject.qtxmhj = unitCostCodePrice.price;
                    break;
                case "ZLJE":
                    unitProject.qtxmzlje = unitCostCodePrice.price;
                    break;
                case "ZYGCZGJ":
                    unitProject.qtxmzygczgj = unitCostCodePrice.price;
                    break;
                case "ZCBFWF":
                    unitProject.qtxmzcbfwf = unitCostCodePrice.price;
                    break;
                case "JRG":
                    unitProject.qtxmjrg = unitCostCodePrice.price;
                    break;
                case "GFHJ":
                    unitProject.gfee = unitCostCodePrice.price;
                    break;
                case "AQWMSGF":
                    unitProject.safeFee = unitCostCodePrice.price;
                    break;
                default:
                    break;
            }
        }
        //费用汇总
        let projectTaxCalculation = unitProject.projectTaxCalculation

        //一般计税
        if (projectTaxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.GENERAL.code) {
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let unitCostSummary = unitCostSummaryArray[i];
                switch (unitCostSummary.type) {
                    case "进项税额":
                        unitProject.jxse = unitCostSummary.price;
                        break;
                    case "销项税额":
                        unitProject.xxse = unitCostSummary.price;
                        break;
                    case "增值税应纳税额":
                        unitProject.zzsynse = unitCostSummary.price;
                        break;
                    case "附加税费":
                        unitProject.fjse = unitCostSummary.price;
                        break;
                    case "税金":
                        unitProject.sj = unitCostSummary.price;
                        break;
                    case "工程造价":
                        unitProject.gczj = unitCostSummary.price;
                        gczjsbsj = NumberUtil.add(gczjsbsj, unitCostSummary.price);
                        gczjsbsjjg = NumberUtil.add(gczjsbsjjg, unitCostSummary.price);
                        break;
                    default:
                        break;
                }
            }
        } else {
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let unitCostSummary = unitCostSummaryArray[i];
                switch (unitCostSummary.type) {
                    case "税金":
                        unitProject.sj = unitCostSummary.price;
                        break;
                    case "工程造价": //22定额简易计税模板名字叫 工程造价
                        unitProject.gczj = unitCostSummary.price;
                        gczjsbsj = NumberUtil.add(gczjsbsj, unitCostSummary.price);
                        gczjsbsjjg = NumberUtil.add(gczjsbsjjg, unitCostSummary.price);
                        break;
                    case "含税工程造价":
                        unitProject.gczj = unitCostSummary.price;
                        gczjsbsj = NumberUtil.add(gczjsbsj, unitCostSummary.price);
                        gczjsbsjjg = NumberUtil.add(gczjsbsjjg, unitCostSummary.price);
                        break;
                    default:
                        break;
                }
            }
        }

        //处理造价分析中22定额设备费税金计算
        let unitIs2022 = PricingFileFindUtils.is22Unit(unitProject);
        if (unitIs2022) {
            if (unitProject.projectTaxCalculation.taxCalculationMethod==TaxCalculationMethodEnum.GENERAL.code
            ) {
                sbfsj = NumberUtil.numberScale(NumberUtil.multiply(sbfsj, ConstantUtil.SHEBEI_RATE),this.decimalPointConfig.costPrice);
                sbfsjjg = NumberUtil.numberScale(NumberUtil.multiply(sbfsjjg, ConstantUtil.SHEBEI_RATE),this.decimalPointConfig.costPrice);
            }else {
                sbfsj = NumberUtil.numberScale(NumberUtil.multiply(sbfsj, ConstantUtil.SHEBEI_RATE_SIMPLE),this.decimalPointConfig.costPrice);
                sbfsjjg = NumberUtil.numberScale(NumberUtil.multiply(sbfsjjg, ConstantUtil.SHEBEI_RATE_SIMPLE),this.decimalPointConfig.costPrice);
            }
        }else {
            if (unitProject.projectTaxCalculation.taxCalculationMethod==TaxCalculationMethodEnum.SIMPLE.code
            ){
                sbfsj = NumberUtil.numberScale(NumberUtil.multiply(sbfsj, ConstantUtil.SHEBEI_RATE_SIMPLE_12),this.decimalPointConfig.costPrice);
                sbfsjjg = NumberUtil.numberScale(NumberUtil.multiply(sbfsjjg, ConstantUtil.SHEBEI_RATE_SIMPLE_12),this.decimalPointConfig.costPrice);
            }
        }
        unitProject.gczjsbsj = NumberUtil.add(unitProject.gczj, sbfsj);//由于产品需求变更 所以原来的工程总造价含设备费及其税金 不含甲供或含甲供 的计算方式作废
        unitProject.gczjsbsjjg = NumberUtil.add(unitProject.gczj, sbfsjjg);
        unitProject.sbfsj = sbfsj;
        unitProject.sbfsjjg = sbfsjjg;
        if (ObjectUtils.isNotEmpty(unitProject.average) && unitProject.average != 0) {
            unitProject.unitcost = NumberUtil.divide(unitProject.gczj, unitProject.average);//计算单方造价
        }

        /**
         * 计算单单项工程
         */
        // PricingFileWriteUtils.countSingleProject(unitProject.constructId,unitProject.spId)
        PricingFileWriteUtils.countSingleProjectFromBottom(unitProject.constructId, unitProject.spId);
        /**
         * 计算工作台底部价格汇总计算
         */
        // PricingFileWriteUtils.countConstructProject(unitProject.constructId)
    }


    /**
     * 处理公式修改逻辑
     * @param param
     * @param codePriceMap
     * @param codeFormulaMap
     * @param codeInstructionsMap
     */
    async handleUpdateCalculateFormula(param, codePriceMap, codeFormulaMap, codeInstructionsMap) {
        // 对本次新增或者修改进行正确性检测
        let set = new Set();
        set.add(param.code);
        try {
            await this.doInspection(set, param.calculateFormula, codeFormulaMap);

        } catch (e) {
            return ResponseData.fail(e.message);
            ;
        }


        // 如果没有抛出异常  说明检查通过了
        let res = await this.doCalculator(param.calculateFormula, codePriceMap);
        if (param.rate) {
            res = parseFloat((res * param.rate / 100).toFixed(3));
        }

        param.price = Math.round(res * 100) / 100;
        param.instructions = await this.getFormulaInstructions(param.calculateFormula, codeInstructionsMap)
    }

    /**
     * 对公式的正确性进行检查 检查是否有错误引用或者循环引用
     * ps：
     * 这个有个简单的做法  就是从修改的公式code开始  把每一层的每一个元素都分解到一个二叉树里面
     * 如果这个二叉树的任何一条从顶层到底层的分支中出现重复的元素  那就说明这个公式存在循环引用   但是这样做的话错误提示不明确
     * @param codes
     * @param formula
     * @param codeFormulaMap
     */
    async doInspection(codes, formula, codeFormulaMap) {
        // 根据 加减乘除 分割计算公式
        let codeList = formula.split(/[\+\-\*\/\(\)]+/);
        if (codeList.length === 0) {
            throw new Error("运算公式格式错误，请检查并修改");
        }
        if (codeList.length === 1) {
            let codeFormula = codeFormulaMap.get(codeList[0].replace(/\s/g, ''));
            if (ObjectUtils.isEmpty(codeFormula)) {
                // 在map里面没找到  那就是引用了一个不存在的基数code
                //判断是否为数字
                if (isNaN(Number(codeList[0]))) {

                    throw new Error("公式存在未知引用，请检查并修改");
                }

            }
            return;
        }
        for (let i = 0; i < codeList.length; i++) {
            const c = codeList[i];
            if (codes.has(c)) {
                // 说明是自己的公式引用了自己

                throw new Error('公式存在循环引用，请检查并修改');
            }
            let codeFormula = codeFormulaMap.get(c.replace(/\s/g, ''));
            if (!codeFormula) {
                // 在map里面没找到  那就是引用了一个不存在的基数code
                if (isNaN(Number(c))) {
                    throw new Error("公式存在未知引用，请检查并修改");
                } else {
                    codeFormula = c;
                }
            }
            let newCodes = new Set(codes);
            if (c !== codeFormula) {
                newCodes.add(c);
            }
            await this.doInspection(newCodes, codeFormula, codeFormulaMap);
        }
    }


    getFormulaInstructions(formula, formulaInstructionsMap) {
        // 把公式进行分割
        let codeList = formula.split(/[\+\-\*\/\(\)]+/);
        if (!codeList.length) {
            return "";
        }
        for (let i = 0; i < codeList.length; i++) {
            let code = codeList[i];
            let instruction = formulaInstructionsMap.get(code);
            if (!ObjectUtils.isEmpty(instruction) && instruction.trim()) {
                formula = formula.replace(code, instruction);
            }
        }
        return formula;
    }

    handleCodePriceChange(code, codePriceMap, codeFormulaMap, codeRateMap) {
        for (let [key, value] of codeFormulaMap.entries()) {
            // 对公式进行分解
            let codeList = value.split(/[\+\-\*\/\(\)]+/);
            if (codeList.length > 1) {
                if (codeList.includes(code)) {
                    let res = this.doCalculator(value, codePriceMap);
                    let rate = codeRateMap.get(key);
                    if (rate) {
                        res = parseFloat((res * rate / 100).toFixed(3));
                        codePriceMap.set(key, Math.round(res * 100) / 100)
                    } else {
                        codePriceMap.set(key, res);
                    }
                    this.handleCodePriceChange(key, codePriceMap, codeFormulaMap, codeRateMap);
                }
            }
        }
    }

    setUnitCostSummaryData(args, unitCostSummaryArray, priceChangeArray) {
        //新增的数据
        let newUnitCostSummary = args.unitCostSummary;

        if (ObjectUtils.isEmpty(newUnitCostSummary.sequenceNbr)) {
            //新增
            newUnitCostSummary.sequenceNbr = Snowflake.nextId();
            for (let i = unitCostSummaryArray.length - 1; i >= 0; i--) {
                const item = unitCostSummaryArray[i];
                if (item.sortNum >= newUnitCostSummary.sortNum) {
                    item.sortNum += 1;
                }
            }
            unitCostSummaryArray.push(newUnitCostSummary);
        } else {
            //修改
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let element = unitCostSummaryArray[i];
                if (element.sequenceNbr === newUnitCostSummary.sequenceNbr) {
                    unitCostSummaryArray[i] = newUnitCostSummary;
                }
            }
        }
        //更新修改后的金额
        if (!ObjectUtils.isEmpty(priceChangeArray)) {
            for (let i = 0; i < priceChangeArray.length; i++) {
                let item = priceChangeArray[i];
                for (let j = 0; j < unitCostSummaryArray.length; j++) {
                    let element = unitCostSummaryArray[j];
                    if (element.sequenceNbr === item.sequenceNbr) {
                        if (!ObjectUtils.isEmpty(item.price)) {
                            unitCostSummaryArray[j].price = item.price
                        }
                        if (!ObjectUtils.isEmpty(item.calculateFormula)) {
                            unitCostSummaryArray[j].calculateFormula = item.calculateFormula
                        }
                    }
                }
            }
        }
        //更新内存中的费用汇总
        PricingFileWriteUtils.updateUnitCostSummary(unitCostSummaryArray, args.constructId, args.singleId, args.unitId)
    }

    /**
     * 初始化模板
     * @returns {Promise<void>}
     */
    async initTemplate(force = false){
        // 目标路径基础目录
        const baseTargetDir = path.join(os.homedir(), '.xilidata', '费用汇总模板');

        // 项目中的文件路径
        const jsonFiles = [

            {
                name: '河北标准13清单规范_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '12', '一般计税', '河北标准13清单规范_费用汇总模板.FYMB'),
                targetDir: '12',
                taxType: '一般计税'
            },
            {
                name: '标准13清单规范(含规费明细)_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '12', '一般计税', '标准13清单规范(含规费明细)_费用汇总模板.FYMB'),
                targetDir: '12',
                taxType: '一般计税'
            },
            {
                name: '标准13清单规范_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '12', '一般计税', '标准13清单规范_费用汇总模板.FYMB'),
                targetDir: '12',
                taxType: '一般计税'
            },
            {
                name: '河北标准13清单规范（进项税与销项税不含设备费）_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '12', '一般计税', '河北标准13清单规范（进项税与销项税不含设备费）_费用汇总模板.FYMB'),
                targetDir: '12',
                taxType: '一般计税'
            },
            {
                name: '河北标准13清单规范（含设备费）_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '12', '一般计税', '河北标准13清单规范（含设备费）_费用汇总模板.FYMB'),
                targetDir: '12',
                taxType: '一般计税'
            },
            {
                name: '河北标准13清单规范（含规费明细）_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '12', '一般计税', '河北标准13清单规范（含规费明细）_费用汇总模板.FYMB'),
                targetDir: '12',
                taxType: '一般计税'
            },
            {
                name: '河北标准13清单规范（管廊工程）_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '12', '一般计税', '河北标准13清单规范（管廊工程）_费用汇总模板.FYMB'),
                targetDir: '12',
                taxType: '一般计税'
            },
            {
                name: '河北标准13清单规范_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '12', '简易计税', '河北标准13清单规范_费用汇总模板.FYMB'),
                targetDir: '12',
                taxType: '简易计税'
            },
            {
                name: '标准13清单规范_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '12', '简易计税', '标准13清单规范_费用汇总模板.FYMB'),
                targetDir: '12',
                taxType: '简易计税'
            },
            {
                name: '标准13清单规范(含规费明细)_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '12', '简易计税', '标准13清单规范(含规费明细)_费用汇总模板.FYMB'),
                targetDir: '12',
                taxType: '简易计税'
            },
            {
                name: '河北标准13清单规范（含设备费）_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '12', '简易计税', '河北标准13清单规范（含设备费）_费用汇总模板.FYMB'),
                targetDir: '12',
                taxType: '简易计税'
            },
            {
                name: '河北标准13清单规范（含规费明细）_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '12', '简易计税', '河北标准13清单规范（含规费明细）_费用汇总模板.FYMB'),
                targetDir: '12',
                taxType: '简易计税'
            },
            {
                name: '河北标准13清单规范（管廊工程）_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '12', '简易计税', '河北标准13清单规范（管廊工程）_费用汇总模板.FYMB'),
                targetDir: '12',
                taxType: '简易计税'
            },
            {
                name: '标准13清单规范_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '22', '一般计税', '标准13清单规范_费用汇总模板.FYMB'),
                targetDir: '22',
                taxType: '一般计税'
            },
            {
                name: '标准13清单规范_费用汇总模板.FYMB',
                path: path.join(__dirname, '..', 'jsonData','fyhz', '22', '简易计税', '标准13清单规范_费用汇总模板.FYMB'),
                targetDir: '22',
                taxType: '简易计税'
            }
        ];

        // 将文件复制到目标路径
        jsonFiles.forEach(file => {
            // 构建目标路径
            const targetPath = path.join(baseTargetDir, file.targetDir, file.taxType, file.name);

            // 创建目标目录（如果不存在）
            const targetDir = path.dirname(targetPath);
            if (!fs.existsSync(targetDir)) {
                fs.mkdirSync(targetDir, { recursive: true });
            }

            // 检查源文件是否存在
            if (!fs.existsSync(file.path)) {
                return;
            }

            // 读取源文件内容
            const fileContent = fs.readFileSync(file.path, 'utf-8');

            // 写入目标路径
            fs.writeFileSync(targetPath, fileContent, 'utf-8');

        });

    }


    /**
     * 根据计税方式获取费用汇总模板列表
     * @param args
     * @returns {{folder: (*|string)[], files: *}}
     */
    async getCostSummaryTemplate(args) {
        let defaultPath =await this.getDefaultPath(args);
        let {path} = args;
        let defaultStoragePath = path ? path : defaultPath;

        let folder = [defaultStoragePath];
        if (!defaultStoragePath.includes(".xilidata")) {
            let files = getFiles(defaultStoragePath, itemInfo => {
                if (itemInfo.path.endsWith(ConstantUtil.FYHZ_FILE_SUFFIX)) return true;
                return false;
            });
            return {folder, files};
        }
        let files = getAllFiles(defaultStoragePath, itemInfo => {
            if (itemInfo.isDirectory) {
                folder.push(itemInfo.path);
                return true;
            }
            if (itemInfo.path.endsWith(ConstantUtil.FYHZ_FILE_SUFFIX)) return true;
            return false;
        });
        return {folder, files};
    }


    /**
     * 根据计税方式和定额册版本获取默认路径
     * @param args
     * @returns {string}
     */
    getDefaultPath(args) {
        //获取定额册版本 12/22
        let libraryCodeVersion = args.libraryCodeVersion;
        //获取计税方式
        let taxMode = args.taxMode;

        let defaultPath = `${os.homedir()}\\.xilidata\\费用汇总模板`;

        if (libraryCodeVersion == 12) {
            //12定额
            if (taxMode == TaxCalculationMethodEnum.GENERAL.code) {
                //一般计税
                return defaultPath + '\\12\\一般计税'
            } else {
                return defaultPath + '\\12\\简易计税'
            }
        } else {
            //22定额
            if (taxMode == TaxCalculationMethodEnum.GENERAL.code) {
                //一般计税
                return defaultPath + '\\22\\一般计税'
            } else {
                //简易计税
                return defaultPath + '\\22\\简易计税'
            }
        }
    }

    /**
     * 选择费用汇总模板
     * @returns {Promise<void>}
     */
    async selectCostSummaryTemplate(args) {
        //根据名称和路径获取模板数据
        let countUnitCostSummaryArray = await this.getTemplateData(args)


        let unitObj = await PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);
        let unitIs2022 = PricingFileFindUtils.is22Unit(unitObj);
        let projectTaxCalculation = unitObj.projectTaxCalculation
        //费用代码
        let unitCostCodePriceArray = unitObj.unitCostCodePrices

        for (let i = 0; i <countUnitCostSummaryArray.length; i++) {
            let unitCostSummary = countUnitCostSummaryArray[i]
            if ('销项税额' === unitCostSummary.type) {
                unitCostSummary.rate = projectTaxCalculation.outputTaxRate
            } else if ('附加税费' === unitCostSummary.type) {
                unitCostSummary.rate = projectTaxCalculation.additionalTaxRate
            } else if ('税金' === unitCostSummary.type) {

                if (unitIs2022) {
                    unitCostSummary.rate = projectTaxCalculation.taxRate
                } else {
                    if (ObjectUtils.isEmpty(projectTaxCalculation.simpleRate)) {
                        //12 一般
                        unitCostSummary.rate = null
                    } else {
                        unitCostSummary.rate = projectTaxCalculation.simpleRate
                    }

                }

            } else {
                if (ObjectUtils.isEmpty(unitCostSummary.rate)) {
                    unitCostSummary.rate = null;
                }
            }
        }
        let resArray =await this.countUnitCostSummary(unitCostCodePriceArray, countUnitCostSummaryArray, unitObj);
        unitObj.unitCostSummarys = resArray;
        // 根据已初始化完成的单位费用代码数据进行单位的费用汇总数据计算
        await this.setCostAnalysisData(unitObj, resArray, unitCostCodePriceArray);
    }

    /**
     * 根据模板名称获取数据
     */
    getTemplateData(args) {
        let template = args.path;
        //根据名称和路径获取模板数据
        const fileContent = fs.readFileSync(template, 'utf-8');
        // 解析 JSON 数据
        const fyhz_template = JSON.parse(fileContent);
        let countUnitCostSummaryArray = new Array();
        let unitObj = PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);
        let unitIs2022 = PricingFileFindUtils.is22Unit(unitObj);
        let projectTaxCalculation = unitObj.projectTaxCalculation
        let sort = 1;

        for (let i in fyhz_template) {
            sort++;
            let obj = new UnitCostSummary();
            ConvertUtil.setDstBySrc(fyhz_template[i], obj)
            obj.sequenceNbr = Snowflake.nextId();
            if (ObjectUtils.isEmpty(obj.rate)) {
                        obj.rate = null;
            }
            obj.orderNum = sort;
            obj.whetherPrint = 1;
            obj.unitId = unitObj.sequenceNbr;
            obj.price = 0;
            countUnitCostSummaryArray.push(obj);
        }

        return countUnitCostSummaryArray;
    }

    moveUpDown(args){
        let unitCostSummary = this.getUnitCostSummary(args);
        let {move,index} = args
        let indexNum = Number(index)
        if(move=='up'){
            unitCostSummary = this.moveUp(unitCostSummary,indexNum)
        }else {
            unitCostSummary = this.moveDown(unitCostSummary,indexNum)
        }

    }

    moveUp(array, index) {
        if (index > 0) {
            array.splice(index - 1, 0, array.splice(index, 1)[0]);
        }
        return array;
    }

    moveDown(array, index) {
        if (index >= 0 && index < array.length - 1) {
            array.splice(index + 1, 0, array.splice(index, 1)[0]);
        }
        return array;
    }

    /**
     * 保存模板
     * @param args
     */
    saveTemplate(args){
        let  unitCostSummary = this.getUnitCostSummary(args)
        let defaultPath =   this.getDefaultPath(args);

        let defaultName = ''
        if (args.taxMode == TaxCalculationMethodEnum.GENERAL.code) {
            defaultName = '增值税_一般计税'
        }else {
            defaultName = '增值税_简易计税'
        }
        // 生成唯一的文件名
        let uniqueFileName = defaultName;
        let counter = 1;
        while (fs.existsSync(path.join(defaultPath, `${uniqueFileName}.${ConstantUtil.FYHZ_FILE_SUFFIX}`))) {
            uniqueFileName = `${defaultName}${counter}`;
            counter++;
        }
        // 拼接最终的文件路径
        defaultPath = path.join(defaultPath, `${uniqueFileName}.${ConstantUtil.FYHZ_FILE_SUFFIX}`);

        const dialogOptions = {
            title: '保存文件',
            defaultPath: defaultPath,
            filters: [{name: '费用汇总模板', extensions: [ConstantUtil.FYHZ_FILE_SUFFIX]}]
        };

        let result = dialog.showSaveDialogSync(null, dialogOptions);
        if (result && !result.canceled) {
            if(unitCostSummary){
                let fyfzTemplate=  unitCostSummary.map(item =>({
                    dispNo: item.dispNo,
                    code: item.code,
                    name: item.name,
                    calculateFormula: item.calculateFormula,
                    instructions: item.instructions,
                    rate: item.rate,
                    type: item.type
                }))
                writeFileSync(result, JSON.stringify(fyfzTemplate), 'utf-8');
            }

        }

    }


    /**
     * 批量替换
     * @param args
     */
    batchReplacement(args){
        let {idArray} =args
        let unitCostSummary = PricingFileFindUtils.getUnitCostSummary(args.constructId, args.singleId, args.unitId);

        if(idArray){
            for (let i = 0; i < idArray.length; i++) {
                let constructId =idArray[i].constructId
                let singleId =idArray[i].singleId
                let unitId =idArray[i].unitId
                let unit =PricingFileFindUtils.getUnit(constructId, singleId, unitId)
                unit.unitCostSummarys = unitCostSummary.map(unitCostSummary => {
                    return { ...unitCostSummary, unitId: unitId };
                });
                ObjectUtils.updatePropertyValue(unit.unitCostSummarys, 'unitId', unitId);
                //修改费率并计算
                this.updateUnitCostSummaryRate(unit)
            }
        }
    }
}

UnitCostSummaryService.toString = () => '[class UnitCostSummaryService]';
module.exports = UnitCostSummaryService;
