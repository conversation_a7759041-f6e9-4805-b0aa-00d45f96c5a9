<template>
  <div
    class="submenu"
    ref="submenu"
    :style="{
      position: 'fixed',
      zIndex: '9998',
      boxShadow: 'var(--surely-table-popup-shadow)',
    }"
  >
    <a-menu
      style="width: 180px; font-size: 12px"
      v-for="(item, index) in menuConfig.options"
      mode="vertical"
    >
      <a-sub-menu
        :key="item.code"
        v-if="item.children?.length > 0 && item.visible"
      >
        <template #title>{{ item.name }}</template>
        <a-menu-item
          style="height: 30px; line-height: 30px; font-size: 12px"
          :style="{ display: cItem.visible ? 'block' : 'none' }"
          v-for="(cItem, cIndex) in item.children"
          :key="cItem.code"
          @click="
            emit('contextMenuClickEvent', {
              menu: cItem,
              row: props.currentInfo,
            }),
              props.args.hidePopup()
          "
          :disabled="cItem.disabled"
          >{{ cItem.name }}</a-menu-item
        >
      </a-sub-menu>
      <a-menu-item
        :key="item.code"
        :disabled="item.disabled"
        @click="
          emit('contextMenuClickEvent', { menu: item, row: props.currentInfo }),
            props.args.hidePopup()
        "
        v-if="!item.children && item.visible"
      >
        {{ item.name }}
      </a-menu-item>
    </a-menu>
  </div>
</template>
<script setup>
import { reactive, ref, onMounted, nextTick } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { useSubItem } from '@gongLiaoJi/hooks/useSubItemStable.js';

const projectStore = projectDetailStore();
const props = defineProps({
  args: {
    type: Object,
  },
  deleteStateFn: {
    type: Function,
  },
  pasteRowVisible: {
    type: Function,
  },
  hangMenuList: {
    type: Array,
  },
  currentInfo: {
    type: Object,
  },
  tableData: {
    type: Object,
  },
  copyData: {
    type: Object,
  },
  originalData: {
    type: Object,
  },
  selectState: {
    type: Object,
  },
});
const emit = defineEmits([
  'update:currentInfo',
  'contextMenuClickEvent',
  'handleNote',
  'handleMainList',
]);
let submenu = ref();
// 定位右键元素
let menuEl = ref();
onMounted(() => {
  // 获取目标元素
  let tableEl = document.querySelector('.surely-table-body-viewport-container');
  // 添加滚动事件监听器
  tableEl.addEventListener('scroll', function () {
    // 输出滚动位置
    if (submenu.value) {
      updatePosition();
    }
  });
  function calculateDistanceToViewportBottom(element) {
    const rect = element.getBoundingClientRect();
    const viewportBottom = window.innerHeight;
    const elementBottom = rect.bottom;
    return viewportBottom - elementBottom;
  }
  let isFixed = false;
  function updatePosition() {
    const distance = calculateDistanceToViewportBottom(submenu.value);
    console.log('distance', distance);
    if (!isFixed && distance <= 33) {
      submenu.value.classList.add('fixed-bottom');
      isFixed = true;
    } else if (
      isFixed &&
      calculateDistanceToViewportBottom(menuEl.value) >
        submenu.value.offsetHeight
    ) {
      // 当滚动距离大于元素高度时解锁
      submenu.value.classList.remove('fixed-bottom');
      isFixed = false;
    }
  }
  setTimeout(() => {
    updatePosition();
  }, 50);

  // 假设你有一个ID为"myElement"的元素
});
let { needAddQDandFB } = useSubItem({
  pageType: 'csxm',
});
const menuConfig = reactive({
  options: [
    {
      code: 'add',
      name: '插入',
      visible: true,
      children: [
        {
          code: 0,
          name: `插入标题`,
          kind: '01',
          visible: true,
          disabled: true,
        },
        {
          code: 1,
          name: `插入子项`,
          kind: '02',
          visible: true,
          disabled: true,
        },
        {
          code: 2,
          name: '插入措施项',
          kind: '03',
          visible: true,
          disabled: true,
        },
        {
          code: 3,
          name: '插入子目',
          kind: '04',
          visible: true,
          disabled: true,
        },
      ],
    },
    {
      code: 'zcsbAdd',
      name: '插入',
      visible: false,
      disabled: true,
    },
    {
      code: 'copy',
      name: '复制',
      visible: true,
      disabled: false,
    },
    {
      code: 'copyCell',
      name: '复制单元格内容',
      visible: true,
      disabled: false,
    },
    {
      code: 'paste',
      name: '粘贴',
      visible: true,
      disabled: false,
    },
    {
      code: 'pasteChild',
      name: '粘贴为子项',
      visible: true,
      disabled: true,
    },

    {
      code: 'deleteList',
      name: '删除',
      visible: true,
      disabled: false,
      children: [
        {
          code: 'delete',
          name: '删除行',
          visible: true,
          disabled: true,
        },
        {
          code: 'tempDelete',
          name: '临时删除',
          visible: true,
          disabled: false,
        },
        {
          code: 'cancelTempDelete',
          name: '取消临时删除',
          visible: true,
          disabled: false,
        },
        {
          code: 'batchDelete-child3',
          name: '批量取消临时删除',
          type: 3,
          visible: true,
          disabled: false,
        },
        {
          code: 'batchDelete-allDelete',
          name: '批量删除子目',
          visible: true,
          disabled: false,
        },
        // {
        //   code: 'subtileAllDelete',
        //   name: '删除所有子目',
        //   visible: true,
        //   disabled: false,
        // },
        {
          code: 'batchDelete-child1',
          name: '批量删除所有临时删除项',
          type: 1,
          visible: true,
          disabled: false,
        },
        {
          code: 'batchDelete-child2',
          name: '批量删除所有工程量为0项',
          type: 2,
          visible: true,
          disabled: false,
        },
      ],
    },
    {
      code: 'noteList',
      name: '批注',
      visible: true,
      disabled: false,
      children: [
        {
          code: 'add-note',
          name: '插入批注',
          type: 1,
          visible: true,
          disabled: false,
        },
        {
          code: 'edit-note',
          name: '编辑批注',
          type: 2,
          visible: true,
          disabled: false,
        },
        {
          code: 'del-note',
          name: '删除批注',
          type: 3,
          visible: true,
          disabled: false,
        },
        {
          code: 'show-note',
          name: '显示批注',
          type: 4,
          visible: true,
          disabled: false,
        },
        {
          code: 'hide-note',
          name: '隐藏批注',
          type: 5,
          visible: true,
          disabled: false,
        },
        {
          code: 'del-all-note',
          name: '删除所有批注',
          type: 6,
          visible: true,
          disabled: false,
        },
      ],
    },
    ...props.hangMenuList,
  ],
});
const deTypes = ['04', '05', '06', '07', '08', '09', '10', '-1']; //定额项 05 主材
const areAllTypesSame = arr => {
  // 空数组处理
  if (arr.length === 0) return false;
  if (arr.length === 1) return true;
  // 获取第一个元素的type值作为基准
  const itemFirst = arr[0];
  // const deTypes = ['04', '06', '07', '08', '09', '10', '11', '12']; //定额项
  // 检查后续所有元素的type值是否与基准相同
  //可竞争项目和措施项
  if (itemFirst.type === '01' || itemFirst.type === '03') {
    return arr.every(obj => {
      return obj.type === itemFirst.type;
    });
  } else {
    return arr.every(obj => {
      return deTypes.includes(obj.type);
    });
  }
};
const visibleMethod = async () => {
  let options = menuConfig.options;
  let row = props.args.record;
  Array.from(document.querySelectorAll('.surely-table-row')).map(a => {
    if (a.dataset.rowKey === row.sequenceNbr) {
      menuEl.value = a;
    }
  });
  if (!row) return;
  emit('update:currentInfo', row);
  projectStore.SET_SUB_CURRENT_INFO(row);
  props.deleteStateFn();
  console.log('row', row);
  options.forEach(item => {
    item.disabled = false;
    item.children?.forEach(childItem => {
      childItem.disabled = false;
    });
    if (item.code === 'add') item.visible = ![94, 95].includes(row.kind);
    if (item.code === 'zcsbAdd') item.visible = [94, 95].includes(row.kind);
    // if (
    //   (!props.copyData || props.copyData?.length === 0) &&
    //   item.code === 'paste'
    // ) {
    //   item.disabled = true;
    // }
    // if (props.copyData?.length > 0 && item.code === 'paste') {
    //   item.disabled = false;
    // }
    if (item.code === 'copy') {
      nextTick(() => {
        if (
          props.selectState.selectedRowKeys &&
          props.selectState.selectedRowKeys.length > 0
        ) {
          const selectItems = props.tableData.filter(item =>
            props.selectState.selectedRowKeys.includes(item.sequenceNbr)
          );
          const hasCantCopy = selectItems.some(item => {
            //顶层措施项目和不可竞争措施
            return item.type == '0' || item.awfType === 2;
          });
          if (hasCantCopy) {
            item.disabled = true;
          } else {
            const isAllZcSb = selectItems.every(item => item.type === '05'); // 选的全部是主材，不能复制
            const isSameType = areAllTypesSame(selectItems);
            item.disabled = isAllZcSb ? true : isSameType ? false : true;
          }
        } else {
          item.disabled = true;
        }
      });
    }
    if (item.code === 'paste') {
      if (props.copyData && props.copyData.length > 0) {
        // const copyItem = props.tableData.find(
        //   item => item.sequenceNbr === props.copyData[0]
        // );
        const copyItem = props.originalData?.[0];
        if (!copyItem) {
          item.disabled = true;
        } else {
          if (row.type === '01' || row.type === '03') {
            item.disabled = copyItem.type === row.type ? false : true;
          } else if (row.type === '05') {
            item.disabled = true;
          } else {
            item.disabled =
              deTypes.includes(copyItem.type) && deTypes.includes(row.type)
                ? false
                : true;
          }
        }
      } else {
        item.disabled = true;
      }
    }

    if (item.code === 'pasteChild') {
      if (props.copyData && props.copyData.length > 0) {
        // const copyItem = props.tableData.find(
        //   item => item.sequenceNbr === props.copyData[0]
        // );
        const copyItem = props.originalData?.[0];
        if (!copyItem) {
          item.disabled = true;
        } else {
          if (copyItem.type === '01') {
            item.disabled = row.type == '0' ? false : true;
          }
          if (copyItem.type === '03') {
            item.disabled = row.type === '01' ? false : true;
          }
          if (deTypes.includes(copyItem.type)) {
            //组价方式为定额组价的，定额才能粘贴为子项
            item.disabled =
              row.type === '03' && row.pricingMethod == 2 ? false : true;
          }
        }
      } else {
        item.disabled = true;
      }
    }
    if (item.code === 'deleteList') {
      // if (row?.kind == 1 && row?.awfType == 1) {
      //   item.visible = false;
      //   return;
      // }
      // 删除行操作
      if (row.kind !== '00') {
        item.children[0].disabled = false;
      } else {
        item.children[0].disabled = true;
      }
      // 分部、措施项不可以临时删除、取消临时删除操作
      if (
        row.kind === '00' ||
        row.kind === '01' ||
        row.kind === '02' ||
        row.kind === '03'
      ) {
        item.children[1].visible = false;
        item.children[2].visible = false;
        // 如果是临时删除的行隐藏临时删除按钮
      } else if (row.isTempRemove == 1) {
        item.children[1].visible = false;
        // 如果父级为临时删除则显示取消临时删除按钮否则隐藏
        if (row.isFirstTempRemove === 1) {
          item.children[2].visible = true;
        } else {
          item.children[2].visible = false;
        }
      } else {
        item.children[1].visible = true;
        item.children[2].visible = false;
      }
    }
    if (
      item.children &&
      !['batchDelete', 'noteList', 'MainList', 'deleteList'].includes(item.code)
    ) {
      item.disabled = false;
      item.children.forEach(childItem => {
        childItem.disabled = true;
        row.optionMenu.forEach(child => {
          if (child === childItem.code) {
            childItem.disabled = false;
          }
        });
      });
    }
    if (item.code === 'supplement') {
      item.children.forEach(childItem => {
        childItem.disabled = true;
        row.optionMenu.forEach(child => {
          if (
            child === childItem.type ||
            ([94, 95].includes(row.kind) && childItem.name === '补充人材机')
          ) {
            childItem.disabled = false;
          }
        });
      });
    }
    if (item.code === 'add') needAddQDandFB(item, row);
    emit('handleNote', item, row);
    emit('handleMainList', item, row);
    emit('hangMenuDisabledHandler', item, row);
  });
  console.log(options);
};
visibleMethod();
</script>
<style lang="scss" scoped>
.submenu {
}
::v-deep(.ant-menu-item) {
  font-size: 11px;
  height: 24px;
  line-height: 24px;
}
::v-deep(.ant-menu-submenu-title) {
  font-size: 11px;
  height: 24px !important;
  line-height: 24px !important;
}
.fixed-bottom {
  bottom: 33px;
}
</style>
