<!--
 * @Descripttion:人材机汇总
 * @Author: liuxia
 * @Date: 2024-03-14 15:26:47
 * @LastEditors: k<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-14 16:11:09
-->
<template>
  <div class="table-content table-content-flex-column" id="humanTable">
    <!-- contractOriginalFlag&&projectStore.asideMenuCurrentInfo?.key==0?'100/0': -->
    <split
      horizontal
      :ratio="'2/1'"
      :horizontalBottom="35"
      :minHorizontalTop="contractOriginalFlag&&projectStore.asideMenuCurrentInfo?.key==0?400:450"
      style="height: 100%"
      mode="vertical"
      :onlyPart = "projectStore.currentTreeInfo?.levelType == 3 && [0,20].includes(Number(projectStore.asideMenuCurrentInfo?.key)) ? 'Top' : 'all'"
    >
      <template #one>
        <div class="table-content">
          <Teleport to=".tab-menus" v-if="projectStore.tabSelectName === '人材机调整'">
            <span class="price-list">
              <span  v-if="
                  ([1,2].includes(projectStore.currentTreeInfo.levelType) &&
                    projectStore.asideMenuCurrentInfo?.key !== '20') ||
                  (projectStore.currentTreeInfo?.levelType === 3 &&
                    projectStore.currentTreeInfo?.originalFlag &&
                    projectStore.asideMenuCurrentInfo?.key === '0') ||
                  (projectStore.currentTreeInfo?.levelType === 3 &&
                    !projectStore.currentTreeInfo?.originalFlag &&
                    projectStore.asideMenuCurrentInfo?.key !== '20')
                ">
                <icon-font style="padding-right: 4px" type="icon-jiesuanhejia"></icon-font>
                结算合价: {{ jieSuanPriceTotal.toFixed(2) }}
              </span>
              <span v-if="
                  [1,2].includes(projectStore.currentTreeInfo.levelType) ||
                  (projectStore.currentTreeInfo?.levelType === 3 &&
                    projectStore.currentTreeInfo?.originalFlag) ||
                  (projectStore.currentTreeInfo?.levelType === 3 &&
                    !projectStore.currentTreeInfo?.originalFlag &&
                    projectStore.asideMenuCurrentInfo?.key == '20')
                ">
                <icon-font style="padding-right: 4px" type="icon-jiesuanhejia"></icon-font>
                价差合计: {{ jieSuanPriceDifferencSum.toFixed(2) }}
              </span>
              <span
                v-if="
                  projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.frequencyList?.length > 0 &&  
                  projectStore.currentStageInfo &&
                  projectStore.asideMenuCurrentInfo?.defaultFeeFlag.batchType==1
                "
              >
                <icon-font type="icon-tiaochazhouqi"></icon-font>
                调差周期: 第{{projectStore.currentStageInfo?.num}}期
              </span>
              <span
                v-if="
                  projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.frequencyList?.length > 0 && 
                  projectStore.currentStageInfo &&
                  projectStore.asideMenuCurrentInfo?.defaultFeeFlag.batchType==2
                "
              >
                <icon-font type="icon-tiaochazhouqi"></icon-font>
                调差周期: 第{{projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.frequencyList[projectStore.currentStageInfo?.num-1][0]}}期 ~
                第{{ projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.frequencyList[projectStore.currentStageInfo?.num-1][1]}}期
              </span>
              <icon-font
                @click="showPageColumnSetting"
                style="padding-right: 15px"
                type="icon-xianshilieshezhi"
              ></icon-font>
            </span>
          </Teleport>
<!--          <frameSelect-->
<!--            eventDom="multiple-select"-->
<!--            ref="frameSelectRef"-->
<!--            type="human"-->
<!--            :tableData="tableData"-->
<!--            @scrollTo="scrollTo"-->
<!--            @selectData="getSelectData"-->
<!--            class="table-content"-->
<!--          >-->
            <vxe-table
              align="center"
              ref="humanTable"
              :loading="loading"
              height="auto"
              :menu-config="menuConfig"
              :column-config="{ resizable: true }"
              :row-config="{
                keyField: 'sequenceNbr',
                isHover: true,
                isCurrent: true,
              }"
              :data="tableData"
              :cell-style="
                projectStore.currentTreeInfo.levelType === 3 ? cellStyle : cellTableStyle
              "
              :row-style="rowStyle"
              @edit-closed="editClosedEvent"
              keep-source
              @menu-click="contextMenuClickEvent"
              @cell-click="useCellClickEvent"
              class="table-edit-common"
              :cell-class-name="cellClassName"
              :edit-config="{
                trigger: 'click',
                mode: 'cell',
                beforeEditMethod: cellBeforeEditMethod,
                enabled: isEditEnabled,
              }"
              :scroll-y="{ enabled: true, gt: 30 }"
              @current-change="currentChange"
              show-overflow
              :header-cell-class-name="setHeaderCellClassName"
            >
              <vxe-column  type="checkbox"  width="40"  fixed="left"  v-if="!projectStore.currentTreeInfo?.originalFlag" >
                <template #header="{ checked, indeterminate }">
                    <span  class="custom-checkbox" @click.stop="toggleAllCheckboxEvent" >
                      <i  v-if="indeterminate" style="color: #1888d7" class="vxe-icon-square-minus-fill" ></i>
                      <i v-else-if="checked" style="color: #1888d7" class="vxe-icon-square-checked-fill" ></i>
                      <i v-else style="color: #1888d7" class="vxe-icon-checkbox-unchecked" ></i>
                    </span>
                </template>
              </vxe-column>
              <vxe-column v-for="columns of handlerColumns" v-bind="columns" >
                <template #header="{ column, columnIndex, $columnIndex, _columnIndex, $rowIndex }" >
                  <span class="custom-header" style="cursor: pointer">
                    <span  v-if=" columns.field === 'marketPrice' &&
                      Array.isArray(projectStore.currentStageInfo?.scope)&&
                      projectStore.currentStageInfo?.num"  @click="sortClick(column.field)">
                      第{{ projectStore.currentStageInfo?.num }}次单价
                    </span>
                    <span
                      v-else-if="
                        columns.field === 'marketPrice' &&
                        !Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}期单价</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'jieSuanStageDifferenceQuantity' &&
                        Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}次调差工程量</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'jieSuanStageDifferenceQuantity' &&
                        !Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}期调差工程量</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'jieSuanPriceSource' &&
                        Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}次单价来源</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'jieSuanPriceSource' &&
                        !Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}期单价来源</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'jieSuanPriceLimit' &&
                        projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.frequencyList
                          ?.length > 0 &&
                        Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}次单价涨/跌幅(%)</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'jieSuanPriceLimit' &&
                        projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.frequencyList
                          ?.length > 0 &&
                        !Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}期单价涨/跌幅(%)</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'jieSuanPriceDifferenc' &&
                        Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}次单位价差</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'jieSuanPriceDifferenc' &&
                        !Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}期单位价差</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'jieSuanStagePriceDifferencSum' &&
                        Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}次价差合计</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'jieSuanStagePriceDifferencSum' &&
                        !Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}期价差合计</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'taxRemoval' &&
                        Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}次除税系数(%)</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'taxRemoval' &&
                        !Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}期除税系数(%)</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'settlementPriceDifferencInputTax' &&
                        Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}次价差进项税额</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'settlementPriceDifferencInputTax' &&
                        !Array.isArray(projectStore.currentStageInfo?.scope)&&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}期价差进项税额</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'jieSuanPriceDifferencSum' &&
                        !Array.isArray(projectStore.currentStageInfo?.scope) &&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}期价差合计</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'priceMarketTax' &&
                        !Array.isArray(projectStore.currentStageInfo?.scope) &&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}期含税单价</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'priceMarket' &&
                        !Array.isArray(projectStore.currentStageInfo?.scope) &&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}期不含税单价</span
                    >
                    <span
                      v-else-if="
                        columns.field === 'taxRate' &&
                        !Array.isArray(projectStore.currentStageInfo?.scope) &&
                        projectStore.currentStageInfo?.num
                      "
                      @click="sortClick(columns)"
                      >第{{ projectStore.currentStageInfo?.num }}期结算税率(%)</span
                    >
                    <span v-else @click="sortClick(columns)">{{ column.title }}</span>
                    <img class="sortImg" src="@/assets/img/upSort.png" v-if="!!columns.sortableFlag && sortFiled === column.field && sortVal" alt=""/>
                    <img class="sortImg" src="@/assets/img/downSort.png" v-if="!!columns.sortableFlag &&sortFiled === column.field && !sortVal" alt=""/>
                    <CloseOutlined class="icon-close" @click="closeColumn({ column })" />
                  </span>
                </template>
                <template v-if="columns.slot" #default="{ column, row, $columnIndex }">
                  <template v-if="columns.field === 'markSum'">
                    <vxe-checkbox
                      v-model="row.markSum"
                      size="small"
                      content=""
                      :checked-value="1"
                      :unchecked-value="0"
                      :disabled="row.isGray"
                      @change="CheckboxChange(row, 'markSum')"
                      v-if="[1, 2].includes(Number(row.levelMark))"
                    ></vxe-checkbox>
                  </template>
                  <template v-else-if="columns.field === 'outputToken'">
                    <vxe-checkbox
                      v-model="row.outputToken"
                      size="small"
                      content=""
                      :checked-value="1"
                      :unchecked-value="2"
                      @change="CheckboxChange(row, 'outputToken')"
                    ></vxe-checkbox>
                  </template>
                  <!-- 风险幅度范围-->
                  <template v-else-if="columns.field === 'riskAmplitudeRangeMin'">
                    <icon-font
                      type="icon-bianji"
                      class="more-icon"
                      v-if="
                        isSelectedCell({
                          $columnIndex,
                          column,
                          row,
                        })
                      "
                      @click="riskOpen(row)"
                    ></icon-font>
                    <span
                      >{{ row.riskAmplitudeRangeMin }} ~ {{ row.riskAmplitudeRangeMax }}</span
                    >
                  </template>
                  <template v-else-if="columns.field === 'ifDonorMaterial'">
                    <span>{{ getDonorMaterialText(row.ifDonorMaterial) }}</span>
                  </template>
                  <template v-else-if="columns.field === 'ifLockStandardPrice'">
                    <vxe-checkbox
                      v-model="row.ifLockStandardPrice"
                      size="small"
                      content=""
                      :checked-value="1"
                      :unchecked-value="0"
                      @change="CheckboxChange(row, 'ifLockStandardPrice')"
                      v-if="row.checkIsShow"
                      :disabled="row.isGray"
                    ></vxe-checkbox>
                  </template>
                  <!-- :disabled="
                        row.isGray ||
                        (!contractOriginalFlag && !isDifference) ||
                        ['10000001', '10000002', '10000003'].includes(
                          row.materialCode.includes('#')
                            ? row.materialCode.split('#')[0]
                            : row.materialCode
                        ) ||
                        row.ifProvisionalEstimate == 1 ||
                        row.ifDonorMaterial == 1 -->
                  <!--  合同外，单项和项目中置灰 String(projectStore.currentTreeInfo.levelType) === '1' && String(projectStore.asideMenuCurrentInfo?.key) === '21'  -->
                  <!--  合同内，单项和项目中，是否分期，如果分期过，置灰 ['1','2'].includes(String(projectStore.currentTreeInfo.levelType)) && String(projectStore.asideMenuCurrentInfo?.key) === '0' && isStaged  -->
                  <template v-else-if="columns.field === 'isDifference'">
                    <vxe-checkbox
                      v-model="row.isDifference"
                      size="small"
                      content=""
                      :checked-value="true"
                      :unchecked-value="false"
                      :disabled="(!isDifference&&!projectStore.currentTreeInfo?.originalFlag)||
                                  String(projectStore.currentTreeInfo.levelType) === '1' && String(projectStore.asideMenuCurrentInfo?.key) === '21' ||
                                  ['1','2'].includes(String(projectStore.currentTreeInfo.levelType)) && String(projectStore.asideMenuCurrentInfo?.key) === '0' && isStaged||
                                  (row.isDifference && row.kind == 1) ||  row.ifDonorMaterial == 1 || row.isGray || Boolean(row.ifProvisionalEstimate)"
                      @change="CheckboxChange(row, 'isDifference')"
                    ></vxe-checkbox>
                  </template>
                  <template v-else-if="columns.field === 'ifProvisionalEstimate'">
                    <vxe-checkbox
                      v-model="row.ifProvisionalEstimate"
                      size="small"
                      content=""
                      :checked-value="1"
                      :unchecked-value="0"
                      :disabled="true"
                    ></vxe-checkbox>
                  </template>
                  <template v-else-if="columns.field === 'jieSuanFee'">
                    {{ getFeeType(row.jieSuanFee) }}
                  </template>
                  <template v-else-if="['jieSuanPriceMarket','priceMarket'].includes(columns.field)&&
                  projectStore.currentTreeInfo.levelType<3&&
                      row.deStandardReleaseYear=='12'&&projectStore.taxMade=='0'">
                    <span>/</span>
                  </template>
                  <template v-else-if="['jieSuanPriceMarketTax','priceMarketTax'].includes(columns.field)&&
                  projectStore.currentTreeInfo.levelType<3&&
                      row.deStandardReleaseYear=='12'&&projectStore.taxMade=='1'">
                    <span>/</span>
                  </template>
                  <!--合同税率 合同税率-->
                  <template v-else-if="['jieSuantaxRate','taxRate'].includes(columns.field)">
                    <span v-if=" projectStore.currentTreeInfo.levelType<3&& row.deStandardReleaseYear=='12'">/</span>
                    <span v-else-if="projectStore.deStandardReleaseYear === '22' && ![null, undefined].includes(row.isChangeAva) && Number(row.isChangeAva) === 0 ">-</span>
                    <span v-else>{{ row[columns.field] }}</span>
                  </template>
                  <!--  合同不含税市场价/合同含税市场价/合同含税基期价  /合同不含税基期价
                  合同/确认不含税单价 priceMarket ，  不含税基期价 priceBaseJournal， 合同/确认含税单价 priceMarketTax-->
                  <template v-else-if="['jieSuanPriceMarket','jieSuanPriceMarketTax','jieSuanMarketPrice','priceBaseJournal','priceMarket','priceMarketTax'].includes(columns.field) && projectStore.deStandardReleaseYear === '22'
                            && ![null, undefined].includes(row.isChangeAva) && Number(row.isChangeAva) === 0 ">
                    <span>-</span>
                  </template>
                  <template v-else-if="['materialName'].includes(column.field)">
                    <a-popover v-if="popoverDisplay({ column, row, $columnIndex, rowIndex })"  v-model:visible="row.annotationsVisible"  trigger="click" placement="rightTop"
                               :align="{ offset: [10, -10] }" overlayClassName="annotations-pop" :overlayStyle="{ cursor: 'pointer',  width: '250px',  height: '140px', 'z-index': '100'}"
                              @visibleChange="val => visibleChange(val, row)" :getPopupContainer="triggerNode => deNameRef(triggerNode)">
                      <template #content>
                        <Annotations @click="annotationClick" @close="v => closeAnnotations(v, row)" @onfocusNode="onFocusNode(row, $event)" :note="row.annotations"
                            style="left: 10px" :isDisabled="row?.noteEditVisible" :ref="el => getAnnotationsRef(el, row)">
                        </Annotations>
                      </template>
                      <div @mouseover="NameMouseEnterEvent({ row, column })" @mouseout="cellMouseLeaveEvent({ row, column })">
                        {{ row.materialName }}
                      </div>
                    </a-popover>
                    <div v-else  @mouseover="NameMouseEnterEvent({ row, column })">
                      {{ row.materialName }}
                    </div>
                  </template>
                  <template v-else>
                    <span>{{ row[columns.field] }}</span>
                  </template>
                </template>
                <template v-if="columns.slot" #edit="{ row, $rowIndex }">
                  <template v-if="columns.field === 'materialCode'">
                    <vxe-select
                      v-model="row.type"
                      :clearable="false"
                      transfer
                      v-if="
                        (row.type === '主材费' ||
                          row.type === '材料费' ||
                          row.type === '设备费') &&
                        !(row.markSum === 1 && (row.levelMark === 1 || row.levelMark === 2)) &&
                        Number(row.edit) !== 1
                      "
                    >
                      <vxe-option
                        v-for="item in selectOptions"
                        :key="item.type"
                        :value="item.type"
                        :label="item.type"
                      ></vxe-option>
                    </vxe-select>
                    <span v-else>{{ row.type }} </span>
                  </template>
                  <template v-if="columns.field === 'type'">
                    <vxe-select
                      v-model="row.type"
                      :clearable="false"
                      transfer
                      v-if="
                        (row.type === '主材费' ||
                          row.type === '材料费' ||
                          row.type === '设备费') &&
                        !(row.markSum === 1 && (row.levelMark === 1 || row.levelMark === 2)) &&
                        Number(row.edit) !== 1
                      "
                    >
                      <vxe-option
                        v-for="item in selectOptions"
                        :key="item.type"
                        :value="item.type"
                        :label="item.type"
                      ></vxe-option>
                    </vxe-select>
                    <span v-else>{{ row.type }} </span>
                  </template>
                  <template v-if="columns.field === 'materialName'">
                    <vxe-input
                      v-if="Number(row.edit) !== 1"
                      :clearable="false"
                      v-model.trim="row.materialName"
                      type="text"
                      @blur="clear()"
                    ></vxe-input>
                    <span v-else>{{ row.materialName }}</span>
                  </template>
                  <template v-if="columns.field === 'specification'">
                    <vxe-input
                      :clearable="false"
                      v-model.trim="row.specification"
                      type="text"
                      @blur="clear()"
                      v-if="Number(row.edit) !== 1"
                    ></vxe-input>
                    <span v-else>{{ row.specification }}</span>
                  </template>
                  <template v-if="columns.field === 'unit'">
                    <vxeTableEditSelect
                      v-if="Number(row.edit) !== 1"
                      :filedValue="row.unit"
                      :list="projectStore.unitListString"
                      @update:filedValue="
                        (newValue) => {
                          saveCustomInput(newValue, row, 'unit', $rowIndex);
                        }
                      "
                    ></vxeTableEditSelect>
                  </template>
                  <template v-if="columns.field === 'jieSuanMarketPrice'">
                    <!-- v-if="
                        row.ifLockStandardPrice !== 1 &&
                        !(row.markSum === 1 && (row.levelMark === 1 || row.levelMark === 2)) &&
                        Number(row.edit) !== 1 &&
                        projectStore.asideMenuCurrentInfo?.key !== '20'
                      " -->
                    <vxe-input
                      :clearable="false"
                      v-model.trim="row.jieSuanMarketPrice"
                      type="text"
                      @blur="clear();"
                    ></vxe-input>
                    <!-- row.jieSuanMarketPrice = pureNumber(row.jieSuanMarketPrice, 2);-->
                    <!-- <span v-else>{{ row.jieSuanMarketPrice }}</span> -->
                  </template>
                  <!-- 结算单价\现行价格指数Ft\结算不含税单价\结算含税单价\不含税基期价\含税基期价\基期价\第n期含税单价\第n期不含税单价\合同/确认不含税单价\合同/确认含税单价 -->
                  <template v-if="['jieSuanPrice','marketPrice','jieSuanCurrentPriceFt','priceMarket','priceMarketTax','priceBaseJournal','priceBaseJournalTax','jieSuanBasePrice','jieSuanPriceMarket','jieSuanPriceMarketTax'].includes(columns.field)">
                    <span
                      v-if="['jieSuanPriceMarket','priceMarket'].includes(columns.field)&&
                      projectStore.currentTreeInfo.levelType<3&&
                      row.deStandardReleaseYear=='12'&&projectStore.taxMade=='0'">/</span>
                    <span
                      v-else-if="['jieSuanPriceMarketTax','priceMarketTax'].includes(columns.field)&&
                      projectStore.currentTreeInfo.levelType<3&&
                      row.deStandardReleaseYear=='12'&&projectStore.taxMade=='1'">/</span>
                    <span
                      v-else-if="['taxRate'].includes(columns.field)&&
                      projectStore.currentTreeInfo.levelType<3&&
                      row.deStandardReleaseYear=='12'">/</span>
                    <vxe-input
                      v-else
                      :clearable="false"
                      v-model.trim="row[columns.field]"
                      type="text"
                      @blur="clear();"
                    ></vxe-input>
                  <!--row[columns.field] = pureNumber(row[columns.field], 2);-->
                  </template>
                  <!-- 合同税率 -->
                  <template v-if="columns.field === 'jieSuantaxRate'">
                    <span
                      v-if="projectStore.currentTreeInfo.levelType<3&&
                      row.deStandardReleaseYear=='12'">/</span>
<!--                    <span v-else>{{ row.jieSuantaxRate }}</span>-->
                    <span v-else-if="projectStore.deStandardReleaseYear === '22' && ![null, undefined].includes(row.isChangeAva) && Number(row.isChangeAva) === 0 ">-</span>
                    <vxe-input v-else :clearable="false" v-model.trim="row.jieSuantaxRate" type="text"
                              @blur="clear(); "
                              placeholder='请输入' ></vxe-input>
<!--                     row[columns.field] = pureNumber(row[columns.field], 2); -->
                  </template>
                  <!-- 结算税率 -->
                  <template v-if="columns.field === 'taxRate'">
                    <span
                      v-if="projectStore.currentTreeInfo.levelType<3&&
                      row.deStandardReleaseYear=='12'">/</span>
<!--                    <span v-else>{{ row.taxRate }}</span>-->
                    <span v-else-if="projectStore.deStandardReleaseYear === '22' && ![null, undefined].includes(row.isChangeAva) && Number(row.isChangeAva) === 0 ">-</span>
                    <vxe-input v-else :clearable="false" v-model.trim="row.taxRate" type="text"
                               @blur=" clear() "
                               placeholder='请输入' ></vxe-input>
                    <!--row[columns.field] = pureNumber(row[columns.field], 2);-->
                  </template>
                  <!-- 供货方式 -->
                  <template v-if="columns.field === 'ifDonorMaterial'">
                    <vxe-select
                      v-if="row.checkIsShow"
                      v-model="row.ifDonorMaterial"
                      @change="CheckboxChange(row, 'ifDonorMaterial')"
                      transfer
                    >
                      <vxe-option
                        v-for="item in donorMaterialList"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                      ></vxe-option>
                    </vxe-select>
                    <span v-else>{{ getDonorMaterialText(row.ifDonorMaterial) }}</span>
                  </template>
                  <template v-if="columns.field === 'donorMaterialNumber'">
                    <vxe-input
                      :clearable="false"
                      v-model.trim="row.donorMaterialNumber"
                      type="text"
                      @blur="clear()"
                      @keyup="
                        row.donorMaterialNumber = row.donorMaterialNumber.replace(
                          /^(\-)*(\d+)\.(\d\d\d\d).*$/,
                          '$1$2.$3'
                        )
                      "
                    ></vxe-input>
                  </template>
                  <template v-if="columns.field === 'riskAmplitudeRangeMin'">
                    <span @click="riskOpen(row)"
                      >{{ row.riskAmplitudeRangeMin }} ~ {{ row.riskAmplitudeRangeMax }}</span
                    >
                  </template>
                  <!-- 保管费率(%) -->
                  <template v-if="columns.field === 'jieSuanAdminRate'">
                    <vxe-input
                      :clearable="false"
                      v-model.trim="row.jieSuanAdminRate"
                      type="text"
                      @blur="clear();"
                      :placeholder="row.ifDonorMaterial==1?'请输入':''"
                      :disabled="row.ifDonorMaterial!=1"
                    ></vxe-input>
                  <!--row[columns.field] = pureNumber(row[columns.field], 2);-->
                  </template>
                  <template v-if="columns.field === 'taxRemoval'">
                    <!-- 如果为结算除税系数(%)或者第n期除税系数(%)  则通过是否分期进行判断显示哪个条件 -->
                    <vxe-input
                      v-if="projectStore.currentStageInfo?.num"
                      :clearable="false"
                      v-model.trim="row.taxRemoval"
                      type="text"
                      @blur="clear()"
                      @keyup="
                        row.taxRemoval = row.taxRemoval.replace(
                          /^(\-)*(\d+)\.(\d\d\d\d).*$/,
                          '$1$2.$3'
                        )
                      "
                    ></vxe-input>
                    <vxe-input
                      v-else-if="
                        row.type !== '人工费' &&
                        row.materialCode !== 'JXPB-005' &&
                        row.materialCode !== 'JXPB-006'
                      "
                      :clearable="false"
                      v-model.trim="row.taxRemoval"
                      type="text"
                      @blur="clear()"
                      @keyup="
                        row.taxRemoval = row.taxRemoval.replace(
                          /^(\-)*(\d+)\.(\d\d\d\d).*$/,
                          '$1$2.$3'
                        )
                      "
                    ></vxe-input>
                    <span v-else>{{ row.taxRemoval }}</span>
                  </template>
                  <template v-if="columns.field === 'jieSuanBasePriceF0'">
                    <vxe-input
                      :clearable="false"
                      v-model.trim="row.jieSuanBasePriceF0"
                      type="text"
                      @blur="clear()"
                      @keyup="
                        row.jieSuanBasePriceF0 = row.jieSuanBasePriceF0.replace(
                          /^(\-)*(\d+)\.(\d\d\d\d).*$/,
                          '$1$2.$3'
                        )
                      "
                    ></vxe-input>
                  </template>
                  <template v-if="columns.field === 'jieSuanCurrentPriceF0'">
                    <vxe-input
                      :clearable="false"
                      v-model.trim="row.jieSuanCurrentPriceF0"
                      type="text"
                      @blur="clear()"
                      @keyup="
                        row.jieSuanCurrentPriceF0 = row.jieSuanCurrentPriceF0.replace(
                          /^(\-)*(\d+)\.(\d\d\d\d).*$/,
                          '$1$2.$3'
                        )
                      "
                    ></vxe-input>
                  </template>
                  <template v-if="columns.field === 'producer'">
                    <vxe-input
                      :clearable="false"
                      v-model.trim="row.producer"
                      type="text"
                      @blur="clear()"
                    ></vxe-input>
                  </template>
                  <template v-if="columns.field === 'manufactor'">
                    <vxe-input
                      :clearable="false"
                      v-model.trim="row.manufactor"
                      type="text"
                      @blur="clear()"
                    ></vxe-input>
                  </template>
                  <template v-if="columns.field === 'brand'">
                    <vxe-input
                      :clearable="false"
                      v-model.trim="row.brand"
                      type="text"
                      @blur="clear()"
                    ></vxe-input>
                  </template>
                  <template v-if="columns.field === 'deliveryLocation'">
                    <vxe-input
                      :clearable="false"
                      v-model.trim="row.deliveryLocation"
                      type="text"
                      @blur="clear()"
                    ></vxe-input>
                  </template>
                  <template v-if="columns.field === 'qualityGrade'">
                    <vxe-input
                      :clearable="false"
                      v-model.trim="row.qualityGrade"
                      type="text"
                      @blur="clear()"
                    ></vxe-input>
                  </template>
                  <template v-if="columns.field === 'jieSuanFee'">
                    <vxe-select v-model="row.jieSuanFee" transfer>
                      <vxe-option
                        v-for="item in selectList"
                        :key="item.code"
                        :value="item.code"
                        :label="item.name"
                      ></vxe-option>
                    </vxe-select>
                  </template>
                  <template v-if="columns.field === 'remark'">
                    <vxe-input
                      :clearable="false"
                      v-model.trim="row.remark"
                      type="text"
                      @blur="clear()"
                    ></vxe-input>
                  </template>
                </template>
              </vxe-column>
              <template #empty>
                <span style="color: #898989; font-size: 14px; display: block; margin: 25px 0">
                  <img :src="getUrl('newCsProject/none.png')" />
                </span>
              </template>
            </vxe-table>
<!--          </frameSelect>-->
        </div>
      </template>
      <!-- v-if="!contractOriginalFlag||projectStore.asideMenuCurrentInfo?.key!=0" -->
      <!-- v-if="projectStore.currentTreeInfo?.originalFlag && +projectStore.asideMenuCurrentInfo?.key !== 0"-->
      <template #two>
        <div
          v-if="projectStore.currentTreeInfo.levelType < 3"
          style="height: 100%"
        >
          <p class="selectTab">
            <a-radio-group  v-model:value="selectdTab" :style="{ marginBottom: '8px' }">
              <a-radio-button value="lyfx">来源分析</a-radio-button>
              <a-radio-button value="xxfw" v-if="!['0','20'].includes(String(projectStore.asideMenuCurrentInfo?.key))">信息价服务</a-radio-button>
            </a-radio-group>
          </p>
          <keep-alive>
            <component
              :is="components.get(selectdTab)"
              @getUpList="getHumanMachineData"
              @upDateMarketPrice="upDateMarketPrice"
              :showInfo="isCurrent"
              :unitIdList="setAggreUnitList"
            ></component>
          </keep-alive>
        </div>
<!--   [8, 10].includes(+projectStore.asideMenuCurrentInfo?.key)-->
        <template  v-else-if="projectStore.currentTreeInfo.levelType === 3">
          <associated-information  v-if="[10].includes(+projectStore.asideMenuCurrentInfo?.key)" :showInfo="isCurrent" @refresh="getHumanMachineData">
          </associated-information>
          <machine-service v-else-if="(projectStore.currentTreeInfo?.originalFlag && +projectStore.asideMenuCurrentInfo?.key !== 0) ||
                                      !projectStore.currentTreeInfo?.originalFlag && +projectStore.asideMenuCurrentInfo?.key !== 20"
                           @getUpList="getHumanMachineData" @upDateMarketPrice="upDateMarketPrice"  :showInfo="isCurrent" :tableData="tableData"
          ></machine-service>
        </template>

      </template>
    </split>
  </div>
  <common-modal
    className="dialog-comm"
    :title="typeModal"
    width="1020"
    :height="typeModal === '载价编辑' ? 560 : typeModal === '载价报告' ? 500 : 530"
    v-model:modelValue="reportModel"
    @cancel="cancel"
    @close="reportModel = false"
    :mask="true"
    :lockView="true"
  >
    <!--     :mask="typeModal === '载价编辑' ? false : true"
    :lockView="typeModal === '载价编辑' ? false : true" -->
    <batch-load-price
      v-if="typeModal === '批量载价'"
      :priceType="priceType"
      :isOriginalFlag="isOriginalFlag()"
      @close="close"
    ></batch-load-price>
    <edit-load-price
      v-if="typeModal === '载价编辑'"
      :isOriginalFlag="isOriginalFlag()"
      :propsData="propsData"
      :priceType="priceType"
      @close="close"
    ></edit-load-price>
    <report-load-price
      :priceType="priceType"
      :propsData="propsData"
      :isOriginalFlag="isOriginalFlag()"
      v-if="typeModal === '载价报告'"
    ></report-load-price>
    <!-- 载价报告 -->
  </common-modal>

  <!-- 显示对应子目弹窗 -->
  <quota-popup
    v-if="quotaPopupVisible"
    :levelType="projectStore.currentTreeInfo.levelType"
    :HeaderData="quotaHeaderData"
    @closeDialog="quotaPopupVisible = false"
  ></quota-popup>
  <!-- 风险幅度范围 -->
  <risk-range
    v-model:riskVisible="riskVisible"
    :currentInfo="currentInfo"
    :isEditCurrentData="isEditCurrentData"
    :selectData="selectData"
    @updateData="getInitList"
    @updateCurrentInfo="updateCurrentInfo"
    @updateTreeVal="updateTreeVal"
  ></risk-range>
  <PageColumnSetting
    :columnOptions="handlerColumns"
    ref="columnSettingRef"
    title="页面显示列设置"
    @save="updateColumns"
    :getDefaultColumns="getDefaultColumns"
  />
  <material-machine-index
    pageFr="rcjSummary"
    v-model:indexVisible="indexVisible"
    :currentMaterialInfo="currentInfo"
    :indexLoading="indexLoading"
    @addChildrenRcjData="() => {}"
    @currentInfoReplace="currentInfoReplace"
  ></material-machine-index>
  <SetMainMaterials
    :tableData="allRCJTableData"
    @successCallback="getHumanMachineData"
    v-model:materialVisible="materialVisible"
  />
  <LookupFilter
    v-model:lookupVisible="lookupVisible"
    v-model:lookupConfig="lookupConfig"
    @lookupCallback="lookupCallback"
    @changeCurrentInfo="changeCurrentInfo"
  />
  <!-- 调整市场价系数 -->
  <common-modal
    className="dialog-comm"
    title="调整市场价系数"
    width="300"
    height="200"
    v-model:modelValue="adjustFactor"
    :mask="true"
  >
    <div class="adjustFactorMoadl">
      <!-- <p class="title">
        该功能针对所有选中行进行调整
      </p> -->
      <div>
        <span> 市场价系数： </span>
        <a-input
          v-model:value="marcketFactor"
          placeholder="请输入市场价系数"
          @blur="marcketFactor = selfCheck(marcketFactor, 2, 0, 1000)"
          @keyup="marcketFactor = marcketFactor.replace(/[^\d.]/g, '')"
        />
      </div>
      <p class="footor">
        <a-button @click="adjustFactor = false">取消</a-button>
        <a-button type="primary" @click="sureOrCancel()">确定</a-button>
      </p>
    </div>
  </common-modal>
  <batch-set-tax-removal
    v-model:visible="taxRemovalVisible"
    :selectData="selectData"
    @updateData="getInitList"
  ></batch-set-tax-removal>
  <import-excel
      ref="importExcelRef"
      @updateImportData="updateImportData"
      @closeImportExcel="getHumanMachineData"
  ></import-excel>
  <common-modal  className="dialog-comm area-modal" @close="delNoteMol.visible = false"  width="500"  v-model:modelValue="delNoteMol.visible" title="是否删除所有批注？">
    <div class="tree-content-wrap">
      <div class="group-list">
        <p>需要确定删除范围：</p>
        <a-radio-group v-model:value="delNoteMol.dataStatus">
          <a-radio value="3" v-if="projectStore.currentTreeInfo.levelType === 3">删除当前单位工程所有批注</a-radio>
          <a-radio value="2" v-if="projectStore.currentTreeInfo.levelType == 2">删除当前单项工程所有批注</a-radio>
          <a-radio value="1">删除工程项目所有批注</a-radio>
        </a-radio-group>
      </div>
      <div class="footer-btn-list">
        <a-button @click="delNoteMol.visible = false">取消</a-button>
        <a-button type="primary" @click="delAllNote(+delNoteMol.dataStatus)">确定</a-button>
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import {
  onActivated,
  onMounted,
  onUpdated,
  ref,
  watch,
  getCurrentInstance,
  provide,
  reactive,
  defineAsyncComponent,
  nextTick,
  computed,
  onDeactivated,
  markRaw,
  toRaw
} from "vue";
import xeUtils from "xe-utils";
import { message } from "ant-design-vue";
import { projectDetailStore } from "@/store/projectDetail";
import feePro from "@/api/feePro";
import loadApi from "@/api/loadPrice";
import infoMode from "@/plugins/infoMode.js";
import csProject from "@/api/csProject";
import jieSuanDetail from "@/api/jiesuanApi";
import { getUrl, pureNumber } from "@/utils/index";
import HumanHeader from "./HumanHeader.vue";
import { insetBus } from "@/hooks/insetBus";
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
import operateList from "@/views/projectDetail/customize/operate";
import { updateOperateByName } from "@/views/projectDetail/customize/operate";
import BatchLoadPrice from "@/views/projectDetail/customize/HumanMachineSummary/BatchLoadPrice.vue";
import EditLoadPrice from "@/views/projectDetail/customize/HumanMachineSummary/EditLoadPrice.vue";
import ReportLoadPrice from "@/views/projectDetail/customize/HumanMachineSummary/ReportLoadPrice.vue";
import MachineService from '@/views/projectDetail/customize/HumanMachineSummary/MachineService.vue';
import NumberUtil from '@/components/qdQuickPricing/utils/NumberUtil';
import ObjectUtils from '@/components/qdQuickPricing/utils/ObjectUtils';
import { isOriginalFlag } from "./common.js";
import { getTableColumns } from "./columns.js";
import Annotations from '@/components/Annotations/index.vue';
let reportModel = ref(false);
let priceType = ref(1);
let typeModal = ref(null); //弹框类型
let propsData = ref(); //载价编辑的优先级
let jieSuanPriceTotal = ref(0); // 结算单价总计
let jieSuanPriceDifferencSum = ref(0); // 价差合计总计
let treeMinVal=ref(null)
let treeMaxVal=ref(null)
import { useDecimalPoint } from '@/hooks/useDecimalPoint';
const { rateFormat, costPriceFormat, rcjSummaryAmountFormat } =
    useDecimalPoint();
import { useCellClick } from "@/hooks/useCellClick";
import RiskRange from "./riskRange.vue";
import { useFormatTableColumns } from "@/hooks/useFormatTableColumns.js";
import api from "@/api/projectDetail.js";
import { setGlobalLoading } from "@/hooks/publicApiData.js";
import ImportExcel from '@/views/projectDetail/customize/HumanMachineSummary/ImportExcel.vue';
const {
  useCellClickEvent,
  cellBeforeEditMethod,
  selectedClassName,
  isSelectedCell,
} = useCellClick();
const components = markRaw(new Map());
components.set(
  'lyfx',
  defineAsyncComponent(() => import('@/views/projectDetail/customize/HumanMachineSummary/SourceAnalysis.vue'))
);
components.set(
  'xxfw',
  defineAsyncComponent(() => import('@/views/projectDetail/customize/HumanMachineSummary/MachineService.vue'))
);
//下表格-两部分
let selectdTab = ref('lyfx');
const activeOptions = reactive([
  {
    key: 'lyfx',
    tab: '来源分析',
  },
  {
    key: 'xxfw',
    tab: '信息价服务',
  },
]);
const quotaPopup = defineAsyncComponent(() =>
  import("@/components/SummaryPopup/index.vue")
);

// 是否合同内文件true是false否projectStore.currentTreeInfo?.originalFlag
let contractOriginalFlag = computed(() => projectStore.currentTreeInfo?.originalFlag); // 暂时默认为true

let setAggreUnitList = ref([]);
let unifyData = operateList.value.find(
  (item) => item.name === "unify-humanMachineSummary"
);
let isLoad = operateList.value.find((item) => item.name === "batch-loadprice");
// let isSeeReport = operateList.value.find(
//   item => item.name === 'loadprice-report'
// );
let quotaHeaderData = ref(null);
let isCurrent = ref(null);
let colorUnitList = ref([]);
let oldData = ref([]); //工程项目级别人材机汇总旧数据
const props = defineProps({
  activeKey: {
    type: Number,
    default: 1,
  },
});
const emits = defineEmits(["updateMenuList"]);
let humanTable = ref();
let loading = ref(false);
const projectStore = projectDetailStore();
let tableData = ref([]);
let upDateRow = ref();
let selectData = ref([]);
const selectOptions = [{ type: "主材费" }, { type: "材料费" }, { type: "设备费" }];
const selectList = ref([
  {
    name: "计取税金",
    code: 1,
  },
  {
    name: "计取规费、税金",
    code: 2,
  },
  {
    name: "计取安文费、税金",
    code: 3,
  },
]);

const getFeeType = (type) => {
  let value;
  switch (type) {
    case 1:
      value = "计取税金";
      break;
    case 2:
      value = "计取规费、税金";
      break;
    case 3:
      value = "计取安文费、税金";
      break;
  }
  return value;
};
const donorMaterialList = [
  {
    label: "自行采购",
    value: 0,
  },
  {
    label: "甲方供应",
    value: 1,
  },
  {
    label: "甲定乙供",
    value: 2,
  },
];
const getDonorMaterialText = (value) => {
  const item = donorMaterialList.find((item) => item.value === value);
  return item ? item.label : "";
};
let riskVisible = ref(false); // 风险幅度范围弹框
let isEditEnabled = ref(true); // 是否可编辑行
let currentInfo = ref(null); // 当前点击行信息
let isEditCurrentData = ref(false); // 用于判断风险幅度范围是批量修改还是单条修改
let isDifference = ref(false); // 是否参与人材机调差
const taxRemovalVisible = ref(false); // 批量设置结算除税系数弹框

const quotaPopupVisible = ref(false); // 显示对应子目弹窗
const menuConfig = reactive({
  className: "my-menus",
  body: {
    options: [
      [
        {
          code: 'noteList',
          name: '批注',
          visible: true,
          disabled: false,
          children: [
            {
              code: 'add-note',
              name: '插入批注',
              type: 1,
              visible: false,
              disabled: false,
            },
            {
              code: 'edit-note',
              name: '编辑批注',
              type: 2,
              visible: false,
              disabled: false,
            },
            {
              code: 'del-note',
              name: '删除批注',
              type: 3,
              visible: false,
              disabled: false,
            },
            {
              code: 'show-note',
              name: '显示批注',
              type: 4,
              visible: false,
              disabled: false,
            },
            {
              code: 'hide-note',
              name: '隐藏批注',
              type: 5,
              visible: false,
              disabled: false,
            },
            {
              code: 'del-all-note',
              name: '删除所有批注',
              type: 6,
              visible: true,
              disabled: false,
            },
          ],
        },
        {
          code: "search",
          name: "显示对应子目",
          visible: true,
          disabled: false,
        },
        {
          code: "remove",
          name: "清除载价",
          visible: true,
          disabled: false,
        },
        {
          code: "pageColumnSetting",
          name: "页面显示列设置",
          visible: true,
          disabled: false,
        },
        {
          code: "riskRange",
          name: "风险幅度范围",
          visible: true,
          disabled: false,
        },
        {
          code: "batchSettingFactor",
          name: "批量设置结算除税系数",
          visible: true,
          disabled: false,
        },
        {
          code: "closeDifference",
          name: "取消调差",
          visible: true,
          disabled: false,
        },
        {
          code: "export",
          name: "导出excel",
          visible: true,
          disabled: false,
        }
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log("options, column", options, row);
    if (!row) return;
    options[0][1].disabled = !row.highlight || row.sourcePrice == "自行载价";
    options.forEach((list) => {
      list.forEach((item) => {
        if (item.code === "closeDifference") {
          if (row.isDifference && row.type !== "人工费") {
            item.visible = true;
          } else {
            item.visible = false;
          }
        } else if (item.code === "riskRange") {
          if (
            ([1,2].includes(projectStore.currentTreeInfo.levelType) &&
              ["1", "2", "3"].includes(projectStore.asideMenuCurrentInfo?.key)) ||
            (projectStore.currentTreeInfo.levelType === 3 &&
              projectStore.currentTreeInfo.originalFlag &&
              !projectStore.currentStageInfo &&
              ["1", "2", "3"].includes(projectStore.asideMenuCurrentInfo?.key))
          ) {
            item.visible = true;
          } else {
            item.visible = false;
          }
        } else if (item.code === "batchSettingFactor") {
          if (
            (([1,2].includes(projectStore.currentTreeInfo.levelType) &&
              ["1", "2", "3", "8", "20"].includes(
                projectStore.asideMenuCurrentInfo?.key
              )) ||
            (projectStore.currentTreeInfo.levelType === 3 &&
              ((projectStore.currentTreeInfo.originalFlag &&
                !projectStore.currentStageInfo &&
                ["1", "2", "3", "8"].includes(projectStore.asideMenuCurrentInfo?.key)) ||
                (!projectStore.currentTreeInfo.originalFlag &&
                  ["20"].includes(projectStore.asideMenuCurrentInfo?.key)))))&&projectStore.currentTreeInfo.deStandardReleaseYear=='12'
          ) {
            item.visible = true;
          } else {
            item.visible = false;
          }
        }else if(item.code === "noteList"){
          let noteList = options[0].find(item => item.code === 'noteList');
          if(!projectStore.currentTreeInfo?.originalFlag){
            // 暂估+承包人无插入批注功能
            if (![8, 10].includes(+projectStore.asideMenuCurrentInfo?.key)) {
              noteList.visible = true;
            } else {
              noteList.visible = false;
            }
            noteList.children.map(a => {
              if (['add-note'].includes(a.code)) {
                a.visible = !row.annotations;
              }
              if (
                  ['edit-note', 'del-note', 'show-note', 'hide-note'].includes(a.code)
              ) {
                a.visible = row.annotations?.length > 0;
              }
              if (a.code === 'show-note') a.disabled = row?.isShowAnnotations;
              if (a.code === 'hide-note') a.disabled = !row?.isShowAnnotations;
            });
          }else{
            noteList.visible = false;
          }
        }
      });
    });
    return true;
  },
});

const NameMouseEnterEvent = ({ row, column }) => {
  row.annotationsVisible = row.annotations?.length > 0;
};
const cellMouseLeaveEvent = ({ row, column }) => {
  if (row.isShowAnnotations) return;
  row.annotationsVisible = false;
};
// 关闭编辑的
const closeAnnotations = (v, row) => {
  if (!row.isShowAnnotations) {
    row.annotationsVisible = false; //非常显状态 编辑之后关闭
  }
  row.noteEditVisible = false;
  if (v == row?.annotations) {
    return;
  }
  row.annotations = v;
  let levelType = projectStore.currentTreeInfo?.levelType;
  let field = annotationsObj['noteFeild'][levelType];
  row[field] = v;
  if (levelType === 3) {
    // debugger;
    upDate(row, field);
  } else {
    let formData = getParamsData({
      type: levelType,
      sequenceNbr: row.sequenceNbr,
      postil: row.annotations,
    });
    console.log('updatePostil-formData', formData);
    csProject.updatePostil(formData).then(res => {
      console.log('updatePostil', res, formData);
    });
  }
};
const onFocusNode = async (row, el) => {
};
const getAnnotationsRef = (el, row) => {
  // if (el) {
  //   AnnotationsRefList.value[row.sequenceNbr] = el;
  // } else {
  //   AnnotationsRefList.value[row.sequenceNbr] = null;
  // }
};
const annotationClick = el => {
  console.log('annotationClick', el);
  const element = el.target.parentNode.parentNode.parentNode.parentNode;
  element.style.setProperty('z-index', '101', 'important');
  // debugger
};

const visibleChange = (val, row) => {
  console.log(val, row);
  // if (
  //     !val &&
  //     (row?.noteViewVisible || row?.isShowAnnotations || row?.noteEditVisible)
  // ) {
  //   row.annotationsVisible =
  //       row?.noteViewVisible || row?.isShowAnnotations || row?.noteEditVisible;
  // }
  row.annotationsVisible = true;
};

const popoverDisplay = ({ $columnIndex, row, column }) => {
  if (['materialName'].includes(column.field) && row.annotationsVisible) {
    return true;
  }
  return false;
};

const deNameRef = triggerNode => {
  // console.log(
  //   'deNameRef',
  //   triggerNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode
  // );
  return triggerNode.parentNode.parentNode.parentNode.parentNode.parentNode
      .parentNode.parentNode.parentNode.parentNode;
  //气泡放在外面的情况
  // return document.querySelector('.table-edit-common .vxe-body--row');
};


// 右键点击事件
const contextMenuClickEvent = ({ menu, row }) => {
  console.log("menu, row", menu, row);
  if (!row) return;
  switch (menu.code) {
    case "search":
      quotaHeaderData.value = row;
      quotaPopupVisible.value = true;
      break;
    case "remove":
      // 掉接口恢复系统默认值
      infoMode.show({
        iconType: "icon-querenshanchu",
        infoText:
          +row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice
            ? "该条材料市场价已被锁定，"
            : "是否确定清除选中数据的载价数据？",
        descText:
          +row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice
            ? "请取消勾选后再进行清除载价操作"
            : "删除后无法撤销恢复",
        isFunction: false,
        confirm: () => {
          if (!row.ifLockStandardPrice || !row.cusTomIfLockStandardPrice) {
            clearZaijia(row);
          }
          infoMode.hide();
        },
        close: () => {
          infoMode.hide();
        },
      });
      break;
    case "pageColumnSetting":
      showPageColumnSetting();
      break;
    case "batchSettingFactor":
      taxRemovalVisible.value = true;
      break;
    case "riskRange":
      riskOpen(row);
      break;
    case "closeDifference":
      row.isDifference = false;
      upDate(row, "isDifference");
      break;
    case 'export':
      exportExcel('all');
      break;
      //批注操作
    case 'add-note':
    case 'edit-note':
    case 'del-note':
    case 'show-note':
    case 'hide-note':
    case 'del-all-note':
      operateNote(menu, row);
      break;
  }
};
const annotationsObj = {
  noteFeild: {
    1: 'constructPostil',
    2: 'singlePostil',
    3: 'unitPostil',
  },
  showFeild: {
    1: 'constructPostilState',
    2: 'singlePostilState',
    3: 'unitPostilState',
  },
};
let delNoteMol = ref({
  visible: false,
  dataStatus: null,
});
let isChangePage = ref([]);
const operateNote = (menu, row) => {
  console.log('operateNote', menu, row);
  let levelType = projectStore.currentTreeInfo?.levelType;
  let noteField = annotationsObj['noteFeild'][levelType];
  let showField = annotationsObj['showFeild'][levelType];
  let formData;
  switch (menu.code) {
    case 'add-note':
      row.annotations = '';
      row.annotationsVisible = true;
      row.noteEditVisible = true;
      break;
    case 'edit-note':
      row.noteEditVisible = true;
      row.annotationsVisible = true;
      break;
    case 'del-note':
      row.annotations = null;
      row.annotationsVisible = false;
      row[noteField] = null;
      if (levelType === 3) {
        upDate(row, noteField);
      } else {
        formData = getParamsData({
          type: levelType,
          sequenceNbr: row.sequenceNbr,
          postil: row[noteField],
        });
        console.log('updatePostil-formData', formData);
        csProject.updatePostil(formData).then(res => {
          console.log('updatePostil', res, formData);
        });
      }
      break;
    case 'show-note': //尝显示批注
    case 'hide-note': //隐藏批注
      row.isShowAnnotations = menu.code === 'show-note';
      row.annotationsVisible = menu.code === 'show-note';
      row[showField] = menu.code === 'show-note' ? 1 : 0;
      if (levelType === 3) {
        if (menu.code === 'show-note') {
          isChangePage.value.push(row.sequenceNbr);
        } else {
          let idx = isChangePage.value.findIndex(a => a === row.sequenceNbr);
          isChangePage.value.splice(idx, 1);
        }
        upDate(row, showField);
      } else {
        //工程和单项不设置
        // formData = getParamsData({
        //   sequenceNbr: row.sequenceNbr,
        //   state: row[showField],
        //   type: levelType,
        // });
        // console.log('updatePostilState-formData', formData);
        // csProject.updatePostilState(formData).then(res => {
        //   console.log('updatePostilState', res, formData);
        // });
      }
      break;
    case 'del-all-note':
      if (levelType === 1) {
        infoMode.show({
          isSureModal: false,
          iconType: 'icon-querenshanchu',
          infoText: '是否删除所有批注？',
          confirm: async () => {
            delAllNote(1);
            infoMode.hide();
          },
          close: () => {
            infoMode.hide();
          },
        });
      } else {
        delNoteMol.value.visible = true;
        delNoteMol.value.dataStatus = levelType + '';
      }
      break;
  }
};
const delAllNote = levelType => {
  let levelTypeTrue = projectStore.currentTreeInfo?.levelType;
  let noteField = annotationsObj['noteFeild'][levelTypeTrue];
  let showField = annotationsObj['showFeild'][levelTypeTrue];
  if (levelTypeTrue < 3) {
    //单项+工程项目将所有数据的批注信息清空
    tableData.value.map(a => {
      a[showField] = null;
      a[noteField] = null;
      a.annotations = null;
      a.isShowAnnotations = null;
      a.annotationsVisible = false;
    });
  }
  // debugger;
  let formData = {
    type: levelType,
    constructId:
        projectStore.currentTreeInfo.levelType === 1
            ? projectStore.currentTreeInfo?.id
            : projectStore.currentTreeGroupInfo?.constructId,
  };
  if (levelType === 3) {
    formData = getParamsData(formData);
  } else if (levelType === 2) {
    formData.singleId = projectStore.currentTreeInfo?.id;
  }
  console.log('deletePostilState-formData', formData);
  csProject.deletePostilState(formData).then(res => {
    console.log('deletePostilState', res, formData);
    if (levelTypeTrue === 3) getHumanMachineData();
    delNoteMol.value.visible = false;
  });
};
const getParamsData = data => {
  let apiData = { ...data };
  apiData.constructId =
      projectStore.currentTreeInfo.levelType === 1
          ? projectStore.currentTreeInfo?.id
          : projectStore.currentTreeGroupInfo?.constructId;
  if (projectStore.currentTreeInfo.levelType === 2) {
    apiData.singleId = projectStore.currentTreeInfo?.id; //单项ID
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  return apiData;
};
// 清除载价格
const clearZaijia = (data) => {
  const {
    defaultFeeFlag: { frequencyList },
  } = projectStore.asideMenuCurrentInfo || {};
  const { num = null } = projectStore.currentStageInfo || {};
  let postData = {
    // type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
    type: projectStore.currentTreeInfo.levelType ,
    rcj: { ...JSON.parse(JSON.stringify(data)) },
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
    sequenceNbr: data.sequenceNbr,
    num: frequencyList && frequencyList.length ? num : null,
  };
  if(projectStore.currentTreeInfo.levelType === 2){
    postData['singleId']=projectStore.currentTreeGroupInfo?.singleId
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    postData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    postData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }

  console.log(
    "🚀 ~ file: index.vue:768 ~ csProject.clearLoadPriceUse ~ postData:",
    postData
  );
  jieSuanDetail.jiesuanClearLoadPriceUse(postData).then((res) => {
    console.log("🚀 ~ file: index.vue:760 ~ csProject.clearLoadPriceUse ~ res:", res);
    if (res.result) {
      message.success("清除成功");
      if ([1,2].includes(projectStore.currentTreeInfo.levelType)) {
        data.isChange = true; //标识编辑行
        // getSameUnit();
        // let upDateList = getPropData();
        // console.log('upDateList', upDateList);
        // if (upDateList && upDateList.length > 0) {
        projectStore.SET_HUMAN_UPDATA_DATA({
          isEdit: true,
          name: "unify-humanMachineSummary",
          // updataData: upDateList,
          updataData: [],
        });
        // }
        unifyData.disabled = false;
      }
      getInitList();
    }
  });
};

const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log("newValue", newValue, "row", row, "name", name, "index", index);
  // saveOrUpdateFeature({ data: featureData.value }, 0);
};
const clear = () => {
  //清除编辑状态
  const $table = humanTable.value;
  $table.clearEdit();
};

const scrollTo = (x, y) => {
  vexTable.value.scrollTo(0, vexTable.value.getScroll().scrollTop + y);
};
const getSelectData = (val) => {
  console.log("selectData", val, currentInfo.value);
  selectData.value = val;
};

// 设置主要材料
let materialVisible = ref(false);
let allRCJTableData = ref([]);
const openSetMainMaterial = () => {
  materialVisible.value = true;
};
const closeSetMainMaterial = () => {
  materialVisible.value = false;
};

// 人材机索引
let indexVisible = ref(false);
// 人材机索引数据loading
let indexLoading = ref(false);
/**
 * 菜单右键替换数据处理
 */
const menuReplaceHandler = () => {
  indexVisible.value = true;
};
/**
 * 关闭替换人材机索引
 */
const closeReplaceRCJ = () => {
  indexVisible.value = false;
};

/**
 * 数据替换
 * replaceRcj 被替换的人材机
 * targetRcj 目标人材机
 */
const currentInfoReplace = (targetInfo) => {
  const params = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id, //单位ID
    replaceRcj: JSON.parse(JSON.stringify(currentInfo.value)),
    targetRcj: JSON.parse(JSON.stringify(targetInfo)),
  };
  api.replaceRcjToUnit(params).then((res) => {
    console.log("人材机数据替换", params, res);
    if (res.status === 200) {
      message.success("替换成功!");
      getHumanMachineData();
      closeReplaceRCJ();
    }
  });
};
const limitNum = value => {
  if (typeof value !== 'string') return value;
  return value.replace(/[^(-?\d+)\.?(\d*)$]/g, '');
}; //限制数字
const numLimit = {
  //限制小数位数
  price: {
    list: [
      //合同内
      'jieSuanMarketPrice',
      // 'priceMarketTax',
      // 'priceMarket',
      // 'priceMarketTax',
      // 'priceMarket',
      'priceBaseJournalTax',
      'priceBaseJournal',
      //合同外
      'marketPrice',
      'priceMarketTax',
      'priceMarket',
      'jieSuanPriceMarketTax',
      'jieSuanPriceMarket',
    ],
    code: costPriceFormat,
  },
  amount: {
    list: [
        // 'totalNumber', 'donorMaterialNumber', 'taxRemoval',
        //合同内
      'donorMaterialNumber','jieSuanBasePriceF0','jieSuanCurrentPriceFt','taxRemoval',
    ],
    code: rcjSummaryAmountFormat,
  },
  rate: {
    list: [
        // 'riskCoefficient', 'taxRate', 'transferFactor',jieSuanTaxRemoval
        'jieSuantaxRate','taxRate',
    ],
    code: rateFormat,
  },
};
const editClosedEvent = (e) => {
  const { $table, row, column } = e;
  const field = column.field;

  // 市场价以前是数字类型，编辑框后返回的是字符串，需要处理
  if (["jieSuanMarketPrice"].includes(field)) {
    row[field] = +row[field];
  }

  let value = row[field];
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  switch (field) {
    case "unit":
    case "materialName":
    case "specification":
    case "producer":
    case "manufactor":
    case "brand":
    case "deliveryLocation":
    case "qualityGrade":
      //输入长度不超过2000字符
      if (value && value.length > 2000) {
        message.warn("输入内容长度应在2000字符以内");
        row[field] = value.slice(0, 2000);
      }

      break;
  }
  // 小数位处理
  let limitList = [
    ...numLimit.price.list,
    ...numLimit.amount.list,
    ...numLimit.rate.list,
  ];
  if (limitList.includes(field)) {
    //数量+金额+税率限制
    row[field] = limitNum(row[field]); //数字
    for (let a in numLimit) {
      if (numLimit[a]['list'].includes(field)) {
        row[field] = numLimit[a]['code'](row[field]);
      }
    }
  }

  if (field === "jieSuanMarketPrice" && value < 0) {
    $table.revertData(row, field);
  } else if (field === "jieSuanMarketPrice" && value > 0 && row.jieSuanMarketPrice.length > 20) {
    row.jieSuanMarketPrice = value.slice(0, 20);
  }
  if (
    field === "jieSuanMarketPrice" &&
    value > 0 &&
    [1,2].includes(projectStore.currentTreeInfo.levelType)
  ) {
    row.total = (row.totalNumber * value).toFixed(2);
    row.priceDifferenc = (Number(row.jieSuanMarketPrice) - Number(row.dePrice)).toFixed(2);
    row.priceDifferencSum = (row.totalNumber * row.priceDifferenc).toFixed(2);
  }
  if (field === "donorMaterialNumber") {
    if (row.ifDonorMaterial === 1 && value > row.totalNumber) {
      $table.revertData(row, field);
      message.warn(`甲供数量>数量，请重新输入`);
    } else if (row.ifDonorMaterial !== 1 && value > row.totalNumber) {
      $table.revertData(row, field);
      message.warn(`甲供数量>数量，请重新输入`);
    } else if (row.ifDonorMaterial !== 1 && value <= row.totalNumber && value > 0) {
      row.ifDonorMaterial = 1;
    } else if (row.ifDonorMaterial === 1 && (value <= 0 || value === "")) {
      row.ifDonorMaterial = 0;
      row.donorMaterialNumber = "";
    }
  }
  if (projectStore.currentTreeInfo.levelType !== 3) {
    row.isChange = true; //标识编辑行
    getSameUnit();
    let upDateList = getPropData();
    if (upDateList && upDateList.length > 0) {
      projectStore.SET_HUMAN_UPDATA_DATA({
        isEdit: true,
        name: "unify-humanMachineSummary",
        updataData: upDateList,
      });
    }
  }
  upDate(row, field);
};

//市场价-税率之间的联动修改
const linkageRevisePrice = (row, field, value) => {
  if (['priceMarket', 'priceMarketTax', 'taxRate'].includes(field)) {
    if (field === 'priceMarket' && !ObjectUtils.is_Undefined(value)) {
      //不含税市场价
      row.priceMarketTax = NumberUtil.numberScale2(
          NumberUtil.multiply(
              0.01,
              NumberUtil.multiply(value, NumberUtil.add(100, row.taxRate))
          )
      );
    }
    //含税市场价
    if (field === 'priceMarketTax' && !ObjectUtils.is_Undefined(value)) {
      //不含税市场价
      row.priceMarket = NumberUtil.numberScale2(
          NumberUtil.multiply(
              100,
              NumberUtil.divide(value, NumberUtil.add(100, row.taxRate))
          )
      );
    }

    //税率
    if (field === 'taxRate' && !ObjectUtils.is_Undefined(value)) {
      //含税市场价
      if (+projectStore.taxMade === 1) {
        row.priceMarketTax = NumberUtil.numberScale2(
            NumberUtil.multiply(
                0.01,
                NumberUtil.multiply(row.priceMarket, NumberUtil.add(100, value))
            )
        );
      } else {
        row.priceMarket = NumberUtil.numberScale2(
            NumberUtil.multiply(
                100,
                NumberUtil.divide(row.priceMarketTax, NumberUtil.add(100, value))
            )
        );
      }
    }
  }
};

const updateTreeVal = (max,min) => {
  treeMaxVal.value=max
  treeMinVal.value=min
  let upDateList = getPropData();
  if (projectStore.currentTreeInfo.levelType !== 3) {
    projectStore.SET_HUMAN_UPDATA_DATA({
      isEdit: true,
      name: "unify-humanMachineSummary",
      updataData: upDateList,
    });
  }
  riskVisible.value = false;
  isEditCurrentData.value = false;
}
const updateCurrentInfo = (formData) => {
  currentInfo.value.riskAmplitudeRangeMax = formData.riskAmplitudeRangeMax;
  currentInfo.value.riskAmplitudeRangeMin = formData.riskAmplitudeRangeMin;
  if (projectStore.currentTreeInfo.levelType === 3) {
    upDate(currentInfo.value, "riskAmplitudeRangeMin");
  }else{
    // $table, row, column
    editClosedEvent({$table:humanTable.value,row:currentInfo.value,column:{field:'riskAmplitudeRangeMax'}})
    editClosedEvent({$table:humanTable.value,row:currentInfo.value,column:{field:'riskAmplitudeRangeMin'}})
    riskVisible.value = false;
    isEditCurrentData.value = false;
  }
};
const getPropData = () => {
  let constructProjectRcjList = [];
  let upDateList = tableData.value.filter((item) => item.isChange === true);
  console.log("upDateList", upDateList, oldData.value);
  upDateList.map((item) => {
    let obj = {};
    let same = oldData.value.filter((l) => l.sequenceNbr === item.sequenceNbr)[0];
    if (item.jieSuanMarketPrice !== same.jieSuanMarketPrice) {
      obj.jieSuanMarketPrice = item.jieSuanMarketPrice;
    }
    if (
      item.ifDonorMaterial != same.ifDonorMaterial ||
      item.donorMaterialNumber != same.donorMaterialNumber
    ) {
      obj.ifDonorMaterial = item.ifDonorMaterial;
      if (obj.ifDonorMaterial === 1) {
        obj.donorMaterialNumber = item.totalNumber;
      } else {
        obj.donorMaterialNumber = "";
      }
    }
    if (item.jieSuanBasePriceF0 != same.jieSuanBasePriceF0) {
      obj.jieSuanBasePriceF0 = item.jieSuanBasePriceF0;
    }
    if (item.jieSuanCurrentPriceF0 != same.jieSuanCurrentPriceF0) {
      obj.jieSuanCurrentPriceF0 = item.jieSuanCurrentPriceF0;
    }
    if (item.jieSuanCurrentPriceFt != same.jieSuanCurrentPriceFt) {
      obj.jieSuanCurrentPriceFt = item.jieSuanCurrentPriceFt;
    }
    if (item.jieSuanBasePrice != same.jieSuanBasePrice) {
      obj.jieSuanBasePrice = item.jieSuanBasePrice;
    }
    if (item.marketPrice != same.marketPrice) {
      obj.marketPrice = item.marketPrice;
    }
    if (item.riskAmplitudeRangeMin != same.riskAmplitudeRangeMin) {
      obj.riskAmplitudeRangeMin = item.riskAmplitudeRangeMin;
    }
    if (item.riskAmplitudeRangeMax != same.riskAmplitudeRangeMax) {
      obj.riskAmplitudeRangeMax = item.riskAmplitudeRangeMax;
    }
    if (item.taxRemoval != same.taxRemoval) {
      obj.taxRemoval = item.taxRemoval;
    }
    if (item.jieSuanAdminRate != same.jieSuanAdminRate) {
      obj.jieSuanAdminRate = item.jieSuanAdminRate;
    }
    if (item.jieSuanFee != same.jieSuanFee) {
      obj.jieSuanFee = item.jieSuanFee;
    }
    if (item.remark != same.remark) {
      obj.remark = item.remark;
    }
    if (item.ifLockStandardPrice != same.ifLockStandardPrice) {
      obj.ifLockStandardPrice = item.ifLockStandardPrice;
    }
    if (item.isDifference != same.isDifference) {
      obj.isDifference = item.isDifference;
    }
    if (item.priceBaseJournalTax != same.priceBaseJournalTax) {
      obj.priceBaseJournalTax = item.priceBaseJournalTax;
    }
    if (item.materialName != same.materialName) {
      obj.materialName = item.materialName;
    }
    if (item.specification != same.specification) {
      obj.specification = item.specification;
    }
    if (item.unit != same.unit) {
      obj.unit = item.unit;
    }
    if (item.jieSuantaxRate != same.jieSuantaxRate) {
      obj.jieSuantaxRate = item.jieSuantaxRate;
    }
    if (item.taxRate != same.taxRate) {
      obj.taxRate = item.taxRate;
    }
    if (item.producer != same.producer) {
      obj.producer = item.producer;
    }
    if (item.manufactor != same.manufactor) {
      obj.manufactor = item.manufactor;
    }
    if (item.brand != same.brand) {
      obj.brand = item.brand;
    }
    if (item.deliveryLocation != same.deliveryLocation) {
      obj.deliveryLocation = item.deliveryLocation;
    }
    if (item.qualityGrade != same.qualityGrade) {
      obj.qualityGrade = item.qualityGrade;
    }
    if (item.priceMarket != same.priceMarket) {
      obj.priceMarket = item.priceMarket;
    }
    if (item.priceMarketTax != same.priceMarketTax) {
      obj.priceMarketTax = item.priceMarketTax;
    }
    if (item.priceBaseJournal != same.priceBaseJournal) {
      obj.priceBaseJournal = item.priceBaseJournal;
    }
    if (item.jieSuanPriceMarket != same.jieSuanPriceMarket) {
      obj.jieSuanPriceMarket = item.jieSuanPriceMarket;
    }
    if (item.jieSuanPriceMarketTax != same.jieSuanPriceMarketTax) {
      obj.jieSuanPriceMarketTax = item.jieSuanPriceMarketTax;
    }
    if (
      obj.hasOwnProperty("ifDonorMaterial") &&
      !obj.hasOwnProperty("donorMaterialNumber")
    ) {
      obj.donorMaterialNumber = obj.ifDonorMaterial === 1 ? item.totalNumber : "";
    }
    obj.sequenceNbr = item.sequenceNbr;
    constructProjectRcjList.push(obj);
  });
  return constructProjectRcjList;
};
const isUse = async () => {
  //点击统一应用按钮
  if (!projectStore.humanUpdataData) {
    return;
  }

  // let tar = apiData.constructProjectRcjList[0];
  // if (
  //   Object.keys(tar).length === 1 &&
  //   apiData.constructProjectRcjList.length === 1
  // ) {
  //   apiData.constructProjectRcjList = [];
  // }

  //只是清除载价就传空值，清除载价+改市场价传修改数据
  setGlobalLoading(true, "统一应用中，请稍后...");
  // if (projectStore.humanUpdataData.adjustFactor?.isEdit) {
  //   let apiData = {
  //     constructId: projectStore.currentTreeGroupInfo?.constructId,
  //     coefficient: projectStore.humanUpdataData.adjustFactor.marcketFactor * 1,
  //     rcjList: JSON.parse(
  //       JSON.stringify(projectStore.humanUpdataData.adjustFactor.selectRows)
  //     ),
  //   };
  //   await csProject.constructAdjustmentCoefficient(apiData).then((res) => {
  //     console.log("统一应用系数", res);
  //   });
  // }
  if (projectStore.humanUpdataData.updataData) {
    // let apiData = {
    //   levelType: projectStore.currentTreeInfo.levelType,
    //   constructId: projectStore.currentTreeGroupInfo?.constructId,
    //   constructProjectRcjList: JSON.parse(
    //     JSON.stringify(projectStore.humanUpdataData.updataData)
    //   ),
    //   isFlag:true
    // };
    // if (projectStore.currentTreeInfo.levelType === 2) {
    //   apiData.singleId = projectStore.currentTreeInfo?.id; //单项ID
    // }
    // if(projectStore.asideMenuCurrentInfo?.key!='0'){
    //   apiData['kind']=projectStore.asideMenuCurrentInfo?.defaultFeeFlag.kind
    // }
    let apiData = {
      levelType: projectStore.currentTreeInfo.levelType,
      constructId: projectStore.currentTreeGroupInfo?.constructId
    };
    await jieSuanDetail.unifyUse(apiData).then((res) => {
      console.log("统一应用接口返回结果", res);
    });
  }
  if(treeMaxVal.value&&treeMinVal.value){
     let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      tcType: Number(projectStore.asideMenuCurrentInfo?.key),
      list: null,
      max: treeMaxVal.value,
      min: treeMinVal.value,
      levelType:projectStore.currentTreeInfo.levelType
    };
    if(projectStore.currentTreeInfo.levelType==2){
      apiData['singleId']=projectStore.currentTreeGroupInfo?.singleId
    }
    console.log('apiData', apiData);
    jieSuanDetail.constructRiskAmplitudeRangeController(apiData).then(res => {});
  }
  message.success("应用成功!");
  projectStore.SET_HUMAN_UPDATA_DATA(null);
  // getHumanMachineData();
  selectData.value = [];
  projectRcjList();
  unifyData.disabled = true;
  setGlobalLoading(false);
  humanTable.value.clearCheckboxRow();// 清空选中状态
};

const upDate = (row, field) => {
  let apiData = {
    levelType: projectStore.currentTreeInfo.levelType,
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
    sequenceNbr: row.sequenceNbr,
    pointLine:JSON.parse(JSON.stringify(row)),
    constructProjectRcj: {},
    adjustMethod: projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.adjustMethod,
    num:null,
  };
  if (projectStore.currentStageInfo) {
    apiData.num = projectStore.currentStageInfo?.num;
  }
  if (field === "type") {
    apiData.constructProjectRcj.kind = getKind(row.type);
  } else if (field === "ifDonorMaterial" && row.ifDonorMaterial === 1) {
    apiData.constructProjectRcj.ifDonorMaterial = row[field];
    apiData.constructProjectRcj.donorMaterialNumber = row.totalNumber;
  } else if (field === "ifDonorMaterial" && row.ifDonorMaterial === 0) {
    apiData.constructProjectRcj.ifDonorMaterial = row[field];
  } else if (field === "isDifference") {
    apiData.constructProjectRcj.isDifference = row[field];
  } else if (field === "riskAmplitudeRangeMin") {
    apiData.constructProjectRcj.riskAmplitudeRangeMax = row.riskAmplitudeRangeMax;
    apiData.constructProjectRcj.riskAmplitudeRangeMin = row.riskAmplitudeRangeMin;
  } else {
    apiData.constructProjectRcj[field] = row[field];
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }else{
    let obj=[{'sequenceNbr':row.sequenceNbr}]
    obj[0][field]=row[field]
    apiData['constructProjectRcjList']=obj
  }
  if (projectStore.currentTreeInfo.levelType === 2) {
    apiData['singleId']=projectStore.currentTreeGroupInfo?.singleId
  }
  if(projectStore.asideMenuCurrentInfo?.key!='0'){
    apiData['kind']=projectStore.asideMenuCurrentInfo?.defaultFeeFlag.kind
  }
  console.log("修改人材机数据传参", apiData);
  jieSuanDetail.changeRcjNewJieSuan(apiData).then((res) => {
    if (res.status === 200) {
      console.log("修改人材机数据返回结果", res);
      isCurrent.value = row;
      if (field === "riskAmplitudeRangeMin") {
        riskVisible.value = false;
        isEditCurrentData.value = false;
      }
      // if (projectStore.currentTreeInfo?.originalFlag) {
      //   priceDifferenceAdjustmentMethodController(
      //     projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.adjustMethod
      //   );
      // }
      getInitList();
      emits("updateMenuList");
      updateValue();
    }
  });
};

// =====================查找逻辑
let lookupVisible = ref(false);
const openLookup = (event) => {
  // console.log(event);
  if (event) {
    if (event.ctrlKey && event.code === "KeyF") {
      lookupVisible.value = true;
    }
  } else {
    lookupVisible.value = true;
  }
};
let originalTableData = []; // 查找之前的源数据
const lookupConfig = reactive({
  columns: [
    {
      field: "materialName",
      label: "名称",
      type: "input",
      value: "",
      config: {
        allowClear: true,
      },
    },
    {
      field: "specification",
      label: "规格",
      type: "input",
      value: "",
      config: {
        allowClear: true,
      },
    },
    {
      field: "unit",
      label: "单位",
      type: "select",
      value: null,
      config: {
        options: (projectStore.unitListString || []).split(",").map((item) => {
          return { label: item, value: item };
        }),
        allowClear: true,
      },
    },
    {
      field: "materialCode",
      label: "编码",
      type: "input",
      value: "",
      config: {
        allowClear: true,
      },
    },
    {
      field: "jieSuanMarketPrice",
      label: "市场价",
      type: "input",
      value: "",
      showCondition: true,
      conditionVal: "",
      config: {
        allowClear: true,
      },
    },
    {
      field: "totalNumber",
      label: "数量",
      type: "input",
      value: "",
      showCondition: true,
      conditionVal: "",
      config: {
        allowClear: true,
      },
    },
  ],
  conditionType: "&&",
  tableData: tableData,
});
const lookupCallback = (rows) => {
  if (!rows || !rows.length) {
    tableData.value = xeUtils.clone(originalTableData, true);
  } else {
    tableData.value = rows;
  }
  nextTick(() => {
    const info = tableData.value && tableData.value[0];
    humanTable.value.setCurrentRow(info);
    currentInfo.value = info;
    isCurrent.value = info;
  });
};

const changeCurrentInfo = (row) => {
  if (row) {
     humanTable.value.setCurrentRow(row);
     currentInfo.value = row;
     isCurrent.value = row;
  }
};

const exportExcel = (dataType = "") => {
  const $table = humanTable.value;
  if (dataType !== "all" && $table.getCheckboxRecords().length === 0) {
    message.info("请选择导出数据");
    return;
  }
  $table.exportData({
    filename: "人材机汇总导出报表",
    sheetName: "Sheet1",
    type: "xlsx",
    // types: ['xlsx', 'csv', 'html', 'xml', 'txt'],
    // sheetMethod: sheetMethod, // 配置导出样式
    useStyle: true, //是否导出样式 // 如果没有设置这项 就不会调用sheetMethod方法
    isFooter: true, //是否导出表尾（比如合计）
    data: dataType === "all" ? tableData.value : $table.getCheckboxRecords(),
    columnFilterMethod({ column, $columnIndex }) {
      return !($columnIndex === 0);
    },
  });
};

const loadPriceOperateHandler = () => {
  const { adjustMethod, frequencyList } =
    projectStore.asideMenuCurrentInfo?.defaultFeeFlag || {};
  console.log(
    "projectStore.asideMenuCurrentInfo",
    projectStore?.asideMenuCurrentInfo,
    projectStore?.currentTreeInfo
  );
  const { num } = projectStore.currentStageInfo || {};
  const { key } = projectStore.asideMenuCurrentInfo || {};
  const { isStage, levelType, originalFlag } = projectStore.currentTreeInfo || {};

  updateOperateByName("batch-loadprice", (info) => {
    info.type = "select";
    info.disabled = false;
    if (isOriginalFlag()) {
      info.options = [
        {
          type: 1,
          name: "批量载入结算价",
          kind: "1",
          isValid: true,
        },
        {
          type: 2,
          name: "基期价格批量载价",
          kind: "2",
          isValid: true,
        },
      ];
    } else {
      info.options = [
        {
          type: 3,
          name: "批量载入合同确认价",
          kind: "3",
          isValid: true,
        },
      ];
    }
    info.options.forEach((opt) => {
      opt.isValid = true;
    });
    if (adjustMethod === 3) {
      info.options[1].isValid = false;
    }
    if (
      adjustMethod === 4 ||
      (frequencyList && frequencyList.length && !num) || // 有分期没有选择分期数
      (key === "0" && contractOriginalFlag.value) ||
      key === "20" || // 差价
      (isStage && levelType === 1 && key === "0")|| // 分期&&工程项目级别&合同内所有
      tableData.value.length==0 //列表为空
    ) {
      info.options.forEach((opt) => {
        opt.isValid = false;
      });
    }
    //暂估价材料调差 ，基期价载价置灰
    if(['8'].includes(String(key))){
      info.options.forEach((opt) => {
        if( opt.kind === '2'){
          opt.isValid = false;
        }
      });
    }
  });
};
const loadPrice = (item) => {
  const { name, activeKind } = item;
  priceType.value = Number(activeKind);
  // reportModel.value = false;
  switch (name) {
    case "batch-loadprice":
      typeModal.value = "批量载价";
      break;
    case "loadprice-report":
      typeModal.value = "载价报告";
      break;
  }
  setTimeout(() => {
    reportModel.value = true;
    console.log("执行loadPrice", reportModel.value);
  }, 24);
};
const close = (bol) => {
  reportModel.value = false;
  console.log("执行close", reportModel.value);
};
const nextEdit = (data) => {
  propsData.value = data;
  typeModal.value = data.nextType;
  reportModel.value = true;
  if (typeModal.value === "载价报告") {
    if (projectStore.currentTreeInfo.levelType !== 3) {
      projectStore.SET_HUMAN_UPDATA_DATA({
        isEdit: true,
        name: "unify-humanMachineSummary",
        updataData: [],
      });
    }
    getInitList();
  }
  console.log("执行nextEdit", reportModel.value);
};
provide("nextStep", nextEdit);
let adjustFactor = ref(false); //调整市场价系数
let marcketFactor = ref("1");
const selfCheck = (value, length, min, max) => {
  // length-小数点长度   min-最小值  max-最大值
  let newValue = value * 1 + "";
  if (newValue === "") return oldMarketFactor.value;
  if (newValue <= min || newValue > max) {
    newValue = oldMarketFactor.value;
    message.info("市场价系数输入范围为(0,1000])");
  }
  let after = newValue.split(".")[1];
  if (after > 0 && length > 0 && after.length > length) {
    newValue = parseFloat(newValue).toFixed(length);
  }
  oldMarketFactor.value = newValue;
  return newValue;
};
const changeMarketFactor = () => {
  //调整市场价系数
  let apiData = {
    // constructId: projectStore.currentTreeGroupInfo?.constructId,
    // coefficient: projectStore.humanUpdataData.adjustFactor.marcketFactor * 1,
    coefficient: marcketFactor.value * 1,
    // rcjList: JSON.parse(
    //   JSON.stringify(projectStore.humanUpdataData.adjustFactor.selectRows)
    // ),
    rcjList: JSON.parse(JSON.stringify(humanTable.value.getCheckboxRecords())),
    levelType: projectStore.currentTreeInfo.levelType
  };
  apiData = getParamsData(apiData);
  console.log("调整市场价系数", apiData);
  // let apiName = 'adjustmentCoefficient'
  //   projectStore.currentTreeInfo.levelType === 3 ? "unitAdjustmentCoefficient" : "";
  csProject['adjustmentCoefficient'](apiData).then((res) => {
    if (res.status === 200) {
      console.log("调整市场价系数返回结果", res);
    }
  });
};
const sureOrCancel = () => {
  let hasCheck = humanTable.value.getCheckboxRecords().length === 0 ? false : true;
  if (!hasCheck) {
    infoMode.show({
      isSureModal: true,
      iconType: "icon-querenshanchu",
      infoText: "请选中要调整的人材机数据行",
      confirm: () => {
        infoMode.hide();
      },
    });
  }
  if (!hasCheck) return;
  changeMarketFactor();
  if (projectStore.currentTreeInfo.levelType === 3 ) {
    unitRcjQuery();
    humanTable.value.clearCheckboxRow();// 清空选中状态
  }else{
    let selectRows = humanTable.value.getCheckboxRecords();
    selectRows.map(row => {
      if (
          !(
              (row.markSum === 1 && [1, 2].includes(Number(row.levelMark))) ||
              row.isFyrcj === 0 ||
              [
                'QTCLFBFB',
                '34000001-2',
                'J00004',
                'J00031',
                'J00031',
                'C11384',
                'C00007',
                'C000200',
                'C11408',
              ].includes(row.materialCode)
          )
      ) {
        row.isChange = true; //标识编辑行
        row.isUpdateNum = true; //调整市场价系数
        row.sourcePrice = '自行询价';
      }
    });
    getSameUnit();
    let upDateList = getPropData();
    if (upDateList && upDateList.length > 0) {
      // setProUpdate(upDateList);
      projectStore.SET_HUMAN_UPDATA_DATA({
        isEdit: [1,2].includes(projectStore.currentTreeInfo.levelType) ? true : false,
        name: "unify-humanMachineSummary",
        updataData: upDateList,
        adjustFactor: {
          isEdit: [1,2].includes(projectStore.currentTreeInfo.levelType) ? true : false,
          marcketFactor: marcketFactor.value,
          selectRows: humanTable.value.getCheckboxRecords(),
        },
      });
    }
    changeMarketFactor();
    projectRcjList();
  }
  adjustFactor.value = false;
};
let oldMarketFactor = ref("1"); //调整市场价系数旧值
const jsz = () => {
  marcketFactor.value = "1";
  oldMarketFactor.value = "1";
  adjustFactor.value = true;
};
/**
 * 设置主要材料显示隐藏
 */
const showSetMainMaterial = () => {
  let mainMaterial = operateList.value.find((item) => item.name === "set-main-materials");
  mainMaterial.levelType = Number(projectStore.asideMenuCurrentInfo.key) === 7 ? [3] : [];
};

const {
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
  setHeaderCellClassName,
} = useFormatTableColumns({
  type: 'jieSuan',
  initCallback: () => {},
  initColumnsCallback: () => {
    initColumns({
      columns: humanShowTableColumns.value,
      pageName: `rcjtz_${projectStore.currentTreeInfo.levelType}_${projectStore.deStandardReleaseYear}`,
    });
  },
});

let humanShowTableColumns = ref([]); // 选中子菜单数据需要展示的列
watch(
  () => [projectStore.asideMenuCurrentInfo, projectStore.currentTreeInfo.levelType],
  () => {
    if (projectStore.tabSelectName === "人材机调整" &&
      !projectStore.currentStageInfo
    ) {
      if(projectStore.currentTreeInfo.levelType<3 && contractOriginalFlag.value){
        getIsStage()
      }
      // isChangePage.value = [];
      getInitColumns();
      initOperateList();
      findUnitProjectById();
      if ([1,2].includes(projectStore.currentTreeInfo.levelType)&&contractOriginalFlag.value) {
        jieSuanConstructProjectFq();
      }
      //侧边栏数据变化重新更新
      // getHumanMachineData();
      getInitList();
      selectData.value = [];
      if (["1", "2", "3"].includes(projectStore.asideMenuCurrentInfo.key)) {
        updateValue();
      }
      // 动态表格列初始化设置
      // initColumns({
      //   columns: contractOriginalFlag.value
      //     ? humanShowTableColumns.value
      //     : getTableColumns(),
      //   pageName: 'htnrcjtz',
      // });
    } else if (projectStore.tabSelectName === "人材机调整") {
      initOperateList();
    }
    console.log("侧边栏数据变化重新更新", projectStore?.currentTreeInfo);
    let newSelectList = [
      {
        name: "计取税金",
        code: 1,
      },
      {
        name: "计取规费、税金",
        code: 2,
      },
      {
        name: "计取安文费、税金",
        code: 3,
      },
    ];
    if (projectStore?.currentTreeInfo?.deStandardReleaseYear === "22") {
      newSelectList = newSelectList.filter((item) => {
        return item.code !== 2;
      });
      selectList.value = newSelectList;
    } else {
      selectList.value = newSelectList;
    }
    if(['1','2'].includes(String(projectStore?.currentTreeInfo?.levelType))){
      selectdTab.value = 'lyfx';
    }
  }
);
watch(
  () => projectStore.humanUpdataData,
  () => {
    if (projectStore.humanUpdataData &&
      projectStore.tabSelectName === "人材机调整" && 
      projectStore.humanUpdataData.isEdit) {
      unifyData.disabled = false;
    }
  }
);

watch(
  () => projectStore.currentStageInfo,
  () => {
    if(projectStore.tabSelectName === "人材机调整"){
      getInitColumns();
      initOperateList();
      unitRcjQuery();
    }
  }
);
watch(
  () => projectStore.currentTreeInfo?.originalFlag,
  () => {
    if(projectStore.tabSelectName === "人材机调整"){
      initOperateList();
    }
  }
);
// () => [projectStore.asideMenuCurrentInfo, projectStore.currentTreeInfo.levelType],
//   () => {
//     if (projectStore.tabSelectName === "人材机调整" &&
//       !projectStore.currentStageInfo
    // ) {

// watch(
//   () => projectStore.asideMenuCurrentInfo.newId,
//   () => {
    
//   }
// );
let importExcelRef = ref();
const importExcelHandle = () => {
  importExcelRef.value.open();
};
const updateImportData = updateRcj => {
  // 这是预算中处理的做法
  // for (let rcj of updateRcj) {
  //   let data = tableData.value.find(
  //       item => item.sequenceNbr === rcj.sequenceNbr
  //   );
  //   data[rcj.updatePrice] = rcj[rcj.updatePrice];
  //   data.sourcePrice = '线下数据导入';
  //   data.isImportDataType = true;
  //   editClosedEvent({
  //     $table: humanTable.value,
  //     row: data,
  //     column: { field: rcj.updatePrice },
  //   });
  // }
  // 这是结算中处理的方法
  getInitList();
};
onActivated(() => {
  loadPriceOperateHandler();
  initOperateList();
  insetBus(bus, projectStore.componentId, "humanMachineSummary", async (data) => {
    
    operateList.value.find(
      (item) => item.name === "rcj-adjustment"
    ).label = isDifference.value ? "人材机取消调差" : "人材机参与调差";
    if (data.name === "batch-loadprice") {
      console.log("执行载价", data);
      if (data.activeKind) {
        loadPrice(data);
      } else {
        loadPriceOperateHandler();
      }
    }
    if (data.name === "loadprice-report") console.log("载价报告"), loadPrice(data);
    if (data.name === "market-price") console.log("调整市场价系数"), jsz();
    if (data.name === "unify-humanMachineSummary") {
      isUse();
    }
    if (data.name === "export-table") console.log("导出报表"), exportExcel();
    if (data.name === "risk-range") riskVisible.value = true;
    if (data.name === "batch-adjustment-material") materialVisible.value = true;
    if (data.name === "set-main-materials") openSetMainMaterial();
    if (data.name === "lookup") openLookup();
    if (data.name === 'importExcel') {
      importExcelHandle();
    }
    if (data.name === "mergeMaterials") console.log("合并材料"), openMergeMaterials();
    if (data.name === "rcj-adjustment") {
      if (isDifference.value) {
        cancelRcjParticipateInAdjustment();
      } else {
        rcjParticipateInAdjustment();
      }
    }
    if (data.name === "corresponding-item") {
      let isCurrentList = tableData.value.filter(
        (item) => isCurrent.value.sequenceNbr === item.sequenceNbr
      );
      console.log("sicurrentlist", isCurrentList);
      quotaHeaderData.value = isCurrentList[0];
      quotaPopupVisible.value = true;
    }
    if (data.name === "settlement-adjustment-method") {
      console.log("结算调整法修改", data);
      if (data.activeKind) {
        if (projectStore.currentTreeInfo.levelType === 3) {
          priceDifferenceAdjustmentMethodController(data.activeKind);
        } else {
          constructPriceDifferenceAdjustmentMethodController(data.activeKind);
        }
      }
    }
  });
});
onMounted(() => {
  if (
    projectStore.tabSelectName === "人材机调整" &&
    projectStore.asideMenuCurrentInfo?.key === "0"
  ) {
    // tableData.value = [];
    // getHumanMachineData();
    getInitColumns();
    if(projectStore.currentTreeInfo.levelType<3 && contractOriginalFlag.value){
      getIsStage()
    }
    initOperateList();
    findUnitProjectById();
    getInitList();
    if (["1", "2", "3"].includes(projectStore.asideMenuCurrentInfo.key)) {
      updateValue();
    }
    setAggreUnitList.value = [];
  }
  // getLoadStatus();
});

// 页面列设置
let columnSettingRef = ref();
const showPageColumnSetting = () => {
  columnSettingRef.value.open();
};
function hasMatchingElement(str, arr) {
  // 遍历数组中的每个元素
  for (let element of arr) {
    // 检查字符串是否包含当前数组元素
    if (str.includes(element)) {
      return true; // 如果找到匹配，返回 true
    }
  }
  return false; // 如果遍历完数组没有找到匹配，返回 false
}
// 获取初始化表头列
const getInitColumns = () => {
  // projectStore?.currentStageInfo  当前点击的是第几期,如果点击的不是分期则为null
  // projectStore.asideMenuCurrentInfo.key  1:人工调差;2:材料调差;3:机械调差;4:设备;5:主材;6:预拌混凝土;8:暂估价材料调差;20:价差
  // projectStore.asideMenuCurrentInfo?.defaultFeeFlag.adjustMethod   4种调整法 1 造价信息价格差额调整法 2 结算价与基期价差额调整法 3 结算价与合同价差额调整法 4 价格指数差额调整法
  let isFq=projectStore.currentStageInfo?projectStore.currentStageInfo:2
  let pageNameSuffix=projectStore.currentTreeInfo.deStandardReleaseYear + projectStore.asideMenuCurrentInfo?.key + (projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.adjustMethod?projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.adjustMethod:projectStore.asideMenuCurrentInfo?.defaultFeeFlag) + isFq
  // 如果是单位工程
  if(projectStore.currentTreeInfo.levelType === 3){
    // 如果是合同内
    if (contractOriginalFlag.value) {
      // 如果是所有人材机
      if (projectStore.asideMenuCurrentInfo.key === "0") {
        humanShowTableColumns.value = getTableColumns().filter((x) =>
          x.type?.includes(0)&&(projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName: "htnrcjtz0_" + pageNameSuffix
        });
      } else if (
        // 如果是人材机调差并且点击的不是分期并且没有分期
        ["1", "2", "3"].includes(projectStore.asideMenuCurrentInfo.key) &&
        !projectStore?.currentStageInfo && (!projectStore.asideMenuCurrentInfo?.defaultFeeFlag.frequencyList||projectStore.asideMenuCurrentInfo?.defaultFeeFlag.frequencyList.length==0)
      ) {
        humanShowTableColumns.value = getTableColumns().filter(
          (x) =>
            (x.type?.includes(1) || x.type?.includes(2) || x.type?.includes(3)) &&
            x.adjustmentType?.includes(
              projectStore.asideMenuCurrentInfo?.defaultFeeFlag.adjustMethod
            ) &&
            x.stageType?.includes(0) &&
            x.titleType?.includes(0)&&(projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName:
            "htnrcjtz1_" + pageNameSuffix
        });
      } else if (
        // 如果是人材机调差并且点击的不是分期并且已经分期
        ["1", "2", "3"].includes(projectStore.asideMenuCurrentInfo.key) &&
        !projectStore?.currentStageInfo &&
        projectStore.asideMenuCurrentInfo?.defaultFeeFlag.frequencyList?.length > 0
      ) {
        humanShowTableColumns.value = getTableColumns().filter(
          (x) =>
            (x.type?.includes(1) || x.type?.includes(2) || x.type?.includes(3)) &&
            x.adjustmentType?.includes(
              projectStore.asideMenuCurrentInfo?.defaultFeeFlag.adjustMethod
            ) &&
            x.stageType?.includes(1) &&
            x.titleType?.includes(1)&&(projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName:
            "htnrcjtz2_" + pageNameSuffix
        });
      } else if (
        // 如果是人材机调差并且点击的是分期
        ["1", "2", "3"].includes(projectStore.asideMenuCurrentInfo.key) &&
        projectStore?.currentStageInfo
      ) {
        humanShowTableColumns.value = getTableColumns().filter(
          (x) =>
            (x.type?.includes(1) || x.type?.includes(2) || x.type?.includes(3)) &&
            x.adjustmentType?.includes(
              projectStore.asideMenuCurrentInfo?.defaultFeeFlag.adjustMethod
            ) &&
            x.stageType?.includes(1) &&
            x.titleType?.includes(2)&&(projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName:
            "htnrcjtz3_" + pageNameSuffix
        });
      } else if (
        // 如果点击的是暂估价材料调差并且没有分期
        ["8"].includes(projectStore.asideMenuCurrentInfo.key) &&
        !projectStore?.currentStageInfo && (!projectStore.asideMenuCurrentInfo?.defaultFeeFlag.frequencyList||projectStore.asideMenuCurrentInfo?.defaultFeeFlag.frequencyList.length==0)
      ) {
        humanShowTableColumns.value = getTableColumns().filter(
          (x) => x.type?.includes(8) && x.stageType?.includes(0) && x.titleType?.includes(0)&&(projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName: "htnrcjtz4_" + pageNameSuffix
        });
      } else if (
        // 如果点击的是暂估价材料调差并且没有点击分期并且已经分期
        ["8"].includes(projectStore.asideMenuCurrentInfo.key) &&
        !projectStore?.currentStageInfo &&
        projectStore.asideMenuCurrentInfo?.defaultFeeFlag.frequencyList.length > 0
      ) {
        humanShowTableColumns.value = getTableColumns().filter(
          (x) => x.type?.includes(8) && x.stageType?.includes(1) && x.titleType?.includes(1)&&(projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName: "htnrcjtz5_" + pageNameSuffix
        });
      } else if (
        // 如果点击的是暂估价材料调差并且点击分期
        ["8"].includes(projectStore.asideMenuCurrentInfo.key) &&
        projectStore?.currentStageInfo
      ) {
        humanShowTableColumns.value = getTableColumns().filter(
          (x) => x.type?.includes(8) && x.stageType?.includes(1) && x.titleType?.includes(2)&&(projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName: "htnrcjtz6_" + pageNameSuffix
        });
      }
    } else {// 如果是合同外
      // 如果是价差
      if( projectStore.asideMenuCurrentInfo.key == "20"){
        humanShowTableColumns.value = getTableColumns().filter((x) =>
          x.classify?.includes(2)&&(projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName: "htnrcjtz7_" + pageNameSuffix
        });
        // 如果是所有人材机
      }else if( projectStore.asideMenuCurrentInfo.key == "0"){
        humanShowTableColumns.value = getTableColumns().filter((x) =>
          x.classify?.includes(1)&&
          (projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName: "htnrcjtz8_" + pageNameSuffix
        });
      }else{
        humanShowTableColumns.value = getTableColumns().filter((x) =>
          x.classify?.includes(3)&&(projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName: "htnrcjtz9_" + pageNameSuffix
        });
      }
    }
  // 如果为工程项目、单项级别
  }else{
    // 如果是合同内并且二级树选中的不是价差和合同外所有人材机
    if (contractOriginalFlag.value&&!['20','21'].includes(projectStore.asideMenuCurrentInfo.key)) {
      // 如果是所有人材机
      if(projectStore.asideMenuCurrentInfo.key === "0"){
        humanShowTableColumns.value = getTableColumns().filter((x) =>
          x.type?.includes(0)&&
          x.titleType?.includes(3)&&
          x.stageType?.includes(0)&&
          (projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&
          !x.levelType12&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName: "htnrcjtz10_" + pageNameSuffix
        });
        // 如果是人材机调差
      }else if (["1", "2", "3"].includes(projectStore.asideMenuCurrentInfo.key)) {
        humanShowTableColumns.value = getTableColumns().filter(
          (x) =>
            (x.type?.includes(1) || x.type?.includes(2) || x.type?.includes(3)) &&
            x.adjustmentType?.includes(projectStore.asideMenuCurrentInfo?.defaultFeeFlag.adjustMethod) &&
            x.titleType?.includes(3)&&
            x.stageType?.includes(0)&&
            (projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName: "htnrcjtz11_" + pageNameSuffix
        });
        // 如果是暂估价材料调差
      } else if (["8"].includes(projectStore.asideMenuCurrentInfo.key)) {
        humanShowTableColumns.value = getTableColumns().filter(
          (x) =>
            x.type?.includes(8) &&
            x.adjustmentType?.includes(projectStore.asideMenuCurrentInfo?.defaultFeeFlag.adjustMethod) &&
            x.titleType?.includes(3)&&
            x.stageType?.includes(0)&&
            (projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName: "htnrcjtz12_" + pageNameSuffix
        });
      }
    }else{
      // 如果是价差
      if (["20"].includes(projectStore.asideMenuCurrentInfo.key)) {
        humanShowTableColumns.value = getTableColumns().filter((x) =>
          x.classify?.includes(2)&&
          (projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName: "htnrcjtz13_" + pageNameSuffix
        });
        // 如果是单项工程点击所有人材机或者是工程项目点击合同外所有人材机
      }else if( (projectStore.currentTreeInfo.levelType==2&&projectStore.asideMenuCurrentInfo.key == "0")||(projectStore.currentTreeInfo.levelType==1&&projectStore.asideMenuCurrentInfo.key == "21")){
        humanShowTableColumns.value = getTableColumns().filter((x) =>
          x.classify?.includes(1)&&
          (projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName: "htnrcjtz14_" + pageNameSuffix
        });
      }else{
        humanShowTableColumns.value = getTableColumns().filter((x) =>
          x.classify?.includes(3)&&
          (projectStore.currentTreeInfo.deStandardReleaseYear=='12'?x.deStandardReleaseYear.includes('12'):x.deStandardReleaseYear.includes('22'))&&(projectStore.taxMade=='1'?x.taxMode.includes(1):x.taxMode.includes(0))
        );
        // 动态表格列初始化设置
        initColumns({
          columns: humanShowTableColumns.value,
          pageName: "htnrcjtz15_" + pageNameSuffix
        });
      }
    } 
    // else if (["21"].includes(projectStore.asideMenuCurrentInfo.key)) {
    //   humanShowTableColumns.value = getTableColumns().filter((x) =>
    //     x.classify?.includes(1)
    //   );
    //   // 动态表格列初始化设置
    //   initColumns({
    //     columns: humanShowTableColumns.value,
    //     pageName: "htnrcjtz12_" + pageNameSuffix
    //   });
    //   // 如果是价差
    // } else if (["20"].includes(projectStore.asideMenuCurrentInfo.key)) {
    //   humanShowTableColumns.value = getTableColumns().filter((x) =>
    //     x.classify?.includes(2)
    //   );
    //   // 动态表格列初始化设置
    //   initColumns({
    //     columns: humanShowTableColumns.value,
    //     pageName: "htnrcjtz13_" + pageNameSuffix
    //   });
    // }
  }
};

const cellClassName = info => {
  let className = selectedClassName(info);
  className +=  customCell(info);
  return className;
};
// 批注样式处理
const customCell = ({ rowIndex, column, row }) => {
  let className = '';
  // // 批注提示
  if (column.field === 'materialName' && row?.annotations) {
    className += ' note-tips';
  }
  return className;
};

// 单期/多期设置成功回调
const updateAllData = () => {
  emits("updateMenuList");
  getInitList();
};

// 获取列表数据
const getInitList = () => {
  if (projectStore.currentTreeInfo.levelType === 3) {
    unitRcjQuery();
  } else {
    projectRcjList();
  }
};
const toggleAllCheckboxEvent = () => {
  const $table = humanTable.value;
  if ($table) {
    $table.toggleAllCheckboxRow();
  }
};
onDeactivated(() => {
  console.log("onDeactivated");
  lookupVisible.value = false;
  window.removeEventListener("keydown", openLookup);
});

const getLoadStatus = () => {
  let apiData = {
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单项ID
  }
  let apiFun = loadApi.loadPriceStatus;
  if (isOriginalFlag()) {
    // 合同内特有参数
    apiFun = loadApi.loadPriceStatusOriginal;
  }
  apiFun(apiData).then((res) => {
    console.log("++++++++++++++", res);
    // isSeeReport.disabled = false;
    if (res.result && res.result.includes(5)) {
      isLoad.disabled = true;
    } else if (res.result && res.result.includes(3)) {
      // isSeeReport.disabled = true;
    } else if (res.result && res.result.includes(4)) {
      feePro.isOnline().then((res) => {
        console.log("判断是否有网------", res);
        if (res.result) {
          isLoad.disabled = false;
        } else {
          message.error("请连接网络后使用！");
        }
      });
    }
  });
};
const checkBoxIsShow = () => {
  tableData.value &&
    tableData.value.map((item) => {
      //有父子级关系的父级二次解析，父级暂估，甲供，市场价锁定不可勾选
      if (item.markSum == 1 && (item.levelMark == 1 || item.levelMark == 2)) {
        item.checkIsShow = false;
      } else {
        item.checkIsShow = true;
      }
    });
};
const setCurrentInfo = () => {
  if(tableData.value.length==0){
    isCurrent.value=null
    currentInfo.value=null
    return;
  }
  //设置当前选中行
  let idx = -1;
  if (
    isCurrent.value?.sequenceNbr &&
    currentInfo.value?.sequenceNbr &&
    isCurrent.value.sequenceNbr === currentInfo.value.sequenceNbr
  ) {
    //如果选中行和当前行一致-重新选中
    idx = tableData.value.findIndex(
      a => a.sequenceNbr === isCurrent.value.sequenceNbr
    );
  }
  if (idx === -1) {
    //设置新的选中行
    let isCurrentRow;
    if (isCurrent.value) {
      isCurrentRow = tableData.value.find(
        item => isCurrent.value.sequenceNbr === item.sequenceNbr
      );
    } else {
      isCurrent.value = tableData.value[0];
      
    }
    changeCurrentInfo(isCurrentRow || tableData.value[0]);
  } else {
    changeCurrentInfo(tableData.value[idx]);
  }
  //设置复选框的置灰状态
  checkBoxIsShow();
};
const getKind = (type) => {
  let value;
  switch (type) {
    case "其他费":
      value = 0;
      break;
    case "人工费":
      value = 1;
      break;
    case "材料费":
      value = 2;
      break;
    case "机械费":
      value = 3;
      break;
    case "设备费":
      value = 4;
      break;
    case "主材费":
      value = 5;
      break;
    case "商砼":
      value = 6;
      break;
    case "砼":
      value = 7;
      break;
    case "浆":
      value = 8;
      break;
    case "商浆":
      value = 9;
      break;
    case "配比":
      value = 10;
      break;
  }
  return value;
};
const getType = (type) => {
  let value = "";
  switch (type) {
    case 0:
      value = "其他费";
      break;
    case 1:
      value = "人工费";
      break;
    case 2:
      value = "材料费";
      break;
    case 3:
      value = "机械费";
      break;
    case 4:
      value = "设备费";
      break;
    case 5:
      value = "主材费";
      break;
    case 6:
      value = "商砼";
      break;
    case 7:
      value = "砼";
      break;
    case 8:
      value = "浆";
      break;
    case 9:
      value = "商浆";
      break;
    case 10:
      value = "配比";
      break;
  }
  return value;
};
// const getOldData = () => {
//   tableData.value &&
//     tableData.value.map((item) => {
//       oldData.value.push({
//         sequenceNbr: item.sequenceNbr,
//         jieSuanMarketPrice: item.jieSuanMarketPrice,
//         ifDonorMaterial: item.ifDonorMaterial,
//         donorMaterialNumber: item.donorMaterialNumber,
//         ifProvisionalEstimate: item.ifProvisionalEstimate,
//         ifLockStandardPrice: item.ifLockStandardPrice,
//         jieSuanBasePriceF0: item.jieSuanBasePriceF0,
//         jieSuanCurrentPriceF0: item.jieSuanCurrentPriceF0,
//         jieSuanBasePrice: item.jieSuanBasePrice,
//         marketPrice: item.marketPrice,
//         riskAmplitudeRangeMin: item.riskAmplitudeRangeMin,
//         jieSuanTaxRemoval: item.jieSuanTaxRemoval,
//         taxRemoval: item.taxRemoval,
//         jieSuanFee: item.jieSuanFee,
//         remark: item.remark,
//         materialName: item.materialName,
//         type: item.type,
//         specification: item.specification,
//         unit: item.unit,
//         totalNumber: item.totalNumber,
//         dePrice: item.dePrice,
//         isDifference: item.isDifference,
//         jieSuanAdminRate: item.jieSuanAdminRate,
//         producer: item.producer,
//         manufactor: item.manufactor,
//         markSum: item.markSum,
//         qualityGrade: item.qualityGrade,
//         brand: item.brand,
//         deliveryLocation: item.deliveryLocation,
//       });
//     });
//   console.log("getOldData", oldData.value);
// };
const getOldData = () => {
  if(tableData.value){
    oldData.value=JSON.parse(JSON.stringify(tableData.value))
  }
}
const getHumanMachineData = () => {
  getInitList();
  // loading.value = true;
  // let formData = {
  //   type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
  //   kind: Number(projectStore.asideMenuCurrentInfo?.key),
  //   constructId:
  //     projectStore.currentTreeInfo.levelType === 1
  //       ? projectStore.currentTreeInfo?.id
  //       : projectStore.currentTreeGroupInfo?.constructId,
  // };
  // if (projectStore.currentTreeInfo.levelType === 3) {
  //   formData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
  //   formData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  // }
  // feePro
  //   .queryConstructRcjByDeId(formData)
  //   .then((res) => {
  //     if (res.status === 200 && res.result && res.result.length > 0) {
  //       let num = 1;
  //       res.result &&
  //         res.result.map((item, index) => {
  //           item.dispNo = num++;
  //           item.type = getType(item.kind);
  //           item.donorMaterialNumber =
  //             Number(item.donorMaterialNumber) === 0 ? "" : item.donorMaterialNumber;
  //           item.origindonorMaterialNum = item.donorMaterialNumber
  //             ? item.donorMaterialNumber
  //             : "0";
  //         });
  //       tableData.value = res.result;
  //
  //       nextTick(() => {
  //         if (isCurrent.value && projectStore.currentTreeInfo.levelType === 3) {
  //           let isCurrentList = tableData.value.filter(
  //             (item) => isCurrent.value.sequenceNbr === item.sequenceNbr
  //           );
  //           humanTable.value.setCurrentRow(isCurrentList[0]);
  //         } else {
  //           humanTable.value.setCurrentRow(tableData.value && tableData.value[0]);
  //         }
  //       });
  //
  //       checkBoxIsShow();
  //       if ([1,2].includes(projectStore.currentTreeInfo.levelType)) {
  //         getOldData();
  //         getSameUnit();
  //       }
  //       console.log("人材机汇总表格数据", tableData.value);
  //     } else {
  //       tableData.value = [];
  //     }
  //   })
  //   .finally(() => {
  //     loading.value = false;
  //   });
};

const unitRcjQuery = () => {
  let apiData = {
    kind: Number(projectStore.asideMenuCurrentInfo?.key),
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeInfo?.parentId,
    unitId: projectStore.currentTreeInfo?.id,
    num: projectStore.currentStageInfo?.num,
  };
  if( sort.value.order ){
    apiData.sort = toRaw(sort.value);
  }
  console.log("获取列表数据参数", apiData);
  let levelType = projectStore.currentTreeInfo.levelType;
  jieSuanDetail.unitRcjQuery(apiData).then((res) => {
    console.log("res222222222", res);
    if (res.status === 200 && res.result) {
      let num = 1;
      jieSuanPriceTotal.value = 0;
      jieSuanPriceDifferencSum.value = 0;
      res.result &&
        res.result.map((item, index) => {
          jieSuanPriceTotal.value += Number(!item.isGray ? item.total || 0 : 0);
          if(item.isDifference){
            jieSuanPriceDifferencSum.value += Number(item.jieSuanPriceDifferencSum || 0);
          }
          item.dispNo = num++;
          item.type = getType(item.kind);
          // item.jieSuanFee = getFeeType(item.jieSuanFee);
          item.donorMaterialNumber =
            Number(item.donorMaterialNumber) === 0 ? "" : item.donorMaterialNumber;
          item.origindonorMaterialNum = item.donorMaterialNumber
            ? item.donorMaterialNumber
            : "0";
          //批注-暂时
          item.annotations = item[annotationsObj.noteFeild[levelType]]?.toString(); //批注重新赋值
          item.isShowAnnotations = isChangePage.value.includes(item.sequenceNbr) && item[annotationsObj['showFeild'][levelType]] && item?.annotations?.length > 0;
          item.annotationsVisible = isChangePage.value.includes(item.sequenceNbr) &&  item?.isShowAnnotations  ? true : false;
        });
      tableData.value = res.result;
      // if(res.result.length>0){
      //   isCurrent.value = res.result[0];
      //   currentInfo.value=res.result[0]
      // }
      checkBoxIsShow();
      setCurrentInfo()
      originalTableData = xeUtils.clone(tableData.value, true);
    } else {
      tableData.value = [];
    }
  });
};
// 排序标识
const sortFiled = ref('');
const sortVal = ref(false);
let sort = ref({ field:'', order:'' });//field是属性名称  order是排序规则，asc或者desc
// 点击排序
const sortClick = (columns) => {
  // 判断是否有排序标识
  if(!columns?.sortableFlag){
    return null;
  }
  // 判断排序字段
  if (sortFiled.value === columns.field) {
    sortVal.value = !sortVal.value;
  } else {
    sortVal.value = true;
  }
  sortFiled.value = columns.field;
  sort.value.field = sortFiled.value ? columns.field : null ;
  sort.value.order = sortVal.value ? 'asc':'desc';
  getInitList();
};

// 工程项目级别数据获取
const projectRcjList = () => {
  let apiData = {
    type: ['20','21'].includes(projectStore.asideMenuCurrentInfo?.key)?2:projectStore.currentTreeInfo.originalFlag?1:2,
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
    kind: projectStore.asideMenuCurrentInfo?.key || null,
    levelType:projectStore.currentTreeInfo.levelType,
    adjustMethod:projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.adjustMethod,

  };
  if( sort.value.order ){
    apiData.sort = toRaw(sort.value);
  }
  if(projectStore.currentTreeInfo.levelType===2){
    apiData['singleId']=projectStore.currentTreeGroupInfo?.singleId
  }
  let levelType = projectStore.currentTreeInfo.levelType;
  console.log("获取列表数据参数", apiData);
  jieSuanDetail.projectRcjList(apiData).then((res) => {
    console.log("项目层级数据返回", res.result);
    if (res.status === 200 && res.result) {
      let num = 1;
      jieSuanPriceTotal.value = 0;
      jieSuanPriceDifferencSum.value = 0;
      res.result &&
        res.result.map((item, index) => {
          jieSuanPriceTotal.value += Number(!item.isGray ? item.total || 0 : 0);
          if(item.isDifference){
            jieSuanPriceDifferencSum.value += Number(item.jieSuanPriceDifferencSum || 0);
          }
          item.dispNo = num++;
          item.type = getType(item.kind);
          // item.jieSuanFee = getFeeType(item.jieSuanFee);
          item.donorMaterialNumber =
            Number(item.donorMaterialNumber) === 0 ? "" : item.donorMaterialNumber;
          item.origindonorMaterialNum = item.donorMaterialNumber
            ? item.donorMaterialNumber
            : "0";

          //批注-暂时
          item.annotations = item[annotationsObj.noteFeild[levelType]]?.toString(); //批注重新赋值
        });
      tableData.value = res.result;
      // nextTick(() => {
      //   if (isCurrent.value && projectStore.currentTreeInfo.levelType === 3) {
      //     let isCurrentList = tableData.value.filter(
      //       (item) => isCurrent.value.sequenceNbr === item.sequenceNbr
      //     );
      //     isCurrent.value = isCurrentList[0];
      //     humanTable.value.setCurrentRow(isCurrentList[0]);
      //   } else {
      //     isCurrent.value = tableData.value[0];
      //     humanTable.value.setCurrentRow(tableData.value[0]);
      //   }
      //   currentInfo.value= isCurrent.value;
      // });

      checkBoxIsShow();
      setCurrentInfo()

      if ([1,2].includes(projectStore.currentTreeInfo.levelType)) {
        getOldData();
        getSameUnit();
      }
    }
  });
};
const getSameUnit = () => {
  let list = [];
  let addColorList = [];
  tableData.value &&
    tableData.value.map((item) => {
      let otherSameUnit = tableData.value.filter(
        (unit) =>
          unit.materialCode === item.materialCode &&
          unit.materialName === item.materialName &&
          // unit.unitId === item.unitId &&
          unit.unit === item.unit &&
          unit.specification === item.specification &&
          Number(unit.dePrice) === Number(item.dePrice) &&
          unit.ifDonorMaterial == item.ifDonorMaterial &&
          unit.ifProvisionalEstimate == item.ifProvisionalEstimate &&
          unit.ifLockStandardPrice == item.ifLockStandardPrice &&
          unit.sequenceNbr !== item.sequenceNbr &&
          Number(unit.jieSuanMarketPrice) !== Number(item.jieSuanMarketPrice)
      );
      if (
        otherSameUnit &&
        otherSameUnit.length > 0 &&
        !addColorList.includes(item.sequenceNbr)
      ) {
        addColorList.push(item.sequenceNbr);
      }
    });
  tableData.value &&
    tableData.value.map(
      (item) => (item.addColor = addColorList.includes(item.sequenceNbr) ? true : false)
    );
  console.log("addColorList", addColorList);
};
const cellTableStyle = ({ row, column }) => {
  if (column.field === "jieSuanMarketPrice") {
    //工程项目级别人材机汇总八要素一致市场价不一致的数据标识不一样
    if (row.addColor) {
      return {
        color: "#059421",
        backgroundColor: "rgba(22, 225, 83, 0.4)",
      };
    }
  }
};
const cellStyle = ({ row, column }) => {
  if (column.field === "jieSuanMarketPrice") {
    //市场价高于定额价标红，低于的话标绿
    if (row.jieSuanMarketPrice >= 0 && row.dePrice >= 0 && row.jieSuanMarketPrice > row.dePrice) {
      return {
        color: "#D40C0C",
      };
    } else if (
      row.jieSuanMarketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.jieSuanMarketPrice < row.dePrice
    ) {
      return {
        color: "#059421",
      };
    }
  }
};
const rowStyle = ({ row }) => {
  if (row.isGray) {
    if (row.field !== "dispNo") {
      return {
        color: "#ACACAC",
      };
    }
  }
  if (row.priceDifferencSum > 0) {
    return {
      backgroundColor: "#EFEAFF",
    };
  }
  if (row.highlight) {
    return {
      backgroundColor: "#F9FFF8",
    };
  }
};
const CheckboxChange = (row, type) => {
  switch (type) {
    case "markSum":
      row.checkIsShow = row.markSum === 1 ? false : true;
      break;
    case "ifDonorMaterial":
      break;
    case "ifProvisionalEstimate":
      break;
    case "ifLockStandardPrice":
      row.cusTomIfLockStandardPrice = row.ifLockStandardPrice;
      break;
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    upDate(row, type);
  } else if ([1,2].includes(projectStore.currentTreeInfo.levelType)) {
    //工程项目级别
    row.isChange = true; //标识编辑行
    let upDateList = getPropData();
    if (upDateList && upDateList.length > 0) {
      projectStore.SET_HUMAN_UPDATA_DATA({
        isEdit: true,
        name: "unify-humanMachineSummary",
        updataData: upDateList,
      });
    }
    if (type === "ifDonorMaterial") {
      row.donorMaterialNumber = row.ifDonorMaterial === 1 ? row.totalNumber : "";
      humanTable.value.reloadRow(row, {});
    }
    upDate(row, type);
  }
};

const setProUpdate = (updataData = [], type = 'update') => {
  projectStore.SET_HUMAN_UPDATA_DATA({
    isEdit: true,
    name: 'unify-humanMachineSummary',
    updataData:
      type === 'update' ? updataData : projectStore.humanUpdataData?.updataData,
    unitIdList: [...setAggreUnitList.value], //设置汇总范围选中的单位列表
    adjustFactor: projectStore.humanUpdataData?.adjustFactor,
    rcjwjcList:
      type === 'rcjwjc' ? updataData : projectStore.humanUpdataData?.rcjwjcList,
  });
};

//工程项目更新载价市场价
const upDateMarketPrice = row => {
  let target = tableData.value.find(
    a => a.sequenceNbr === currentInfo.value.sequenceNbr
  );
  if (
    projectStore.deStandardReleaseYear === '12' ||
    (target.deStandardReleaseYear === '12' &&
      projectStore.deStandardReleaseYear === '22')
  ) {
    target.jieSuanMarketPrice = row.jieSuanMarketPrice;
  } else {
    if (Number(projectStore?.taxMade) === 1) {
      target.priceMarket = row.jieSuanMarketPrice;
      target.priceMarketTax = NumberUtil.numberScale2(
        NumberUtil.multiply(
          0.01,
          NumberUtil.multiply(
            target.priceMarket,
            NumberUtil.add(100, target.taxRate)
          )
        )
      );
    } else {
      //  简易----含税
      target.priceMarketTax = row.jieSuanMarketPrice;
      target.priceMarket = NumberUtil.numberScale2(
        NumberUtil.multiply(
          100,
          NumberUtil.divide(
            target.priceMarketTax,
            NumberUtil.add(100, target.taxRate)
          )
        )
      );
    }
  }
  target.sourcePrice = row.sourcePrice;
  target.isExecuteLoadPrice = true;
  target.isChange = true; //标识编辑行
  getSameUnit();
  let upDateList = getPropData();
  if (upDateList && upDateList.length > 0) {
    getInitList();// 载价后，需要刷新界面，然后再统一应用
    setProUpdate(upDateList);
  }
};
const getCurrentIndex = (item, type) => {
  console.log("item=======", item, tableData.value);
  if (item) {
    tableData.value.map((n, index) => {
      if (n.sequenceNbr === item.sequenceNbr) {
        isCurrent.value = n;
      }
    });
  } else {
    isCurrent.value = tableData.value[0];
  }
  humanTable.value.setCurrentRow(isCurrent.value);
  console.log("==============", isCurrent);
};

const currentChange = ({ row }) => {
  currentInfo.value = { ...toRaw(row) };
  isCurrent.value = { ...toRaw(row) };
  //if ([1,2].includes(projectStore.currentTreeInfo.levelType)) {
  getCurrentIndex(row);
  setEditEnabled(row);
  //}
};

// 单位工程级别四种调整法修改
const priceDifferenceAdjustmentMethodController = (methodType) => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    clType: Number(projectStore.asideMenuCurrentInfo?.key),
    methodType: Number(methodType),
    levelType:projectStore.currentTreeInfo.levelType
  };
  console.log("apiData", apiData);
  jieSuanDetail.priceDifferenceAdjustmentMethodController(apiData).then((res) => {
    if (res.status === 200 && res.result) {
      message.success("调整成功");
      getInitList();
      emits("updateMenuList");
      // setTimeout(() => {
      //   getInitColumns();
      // }, 500);
      updateValue();
    }
  });
};

const updateValue = () => {
  updateOperateByName("settlement-adjustment-method", (info) => {
    // if ([1,2].includes(projectStore.currentTreeInfo.levelType)) {
    //   info.value = projectStore.currentTreeInfo.jieSuanRcjDifferenceTypeList?.filter(
    //     (x) => x.adjustMethod === Number(projectStore.asideMenuCurrentInfo.key)
    //   )[0]?.JieSuanPriceAdjustmentMethodType;
    // } else {
      info.value = projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.adjustMethod;
    // }
  });
};
// 工程项目级别四种调整法修改
const constructPriceDifferenceAdjustmentMethodController = (methodType) => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    clType: Number(projectStore.asideMenuCurrentInfo?.key),
    methodType: Number(methodType),
    levelType:projectStore.currentTreeInfo.levelType,
    kind:projectStore.asideMenuCurrentInfo?.defaultFeeFlag.kind
  };
  console.log("apiData", apiData);
  jieSuanDetail
    .constructPriceDifferenceAdjustmentMethodController(apiData)
    .then((res) => {
      if (res.status === 200 && res.result) {
        message.success("调整成功");
        // projectStore.SET_IS_REFRESH_PROJECT_TREE(true);
        getInitList();
        emits("updateMenuList");
        getInitColumns();
        updateValue();
        if (projectStore.currentTreeInfo.levelType !== 3) {
          projectStore.SET_HUMAN_UPDATA_DATA({
            isEdit: true,
            name: "unify-humanMachineSummary",
            updataData: [],
          });
          unifyData.disabled = false;
        }
      }
    });
};

const rcjParticipateInAdjustment = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    levelType:projectStore.currentTreeInfo.levelType,
  };
  if(projectStore.currentTreeInfo.levelType==2){
    apiData['singleId']=projectStore.currentTreeInfo?.id
  }
  if(projectStore.currentTreeInfo.levelType==3){
    apiData['singleId']=projectStore.currentTreeInfo?.parentId
    apiData['unitId']=projectStore.currentTreeInfo?.id
  }
  console.log("人材机参与调差参数", apiData);
  // 222222222222
  jieSuanDetail.rcjParticipateInAdjustment(apiData).then((res) => {
    console.log("人材机参与调差结果", res);
    if (res.status === 200 && res.result) {
      message.success("人材机参与调差设置成功");
      emits("updateMenuList");
    }
  });
};

// 是否编辑处理
const setEditEnabled = (row) => {
  if (row.isGray) {
    isEditEnabled.value = false;
    return;
  }
  isEditEnabled.value = true;
};

const riskOpen = (row) => {
  isEditCurrentData.value = true;
  riskVisible.value = true;
  currentInfo.value = row;
};
// 获取当前是否分期
const getIsStage = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    levelType:projectStore.currentTreeInfo.levelType,
  };
  if(projectStore.currentTreeInfo.levelType==2){
    apiData['singleId']=projectStore.currentTreeInfo?.id
  }
  console.info(424234234234,apiData)
  jieSuanDetail.getIsStage(apiData).then((res) => {
    // 如果分期并且是合同内所有人采集则隐藏统一应用  分期都隐藏批量选择调差材料按钮否则显示
    if(res.status==200){
      operateList.value.find((item) => item.name=== 'unify-humanMachineSummary').hidden = res.result&&projectStore.asideMenuCurrentInfo?.key!=='21';
      // 项目  合同外所有人材机和价差 不展示
      if(["21", "20"].includes(String(projectStore.asideMenuCurrentInfo?.key))){
        operateList.value.find((item) => item.name==='batch-adjustment-material').hidden = true;
      }else{
        operateList.value.find((item) => item.name==='batch-adjustment-material').hidden = res.result;
      }
    }
  });
}
// 获取当前单位
const findUnitProjectById = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    levelType:projectStore.currentTreeInfo.levelType,
  };
  if(projectStore.currentTreeInfo.levelType==2){
    apiData['singleId']=projectStore.currentTreeInfo?.id
  }
  if(projectStore.currentTreeInfo.levelType==3){
    apiData['singleId']=projectStore.currentTreeInfo?.parentId
    apiData['unitId']=projectStore.currentTreeInfo?.id
  }
  jieSuanDetail.findUnitProjectById(apiData).then((res) => {
    isDifference.value = res.result?res.result:false;
    operateList.value.find(
      (item) => item.name === "rcj-adjustment"
    ).label = isDifference.value ? "人材机取消调差" : "人材机参与调差";
  });
};

// 取消人材机调差
const cancelRcjParticipateInAdjustment = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    levelType:projectStore.currentTreeInfo.levelType,
  };
  if(projectStore.currentTreeInfo.levelType==2){
    apiData['singleId']=projectStore.currentTreeInfo?.id
  }
  if(projectStore.currentTreeInfo.levelType==3){
    apiData['singleId']=projectStore.currentTreeInfo?.parentId
    apiData['unitId']=projectStore.currentTreeInfo?.id
  }
  jieSuanDetail.cancelRcjParticipateInAdjustment(apiData).then((res) => {
    if (res.status === 200 && res.result) {
      message.success("人材机取消调差设置成功");
      emits("updateMenuList");
    }
  });
};
let isStaged = ref(false);// 在 项目和单项上，是否分期
const jieSuanConstructProjectFq = () => {
  let apiData = {
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
    levelType:projectStore.currentTreeInfo.levelType,
  };
  if(projectStore.currentTreeInfo.levelType==2){
    apiData['singleId']=projectStore.currentTreeInfo?.id
  }
  if(projectStore.currentTreeInfo.levelType==3){
    apiData['singleId']=projectStore.currentTreeInfo?.parentId
    apiData['unitId']=projectStore.currentTreeInfo?.id
  }
  // 2222222222222
  console.log("新增接口", apiData);
  jieSuanDetail.jieSuanConstructProjectFq(apiData).then((res) => {
    if (res.status === 200 && res.result) {
      operateList.value.find((item) => item.name === "rcj-adjustment").hidden =  res.result;
    }
    if(res.status === 200) {
      isStaged.value = res.result;
    }
  });
};

const initOperateList = () => {
  if (projectStore.currentTreeInfo.levelType === 3) {
    operateList.value.forEach((item) => {
      if (item.label === "统一应用") {
        item.hidden = true;
      }
    });
  } else {
    operateList.value.forEach((item) => {
      if (item.label === "统一应用") {
        item.hidden = false;
      }
    });
  }
  if(projectStore.currentTreeInfo.originalFlag){
    if (
      ([1,2].includes(projectStore.currentTreeInfo.levelType) &&
      !projectStore.currentTreeInfo.isStage &&
      ["0", "1", "2", "3"].includes(projectStore.asideMenuCurrentInfo?.key)) ||
    (projectStore.currentTreeInfo.isStage &&
      projectStore.asideMenuCurrentInfo?.key === "0") ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      projectStore.currentTreeInfo.originalFlag &&
      ((!projectStore.stageCount && projectStore.asideMenuCurrentInfo?.key !== "8") ||
        (projectStore.stageCount &&
          ["0", "1", "2", "3"].includes(projectStore.asideMenuCurrentInfo?.key) &&
          !projectStore.currentStageInfo)))
    ) {
      operateList.value.forEach((item) => {
        if (item.label === "批量选择调差材料") {
          item.hidden = false;
        }
      });
    } else {
      operateList.value.forEach((item) => {
        if (item.label === "批量选择调差材料") {
          item.hidden = true;
        }
      });
    }
  }else{
    operateList.value.forEach((item) => {
      if (item.label === "批量选择调差材料") {
        item.hidden = true;
      }
    });
  }
  
  // if (
  //   ([1,2].includes(projectStore.currentTreeInfo.levelType) &&
  //     ["0", "1", "2", "3"].includes(projectStore.asideMenuCurrentInfo?.key)) ||
  //   (projectStore.currentTreeInfo.levelType === 3 &&
  //     projectStore.currentTreeInfo.originalFlag &&
  //     !projectStore.currentStageInfo &&
  //     ["0", "1", "2", "3"].includes(projectStore.asideMenuCurrentInfo?.key))
  // ) {
  //   operateList.value.forEach((item) => {
  //     if (item.label === "批量选择调差材料") {
  //       item.hidden = false;
  //     }
  //   });
  // } else {
  //   operateList.value.forEach((item) => {
  //     if (item.label === "批量选择调差材料") {
  //       item.hidden = true;
  //     }
  //   });
  // }
  // if (
  //   ([1,2].includes(projectStore.currentTreeInfo.levelType) &&
  //     projectStore.asideMenuCurrentInfo?.key === '21') ||
  //   (projectStore.currentTreeInfo.levelType === 3 &&
  //     !projectStore.currentTreeInfo.originalFlag &&
  //     projectStore.asideMenuCurrentInfo?.key !== '20')
  // ) {
  //   operateList.value.forEach(item => {
  //     if (item.label === '调整市场价系数') {
  //       item.hidden = false;
  //     }
  //   });
  // } else {
  //   operateList.value.forEach(item => {
  //     if (item.label === '调整市场价系数') {
  //       item.hidden = true;
  //     }
  //   });
  // }


  if (
    ([1,2].includes(projectStore.currentTreeInfo.levelType) &&
    projectStore.currentTreeInfo.originalFlag &&
      ["1", "2", "3"].includes(projectStore.asideMenuCurrentInfo?.key)) ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      projectStore.currentTreeInfo.originalFlag &&
      !projectStore.currentStageInfo &&
      ["1", "2", "3"].includes(projectStore.asideMenuCurrentInfo?.key))
  ) {
    operateList.value.forEach((item) => {
      if (item.label === "风险幅度范围" || item.label === "四种结算人材机调整法") {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach((item) => {
      if (item.label === "风险幅度范围" || item.label === "四种结算人材机调整法") {
        item.hidden = true;
      }
    });
  }

  if (
    ([1,2].includes(projectStore.currentTreeInfo.levelType) &&
    projectStore.currentTreeInfo.originalFlag &&
      ["1", "2", "3", "8", "20"].includes(projectStore.asideMenuCurrentInfo?.key)) ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      ((projectStore.currentTreeInfo.originalFlag &&
        !projectStore.currentStageInfo &&
        (["8"].includes(projectStore.asideMenuCurrentInfo?.key) ||
          (["1", "2", "3"].includes(projectStore.asideMenuCurrentInfo?.key) &&
            projectStore.asideMenuCurrentInfo?.defaultFeeFlag.adjustMethod !==
              4))) ||
        (!projectStore.currentTreeInfo.originalFlag &&
          ["20"].includes(projectStore.asideMenuCurrentInfo?.key))))
  ) {
    operateList.value.forEach((item) => {
      if (item.label === "价差取费设置") {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach((item) => {
      if (item.label === "价差取费设置") {
        item.hidden = true;
      }
    });
  }

  if (
    ([1,2].includes(projectStore.currentTreeInfo.levelType) &&
      ["1", "2", "3", "8", "20"].includes(projectStore.asideMenuCurrentInfo?.key)) ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      ((projectStore.currentTreeInfo.originalFlag &&
        !projectStore.currentStageInfo &&
        ["1", "2", "3", "8"].includes(projectStore.asideMenuCurrentInfo?.key)) ||
        (!projectStore.currentTreeInfo.originalFlag &&
          ["20"].includes(projectStore.asideMenuCurrentInfo?.key))))
  ) {
    operateList.value.forEach((item) => {
      if (item.label === "批量设置结算除税系数") {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach((item) => {
      if (item.label === "批量设置结算除税系数") {
        item.hidden = true;
      }
    });
  }

  if (
    projectStore.currentTreeInfo.levelType === 3 &&
    projectStore.currentTreeInfo.originalFlag &&
    !projectStore.currentStageInfo &&
    ["0", "2"].includes(projectStore.asideMenuCurrentInfo?.key)
  ) {
    operateList.value.forEach((item) => {
      if (item.label === "自动过滤调差材料") {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach((item) => {
      if (item.label === "自动过滤调差材料") {
        item.hidden = true;
      }
    });
  }

  if (
    projectStore.currentTreeInfo.levelType === 3 &&
    projectStore.currentTreeInfo.originalFlag &&
    projectStore.stageCount &&
    !projectStore.currentStageInfo &&
    ["1", "2", "3", "8"].includes(projectStore.asideMenuCurrentInfo?.key)
  ) {
    operateList.value.forEach((item) => {
      if (item.label === "单期/多期调差设置") {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach((item) => {
      if (item.label === "单期/多期调差设置") {
        item.hidden = true;
      }
    });
  }

  if (
    projectStore.currentTreeInfo.levelType === 3 &&
    projectStore.currentTreeInfo.originalFlag &&
    projectStore.stageCount &&
    !projectStore.currentStageInfo &&
    ["0"].includes(projectStore.asideMenuCurrentInfo?.key)
  ) {
    operateList.value.forEach((item) => {
      if (item.label === "分期量查看") {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach((item) => {
      if (item.label === "分期量查看") {
        item.hidden = true;
      }
    });
  }
  // 如果是合同内则隐藏查找功能，合同外显示
  if(projectStore.currentTreeInfo.originalFlag){
    operateList.value.forEach((item) => {
      if (item.label === "查找") {
        item.hidden = true;
      }
    });
  }else{
    operateList.value.forEach((item) => {
      if (item.label === "查找") {
        item.hidden = false;
      }
    });
  }
  if (
    ([1,2].includes(projectStore.currentTreeInfo.levelType) &&
      !["0", "20"].includes(projectStore.asideMenuCurrentInfo?.key)) ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      projectStore.currentTreeInfo.originalFlag &&
      ((projectStore.stageCount && projectStore.currentStageInfo) ||
        (!projectStore.stageCount && projectStore.asideMenuCurrentInfo?.key !== "0"))) ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      !projectStore.currentTreeInfo.originalFlag &&
      projectStore.asideMenuCurrentInfo?.key !== "20")
  ) {
    operateList.value.forEach((item) => {
      if (item.label === "载价") {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach((item) => {
      if (item.label === "载价") {
        item.hidden = true;
      }
    });
  }
  
  if (
    ([1,2].includes(projectStore.currentTreeInfo.levelType) &&
      ["20", "21"].includes(projectStore.asideMenuCurrentInfo?.key)) ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      !projectStore.currentTreeInfo.originalFlag)||
      (!projectStore.currentTreeInfo.originalFlag && 
      projectStore.currentTreeInfo.levelType == 2 && 
      projectStore.asideMenuCurrentInfo?.key == '0')
  ) {
    operateList.value.forEach((item) => {
      if (item.label === "人材机参与调差" || item.label === "人材机取消调差") {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach((item) => {
      if (item.label === "人材机参与调差" || item.label === "人材机取消调差") {
        item.hidden = true;
      }
    });
  }

};

// 定位方法
const posRow = (sequenceNbr) => {
  console.log("人材机汇总定位", sequenceNbr);
  // 块料楼地面 bdCode: "011102003006"
  // let testId = '1725055299942461634'
  getInitList();
  setTimeout(() => {
    getCurrentIndex({ sequenceNbr });
  }, 800);
  // currentInfo.value = { sequenceNbr };
};

defineExpose({
  posRow,
  selectData,
  getInitList,
});
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  // height: 40px;
  padding: 0 10px;
}
.table-content {
  width: 100%;
  // min-height: 450px !important;
  height: calc(100%);
  background: white;
  :deep(.background-red) {
    color: #2a2a2a;
    background: #de3f3f;
  }
  :deep(.vxe-cell .vxe-input>.vxe-input--inner) {
    background: white;
  }
}
.table-content :deep(.vxe-table) {
  .title-bold {
    font-weight: bold;
  }
  .color-red {
    color: #de3f3f;
  }
  .row--current {
    background-color: var(--vxe-table-row-current-background-color) !important;
  }
}

// ::v-deep(.is--disabled .vxe-icon-checkbox-unchecked) {
//   background-color: #dcdfe6;
// }
::v-deep(.ant-empty) {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(25%, -25%);
}
.price-list {
  position: relative;
  margin-right: 18px;
  span {
    display: inline-block;
    font-size: 14px;
    color: #2a2a2a;
    padding: 0 10px;
  }
  .line {
    color: #007aff;
    width: 1px;
  }
}
.price-list {
  position: relative;
  margin-right: 18px;
  span {
    display: inline-block;
    font-size: 14px;
    color: #2a2a2a;
    padding: 0 10px;
  }
  .line {
    color: #007aff;
    width: 1px;
  }
}
.adjustFactorMoadl {
  .title {
    font-size: 14px;
  }
  div {
    display: flex;
    width: 100%;
    padding-bottom: 20px;
    // padding: 20px 0px 20px 5px;
    // border: 1px solid #6666;
    .ant-input {
      width: 78%;
    }
    span {
      width: 21%;
      margin: auto;
    }
  }
  .footor {
    display: flex;
    justify-content: space-between;
    width: 150px;
    margin: 10px auto 0;
  }
}

.selectTab {
  background-color: #e7e7e7;
  height: 32px;
  line-height: 30px;
  // padding-left: 20px;
  position: relative;
  border-bottom: 2px solid #e7e7e7;
  margin: 3px 0;
  .label {
    color: grey;
    font-size: 12px;
  }
  .showTitle {
    position: absolute;
    right: 0px;
    top: 0px;
    line-height: 30px;
    height: 30px;
    padding: 0 20px;
    font-size: 12px;
    // background-color: #e7e7e7;
    border-radius: 5px;
  }
  .ant-radio-button-wrapper {
    font-size: 12px;
    background-color: #e7e7e7;
    border: none;
    box-shadow: none;
    // border-radius: 5px;
  }
  .ant-radio-button-wrapper::before {
    content: '';
    width: 0;
  }
  .ant-radio-button-wrapper-checked {
    // border-color: none;
    background-color: white;
    border: none;
    border-top: 3px solid #4786ff;
    color: black;
    &:hover {
      color: black;
    }
  }
}
::v-deep(.box .vxe-table .vxe-sort--desc-btn) {
  position: absolute;
  left: 0.2em;
  width: auto;
  text-align: center;
  height: auto;
  line-height: normal;
  cursor: pointer;
  top: 0.4em;
  font-size: 1.1em;
  border: 0 solid transparent;
}
::v-deep(.vxe-table .vxe-sort--asc-btn.sort--active, .vxe-table .vxe-sort--desc-btn.sort--active) {
  color: var(--vxe-primary-color);
}
::v-deep(.vxe-icon-caret-down):before {
  content: "\e8ed" !important;
}
::v-deep(.ant-radio-wrapper){
  font-size: 12px;
}
::v-deep(.ant-checkbox-wrapper){
  font-size: 12px;
}

</style>
