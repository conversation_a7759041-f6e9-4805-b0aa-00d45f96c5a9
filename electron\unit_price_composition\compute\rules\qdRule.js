const {<PERSON>,<PERSON>} = require("@valuation/rules-engine");
/*
* 清单金额填充规则
* */
/*
* 清单行填充规则---------------------------------start-----------------------------------
* */
const baseFn = {
    "context":()=>{
        return {
            "from": "context",
            "kind": "",
            "column": "context"
        };
    },
    //定额综合单价
    "DE_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "total"
        };
    },
    //人工费合价
    "DE_RGF_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "totalRfee"
        };
    },
    //人工费合价(定额价)
    "DE_RGF_TOTAL_DEJ": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "totalRfeeDe"
        };  
    },
    //材料费合价
    "DE_CLF_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "totalCfee"
        };
    },
    //材料费合价(定额价)
    "DE_CLF_TOTAL_DEJ": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "totalCfeeDe"
        };
    },
    //机械费合价
    "DE_JXF_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "totalJfee"
        };
    },
    //机械费合价(定额价)
    "DE_JXF_TOTAL_DEJ": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "totalJfeeDe"
        };
    },
    //主材费合价
    "DE_ZCF_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "totalZcfee"
        };
    },
    //主材费合价(定额价)
    "DE_ZCF_TOTAL_DEJ": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "totalZcfeeDe"    
        };
    },
    //工日合价
    "DE_GR_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "totalGrPrice"    
        };
    },
    
    //设备费合价
    "DE_SBF_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "sbfTotal"
        };
    },
    //设备费合价(定额价)
    "DE_SBF_TOTAL_DEJ": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "sbfTotalDe"  
        };
    },
    
    //暂估合价
    "DE_ZGF_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "zgfTotal"
        };
    },
    //管理费合价
    "DE_GLF_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "totalManagerFee"
        };
    },
    //利润合价
    "DE_LR_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "totalProfitFee"
        };
    },
    //规费合价
    "DE_GF_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "gfTotal"
        };
    },
    //直接费合价
    "DE_ZJF_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "zjfTotal"
        };
    },
    "quantity": () => {
        return {
            "from": "qd",
            "kind": "",
            "column": "quantity"
        };
    },

    //生产工具使用费合价
    "DE_SCGJSYF_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "scgjsyfTotal"
        };
    },
    //繁华地段管理增加费合价
    "DE_FHDDGLZJF_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "fhddglzjfTotal"
        };
    },
    //冬季防寒费合价
    "DE_GJFHF_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "gjfhfTotal"
        };
    }
    ,
    //山地管护增加费合价
    "DE_SDGHZJF_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "sdghzjfTotal"
        };
    }
    ,
    //绿色施工安全防护措施费合价
    "DE_LSSGAQFHCSF_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "lssgaqfhcsfTotal"
        };
    },
    //进项税额合价
    "DE_JXSE_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "jxseTotal"
        };
    },
    //销项税额合价
    "DE_XXSE_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "xxseTotal"
        };
    },
    //增值税应纳税额合价
    "DE_ZZSYNSE_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "zzsynseTotal"
        };
    },
    //附加税费合价
    "DE_FJSE_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "fjseTotal"
        };
    },
    //税前工程造价合价
    "DE_SQGCZJ_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "sqgczjTotal"
        };
    },
    //风险费用合价
    "DE_FXFY_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "fxfyTotal"
        };
    },
    //税金合价
    "DE_SJ_TOTAL": () => {
        return {
            "from": "DE",
            "kind": "",
            "column": "sjTotal"
        };
    }

}
const rules = {
    'sbfPrice': Organ.create({
        name: 'sbfPrice',
        description: '设备费单价',
        gene: 'quantity>0?(NumberUtil.divide(sbfTotal,quantity)):0'
    }),
    "sbfTotal": Organ.create({name:"sbfTotal",description:"设备费合价",gene:"_.sum(DE_SBF_TOTAL)"}),

    'sbfPriceDe': Organ.create({
        name: 'sbfPriceDe',
        description: '设备费单价(定额价)',
        gene: 'quantity>0?(NumberUtil.divide(sbfTotalDe,quantity)):0'
    }),
    "sbfTotalDe": Organ.create({name:"sbfTotalDe",description:"设备费合价(定额价)",gene:"_.sum(DE_SBF_TOTAL_DEJ)"}),


    'zgfPrice': Organ.create({
        name: 'zgfPrice',
        description: '暂估单价',
        gene: 'quantity>0?(NumberUtil.divide(zgfTotal,quantity)):0'
    }),
    "zgfTotal": Organ.create({name:"zgfTotal",description:"暂估合价",gene:"_.sum(DE_ZGF_TOTAL)"}),
    'zjfPrice': Organ.create({
        name: 'zjfPrice',
        description: '直接费单价',
        gene: 'quantity>0?(NumberUtil.divide(zjfTotal,quantity)):0'
    }),
    "zjfTotal": Organ.create({name:"zjfTotal",description:"直接费合价",gene:"_.sum(DE_ZJF_TOTAL)"}),


    'rfee': Organ.create({
        name: 'rfee',
        description: '人工费单价',
        gene: 'quantity>0?(NumberUtil.costPriceAmountFormat(NumberUtil.divide(totalRfee,quantity))):0'
    }),
    "totalRfee": Organ.create({name:"totalRfee",description:"人工费合价",gene:"_.sum(DE_RGF_TOTAL)"}),
    'rfeeDe': Organ.create({
        name: 'rfeeDe',
        description: '人工费单价(定额价)',
        gene: 'quantity>0?(NumberUtil.costPriceAmountFormat(NumberUtil.divide(totalRfeeDe,quantity))):0'
    }),
    "totalRfeeDe": Organ.create({name:"totalRfeeDe",description:"人工费合价(定额价)",gene:"_.sum(DE_RGF_TOTAL_DEJ)"}),

    'cfee': Organ.create({
        name: 'cfee',
        description: '材料费单价',
        gene: 'quantity>0?(NumberUtil.divide(totalCfee,quantity)):0'
    }),
    "totalCfee": Organ.create({name:"totalCfee",description:"材料费合价",gene:"_.sum(DE_CLF_TOTAL)"}),
    'cfeeDe': Organ.create({
        name: 'cfeeDe',
        description: '材料费单价(定额价)',
        gene: 'quantity>0?(NumberUtil.divide(totalCfeeDe,quantity)):0'
    }),
    "totalCfeeDe": Organ.create({name:"totalCfeeDe",description:"材料费合价(定额价)",gene:"_.sum(DE_CLF_TOTAL_DEJ)"}),


    'jfee': Organ.create({
        name: 'jfee',
        description: '机械费单价',
        gene: 'quantity>0?(NumberUtil.divide(totalJfee,quantity)):0'
    }),
    "totalJfee": Organ.create({name:"totalJfee",description:"机械费合价",gene:"_.sum(DE_JXF_TOTAL)"}),
    'jfeeDe': Organ.create({
        name: 'jfeeDe',
        description: '机械费单价(定额价)',
        gene: 'quantity>0?(NumberUtil.divide(totalJfeeDe,quantity)):0'
    }),
    "totalJfeeDe": Organ.create({name:"totalJfeeDe",description:"机械费合价(定额价)",gene:"_.sum(DE_JXF_TOTAL_DEJ)"}),


    'profitFee': Organ.create({
        name: 'profitFee',
        description: '利润费单价',
        gene: 'quantity>0?(NumberUtil.divide(totalProfitFee,quantity)):0'
    }),
    "totalProfitFee": Organ.create({name:"totalProfitFee",description:"利润费合价",gene:"_.sum(DE_LR_TOTAL)"}),
    'managerFee': Organ.create({
        name: 'managerFee',
        description: '管理费单价',
        gene: 'quantity>0?(NumberUtil.divide(totalManagerFee,quantity)):0'
    }),
    "totalManagerFee": Organ.create({name:"totalManagerFee",description:"管理费合价",gene:"_.sum(DE_GLF_TOTAL)"}),

    'zcfee': Organ.create({
        name: 'zcfee',
        description: '主材费单间',
        gene: 'quantity>0?(NumberUtil.divide(totalZcfee,quantity)):0'
    }),
    "totalZcfee": Organ.create({name:"totalZcfee",description:"主材费合价",gene:"_.sum(DE_ZCF_TOTAL)"}),
    'zcfeeDe': Organ.create({
        name: 'zcfeeDe',
        description: '主材费单价(定额价)',
        gene: 'quantity>0?(NumberUtil.divide(totalZcfeeDe,quantity)):0'
    }),
    "totalZcfeeDe": Organ.create({name:"totalZcfeeDe",description:"主材费合价(定额价)",gene:"_.sum(DE_ZCF_TOTAL_DEJ)"}),


    'grPrice': Organ.create({
        name: 'grPrice',
        description: '工日单价',
        gene: 'quantity>0?(NumberUtil.divide(totalGrPrice,quantity)):0'
    }),
    "totalGrPrice": Organ.create({name:"totalGrPrice",description:"工日合价",gene:"_.sum(DE_GR_TOTAL)"}),


    "price": Organ.create({name:"price",description:"工程造价单价",gene:Gene.from(["quantity","DE_TOTAL"],({quantity,DE_TOTAL,_})=>{
            return _.sumBy(DE_TOTAL, pair => quantity > 0 ? (NumberUtil.divide(pair, quantity)) : 0);
    })}),
    //"total": Organ.create({name:"total",description:"工程造价合价",gene:"price*quantity"}),
    //"total": Organ.create({name:"total",description:"工程造价合价",gene:"NumberUtil.costPriceAmountFormat(NumberUtil.multiply(price,quantity))"}),
    "total": Organ.create({name:"total",description:"工程造价合价",gene:Gene.from(["price","DE_TOTAL","quantity","context"],({price,DE_TOTAL,quantity,context,_})=>{

        let total = 0
        //清单综合合价计算方式：1-清单综合合价=清单综合单价*清单工程量，2-清单综合合价=∑子目综合单价
        if('1'==context.projectConfig.budget.jiSuan.qdzhjsfs){
            total=  NumberUtil.costPriceAmountFormat(NumberUtil.multiply(price,quantity));
        }else {
            total=   _.sum(DE_TOTAL);
        }
        return total
        })}),
    'gfPrice': Organ.create({
        name: 'gfPrice',
        description: '规费单价',
        gene: 'quantity>0?(NumberUtil.divide(gfTotal,quantity)):0'
    }),
    "gfTotal": Organ.create({name:"gfTotal",description:"规费合价",gene:"_.sum(DE_GF_TOTAL)"}),
    'scgjsyfPrice': Organ.create({
        name: 'scgjsyfPrice',
        description: '生产工具使用费单价',
        gene: 'quantity>0?(NumberUtil.divide(scgjsyfTotal,quantity)):0'
    }),
    "scgjsyfTotal": Organ.create({name:"scgjsyfTotal",description:"生产工具使用费合价",gene:"_.sum(DE_SCGJSYF_TOTAL)"}),
    'fhddglzjfPrice': Organ.create({
        name: 'fhddglzjfPrice',
        description: '繁华地段管理增加费单价',
        gene: 'quantity>0?(NumberUtil.divide(fhddglzjfTotal,quantity)):0'
    }),
    "fhddglzjfTotal": Organ.create({name:"fhddglzjfTotal",description:"繁华地段管理增加费合价",gene:"_.sum(DE_FHDDGLZJF_TOTAL)"}),
    'gjfhfPrice': Organ.create({
        name: 'gjfhfPrice',
        description: '冬季防寒费单价',
        gene: 'quantity>0?(NumberUtil.divide(gjfhfTotal,quantity)):0'
    }),
    "gjfhfTotal": Organ.create({name:"gjfhfTotal",description:"冬季防寒费合价",gene:"_.sum(DE_GJFHF_TOTAL)"}),
    'sdghzjfPrice': Organ.create({
        name: 'sdghzjfPrice',
        description: '山地管护增加费单价',
        gene: 'quantity>0?(NumberUtil.divide(sdghzjfTotal,quantity)):0'
    }),
    "sdghzjfTotal": Organ.create({name:"sdghzjfTotal",description:"山地管护增加费合价",gene:"_.sum(DE_SDGHZJF_TOTAL)"}),
    'lssgaqfhcsfPrice': Organ.create({
        name: 'lssgaqfhcsfPrice',
        description: '绿色施工安全防护措施费单价',
        gene: 'quantity>0?(NumberUtil.divide(lssgaqfhcsfTotal,quantity)):0'
    }),
    "lssgaqfhcsfTotal": Organ.create({name:"lssgaqfhcsfTotal",description:"绿色施工安全防护措施费合价",gene:"_.sum(DE_LSSGAQFHCSF_TOTAL)"}),
    'jxsePrice': Organ.create({
        name: 'jxsePrice',
        description: '进项税额单价',
        gene: 'quantity>0?(NumberUtil.divide(jxseTotal,quantity)):0'
    }),
    "jxseTotal": Organ.create({name:"jxseTotal",description:"进项税额合价",gene:"_.sum(DE_JXSE_TOTAL)"}),
    'xxsePrice': Organ.create({
        name: 'xxsePrice',
        description: '销项税额单价',
        gene: 'quantity>0?(NumberUtil.divide(xxseTotal,quantity)):0'
    }),
    "xxseTotal": Organ.create({name:"xxseTotal",description:"销项税额合价",gene:"_.sum(DE_XXSE_TOTAL)"}),
    'zzsynsePrice': Organ.create({
        name: 'zzsynsePrice',
        description: '增值税应纳税额单价',
        gene: 'quantity>0?(NumberUtil.divide(zzsynseTotal,quantity)):0'
    }),
    "zzsynseTotal": Organ.create({name:"zzsynseTotal",description:"增值税应纳税额合价",gene:"_.sum(DE_ZZSYNSE_TOTAL)"}),
    'fjsePrice': Organ.create({
        name: 'fjsePrice',
        description: '附加税费单价',
        gene: 'quantity>0?(NumberUtil.divide(fjseTotal,quantity)):0'
    }),
    "fjseTotal": Organ.create({name:"fjseTotal",description:"附加税费合价",gene:"_.sum(DE_FJSE_TOTAL)"}),
    'sqgczjPrice': Organ.create({
        name: 'sqgczjPrice',
        description: '税前工程造价单价',
        gene: 'quantity>0?(NumberUtil.divide(sqgczjTotal,quantity)):0'
    }),
    "sqgczjTotal": Organ.create({name:"sqgczjTotal",description:"税前工程造价合价",gene:"_.sum(DE_SQGCZJ_TOTAL)"}),
    'fxfyPrice': Organ.create({
        name: 'fxfyPrice',
        description: '风险费用单价',
        gene: 'quantity>0?(NumberUtil.divide(fxfyTotal,quantity)):0'
    }),
    "fxfyTotal": Organ.create({name:"fxfyTotal",description:"风险费用合价",gene:"_.sum(DE_FXFY_TOTAL)"}),
    'sjPrice': Organ.create({
        name: 'sjPrice',
        description: '税金单价',
        gene: 'quantity>0?(NumberUtil.divide(sjTotal,quantity)):0'
    }),
    "sjTotal": Organ.create({name:"sjTotal",description:"税金合价",gene:"_.sum(DE_SJ_TOTAL)"})
}
/*
* 清单行填充规则---------------------------end-------------------------------
* */

/*
* 单价构成计算规则---------------------------start-------------------------------
* */

const upcBaseFn = {
    //直接费合价
    "UPC_ZJF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_ZJF",
            "column": "displayAllPrice"
        };
    }
    ,
    //利润费合价
    "UPC_LR_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_LR",
            "column": "displayAllPrice"
        };
    },

    //管理费合价
    "UPC_GLF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_GLF",
            "column": "displayAllPrice"
        };
    },
    //工程造价合价
    "UPC_GCZJ_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_GCZJ",
            "column": "displayAllPrice"
        };
    },
    //生产工具使用费
    "UPC_SCGJSYF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_SCGJSYF",
            "column": "displayAllPrice"
        };
    },
    //繁华地段管理增加费
    "UPC_FHDDGLZJF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_FHDDGLZJF",
            "column": "displayAllPrice"
        };
    },
    //冬季防寒费
    "UPC_GJFHF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_GJFHF",
            "column": "displayAllPrice"
        };
    },
    //山地管理增加费
    "UPC_SDGLZJF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_SDGLZJF",
            "column": "displayAllPrice"
        };
    },
    //绿色施工安全防护措施费
    "UPC_LSSGAQFHCSF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_LSSGAQFHCSF",
            "column": "displayAllPrice"
        };
    },
    //税金
    "UPC_SJ_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_SJ",
            "column": "displayAllPrice"
        };
    },
    //风险费用
    "UPC_FXFY_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_FXFY",
            "column": "displayAllPrice"
        };
    },
    //规费明细
    "UPC_GFMX_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_GFMX",
            "column": "displayAllPrice"
        };
    },
    //社会保障费
    "UPC_SHBZF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_SHBZF",
            "column": "displayAllPrice"
        };
    },
    //综合费
    "UPC_ZHF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_ZHF",
            "column": "displayAllPrice"
        };
    },
    //人工费调整额
    "UPC_RGFTZE_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_ZHF",
            "column": "displayAllPrice"
        };
    },
    //现场管理费
    "UPC_ZCGLF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_ZCGLF",
            "column": "displayAllPrice"
        };
    },
    //
    //企业管理费
    "UPC_QYGLF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_QYGLF",
            "column": "displayAllPrice"
        };
    },
    //财务费
    "UPC_CWF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_QYGLF",
            "column": "displayAllPrice"
        };
    },
    //职工失业保险
    "UPC_ZGSYBX_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_ZGSYBX",
            "column": "displayAllPrice"
        };
    },
    //医疗保险费
    "UPC_YLBXF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_YLBXF",
            "column": "displayAllPrice"
        };
    },
    //单价措施项目费
    "UPC_DJCSXMF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_DJCSXMF",
            "column": "displayAllPrice"
        };
    },
    //其他总价措施项目费
    "UPC_QTZJCSXMF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_QTZJCSXMF",
            "column": "displayAllPrice"
        };
    },
    //安全文明施工费基本费
    "UPC_AQWMSGFJBF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_AQWMSGFJBF",
            "column": "displayAllPrice"
        };
    },
    //安全文明施工费增加费
    "UPC_AQWMSGFZJF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_AQWMSGFZJF",
            "column": "displayAllPrice"
        };
    },

    //规费合价
    "UPC_GF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_GF",
            "column": "displayAllPrice"
        };
    },
    //安全生产、文明施工费
    "UPC_AWF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_AWF",
            "column": "displayAllPrice"
        };
    },
    //生产工具使用费合价
    "UPC_SCGJSYF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_SCGJSYF",
            "column": "displayAllPrice"
        };
    },
    //繁华地段管理增加费合价
    "UPC_FHDDGLZJF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_FHDDGLZJF",
            "column": "displayAllPrice"
        };
    },
    //冬季防寒费合价
    "UPC_GJFHF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_GJFHF",
            "column": "displayAllPrice"
        };
    },
    //山地管护增加费合价
    "UPC_SDGLZJF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_SDGLZJF",
            "column": "displayAllPrice"
        };
    },
    //绿色施工安全防护措施费合价
    "UPC_LSSGAQFHCSF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_LSSGAQFHCSF",
            "column": "displayAllPrice"
        };
    },
    //进项税额合价
    "UPC_JXSE_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_JXSE",
            "column": "displayAllPrice"
        };
    },
    //进项税额合价
    "UPC_FJSE_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_FJSE",
            "column": "displayAllPrice"
        };
    },
    "UPC_YLSSWHF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_YLSSWHF",
            "column": "displayAllPrice"
        };
    },
    "UPC_LHSSWHF_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_LHSSWHF",
            "column": "displayAllPrice"
        };
    },

    //销项税额合价
    "UPC_XXSE_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_XXSE",
            "column": "displayAllPrice"
        };
    },
    //增值税应纳税额合价
    "UPC_ZZSYNSE_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_ZZSYNSE",
            "column": "displayAllPrice"
        };
    },
    //税前工程造价合价
    "UPC_SQGCZJ_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_SQGCZJ",
            "column": "displayAllPrice"
        };
    },
    //风险费用合价
    "UPC_FXFY_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_FXFY",
            "column": "displayAllPrice"
        };
    },
    //税金合价
    "UPC_SJ_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_SJ",
            "column": "displayAllPrice"
        };
    },
    //税金合价
    "UPC_SJ_TOTAL": () => {
        return {
            "from": "UPC",
            "kind": "UPC_SJ",
            "column": "displayAllPrice"
        };
    },


};
//单价构成字段规则
const upcRules = {
    "UPC_ZJF_allPrice": Organ.create({name:"UPC_ZJF_allPrice",description:"直接费",gene:"_.sum(UPC_ZJF_TOTAL)"}),
    "UPC_GLF_allPrice": Organ.create({name:"UPC_GLF_allPrice",description:"管理费",gene:"_.sum(UPC_GLF_TOTAL)"}),
    "UPC_LR_allPrice": Organ.create({name:"UPC_LR_allPrice",description:"利润",gene:"_.sum(UPC_LR_TOTAL)"}),
    "UPC_GF_allPrice": Organ.create({name:"UPC_GF_allPrice",description:"规费",gene:"_.sum(UPC_GF_TOTAL)"}),
    "UPC_AWF_allPrice": Organ.create({name:"UPC_AWF_allPrice",description:"安全生产、文明施工费",gene:"_.sum(UPC_AWF_TOTAL)"}),
    "UPC_GCZJ_allPrice": Organ.create({name:"UPC_GCZJ_allPrice",description:"工程造价",gene:"_.sum(UPC_GCZJ_TOTAL)"}),
    "UPC_RGF_allPrice": Organ.create({name:"UPC_RGF_allPrice",description:"人工费",gene:"_.sum(DE_RGF_TOTAL)"}),
    "UPC_CLF_allPrice": Organ.create({name:"UPC_CLF_allPrice",description:"材料费",gene:"_.sum(DE_CLF_TOTAL)"}),
    "UPC_JXF_allPrice": Organ.create({name:"UPC_JXF_allPrice",description:"机械费",gene:"_.sum(DE_JXF_TOTAL)"}),
    "UPC_SBF_allPrice": Organ.create({name:"UPC_SBF_allPrice",description:"设备费",gene:"_.sum(DE_SBF_TOTAL)"}),
    "UPC_ZCF_allPrice": Organ.create({name:"UPC_ZCF_allPrice",description:"主材费",gene:"_.sum(DE_ZCF_TOTAL)"}),
    "UPC_SCGJSYF_allPrice": Organ.create({name:"UPC_SCGJSYF_allPrice",description:"生产工具使用费",gene:"_.sum(UPC_SCGJSYF_TOTAL)"}),
    "UPC_FHDDGLZJF_allPrice": Organ.create({name:"UPC_FHDDGLZJF_allPrice",description:"繁华地段管理增加费",gene:"_.sum(UPC_FHDDGLZJF_TOTAL)"}),
    "UPC_GJFHF_allPrice": Organ.create({name:"UPC_GJFHF_allPrice",description:"冬季防寒费",gene:"_.sum(UPC_GJFHF_TOTAL)"}),
    "UPC_SDGLZJF_allPrice": Organ.create({name:"UPC_SDGLZJF_allPrice",description:"山地管理增加费",gene:"_.sum(UPC_SDGLZJF_TOTAL)"}),
    "UPC_LSSGAQFHCSF_allPrice": Organ.create({name:"UPC_LSSGAQFHCSF_allPrice",description:"绿色施工安全防护措施费",gene:"_.sum(UPC_LSSGAQFHCSF_TOTAL)"}),
    "UPC_SJ_allPrice": Organ.create({name:"UPC_SJ_allPrice",description:"税金",gene:"_.sum(UPC_SJ_TOTAL)"}),
    "UPC_FXFY_allPrice": Organ.create({name:"UPC_FXFY_allPrice",description:"风险费用",gene:"_.sum(UPC_FXFY_TOTAL)"}),
    "UPC_GFMX_allPrice": Organ.create({name:"UPC_GFMX_allPrice",description:"风险费用",gene:"_.sum(UPC_GFMX_TOTAL)"}),
    "UPC_SHBZF_allPrice": Organ.create({name:"UPC_SHBZF_allPrice",description:"社会保障费",gene:"_.sum(UPC_SHBZF_TOTAL)"}),
    "UPC_ZHF_allPrice": Organ.create({name:"UPC_ZHF_allPrice",description:"综合费",gene:"_.sum(UPC_ZHF_TOTAL)"}),
    "UPC_RGFTZE_allPrice": Organ.create({name:"UPC_RGFTZE_allPrice",description:"人工费调整额",gene:"_.sum(UPC_RGFTZE_TOTAL)"}),
    "UPC_ZCGLF_allPrice": Organ.create({name:"UPC_ZCGLF_allPrice",description:"现场管理费",gene:"_.sum(UPC_ZCGLF_TOTAL)"}),
    "UPC_QYGLF_allPrice": Organ.create({name:"UPC_QYGLF_allPrice",description:"企业管理费",gene:"_.sum(UPC_QYGLF_TOTAL)"}),
    "UPC_CWF_allPrice": Organ.create({name:"UPC_CWF_allPrice",description:"财务费",gene:"_.sum(UPC_CWF_TOTAL)"}),
    "UPC_ZGSYBX_allPrice": Organ.create({name:"UPC_ZGSYBX_allPrice",description:"职工失业保险",gene:"_.sum(UPC_ZGSYBX_TOTAL)"}),
    "UPC_YLBXF_allPrice": Organ.create({name:"UPC_YLBXF_allPrice",description:"医疗保险费",gene:"_.sum(UPC_YLBXF_TOTAL)"}),
    "UPC_DJCSXMF_allPrice": Organ.create({name:"UPC_DJCSXMF_allPrice",description:"单价措施项目费",gene:"_.sum(UPC_DJCSXMF_TOTAL)"}),
    "UPC_QTZJCSXMF_allPrice": Organ.create({name:"UPC_QTZJCSXMF_allPrice",description:"其他总价措施项目费",gene:"_.sum(UPC_QTZJCSXMF_TOTAL)"}),
    "UPC_AQWMSGFJBF_allPrice": Organ.create({name:"UPC_AQWMSGFJBF_allPrice",description:"安全文明施工费基本费",gene:"_.sum(UPC_AQWMSGFJBF_TOTAL)"}),
    "UPC_AQWMSGFZJF_allPrice": Organ.create({name:"UPC_AQWMSGFZJF_allPrice",description:"安全文明施工费增加费",gene:"_.sum(UPC_AQWMSGFZJF_TOTAL)"}),
    "UPC_SQGCZJ_allPrice": Organ.create({name:"UPC_SQGCZJ_allPrice",description:"税前工程造价",gene:"_.sum(UPC_SQGCZJ_TOTAL)"}),
    "UPC_YLSSWHF_allPrice": Organ.create({name:"UPC_YLSSWHF_allPrice",description:"园林设施维护费",gene:"_.sum(UPC_YLSSWHF_TOTAL)"}),
    "UPC_LHSSWHF_allPrice": Organ.create({name:"UPC_LHSSWHF_allPrice",description:"亮化设施维护费",gene:"_.sum(UPC_LHSSWHF_TOTAL)"})
}
const upcRules2012 = {
    "UPC_JXSE_allPrice": Organ.create({name:"UPC_JXSE_allPrice",description:"进项税额",gene:"_.sum(UPC_JXSE_TOTAL)"}),
    "UPC_XXSE_allPrice": Organ.create({name:"UPC_XXSE_allPrice",description:"进项税额",gene:"_.sum(UPC_XXSE_TOTAL)"}),
    "UPC_ZZSYNSE_allPrice": Organ.create({name:"UPC_ZZSYNSE_allPrice",description:"增值税应纳税额",gene:"_.sum(UPC_ZZSYNSE_TOTAL)"}),
    "UPC_FJSE_allPrice": Organ.create({name:"UPC_FJSE_allPrice",description:"附加税额",gene:"_.sum(UPC_FJSE_TOTAL)"})
}
/*
* 单价构成计算规则---------------------------end-------------------------------
* */


module.exports = {
    qdFillBaseRule: baseFn,
    qdFillRules: rules,
    qdUPCBaseRule: upcBaseFn,
    qdUPCRules: upcRules,
    qdUPCRules2012: {...upcRules, ...upcRules2012}
}

