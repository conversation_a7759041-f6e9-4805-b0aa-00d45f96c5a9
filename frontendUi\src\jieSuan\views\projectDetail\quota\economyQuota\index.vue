<template>
  <div class="table-content subItem-project custom-tree-table zbFxBox">
    <s-table v-if="loadingTable" :columns="projectStore.tabSelectName == '主要经济指标' ? columnArr1 : columnArr2"
      size="small" ref="stableRef" class="s-table" row-key="id" :virtual-scroll="false" :defaultExpandAllRows="true"
      bordered :custom-cell="customCell" :delay="200" :rangeSelection="true" :scroll="{ y: stableHeight }"
      :animateRows="false" :pagination="false" :data-source="tableData">
      <!--自定义内容 -->
      <template #bodyCell="{
        text,
        record: row,
        index,
        column,
        key,
        openEditor,
        closeEditor,
      }">
        <div class="cell-line-break-el" v-if="column.field === 'name'">
          <icon-font v-if="row.isLocked" type="icon-qingdan-suoding"></icon-font>
          <i @click.stop="changeStatus(row)" v-if="row.displaySign === 1 && tableData.length > 1"
            class="vxe-icon-caret-down"></i>
          <i @click.stop="changeStatus(row)" v-if="row.displaySign === 2" class="vxe-icon-caret-right"></i>
          <span style="display:inline-block;margin-left: 10px;">{{ row.name }}</span>
        </div>
      </template>
    </s-table>
    <!-- 设置查看范围 -->
    <setViewingRange ref="setViewingRangeRef" @refresh="getAnalysisType"></setViewingRange>
    <!-- 参考指标设置 -->
    <referenceIndicator ref="referenceIndicatorRef"></referenceIndicator>
    <!-- 匹配指标 -->
    <matchingIndicators ref="matchingIndicatorsRef" @refresh="getAnalysisType"></matchingIndicators>
  </div>
</template>

<script setup>
import { onMounted,reactive, ref, watch,onActivated,getCurrentInstance,} from 'vue';
import jiesuanApi from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
import { updateOperateByName } from '@/jieSuan/views/projectDetail/quota/operate';
import setViewingRange from '../components/setViewingRange.vue';
import referenceIndicator from '../components/referenceIndicator.vue';
import matchingIndicators from '../components/matchingIndicators.vue';
import xeUtils from 'xe-utils';
const props = defineProps(['currentInfo']);
const emits = defineEmits(['update:visible', 'updateData']);
import {
  customCell,
} from './classAndStyleMethod';

import { insetBus } from '@/hooks/jieSuanZbInsetBus';
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const projectStore = projectDetailStore();
const oldTableData = ref([]);
const tableData = ref([]);
const loadingTable = ref(false);
const selProjectActive=ref(1)
const stableHeight = ref(400);
const setViewingRangeRef = ref()
const referenceIndicatorRef = ref()
const matchingIndicatorsRef = ref()
const columnArr1=ref([
  {
    title: '序号',
    dataIndex: 'disp',
    field: 'disp',
    key: 'disp',
    width:50,
    align:'center',
    fixed: 'left',
  },
  {
    title: '科目名称',
    dataIndex: 'name',
    field: 'name',
    key: 'name',
    autoHeight:true,
    width:200,
    fixed: 'left',
  },
  {
    title: '造价（元）',
    dataIndex: 'price',
    field: 'price',
    key: 'price',
    width:100,
    align: 'center',
  },
  {
    title: '单方造价',
    dataIndex: 'unitPrice',
    field: 'unitPrice',
    key: 'unitPrice',
    width:100,
    align: 'center',
  },
  {
    title: '造价占比（%）',
    dataIndex: 'proportionPrice',
    field: 'proportionPrice',
    key: 'proportionPrice',
    width:100,
    align: 'center',
  },
  {
    title: '计算口径',
    dataIndex: 'indexCountCaliber',
    field: 'indexCountCaliber',
    key: 'indexCountCaliber',
    width:100,
    align: 'center',
  },
])
const columnArr2=ref([
  {
    title: '序号',
    dataIndex: 'disp',
    field: 'disp',
    key: 'disp',
    align:'center',
    width:50,
    fixed: 'left',
  },
  {
    title: '科目名称',
    dataIndex: 'name',
    field: 'name',
    key: 'name',
    width:200,
    fixed: 'left',
  },
  {
    title: '工程量',
    dataIndex: 'quantity',
    field: 'quantity',
    key: 'quantity',
    width:100,
    align: 'center',
  },
  {
    title: '单方量',
    dataIndex: 'unitQuantity',
    field: 'unitQuantity',
    key: 'unitQuantity',
    width:100,
    align: 'center',
  },
  {
    title: '计算口径',
    dataIndex: 'indexCountCaliber',
    field: 'indexCountCaliber',
    key: 'indexCountCaliber',
    width:100,
    align: 'center',
  },
])
onMounted(()=>{
  let tableEl = document.querySelector('.table-content');
  stableHeight.value = tableEl.clientHeight - 50;
})
watch(
  () => projectStore.tabSelectName,
  () => {
    setFxList()
    if(['主要经济指标','主要工程量指标','主要工料指标'].includes(projectStore.tabSelectName)){
      setTimeout(()=>{
        getAnalysisType()
      },200)
    }
  }
);
onActivated(() => {
  setFxList()
  insetBus(bus, projectStore.componentId?projectStore.componentId:'economyQuota' , 'economyQuota', async data => {
    //匹配指标
    if (data.name === 'matching-indicators'){
      matchingIndicatorsRef.value.open(true);
    };
    //设置查看范围
    if (data.name === 'set-viewing-range'){
      setViewingRangeRef.value.open(true);
    };
    //参考指标设置
    if (data.name === 'reference-indicator-setting'){
      referenceIndicatorRef.value.open(true);
    };
    //分析方式
    if (data.name === 'analysis-method'){
      updateOperateByName('analysis-method', info => {
        if(data.activeKind!==undefined){
          info.value = data.activeKind;
          selProjectActive.value=data.activeKind
          getAnalysisType('edit',data.activeKind)
        }
      });
    };
  });
  if(['主要经济指标','主要工程量指标','主要工料指标'].includes(projectStore.tabSelectName)){
    setTimeout(()=>{
      getAnalysisType()
    },200)
  }
})
// 设置分析方式下拉项
const setFxList = () => {
  if(['主要经济指标','主要工程量指标','主要工料指标'].includes(projectStore.tabSelectName)){
    let arr=[
      {
        type: 1,
        name: '按工程分析',
        kind: 1,
        isValid: true,
      },
      {
        type: 2,
        name: '按专业分析',
        kind: 2,
        isValid: true,
      },
      {
        type: 3,
        name: '按费用分析',
        kind: 3,
        isValid: true,
      },
    ]
    if(projectStore.tabSelectName!=='主要经济指标'){
      arr.splice(2,1)
    }
    updateOperateByName('analysis-method', info => {
      info.options = arr
    });
  }
}

// 获取、修改指标方式
const getAnalysisType = (type='get',method) => {
  loadingTable.value=false
  let apiData={
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    type:projectStore.tabSelectName=='主要经济指标'?1:projectStore.tabSelectName=='主要工程量指标'?2:3,
  }
  let aipFun=jiesuanApi.getIndexOfFenXiColl
  // 如果是修改
  if(type=='edit'){
    apiData['method']=method
    aipFun=jiesuanApi.updateIndexOfFenXiColl
  }
  console.info('获取指标方式参数',apiData)
  aipFun(apiData).then((res) => {
    console.info('获取指标方式返回结果',res)
    if(res.code!==200){
      return message.error(res.message)
    }
    if(type=='get'){
      let selKey = res.result==undefined?1:res.result;
      updateOperateByName('analysis-method', info => {
        info.value = selKey
        selProjectActive.value = selKey
      });
      updateOperateByName('set-viewing-range', info => {
        info.disabled = selKey==1?false:true
      });
    }else{
      updateOperateByName('set-viewing-range', info => {
        info.disabled = method==1?false:true
      });
    }
    
    getDataList()
  });
}
const getDataList = () => {
  tableData.value=[]
  let apiData={
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    fenXiType:projectStore.tabSelectName=='主要经济指标'?1:projectStore.tabSelectName=='主要工程量指标'?2:3,
    fenXiMethod:selProjectActive.value
  }
  console.info('获取指标分析列表数据参数',apiData)
  jiesuanApi.queryUnitIndexFenXiColl(apiData).then((res) => {
    console.info('获取指标分析列表数据返回结果',res)
    if(res.code!==200){
      return message.error(res.message)
    }
    let datas=res.result
    if(datas==undefined){
      return;
    }
    let testTreeData =  xeUtils.toArrayTree(datas, {
      key: 'id',
      parentKey: 'parentId',
    });
    testTreeData=xeUtils.toTreeArray(addLevelToTree(testTreeData)).map(i => {
      i.key = i.id;
      delete i.children;
      return i;
    });
    console.info('指标分析列表数据',testTreeData)
    tableData.value=testTreeData
    oldTableData.value=testTreeData
    loadingTable.value=true
  });
}
const addLevelToTree = (data, parentLevel = 0, parent = null) => {
  return data.map(node => {
    node.customParent = parent; // 自定义字段父级数据
    const { children, ...other } = node;
    return {
      ...node,
      customLevel: parentLevel + 1,
      displaySign:(node.children || []).length > 0 ? 1 : 0,
      children:
        (node.children || []).length > 0
          ? addLevelToTree(node.children, parentLevel + 1, other)
          : [],
    };
  });
};
const changeStatus = row => {
  if (row.displaySign === 1) {
    row.displaySign = 2;
    tableData.value = removeItemsByParentId(tableData.value,row.id)
  } else if (row.displaySign === 2) {
    row.displaySign = 1;
    let oldData=JSON.parse(JSON.stringify(oldTableData.value))
    let datas = getItemsByParentId(oldData,row.id)
    let nowData=JSON.parse(JSON.stringify(tableData.value))
    let index = nowData.findIndex(x => x.id === row.id);
    nowData.splice(index+1, 0, ...datas);
    let testTreeData =  xeUtils.toArrayTree(nowData, {
      key: 'id',
      parentKey: 'parentId',
    });
    testTreeData=xeUtils.toTreeArray(testTreeData).map(i => {
      i.key = i.id;
      delete i.children;
      return i;
    });
    tableData.value = testTreeData
  }
};
// 递归删除函数
function removeItemsByParentId(data, parentId) {
  // 过滤掉所有 parentId 等于 parentId 的数据项
  let filteredData = data.filter(item => item.parentId !== parentId);
  // 找到所有被删除的项的 id
  const deletedIds = data.filter(item => item.parentId === parentId).map(item => item.id);
  // 递归删除所有 parentId 等于被删除项 id 的数据项
  deletedIds.forEach(deletedId => {
    filteredData = removeItemsByParentId(filteredData, deletedId);
  });
  return filteredData;
}
// 递归获取函数
function getItemsByParentId(data, parentId) {
  // 获取所有 parentId 等于 parentId 的数据项
  let foundItems = data.filter(item => item.parentId === parentId);
  // 找到所有被获取的项的 id
  const foundIds = foundItems.map(item => item.id);
  // 递归获取所有 parentId 等于被获取项 id 的数据项
  foundIds.forEach(foundId => {
    const moreItems = getItemsByParentId(data, foundId);
    foundItems = foundItems.concat(moreItems);
  });
  return foundItems;
}
defineExpose({
  getDataList,
});
</script>
<style lang="scss" src="@/assets/css/subItemProject.scss" scoped></style>
<style lang="scss" scoped>
@import '@/views/projectDetail/customize/subItemProject/s-table.scss';

.zbFxBox {
  width: 660px;
}
</style>
