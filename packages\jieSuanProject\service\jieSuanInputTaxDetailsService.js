const { ObjectUtils } = require('../../../electron/utils/ObjectUtils');
const { Service } = require('../../../core');
const { PricingFileFindUtils } = require('../../../electron/utils/PricingFileFindUtils');
const { NumberUtil } = require('../../../electron/utils/NumberUtil');

class JieSuanInputTaxDetailsService extends Service{

    constructor(ctx) {
        super(ctx);
    }


    /**
     * 计算进项税明细
     * @param args
     * @returns {Promise<void>}
     */
    async countInputTaxDetails(unit){
        //进项税明细
        let inputTaxDetails = unit.inputTaxDetails;
        if(ObjectUtils.isEmpty(inputTaxDetails)){
            return ;
        }
        let unitCostCodePrices = unit.unitCostCodePrices;
        //费用代码<费用代码,price>
        let priceMap = new Map();
        //费用代码
        for (let i = 0; i < unitCostCodePrices.length; i++) {
            let unitCostCodePrice = unitCostCodePrices[i];
            priceMap.set(unitCostCodePrice.code, unitCostCodePrice.price??0)
        }
        for (let i = 0; i < inputTaxDetails.length; i++) {
            await this.countInputTax(inputTaxDetails[i], priceMap);
        }

    }


    /**
     * 计算一行进项税明细
     * @param inputTaxDetail
     * @param priceMap
     * @param rate
     */
    countInputTax(inputTaxDetail, priceMap,rate) {
        //计算基数
        let calculateFormula = inputTaxDetail.calculateFormula;
        //存放替换后的计算公式
        let afterCalculateFormula = calculateFormula;
        // 分解字符串成表达式和变量名
        const variablesToReplace = calculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
        for (let variable of variablesToReplace) {
            if (priceMap.has(variable)) {
                if (priceMap.get(variable) < 0) {
                    afterCalculateFormula = afterCalculateFormula.replace(variable, '(' + priceMap.get(variable) + ')');
                } else {
                    afterCalculateFormula = afterCalculateFormula.replace(variable, priceMap.get(variable));
                }
            } else {
                afterCalculateFormula = afterCalculateFormula.replace(variable, 0);
            }
        }

        let result;
        let jieSuanResult;
        if (ObjectUtils.isNotEmpty(inputTaxDetail.rate)) {
            result = parseFloat((eval(afterCalculateFormula) * inputTaxDetail.rate / 100).toFixed(2));
        } else {
            result = eval(afterCalculateFormula).toFixed(2);
        }

        if (ObjectUtils.isNotEmpty(inputTaxDetail.jieSuanRate)) {
            jieSuanResult = parseFloat((eval(afterCalculateFormula) * inputTaxDetail.jieSuanRate / 100).toFixed(2));
        } else {
            jieSuanResult = eval(afterCalculateFormula).toFixed(2);
        }

        inputTaxDetail.price = NumberUtil.costPriceAmountFormat(result);
        inputTaxDetail.jieSuanPrice = NumberUtil.costPriceAmountFormat(jieSuanResult);
        if(inputTaxDetail.name==='计日工进项税额' || inputTaxDetail.name==='进项税额合计' ||inputTaxDetail.name==='价差材料费进项税额' || inputTaxDetail.name==='价差机械费进项税额'
          || inputTaxDetail.name==='价差设备费进项税额' || inputTaxDetail.name==='价差安全生产、文明施工费进项税额' || inputTaxDetail.name==='进项税额合计(调差后)'){
            inputTaxDetail.price = '';
        }
    }


    /**
     * 修改进项税
     * @param args
     * @returns {Promise<void>}
     */
    async updateInputTaxDetails(args){
        let inputTaxDetails = this.getInputTaxDetails(args);
        let find = inputTaxDetails.find(item =>item.sequenceNbr === args.sequenceNbr);
        if(ObjectUtils.isNotEmpty(find)){
            find.remark = args.remark;
        }
        if(ObjectUtils.isNotEmpty(args.jieSuanRate)){
            find.jieSuanRate = args.jieSuanRate;
            let unit = await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId)
            //费用代码<费用代码,price>
            let priceMap = new Map();
            //费用代码
            let unitCostCodePrices = unit.unitCostCodePrices;
            for (let i = 0; i < unitCostCodePrices.length; i++) {
                let unitCostCodePrice = unitCostCodePrices[i];
                priceMap.set(unitCostCodePrice.code, unitCostCodePrice.price)
            }
            await this.countInputTax(find, priceMap);
            await this.service.unitCostCodePriceService.countCostCodePrice(args);
            let otherProjects = unit.otherProjects;
            let otherProject =  otherProjects.find(element =>element.type = find.calculateFormula);
            otherProject.taxRemoval = find.jieSuanRate;
            await this.service.otherProjectService.updateAllOtherProjects(args);
        }

    }

    /**
     * 查询
     * @param args
     * @returns {Array<UnitCostSummary>}
     */
    getInputTaxDetails(args){
        let inputTaxDetails = PricingFileFindUtils.getInputTaxDetails(args.constructId,args.singleId, args.unitId);
        return  inputTaxDetails;
    }

}
JieSuanInputTaxDetailsService.toString = () => '[class JieSuanInputTaxDetailsService]';
module.exports = JieSuanInputTaxDetailsService;
