<template>
  <common-modal
    className="dialog-comm"
    title="补充人材机"
    width="auto"
    v-model:modelValue="props.visible"
    :title="dialogTitle"
    @cancel="cancel"
    @close="cancel"
  >
    <!-- align="left" -->
    <div class="form-wrap">
      <div class="form-content">
        <a-spin tip="解析中..." :spinning="spinning">
          <a-form
            :model="inputData"
            :label-col="labelCol"
            ref="form"
            @finish="onSubmit"
          >
            <a-form-item
              label="编码"
              name="materialCode"
              class="form-item-two"
              :rules="[
                {
                  required: true,
                  message: '请输入材料编码！',
                },
              ]"
            >
              <a-input
                v-model:value.trim="inputData.materialCode"
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item
              label="类型"
              class="form-item-two"
              name="kind"
              :rules="[{ required: true, message: '请选择类型！' }]"
            >
              <a-select
                :disabled="ispbs"
                v-model:value="inputData.kind"
                :options="lists.rcjTypeList"
                placeholder="请选择"
                :size="colStyle.colSize"
                @change="selectChange"
              ></a-select>
            </a-form-item>
            <a-form-item
              label="名称"
              name="materialName"
              class="form-item-one"
              :rules="[{ required: true, message: '请输入材料名称！' }]"
            >
              <a-input
                v-model:value.trim="inputData.materialName"
                placeholder="请输入"
              />
            </a-form-item>

            <a-form-item
              label="单位"
              name="unit"
              class="form-item-two"
              :rules="[{ required: true, message: '请选择或输入单位！' }]"
            >
              <vxeTableEditSelect
                v-if="props.visible"
                :filedValue="inputData.unit"
                :list="projectStore.unitListString"
                :isNotLimit="true"
                :transfer="true"
                @update:filedValue="
                  newValue => {
                    saveCustomInput(newValue, inputData, 'unit', $rowIndex);
                  }
                "
              ></vxeTableEditSelect>
            </a-form-item>

            <a-form-item
              label="消耗量"
              v-if="showResqty"
              name="resQty"
              class="form-item-two"
            >
              <a-input
                v-model:value.trim="inputData.resQty"
                placeholder="请输入"
                @blur="inputData.resQty = pureNumber0(inputData.resQty)"
              />
            </a-form-item>

            <a-form-item
              label="规格型号"
              name="constructName"
              class="form-item-one"
            >
              <a-input
                v-model:value.trim="inputData.specification"
                placeholder="请输入"
              />
            </a-form-item>

            <a-form-item
              :label="`${projectStore.taxMade == 1 ? '不' : ''}含税市场价(元)`"
              name="dePrice"
              class="form-item-two"
              :rules="[
                {
                  required: true,
                  message: `请输入！`,
                },
              ]"
            >
              <a-input
                v-model:value.trim="inputData.dePrice"
                placeholder="请输入"
                @blur="() => {}"
              />
            </a-form-item>
            <!-- <a-form-item
              label="除税系数(%)"
              name="taxRemoval"
              class="form-item-two"
            >
              <a-input
                v-model:value.trim="inputData.taxRemoval"
                placeholder="请输入"
                @blur="
                  inputData.taxRemoval = pureNumber0(
                    inputData.taxRemoval
                  )
                "
              />
            </a-form-item> -->
            <a-form-item
              label="税率（%）"
              name="taxRate"
              class="form-item-two"
              v-if="inputData.kind && ![1].includes(inputData.kind)"
            >
              <a-input
                v-model:value.trim="inputData.taxRate"
                placeholder="请输入"
                @blur="inputData.taxRate = pureNumber0(inputData.taxRate)"
              />
            </a-form-item>
            <a-form-item class="form-item-one">
              <div class="footer-btn-list">
                <a-button @click="cancel">取消</a-button>
                <a-button
                  type="primary"
                  html-type="submit"
                  :loading="spinning || loading"
                  >新建</a-button
                >
              </div>
            </a-form-item>
          </a-form>
        </a-spin>
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import { message } from 'ant-design-vue';
import api from '../../../../../api/projectDetail.js';
import {
  getCurrentInstance,
  ref,
  reactive,
  watch,
  onMounted,
  toRaw,
  toRefs,
} from 'vue';
import { pureNumber0 } from '@/utils/index';
import infoMode from '@/plugins/infoMode.js';
import { projectDetailStore } from '@/store/projectDetail.js';

const store = projectDetailStore();
const form = ref();
const loading = ref(false);
const spinning = ref(false);
const emit = defineEmits(['update:visible', 'rcjSaveData']);
const lists = reactive({
  rcjTypeList: [],
});

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  showResqty: {
    type: Boolean,
    default: true,
  },
  code: {
    type: String,
    default: '',
  },
  currentInfo: {
    type: Object,
    default: () => {},
  },
  ysscurrentInfo: {
    type: Object,
    default: () => {},
  },
  supplement: {
    type: Boolean,
    default: true,
  },
});
const ispbs = ref(true);
const projectStore = projectDetailStore();
const colStyle = reactive({
  colSize: null,
});

const labelCol = reactive({ style: { width: '150px' } });

const inputData = reactive({
  materialName: null, //项目名称
  materialCode: null, //项目编码
  specification: null, // 规格及型号
  unit: null, // 单位
  resQty: null, // 材料消耗量
  dePrice: null, // 定额价
  marketPrice: null, // 市场价
});

onMounted(() => {});

// input框输入值置为空
const reset = () => {
  loading.value = false;
  for (let key in inputData) {
    inputData[key] = null;
  }
};

const cancel = () => {
  form.value.resetFields();
  reset();
  emit('bcCancel', 3);
};

const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
  // saveOrUpdateFeature({ data: featureData.value }, 0);
};

watch(
  () => props.visible,
  (val, oldVal) => {
    console.log('不进来么', props.ysscurrentInfo);
    if (val) {
      form.value?.resetFields();
      ispbs.value = false;
      lists.rcjTypeList = [];
      reset();
      ispbs.value = props.currentInfo
        ? !Object.hasOwnProperty.call(props.currentInfo, 'pbs')
        : false;
      if (props.ysscurrentInfo) {
        if (
          props.ysscurrentInfo.kind == '06' &&
          (props.ysscurrentInfo.levelMark == 1 ||
            props.ysscurrentInfo.levelMark == 2)
        ) {
          ispbs.value = true;
        }
      }

      console.log(
        'ispbs',
        props.currentInfo,
        props.ysscurrentInfo,
        ispbs.value
      );
      inputData.taxRemoval = 11.28;
      inputData.materialCode = props.code;
      getTypeList();
    }
  }
);

const getTypeList = () => {
  console.log('这儿来不');
  api.getTypeList().then(res => {
    console.log('33333', res);
    if (res) {
      res.forEach(item => {
        lists.rcjTypeList.push({
          label: item.type,
          value: item.kind,
        });
      });
      if (ispbs.value) {
        console.log(
          'props.currentInfo.type',
          props.currentInfo.type,
          props.currentInfo.parentType
        );
        if (
          !['浆', '砼'].includes(props.currentInfo.parentType) &&
          !['主材费'].includes(props.currentInfo.type)
        ) {
          inputData.kind = lists.rcjTypeList.find(
            a => a.label === props.currentInfo.type
          ).value;
        } else {
          inputData.kind = lists.rcjTypeList.find(
            a => a.label === '材料费'
          ).value;
        }
        if (props.code) {
          inputData.materialCode = props.code;
        } else {
          let type =
            !['浆', '砼'].includes(props.currentInfo.parentType) &&
            !['主材费'].includes(props.currentInfo.type);
          getDefaultCode(
            type ? props.currentInfo.type.replace('费', '') : '材料'
          );
        }
      } else if (props.supplement) {
        inputData.kind = lists.rcjTypeList.find(
          a => a.label === '材料费'
        ).value;
        if (props.code) {
          inputData.materialCode = props.code;
        } else {
          getDefaultCode('材料');
        }
        // inputData.materialCode = '补充材料费001'
      }

      // 除了人工费
      if (![1].includes(inputData.kind)) {
        inputData.taxRate = 0;
      } else {
        inputData.taxRate = null;
      }
    }
  });
};
const getDefaultCode = materialCode => {
  console.log('getDefaultCode', {
    constructId: store.currentTreeGroupInfo?.constructId,
    prefix: '补充' + materialCode,
  });
  api
    .getDefaultCode({
      constructId: store.currentTreeGroupInfo?.constructId,
      prefix: '补充' + materialCode,
      unitId: store.currentTreeInfo?.id,
    })
    .then(res => {
      console.log('getDefaultCode', res);
      inputData.materialCode = '补充' + materialCode + res.result;
    });
};
const codeExistInUnit = () => {
  let apiData = {
    unitId: store.currentTreeInfo?.id,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    code: inputData.materialCode,
  };
  console.log('判断编码是否存在', apiData);
  api.codeExistInUnit(apiData).then(res => {
    if (res.status === 200) {
      if (res.result) {
        infoMode.show({
          isSureModal: true,
          iconType: 'icon-querenshanchu',
          infoText: '单位工程中已存在该材料编码，请重新输入',
          confirm: () => {
            infoMode.hide();
            loading.value = false;
          },
        });
      } else {
        let postData = {
          ...inputData,
          resQty: +inputData.resQty,
          marketPrice: +inputData.marketPrice,
        };
        // 除了人工费
        if (![1].includes(inputData.kind)) {
          postData.taxRate = +postData.taxRate;
        } else {
          postData.taxRate = null;
        }
        console.log('🚀 ~ api.codeExistInUnit ~ inputData:', postData);
        emit('rcjSaveData', postData);
      }
    }
  });
};

const deProiceInfo = () => {
  inputData.dePrice = pureNumber0(inputData.dePrice) || 0;
  if (inputData.dePrice == 0) {
    message.warning('请设置补充材料价格');
  }
};

const onSubmit = () => {
  const regex = /[[@:;#]/;

  if (inputData.dePrice == 0) {
    message.warning('请设置补充材料价格');
    return;
  }

  if (regex.test(inputData.materialCode)) {
    message.error('编码中含有非法字符，不能带有@或:或#等符号，请重新输入!');
    return;
  }
  loading.value = true;
  inputData.marketPrice = inputData.dePrice;
  inputData.marketTaxPrice = inputData.dePrice;
  isStandardRcj();
};

// 判断输入的材料编码是否标准人材机数据
const isStandardRcj = () => {
  let apiData = {
    unitId: store.currentTreeInfo?.id,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    code: inputData.materialCode,
  };
  api.isStandardRcj(apiData).then(res => {
    if (res.status === 200) {
      if (res.result) {
        infoMode.show({
          isSureModal: true,
          iconType: 'icon-querenshanchu',
          infoText: '材料编码不可与标准材料编码相同，请重新输入',
          confirm: () => {
            infoMode.hide();
            loading.value = false;
          },
        });
      } else {
        codeExistInUnit();
      }
    }
  });
};

const selectChange = value => {
  let label = lists.rcjTypeList.find(a => a.value === value).label;
  getDefaultCode(label.replace('费', ''));

  if (value != 1) {
    inputData.taxRate = 0;
  } else {
    inputData.taxRate = null;
  }
  if (value === 4) {
    inputData.taxRemoval = 11.36;
  } else {
    inputData.taxRemoval = 11.28;
  }
};
</script>
<style lang="scss" scoped>
@import './style.scss';
</style>
