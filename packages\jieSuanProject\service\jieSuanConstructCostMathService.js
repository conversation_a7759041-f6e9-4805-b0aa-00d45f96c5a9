
const ConstructCostMathService = require("../../../electron/service/constructCostMathService");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const ConstructionMeasureTypeConstant = require("../../../electron/enum/ConstructionMeasureTypeConstant");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {JieSuanRcjStageUtils} = require("../utils/JieSuanRcjStageUtils");
const {ArrayUtil} = require("../../../electron/utils/ArrayUtil");
const ConstantUtil = require("../../../electron/enum/ConstantUtil");
const StepItemCostLevelConstant = require("../../../electron/enum/StepItemCostLevelConstant");
const DePropertyTypeConstant = require("../../../electron/enum/DePropertyTypeConstant");
const GroundTypeConstant = require("../../../electron/enum/GroundTypeConstant");
const {BaseDe2022, BaseDe} = require("../../../electron/model/BaseDe");
const {In} = require("typeorm");
const LibraryCodeConstant = require("../../../electron/enum/LibraryCodeConstant");
const BranchProjectLevelConstant = require("../../../electron/enum/BranchProjectLevelConstant");
const InsertStrategy = require("../../../electron/main_editor/insert/insertStrategy");

class JieSuanConstructCostMathService extends ConstructCostMathService {

    constructor(ctx) {
        super(ctx);
    }


    async initRcj(unit, de) {
        let constructProjectRcjs = unit.constructProjectRcjs;
        if (!ObjectUtils.isEmpty(constructProjectRcjs)) {
            let constructProjectRcjs1 = constructProjectRcjs.filter(i => i.deId === de.sequenceNbr);
            JieSuanRcjStageUtils.rcjDataHandler(constructProjectRcjs1, false, unit);
        }
        let rcjDetailList = unit.rcjDetailList;
        if (!ObjectUtils.isEmpty(rcjDetailList)) {
            let rcjDetailList1 = rcjDetailList.filter(i => i.deId === de.sequenceNbr);
            if (!ObjectUtils.isEmpty(rcjDetailList1)) {
                JieSuanRcjStageUtils.rcjDataHandler(rcjDetailList1, false,unit);
            }
        }
    }


    /**
     *  垂运清单下挂上垂运定额
     * @param qdList 原始的清单数据，一定是从分部分项或者措施项目中取出来的数据
     * @param deList 定额数据
     */
    async cyQdAddDe(unit, qd, deList, constructionMeasureType) {
        if (ObjectUtils.isEmpty(qd)) {
            return;
        }
        if (ObjectUtils.isEmpty(deList)) {
            return;
        }
        let { constructId, spId, sequenceNbr } = unit;
        for (const deItem of deList) {
            let newDe;
            if (constructionMeasureType === ConstructionMeasureTypeConstant.FBFX) {
                // 检查要添加的定额所属清单是不是临时删除的  如果是临时删除的就需要恢复这个清单和清单下的所有数据
                this.service.itemBillProjectOptionService.cleanQdDelTempStatus({
                    constructId: constructId,
                    singleId: spId,
                    unitId: sequenceNbr,
                    id: qd.sequenceNbr,
                    modelType: 1,
                    tempDeleteFlag: false
                });
                let insertStrategy = new InsertStrategy({
                    constructId,
                    singleId: spId,
                    unitId: sequenceNbr,
                    pageType: 'fbfx'
                });
                newDe = await insertStrategy.execute({
                    pointLine: qd,
                    newLine: this.dataHandler(deItem),
                    indexId: deItem.sequenceNbr,
                    libraryCode: deItem.libraryCode,
                    option: 'insert',
                    skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
                    overwriteColumn: false
                });
            } else {
                // 检查要添加的定额所属清单是不是临时删除的  如果是临时删除的就需要恢复这个清单和清单下的所有数据
                this.service.itemBillProjectOptionService.cleanQdDelTempStatus({
                    constructId: constructId,
                    singleId: spId,
                    unitId: sequenceNbr,
                    id: qd.sequenceNbr,
                    modelType: 2,
                    tempDeleteFlag: false
                });
                let insertStrategy = new InsertStrategy({
                    constructId,
                    singleId: spId,
                    unitId: sequenceNbr,
                    pageType: 'csxm'
                });
                newDe = await insertStrategy.execute({
                    pointLine: qd,
                    newLine: this.dataHandler(deItem),
                    indexId: deItem.sequenceNbr,
                    libraryCode: deItem.libraryCode,
                    option: 'insert',
                    skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
                    overwriteColumn: false
                });
            }
            await this.initRcj(unit,newDe)
        }
    }


    async confirmQdAddCostDe(unit, qd, costDe, type, deMathBase) {
        if (ObjectUtils.isEmpty(qd)) {
            return;
        }

        let { constructId, spId, sequenceNbr } = unit;
        //下挂费用定额
        let insertStrategy = new InsertStrategy({
            constructId,
            singleId: spId,
            unitId: sequenceNbr,
            pageType: 'csxm'
        });
        let newCostDe = {};
        if (type === ConstructionMeasureTypeConstant.AWF) {
            newCostDe = await insertStrategy.execute({
                pointLine: qd,
                newLine: costDe,
                indexId: costDe.standardId,
                libraryCode: costDe.libraryCode,
                option: 'insert',
                skip: { rcj: true, quantity: true, jiqu: true, huizong: true },
                overwriteColumn: false
            });
        } else {
            // 检查要添加的定额所属清单是不是临时删除的  如果是临时删除的就需要恢复这个清单和清单下的所有数据
            this.service.itemBillProjectOptionService.cleanQdDelTempStatus({
                constructId: constructId,
                singleId: spId,
                unitId: sequenceNbr,
                id: qd.sequenceNbr,
                modelType: 2,
                tempDeleteFlag: false
            });
            newCostDe = await insertStrategy.execute({
                pointLine: qd,
                newLine: costDe,
                indexId: costDe.standardId,
                libraryCode: costDe.libraryCode,
                option: 'insert',
                skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
                overwriteColumn: false
            });
        }

        await this.initRcj(unit,newCostDe)

        return {
            'de': newCostDe,
            'titleData': PricingFileFindUtils.getUnit(constructId, spId, unit.sequenceNbr).measureProjectTables
        };
    }


    async awfCostMath(arg) {
        let {
            constructId,
            singleId,
            unitId,
            data,
            unitIdList,
            increaseFeeHeight,
            heatingFee,
            rainySeasonConstruction,
            csfyCalculateBaseCode,
            csfyCalculateBaseArea
        } = arg;
        console.time('总价措施记取 总耗时');
        let changeDeIds = new Set();
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        unit.zjcsCostMathCache = arg;
        //过滤掉不计取的
        data = data.filter(k => k.isCheck === 1);
        //将前端传递的数据处理分别返回安文费集合总价措施清单集合
        let { awf, zjcs } = this.dataFiltrate(data);
        if (ObjectUtils.isEmpty(awf) && ObjectUtils.isEmpty(zjcs)) {
            return;
        }
        // 判断是否22定额标准
        let is22De = PricingFileFindUtils.is22Unit(unit);

        console.time('总价措施费用定额记取耗时');
        //开始循环前端选择的单位集合
        for (const uId of unitIdList) {
            //1.根据单位ID获取到单位数据，并且删除单位中已经记取了的安文费定额和总价措施定额
            let unit = await this.getUnitAndClearCostDe(constructId, uId, changeDeIds);
            //获取计税方式 '1'?'一般计税':'简易计税'
            let taxCalculationMethod = unit.projectTaxCalculation.taxCalculationMethod;
            //2.开始计算记取流程,先记取总价措施费用
            if (ObjectUtils.isNotEmpty(zjcs)) {
                //2.1 获取总价措施需要的基数定额数据
                let otherZJCSUseDe = this.getOtherZJCSUseDe(unit);
                if (ObjectUtils.isNotEmpty(otherZJCSUseDe)) {
                    //2.2 处理基数定额并且根据基数定额施工组织措施类别对于基数定额进行分组（处理随主工程的情况）
                    //主取费文件
                    let mainFeeFile = PricingFileFindUtils.getMainFeeFileByUnitObj(unit);
                    let baseDeMap = await this.baseDeGroup(otherZJCSUseDe, unit, mainFeeFile);

                    //循环所选择记取总价措施清单集合
                    for (const zjcsQd of zjcs) {
                        //2.3  首先确认清单数据，如果该清单不存在则需要新增
                        let qd = this.confirmQdData(zjcsQd, unit, ConstructionMeasureTypeConstant.ZJCS);
                        if (ObjectUtils.isEmpty(qd)) {
                            continue;
                        }
                        //2.4 计算--基数定额计算基数
                        let costBase = this.costBase(baseDeMap, unit, arg);
                        //2.5 确定该清单所需要的费用定额是那些 ,排除不符合条件的定额
                        let costDeList = await this.getCostDeByQdzjcs(zjcsQd, baseDeMap, increaseFeeHeight, qd, unit, is22De, csfyCalculateBaseArea);
                        //2.6 清单下挂定额
                        for (const costDe of costDeList) {
                            //获取计算基数
                            let deMathBase = costBase[costDe.rateName];

                            // 判断是否计算基数乘以0.5
                            //雨季施工增加费   冬季施工增加费
                            if (heatingFee && costDe.zjcsClassCode === '1') {
                                deMathBase = NumberUtil.multiply(deMathBase, 0.5);
                            }
                            if (rainySeasonConstruction && costDe.zjcsClassCode === '2') {
                                deMathBase = NumberUtil.multiply(deMathBase, 0.5);
                            }
                            //赋值计算基数
                            costDe.formula = deMathBase;
                            costDe.caculatePrice = 1;
                            costDe.baseNum = { def: deMathBase };
                            //给清单下挂定额以及下挂人材机数据
                            let {
                                de,
                                titleData
                            } = await this.confirmQdAddCostDe(unit, qd, costDe, ConstructionMeasureTypeConstant.ZJCS, deMathBase);

                            if (arg.jieSuanFlag) {
                                let constructProjectRcjs = unit.constructProjectRcjs;
                                if (!ObjectUtils.isEmpty(constructProjectRcjs)) {
                                    let constructProjectRcjs1 = constructProjectRcjs.filter(i => i.deId === de.sequenceNbr);
                                    JieSuanRcjStageUtils.rcjDataHandler(constructProjectRcjs1, false,unit);
                                }

                                let rcjDetailList = unit.rcjDetailList;
                                if (!ObjectUtils.isEmpty(rcjDetailList)) {
                                    let rcjDetailList1 = rcjDetailList.filter(i => i.deId === de.sequenceNbr);
                                    if (!ObjectUtils.isEmpty(rcjDetailList1)) {
                                        JieSuanRcjStageUtils.rcjDataHandler(rcjDetailList1, false,unit);
                                    }
                                }
                            }

                            // 检测是否需要措施中人工费调整
                            await this.service.cszRgfAdjustProcess.checkDeRgfAdjustAndAddIfNecessary(unit.constructId, unit.spId, unit.sequenceNbr, de);
                            //计算单价构成
                            this.service.unitPriceService.caculataDEUnitPrice(unit.constructId, unit.spId, unit.sequenceNbr,
                                de.sequenceNbr, true, titleData, false);
                        }
                    }
                }
            }
            console.timeEnd('总价措施费用定额记取耗时');

            console.time('安文费 费用定额记取耗时');
            //3.计算安文费
            if (ObjectUtils.isNotEmpty(awf)) {
                //获取到安文费需要的基数定额数据
                let awfBaseDeList = this.getAwfUseDe(unit);
                //将基数定额根据取费文件分组
                let baseDeGroupByCostMajorName = ArrayUtil.group(awfBaseDeList, 'costMajorName');
                const szgcValue = baseDeGroupByCostMajorName.get(ConstantUtil.TITLE_WITH_MARJOR_PROJECT);
                if (ObjectUtils.isNotEmpty(szgcValue)) {
                    // 如果根据costMajorName分组后存在“随主工程”  就需要把随主工程的这一组转为单位工程的主取费文件
                    let mainFeeFile = PricingFileFindUtils.getMainFeeFileByUnitObj(unit);
                    baseDeGroupByCostMajorName.set(mainFeeFile.feeFileName, szgcValue);
                }
                //确定安文费的费用定额
                let awfCostList = await this.getAwfCostDe(unit);
                if (ObjectUtils.isEmpty(awfCostList)) {
                    return;
                }

                //循环前端安文费清单
                for (const awfQd of awf) {
                    //确定安文费清单
                    let qd = this.confirmQdData(awfQd, unit, ConstructionMeasureTypeConstant.AWF);
                    if (ObjectUtils.isEmpty(qd)) {
                        continue;
                    }
                    await this.hanldeAddAwfCostDe(awfCostList, baseDeGroupByCostMajorName, unit, qd);
                }
            }
        }
        console.timeEnd('安文费 费用定额记取耗时');

        await this.service.autoCostMathService.autoCostMath({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
            countCostCodeFlag: true,
            changeDeIdArr: changeDeIds
        });
        console.timeEnd('总价措施记取 总耗时');
    }



}



JieSuanConstructCostMathService.toString = () => '[class JieSuanConstructCostMathService]';
module.exports = JieSuanConstructCostMathService;