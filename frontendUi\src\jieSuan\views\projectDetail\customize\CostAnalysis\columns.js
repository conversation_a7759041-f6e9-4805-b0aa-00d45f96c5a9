import { ref,computed } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';

/**
  对比的结果及规律，前提以当前界面显示的字段为标准

  12 一般计税   合同内  	（通过）

  12 简易计税   合同内   	多这几个（进项税金，销项税额，增值税应纳税额，附加税费
  价差进项税额，价差销项税额，价差增值税应纳税额，价差附加税费）


  22 一般计税   合同内  	多这几个（进项税金，销项税额，增值税应纳税额，附加税费
  价差进项税额，价差销项税额，价差增值税应纳税额，价差附加税费）

  22 简易计税   合同内   	多这几个（进项税金，销项税额，增值税应纳税额，附加税费
  价差进项税额，价差销项税额，价差增值税应纳税额，价差附加税费）


  12 一般计税   合同外  	多这几个（合同金额(不含设备费及其税金) ）

  12 简易计税   合同外   	多这几个（合同金额(不含设备费及其税金)
  进项税金，销项税额，增值税应纳税额，附加税费
  价差进项税额，价差销项税额，价差增值税应纳税额，价差附加税费）


  22 一般计税   合同外  	多这几个（合同金额(不含设备费及其税金)
  进项税金，销项税额，增值税应纳税额，附加税费
  价差进项税额，价差销项税额，价差增值税应纳税额，价差附加税费

  22 简易计税   合同外   	多这几个（合同金额(不含设备费及其税金)
  进项税金，销项税额，增值税应纳税额，附加税费
  价差进项税额，价差销项税额，价差增值税应纳税额，价差附加税费）
*/
// 固定列配置
const fixedColumns = {
  index: { // 序号列
    title: '序号',
    field: 'dispNo',
    width: 90,
    align: 'left',
    classType: 1,
    sort: 1,
    treeNode: true,
    slot: false,
    visible: true,
    children: [],
    initialize: true,
    isDisabled: true,
    showOverflow: 'ellipsis',
  },
  projectName: { // 项目名称列
    title: '项目名称',
    field: 'projectName',
    slot: false,
    classType: 1,
    align: 'left',
    visible: true,
    width: 140,
    children: [],
    initialize: true,
  },
  settlementAmount: { // 结算金额列
    title: '结算金额(含人材机调整、不含设备费及其税金）',
    field: 'jieSuanPrice',
    slot: false,
    classType: 1,
    sort: 6,
    visible: true,
    width: 280,
    children: [],
    initialize: true,
  },
  contractAmount: { // 合同金额列 （contractPrice  gczj_ys  后端也目前不确认 2025/3/26）
    title: '合同金额(不含设备费及其税金)',
    field: 'contractPrice',
    slot: false,
    classType: 1,
    visible: true,
    width: 100,
    align: 'right',
    children: [],
    initialize: true,
  },
}


// 子项配置生成器
const generateChildren1 = (excludeTaxFields = false) => [
  {
    title: '结算金额（不含人材机调整）',
    field: 'jsje',
    treeNode: true,
    slot: false,
    classType: 1,
    sort: 4,
    visible: true,
    children: [
      // { title: '结算合计（不含设备费及其税金）', field: 'gczjsbsj', width: 200, sort: 1,  visible: false},
      { title: '结算合计（不含设备费及其税金）', field: 'gczj', width: 210, sort: 1,initialize: true, visible: true},
      { title: '分部分项', field: 'fbfxhj', width: 120,sort: 2, initialize: false, visible: false},
      { title: '措施项目', field: 'csxhj', width: 120,sort: 3, initialize: false,visible: false},
      { title: '其他项目', field: 'qtxmhj', width: 120,sort: 4, initialize: false,visible: false },
      { title: '规费', field: 'gfee', width: 120, sort: 5,initialize: false,visible: false},
      { title: '安全、生产文明施工费', field: 'safeFee', sort: 6, width: 180, initialize: false,visible: false},
      ...(excludeTaxFields ? [] : [
        { title: '进项税额', field: 'jxse', width: 120, sort: 7,initialize: false,visible: false},
        { title: '销项税额', field: 'xxse', width: 120, sort: 8,initialize: false,visible: false},
        { title: '增值税应纳税额', field: 'zzsynse',  sort: 9,width: 120, initialize: false,visible: false},
        { title: '附加税费', field: 'fjse', width: 120,  sort: 10,initialize: false,visible: false},
      ]),
      { title: '税金', field: 'sj', width: 120, sort: 11, initialize: false,visible: false},
      { title: '设备费及其税金', field: 'sbfsj', width: 120,sort: 12 ,initialize: true, visible: true}
    ].map(item => ({
      ...item,
      classType: 1,
      parentField: 'jsje',
      parentName:'结算金额（不含人材机调整）',
      slot: false,
      children: [],
    })),
    initialize: true,
  },
  {
    title: '人材机调整',
    field: 'rcjTz',
    treeNode: true,
    slot: false,
    classType: 1,
    sort: 5,
    visible: true,
    children: [
      { title: '合计（不含设备费及其税金）', field: 'jsjc', width: 180, sort: 1,initialize: true, visible: true },
      { title: '人工价差', field: 'jsjcrgf', width: 120, sort: 2,initialize: false, visible: false },
      { title: '材料价差', field: 'jsjcclf', width: 120, sort: 3, initialize: false,visible: false},
      { title: '暂估材料价差', field: 'jsjczgj', width: 120, sort: 4,initialize: false, visible: false},
      { title: '机械价差', field: 'jsjcjxf', width: 120, sort: 5, initialize: false,visible: false},
      { title: '价差规费', field: 'jcgfhj', width: 120, sort: 6,initialize: false, visible: false},
      { title: '价差安全、生产文明施工费', field: 'jcaqwmsgfhj',initialize: false, width: 180,sort: 7, visible: false},
      ...(excludeTaxFields ? [] : [
        { title: '价差进项税额', field: 'jcjxse', width: 120,sort: 8, initialize: false,visible: false},
        { title: '价差销项税额', field: 'jcg', width: 120,sort: 9, initialize: false,visible: false},
        { title: '价差增值税应纳税额', field: 'jch', width: 140, sort: 10, initialize: false,visible: false },
        { title: '价差附加税费', field: 'jci', width: 120, sort: 11,initialize: false, visible: false },
      ]),
      { title: '价差税金', field: 'jcs', width: 120, sort: 12, initialize: false,visible: false},
      { title: '设备费及其税金', field: 'jsjcsbf', width: 120,sort: 13,initialize: true, visible: true }
    ].map(item => ({
      ...item,
      classType: 1,
      parentField: 'rcjTz',
      parentName:'人材机调整',
      slot: false,
      children: [],
    })),
    initialize: true,
  }
];
const generateChildren2 = (excludeTaxFields = false) => [
  {
    title: '结算金额（不含人材机调整）',
    field: 'jsje',
    treeNode: true,
    slot: false,
    classType: 1,
    sort: 4,
    visible: true,
    children: [
      // { title: '结算合计（不含设备费及其税金）', field: 'gczjsbsj', width: 200, sort: 1,  visible: false},
      { title: '结算合计（不含设备费及其税金）', field: 'gczj', width: 210, sort: 1,initialize: true, visible: true},
      { title: '分部分项', field: 'fbfxhj', width: 120,sort: 2, initialize: false, visible: false},
      { title: '措施项目', field: 'csxhj', width: 120,sort: 3, initialize: false,visible: false},
      { title: '其他项目', field: 'qtxmhj', width: 120,sort: 4, initialize: false,visible: false },
      { title: '规费', field: 'gfee', width: 120, sort: 5,initialize: false,visible: false},
      { title: '安全、生产文明施工费', field: 'safeFee', sort: 6, width: 180, initialize: false,visible: false},
      ...(excludeTaxFields ? [] : [
        { title: '进项税额', field: 'jxse', width: 120, sort: 7,initialize: false,visible: false},
        { title: '销项税额', field: 'xxse', width: 120, sort: 8,initialize: false,visible: false},
        { title: '增值税应纳税额', field: 'zzsynse',  sort: 9,width: 120, initialize: false,visible: false},
        { title: '附加税费', field: 'fjse', width: 120,  sort: 10,initialize: false,visible: false},
      ]),
      { title: '税金', field: 'sj', width: 120, sort: 11, initialize: false,visible: false},
      { title: '设备费及其税金', field: 'sbfsj', width: 120,sort: 12 ,initialize: true, visible: true}
    ].map(item => ({
      ...item,
      classType: 1,
      visibleItem: item.visible,
      parentField: 'jsje',
      parentName:'结算金额（不含人材机调整）',
      slot: false,
      children: [],
    })),
    initialize: true,
  },
  {
    title: '人材机调整',
    field: 'rcjTz',
    treeNode: true,
    slot: false,
    classType: 1,
    sort: 5,
    visible: true,
    children: [
      { title: '合计（不含设备费及其税金）', field: 'jsjc', width: 180, sort: 1,initialize: true, visible: true },
      { title: '人工差价', field: 'jsjcrgf', width: 120, sort: 2,initialize: false, visible: false },
      { title: '材料差价', field: 'jsjcclf', width: 120, sort: 3, initialize: false,visible: false},
      { title: '机械价差', field: 'jsjcjxf', width: 120, sort: 5, initialize: false,visible: false},
      { title: '价差规费', field: 'jcgfhj', width: 120, sort: 6,initialize: false, visible: false},
      { title: '价差安全、生产文明施工费', field: 'jcaqwmsgfhj',initialize: false, width: 180,sort: 7, visible: false},
      ...(excludeTaxFields ? [] : [
        { title: '价差进项税额', field: 'jcjxse', width: 120,sort: 8, initialize: false,visible: false},
        { title: '价差销项税额', field: 'jcg', width: 120,sort: 9, initialize: false,visible: false},
        { title: '价差增值税应纳税额', field: 'jch', width: 140, sort: 10, initialize: false,visible: false },
        { title: '价差附加税费', field: 'jci', width: 120, sort: 11,initialize: false, visible: false },
      ]),
      { title: '价差税金', field: 'jcs', width: 120, sort: 12, initialize: false,visible: false},
      { title: '设备费及其税金', field: 'jsjcsbf', width: 120,sort: 13,initialize: true, visible: true }
    ].map(item => ({
      ...item,
      visibleItem: item.visible,
      classType: 1,
      parentField: 'rcjTz',
      parentName:'人材机调整',
      slot: false,
      children: [],
    })),
    initialize: true,
  }
]
/**
 * 12/22定额:{{store.constructConfigInfo.deStandardReleaseYear}}
 * 1. 一般计税/ 0. 简易计税 : {{store.constructConfigInfo.taxMode}}
 * 合同内 / undefined 合同外: {{store.currentTreeInfo.originalFlag}}
 */
// 配置策略
const configStrategy = ()=>{
  const p1 = [fixedColumns.index, fixedColumns.projectName, fixedColumns.contractAmount, ...generateChildren1(false), fixedColumns.settlementAmount]
  const p2 = [fixedColumns.index, fixedColumns.projectName, ...generateChildren2(false), fixedColumns.settlementAmount];
  const p3 = [fixedColumns.index, fixedColumns.projectName, fixedColumns.contractAmount, ...generateChildren1(true), fixedColumns.settlementAmount]
  const p4 = [fixedColumns.index, fixedColumns.projectName, ...generateChildren2(true), fixedColumns.settlementAmount]

  const p11 = [fixedColumns.index, fixedColumns.projectName, fixedColumns.contractAmount, ...generateChildren1(true), fixedColumns.settlementAmount]
  const p22 = [fixedColumns.index, fixedColumns.projectName, ...generateChildren2(true), fixedColumns.settlementAmount];
  const p33 = [fixedColumns.index, fixedColumns.projectName, fixedColumns.contractAmount, ...generateChildren1(true), fixedColumns.settlementAmount]
  const p44= [fixedColumns.index, fixedColumns.projectName, ...generateChildren2(true), fixedColumns.settlementAmount]
  return {
    '12': {
      '1': { // 一般计税
        original: p1,
        nonOriginal: p2
      },
      '0': { // 简易计税
        original: p3,
        nonOriginal:p4
      }
    },
    '22': {
      '1': { // 一般计税
        original: p11,
        nonOriginal: p22
      },
      '0': { // 简易计税
        original: p33,
        nonOriginal: p44
      }
    }
  }
};

//获取配置
const fbTableColumns = () => {
  const store = projectDetailStore();
  const year = String(store.constructConfigInfo.deStandardReleaseYear);
  const taxMode = String(store.constructConfigInfo.taxMode);
  const isOriginal = store.currentTreeInfo.originalFlag;

  const strategy = configStrategy()[year]?.[taxMode];
  if (!strategy) return [];
  return JSON.parse(JSON.stringify(isOriginal ? strategy.original : strategy.nonOriginal));
};
export const tableColumns = ref(fbTableColumns());

// export const fbTableColumns = ref([
//   {
//     title: '序号',
//     field: 'dispNo',
//     width: 100,
//     align: 'center',
//     classType: 1,
//     sort: 1,
//     visible: true,
//     children: [],
//     initialize: true,
//   },
//   {
//     title: '项目名称',
//     field: 'projectName',
//     treeNode: true,
//     slot: true,
//     classType: 1,
//     sort: 2,
//     visible: true,
//     width: 180,
//     children: [],
//     initialize: true,
//   },
//   {
//     title: '合同金额(不含设备费及其税金)',
//     field: 'gczj_ys',
//     treeNode: true,
//     slot: true,
//     classType: 1,
//     sort: 3,
//     visible: true,
//     width: 200,
//     children: [],
//     initialize: true,
//   },
//   {
//     title: '结算金额（不含人材机调整）',
//     field: 'jsje',
//     treeNode: true,
//     slot: true,
//     classType: 1,
//     sort: 4,
//     visible: true,
//     children: [
//       {
//         title: '结算合计（不含设备费及其税金）',
//         field: 'gczjsbsj',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 1,
//         visible: true,
//         width: 200,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '分部分项',
//         field: 'fbfxhj',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 2,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '措施项目',
//         field: 'csxhj',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 3,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '其他项目',
//         field: 'qtxmhj',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 4,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '规费',
//         field: 'gfee',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 5,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '安全、生产文明施工费',
//         field: 'safeFee',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 6,
//         visible: true,
//         width: 180,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '进项税额',
//         field: 'jxse',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 7,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '销项税额',
//         field: 'xxse',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 8,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '增值税应纳税额',
//         field: 'zzsynse',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 9,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '附加税费',
//         field: 'fjse',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 10,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '税金',
//         field: 'sj',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 11,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '设备费及其税金',
//         field: 'sbfsj',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 12,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//     ],
//     initialize: true,
//   },
//   {
//     title: '人材机调整',
//     field: 'rcjTz',
//     treeNode: true,
//     slot: true,
//     classType: 1,
//     sort: 5,
//     visible: true,
//     children: [
//       {
//         title: '合计（不含设备费及其税金）',
//         field: 'jsjc',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 1,
//         visible: true,
//         width: 180,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '人工差价',
//         field: 'jsjcrgf',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 2,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '材料差价',
//         field: 'jsjcclf',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 3,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '暂估材料差',
//         field: 'jsjczgj',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 4,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '机械价差',
//         field: 'jsjcjxf',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 5,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '价差规费',
//         field: 'jcgfhj',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 6,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '价差安全、生产文明施工费',
//         field: 'jcaqwmsgfhj',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 7,
//         visible: true,
//         width: 180,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '价差进项税额',
//         field: 'jcjxse',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 8,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '价差销项税额',
//         field: 'jcg',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 9,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '价差增值税应纳税额',
//         field: 'jch',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 10,
//         visible: true,
//         width: 140,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '价差附加税费',
//         field: 'jci',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 11,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '价差税金',
//         field: 'jcj',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 12,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//       {
//         title: '设备费及其税金',
//         field: 'jsjcsbf',
//         treeNode: true,
//         slot: true,
//         classType: 1,
//         sort: 13,
//         visible: true,
//         width: 120,
//         children: [],
//         initialize: true,
//       },
//     ],
//     initialize: true,
//   },
//   {
//     title: '结算金额(含人材机调整、不含设备费及其税金）',
//     field: 'jieSuanPrice',
//     treeNode: true,
//     slot: true,
//     classType: 1,
//     sort: 6,
//     visible: true,
//     width: 280,
//     children: [],
//     initialize: true,
//   },
// ]);
