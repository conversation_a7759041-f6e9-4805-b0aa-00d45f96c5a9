const ConstantUtil = require("../../../enum/ConstantUtil");
const EE = require('../../../../core/ee');
const _ = require("lodash");
const {NumberUtil} = require("../../../utils/NumberUtil");
const {CupmuteContext} = require("../../../unit_price_composition/compute/ComputingCore");
const {keysRcjMap, baseRcjFn} = require("../../rules/rcj");
const {keysSupplementRcjMap,baseSupplementRcjFn} = require("../../rules/supplemetRcj");
const {PricingFileFindUtils} = require("../../../utils/PricingFileFindUtils");
const InsertRcjStrategy = require("../../../rcj_handle/insert/insertRcjStrategy");
const {getDeUnitFormatEnum} = require("../../rules/format");
const {isSameUnit} = require("../../util");
const {BaseDeLibrary} = require("../../../model/BaseDeLibrary");
const {BaseDeLibrary2022} = require("../../../model/BaseDeLibrary2022");


/**
 *补充人材机逻辑
 */
class SupplementRcjHandler extends CupmuteContext{
    constructor(ctx,pageInfo) {
        super();
        this.ctx = ctx;
        this.allData = ctx.allData;
        this.pageInfo=pageInfo;
    }
    async prepare() {
        let {pointLine} = this.pageInfo;
        this.pointLine =this.allData.getNodeById(pointLine.sequenceNbr);
        this.pointLine.isSupplement = 1;
        this.unitFeeFileDTO=await this._getDefaultFeeFile();
        this.analyzeBaseFn(baseSupplementRcjFn);
    }
    getValue({type, kind, cloumn}) {
        let value = "";
        switch (type) {
            case "FEE_FILE": {
                //取费文件
                if (typeof cloumn == "function") {
                    value = cloumn(this.unitFeeFileDTO);
                } else {
                    value = this.unitFeeFileDTO[cloumn] || "";
                }
                break;
            }
            case "from": {
                //取费文件
                if (typeof cloumn == "function") {
                    value = cloumn(this.pageInfo);
                } else {
                    value = this.pageInfo[cloumn] || "";
                }
                break;
            }
            default:{
                if (typeof cloumn == "function") {
                    value = cloumn(this);
                }
            }
        }
        return value;
    }
    async replace() {
        await this.prepare()
        await this._fillLineData();
        await this._fillRcjData();
        return this.pointLine;
    }
    async _fillLineData() {
        this.pointLine.isSupplement = 1;
        this.pointLine.isStandard = 0;
        this.pointLine.standardId = null;
        this.pointLine.isCostDe = 0;
        this.pointLine.rcjFlag= 1;
        for (const key of keysSupplementRcjMap) {
            this.pointLine[key] = this.parseParams(key);
        }
        //处理工程量和工程量表达式
        await this.handlerQuantity();

    }
    async handlerQuantity() {
        let {service} = EE.app;
        let {constructId, singleId, unitId} = this.ctx;
        service.baseBranchProjectOptionService.dealQuantityExpression(constructId, singleId, unitId, this.pointLine.sequenceNbr);
        //补充人材机 赋值消耗量

        let unitNum = Number.parseInt(this.pageInfo.unit);

        if (Number.isNaN(unitNum)) {
            unitNum = 1;
        }

        let unitBlNum = getDeUnitFormatEnum(this.pageInfo.unit, constructId).value;
        if (unitNum == 1){
            this.pointLine.quantity = this.pageInfo.resQty;
            this.pointLine.quantityExpression = this.pageInfo.resQty;
            this.pointLine.quantityExpressionNbr = NumberUtil.numberScale(this.pageInfo.resQty,unitBlNum);
        }else {
            this.pointLine.quantityExpressionNbr = this.pageInfo.resQty;
            this.pointLine.quantityExpression = this.pageInfo.resQty;
            this.pointLine.quantity = NumberUtil.numberScale(NumberUtil.divide(
                this.pointLine.quantityExpressionNbr,
                unitNum
            ),unitBlNum);
        }

        this.pointLine.zjfPrice = this.pageInfo.marketPrice;
        this.pointLine.price = this.pageInfo.marketPrice;
        this.pointLine.specification = this.pageInfo.specification;
        if( this.pageInfo.zcFlag){
            this.pointLine.quantityExpression = this.pageInfo.quantityExpression;
            this.pointLine.quantity = this.pageInfo.quantity;
        }

        //补充人材机 传值 数据 处理 后续补充人材机删除
        //规格型号
        this.pointLine.bcRcjGGXH = this.pageInfo.specification;
        //除税系数
        this.pointLine.bcRcjCSXS = this.pageInfo.taxRemoval;
    }
    async _getDefaultFeeFile() {
        let {service,betterSqlite3DataSource} = EE.app;
        let {is2022: unitIs2022, constructId, singleId, unitId,unit} = this.ctx;
        let mainDeLib = unit.mainDeLibrary;
        let constructMajorType = unit.constructMajorType;
        // 根据 主定额库 和 工程专业 获取取费文件code
        let querySql = "select default_qf_code as feecode\n" +
          (PricingFileFindUtils.is22Unit(unit) ? 'from base_speciality_de_fee_relation_2022\n' : 'from base_speciality_de_fee_relation\n') +
            "where library_code = ?\n" +
            "  and unit_project_name = ?";
        let sqlRes = betterSqlite3DataSource.prepare(querySql).all(mainDeLib, constructMajorType);
        let feeCode = sqlRes[0].feecode;
        // 根据 feeCode 查询取费文件
        let feeFile = await service.baseFeeFileService.updateFBFXFeeFile(constructId, singleId, unitId, feeCode);

        return feeFile;
    }
    async _fillRcjData() {
        let {service} = EE.app;
        let {constructId, singleId, unitId} = this.ctx;
        let insertRcjStrategy = new InsertRcjStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId)});
        await insertRcjStrategy.execute({de:this.pointLine,args:this.pageInfo})
        //已经计算了
    }
}


/**
 * 人材机插入逻辑需要优化
 */
class RcjHandler{
    RCJKIN = ["", "人工费", "材料费", "机械费", "设备费", "主材费", "商砼", "砼", "浆", "商浆", "配比"];
    constructor(ctx, upDateInfo) {
        this.ctx = ctx;
        this.allData = ctx.allData;
        this.pointLine = null;
        this.upDateInfo = upDateInfo;
    }
    async prepare() {
        let {pointLine} = this.upDateInfo;
        this.pointLine = this.allData.getNodeById(pointLine.sequenceNbr);
    }
    async replace() {
       await this.prepare();
        let {rcjFlag,libraryCode} = this.upDateInfo;
        this.pointLine.rcjFlag= rcjFlag;
        this.pointLine.libraryCode=libraryCode;
        await this.doUpdateRcjLine();
        this.pointLine.type=this.pointLine.rcjKind;
        this.pointLine.appendType=[this.pointLine.rcjKind];
        return this.pointLine;
    }

    /**
     * TODO 垃圾代码需要优化
     * @returns {Promise<void>}
     */
    async doUpdateRcjLine() {
        let {service} = EE.app;
        let {constructId, singleId, unitId,unit} = this.ctx;
        let {indexId, is2022,rcjFlag} = this.upDateInfo;
        // 1.获取人材机 (增加22定额)
        let {rcj, pb} = await service.rcjProcess.addRcjLineOnOptionMenu(constructId, singleId, unitId, indexId, null, is2022);

        // 3.存入新的人材机
        await this._fillRcjLine(rcj,indexId);
        // 处理工程量表达式
        let qdLine = this.pointLine.parent;// this._findLineById(all, pointLine.newData.parentId);
        if (isSameUnit(qdLine.unit, rcj.unit)) {
            rcj.quantityExpression = qdLine.quantityExpression;
            //this.pointLine.quantityExpression = qdLine.quantityExpression;
            this.pointLine.quantityExpression = "QDL";
            service.baseBranchProjectOptionService.caculateQuantityExpressionAndQuantity(constructId, singleId, unitId, this.pointLine);
            rcj.quantityExpressionNbr = this.pointLine.quantityExpressionNbr;
            //rcj.quantityExpressionNbr = "QDL";
            rcj.quantity = this.pointLine.quantity;
        } else {
            rcj.quantityExpression = "0";
            this.pointLine.quantityExpression = "QDL*0";
            rcj.quantityExpressionNbr = 0;
            this.pointLine.quantityExpressionNbr = 0;
            rcj.quantity = 0;
            this.pointLine.quantity = 0;
        }
        let insertRcjStrategy = new InsertRcjStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId)});
        await insertRcjStrategy.execute({de:this.pointLine})

    }
    async _fillRcjLine(baseRcj,standardId) {
        let {constructId, singleId, unitId, unit, is2022} = this.ctx;
        let {appDataSource, service} = EE.app;
        this.pointLine.unit = baseRcj.unit;
        this.pointLine.unitCoefficient = baseRcj.unitCoefficient;
        this.pointLine.quantityExpression = this.pointLine.quantityExpression ? this.pointLine.quantityExpression : "0";
        this.pointLine.quantityExpressionNbr = Number.isNaN(Number.parseInt(baseRcj.unit)) || Number.parseInt(baseRcj.unit) === 0 ? 0 : 1 / Number.parseInt(baseRcj.unit);
        this.pointLine.quantity = 0;
        this.pointLine.kind = "04";
        this.pointLine.zjfPrice = baseRcj.priceMarket|| baseRcj.marketPrice;
        this.pointLine.zjfTotal = baseRcj.total;
        this.pointLine.standardId = standardId;
        this.pointLine.bdCode = baseRcj.materialCode;
        this.pointLine.specification = baseRcj.specification;
        this.pointLine.bdName = baseRcj.materialName;
        this.pointLine.fxCode = baseRcj.materialCode;
        this.pointLine.name = baseRcj.materialName;
        this.pointLine.isChangeAva = baseRcj.isChangeAva;
        let library = await appDataSource.manager.getRepository(is2022 ? BaseDeLibrary2022 : BaseDeLibrary).findOneBy({libraryCode: baseRcj.libraryCode});
        this.pointLine.remark = library.libraryName;
        this.pointLine.description = library.libraryName;
        this.pointLine.price = baseRcj.dePrice;
        this.pointLine.unitId = unitId;
        this.pointLine.constructId = constructId;
        this.pointLine.extend1 = baseRcj.libraryCode;
        this.pointLine.rcjKind = _.clone(this.RCJKIN[baseRcj.kind]);
        this.pointLine.levelMark = baseRcj.levelMark;
        //取费文件   当前单位的主取费文件
        let mainDeLib = unit.mainDeLibrary;
        let constructMajorType = unit.constructMajorType;
        let feeFile = await this._getDefaultFeeFile(constructId, singleId, unitId, mainDeLib, constructMajorType, is2022);
        this.pointLine.majorName = feeFile.feeFileName;
        this.pointLine.costFileCode = feeFile.feeFileCode;
        this.pointLine.costMajorName = feeFile.feeFileName;
        this.pointLine.qfCode = feeFile.feeFileCode;
        this.pointLine.qfName = feeFile.feeFileName;
        this.pointLine.measureType = unit.secondInstallationProjectName;
        this.pointLine.isSupplement = 0;
    }
    async _getDefaultFeeFile(constructId, singleId, unitWorkId, mainDeLib, constructMajorType, is2022) {
        let {betterSqlite3DataSource,service} = EE.app;
        let table_name;
        if (is2022) {
            table_name = "base_speciality_de_fee_relation_2022"
        } else {
            table_name = "base_speciality_de_fee_relation"
        }
        // 根据 主定额库 和 工程专业 获取取费文件code
        let querySql = "select default_qf_code as feecode\n" +
            "from " + table_name + "\n" +
            "where library_code = ?\n" +
            "  and unit_project_name = ?";
        let sqlRes = betterSqlite3DataSource.prepare(querySql).all(mainDeLib, constructMajorType);
        let feeCode = sqlRes[0].feecode;
        // 根据 feeCode 查询取费文件
        let feeFile = await service.baseFeeFileService.updateFBFXFeeFile(constructId, singleId, unitWorkId, feeCode);

        return feeFile;
    }
}

module.exports = {
    RcjHandler,SupplementRcjHandler
}
