<!--
 * @Descripttion: 导出xml流程弹窗 报表和电子标

-->
<template>
  <common-modal
    v-model:modelValue="isShowXML"
    className="dialog-comm"
    title="导出xml类型"
    width="auto"
  >
    <a-spin :spinning="spinning">
      <div class="radio-list">
        <div class="type-box">
          <div class="radio-title">
            <icon-font type="icon-zhaobiaoleixing" class="icon" />
            <span>招标类型</span>
          </div>
          <a-radio-group v-model:value="xmlType">
            <a-radio :style="radioStyle" :value="0">招标文件</a-radio>
            <a-radio :style="radioStyle" :value="1">投标文件</a-radio>
            <a-radio :style="radioStyle" :value="2">招标控制价文件</a-radio>
          </a-radio-group>
        </div>

        <div class="type-box">
          <div class="radio-title">
            <icon-font type="icon-changjia" class="icon" />
            <span>厂家</span>
          </div>
          <a-radio-group v-model:value="xmlCjType">
            <a-radio :style="radioStyle" :value="0">招标通</a-radio>
            <a-radio :style="radioStyle" :value="1">惠招标</a-radio>
          </a-radio-group>
        </div>
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancelDialog">取消</a-button>
        <a-button
          type="primary"
          :disabled="![0, 1, 2].includes(xmlType) || exportXmlLoading"
          @click="selfCheckDialog()"
          >确定</a-button
        >
      </div>
    </a-spin>
  </common-modal>
  <zbInfo
    v-model:infoVisible="infoVisible"
    :xmlType="xmlType"
    :checkGetMac="checkGetMac"
    @successHandle="saveExportXml"
  ></zbInfo>
  <ExportCheck v-model:visible="checkVisible" @next="checkNext"></ExportCheck>
</template>

<script setup>
import { ref, watch, reactive } from 'vue';
import zbInfo from '../reportInfo/zbInfo.vue';
import ExportCheck from './Check.vue';
import csProject from '@/api/csProject';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';

const projectStore = projectDetailStore();
let infoVisible = ref(false); // 导出xml信息弹框是否展示
let isShowXML = ref(false);
let xmlType = ref(-1);
let xmlCjType = ref(0);
let exportXmlLoading = ref(false);
const radioStyle = reactive({
  fontSize: '14px',
});
let spinning = ref(false);
let checkVisible = ref(false);
let checkGetMac = ref('');
const selfCheckDialog = async () => {
  spinning.value = true;
  const params = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    vender: xmlCjType.value,
  };
  const check = await csProject.validateManufacturerXML(params);
  console.log('🚀 ~ selfCheckDialog ~ check:', check, params);
  spinning.value = false;
  const { getMac, qdCode } = check.result;
  checkGetMac.value = getMac;
  if (qdCode) {
    infoVisible.value = true;
  } else {
    checkVisible.value = true;
  }
};
const checkNext = () => {
  checkVisible.value = false;
  infoVisible.value = true;
};
const cancelDialog = () => {
  xmlType.value = -1;
  infoVisible.value = false;
  isShowXML.value = false;
};
const saveExportXml = async () => {
  if (exportXmlLoading.value) return;
  exportXmlLoading.value = true;
  try {
    const postData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      type: xmlType.value,
      vender: xmlCjType.value,
    };
    const res = await csProject.generateXml(postData);
    exportXmlLoading.value = false;
    if (res?.result) {
      message.success('导出成功！');
      cancelDialog();
    }
  } catch (error) {
    console.error(error);
  } finally {
    exportXmlLoading.value = false;
  }
};

watch(
  () => projectStore.reportNextOption,
  value => {
    if (value) {
      isShowXML.value = true;
      projectStore.SET_REPORT_NEXT_OPTION(false);
    }
  },
);
</script>
<style lang="scss" scoped>
.radio-list {
  width: 600px;
  .type-box {
    background: #ffffff;
    border: 1px solid #d9d9d9;
    margin-bottom: 11px;
    border-radius: 2px;
    padding: 14px 13px;
  }
  .radio-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    span {
      font-weight: 400;
      font-size: 13px;
      color: #287cfa;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 4px;
    }
  }
}
</style>
