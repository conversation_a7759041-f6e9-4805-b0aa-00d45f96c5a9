'use strict';

const ConstantUtil = require("../../../electron/enum/ConstantUtil");
const {treeToArray} = require("../../../electron/main_editor/tree");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {ProjectFileUtils} = require("../../../common/ProjectFileUtils");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {NumberUtil} = require("../../../common/NumberUtil");
const {BaseList} = require("../../../electron/model/BaseList");
const RemoveStrategy = require('../../../electron/main_editor/remove/removeStrategy');
const BranchProjectLevelConstant = require('../../../electron/enum/BranchProjectLevelConstant');
const SecurityFeeProjectService = require("../../../electron/service/securityFeeProjectService");

/**
 *
 * @class
 */
class JieSuanSecurityFeeProjectService extends SecurityFeeProjectService {

    constructor(ctx) {
        super(ctx);
    }

    // baseListDao = this.app.appDataSource.manager.getRepository(BaseList);


    async queryAllProjectSecurity(constructId) {

        let unit = PricingFileFindUtils.getProjectObjById(constructId)
        if (ObjectUtils.isEmpty(unit)) {
            return ResponseData.fail("获取单位工程失败");
        }

        // 单项列表
        let subSingleProjects = unit.singleProjects;
        let arr = [];
        let dispNo = "";
        arr.push({sequenceNbr: unit.sequenceNbr, dispNo: dispNo, name: unit.constructName, total: 0, levelType: "1"})
        let obj = await this.queryAll(subSingleProjects, constructId, arr, dispNo, unit.deStandardReleaseYear, unit.sequenceNbr);
        // 当前单项的安文费为单项下单位总和+子项总和
        for (let arrElement of arr) {
            if (unit.sequenceNbr === arrElement.sequenceNbr) {
                if (ObjectUtils.isEmpty(unit.securityFee)) {
                    arrElement.total = NumberUtil.add(obj.subSingleNum, 0).toFixed(2);
                    arrElement.hstotal = NumberUtil.add(obj.hsNum, 0).toFixed(2);
                } else {
                    arrElement.total = "————";
                    arrElement.hstotal = NumberUtil.add(obj.hsNum, Number(unit.securityFee)).toFixed(2);
                }
            } else {
                arrElement.total = NumberUtil.add(arrElement.total, 0).toFixed(2);
                arrElement.hstotal = NumberUtil.add(arrElement.hstotal, 0).toFixed(2);
            }
        }

        return {datas: arr, unitProjects: unit.securityFee};

    }

    async updateAllProjectSecurity(constructId, securityFee, unitList) {
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        if (ObjectUtils.isEmpty(projectObjById)) {
            return ResponseData.fail("获取单位工程失败");
        }
        unitList = await this.queryAllProjectSecurity(constructId);
        for (let unit of unitList.datas) {
            if ("dw" === unit.type) {
                let qdByDjcs = PricingFileFindUtils.getUnit(constructId, unit.parentId, unit.sequenceNbr);
                // 设置固定安文费标识
                qdByDjcs.fixationSecurityFee = '1';
                let measureProjectTables = qdByDjcs.measureProjectTables;
                // 安文费总价措施参数缓存修改
                if (ObjectUtils.isNotEmpty(qdByDjcs.zjcsCostMathCache) && ObjectUtils.isNotEmpty(qdByDjcs.zjcsCostMathCache.data)) {
                    let data = qdByDjcs.zjcsCostMathCache.data;
                    if (!ObjectUtils.isEmpty(data)) {
                        for (let datum of data) {
                            if (datum.zjcsClassCode === "0") {
                                datum.isCheck = 0;
                                break;
                            }
                        }
                    }
                }

                // 安文费有删除  需要获取对应的清单做汇总
                let changeDeIds = new Set();
                // 获取到安文费清单列
                let find = measureProjectTables.find(a => a.zjcsClassCode == "0");
                let filter = measureProjectTables.filter(p => p.parentId === find.sequenceNbr);
                if (ObjectUtils.isNotEmpty(filter)) {
                    changeDeIds.add(find.sequenceNbr);
                    let removeStrategy = new RemoveStrategy({
                        constructId: constructId,
                        singleId: unit.parentId,
                        unitId: unit.sequenceNbr,
                        pageType: 'csxm'
                    });
                    for (let item of filter) {
                        // qdByDjcs.measureProjectTables.removeNode(item.sequenceNbr);
                        await removeStrategy.execute({
                            pointLine: item,
                            isBlock: false
                        });
                    }
                }

                // let measureProjectTablesArray = treeToArray(measureProjectTables);
                // for (let i = 0; i < measureProjectTablesArray.length; i++) {
                //     let measureProjectTable = measureProjectTablesArray[i];
                //     if (find.sequenceNbr === measureProjectTable.parentId) {
                //         measureProjectTablesArray.splice(i, 1);
                //         i--;
                //     }
                // }
                // qdByDjcs.measureProjectTables = arrayToTree(measureProjectTablesArray);

                //触发自动记取
                await this.service.autoCostMathService.autoCostMath({
                    constructId: constructId,
                    singleId: unit.parentId,
                    unitId: unit.sequenceNbr,
                    changeDeIdArr: changeDeIds
                });
                // 触发费用代码计算以及费用汇总计算
                await this.service.unitCostCodePriceService.countCostCodePrice({
                    constructId: constructId,
                    singleId: unit.parentId,
                    unitId: unit.sequenceNbr,
                });
            }
        }

        projectObjById.jieSuanSecurityFee = securityFee;
        return ResponseData.success("设置成功");

    }


    /**
     * 获取当前清单下所有的单项以及单位工程安文费
     * */
    async queryAll(subSingleProjects, constructId, arr, dispNo, taxCalculationMethod, parentId) {
        // 当前项安文费总和
        let subSingleNum = 0;
        let hsNum = 0;
        for (let i = 0; i < subSingleProjects.length; i++) {
            let subSingleProject = subSingleProjects[i];
            let subDispNo;
            if (ObjectUtils.isEmpty(dispNo)) {
                subDispNo = "" + (i + 1);
            } else {
                subDispNo = dispNo + "." + (i + 1);
            }
            arr.push({
                sequenceNbr: subSingleProject.sequenceNbr,
                dispNo: subDispNo,
                name: subSingleProject.projectName,
                total: 0,
                levelType: "2",
                parentId: parentId
            })
            // 单位列表
            let unitProjects = subSingleProject.unitProjects;
            let unitNum = 0;
            let hsunitNum = 0;
            if (!ObjectUtils.isEmpty(unitProjects)) {
                // let promise = await this.baseListDao.findOneBy({bdCodeLevel04: code});
                for (let j = 0; j < unitProjects.length; j++) {
                    let unitProject = unitProjects[j];
                    // 获取计税方式
                    let taxCalculationMethod = unitProject.projectTaxCalculation;
                    // 获取单位下安文费定额清单
                    let deByAwf = PricingFileFindUtils.getDeByAwf(constructId, subSingleProject.sequenceNbr, unitProject.sequenceNbr);
                    let num = 0;
                    deByAwf.forEach(a => num += a.total);
                    let hsnum = 0;
                    // 12定额的一般计税
                    if (!PricingFileFindUtils.is22Unit(unitProject) && taxCalculationMethod.taxCalculationMethod == '1') {
                        // 增值税费率
                        let zzNumber = Number(NumberUtil.multiply(Number(taxCalculationMethod.outputTaxRate), 0.01).toFixed(2));
                        // 附加税费
                        let fjNumber = Number(NumberUtil.multiply(Number(taxCalculationMethod.additionalTaxRate), 0.01).toFixed(2));
                        // (不含税安文费-不含税安文费*除税系数3%)*增值税费率9%-不含税安文费*除税系数3%+
                        // 【（不含税安文费-不含税安文费*除税系数3%）*增值税费率-不含税安文费*除税系数3%】*附加税费
                        let number1 = NumberUtil.subtract(NumberUtil.multiply(
                                NumberUtil.subtract(num, Number(NumberUtil.multiply(num, 0.03).toFixed(2)))
                                , zzNumber).toFixed(2),
                            Number(NumberUtil.multiply(num, 0.03).toFixed(2)));
                        let number2 = NumberUtil.subtract(NumberUtil.multiply(
                                NumberUtil.subtract(num, Number(NumberUtil.multiply(num, 0.03).toFixed(2)))
                                , zzNumber).toFixed(2),
                            Number(NumberUtil.multiply(num, 0.03).toFixed(2))
                        );
                        let number3 = NumberUtil.add(number1, Number(NumberUtil.multiply(number2, fjNumber).toFixed(2)));
                        hsnum = NumberUtil.add(num, number3);
                    } else {
                        // 税率
                        let zzNumber = NumberUtil.multiply(Number(taxCalculationMethod.taxRate), 0.01).toFixed(2);
                        // 安文费金额+安文费税金（不含税安文费*税率）
                        hsnum = NumberUtil.add(num, Number(NumberUtil.multiply(num, Number(zzNumber)).toFixed(2)));
                    }


                    arr.push({
                        sequenceNbr: unitProject.sequenceNbr,
                        dispNo: subDispNo + "." + (j + 1),
                        name: unitProject.upName,
                        total: num,
                        hstotal: hsnum,
                        type: "dw",
                        parentId: subSingleProject.sequenceNbr,
                        levelType: "3"
                    })

                    unitNum += num;
                    hsunitNum = NumberUtil.add(hsunitNum,hsnum);
                }
            }
            // 单项列表
            let subSinglearr = subSingleProject.subSingleProjects;
            let number = 0;
            let hsnumber = 0;
            if (!ObjectUtils.isEmpty(subSinglearr)) {
                let obj = await this.queryAll(subSinglearr, constructId, arr, subDispNo, taxCalculationMethod, subSingleProject.sequenceNbr);
                number = obj.subSingleNum;
                hsnumber = obj.hsNum;
            }

            // 当前单项的安文费为单项下单位总和+子项总和
            for (let arrElement of arr) {
                if (subSingleProject.sequenceNbr === arrElement.sequenceNbr) {
                    arrElement.total = unitNum + number;
                    arrElement.hstotal = hsunitNum + hsnumber;
                    break;
                }
            }

            subSingleNum = subSingleNum + unitNum + number;
            hsNum = hsNum + hsunitNum + hsnumber;
        }
        return {subSingleNum, hsNum};
    }
}

JieSuanSecurityFeeProjectService.toString = () => '[class JieSuanSecurityFeeProjectService]';
module.exports = JieSuanSecurityFeeProjectService;
