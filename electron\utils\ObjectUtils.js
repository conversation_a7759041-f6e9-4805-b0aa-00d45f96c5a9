const _ = require('lodash');

class ObjectUtils{


    constructor() {
        function isType(type) {
            return function(obj) {
                return Object.prototype.toString.call(obj) === "[object " + type + "]";
            };
        }
        this.isObject = isType("Object");
        this.isString = isType("String")
        this.isArray = Array.isArray || isType("Array")
        this.isFunction = isType("Function")
        this.isUndefined = isType("Undefined")
        this.isNull = isType("Null")
        this.isNumber = isType("Number")
    }

    isEmpty(obj){
        return this.isNull(obj)
            || this.isUndefined(obj)
            || (this.isArray(obj) && obj.length === 0)
            || (this.isString(obj) && obj === '')
    }

    isNotEmpty(obj){
        return !this.isEmpty(obj);
    }

    is_Undefined(obj){
        return this.isUndefined(obj)

    }

    toJsonString(obj){
        return JSON.stringify(obj,(key, value) => typeof value === 'undefined' ? null : value);
    }


    updatePropertyValue(obj, propertyKey, newValue) {
        for (let key in obj) {
            if (key == "parent" || key == "prev" || key == "next") {
                continue;
            }
            if (typeof obj[key] === 'object') {
                if (Array.isArray(obj[key])) {
                    // 如果属性的值是数组，则循环遍历数组并递归调用更新函数
                    obj[key].forEach((item) => {
                        this.updatePropertyValue(item, propertyKey, newValue);
                    });
                } else {
                    // 如果属性的值是对象，则递归调用更新函数
                    this.updatePropertyValue(obj[key], propertyKey, newValue);
                }
            } else if (key === propertyKey && !this.isEmpty(obj[key])) {
                // 如果属性的键等于目标属性键，并且属性具有值，则更新属性的值
                obj[key] = newValue;
            }
        }
    }

     compareJSON(json1, json2) {
        var obj1 = JSON.parse(json1);
        var obj2 = JSON.parse(json2);

        return this.deepEqual(obj1, obj2);
    }

     deepEqual(obj1, obj2) {

         obj1 = obj1 === undefined ? null :obj1;
         obj2 = obj2 === undefined ? null :obj2;

        if (obj1 === obj2) {
            return true;
        }
        if (typeof obj1 !== 'object' || typeof obj2 !== 'object' || obj1 === null || obj2 === null) {
            return false;
        }

        var keys1 = Object.keys(obj1);
        var keys2 = Object.keys(obj2);

        if (keys1.length !== keys2.length) {
            return false;
        }

        for (var key of keys1) {
            if (!keys2.includes(key) || !this.deepEqual(obj1[key], obj2[key])) {
                return false;
            }
        }

        return true;
    }


  isNumberStr(numStr) {
    return !isNaN(parseFloat(numStr));
  }


  cloneDeep(obj){
    return _.cloneDeep(obj);
  }

    /**
     * 递归对比a、b两个对象的属性值，如果不相同则记录下来，差异部分结构同a对象，是a对象的一个子集
     * @param a
     * @param b
     * @return {*[]|undefined|*}
     */
    diff(a, b) {
        // 基本类型直接比较
        if (a === b) return undefined;
        if (a === null || b === null) return a;
        if (typeof a !== 'object' || typeof b !== 'object') return a;

        // 处理数组
        if (Array.isArray(a)) {
            if (!Array.isArray(b)) return a; // b不是数组，返回整个a
            const result = [];
            let hasDiff = false;
            for (let i = 0; i < a.length; i++) {
                const itemA = a[i];
                const itemB = i < b.length ? b[i] : undefined;
                const difference = this.diff(itemA, itemB);
                if (difference !== undefined) {
                    result[i] = difference;
                    hasDiff = true;
                }
            }
            return hasDiff ? result : undefined;
        }

        // 处理普通对象
        const result = {};
        let hasDiff = false;
        const keys = Object.keys(a);

        for (const key of keys) {
            // b中不存在该属性
            if (!(key in b)) {
                result[key] = a[key];
                hasDiff = true;
                continue;
            }

            // 递归比较属性
            const valA = a[key];
            const valB = b[key];
            const difference = this.diff(valA, valB);

            if (difference !== undefined) {
                result[key] = difference;
                hasDiff = true;
            }
        }

        return hasDiff ? result : undefined;
    }

    /**
     * 递归对比a、b两个对象的属性，如果a中属性在b中不存在则记录下来，记录结果的结构与a相同，是a对象的一个子集
     * @param a
     * @param b
     * @return {*[]|undefined|*}
     */
    diffOnlyInA(a, b) {
        // 如果 a 是 null 或非对象，直接返回 undefined（不记录）
        if (a === null || typeof a !== 'object') {
            return undefined;
        }

        // 如果 b 不是对象，则返回整个 a（因为 b 中没有对应结构）
        if (b === null || typeof b !== 'object') {
            return a;
        }

        // 根据 a 的类型创建结果容器（数组或普通对象）
        const result = Array.isArray(a) ? [] : {};

        // 遍历 a 的所有自身属性
        for (const key in a) {
            if (Object.hasOwnProperty.call(a, key)) {
                // 如果是数组且属性是 'length'，跳过不比较
                if (Array.isArray(a) && key === 'length') {
                    continue;
                }

                const aVal = a[key];
                const bVal = b[key];

                // 情况1: b 中不存在该属性 -> 记录 a 中的值
                if (!Object.hasOwnProperty.call(b, key)) {
                    result[key] = aVal;
                }
                // 情况2: a 的值是对象 -> 递归比较子属性
                else if (typeof aVal === 'object' && aVal !== null) {
                    const subDiff = this.diffOnlyInA(aVal, bVal);
                    // 子比较结果非空则记录
                    if (subDiff !== undefined) {
                        result[key] = subDiff;
                    }
                }
                // 情况3: a 的值是基本类型 -> b 中存在则不记录
            }
        }

        // 检查结果是否为空
        const isEmpty = Array.isArray(result)
            ? result.length === 0
            : Object.keys(result).length === 0;

        return isEmpty ? undefined : result;
    }

}

module.exports = {
    ObjectUtils: new ObjectUtils()
}
