'use strict';

const { Service, Log } = require('../../../core');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const { PricingFileFindUtils } = require('../../utils/PricingFileFindUtils');
const waterElectricTemplate = require('../../jsonData/waterElectricTemplate.json');
const waterElectricTemplate2022 = require('../../jsonData/waterElectricTemplate2022.json');
const BranchProjectLevelConstant = require('../../enum/BranchProjectLevelConstant');
const majorLibraryCorrelation = require('../../jsonData/majorLibraryCorrelation.json');
const { NumberUtil } = require('../../utils/NumberUtil');

/**
 * 水电费记取
 */
class WaterElectricCostMatchService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  async getWaterElectricCostData(args) {
    const { unitId, singleId, constructId } = args;
    const unitObj = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    return unitObj.waterElectricCostData || [];
  }

  /**
   * 自动计算水电费接口
   * waterElectricCostData 表示为水电费总体数据
   *
   * 该方法会返回一个计算后的水电费数据  是否更新单位的水电费数据  需要在外部进行处理
   */
  async calculateWaterElectricCost(args) {
    let { unitId, singleId, constructId, waterElectricCostData } = args;
    const unitObj = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const is22Unit = PricingFileFindUtils.is22Unit(unitObj);
    let taxCalculationMethod = unitObj.projectTaxCalculation.taxCalculationMethod;
    // 查看是不是有水电费的初始化数据  没有的话需要先初始化单位的水电费数据
    this.initWaterElectricCostData(unitObj, is22Unit);
    // 默认使用单位缓存的水电费数据
    if (ObjectUtil.isEmpty(waterElectricCostData)) {
      // 如果waterElectricCostData参数传了   那么使用参数传递的水电费数据  主要是用于页面上的临时修改计算
      waterElectricCostData = unitObj.waterElectricCostData;
    }
    // 获取所有的水电费基数定额
    const deArr = await this.getWaterElectricBaseDeData(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(deArr)) {
      // 没有基数定额 所有的费用都要清零
      this.clearWaterElectricCost(waterElectricCostData);
      return waterElectricCostData;
    }
    // 把水电费基数定额根据libraryCode进行分组
    const deMap = deArr.reduce((pre, cur) => {
      if (!pre[cur.libraryCode]) {
        pre[cur.libraryCode] = [cur];
      } else {
        pre[cur.libraryCode].push(cur);
      }
      return pre;
    }, {});
    // 12定额的工程专业和定额册对应关系对象
    let majorLibrary = majorLibraryCorrelation.majorLibrary2012;
    if (PricingFileFindUtils.is22Unit(unitObj)) {
      // 如果是22的就取22专业定额册
      majorLibrary = majorLibraryCorrelation.majorLibrary2022;
    }
    // 遍历水电费的列表，计算每一条数据行的相应值
    for (const costItem of waterElectricCostData.waterElectricData) {
      if (costItem.dataFlag !== 1) {
        // 不需要计算的行
        continue;
      }
      // 筛选出本条水电费列表数据行(costItem，也就是工程专业或者章节)对应的定额
      let deArr = this.filterDeArr(majorLibrary, costItem, deMap, is22Unit);
      // // 到此处之后   本条水电费列表数据行(costItem)对应的基数定额就完全确定了
      // const deIds = deArr.map(de => de.sequenceNbr);
      // // 根据定额获取到定额对应的人材机数据
      // const rcjList = PricingFileFindUtils.batchGetDeRcjList(constructId, singleId, unitId, deIds, unitObj);
      // 根据计算公式和人材机数据计算得到对应的值(基数)
      const baseValue = await this.getBaseValue(costItem.calculateBase, deArr, taxCalculationMethod, is22Unit);
      this.calculateByBaseValue(costItem, baseValue, waterElectricCostData.waterElectricData);
    }
    // 计算最终的水电费
    this.summaryValue(waterElectricCostData);
    return waterElectricCostData;
  }

  summaryValue(data) {
    let totalWaterCost = 0;
    let totalElectricCost = 0;
    let waterElectricCost = 0;
    // 遍历每一行   计算水电费合计
    for (const costItem of data.waterElectricData) {
      if (costItem.dataFlag !== 1) {
        // 不需要计算的行
        continue;
      }
      if (ObjectUtil.isNotEmpty(costItem.selectOptionFlag) && costItem.selectOptionFlag === 0) {
        // 未选中的行不进行计算 selectOptionFlag不为空表示是有选项操作的 值为0才表示未选中
        continue;
      }
      if (ObjectUtil.isNotEmpty(costItem.waterCost)) {
        totalWaterCost = NumberUtil.add(totalWaterCost, Number(costItem.waterCost));
      }
      if (ObjectUtil.isNotEmpty(costItem.electricCost)) {
        totalElectricCost = NumberUtil.add(totalElectricCost, Number(costItem.electricCost));
      }
      if (ObjectUtil.isNotEmpty(costItem.totalCost)) {
        waterElectricCost = NumberUtil.add(waterElectricCost, Number(costItem.totalCost));
      }
    }
    data.totalWaterCost = totalWaterCost;
    data.totalElectricCost = totalElectricCost;
    data.waterElectricCost = waterElectricCost;
  }

  filterDeArr(majorLibrary, costItem, deMap, is22Unit) {
    let deArr = [];
    // 根据模板中配置项的工程专业，获取对应的定额册集合
    const majorLibraryArr = majorLibrary[costItem.projectMajor];
    // 循环定额册  找出定额册对应的定额
    for (const majorLibrary of majorLibraryArr) {
      const deArrByLibraryCode = deMap[majorLibrary];
      if (ObjectUtil.isNotEmpty(deArrByLibraryCode)) {
        deArr = deArr.concat(deArrByLibraryCode);
      }
    }
    // 到此处之后  就确定了这个工程专业下的所有定额
    // 但是市政工程和安装工程还需要根据章节(classify_level1)再次分组
    if (costItem.projectMajor === '安装工程') {
      // 安装的章节(rule)和定额的classifyLevel1完全一致  所以用===
      // 22定额的要用classifyLevel2
      if (is22Unit) {
        if (costItem.dataLevel === 2) {
          // 如果是子级专业  需要使用classifyLevel3
          if (costItem.rule == '其它总价措施费') {
            deArr = deArr.filter(de => ObjectUtil.isNotEmpty(de.classifyLevel3) && de.classifyLevel3 == '3.6 其它');
          } else {
            deArr = deArr.filter(de => ObjectUtil.isNotEmpty(de.classifyLevel3) && de.classifyLevel3.includes(costItem.rule));
          }
        } else {
          deArr = deArr.filter(de => ObjectUtil.isNotEmpty(de.classifyLevel2) && de.classifyLevel2 === costItem.rule);
        }
      } else {
        deArr = deArr.filter(de => ObjectUtil.isNotEmpty(de.classifyLevel1) && de.classifyLevel1 === costItem.rule);
      }
    } else if (costItem.projectMajor === '市政工程') {
      // 市政的章节(rule)是简写 所以用includes
      // 22定额的要用classifyLevel2
      if (is22Unit) {
        if (costItem.dataLevel === 2) {
          // 如果是子级专业  需要使用classifyLevel3
          deArr = deArr.filter(de => ObjectUtil.isNotEmpty(de.classifyLevel3) && de.classifyLevel3.includes(costItem.rule));
        } else {
          deArr = deArr.filter(de => ObjectUtil.isNotEmpty(de.classifyLevel2) && de.classifyLevel2.includes(costItem.rule));
        }
      } else {
        deArr = deArr.filter(de => ObjectUtil.isNotEmpty(de.classifyLevel1) && de.classifyLevel1.includes(costItem.rule));
      }
    }
    return deArr;
  }

  calculateByBaseValue(costItem, baseValue, allData) {
    let waterCost = null;
    let electricCost = null;
    let totalCost = null;
    // 根据基数计算这一行的水费
    if (ObjectUtil.isNumberStr(costItem.waterRate)) {
      waterCost = NumberUtil.costPriceAmountFormat(NumberUtil.multiplyParams(Number(baseValue), Number(costItem.waterRate), 0.01));
    }
    // 根据基数计算这一行的电费
    if (ObjectUtil.isNumberStr(costItem.electricRate)) {
      electricCost = NumberUtil.costPriceAmountFormat(NumberUtil.multiplyParams(Number(baseValue), Number(costItem.electricRate), 0.01));
    }
    // 根据基数计算这一行的合计
    if (ObjectUtil.isNumberStr(costItem.totalRate)) {
      totalCost = NumberUtil.costPriceAmountFormat(NumberUtil.multiplyParams(Number(baseValue), Number(costItem.totalRate), 0.01));
    }
    costItem.waterCost = waterCost;
    costItem.electricCost = electricCost;
    costItem.totalCost = totalCost;
    // 当前这一行计算完成之后，需要确认这一行是不是需要汇总到父级 如果需要汇总到父级，则需要计算父级的基数
    if (costItem.dataLevel === 2) {
      const equativeLevel = allData.filter(item => item.dataLevel === 2 && item.parentId === costItem.parentId);
      if (ObjectUtil.isNotEmpty(equativeLevel)) {
        const parent = allData.find(item => item.id === costItem.parentId);
        let waterCostValue = null;
        let electricCostValue = null;
        let totalCostValue = null;
        for (const item of equativeLevel) {
          if (ObjectUtil.isNotEmpty(item.waterCost)) {
            waterCostValue = NumberUtil.add(waterCostValue, Number(item.waterCost));
          }
          if (ObjectUtil.isNotEmpty(item.electricCost)) {
            electricCostValue = NumberUtil.add(electricCostValue, Number(item.electricCost));
          }
          if (ObjectUtil.isNotEmpty(item.totalCost)) {
            totalCostValue = NumberUtil.add(totalCostValue, Number(item.totalCost));
          }
        }
        parent.waterCost = waterCostValue;
        parent.electricCost = electricCostValue;
        parent.totalCost = totalCostValue;
      }
    }
  }

  /**
   * 初始化水电费数据
   */
  initWaterElectricCostData(unitObj, is22Unit) {
    if (ObjectUtil.isEmpty(unitObj.waterElectricCostData)) {
      const waterElectricArray = [];
      for (const item of (is22Unit ? waterElectricTemplate2022 : waterElectricTemplate)) {
        waterElectricArray.push(ObjectUtil.cloneDeep(item));
      }
      // waterElectricData： 水电费列表数据缓存
      // customWaterElectric： 独立计取水电费设置的值 customWaterElectricFlag 是否独立计取水电费 false 不独立计取 true 独立计取
      // totalWaterCost 水费  totalElectricCost 电费  waterElectricCost  水电费
      unitObj.waterElectricCostData = {
        waterElectricData: waterElectricArray,
        customWaterElectric: null,
        customWaterElectricFlag: false,
        totalWaterCost: 0,
        totalElectricCost: 0,
        waterElectricCost: 0
      };
    }
  }

  /**
   * 清零水电费数据
   */
  clearWaterElectricCost(data) {
    for (const item of data.waterElectricData) {
      this.calculateByBaseValue(item, 0, data.waterElectricData);
    }
    data.totalWaterCost = 0;
    data.totalElectricCost = 0;
    data.waterElectricCost = 0;
  }

  /**
   * 获取水电费的基数定额
   */
  async getWaterElectricBaseDeData(constructId, singleId, unitId) {
    let deArr = [];
    let fbFxArr = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
    if (ObjectUtil.isNotEmpty(fbFxArr)) {
      // 分部分项的所有定额
      fbFxArr = fbFxArr.filter(fbfxItem => fbfxItem.kind === BranchProjectLevelConstant.de);
      deArr = deArr.concat(fbFxArr);
    }
    let csxmArr = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
    if (ObjectUtil.isNotEmpty(csxmArr)) {
      // 措施项目中不包含安文费标题下的所有定额
      csxmArr = csxmArr.filter(csxmItem => csxmItem.kind === BranchProjectLevelConstant.de);
      if (ObjectUtil.isNotEmpty(csxmArr)) {
        const awfDeArr = PricingFileFindUtils.getDeByAwf(constructId, singleId, unitId);
        if (ObjectUtil.isNotEmpty(awfDeArr)) {
          const awfDeIds = awfDeArr.map(awfDe => awfDe.sequenceNbr);
          csxmArr = csxmArr.filter(csxmItem => !awfDeIds.includes(csxmItem.sequenceNbr));
        }
      }
      deArr = deArr.concat(csxmArr);
    }
    const is22Unit = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
    if (is22Unit) {
      const libraryCode = ['2022-JZGC-DEY', '2022-ZSZX-DEY', '2022-AZGC-DEK', '2022-SZGC-DEK', '2023-YLLH-DEK'];
      if (ObjectUtil.isNotEmpty(deArr)) {
        deArr = deArr.filter(de => libraryCode.includes(de.libraryCode));
      }
    } else {
      // 2012年《全国统一安装工程预算定额河北省消耗量定额》
      // 2012年《全国统一建筑装饰装修工程消耗量定额河北省消耗量定额》
      // 2012年《全国统一建筑工程基础定额河北省消耗量定额》
      // 河北省装配式钢结构工程定额(2018试行)
      // 河北省装配式混凝土结构工程定额（2016试行）
      // 2012年《全国统一市政工程预算定额河北省消耗量定额》
      // 2013年《河北省园林绿化工程消耗量定额》
      // 2013年《河北省仿古建筑工程消耗量定额》
      const libraryCode = ['2012-AZGC-DEK', '2012-ZSZX-DEY', '2012-JZGC-DEY', '2018-ZPSGJGGC', '2016-ZPSHNT', '2012-SZGC-DEK', '2013-YLLH-DEK', '2013-FGJZ-DEG'];
      if (ObjectUtil.isNotEmpty(deArr)) {
        deArr = deArr.filter(de => libraryCode.includes(de.libraryCode));
      }
    }
    return deArr;
  }

  /**
   * 根据计算公式和人材机数据计算结果
   */
  async getBaseValue(formula, deArr, taxCalculationMethod, is22Unit) {
    let value = 0;
    if (ObjectUtil.isEmpty(deArr)) {
      return value;
    }
    // formula可能是：人工费定额价+材料费定额价+机械费定额价, 人工费市场价+材料费市场价+机械费市场价, 人工费基期价+材料费基期价+机械费基期价
    const splitArr = formula.split('+');
    for (const de of deArr) {
      for (const str of splitArr) {
        if (str == '人工费基期价' || str == '人工费定额价') {
          value = NumberUtil.add(value, de.totalRfeeDe);
        } else if (str == '材料费基期价' || str == '材料费定额价') {
          value = NumberUtil.add(value, de.totalCfeeDe);
        } else if (str == '机械费基期价' || str == '机械费定额价') {
          value = NumberUtil.add(value, de.totalJfeeDe);
        } else if (str == '人工费市场价') {
          value = NumberUtil.add(value, de.totalRfee);
        } else if (str == '材料费市场价') {
          value = NumberUtil.add(value, de.totalCfee);
        } else if (str == '机械费市场价') {
          value = NumberUtil.add(value, de.totalJfee);
        }
      }
    }

    // const kindMap = {
    //   '人工费': [1],
    //   '机械费': [3],
    //   '材料费': [2, 6, 7, 8, 9, 10]
    // };
    // const fieldMap = {
    //   '定额价': 'dePrice',
    //   '市场价': 'marketPrice',
    //   '基期价': 'priceBaseJournal'   // 基期价需要特殊处理  根据计税方式取对应的含税或者不含税值
    // };
    // // 人工费定额价+材料费定额价+机械费定额价, 人工费市场价+材料费市场价+机械费市场价, 人工费基期价+材料费基期价+机械费基期价
    // const splitArr = formula.split('+');
    // for (const item of splitArr) {
    //   let filterRcjData = [];
    //   for (const key in kindMap) {
    //     if (kindMap.hasOwnProperty(key) && item.includes(key)) {
    //       filterRcjData = filterRcjData.concat(rcjData.filter(rcjItem => kindMap[key].includes(rcjItem.kind)));
    //     }
    //   }
    //   if (ObjectUtil.isNotEmpty(filterRcjData)) {
    //     for (const rcj of filterRcjData) {
    //       let de = deArr.find(de => de.sequenceNbr == rcj.deId);
    //       for (const field in fieldMap) {
    //         if (fieldMap.hasOwnProperty(field) && item.includes(field)) {
    //           // fieldValue 就是定额价或市场价的值
    //           let fieldValue = 0;
    //           if (is22Unit) {
    //             // 如果是22的 这里需要特殊处理
    //             // 如果field是“基期价” 简易计税使用含税基期价，一般计税使用不含税基期价
    //             // 如果field是“市场价” 简易计税使用含税市场价，一般计税使用不含税市场价
    //             if (field == '基期价') {
    //               fieldValue = Number(taxCalculationMethod == 1 ? rcj.priceBaseJournal : rcj.priceBaseJournalTax);
    //             } else if (field == '市场价') {
    //               fieldValue = Number(taxCalculationMethod == 1 ? rcj.priceMarket : rcj.priceMarketTax);
    //             } else {
    //               fieldValue = Number(rcj[fieldMap[field]]);
    //             }
    //           } else {
    //             fieldValue = Number(rcj[fieldMap[field]]);
    //           }
    //           // 定额价/市场价是 ：【定额价/市场价 合价=人材机定额价/市场价*人材机消耗量*定额工程量】
    //           fieldValue = fieldValue * rcj.resQty * de.quantity;
    //           value = NumberUtil.add(value, fieldValue);
    //         }
    //       }
    //     }
    //   }
    // }
    return value;
  }


  async saveWaterElectricCostData(args) {
    const { unitId, singleId, constructId, waterElectricCostData } = args;
    const unitObj = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    unitObj.waterElectricCostData = await this.calculateWaterElectricCost(args);
    // 触发费用代码计算
    await this.service.unitCostCodePriceService.countCostCodePrice({
      constructId: constructId,
      singleId: singleId,
      unitId: unitId
    });
    return true;
  }

  /**
   * 获取水电费
   */
  async getWaterElectricCost(args) {
    const { unitId, singleId, constructId } = args;
    const result = { GCSF: 0, GCDF: 0, GCSDF: 0 };
    const unitObj = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    if (unitObj == null || unitObj.waterElectricCostData == null) {
      return result;
    }

    if (unitObj.waterElectricCostData.customWaterElectricFlag === true) {
      // 勾选了独立记取水电费
      result.GCSDF = ObjectUtil.isEmpty(unitObj.waterElectricCostData.customWaterElectric) ? 0 : unitObj.waterElectricCostData.customWaterElectric;
    } else {
      result.GCDF = ObjectUtil.isEmpty(unitObj.waterElectricCostData.totalElectricCost) ? 0 : unitObj.waterElectricCostData.totalElectricCost;
      result.GCSF = ObjectUtil.isEmpty(unitObj.waterElectricCostData.totalWaterCost) ? 0 : unitObj.waterElectricCostData.totalWaterCost;
      result.GCSDF = ObjectUtil.isEmpty(unitObj.waterElectricCostData.waterElectricCost) ? 0 : unitObj.waterElectricCostData.waterElectricCost;
    }
    return result;
  }

  async updateWaterElectricCostData(args) {
    // 添加临时编辑的标识参数
    return await this.calculateWaterElectricCost(args);
  }

  async autoCalculateWaterElectricCost(args) {
    const { unitId, singleId, constructId } = args;
    const unitObj = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    unitObj.waterElectricCostData = await this.calculateWaterElectricCost(args);
    // 触发费用代码计算
    // await this.service.unitCostCodePriceService.countCostCodePrice({
    //   constructId: constructId,
    //   singleId: singleId,
    //   unitId: unitId
    // });
  }

}

WaterElectricCostMatchService.toString = () => '[class WaterElectricCostMatchService]';
module.exports = WaterElectricCostMatchService;
