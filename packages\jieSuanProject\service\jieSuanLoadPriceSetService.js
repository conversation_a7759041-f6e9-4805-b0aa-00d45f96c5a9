const {ObjectUtil} = require("../../../common/ObjectUtil");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {JieSuanRcjStageUtils} = require("../utils/JieSuanRcjStageUtils");
const {JieSuanUpdateRcjStrategy} = require("../rcj_handle/update/jieSuanUpdateRcjStrategy");
const {Service} = require("../../../core");


class JieSuanLoadPriceSetService extends Service{
    constructor(ctx) {
        super(ctx);
    }



    /**
     * 批量应用接口更新市场价、价格来源
     * @param args
     * @returns {Promise<void>}
     */
    async applyLoadingPriceInRcjDetails(args) {
        let {constructId, singleId, unitId, type, priceType, num, kind,priceMethod} = args;
        let rcjDatas = (await this.getCurrentLoadingRcjs(constructId, singleId, unitId, type)).filter(item => item.isExecuteLoadPrice);

        if (type === 3) {
            let is22 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
            await this.batchchangeRcjZaiJia(constructId, singleId, unitId, type, rcjDatas,num,priceType,is22,kind,priceMethod);
            //计算费用代码和更新费用汇总
            this.service.jieSuanProject.jieSuanUnitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
            });

        }
        if (type === 2) {
            let unitList = PricingFileFindUtils.getUnitListBySingle(constructId, singleId)
            for (let unit of unitList) {
                let is22 = PricingFileFindUtils.is22Unit(unit);
                this.unitLoadPrice(unit,rcjDatas,constructId, unit.spId, unit.sequenceNbr,type,num,priceType,is22,kind,priceMethod);
            }
        }
        if (type == 1) { //工程项目层级
            let unitList = PricingFileFindUtils.getUnitList(constructId);
            unitList = unitList.filter(k => k.originalFlag);
            for (let unit of unitList) {
                let is22 = PricingFileFindUtils.is22Unit(unit);
                this.unitLoadPrice(unit,rcjDatas,constructId, unit.spId, unit.sequenceNbr,type,num,priceType,is22,kind,priceMethod);
            }
        }
        // //拿到所有人材机  进行数据更新
        // let differenceSetNum = await this.queryRcjDifferenceSet(args);
        // let updataRcj = {};
        // for (let i = 0; i < rcjDatas.length; i++) {
        //     let rcjData = rcjDatas[i]
        //     // let jieSuanRcjDifferenceType = rcjData.jieSuanRcjDifferenceTypeList.find(a => a.rcjDifferenceType === differenceSetNum)
        //     //let jieSuanRcjDifferenceType=rcjData.jieSuanRcjDifferenceTypeList[differenceSetNum];
        //     // if (priceType === 1 && num === null) {
        //     //     //单价
        //     //     jieSuanRcjDifferenceType.jieSuanDifferencePriceList[0].jieSuanPrice = rcjData.loadPrice;
        //     //     //单价来源
        //     //     jieSuanRcjDifferenceType.jieSuanDifferencePriceList[0].jieSuanPriceSource = rcjData.sourcePrice;
        //     //     updataRcj.jieSuanPrice = rcjData.loadPrice;
        //     //     updataRcj.jieSuanPriceSource = rcjData.sourcePrice;
        //     //
        //     // }
        //     // if (priceType === 1 && num !== null) {
        //     //     //分期 i期 单价集合
        //     //     jieSuanRcjDifferenceType.jieSuanDifferencePriceList[num - 1].jieSuanPrice = rcjData.loadPrice
        //     //     //结算分期单价来源
        //     //     jieSuanRcjDifferenceType.jieSuanDifferencePriceList[num - 1].jieSuanPriceSource = rcjData.sourcePrice
        //     //     updataRcj.jieSuanPrice = rcjData.loadPrice;
        //     //     updataRcj.jieSuanPriceSource = rcjData.sourcePrice;
        //     //
        //     // }
        //     // if (priceType === 2) {
        //     //     //基期价
        //     //     jieSuanRcjDifferenceType.jieSuanBasePrice = rcjData.loadPrice
        //     //     //基期价来源
        //     //     jieSuanRcjDifferenceType.jieSuanBasePriceSource = rcjData.sourcePrice
        //     //     updataRcj.jieSuanBasePrice = rcjData.loadPrice;
        //     //     updataRcj.jieSuanBasePriceSource = rcjData.sourcePrice;
        //
        //     if(!is22){
        //         if (priceType === 1) {
        //             updataRcj.priceMarket = rcjData.loadPrice;
        //             updataRcj.jieSuanPriceSource = rcjData.sourcePrice;
        //         }
        //         if (priceType === 2) {
        //             updataRcj.jieSuanBasePrice = rcjData.loadPrice;
        //             updataRcj.jieSuanBasePriceSource = rcjData.sourcePrice;
        //         }
        //     }else {
        //         //结算单价
        //         if(priceType === 1){
        //             updataRcj.priceMarket = rcjData.loadPrice;
        //             updataRcj.jieSuanPriceSource = rcjData.sourcePrice;
        //             // if(simpleMethod){
        //             //     updataRcj.priceMarketTax = rcjData.loadPrice;
        //             //     updataRcj.jieSuanPriceSource = rcjData.sourcePrice;
        //             // }else {
        //             //     updataRcj.priceMarket = rcjData.loadPrice;
        //             //     updataRcj.jieSuanPriceSource = rcjData.sourcePrice;
        //             // }
        //         }
        //         //基期价
        //         if(priceType === 2){
        //             updataRcj.jieSuanBasePrice = rcjData.loadPrice;
        //             updataRcj.jieSuanBasePriceSource = rcjData.sourcePrice;
        //             // if(simpleMethod){
        //             //     updataRcj.jieSuanBasePrice = rcjData.loadPrice;
        //             //     updataRcj.jieSuanBasePriceSource = rcjData.sourcePrice;
        //             // }else {
        //             //     updataRcj.priceBaseJournal = rcjData.loadPrice;
        //             //     updataRcj.jieSuanPriceSource = rcjData.sourcePrice;
        //             // }
        //         }
        //     }
        //
        //     updataRcj.highlight=true; //载价记录
        //
        // }



    }


    async unitLoadPrice(unit,rcjDatas,constructId, singleId, unitId,type,num,priceType,is22,kind,priceMethod){
        await this.batchchangeRcjZaiJia(constructId, singleId, unitId, type, rcjDatas,num,priceType,is22,kind,priceMethod);
        //计算费用代码和更新费用汇总
        this.service.jieSuanProject.jieSuanUnitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
        });
        //清除单位的本次载价标识
        if (!ObjectUtils.isEmpty(unit.constructProjectRcjs)) {
            for (let i = 0; i < unit.constructProjectRcjs.length; i++) {
                unit.constructProjectRcjs[i].currentLoadingFinished = null;
            }
        }
        if (!ObjectUtils.isEmpty(unit.rcjDetailList)) {
            for (let i = 0; i < unit.rcjDetailList.length; i++) {
                unit.rcjDetailList[i].currentLoadingFinished = null;
            }
        }
    }

    /**
     * 点击批量载价 返回载价编辑弹窗数据
     * @returns {Promise<void>}
     */
    async loadingPrice(args) {
        //type 1 = 工程项目  2 单项 3 单位工程
        //batchAllFlag,是否批量调整所有价格
        //loadPriortyList 载价优先级设置 1信息价 2市场价  3推荐价  ，如果选择为空传0
        //laodPriceConditionList 载价条件  默认还是 1信息价 2市场价  3推荐价
        //num   分期或者分次   没有分期为null
        //priceType 1 结算价  2 基期价
        let {constructId, singleId,unitId,type,batchAllFlag,loadPriortyList,laodPriceConditionList,num,priceType,priceMethod} = args;

        args.jsFlag=true;
        //远程获取的人材机数据
        let remoteRcjData = await this.service.loadPriceSetService.getRemoteRcjDataAvg(args);
        let is22;
        if(type==3){
            is22=PricingFileFindUtils.is22UnitById(constructId, singleId,unitId);
        }else {
            is22 = (args.deStandardReleaseYear=="22");
        }
        let rcjData = await this.loadPriceRcjData(args,batchAllFlag);
        if (!ObjectUtils.isEmpty(rcjData)){
            rcjData = JSON.parse(JSON.stringify(rcjData))
        }else {
            return  null;
        }
        let simpleMethod = PricingFileFindUtils.getConstructProjectTaxCalculationMethod(constructId);
        for (const item of rcjData) {
            let filter = remoteRcjData.filter(itemRemote => itemRemote.id == item.standardId)[0];
            // if(priceType==1){
            //     if(is22){
            //         item.marketPriceBeforeLoading = simpleMethod?item.priceMarketTax:item.priceMarket;
            //         item.marketSourcePriceBeforeLoading = item.jieSuanPriceSource;
            //     }else {
            //         item.marketPriceBeforeLoading = item.priceMarket;
            //         item.marketSourcePriceBeforeLoading = item.jieSuanPriceSource;
            //     }
            // }else {
            //     if(is22){
            //         item.marketPriceBeforeLoading = simpleMethod?item.priceBaseJournalTax:item.priceBaseJournal;
            //         item.marketSourcePriceBeforeLoading = item.jieSuanBasePriceSource;
            //     }else {
            //         item.marketPriceBeforeLoading = item.jieSuanBasePrice;
            //         item.marketSourcePriceBeforeLoading = item.jieSuanBasePriceSource;
            //     }
            //
            // }
            let queryRcjPriceAndSource1 = this.queryRcjPriceAndSource(item,num,simpleMethod,is22,priceType,priceMethod);
            item.marketPriceBeforeLoading = queryRcjPriceAndSource1.priceLoading;
            item.marketSourcePriceBeforeLoading = queryRcjPriceAndSource1.sourcePriceLoading;
            if(ObjectUtils.isNotEmpty(filter)){
                item.informationPrice = filter.informationPrice;
                item.marketPrice = filter.marketPrice;
                item.recommendPrice = filter.recommendPrice;
                item.informationSourcePrice = filter.informationSourcePrice;
                item.marketSourcePrice = filter.marketSourcePrice;
                item.recommendSourcePrice = filter.recommendSourcePrice;

                //原始的精准数据备份  用于优先级调整后
                item.marketPriceOrigin = filter.marketPrice;
                item.marketSourcePriceOrigin = filter.marketSourcePrice;//市场价价格来源
                item.recommendPriceOrigin = filter.recommendPrice;
                item.recommendSourcePriceOrigin = filter.recommendSourcePrice;
                item.informationPriceOrigin = filter.informationPrice;
                item.informationSourcePriceOrigin = filter.informationSourcePrice;

                item.informationPriceList = filter.informationPriceList;
                item.marketPriceList = filter.marketPriceList;
                item.recommendPriceList = filter.recommendPriceList;
            }


            // //挂待载价格
            // await this.updateLoadPriceByLevel(loadPriortyList, item, 0);

            //以下处理用于 有精准匹配 且匹配数据只有一条时 前端的放大镜不进行展示
            if (item.highlight) {
                if (item.informationPriceList != null && item.informationPriceList.length == 1) {
                    item.informationPriceList = null;
                }
                if (item.marketPriceList != null && item.marketPriceList.length == 1) {
                    item.marketPriceList = null;
                }
                if (item.recommendPriceList != null && item.recommendPriceList.length == 1) {
                    item.recommendPriceList = null;
                }
            }
            item.isExecuteLoadPrice = true;
        }

        if (type == 3) {  //单位工程
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            unit.unitRcjsLoading = rcjData;
        }else if (type == 2) {
            let single = PricingFileFindUtils.getSingleProject(constructId,singleId);
            single.singleRcjsLoading = rcjData;
        } else if (type == 1) {
            let project = PricingFileFindUtils.getProjectObjById(constructId);
            project.projectRcjsLoading = rcjData;
        }

        return rcjData.map(item => item.sequenceNbr);

    }


    /**
     * 获取人材机的待载价格和来源 //priceType 1 结算价  2 基期价
     */
    queryRcjPriceAndSource(rcj,num,taxMethod,is22,priceType,priceMethod){

          let obj={};
          let {tax,noTax,taxRate}=priceMethod;
          //结算单价分期的话每期都有各自的
        if(priceType==1){
            if(is22){
                if(ObjectUtils.isEmpty(num)){
                    if (tax){
                        obj.priceLoading = rcj.priceMarketTax;
                    }
                    if (noTax){
                        obj.priceLoading = rcj.priceMarket;
                    }
                    if (tax && noTax){
                        obj.priceLoading = taxMethod?rcj.priceMarketTax:rcj.priceMarket;
                    }
                    obj.sourcePriceLoading = rcj.jieSuanPriceSource;
                }else {
                    //处理价格来源
                    if(ObjectUtils.isNotEmpty(rcj.jieSuanRcjDifferenceTypeList)){
                        let jieSuanDifferencePriceListElement = rcj.jieSuanRcjDifferenceTypeList[num-1];
                        if (tax){
                            obj.priceLoading = jieSuanDifferencePriceListElement.priceMarketTax;
                        }
                        if (noTax){
                            obj.priceLoading = jieSuanDifferencePriceListElement.priceMarket;
                        }
                        if (tax && noTax){
                            obj.priceLoading=taxMethod?jieSuanDifferencePriceListElement.priceMarketTax:jieSuanDifferencePriceListElement.priceMarket;
                        }
                        obj.sourcePriceLoading=jieSuanDifferencePriceListElement.jieSuanPriceSource;
                    }
                }
            }else {
                if(ObjectUtils.isEmpty(num)){
                    if (tax){
                        obj.priceLoading = rcj.priceMarketTax;
                    }
                    if (noTax){
                        obj.priceLoading = rcj.priceMarket;
                    }
                    if (tax && noTax){
                        obj.priceLoading = taxMethod?rcj.priceMarketTax:rcj.priceMarket;
                    }
                    //obj.priceLoading = rcj.marketPrice;
                    obj.sourcePriceLoading = rcj.jieSuanPriceSource;
                }else {
                    //处理价格来源
                    if(ObjectUtils.isNotEmpty(rcj.jieSuanRcjDifferenceTypeList)){
                        let jieSuanDifferencePriceListElement = rcj.jieSuanRcjDifferenceTypeList[num-1];
                        if (tax){
                            obj.priceLoading = jieSuanDifferencePriceListElement.priceMarketTax;
                        }
                        if (noTax){
                            obj.priceLoading = jieSuanDifferencePriceListElement.priceMarket;
                        }
                        if (tax && noTax){
                            obj.priceLoading = taxMethod?jieSuanDifferencePriceListElement.priceMarketTax:jieSuanDifferencePriceListElement.priceMarket;
                        }
                        //obj.priceLoading=jieSuanDifferencePriceListElement.marketPrice;
                        obj.sourcePriceLoading=jieSuanDifferencePriceListElement.jieSuanPriceSource;
                    }
                }
            }
            obj.taxLoading = rcj.taxRate;
        }else {
            if (tax){
                obj.priceLoading = rcj.priceBaseJournalTax;
            }
            if (noTax){
                obj.priceLoading = rcj.priceBaseJournal;
            }
            if (tax && noTax){
                obj.priceLoading = taxMethod?rcj.priceBaseJournalTax:rcj.priceBaseJournal;
            }
            obj.sourcePriceLoading = rcj.jieSuanBasePriceSource;

            // if(is22){
            //     obj.priceLoading = taxMethod?rcj.priceBaseJournalTax:rcj.priceBaseJournal;
            //     obj.sourcePriceLoading = rcj.jieSuanBasePriceSource;
            // }else {
            //     obj.priceLoading = rcj.jieSuanBasePrice;
            //     obj.sourcePriceLoading = rcj.jieSuanBasePriceSource;
            // }
        }

        return obj;

    }



    /**
     * 根据载价优先级的设置 更改待载价格
     * @returns {Promise<void>}
     */
    //1信息价 2市场价  3推荐价
    async updateLoadPriceByLevel(loadPriortyList,rcjData,index) {

        if (loadPriortyList[index] == 0 && index !=2) {  //前两个选项都为空 走这
            return await this.updateLoadPriceByLevel(loadPriortyList,rcjData,index+1);
        };
        if ((loadPriortyList[index] == 0 && index == 2)||index==3) { //如果走到这里  取原来人材机的市场价
            if (rcjData.informationPrice!=null) {
                if (loadPriortyList[0] == 1 || (loadPriortyList[0] == 0 && loadPriortyList[1] == 1) || (loadPriortyList[0] == 0 && loadPriortyList[1] == 0 && loadPriortyList[2] == 1)) {
                    rcjData.loadPrice = rcjData.informationPrice;
                    rcjData.sourcePrice = rcjData.informationSourcePrice;
                    rcjData['highlight'] = true;
                    return ;
                }
            }
            if (rcjData.recommendPrice!=null) {
                if (loadPriortyList[0] == 3 || (loadPriortyList[0] == 0 && loadPriortyList[1] == 3) || (loadPriortyList[0] == 0 && loadPriortyList[1] == 0 && loadPriortyList[2] == 3)) {
                    rcjData.loadPrice = rcjData.recommendPrice;
                    rcjData.sourcePrice = rcjData.recommendSourcePrice;
                    rcjData['highlight'] = true;
                    return ;
                }
            }
            if (rcjData.marketPrice!=null) {
                if (loadPriortyList[0] == 2 || (loadPriortyList[0] == 0 && loadPriortyList[1] == 2) || (loadPriortyList[0] == 0 && loadPriortyList[1] == 0 && loadPriortyList[2] == 2)) {
                    rcjData.loadPrice = rcjData.marketPrice;
                    rcjData.sourcePrice = rcjData.marketSourcePrice;
                    rcjData['highlight'] = true;
                    return ;
                }
            }
            rcjData.loadPrice = rcjData.marketPriceBeforeLoading;
            rcjData.sourcePrice = rcjData.marketSourcePriceBeforeLoading;
            rcjData['highlight'] = false;
            return;
        }
        if (loadPriortyList[index] == 1) { //如果为信息价
            if (rcjData.informationPriceOrigin != null) {
                rcjData.loadPrice = rcjData.informationPriceOrigin;
                rcjData.sourcePrice = rcjData.informationSourcePriceOrigin;

                //--STRAT---数据初始化--------------------------
                // rcjData.informationPrice = rcjData.informationPriceOrigin;
                // rcjData.recommendPrice = rcjData.recommendPriceOrigin;
                // rcjData.marketPrice = rcjData.marketPriceOrigin;
                //
                // rcjData.informationSourcePrice = rcjData.informationSourcePriceOrigin;
                // rcjData.marketSourcePrice = rcjData.marketSourcePriceOrigin;
                // rcjData.recommendSourcePrice = rcjData.recommendSourcePriceOrigin;
                //--END---数据初始化--------------------------
                rcjData['highlight'] = true;
                return ;
            }else {
                return await this.updateLoadPriceByLevel(loadPriortyList,rcjData,index+1);
            }
        }
        if (loadPriortyList[index] == 2) {  //如果为市场价
            if (rcjData.marketPriceOrigin != null) {
                rcjData.loadPrice = rcjData.marketPriceOrigin;
                rcjData.sourcePrice = rcjData.marketSourcePriceOrigin;

                //--STRAT---数据初始化--------------------------
                // rcjData.informationPrice = rcjData.informationPriceOrigin;
                // rcjData.recommendPrice = rcjData.recommendPriceOrigin;
                // rcjData.marketPrice = rcjData.marketPriceOrigin;
                //
                // rcjData.informationSourcePrice = rcjData.informationSourcePriceOrigin;
                // rcjData.marketSourcePrice = rcjData.marketSourcePriceOrigin;
                // rcjData.recommendSourcePrice = rcjData.recommendSourcePriceOrigin;
                //--END---数据初始化--------------------------
                rcjData['highlight'] = true;
                return ;
            }else {
                return await this.updateLoadPriceByLevel(loadPriortyList,rcjData,index+1);
            }
        }
        if (loadPriortyList[index] == 3) {  //如果为推荐价
            if (rcjData.recommendPriceOrigin != null) {
                rcjData.loadPrice = rcjData.recommendPriceOrigin;
                rcjData.sourcePrice = rcjData.recommendSourcePriceOrigin;

                //--STRAT---数据初始化--------------------------
                // rcjData.informationPrice = rcjData.informationPriceOrigin;
                // rcjData.recommendPrice = rcjData.recommendPriceOrigin;
                // rcjData.marketPrice = rcjData.marketPriceOrigin;
                //
                // rcjData.informationSourcePrice = rcjData.informationSourcePriceOrigin;
                // rcjData.marketSourcePrice = rcjData.marketSourcePriceOrigin;
                // rcjData.recommendSourcePrice = rcjData.recommendSourcePriceOrigin;
                //--END---数据初始化--------------------------
                rcjData['highlight'] = true;
                return ;
            }else {
                return await this.updateLoadPriceByLevel(loadPriortyList,rcjData,index+1);
            }
        }
    }

    // /**
    //  * 双击价格选择弹窗的数据行 更新待载价格
    //  * @param rcjId
    //  * @param popUpDataId
    //  * @param updatePrice
    //  * @returns {Promise<void>}
    //  */
    // async updateLoadPrice(args){
    //     let {constructId, singleId,unitId,type,rcjId,popUpDataId,updatePrice,sourcePrice,loadPrice,vagueSourcePrice,updateRate} = args;
    //     let rcjDatas = await this.getCurrentLoadingRcjs(constructId,singleId,unitId,type);
    //     let data = rcjDatas.filter(item => item.sequenceNbr==rcjId);
    //
    //     if (popUpDataId == null && sourcePrice != null && updatePrice != null) { //表示双击价格
    //         data[0].loadPrice = updatePrice;
    //         data[0].sourcePrice = sourcePrice;
    //         data[0].highlight = true;
    //         return ;
    //     }
    //
    //     if (popUpDataId == null && !ObjectUtils.isEmpty(loadPrice) ){
    //         data[0].loadPrice = loadPrice;
    //         data[0].sourcePrice = "自行询价";
    //         data[0].highlight = true;
    //         return ;
    //     }
    //     let recommendList = [];
    //     let informationList = [];
    //     let marketList = [];
    //     if (data[0].recommendPriceList != null) {
    //         recommendList = data[0].recommendPriceList.map(item => item.sequenceNbr);
    //     }
    //     if (data[0].informationPriceList!=null) {
    //         informationList = data[0].informationPriceList.map(item => item.sequenceNbr);
    //     }
    //     if (data[0].marketPriceList != null) {
    //         marketList = data[0].marketPriceList.map(item => item.sequenceNbr);
    //     }
    //     if (popUpDataId != null && updatePrice==null) {
    //         //根据id判断修改的是市场价还是信息价等  修改待载价格、信息价等及价格来源
    //         if (recommendList.includes(popUpDataId)) {
    //             let filter = data[0].recommendPriceList.filter(item => item.sequenceNbr==popUpDataId);
    //             data[0].loadPrice = filter[0].marketPrice;
    //             data[0].recommendPrice = filter[0].marketPrice;
    //             data[0].recommendSourcePrice = filter[0].sourcePrice;
    //             data[0].sourcePrice = filter[0].sourcePrice;
    //         }
    //         if (informationList.includes(popUpDataId)) {
    //             let filter = data[0].informationPriceList.filter(item => item.sequenceNbr==popUpDataId);
    //             data[0].loadPrice = filter[0].marketPrice;
    //             data[0].informationPrice = filter[0].marketPrice;
    //             data[0].informationSourcePrice = filter[0].sourcePrice;
    //             data[0].sourcePrice = filter[0].sourcePrice;
    //         }
    //         if (marketList.includes(popUpDataId)) {
    //             let filter = data[0].marketPriceList.filter(item => item.sequenceNbr==popUpDataId);
    //             data[0].loadPrice = filter[0].marketPrice;
    //             data[0].marketPrice = filter[0].marketPrice;
    //             data[0].marketSourcePrice = filter[0].sourcePrice;
    //             data[0].sourcePrice = filter[0].sourcePrice;
    //         }
    //         data[0].highlight = true;
    //     }else if (popUpDataId != null && updatePrice!=null) {
    //         //根据id判断修改的是市场价还是信息价等  修改待载价格、信息价等及价格来源
    //         if (recommendList.includes(popUpDataId)) {
    //             let filter = data[0].recommendPriceList.filter(item => item.sequenceNbr==popUpDataId && vagueSourcePrice ==item.sourcePrice);
    //             data[0].loadPrice = updatePrice;
    //             data[0].recommendPrice = updatePrice;
    //             data[0].recommendSourcePrice = filter[0].sourcePrice;
    //             data[0].sourcePrice = filter[0].sourcePrice;
    //         }
    //         if (informationList.includes(popUpDataId)) {
    //             let filter = data[0].informationPriceList.filter(item => item.sequenceNbr==popUpDataId && vagueSourcePrice ==item.sourcePrice);
    //             data[0].loadPrice = updatePrice;
    //             data[0].informationPrice = updatePrice;
    //             data[0].informationSourcePrice = filter[0].sourcePrice;
    //             data[0].sourcePrice = filter[0].sourcePrice;
    //         }
    //         if (marketList.includes(popUpDataId)) {
    //             let filter = data[0].marketPriceList.filter(item => item.sequenceNbr==popUpDataId && vagueSourcePrice ==item.sourcePrice);
    //             data[0].loadPrice = updatePrice;
    //             data[0].marketPrice = updatePrice;
    //             data[0].marketSourcePrice = filter[0].sourcePrice;
    //             data[0].sourcePrice = filter[0].sourcePrice;
    //         }
    //         if(updateRate != null){
    //             data[0].taxRate = updateRate;
    //         }
    //         data[0].highlight = true;
    //     }
    // }

    /**
     * 返回载价编辑弹窗数据
     * @param args
     * @returns {Promise<void>}
     */
    async jieSuanloadPriceEditPage(args) {
        //rcjIdList 为勾选的人材机id   kindList 为勾选的类型
        //num   分期或者分次   没有分期为null
        //priceType   1 结算价  2 基期价
        let {constructId, singleId,unitId,type,pageNum,pageSize,kindList,rcjIdList,loadPriortyList,kind,num,priceType} = args;
        let rcjDatas = await this.getCurrentLoadingRcjs(constructId,singleId,unitId,type);

        //按照优先级进行挂载
        if (loadPriortyList != null) {
            for (let i = 0; i < rcjDatas.length; i++) {
                //根据载价后的数据  设置待载价格和价格来源
                // let sourcercj = this.handleLoadPriceSourcePrice(rcjDatas[i],constructId, singleId, unitId,kind,priceType,num);
                await this.updateLoadPriceByLevel(loadPriortyList,rcjDatas[i],0);
            }
        }
        let rcjDataWant = rcjDatas.filter(item => kindList.includes(item.kind));
        for (let i = 0; i < rcjDataWant.length; i++) {
            rcjDataWant[i].isExecuteLoadPrice = true;
        }
        //同时对过滤掉的标识置为false
        let rcjDataNotWant = rcjDatas.filter(item => !kindList.includes(item.kind));
        for (let i = 0; i < rcjDataNotWant.length; i++) {
            rcjDataNotWant[i].isExecuteLoadPrice = false;
        }
        //对筛选出来的数据未执行勾选的 标识置为false
        for (let i = 0; i < rcjDataWant.length; i++) {
            if (!rcjIdList.includes(rcjDataWant[i].sequenceNbr)) {
                rcjDataWant[i].isExecuteLoadPrice = false;
            }
        }

        let start = (pageNum-1)*pageSize;
        let end = pageNum*pageSize;
        let total = rcjDataWant.length;
        let dataWant = rcjDataWant.slice(start,end);
        // //处理价格来源
        // this.handleSourcePrice(dataWant,constructId, singleId, unitId,kind,priceType,num);
        //同时返回统计信息
        let rcjDataOrigin=await this.queryConstructRcjByDeIdNew(args);

        //不要二次解析的父级材料
        rcjDataOrigin = rcjDataOrigin.filter(k => !(k.markSum == 1 && (k.levelMark == 1 || k.levelMark == 2)));
        let result = await this.service.loadPriceSetService.getLoadPriceVariety(rcjDatas,rcjDataOrigin);
        result['total'] = total;
        result['data'] = dataWant;
        return result;
    }



    async getCurrentLoadingRcjs(constructId, singleId, upId, type) {
        if (type == 3) {
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, upId);
            return unit.unitRcjsLoading;
        } else if (type == 2) {
            let single = PricingFileFindUtils.getSingleProject(constructId,singleId);
            return single.singleRcjsLoading;
        }else if (type == 1) {
            let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
            return projectObjById.projectRcjsLoading;
        }
        return null;
    }


    /**
     *   查询项目对应调整法
     */
    async queryRcjDifferenceSet(args) {
        let {constructId, singleId, unitId, kind,type} = args;
        if (type === 3) {
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            let rcjDifference = unit.adjustMethodList.filter(a => a.kind === kind)
            return rcjDifference[0].adjustMethod;
        }
        if (type === 2) {
            let singleProject = PricingFileFindUtils.getSingleProject(constructId, singleId);
            let rcjDifference = singleProject.adjustMethodList.filter(a => a.kind === kind)
            return rcjDifference[0].adjustMethod;
        }
        if (type === 1) {
            let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
            let rcjDifference = projectObj.adjustMethodList.filter(a => a.kind === kind)
            return rcjDifference[0].adjustMethod;
        }

    }

    async batchchangeRcjZaiJia(constructId, singleId, unitId, type, rclList,num,priceType,is22,kind,priceMethod) {
        let {tax,noTax,taxRate} = priceMethod;
        let simpleMethod = PricingFileFindUtils.getConstructProjectTaxCalculationMethod(constructId);
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let args={
            constructId:constructId,
            singleId:singleId,
            unitId:unitId,
            levelType:type,
            num:num,
            kind:kind,
            type:unit.originalFlag?1:null
        }
        for (let i = 0; i < rclList.length; i++) {
            let rcjData = rclList[i];
            let updataRcj={};
            if(is22){
                let colume = "";
                if (priceType === 1) {
                    if (tax){
                        colume =  "priceMarketTax"
                    }
                    if (noTax){
                        colume =  "priceMarket"
                    }
                    if (tax && noTax){
                        colume = simpleMethod?"priceMarketTax":"priceMarket";
                    }
                    updataRcj[colume] = rcjData.loadPrice;
                    updataRcj.jieSuanPriceSource = rcjData.sourcePrice;
                }
                if (priceType === 2) {
                    if (tax){
                        colume =  "priceBaseJournalTax"
                    }
                    if (noTax){
                        colume =  "priceBaseJournal"
                    }
                    if (tax && noTax){
                        colume = simpleMethod?"priceBaseJournalTax":"priceBaseJournal";
                    }
                    updataRcj[colume] = rcjData.loadPrice;
                    //updataRcj.jieSuanBasePrice = rcjData.loadPrice;
                    updataRcj.jieSuanBasePriceSource = rcjData.sourcePrice;
                }
            }else {
                //结算单价
                if(priceType === 1){
                    //updataRcj.priceMarket = rcjData.loadPrice;
                    updataRcj.marketPrice = rcjData.loadPrice;
                    updataRcj.jieSuanPriceSource = rcjData.sourcePrice;
                    // if(simpleMethod){
                    //     updataRcj.priceMarketTax = rcjData.loadPrice;
                    //     updataRcj.jieSuanPriceSource = rcjData.sourcePrice;
                    // }else {
                    //     updataRcj.priceMarket = rcjData.loadPrice;
                    //     updataRcj.jieSuanPriceSource = rcjData.sourcePrice;
                    // }
                }
                //基期价
                if(priceType === 2){
                    updataRcj.jieSuanBasePrice = rcjData.loadPrice;
                    updataRcj.jieSuanBasePriceSource = rcjData.sourcePrice;
                    // if(simpleMethod){
                    //     updataRcj.jieSuanBasePrice = rcjData.loadPrice;
                    //     updataRcj.jieSuanBasePriceSource = rcjData.sourcePrice;
                    // }else {
                    //     updataRcj.priceBaseJournal = rcjData.loadPrice;
                    //     updataRcj.jieSuanPriceSource = rcjData.sourcePrice;
                    // }
                }
            }
            if (taxRate){
                updataRcj.taxRate = rcjData.taxRate;
            }
            updataRcj.highlight=true; //载价记录
            let jieSuanUpdateRcjStrategy = new JieSuanUpdateRcjStrategy({constructId:rcjData.constructId, singleId:rcjData.singleId, unitId:rcjData.unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId),arg:args});
            await jieSuanUpdateRcjStrategy.execute({pointLine:rcjData,upDateInfo:updataRcj});

        }


        // for (const rcj of rclList) {
        //     //封装修改参数
        //     let constructProjectRcj = {
        //         //marketPrice: rcj.marketPriceBeforeLoading,  //合同确认单价
        //         jieSuanRcjDifferenceTypeList: rcj.jieSuanRcjDifferenceTypeList, //合同确认单价
        //         jieSuanPrice : updataRcj.jieSuanPrice,  //结算单价
        //         jieSuanPriceSource : updataRcj.jieSuanPriceSource,  //结算单价
        //         jieSuanBasePrice: updataRcj.jieSuanBasePrice,  //基期价
        //         jieSuanBasePriceSource: updataRcj.jieSuanBasePriceSource, //基期价来源
        //         highlight: true //载价记录
        //     };
        //     rcjObj.sequenceNbr = rcj.sequenceNbr;
        //     rcjObj.constructProjectRcj = constructProjectRcj;
        //     await this.service.jieSuanProject.jieSuanRcjStageService.changeRcjNewJieSuan(rcjObj);
        // }

    }







    /**
     * 清除载价
     * @param args
     * @returns {Promise<void>}
     */
    jiesuanClearLoadPriceUse(args){
        //type 1 = 工程项目  2 单位工程
        let {constructId, singleId, unitId,type,rcj,num} = args;
        if (type != 3){
             this.clearLoadPriceUseConstruct(args);
        }else {
             this.clearLoadPriceUseUnit(constructId,singleId,unitId,rcj,num);
        }

        return true;

    }

    /**
     * 工程项目汇总 清除载价
     * @param args
     * @returns {Promise<void>}
     */
     clearLoadPriceUseConstruct(args){

        //type 1 = 工程项目  2 单位工程
        let {constructId,singleId,rcj,fqNum,type} = args;
        let unitList = [];
        if (type == 1){
            unitList = PricingFileFindUtils.getUnitList(constructId);
        }
        if (type == 2){
            unitList = PricingFileFindUtils.getUnitListBySingle(constructId,singleId);
        }
        for (let unit of unitList) {
            this.clearLoadPriceUseUnit(constructId,unit.spId,unit.sequenceNbr,rcj,fqNum);
        }
    }


    /**
     * 单位工程汇总 清除载价
     * @param constructId
     * @param singleId
     * @param unitId
     * @param rcjData 人材机对象
     * @returns {Promise<void>}
     */
    clearLoadPriceUseUnit(constructId,singleId,unitId,rcjData,fqNum){

        //type 1 = 工程项目  2 单位工程

        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        //人材机一级
        let constructProjectRcjs = unitProject.constructProjectRcjs;

        //人材机二级
        let rcjDetailList = unitProject.rcjDetailList;
        let isStage = JieSuanRcjStageUtils.isStage(unitProject);


        let rcjs = constructProjectRcjs.filter(item => {
            return item.materialCode === rcjData.materialCode
                && item.materialName === rcjData.materialName
                && item.specification === rcjData.specification
                && item.unit === rcjData.unit
                && item.dePrice === rcjData.dePrice
                && item.markSum === rcjData.markSum
                && item.marketPrice === rcjData.marketPrice
        });
        //二级材料的子级材料
        let rcjDetails = rcjDetailList.filter(item => {
            return item.materialCode === rcjData.materialCode
                && item.materialName === rcjData.materialName
                && item.specification === rcjData.specification
                && item.unit === rcjData.unit
                && item.dePrice === rcjData.dePrice
                && item.markSum === rcjData.markSum
                && item.marketPrice === rcjData.marketPrice
        });

        if (rcjs.length > 0) {
            //修改市场价、价格来源等
            for (let j = 0; j < rcjs.length; j++) {
                //rcjs[j].marketPriceBeforeLoading = null;
                let constructRcjArrayElement =rcjs[j];



                if (!isStage){
                    if (unitProject.originalFlag){
                        constructRcjArrayElement.jieSuanBasePrice =constructRcjArrayElement.jieSuanMarketPrice
                        //合同不含税基期价
                        constructRcjArrayElement.jieSuanPriceBaseJournal = constructRcjArrayElement.priceBaseJournal;
                        //合同含税基期价
                        constructRcjArrayElement.jieSuanPriceBaseJournalTax = constructRcjArrayElement.priceBaseJournalTax;
                        //合同不含税市场价
                        constructRcjArrayElement.priceMarket = constructRcjArrayElement.jieSuanPriceMarket;
                        //合同含税市场价
                        constructRcjArrayElement.priceMarketTax = constructRcjArrayElement.jieSuanPriceMarketTax;
                        //结算备份市场价 /合同/确认单价
                        constructRcjArrayElement.marketPrice = constructRcjArrayElement.jieSuanMarketPrice;
                        //税率
                        constructRcjArrayElement.taxRate = constructRcjArrayElement.jieSuantaxRate;
                        constructRcjArrayElement.jieSuanPriceSource = null;
                        constructRcjArrayElement.jieSuanBasePriceSource = null;
                        constructRcjArrayElement.jieSuanBasePriceSource = null;
                    }else {
                        //合同不含税市场价
                        constructRcjArrayElement.priceMarket = constructRcjArrayElement.jieSuanPriceMarket;
                        //合同含税市场价
                        constructRcjArrayElement.priceMarketTax = constructRcjArrayElement.jieSuanPriceMarketTax;
                        //结算备份市场价 /合同/确认单价
                        constructRcjArrayElement.marketPrice = constructRcjArrayElement.jieSuanDePrice;
                        constructRcjArrayElement.jieSuanPriceSource = null;
                        constructRcjArrayElement.jieSuanBasePriceSource = null;
                        constructRcjArrayElement.sourcePrice = null;
                    }


                }else {
                    let item = constructRcjArrayElement.jieSuanRcjDifferenceTypeList[fqNum-1];
                    if (unitProject.originalFlag){
                        //合同不含税市场价
                        constructRcjArrayElement.priceMarket = constructRcjArrayElement.jieSuanPriceMarket;
                        //合同含税市场价
                        constructRcjArrayElement.priceMarketTax = constructRcjArrayElement.jieSuanPriceMarketTax;
                        //结算备份市场价 /合同/确认单价
                        constructRcjArrayElement.marketPrice = constructRcjArrayElement.jieSuanMarketPrice;
                        //税率
                        constructRcjArrayElement.taxRate = constructRcjArrayElement.jieSuantaxRate;
                        item.jieSuanPriceSource = null;
                        constructRcjArrayElement.jieSuanBasePriceSource = null;
                    }else {
                        //合同不含税市场价
                        constructRcjArrayElement.priceMarket = constructRcjArrayElement.jieSuanPriceMarket;
                        //合同含税市场价
                        constructRcjArrayElement.priceMarketTax = constructRcjArrayElement.jieSuanPriceMarketTax;
                        //结算备份市场价 /合同/确认单价
                        constructRcjArrayElement.marketPrice = constructRcjArrayElement.jieSuanDePrice;
                        item.jieSuanPriceSource = null;
                        constructRcjArrayElement.jieSuanBasePriceSource = null;
                        constructRcjArrayElement.sourcePrice = null;
                    }
                }
                rcjs[j].highlight = false;
                rcjs[j].marketPriceBeforeLoading = null;
            }

        }
        if (rcjDetails.length > 0) { //如果是二次解析材料  rcjDatas[i] 为人材机配比明细
            for (let j = 0; j < rcjDetails.length; j++) {

                //rcjDetails[j].marketPriceBeforeLoading = null;
                let constructRcjArrayElement =rcjDetails[j];
                if (!isStage){
                    if (unitProject.originalFlag){
                        constructRcjArrayElement.jieSuanBasePrice =constructRcjArrayElement.jieSuanMarketPrice
                        //合同不含税基期价
                        constructRcjArrayElement.jieSuanPriceBaseJournal = constructRcjArrayElement.priceBaseJournal;
                        //合同含税基期价
                        constructRcjArrayElement.jieSuanPriceBaseJournalTax = constructRcjArrayElement.priceBaseJournalTax;
                        //合同不含税市场价
                        constructRcjArrayElement.priceMarket = constructRcjArrayElement.jieSuanPriceMarket;
                        //合同含税市场价
                        constructRcjArrayElement.priceMarketTax = constructRcjArrayElement.jieSuanPriceMarketTax;
                        //结算备份市场价 /合同/确认单价
                        constructRcjArrayElement.marketPrice = constructRcjArrayElement.jieSuanMarketPrice;
                        //税率
                        constructRcjArrayElement.taxRate = constructRcjArrayElement.jieSuantaxRate;
                    }else {
                        //合同不含税市场价
                        constructRcjArrayElement.priceMarket = constructRcjArrayElement.jieSuanPriceMarket;
                        //合同含税市场价
                        constructRcjArrayElement.priceMarketTax = constructRcjArrayElement.jieSuanPriceMarketTax;
                        //结算备份市场价 /合同/确认单价
                        constructRcjArrayElement.marketPrice = constructRcjArrayElement.jieSuanDePrice;
                    }


                }else {
                    let item = constructRcjArrayElement.jieSuanRcjDifferenceTypeList[fqNum-1];
                    if (unitProject.originalFlag){
                        //合同不含税市场价
                        constructRcjArrayElement.priceMarket = constructRcjArrayElement.jieSuanPriceMarket;
                        //合同含税市场价
                        constructRcjArrayElement.priceMarketTax = constructRcjArrayElement.jieSuanPriceMarketTax;
                        //结算备份市场价 /合同/确认单价
                        constructRcjArrayElement.marketPrice = constructRcjArrayElement.jieSuanMarketPrice;
                        //税率
                        constructRcjArrayElement.taxRate = constructRcjArrayElement.jieSuantaxRate;
                    }else {
                        //合同不含税市场价
                        constructRcjArrayElement.priceMarket = constructRcjArrayElement.jieSuanPriceMarket;
                        //合同含税市场价
                        constructRcjArrayElement.priceMarketTax = constructRcjArrayElement.jieSuanPriceMarketTax;
                        //结算备份市场价 /合同/确认单价
                        constructRcjArrayElement.marketPrice = constructRcjArrayElement.jieSuanDePrice;
                    }
                }
                constructRcjArrayElement.highlight = false;
                rcjDetails[j].marketPriceBeforeLoading = null;
            }
        }

    }

    /**
     * 批量载价前的人材机数据查询
     * @param type
     * @param kind
     * @param constructId
     * @param singleId
     * @param unitId
     * @param isCheck 是否勾选批量调整所有价格
     *         //priceType 1 结算价  2 基期价
     */
    async loadPriceRcjData(args, isCheck) {
        let{type,constructId, singleId, unitId,priceType}=args;
        // let rcjData = this.service.loadPriceSetService.queryConstructRcjByDeIdNew(type, 0, constructId, singleId, unitId);
        let rcjData;
        if(type==3){
            rcjData= await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        }else {
            args.levelType=type;
            rcjData = await this.service.jieSuanProject.jieSuanRcjProcess.projectRcjList(args);
        }

        if (ObjectUtils.isEmpty(rcjData)) {
            return null;
        }
        //除单位为元、单位为%，已锁定市场价、已勾选是否汇总/二次解析的父级材料以外的
        rcjData = rcjData.filter(k => k.unit !== '元' && k.unit !== '%')
            .filter(k => k.supplementDeRcjFlag !== 1)
            .filter(k => (k.markSum == 1 && (k.levelMark != 1 && k.levelMark != 2)) || (k.markSum == 0 && (k.levelMark == 1 || k.levelMark == 2)))

        if (!isCheck) {
            //没勾的时候过滤  加上价格来源为自行询价条件
            //rcjData = rcjData.filter(k => k.sourcePrice !== "自行询价");
            if (priceType == 1){
                rcjData = rcjData.filter(k => ObjectUtils.isEmpty(k.jieSuanPriceSource))
            }
            if (priceType == 2){
                rcjData = rcjData.filter(k => ObjectUtils.isEmpty(k.jieSuanBasePriceSource));
            }

        }
        return rcjData;
    }

    /**
     * 查询人材机汇总 载价查询专用版
     * @param type 数据类型 1 工程项目层级  2 单项  3 单位层级
     * @param kind 人材机类型 0所有人才机  1：人工；2：材料；3：机械；4：设备；5：主材； 6 预拌混凝土 7 主要材料.设备
     * @param constructId 工程项目id
     * @param singleId 单项工程id
     * @param unitId 单位工程id
     */
    async queryConstructRcjByDeIdNew(args) {
        if(ObjectUtils.isEmpty(args.kind)){
            args.kind=0;
        }
        let rcjQuery;
        if(args.type==3){
            rcjQuery=await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        }else {
            args.levelType=args.type;
            rcjQuery=await this.service.jieSuanProject.jieSuanRcjProcess.projectRcjList(args);
        }

        return rcjQuery;
    }


    /**
     * 结算中鼠标右键查询人材机关联定额
     * @param args
     * @returns {Promise<void>}
     */
    async jieSuanGetRcjDe(args){
        let {constructId, singleId, unitId,rcj} = args;
        if (ObjectUtils.isEmpty(rcj)){
            return [];
        }
        let result = await this.service.loadPriceSetService.getRcjDe(args);

        if (!ObjectUtils.isEmpty(result)){
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            let itemBillProjects = unit.itemBillProjects.getAllNodes();
            let measureProjectTables = unit.measureProjectTables.getAllNodes();

            for (let resultKey of result) {
                let filter = itemBillProjects.find(i=>i.sequenceNbr ==resultKey.sequenceNbr);
                if (ObjectUtils.isEmpty(filter)){
                    filter = measureProjectTables.find(i=>i.sequenceNbr ==resultKey.sequenceNbr);
                }
                if(!ObjectUtils.isEmpty(filter)){
                    resultKey.jieSuanGcl =filter.quantity;
                }

            }
        }

        return result;

    }


    /**
     * 查询载价报告明细列表
     * @param args
     * @returns {Promise<void>}
     */
    async loadPriceList(args){
        let {constructId, singleId, unitId, kind, num,priceType,priceMethod} = args;
        //type 1 = 工程项目  2 单项工程 3单位工程
        let queryConstructRcjByDeIdNew1 = await this.queryConstructRcjByDeIdNew(args);
        if (ObjectUtils.isEmpty(queryConstructRcjByDeIdNew1)){
            return null;
        }
        let ts = queryConstructRcjByDeIdNew1.filter(i=>!ObjectUtils.isEmpty(i.highlight) && i.highlight );
        //处理价格来源
       this.handleSourcePrice(ts,constructId, singleId, unitId,kind,priceType,num,priceMethod);
        return ts;

    }


    //载价报告页面----处理价格来源
    handleSourcePrice(rcjData,constructId, singleId, unitId,kind,priceType,num,priceMethod){
        if(ObjectUtils.isEmpty(priceType)) priceType=1;
        if(ObjectUtils.isEmpty(kind)) kind=2;
        let is22= PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        let simpleMethod = PricingFileFindUtils.getConstructProjectTaxCalculationMethod(constructId);
        for(const rcj of rcjData){
            // if(is22){
            //     if(priceType==1){
            //         rcj.marketPrice = simpleMethod?rcj.priceMarketTax:rcj.priceMarket;
            //         rcj.sourcePrice = rcj.jieSuanPriceSource;
            //     }else {
            //         rcj.marketPrice = simpleMethod?rcj.priceBaseJournalTax:rcj.priceBaseJournal;
            //         rcj.sourcePrice = rcj.jieSuanBasePriceSource;
            //     }
            // }else {
            //     if(priceType==1){
            //         rcj.marketPrice =rcj.priceMarket;
            //         rcj.sourcePrice = rcj.jieSuanPriceSource;
            //     }else {
            //         rcj.marketPrice = rcj.jieSuanBasePrice;
            //         rcj.sourcePrice = rcj.jieSuanBasePriceSource;
            //     }
            // }
            let queryRcjPriceAndSource1 = this.queryRcjPriceAndSource(rcj,num,simpleMethod,is22,priceType,priceMethod);
            rcj.marketPrice = queryRcjPriceAndSource1.priceLoading;
            rcj.sourcePrice = queryRcjPriceAndSource1.sourcePriceLoading;
        }
        // //处理价格来源
        // if(ObjectUtils.isNotEmpty(rcjData)){
        //     let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //     let rcjAdjustMethodList = JieSuanRcjStageUtils.getRcjAdjustMethodList(unit);
        //     let rcjDifferenceInfos = rcjAdjustMethodList.find(k =>k.kind == kind);
        //     if (ObjectUtil.isNotEmpty(rcjDifferenceInfos)){
        // for(const rcj of rcjData){
        //     if(ObjectUtils.isEmpty(rcj.sourcePrice)){
        //         let find = rcj.jieSuanRcjDifferenceTypeList.find(d=>d.rcjDifferenceType==rcjDifferenceInfos.adjustMethod);
        //         if(ObjectUtils.isNotEmpty(find)){
        //             if(ObjectUtils.isNotEmpty(find.jieSuanDifferencePriceList)){
        //                 if(priceType==1){
        //                     rcj.marketPrice = simpleMethod?rcj.priceMarketTax:rcj.priceMarket;
        //                     rcj.sourcePrice = rcj.jieSuanPriceSource;
        //                 }else {
        //                     rcj.marketPrice = simpleMethod?rcj.priceBaseJournalTax:rcj.priceBaseJournal;
        //                     rcj.sourcePrice = rcj.sourcePrice;
        //                 }
        //             }
        //         }
        //     }
        // }
        //     }
        //
        // }
    }

    // handleLoadPriceSourcePrice(rcj,constructId, singleId, unitId,kind,priceType,num){
    //     if(ObjectUtils.isEmpty(priceType)) priceType=1;
    //     if(ObjectUtils.isEmpty(num)) num=1;
    //     if(ObjectUtils.isEmpty(kind)) kind=2;
    //     let loadRcj={};
    //
    //     if(priceType==1){
    //         loadRcj.loadPrice=rcj.marketPrice;
    //         loadRcj.sourcePrice=rcj.jieSuanPriceSource;
    //     }
    //     if(priceType==2){
    //         loadRcj.loadPrice=rcj.jieSuanBasePrice;
    //         loadRcj.sourcePrice=rcj.sourcePrice;
    //     }
    //
    //     return loadRcj;
    //
    //     // //处理价格来源
    //     // let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    //     // let rcjAdjustMethodList = JieSuanRcjStageUtils.getRcjAdjustMethodList(unit);
    //     // let rcjDifferenceInfos = rcjAdjustMethodList.find(k =>k.kind == kind);
    //     // if (ObjectUtil.isNotEmpty(rcjDifferenceInfos)){
    //     //     let find = rcj.jieSuanRcjDifferenceTypeList.find(d=>d.rcjDifferenceType==rcjDifferenceInfos.adjustMethod);
    //     //     if(ObjectUtils.isNotEmpty(find)){
    //     //         if(ObjectUtils.isNotEmpty(find.jieSuanDifferencePriceList)){
    //     //             if(priceType==1){
    //     //                 loadRcj.loadPrice=find.jieSuanDifferencePriceList[num-1].jieSuanPrice;
    //     //                 loadRcj.sourcePrice=find.jieSuanDifferencePriceList[num-1].jieSuanPriceSource;
    //     //             }
    //     //             if(priceType==2){
    //     //                 loadRcj.loadPrice=find.jieSuanDifferencePriceList[num-1].jieSuanBasePrice;
    //     //                 loadRcj.sourcePrice=find.jieSuanDifferencePriceList[num-1].jieSuanBasePriceSource;
    //     //             }
    //     //         }
    //     //     }
    //     //
    //     // }
    //
    // }



    /**
     * 查询载价报告 -扇形图
     * @param args
     * @returns {Promise<void>}
     */
    async queryLoadPriceReportRcj(args){
        //type 1 = 工程项目  2 单位工程
        let {constructId, singleId,unitId,type} = args;
        let rcjList =await this.queryConstructRcjByDeIdNew(args);

        if (ObjectUtils.isEmpty(rcjList)){
            return null;
        }

        let result = rcjList.reduce((acc, item) => {
            const key = item.type;
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(item);
            return acc;
        }, {});

        let array = new Array();
        for (const type of Object.keys(result)) {
            let loadPriceComparison = new LoadPriceComparison();
            loadPriceComparison.type = type;
            let total = 0;
            for (const item of result[type]) {
                total = NumberUtil.add(total, parseFloat(item.total));
            }
            loadPriceComparison.total = total;
            array.push(loadPriceComparison);
        }

        return array;

    }

    /**
     * 查询载价报告 -柱状图
     * @param args
     * @returns {Promise<void>}
     */
    async queryLoadPriceReportTarget(args){
        //type 1 = 工程项目  2 单项工程 3单位工程
        let {constructId, singleId,unitId,type} = args;
        let rcjList =await this.queryConstructRcjByDeIdNew(args);

        if (ObjectUtils.isEmpty(rcjList)){
            return null;
        }

        let result = rcjList.reduce((acc, item) => {
            const key = item.type;
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(item);
            return acc;
        }, {});

        let array = new Array();
        for (const type of Object.keys(result)) {
            let loadPriceComparison = new LoadPriceComparison();
            loadPriceComparison.type = type;
            let beforePrice = 0;
            let afterPrice = 0;
            for (const item of result[type]) {
                if (!ObjectUtils.isEmpty(item.highlight) && item.highlight==true){
                    //进行过载价
                    afterPrice = NumberUtil.add(afterPrice ,NumberUtil.numberScale2(NumberUtil.multiply(item.marketPrice,parseFloat(item.totalNumber))));
                    beforePrice = NumberUtil.add( beforePrice ,NumberUtil.numberScale2(NumberUtil.multiply(item.marketPriceBeforeLoading,parseFloat(item.totalNumber))));

                }else {
                    //没有进行过载价
                    afterPrice = NumberUtil.add(afterPrice ,NumberUtil.numberScale2(NumberUtil.multiply(item.marketPrice,parseFloat(item.totalNumber))));
                    beforePrice =NumberUtil.add(beforePrice ,NumberUtil.numberScale2(NumberUtil.multiply(item.marketPrice,parseFloat(item.totalNumber))));
                }

            }
            loadPriceComparison.beforePrice = beforePrice;
            loadPriceComparison.afterPrice = afterPrice;
            array.push(loadPriceComparison);
        }

        return array;

    }


}

JieSuanLoadPriceSetService.toString = () => '[class JieSuanLoadPriceSetService]';
module.exports = JieSuanLoadPriceSetService;
