<template>
  <commonModal
    className="dialog-comm"
    :width="870"
    v-model:modelValue="show"
    title="锁定材料"
    destroy-on-close
    @close="close"
    @cancel="close"
  >
    <div class="opt-head">
      <div class="opt-list">
        人材机分类
        <a-select
          v-model:value="queryParams.type"
          style="width: 140px; margin-left: 6px"
          @change="handleTypeChange"
        >
          <a-select-option :value="item.key" v-for="item of rcjTypeList">{{
            item.name
          }}</a-select-option>
        </a-select>
        <a-button
          :disabled="!gridOptions.data?.length"
          @click="checkboxHandle(true)"
          >全部选择</a-button
        >
        <a-button
          :disabled="!gridOptions.data?.length"
          @click="checkboxHandle(false)"
          >全不选择</a-button
        >
      </div>
      <div class="filter">
        <a-input
          placeholder="请输入名称"
          v-model:value="queryParams.keyword"
          style="width: 138px"
          @keyup.enter="rcjListFilter"
        ></a-input>
        <a-button @click="rcjListFilter">过滤</a-button>
      </div>
    </div>
    <vxe-grid v-bind="gridOptions" v-on="gridEvents">
      <template #ifDonorMaterial="{ column, row }">
        {{ getDonorMaterialText(row.ifDonorMaterial) }}</template
      >
      <template #totalNumber_default="{ column, row }">
        {{
          decimalFormat(row.totalNumber, 'RCJ_COLLECT_TOTALNUMBER_PATH')
        }}</template
      >

      <template #marketPrice="{ column, row }">
        {{
          isChangeAva(row)
            ? '-'
            : decimalFormat(row.marketPrice, 'RCJ_COLLECT_MARKETPRICE_PATH')
        }}</template
      >
      <template #marketTaxPrice="{ column, row }">
        {{
          isChangeAva(row)
            ? '-'
            : decimalFormat(
                row.marketTaxPrice,
                'RCJ_COLLECT_MARKETTAXPRICE_PATH'
              )
        }}</template
      >
    </vxe-grid>
    <div class="footer-btn-list">
      <a-button type="primary" @click="submit">确定</a-button>
      <a-button type="primary" ghost @click="close">取消</a-button>
    </div>
  </commonModal>
</template>

<script setup>
import { ref, computed, watch, reactive, toRaw } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { useDecimalPoint } from '@gongLiaoJi/hooks/useDecimalPoint.js';
const { decimalFormat, getDecimalPlaces } = useDecimalPoint();

const projectStore = projectDetailStore();
const props = defineProps({
  visible: Boolean,
  tableList: {
    type: Array,
    default: () => [],
  },
});
const donorMaterialList = [
  {
    label: '自行采购',
    value: 0,
  },
  {
    label: '甲方供应',
    value: 1,
  },
  {
    label: '甲定乙供',
    value: 2,
  },
];
const getDonorMaterialText = value => {
  const item = donorMaterialList.find(item => item.value === value);
  return item ? item.label : '';
};
const emit = defineEmits(['update:visible', 'callback']);
const updateColumns = () => {
  const lastColumn = gridOptions.columns[gridOptions.columns.length - 1];
  gridOptions.columns = [
    ...gridOptions.columns.slice(0, 8),
    {
      field: 'marketPrice',
      title: '不含税市场价',
      slots: {
        default: 'marketPrice',
      },
    },
    {
      field: 'marketTaxPrice',
      title: '含税市场价',
      slots: {
        default: 'marketTaxPrice',
      },
    },
    lastColumn,
  ];
};
const isDeType = (type, rowType = '') => {
  // debugger;
  if (rowType) {
    return rowType === type;
  }
  return projectStore.deType === type;
};
const getValueByDeType = (deType, row, field = '') => {
  if (isDeType(deType, row.deStandardReleaseYear)) {
    if (['marketTaxPrice', 'marketPrice'].includes(field)) {
      const taxMade = 'marketPrice' === field ? 1 : 0;
      if (Number(projectStore.taxMade) === taxMade) {
        return isChangeAva(row) ? '-' : row.marketPrice;
      }
    }
    return '/';
  }
  return isChangeAva(row) ? '-' : row[field];
};
// 基期价、市场价为“-
const isChangeAva = row => {
  return Number(row.isDataTaxRate) == 0;
};
const show = computed({
  get: () => {
    if (props.visible) {
      updateColumns();
    }
    return props.visible;
  },
  set: val => {
    emit('update:visible', val);
  },
});
const submit = () => {
  show.value = false;
  emit('callback', {
    type: 'ok',
    data: filterDataList.value,
  });
  queryParams.value = { type: 0, keyword: '' };
};
const close = () => {
  gridOptions.data = JSON.parse(JSON.stringify(originalData.value));
  show.value = false;
  emit('callback', { type: 'cancel' });
  queryParams.value = { type: 0, keyword: '' };
};
let queryParams = ref({
  type: 0,
  keyword: '',
});
const rcjTypeList = ref([
  { key: 0, name: '所有人材机' },
  { key: 1, name: '人工' },
  { key: 2, name: '材料' },
  { key: 3, name: '机械' },
  { key: 4, name: '设备' },
  { key: 5, name: '主材' },
]);
const gridEvents = reactive({
  checkboxChange({ checked, rowIndex }) {
    filterDataList.value[rowIndex].excludedAdjust = checked;
  },
});
const gridOptions = reactive({
  border: 'full',
  height: 435,
  headerAlign: 'center',
  columnConfig: {
    resizable: true,
  },
  checkboxConfig: {
    showHeader: false,
    checkField: 'excludedAdjust',
    checkMethod: ({ row }) => {
      return !row.disabled;
    },
  },
  scrollY: {
    enabled: true,
    gt: 30,
  },
  columns: [
    { type: 'seq', title: ' ', width: 50 },
    { field: 'materialCode', title: '编码', width: 80 },
    { field: 'type', title: '类别', width: 60 },
    { field: 'materialName', title: '名称', align: 'left' },
    { field: 'specification', title: '规格型号' },
    {
      field: 'ifDonorMaterial',
      title: '供货方式',
      slots: { default: 'ifDonorMaterial' },
    },
    { field: 'unit', title: '单位' },
    {
      field: 'totalNumber',
      title: '数量',
      slots: { default: 'totalNumber_default' },
    },
    { type: 'checkbox', title: '锁定', width: 50, align: 'center' },
  ],
  data: [],
});
let originalData = ref([]);
let filterDataList = ref([]);
const rcjListFilter = () => {
  const kind = queryParams.value.type;
  let keyword = queryParams.value.keyword;
  gridOptions.data = filterDataList.value.filter(item => {
    let isWith = keyword === '' ? true : item.materialName.includes(keyword);
    if (!isWith) return false;
    if (kind === 0) {
      return true;
    }
    if (kind === 2) {
      return [2, 5, 6, 7, 8, 9, 10].includes(item.kind);
    }
    return item.kind === kind;
  });
};
const handleTypeChange = () => {
  rcjListFilter();
};
const checkboxHandle = checked => {
  for (let item of gridOptions.data) {
    if (item.disabled) continue;
    item.excludedAdjust = checked;
    filterDataList.value[
      filterDataList.value.findIndex(it => it.sequenceNbr === item.sequenceNbr)
    ].excludedAdjust = checked;
  }
};
watch(
  () => props.tableList,
  val => {
    originalData.value = JSON.parse(JSON.stringify(val));
    filterDataList.value = JSON.parse(JSON.stringify(val));
    gridOptions.data = JSON.parse(JSON.stringify(val));
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>
<style lang="scss" scoped>
.opt-head {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  .opt-list {
    flex: 1;
  }
  :deep(.ant-btn) {
    margin-left: 10px !important;
  }
}
.footer-btn-list {
  margin-top: 12px;
  display: flex;
  justify-content: center;
  :deep(.ant-btn) {
    margin: 0 5px !important;
  }
}
</style>
