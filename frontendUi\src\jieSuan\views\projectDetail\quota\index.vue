<!--
 * @Descripttion: 报表
 * @Author: sunchen
 * @Date:2023-08-25 11:36:44
 * @LastEditors: sunchen
 * @LastEditTime: 2025-04-23 11:02:39
-->
<template>
  <a-spin
    :spinning="projectStore.globalLoading.loading"
    :tip="projectStore.globalLoading.info"
    wrapperClassName="spin-yyy"
  >
    <operate
      v-if="!useSlots().default"
      @executeCommand="executeCommand"
    />
    <section>
      <main-content
        v-if="projectStore.mainContentRefresh"
        ref="mainRontentRef"
      />
      <footer><PricePanel :priceList="priceList" type="zhibiao"/></footer>
    </section>
  </a-spin>
</template>

<script setup>
import MainContent from './mainContent.vue';
import PricePanel from '@/views/projectDetail/customize/PricePanel.vue';
import { projectDetailStore } from '@/store/projectDetail';
import { useRoute } from 'vue-router';
import operate from './operate.vue';
import csProject from '@/api/csProject';
import { ref, reactive, watchEffect, onMounted, onBeforeUnmount, useSlots } from 'vue';
import { message } from 'ant-design-vue';
const projectStore = projectDetailStore();
const route = useRoute();
console.log('🚀 ~ route.query?.type:', route.query?.type);
projectStore.SET_TYPE(route.query?.type || 'ys');
let priceList = ref([]); //底部数据列表

const mainRontentRef = ref();
watchEffect(() => {
});
onMounted(() => {
  window.addEventListener('keydown', save);
});
onBeforeUnmount(() => {
  window.removeEventListener('keydown', save);
});
const clearTimers = () => {
  clearInterval(timer.value);
  timer.value = null;
};
let constructReadOnlyRef = ref();
const save = event => {
  if (event.ctrlKey && event.code == 'KeyS') {
    if (constructReadOnlyRef.value?.readOnlyTip()) return;
    csProject.saveYsfFile(route.query.constructSequenceNbr).then(res => {
      if (res?.result) {
        message.success('保存成功');
      }
    });
  }
};
const executeCommand = item => {
  const child = mainRontentRef.value.childComponentRef;
  if (child && typeof child[item.provideFun] === 'function') {
    child[item.provideFun]();
  }
};
</script>
<style lang="scss" scoped>
section {
  display: flex;
  flex-wrap: wrap;
  height: calc(100vh - 115px);
}
footer {
  width: 100%;
  height: 33px;
  background-color: #d9e1ef;
}
aside {
  // width: 225px;
  width: 100%;
  height: calc(100% - 33px);
  border-right: 2px solid #dcdfe6;
  background: #f8fbff;
  text-align: left;
}
main {
  flex: 1;
  // width: calc(100% - 225px);
}
</style>
