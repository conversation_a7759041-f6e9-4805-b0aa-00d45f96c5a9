/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2024-02-02 10:35:04
 * @LastEditors: sunchen
 * @LastEditTime: 2024-03-28 10:21:42
 */
import infoMode from '@/plugins/infoMode';
import detailApi from '@/api/projectDetail.js';
import { projectDetailStore } from '@/store/projectDetail';
import { useVirtualList } from '@gongLiaoJi/hooks/useVirtualList';
import { message } from 'ant-design-vue';
import { ref, nextTick } from 'vue';
import {
  quantityExpressionHandler,
  removeSpecialCharsFromPrice,
  everyNumericHandler,
} from '@/utils/index';
import xeUtils from 'xe-utils';

// vexTable.value: 表格ref
// codeField：编码字段名
// nameField: 名称字段名
// operateList: 操作列表
// resetCellData 重置当前单元格方法

export const useSubItem = ({
  operateList,
  vexTable,
  codeField = 'bdCode',
  nameField = 'bdName',
  frameSelectRef = null,
  pageType,
  resetCellData = () => {},
  checkUnit = () => {},
  emits = () => {},
  api = {
    updateData: detailApi.updateFbData,
    getList: detailApi.queryBranchDataByFbIdV1,
  },
}) => {
  const isFBFX = codeField === 'bdCode'; // 是否预算书
  const projectStore = projectDetailStore();
  let currentInfo = ref();
  let feeFileList = ref([]);
  let tableData = ref([]);
  let originalTableData = ref([]); // 表格原始数据
  let loading = ref(false);
  let page = ref(1);
  let limit = ref(300000);
  let lockFlag = ref(0); // 整体锁定状态 0 不锁定 1 锁定
  let addDataSequenceNbr = ref('');
  let isIndexAddInfo = ref(false); // 是否从索引页面添加数据

  //多单位------
  let addCurrentInfo = ref(); // 多单位选择时选中的清单数据
  let showUnitTooltip = ref(false); // 是否多单位选择
  let selectUnit = ref(); // 多单位时选择单位
  //end----------
  // 提示信息框------
  let infoVisible = ref(false); // 提示信息框是否显示
  let infoText = ref('工程量明细已被调用，是否清空工程量明细？'); // 提示信息框的展示文本
  let iconType = ref(''); // 提示信息框的图标
  let isSureModal = ref(false); // 提示信息框是否为确认提示框
  //end------
  let isUpdateQuantities = ref(false); // 是否更新工程量明细数据
  let isUpdateFile = ref(false);
  const indexVisible = ref(false);
  // 编辑弹框相关
  let editKey = ref(''); // 单独编辑弹框记录得当前编辑字段
  let isShowModel = ref(false);

  let qdVisible = ref(false);
  let deVisible = ref(false);
  let rcjVisible = ref(false);
  let bdCode = ref('');
  let isSortQdCode = ref(false); // 编码重复是否继续
  let isClearEdit = ref(false); // 是否手动清除编辑状态
  let ishasRCJList = ref(false); //人材机定额且有人材机明细数据---不可编辑单价

  let mainMaterialTableData = ref([]); // 定额数据下挂的主材数据,如若为空,则不展示设置主材价格弹框
  let materialVisible = ref(false); // 是否设置主材市场价弹框
  let DJGCrefreshFeeFile = ref(false); //单价构成需要刷新取费文件列表
  const {
    initVirtual,
    getScroll,
    renderedList,
    init,
    EnterType,
    onDragHeight,
    scrollToPosition,
  } = useVirtualList();

  /**
   * 首字母大写 originalBdCode\originalFxCode
   */
  const originalCode = `original${codeField
    .charAt(0)
    .toUpperCase()}${codeField.slice(1)}`;
  const originalName =
    nameField === 'name'
      ? 'originalFxName'
      : `original${nameField.charAt(0).toUpperCase()}${nameField.slice(1)}`;

  /**
   * 编辑完成之后
   * @param {*} param0
   * @returns
   */
  const editClosedEvent = ({ row, column }) => {
    console.log('表格ref', vexTable.value);
    if (isClearEdit.value) return;
    let field = column.field;
    const codeValue = row[codeField]; // bdCode,fxCode
    // 判断单元格值是否被修改
    if (!vexTable.value.isUpdateByRow(row, field)) return;
    if (field === 'quantity') {
      field = 'quantityExpression';
    }
    row.quantityExpression =
      column.field === 'quantity' ? row.quantity : row.quantityExpression;
    row.quantity = row.originalQuantity;
    if (field === codeField && !row[codeField]) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-qiangtixing',
        infoText: '编码不可为空',
        confirm: () => {
          infoMode.hide();
          currentInfo.value[codeField] = currentInfo.value[originalCode];
        },
      });
      return;
    }

    nextTick(() => {
      // 如果索引弹窗出现，则不出现补充弹窗
      if ([codeField].includes(column.field) && indexVisible.value) return;

      costMajorNameEditEvent(field, row);
      if (row.kind === '03') {
        isStandQd(field, codeValue);
      } else if (row.kind === '04') {
        if (row.rcjFlag === 1) {
          isRcjCodeMainQuotaLibrary(field, codeValue);
        } else {
          isMainQuotaLibraryCode(field, codeValue);
        }
      }
      expressionEditEvent(field, row, () => {
        vexTable.value.revertData(row, 'quantityExpression');
        row.quantityExpression = row.originalQuantityExpression;
      });
      zjfPriceEditEvent(field, row);
      if (![codeField, 'quantityExpression', 'zjfPrice'].includes(field)) {
        updateFbData(row, field);
      }
    });
  };

  /**
   * 表达式处理
   * @param {*} field
   * @param {*} row
   * @param {*} revertDataCallback
   * @returns
   */
  const expressionEditEvent = (field, row, revertDataCallback) => {
    console.log('表达式处理', row);
    if (field !== 'quantityExpression') return;
    const expressionArr = row.quantityExpression.match(/[A-Za-z0-9]+(\.\d+)?/g);
    const orgExpressionArr = row.originalQuantityExpression
      .toString()
      ?.match(/[A-Za-z0-9]+(\.\d+)?/g);
    const [isSuccess, msg] = quantityExpressionHandler(row);
    if (isSuccess) {
      revertDataCallback();
      infoVisible.value = true;
      isSureModal.value = true;
      infoText.value = msg;
    } else if (
      !orgExpressionArr?.includes('HSGCL') &&
      expressionArr.includes('HSGCL')
    ) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-querenshanchu',
        infoText: '计算式输入非法，请重新输入标准四则运算表达式或数值',
        confirm: () => {
          infoMode.hide();
          currentInfo.value.quantityExpression =
            currentInfo.value.originalQuantityExpression;
        },
      });
    } else if (
      !expressionArr.includes(row.quantityVariableName) &&
      orgExpressionArr?.includes(row.quantityVariableName)
    ) {
      infoMode.show({
        iconType: 'icon-qiangtixing',
        infoText: '工程量明细已被调用，是否清空工程量明细？',
        confirm: () => {
          updateFbData(currentInfo.value, 'quantityExpression');
          infoMode.hide();
        },
        close: () => {
          infoMode.hide();
          currentInfo.value.quantityExpression =
            currentInfo.value.originalQuantityExpression;
        },
      });
    } else {
      row.quantityExpression = everyNumericHandler(row.quantityExpression);
      updateFbData(row, field);
    }
  };

  /**
   * 判断输入的定额编码是否为主定额库编码
   * @param {*} field
   * @param {*} code
   * @returns
   */
  const isMainQuotaLibraryCode = (field, code) => {
    if (field !== codeField) return;
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    console.log('判断是否为主定额册下的标准定额参数', apiData);
    detailApi.isMainQuotaLibraryCode(apiData).then(res => {
      console.log('判断是否为主定额册下的标准定额', res);
      if (res.status === 200) {
        if (res.result) {
          updateDeReplaceData(code);
        } else {
          isStandardDe(code);
        }
      }
    });
  };

  /**
   * 判断输入的定额编码是否是标准定额
   * @param {*} code
   */
  const isStandardDe = code => {
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    detailApi.isStandardDe(apiData).then(res => {
      if (res.status === 200) {
        if (res.result) {
          updateDeReplaceData(code);
        } else {
          infoMode.show({
            iconType: 'icon-querenshanchu',
            infoText: '标准定额库下未找到该子目，是否补充子目？',
            confirm: () => {
              deVisible.value = true;
              bdCode.value = code;
              infoMode.hide();
            },
            close: () => {
              infoMode.hide();
              currentInfo.value[codeField] = currentInfo.value[originalCode];
            },
          });
        }
        console.log('判断输入的定额编码是否为主定额库编码', res);
      }
    });
  };

  /**
   * 预算书 措施项目 替换定额数据
   * @param {*} code
   */
  const updateDeReplaceData = code => {
    let apiData = {
      pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
      type: 1,
    };
    console.log('通过标准编码插入定额', apiData);
    detailApi.updateDeReplaceData(apiData).then(res => {
      console.log('通过标准编码插入定额结果', res);
      if (res.status === 200 && res.result) {
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        deVisible.value = false;
        message.success('定额替换成功');
        queryBranchDataById();
      }
    });
  };

  /**
   * 判断输入的材料编码是否与主定额库编码相同
   * @param {*} field
   * @param {*} code
   * @returns
   */
  const isRcjCodeMainQuotaLibrary = (field, code) => {
    if (field !== codeField) return;
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    detailApi.isRcjCodeMainQuotaLibrary(apiData).then(res => {
      if (res.status === 200) {
        if (res.result) {
          // 输入的编码为主定额库编码
          updateBjqRcjReplaceData(code);
        } else {
          isStandardRcj(code);
        }
      }
    });
  };

  /**
   * 预算书 措施项目 替换编辑区的人材机数据
   * @param {*} code
   */
  const updateBjqRcjReplaceData = code => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
      code: code,
      region: 0,
    };
    detailApi.updateBjqRcjReplaceData(apiData).then(res => {
      if (res.status === 200 && res.result) {
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        message.success('人材机替换成功');
        queryBranchDataById();
      }
    });
  };

  /**
   * 判断输入的材料编码是否标准人材机数据
   * @param {*} code
   */
  const isStandardRcj = code => {
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    detailApi.isStandardRcj(apiData).then(res => {
      console.log('=============');
      if (res.status === 200) {
        if (res.result) {
          updateBjqRcjReplaceData(code);
        } else {
          infoMode.show({
            iconType: 'icon-querenshanchu',
            infoText: '标准定额下不存在该材料编码,是否补充人材机？',
            confirm: () => {
              rcjVisible.value = true;
              bdCode.value = code;
              infoMode.hide();
            },
            close: () => {
              currentInfo.value.bdCode = currentInfo.value.originalBdCode;
              infoMode.hide();
            },
          });
        }
      }
    });
  };

  /**
   * 判断是否是标准清单
   * @param {*} field
   * @param {*} code
   * @returns
   */
  const isStandQd = (field, code) => {
    if (field !== codeField) return;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      code: code,
    };
    detailApi.isStandQd(apiData).then(res => {
      console.log('判断是否是标准清单', res);
      if (res.status === 200) {
        if (res.result) {
          const unit = res.result.unit;
          const unitArr = Array.isArray(unit) ? unit : unit?.split('/');
          res.result.unit = unitArr;
          addCurrentInfo.value = res.result;
          addCurrentInfo.value.bdCodeLevel04 = code;
          if (code.length === 9) {
            if (unitArr && unitArr.length > 1) {
              showUnitTooltip.value = true;
            } else {
              updateQdByCode(code, unitArr[0]);
            }
          } else {
            isQdCodeExist(code, res.result);
          }
        } else {
          isQdCodeExist(code, res.result);
        }
      }
    });
  };

  /**
   * 判断清单编码是否存在
   * @param {*} code
   * @param {*} obj
   */
  const isQdCodeExist = (code, obj) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      code: code,
    };
    detailApi.isQdCodeExist(apiData).then(res => {
      console.log('判断清单编码是否存在', res, obj);
      // if (res.status === 200) {
      if (res) {
        // 若存在,则弹框提示是否继续
        infoMode.show({
          iconType: 'icon-querenshanchu',
          infoText: obj ? '' : '是否补充清单？',
          descText: obj
            ? '当前单位工程有相同清单编码，是否自动排序清单编码？'
            : '当前单位工程有相同清单编码，是否继续?',
          confirm: () => {
            if (!obj) {
              bdCode.value = code;
              qdVisible.value = true;
              isSortQdCode.value = false;
            } else {
              isSortQdCode.value = true;
              if (obj.unit && obj.unit.length > 1) {
                showUnitTooltip.value = true;
              } else {
                updateQdByCode(code, obj.unit ? obj.unit[0] : null);
              }
            }
            infoMode.hide();
          },
          close: () => {
            infoMode.hide();
            if (obj) {
              isSortQdCode.value = false;
              if (obj.unit && obj.unit.length > 1) {
                showUnitTooltip.value = true;
              } else {
                updateQdByCode(code, obj.unit ? obj.unit[0] : null);
              }
            } else {
              currentInfo.value[codeField] = currentInfo.value[originalCode];
            }
          },
        });
      } else {
        // 根据是否为标准数据判断替换或补充
        if (!obj) {
          bdCode.value = code;
          qdVisible.value = true;
        } else {
          if (obj.unit && obj.unit.length > 1) {
            showUnitTooltip.value = true;
          } else {
            updateQdByCode(code, obj.unit ? obj.unit[0] : null);
          }
        }
      }
      // }
    });
  };

  /**
   * 通过标准编码插入清单
   * @param {*} code
   * @param {*} unit
   */
  const updateQdByCode = (code, unit) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
      code: code,
      unit: unit,
      isSortQdCode: isSortQdCode.value,
    };
    console.log('==============标准清单编码插入api参数', apiData);
    detailApi.updateQdByCode(apiData).then(res => {
      console.log('标准清单编码插入', res);
      if (res.status === 200 && res.result) {
        selectUnit.value = '';
        message.success('清单插入成功');
        queryBranchDataById();
      }
    });
  };

  const costMajorNameEditEvent = (field, row) => {
    if (field !== 'costMajorName') return;
    row.costFileCode = feeFileList.value.filter(
      x => x.qfName === row.costMajorName
    )[0].qfCode;
    isUpdateFile.value = false;
  };
  const zjfPriceEditEvent = (field, row) => {
    if (field !== 'zjfPrice') return;
    row.zjfPrice = Math.round(row.zjfPrice * 100) / 100;
    if (Number(row.zjfPrice) === 0) {
      infoVisible.value = true;
      isSureModal.value = true;
      infoText.value = '定额单价不能为0';
      iconType.value = 'icon-qiangtixing';
      row.zjfPrice = row.originalZjfPrice;
      return;
    }
    updateFbData(row, field);
  };

  /**
   * 更新数据
   * @param {*} row
   * @param {*} field
   */
  const updateFbData = (row, field) => {
    isUpdateFile.value = true;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitWorkId: projectStore.currentTreeInfo?.id,
      pointLineId: row.sequenceNbr,
      upDateInfo: {
        [nameField]: row[nameField],
        projectAttr: row.projectAttr,
        unit: row.unit,
        costFile: {
          code: row.costFileCode,
          name: row.costMajorName,
        },
        itemCategory: row.itemCategory,
        measureType: row.measureType,
        description: row.description,
        quantityExpression: row.quantityExpression,
        zjfPrice: row.zjfPrice,
        index: row.index,
      },
    };
    console.log('关联数据编辑', row, field);
    console.log('数据更新参数', apiData);
    api.updateData(apiData).then(res => {
      console.log('数据更新结果', res);
      if (res.status === 200 && res.result) {
        if (res.result.enableUpdatePrice) {
          if (field !== 'seq') {
            message.success('修改成功');
            if (editKey.value) {
              isShowModel.value = false;
            }
            if (infoVisible.value) {
              isUpdateQuantities.value = true;
              infoVisible.value = false;
            }
          }
          if (
            (row.kind === '01' || row.kind === '02' || row.kind === '0') &&
            field === nameField
          ) {
            emits('updateMenuList');
          }
          if (
            field === 'costMajorName' ||
            field === 'zjfPrice' ||
            field === 'quantityExpression' ||
            field === 'quantity'
          ) {
            isUpdateFile.value = true;
          }
          addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
          queryBranchDataById();
        } else {
          infoMode.show({
            isSureModal: true,
            iconType: 'icon-qiangtixing',
            infoText: '调整后存在人工/材料/机械单价为0，请重新输入',
            confirm: () => {
              infoMode.hide();
              currentInfo.value.zjfPrice = currentInfo.value.originalZjfPrice;
            },
          });
        }
      }
    });
  };

  const addLevelToTree = (data, parentLevel = 0) => {
    return data.map(node => ({
      ...node,
      customLevel: parentLevel + 1,
      children:
        (node.children || []).length > 0
          ? addLevelToTree(node.children, parentLevel + 1)
          : [],
    }));
  };

  /**
   * 获取列表信息
   * @param {*} EnterType //other 从其他页面需要初始化数据 ，Refresh, 修改了刷新数据
   */
  const queryBranchDataById = (EnterTypes = 'Refresh', posId = '') => {
    if (isFBFX && !projectStore.asideMenuCurrentInfo?.sequenceNbr) return;
    if (!isFBFX && (!projectStore.currentTreeInfo?.id || loading.value)) return;
    checkUnit();
    loading.value = true;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      sequenceNbr: projectStore.asideMenuCurrentInfo?.sequenceNbr,
      pageSize: limit.value,
      pageNum: page.value,
      isAllFlag: !!posId,
    };
    console.log('apiData', apiData, api.getList);

    api.getList(apiData).then(res => {
      console.log('queryBranchDataById', res);
      if (res.status === 200) {
        if (!res.result) {
          loading.value = false;
          tableData.value = [];
          return;
        }
        let testTreeData = xeUtils.toArrayTree(res.result.data, {
          key: 'sequenceNbr',
          parentKey: 'parentId',
        });
        res.result.data = xeUtils.toTreeArray(addLevelToTree(testTreeData));
        changeListHandler(res.result.data);
        tableData.value = res.result.data;
        console.log('🚀 获取的数据 :', tableData.value);
        originalTableData.value = JSON.parse(JSON.stringify(tableData.value));

        virtualListHandler(EnterTypes, posId);
        nextTick(() => {
          if (projectStore.combinedSearchList?.length > 0) {
            filterData(projectStore.combinedSearchList);
          }
        });
        console.log('🚀 ~ nextTick ~ currentInfo.value:', currentInfo.value);

        frameSelectRef?.clearSelect();
        lockFlagHandler();
        addDataHandler(posId);
        loading.value = false;
        emits('getCurrentInfo', currentInfo.value);
        DJGCrefreshFeeFile.value ? queryFeeFileData(true) : ''; //刷新当前行重新获取取费文件列表
        setTimeout(() => {
          addDataSequenceNbr.value = '';
          isIndexAddInfo.value = false;
          addCurrentInfo.value = null;
        }, 500);
        console.log('==========tableData', tableData.value);
      }
    });
  };

  /**
   * 虚拟滚动处理
   * @param {*} type
   * @param {*} posId
   */
  const virtualListHandler = (type, posId) => {
    EnterType.value = type;
    const initList = init(tableData.value);
    setTimeout(() => {
      initList();
      if (posId) {
        console.log('🚀反向定位 ~ nextTick ~ posId:', posId);
        scrollToPosition(posId, tableData.value);
        currentInfo.value = tableData.value.find(
          item => item.sequenceNbr === posId
        );
        vexTable.value.setCurrentRow(currentInfo.value);
        projectStore.isAutoPosition = false;
      }
    }, 10);
  };

  /**
   * 插入数据逻辑处理
   * @returns
   */
  const addDataHandler = posId => {
    if (addDataSequenceNbr.value) {
      tableData.value.forEach(item => {
        if (!isIndexAddInfo.value) {
          if (item.sequenceNbr === addDataSequenceNbr.value) {
            currentInfo.value = item;
            vexTable.value.setCurrentRow(item);
            vexTable.value.scrollToRow(item);
          }
        } else if (item.sequenceNbr === currentInfo.value.sequenceNbr) {
          currentInfo.value = item;
        }
      });
      nextTick(() => {
        frameSelectRef?.clearSelect();
        vexTable.value.setCurrentRow(currentInfo.value);
        resetCellData();
        console.log('nextTick', currentInfo.value);
      });
    } else if (!isIndexAddInfo.value) {
      if (posId) return;
      let hasCurrentInfo = true; // 有点击选中的数据
      if (!currentInfo.value?.sequenceNbr) {
        // 没有点击选中的，则直接替换成数据第一个
        hasCurrentInfo = false;
      } else {
        // 有选中数据，但是在列表中没有找到
        if (
          tableData.value.every(
            item => item.sequenceNbr !== currentInfo.value.sequenceNbr
          )
        ) {
          hasCurrentInfo = false;
        } else {
          currentInfo.value = tableData.value.find(
            item => item.sequenceNbr === currentInfo.value.sequenceNbr
          );
        }
      }

      let handleCurrentInfo = currentInfo.value; // 重新赋值，为了人才机等子页面能监听到变化掉借口
      if (!hasCurrentInfo) {
        // 没有选中的数据，默认第一个选中，并清除所有数据
        handleCurrentInfo = tableData.value[0];
      }
      currentInfo.value = '';
      nextTick(() => {
        currentInfo.value = handleCurrentInfo;
        vexTable.value.setCurrentRow(currentInfo.value);
      });
    }
  };

  /**
   * 锁定处理
   */
  const lockFlagHandler = () => {
    lockFlag.value = tableData.value
      .filter(data => data.kind === '03')
      .some(item => item.isLocked);
    operateList.value.find(item => item.name === 'lock-subItem').label =
      lockFlag.value ? '整体解锁' : '整体锁定';
  };

  /**
   * 组价方案匹配条件筛选
   * @param {*} val
   */
  const filterData = val => {
    let tempList = [];
    tableData.value = [];
    if (val.length === 0 || !val) {
      tableData.value = originalTableData.value;
    } else {
      originalTableData.value.forEach(item => {
        if (val.includes(item.matchStatus)) {
          tempList.push(item.sequenceNbr);
        }
      });
      for (let i = 0; i < originalTableData.value.length; i++) {
        if (
          tempList.includes(originalTableData.value[i].sequenceNbr) ||
          tempList.includes(originalTableData.value[i].parentId)
        ) {
          tableData.value.push(originalTableData.value[i]);
        }
      }
      tableData.value.forEach((item, index) => {
        item.index = (page.value - 1) * limit.value + (index + 1);
      });
    }
    const initList = init(tableData.value);
    nextTick(() => {
      initList();
    });
  };

  /**
   * 对列表原数据做处理，对之前分开处理做合并一块处理，后续需要对数据做循环处理，统一在这里做处理
   * @param {} data
   */
  const changeListHandler = data => {
    for (let i = 0; i < data.length; ++i) {
      let item = data[i];
      if (item.defaultLine) {
        item.measureType = '';
      }
      if (item.appendType && item.appendType.length > 0) {
        if (item.appendType.includes('换')) {
          item.changeFlag = '换';
        }
        if (item.appendType.includes('借')) {
          item.borrowFlag = '借';
        }
      }
      item.index = (page.value - 1) * limit.value + (i + 1);
      item[originalCode] = item[codeField];
      item[originalName] = item[nameField];
      item.originalQuantityExpression = item.quantityExpression;
      item.originalQuantity = item.quantity;
      item.originalZjfPrice = item.zjfPrice;
    }
  };

  /**
   * 获取所有的取费文件列表
   */
  const queryFeeFileData = (isRefresh = false) => {
    console.log('queryFeeFileData', pageType);
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      deStandard: projectStore.deType,
      type: pageType,
    };
    isRefresh ? (apiData.deId = currentInfo.value?.sequenceNbr) : '';
    console.log('queryFeeFileData', apiData);
    detailApi.queryFeeFileData(apiData).then(res => {
      if (res.status === 200 && res.result) {
        feeFileList.value = res.result;
        DJGCrefreshFeeFile.value = false;
      }
    });
  };

  let addDeInfo = ref(null); // 通过索引页面插入定额数据,用于设置标准换算
  /**
   * 获取人材机明细数据
   * @param {*} bol
   * @param {*} deItem
   * @returns
   */
  const queryRcjDataByDeId = (bol = true, deItem = null) => {
    let apiData = {
      id: bol ? addDeInfo.value?.sequenceNbr : deItem.sequenceNbr,
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      branchType: isFBFX ? 1 : 2,
    };
    if (!apiData.id) return;
    console.log('定额明细', apiData);
    detailApi.queryRcjDataByDeId(apiData).then(res => {
      // console.log('定额明细数据', res);
      if (res.status === 200) {
        if (res.result?.length > 0) {
          ishasRCJList.value = true;
        }
        console.log('定额明细数据', res, ishasRCJList.value);

        if (bol) {
          mainMaterialTableData.value = res.result.filter(
            x => x.kind === 5 && x.marketPrice === 0
          );
          if (mainMaterialTableData.value.length > 0) {
            materialVisible.value = true;
          } else {
            queryRule();
          }
        }
      }
    });
  };
  /**
   * 选中单条预算书数据
   * @param {*} param0
   */
  const currentChangeEvent = ({ row }) => {
    // const $table = vexTable.value;
    // 判断单元格值是否被修改
    // if ($table.isEditByRow(currentInfo.value)) return;
    currentInfo.value = row;
    ishasRCJList.value = false;
    if (row.kind === '04' && row.rcjFlag === 1) {
      queryRcjDataByDeId(false, row);
    }
    projectStore.SET_SUB_CURRENT_INFO(row);
    // emits('getCurrentInfo', currentInfo.value);
  };

  let editContent = ref('');
  // 编辑内容保存事件
  const saveContent = () => {
    const valueType = typeof editContent.value;
    if (valueType !== 'string' || editContent.value.trim() === '') {
      if (editKey.value === 'quantityExpression') {
        infoMode.show({
          isSureModal: true,
          iconType: 'icon-qiangtixing',
          infoText: '请输入工程量表达式',
          confirm: () => {
            infoMode.hide();
          },
        });
      }
      return;
    }
    currentInfo.value[editKey.value] = editContent.value;

    expressionEditEvent(editKey.value, currentInfo.value, () => {
      editContent.value = currentInfo.value.originalQuantityExpression;
      vexTable.value.revertData(currentInfo.value, 'quantityExpression');
      currentInfo.value.quantityExpression =
        currentInfo.value.originalQuantityExpression;
    });
    if (editKey.value !== 'quantityExpression') {
      updateFbData(currentInfo.value, editKey.value);
    }
  };

  let showModelTitle = ref('名称编辑');
  /**
   * 打开编辑弹框方法
   * @param {*} field
   */
  const openEditDialog = field => {
    editKey.value = field;
    switch (field) {
      case nameField:
        showModelTitle.value ='名称编辑';
        break;
      case 'projectAttr':
        showModelTitle.value = '项目特征编辑';
        break;
      case 'quantityExpression':
        showModelTitle.value = '工程量表达式编辑';
        break;
    }
    isShowModel.value = true;
    editContent.value = currentInfo.value[field];
  };

  let standardVisible = ref(false);
  /**
   * 获取定额是否存在标准换算信息
   * @returns
   */
  const queryRule = () => {
    if (!addDeInfo.value?.standardId) {
      return;
    }
    let apiData = {
      standardDeId: addDeInfo.value?.standardId,
      fbFxDeId: addDeInfo.value?.sequenceNbr,
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
    };
    console.log('标准换算列表参数', apiData);
    detailApi.queryRule(apiData).then(res => {
      console.log('标准换算列表数据', res);
      if (res.status === 200 && res.result) {
        if (res.result && res.result.length > 0) {
          standardVisible.value = true;
        }
      }
    });
  };

  return {
    queryBranchDataById,
    queryFeeFileData,
    editClosedEvent,
    currentChangeEvent,
    updateFbData,

    onDragHeight,
    initVirtual,
    getScroll,
    renderedList,
    init,
    EnterType,
    scrollToPosition,
    mainMaterialTableData,

    saveContent,
    openEditDialog,
    showModelTitle,

    currentInfo,
    isShowModel,
    editContent,
    editKey,
    infoVisible,
    infoText,
    iconType,
    isSureModal,

    ishasRCJList,
    isClearEdit,
    isSortQdCode,
    bdCode,
    rcjVisible,
    deVisible,
    qdVisible,
    DJGCrefreshFeeFile,
    isUpdateFile,
    indexVisible,
    isUpdateQuantities,
    selectUnit,
    showUnitTooltip,
    addCurrentInfo,
    isIndexAddInfo,
    addDataSequenceNbr,
    lockFlag,
    feeFileList,
    tableData,
    originalTableData,
    materialVisible,
  };
};
