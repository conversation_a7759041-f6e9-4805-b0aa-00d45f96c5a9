
const ConstructionMeasureTypeConstant = require('../../../../electron/enum/ConstructionMeasureTypeConstant');
const InsertStrategy = require('../../../../electron/main_editor/insert/insertStrategy');
const StepItemCostLevelConstant = require('../../../../electron/enum/StepItemCostLevelConstant');
const {ObjectUtils} = require("../../../../electron/utils/ObjectUtils");
const {JieSuanRcjStageUtils} = require("../../utils/JieSuanRcjStageUtils");
const DePropertyTypeConstant = require("../../../../electron/enum/DePropertyTypeConstant");
const {ObjectUtil} = require("../../../../common/ObjectUtil");

class JieSuanCostMatchUtil {

  /**
   * 根据传入的数据集合，获取其所有的父级 定额->清单->分部->顶级 的树结构数据
   */

  async getTreeList(baseItemList, unit) {
    let arr = [];
    if (ObjectUtil.isEmpty(baseItemList)) {
      return arr;
    }
    for (const item of baseItemList) {
      this._getParentData(arr, item, unit);
    }
    return arr;
  }

  _getParentData(arr, data, unit) {
    if (ObjectUtil.isEmpty(arr) || !arr.find(item => item.sequenceNbr === data.sequenceNbr)) {
      arr.push(data);
    }
    if (ObjectUtil.isEmpty(data.parentId) || data.parentId === '0') {
      return;
    }
    let nextNode = unit.itemBillProjects.getNodeById(data.parentId);
    if (ObjectUtil.isEmpty(nextNode)) {
      nextNode = unit.measureProjectTables.getNodeById(data.parentId);
    }
    this._getParentData(arr, nextNode, unit);
  }

  async confirmQdAddCostDe(unit, qd, costDe, constructionMeasureType) {
    let { constructId, spId, sequenceNbr } = unit;
    //插入的定额数据
    let de = null;
    //指定分部分项清单 || 对应分部分项
    if (constructionMeasureType === ConstructionMeasureTypeConstant.FBFX) {
      //给清单下新增定额数据
      let insertStrategy = new InsertStrategy({
        constructId, singleId: spId, unitId: sequenceNbr, pageType: 'fbfx'
      });
      de = await insertStrategy.execute({
        pointLine: qd,
        newLine: costDe,
        indexId: costDe.standardId,
        libraryCode: costDe.libraryCode,
        option: 'insert',
        skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
        overwriteColumn: false
      });
    }
    //指定措施清单
    if (constructionMeasureType === ConstructionMeasureTypeConstant.ZJCS || constructionMeasureType === ConstructionMeasureTypeConstant.DJCS) {
      let insertStrategy = new InsertStrategy({
        constructId, singleId: spId, unitId: sequenceNbr, pageType: 'csxm'
      });
      de = await insertStrategy.execute({
        pointLine: qd,
        newLine: costDe,
        indexId: costDe.standardId,
        libraryCode: costDe.libraryCode,
        option: 'insert',
        skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
        overwriteColumn: false
      });
    }
    this.initRcj(unit, de)
    return de;
  }

   initRcj(unit, de) {
    let constructProjectRcjs = unit.constructProjectRcjs;
    if (!ObjectUtils.isEmpty(constructProjectRcjs)) {
      let constructProjectRcjs1 = constructProjectRcjs.filter(i => i.deId === de.sequenceNbr);
      JieSuanRcjStageUtils.rcjDataHandler(constructProjectRcjs1, false, unit);
    }
    let rcjDetailList = unit.rcjDetailList;
    if (!ObjectUtils.isEmpty(rcjDetailList)) {
      let rcjDetailList1 = rcjDetailList.filter(i => i.deId === de.sequenceNbr);
      if (!ObjectUtils.isEmpty(rcjDetailList1)) {
        JieSuanRcjStageUtils.rcjDataHandler(rcjDetailList1, false,unit);
      }
    }
  }


  getFxtjCostDeByBaseDe(baseDe, isCostDe) {
    return {
      'kind': StepItemCostLevelConstant.de,
      'bdCode': baseDe.deCode,
      'fxCode': baseDe.deCode,
      'bdName': baseDe.deName,
      'unit': baseDe.unit,
      'standardId': baseDe.sequenceNbr,
      'libraryCode': baseDe.libraryCode,
      'isCostDe': isCostDe,
      'quantity': 1,
      'quantityExpression': 1,
      'quantityExpressionNbr': 1,
      'isStandard': DePropertyTypeConstant.STANDARD
    };
  }
}

module.exports = {
  JieSuanCostMatchUtil: new JieSuanCostMatchUtil()
};