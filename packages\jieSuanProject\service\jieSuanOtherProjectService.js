'use strict';


const { ObjectUtil } = require('../../../common/ObjectUtil');
const { ResponseData } = require('../../../electron/utils/ResponseData');
const { Service } = require('../../../core');
const { PricingFileFindUtils } = require('../../../electron/utils/PricingFileFindUtils');
const JieSuanSettlementTypeEnum = require('../enum/JieSuanSettlementTypeEnum');
const { NumberUtil } = require('../../../electron/utils/NumberUtil');
const OtherProjectCalculationBaseConstant = require('../../../electron/enum/OtherProjectCalculationBaseConstant');
const { FormulaCalculateUtil } = require('../../../common/FormulaCalculateUtil');
const { ObjectUtils } = require('../../../electron/utils/ObjectUtils');
const { number } = require('mathjs');
const { OtherProjectServiceCost } = require('../../../electron/model/OtherProjectServiceCost');
const { Snowflake } = require('../../../electron/utils/Snowflake');
const OtherProjectServiceCostService = require('../../../electron/service/otherProjectServiceCostService');
const {OtherProject} = require("../../../electron/model/OtherProject");
const { getConnection, getRepository, getManager, Code } = require('typeorm');

/**
 * 结算其他项目Service
 */
class JieSuanOtherProjectService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  /**
   * 初始化 结算 其他项目数据
   */
  async initOtherProjectAllData(args) {
    try {
      // 其他项目总表
      await this.initOtherProjectData(args);
      // 暂列金
      await this.initProvisionalData(args);
      // 专业工程暂估价
      await this.initZygczgjData(args);
      // 总承包服务费
      await this.initServiceCostsData(args);
      // 计日工
      await this.initDayWorkerData(args);
    } catch (e) {
      console.error('审核项目-初始化其他项目数据异常', e);
    }
  }


  /**
   * 公共初始化方法
   */
  async initializeCommonProperties(item, unit) {
    // 检查并赋值合同数量 = 结算数量
      item.jieSuanAmount = item.amount;
      item.amount = null;

    // 检查并赋值合同金额 = 结算金额 = 结算数量 * 对应子页面金额之和
      item.jieSuanTotal = item.total;
      item.total = null;
    // 检查并赋值合同进项税额 = 结算进项税额
      item.jieSuanJxTotal = item.jxTotal;
      item.jxTotal = null;
    // 检查并赋值合同除税合计 = 结算除税合计
      item.jieSuanCsTotal = item.csTotal;
      item.csTotal = null;
    // 标记初始数据，表示为合同内数据
    if (unit?.originalFlag) {
      item.jiesuanOriginal = 1;
    }
  }

  /**
   * 初始化其他项目总表数据
   */
  async initOtherProjectData(args) {
    const { constructId, singleId, unitId } = args || {};
    if (!constructId || !singleId || !unitId) {
      console.warn("Missing required parameters in initOtherProjectData");
      return;
    }

    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const otherProject = PricingFileFindUtils.getOtherProject(constructId, singleId, unitId);

    if (ObjectUtil.isEmpty(otherProject)) {
      return;
    }

    for (const item of otherProject) {
      item.jieSuanAqwmsgf = item.aqwmsgf;
      item.aqwmsgf = null;
      await this.initializeCommonProperties(item, unit);
    }
  }

  /**
   * 初始化暂列金数据
   */
  async initProvisionalData(args) {
    const { constructId, singleId, unitId } = args || {};
    if (!constructId || !singleId || !unitId) {
      console.warn("Missing required parameters in initProvisionalData");
      return;
    }

    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const otherProjectProvisional = PricingFileFindUtils.getOtherProjectProvisional(constructId, singleId, unitId);

    if (ObjectUtil.isEmpty(otherProjectProvisional)) {
      return;
    }

    for (const item of otherProjectProvisional) {
      await this.initializeCommonProperties(item, unit);
      // 合同单价 = 结算单价
      item.jieSuanPrice = item.price;
      item.price = null;
      // 合同暂定金额 = 结算暂定金额 = 【单价】*【数量】
      item.jieSuanProvisionalSum = item.provisionalSum;
      item.provisionalSum = null;
      // 合同除税系数 = 结算除税系数
      item.jieSuanTaxRemoval = item.taxRemoval;
      item.taxRemoval = null;
      // 合同除税单价 = 结算除税单价 = 单价 * （1-除税系数%）
      item.jieSuanCsPrice = item.csPrice;
      item.csPrice = null;

    }
  }

  /**
   * 初始化专业工程暂估价数据
   */
  async initZygczgjData(args) {
    const { constructId, singleId, unitId } = args || {};
    if (!constructId || !singleId || !unitId) {
      console.warn("Missing required parameters in initZygczgjData");
      return;
    }

    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const otherProjectZygcZgjList = PricingFileFindUtils.getOtherProjectZygcZgjList(constructId, singleId, unitId);

    if (ObjectUtil.isEmpty(otherProjectZygcZgjList)) {
      return;
    }

    for (const item of otherProjectZygcZgjList) {
      await this.initializeCommonProperties(item, unit);

      // 合同单价 = 结算单价
      item.jieSuanPrice = item.price;
      item.price = null;
      // 合同除税系数(%) = 结算除税系数(%)
      item.jieSuanTaxRemoval = item.taxRemoval;
      item.taxRemoval = null;
      // 合同除税单价 = 结算除税单价 = 结算单价 * （1-结算除税系数%）
      item.jieSuanCsPrice = item.csPrice;
      item.csPrice = null;
    }
  }

  /**
   * 初始化总承包服务费数据
   */
  async initServiceCostsData(args) {
    const { constructId, singleId, unitId } = args || {};
    if (!constructId || !singleId || !unitId) {
      console.warn("Missing required parameters in initServiceCostsData");
      return;
    }

    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const otherProjectServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);

    if (ObjectUtil.isEmpty(otherProjectServiceCost)) {
      return;
    }

    for (const item of otherProjectServiceCost) {
      // // 结算方式   默认：同合同合价
      item.settlementType = JieSuanSettlementTypeEnum.custom.code;
      // // 结算金额 = 数量 * 项目价值 * 费率
      item.jieSuanFwje = item.fwje;
      item.fwje = null;

      if (unit?.originalFlag) {
        // 标记初始数据，表示为合同内数据
        item.jiesuanOriginal = 1;
      }
    }

    // 由于总承包服务费有初始的结算金额  所以初始化完就需要汇总到总表
    // await this.updateServiceCostJieSuanFwje(args);
  }

  /**
   * 初始化计日工数据
   */
  async initDayWorkerData(args) {
    const { constructId, singleId, unitId } = args || {};
    if (!constructId || !singleId || !unitId) {
      console.warn("Missing required parameters in initDayWorkerData");
      return;
    }

    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const otherProjectDayWork = PricingFileFindUtils.getOtherProjectDayWork(constructId, singleId, unitId);

    if (ObjectUtil.isEmpty(otherProjectDayWork)) {
      return;
    }

    for (const item of otherProjectDayWork) {
      await this.initializeCommonProperties(item, unit);
      // 表达式
      item.quantitativeExpression = null;
      // 合同数量 = 结算数量
      item.jieSuanTentativeQuantity = item.tentativeQuantity;
      item.tentativeQuantity = null;
      // 合同综合单价 = 结算综合单价
      item.jieSuanPrice = item.price;
      item.price = null;
      // 合同除税系数(%) = 结算除税系数(%)
      item.jieSuanTaxRemoval = item.taxRemoval;
      item.taxRemoval = null;
      // 合同除税单价 = 结算除税单价 = 结算单价 * （1-结算除税系数%）
      item.jieSuanCsPrice = item.csPrice;
      item.csPrice = null;

    }
  }

  /**
   * 获取总承包服务的结算方式
   */
  async getServiceCostSettlementType(args) {
    const resArr = [];
    for (const [key, { code, value }] of Object.entries(JieSuanSettlementTypeEnum)) {
      resArr.push({ code: code, value: value });
    }
    return ResponseData.success(resArr);
  }

  async updateServiceCost(args) {
    // settlementType 表示结算方式  对应JieSuanSettlementTypeEnum
    // jieSuanFwje 表示结算金额
    const { constructId, singleId, unitId, sequenceNbr, settlementType, jieSuanFwje } = args;
    const otherProjectServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(otherProjectServiceCost)) {
      throw new Error('总承包服务费数据异常');
    }
    const item = otherProjectServiceCost.find(item => item.sequenceNbr === sequenceNbr);
    if (ObjectUtil.isEmpty(item)) {
      throw new Error('修改的数据不存在');
    }
    if (item.dataType !== 2) {
      // 标题行不能修改结算方式和结算金额
      throw new Error('仅支持数据行修改');
    }
    item.settlementType = settlementType;
    if (settlementType === JieSuanSettlementTypeEnum.equalContractTotal.code) {
      // 同合同金额
      item.jieSuanFwje = item.fwje;
    } else if (settlementType === JieSuanSettlementTypeEnum.baseCalculation.code) {
      // 按计算基数
      item.jieSuanFwje = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(item.xmje, item.amount, NumberUtil.divide100(item.rate)));
    } else if (settlementType === JieSuanSettlementTypeEnum.custom.code) {
      // 直接输入
      item.jieSuanFwje = null;
    }

    // 修改标题行的结算金额
    const parentItem = otherProjectServiceCost.find(i => i.sequenceNbr === item.parentId);
    const sameLevelItem = otherProjectServiceCost.filter(i => i.parentId === item.parentId);
    if (ObjectUtil.isNotEmpty(parentItem) && ObjectUtil.isNotEmpty(sameLevelItem)) {
      let total = 0;
      for (const { jieSuanFwje } of sameLevelItem) {
        total = NumberUtil.add(total, jieSuanFwje);
      }
      parentItem.jieSuanFwje = total;
    }

    return ResponseData.success(true);
  }

  /**
   * 获取总承包服务费的结算金额总和
   */
  async getServiceCostJieSuanFwje(args) {
    const { constructId, singleId, unitId } = args;
    const otherProjectServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(otherProjectServiceCost)) {
      return;
    }
    // 筛选数据行
    const childrenItem = otherProjectServiceCost.filter(item => item.dataType === 2);
    if (ObjectUtil.isEmpty(childrenItem)) {
      return;
    }
    let total = 0;
    for (const item of childrenItem) {
      total = NumberUtil.add(total, item.jieSuanFwje);
    }
    return total;
  }

  // 操作其他项目总承包服务费数据
  async operateOtherProjectServiceCost(arg) {
    //操作 类型  1:插入 2:粘贴 3删除 4 修改
    let operateType = arg.operateType;

    switch (operateType) {
      case 1:
        await this.addProjectServiceCost(arg);
        break;
      case 2:
        await this.pasteProjectServiceCost(arg);
        break;
      case 3:
        await this.delectProjectServiceCost(arg);
        break;
      case 4:
        await this.updateProjectServiceCost(arg);
        break;
    }
    // 更新总表
    await this.updateServiceCostJieSuanFwje(arg);
  }

  /**
   * 新增总承包服务费数据
   */
  async addProjectServiceCost(arg) {
    let constructId = arg.constructId;
    let singleId = arg.singleId;
    let unitId = arg.unitId;
    let targetSequenceNbr = arg.targetSequenceNbr;
    let dataType = arg.dataType;

    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let list = unit.otherProjectServiceCosts;

    let number;
    if (!ObjectUtils.isEmpty(targetSequenceNbr)) {
      number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);

    } else {
      number = 0;
    }
    let otherProjectServiceCost1 = new OtherProjectServiceCost();
    otherProjectServiceCost1.sequenceNbr = Snowflake.nextId();

    otherProjectServiceCost1.fwje = OtherProjectServiceCostService.defaultXmje;
    // 结算的结算金额默认为0
    otherProjectServiceCost1.jieSuanFwje = OtherProjectServiceCostService.defaultXmje;
    // 结算方式默认为直接输入
    otherProjectServiceCost1.settlementType = JieSuanSettlementTypeEnum.custom.code;

    otherProjectServiceCost1.dataType = dataType;

    if (dataType === OtherProjectServiceCostService.datyTypeShuJu) {
      //otherProjectServiceCost1.amount = Number(1).toFixed(6);
      otherProjectServiceCost1.rate = OtherProjectServiceCostService.defaultRate;
      otherProjectServiceCost1.xmje = OtherProjectServiceCostService.defaultRate;
      if (!ObjectUtils.isEmpty(targetSequenceNbr)) {
        let projectService = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
        if (projectService.dataType === OtherProjectServiceCostService.datyTypeBiaoTi) {
          otherProjectServiceCost1.parentId = targetSequenceNbr;
          number = number + 1;
        } else {
          otherProjectServiceCost1.parentId = projectService.parentId;

        }
      }
    }

    list.splice(number, 0, otherProjectServiceCost1);
  }

  /**
   * 粘贴总承包服务费 数据
   */
  async pasteProjectServiceCost(arg) {
    await this.service.otherProjectServiceCostService.pasteProjectServiceCost(arg);
    // 进行标题行数据汇总  由于粘贴调用预算代码  无法明确获得粘贴后的数据  所以直接对所有标题行进行汇总
    await this.updateServiceCostTitleData(null, arg.constructId, arg.singleId, arg.unitId);
  }

  async delectProjectServiceCost(arg) {
    await this.service.otherProjectServiceCostService.delectProjectServiceCost(arg);
    // 进行标题行数据汇总  由于删除调用预算代码  无法明确获得删除后的数据  所以直接对所有标题行进行汇总
    await this.updateServiceCostTitleData(null, arg.constructId, arg.singleId, arg.unitId);
  }

  async updateProjectServiceCost(arg) {
    let constructId = arg.constructId;
    let singleId = arg.singleId;
    let unitId = arg.unitId;
    let targetSequenceNbr = arg.targetSequenceNbr;

    let fxName = arg.projectServiceCost.fxName;
    let xmje = arg.projectServiceCost.xmje;
    let rate = arg.projectServiceCost.rate;
    let dispNo = arg.projectServiceCost.dispNo;
    let unitBj = arg.projectServiceCost.unit;
    let serviceContent = arg.projectServiceCost.serviceContent;
    let settlementType = arg.projectServiceCost.settlementType;
    let description = arg.projectServiceCost.description;
    let fwje = arg.projectServiceCost.fwje;
    let jieSuanFwje = arg.projectServiceCost.jieSuanFwje;


    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let list = unit.otherProjectServiceCosts;
    let projectServiceCost = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
    if (!ObjectUtils.isEmpty(fxName)) {
      projectServiceCost.fxName = fxName;
    }

    if (!ObjectUtils.isEmpty(xmje)) {
      projectServiceCost.xmje = xmje;
    }

    if (!ObjectUtils.isEmpty(rate)) {
      projectServiceCost.rate = rate;
    }

    if (!ObjectUtils.isEmpty(dispNo)) {
      projectServiceCost.dispNo = dispNo;
    }

    if (!ObjectUtils.isEmpty(unitBj)) {
      projectServiceCost.unit = unitBj;
    }

    if (!ObjectUtils.isEmpty(serviceContent)) {
      projectServiceCost.serviceContent = serviceContent;
    }

    if (arg.projectServiceCost.hasOwnProperty('description')){
      projectServiceCost.description = description;
    }

    if (settlementType === JieSuanSettlementTypeEnum.custom.code) {
      // 直接输入
      projectServiceCost.fwje = NumberUtil.costPriceAmountFormat(fwje);
      if (settlementType !== projectServiceCost.settlementType) {
        // 如果原来不是直接输入  说明是刚改的  需要把结算金额置空
        projectServiceCost.fwje = null;
      }
    } else if (settlementType === JieSuanSettlementTypeEnum.equalContractTotal.code) {
      // 同合同价
      projectServiceCost.fwje = projectServiceCost.jieSuanFwje;
    } else if (settlementType === JieSuanSettlementTypeEnum.baseCalculation.code) {
      // 按计算基数
      projectServiceCost.fwje = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(projectServiceCost.xmje,
        NumberUtil.multiply(projectServiceCost.rate, 0.01)));
    }
    projectServiceCost.settlementType = settlementType;

    if (projectServiceCost.dataType === 2) {
      // 如果修改的是数据行  需要进行标题行数据汇总
      await this.updateServiceCostTitleData(projectServiceCost.parentId, constructId, singleId, unitId);
    }
  }

  /**
   * 汇总总承包服务费的标题行数据
   * sequenceNbr为标题行的数据id  如果为空则汇总所有
   */
  async updateServiceCostTitleData(sequenceNbr, constructId, singleId, unitId) {
    const otherProjectServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(otherProjectServiceCost)) {
      return;
    }
    let titleArray = otherProjectServiceCost.filter(item => item.dataType === 1);
    if (ObjectUtil.isNotEmpty(sequenceNbr)) {
      titleArray = titleArray.filter(item => item.sequenceNbr === sequenceNbr);
    }
    if (ObjectUtil.isEmpty(titleArray)) {
      return;
    }
    for (const titleItem of titleArray) {
      const childrenArray = otherProjectServiceCost.filter(item => item.parentId === titleItem.sequenceNbr);
      if (ObjectUtil.isEmpty(childrenArray)) {
        continue;
      }
      let value = 0;
      for (const child of childrenArray) {
        value = NumberUtil.add(value, child.fwje);
      }
      titleItem.fwje = value;
    }
  }

  /**
   * 更新 其他项目总表 的 总承包服务费的结算金额
   */
  async updateServiceCostJieSuanFwje(args) {
    const { constructId, singleId, unitId } = args;
    let zcbfwfObject = PricingFileFindUtils.getOtherProject(constructId, singleId, unitId).filter(item => item.type === OtherProjectCalculationBaseConstant.zcbfwf);
    if (ObjectUtil.isEmpty(zcbfwfObject)) {
      return;
    }
    zcbfwfObject = zcbfwfObject[0];
    if (zcbfwfObject.calculationBase === 'ZCBFWF') {
      // 如果计算基数就是总承包服务费  那么就不用计算别的了  直接使用
      zcbfwfObject.total =  await this.getServiceCostJieSuanFwje(args);
    } else {
      // 如果不是默认的ZCBFWF   那就需要进行计算了
      if (ObjectUtil.isNotEmpty(zcbfwfObject.calculationBase)) {
        // const costCodePrice = this.service.otherProjectService.costCodePrice(args);
        // let codePriceMap = new Map();
        // costCodePrice.forEach(a => codePriceMap.set(a.code, a.price));
        // 重新设置新的总承包服务费
        const total = await this.getServiceCostJieSuanFwje(args);
        // costCodePrice.set('ZCBFWF', total);
        let doCalculate = FormulaCalculateUtil.doCalculate(zcbfwfObject.calculationBase, codePriceMap);
        if (ObjectUtils.isEmpty(doCalculate) || !doCalculate instanceof number) {
          throw new Error('公式存在未知引用，请检查并修改');
        }

        let rate = 100;
        //修改合计计算逻辑  需要乘以费率
        if (ObjectUtils.isNotEmpty(zcbfwfObject.rate)) {
          rate = zcbfwfObject.rate;
        }
        zcbfwfObject.total = NumberUtil.costPriceAmountFormat(doCalculate * zcbfwfObject.amount * NumberUtil.divide100(rate));
      }
    }
    this.service.otherProjectService.updateAllOtherProjects(args);
  }




  /**
   * 获取 其他项目
   * @param args
   * @returns {any[]}
   */
  async getOtherProjectList(args) {
    try {
      // 验证输入参数是否完整
      if (!args || !args.constructId || !args.singleId || !args.unitId) {
        throw new Error("Missing required parameters: constructId, singleId, or unitId");
      }

      // 获取 Unit 数据并验证
      const unit = PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);
      if (!unit) {
        console.warn(`Unit 数据不存在: constructId=${args.constructId}, singleId=${args.singleId}, unitId=${args.unitId}`);
        return []; // 返回空数组或根据业务需求处理
      }

      const unitIs2022 = PricingFileFindUtils.is22Unit(unit);
      const cloneOtherProject = ObjectUtils.cloneDeep(unit.otherProjects) || [];
      const unitCostSummarys = unit.unitCostSummarys;
      const taxCalculationMethod = unit.projectTaxCalculation?.taxCalculationMethod;
      const feeFiles = unit.feeFiles || [];
      const feeFile = feeFiles.find(e => e.defaultFeeFlag === 1);
      const anwenRateBase = feeFile.anwenRateBase;

      let result = [];
      const totalData = new OtherProject();
      totalData.extraName = '其他项目';

      // 提取公共计算逻辑
      const calculateAndRound = (value, sum) => {
        if (ObjectUtils.isNotEmpty(value) && ObjectUtils.isNumberStr(value)) {
          const roundedValue = NumberUtil.costPriceAmountFormat(value);
          return NumberUtil.costPriceAmountFormat(sum + roundedValue);
        }
        return sum;
      };

      // 合并逻辑，减少多次遍历
      const processCloneOtherProject = (is2022, originalFlag) => {
        const totals = { total: 0, jxTotal: 0, csTotal: 0, jieSuanTotal: 0, jieSuanCsTotal: 0, jieSuanJxTotal: 0, aqwmsgf: 0 , jieSuanAqwmsgf: 0};

        for (const item of cloneOtherProject) {

          if (item.markSafa && !["CLZGJ", "ZYGCZGJ", "SBZGJ"].includes(item.type)){
            item.aqwmsgf =NumberUtil.costPriceAmountFormat( NumberUtil.multiply(item.total, anwenRateBase/100))
            totals.aqwmsgf = calculateAndRound( item.aqwmsgf, totals.aqwmsgf);
            if (originalFlag){
              item.jieSuanAqwmsgf =NumberUtil.costPriceAmountFormat( NumberUtil.multiply(item.jieSuanTotal, anwenRateBase/100))
              totals.jieSuanAqwmsgf = calculateAndRound( item.jieSuanAqwmsgf, totals.jieSuanAqwmsgf);
            }
          }else {
            if (["CLZGJ", "ZYGCZGJ", "SBZGJ"].includes(item.type)) {
              totals.aqwmsgf = calculateAndRound(item.aqwmsgf, totals.aqwmsgf);
              if (originalFlag){
                totals.jieSuanAqwmsgf = calculateAndRound(item.jieSuanAqwmsgf, totals.jieSuanAqwmsgf);
              }
            }else {
              item.aqwmsgf = 0;
            }
          }

          if (!item.putOntotalFlag) continue; // 跳过未计入合价的项目


          // 处理合同内数据
          if (originalFlag && ObjectUtils.isNotEmpty(item.jieSuanTotal) && ObjectUtils.isNumberStr(item.jieSuanTotal)) {
            item.jieSuanTotal = NumberUtil.costPriceAmountFormat(item.jieSuanTotal);
            totals.jieSuanTotal += NumberUtil.costPriceAmountFormat(item.jieSuanTotal);
          }

          // 处理结算总数
          if (ObjectUtils.isNotEmpty(item.total) && ObjectUtils.isNumberStr(item.total)) {
            item.total = NumberUtil.costPriceAmountFormat(item.total);
            totals.total += NumberUtil.costPriceAmountFormat(item.total);
          }

          if (!is2022) {
            // 处理结算 JX 总金额
            if (originalFlag) {
              if (ObjectUtils.isNotEmpty(item.jieSuanJxTotal) && ObjectUtils.isNumberStr(item.jieSuanJxTotal)) {
                item.jieSuanJxTotal = NumberUtil.costPriceAmountFormat(item.jieSuanJxTotal);
                totals.jieSuanJxTotal += NumberUtil.costPriceAmountFormat(item.jieSuanJxTotal);
              }

              // 处理结算 CS 总金额
              if (ObjectUtils.isNotEmpty(item.jieSuanCsTotal) && ObjectUtils.isNumberStr(item.jieSuanCsTotal)) {
                item.jieSuanCsTotal = NumberUtil.costPriceAmountFormat(item.jieSuanCsTotal);
                totals.jieSuanCsTotal += NumberUtil.costPriceAmountFormat(item.jieSuanCsTotal);
              }
            }
            // 处理合同内 JX 和 CS 数据
              if (ObjectUtils.isNotEmpty(item.jxTotal) && ObjectUtils.isNumberStr(item.jxTotal)) {
                item.jxTotal = NumberUtil.costPriceAmountFormat(item.jxTotal);
                totals.jxTotal += NumberUtil.costPriceAmountFormat(item.jxTotal);
              }
              if (ObjectUtils.isNotEmpty(item.csTotal) && ObjectUtils.isNumberStr(item.csTotal)) {
                item.csTotal = NumberUtil.costPriceAmountFormat(item.csTotal);
                totals.csTotal += NumberUtil.costPriceAmountFormat(item.csTotal);
              }
            }
        }

        return totals;
      };

      // 根据 unitIs2022 处理逻辑
      const { total, jxTotal, csTotal,jieSuanTotal,jieSuanCsTotal,jieSuanJxTotal,aqwmsgf, jieSuanAqwmsgf } = processCloneOtherProject(unitIs2022, unit.originalFlag);

      if (unit.originalFlag) {
        totalData.jieSuanAqwmsgf = NumberUtil.costPriceAmountFormat(jieSuanAqwmsgf);
        totalData.jieSuanTotal = NumberUtil.costPriceAmountFormat(jieSuanTotal);
        totalData.total = NumberUtil.costPriceAmountFormat(total);
        totalData.aqwmsgf = NumberUtil.costPriceAmountFormat(aqwmsgf);
        if (!unitIs2022) {
          totalData.jieSuanCsTotal = NumberUtil.costPriceAmountFormat(jieSuanCsTotal);
          totalData.jieSuanJxTotal = NumberUtil.costPriceAmountFormat(jieSuanJxTotal);
          totalData.jxTotal = NumberUtil.costPriceAmountFormat(jxTotal);
          totalData.csTotal = NumberUtil.costPriceAmountFormat(csTotal);
        }
      } else {
        totalData.total = NumberUtil.costPriceAmountFormat(total);
        totalData.aqwmsgf = NumberUtil.costPriceAmountFormat(aqwmsgf);
          if (!unitIs2022) {
            totalData.jxTotal = NumberUtil.costPriceAmountFormat(jxTotal);
            totalData.csTotal = NumberUtil.costPriceAmountFormat(csTotal);
          }
        }
      result.push(totalData);

      // 更新税计算方式
      if (taxCalculationMethod) {
        const inputQTXMHJ = unitCostSummarys?.find(i => i.type === "其他项目清单合计");
        if (inputQTXMHJ) {
          inputQTXMHJ.price = total;
        } else {
          console.warn("未找到类型为 '其他项目清单合计' 的 unitCostSummary");
        }
      }

      // 异步处理页面费用类型展示
      const processedCloneOtherProject = await Promise.all(
          cloneOtherProject.map(async (otherProject) => {
            otherProject.type = await this.otherProjectTypeMap(otherProject.type);
            return otherProject;
          })
      );
      result.push(...processedCloneOtherProject);

      //更新进项税明细
      await this.service.jieSuanProject.jieSuanInputTaxDetailsService.countInputTaxDetails(unit);
      return result;
    } catch (error) {
      console.error("getOtherProjectList 错误:", error, "输入参数:", args);
      throw error; // 重新抛出错误以便调用方处理
    }
  }

  /**
   * 前端展示时  把其他项目的费用类型从code转变为对应的
   */
  async otherProjectTypeMap(type) {
    if (ObjectUtils.isEmpty(type)) {
      return '';
    }
    switch (type) {
      case OtherProjectCalculationBaseConstant.ptfy:
        return '普通费用';
      case OtherProjectCalculationBaseConstant.zljr:
        return '暂列金额';
      case OtherProjectCalculationBaseConstant.zygczgj:
        return '专业工程暂估价';
      case OtherProjectCalculationBaseConstant.zcbfwf:
        return '总承包服务费';
      case OtherProjectCalculationBaseConstant.jrg:
        return '计日工';
      case OtherProjectCalculationBaseConstant.zgj:
        return '暂估价';
      case OtherProjectCalculationBaseConstant.clzgj:
        return '材料暂估价';
      case OtherProjectCalculationBaseConstant.sbzgj:
        return '设备暂估价';
      case OtherProjectCalculationBaseConstant.spyqz:
        return '索赔与现场签证';
      default:
        return '';
    }
  }


  // 其他项目列表重新计算
  async updateAllOtherProjects(args) {
    // constructId:项目id
    // singleId:单项id
    // unitId:单位id
    let unit = PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);

    // 判断为12定额的一般计税时，处理除税计税相关计算
    let istrue = false;
    if (!PricingFileFindUtils.is22Unit(unit) && unit.projectTaxCalculation.taxCalculationMethod == '1') {
      istrue = true;
    }
    let list = unit.otherProjects;

    let costCodePrice1 =  this.service.otherProjectService.costCodePrice(args);
    let codePriceMap = new Map();
    costCodePrice1.forEach(a => codePriceMap.set(a.code, a.price));
    let instructionsMap = new Map();
    costCodePrice1.forEach(a => instructionsMap.set(a.code, a.name));
    for (let listElement of list) {

      this.updateCalculationBase(listElement, listElement.calculationBase, args, istrue, codePriceMap, instructionsMap);
    }

    let changeDeIds = new Set();
    await this.service.autoCostMathService.awfCost(args, changeDeIds);

    //重新计算所有金额
    await this.service.unitPriceService.reCacaulateChange(args.constructId, args.singleId, args.unitId, PricingFileFindUtils.getCSXM(args.constructId, args.singleId, args.unitId), changeDeIds);
    await this.service.unitPriceService.reCacaulateChange(args.constructId, args.singleId, args.unitId, PricingFileFindUtils.getFbFx(args.constructId, args.singleId, args.unitId), changeDeIds);

    await this.service.unitCostCodePriceService.countCostCodePrice({
      constructId: args.constructId,
      singleId: args.singleId,
      unitId: args.unitId
    });
  }


  // 修改计算基数值
  updateCalculationBase(otherProject, calculationBase, args, istrue, codePriceMap, instructionsMap) {

    if (!ObjectUtils.isEmpty(calculationBase)) {

      let doCalculate = FormulaCalculateUtil.doCalculate(calculationBase, codePriceMap);
      if (ObjectUtils.isEmpty(doCalculate)
          || !doCalculate instanceof number) {
        throw new Error('公式存在未知引用，请检查并修改');
      }


      otherProject.calculationBase = calculationBase;
      otherProject.instructions = this.service.unitCostSummaryService.getFormulaInstructions(calculationBase.replace(/\s+/g, ''), instructionsMap);
      if (isNaN(otherProject.amount) || ObjectUtils.isEmpty(otherProject.amount)) {
        otherProject.amount = NumberUtil.removeExtraZerosAndDot(Number(1).toFixed(6));
      }

      let rate;
      //修改合计计算逻辑  需要乘以费率
      if(ObjectUtils.isEmpty(otherProject.rate)){
        rate=100;
      }else {
        rate=otherProject.rate;
      }
      if(otherProject.type === OtherProjectCalculationBaseConstant.zcbfwf){
        // 如果计算基数就是总承包服务费  那么就不用计算别的了  直接使用
        otherProject.total = this.getServiceCostJieSuanFwje(args);
      }else{
        otherProject.total = NumberUtil.costPriceAmountFormat(doCalculate * otherProject.amount*NumberUtil.divide100(rate));
      }
      if (istrue) {
        let taxRemoval = otherProject.taxRemoval;
        if (ObjectUtils.isEmpty(otherProject.taxRemoval)) {
          taxRemoval = 0;
        }
        if (otherProject.type === 'JRG'){
          if (calculationBase !== 'JRGMX'){
            otherProject.jxTotal = 0;
            otherProject.csTotal = 0;
          }else {
            let unit = PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);
            let otherProjectDayWorks =  unit.otherProjectDayWorks;
            let otherProjectDayWork =  otherProjectDayWorks[0];
            otherProject.jxTotal = NumberUtil.removeExtraZerosAndDot(NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.jxTaxAmount, 0.01 * rate)));
            otherProject.csTotal = otherProject.total - otherProject.jxTotal;
          }
          return;
        }
        otherProject.jxTotal = NumberUtil.removeExtraZerosAndDot(NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProject.total, 0.01 * taxRemoval)));
        otherProject.csTotal = otherProject.total - otherProject.jxTotal;
      }

    }
  }

}

JieSuanOtherProjectService.toString = () => '[class JieSuanOtherProjectService]';
module.exports = JieSuanOtherProjectService;
