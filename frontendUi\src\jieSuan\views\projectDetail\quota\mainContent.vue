<!--
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-05-23 14:43:34
 * @LastEditors: sunchen
 * @LastEditTime: 2024-06-19 14:29:37
-->
<template>
  <div class="tab-menus">
    <tab-menu @getActiveKey="getActiveKey"></tab-menu>
  </div>
  <div class="main-content">
    <div class="content" ref="content">
      <keep-alive>
        <component
          :is="components.get(componentId)"
          :key="componentId"
          :componentId="componentId"
          ref="childComponentRef"
          @vue:updated="onMountedChange"
          @getCurrentInfo="getCurrentInfo"
        ></component>
      </keep-alive>
      <combined-search></combined-search>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  onMounted,
  onActivated,
  watch
} from 'vue';
import tabMenu from './tabMenu.vue';
import { getComponents } from './mainContentComponents.js';
import { projectDetailStore } from '@/store/projectDetail';
const projectStore = projectDetailStore();
const childComponentRef = ref();
const components = getComponents();
let winWidth = ref();
let componentId = ref(''); //根据tab栏选中的tab加载对应页面
let currentInfo = ref(); // 点击的当前数据
onMounted(() => {
  componentId.value = 'keyInfo';
  winWidth.value = window.innerWidth;
})
watch(
  () => componentId.value,
  val => {
    projectStore.SET_COMPONENT_ID(val);
  }
);
const getActiveKey = (tabName) => {
  projectStore.SET_TAB_SELECT_NAME(tabName);
  currentContent(tabName);
}
//点击tab获取相应页面
const currentContent = (tabName) => {
  switch (tabName) {
    case '关键信息':
      componentId.value = 'keyInfo';
      break;
    case '主要经济指标':
      componentId.value = 'economyQuota';
      break;
    case '主要工程量指标':
      componentId.value = 'economyQuota';
      break;
    case '主要工料指标':
      componentId.value = 'economyQuota';
      break;
  }
};
const onMountedChange = () => {
  console.info('操作这里了!!!!!')
};
const getCurrentInfo = value => {
  currentInfo.value = value;
};
</script>
<style lang="scss" scoped>
.tab-menus {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width:100%;
  height: 42px;
  border-bottom: 2px solid #dcdfe6;
  padding-right: 40px;
  &-btnList {
    .ant-btn {
      padding: 0 5px;
    }
  }
}
.main-content {
  display: flex;
  width:100%;
  height: calc(
    100vh - var(--project-detail-header-height) -
      var(--project-detail-functional-area-height) -
      var(--project-detail-footer) -
      var(--project-detail-main-content-tabs-menu-height)
  );
}
.content {
  display: flex;
  width:100%;
  max-height: 100%;
  overflow: hidden;
  background: #ffffff;
}
</style>
