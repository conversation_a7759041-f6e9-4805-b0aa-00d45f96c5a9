<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:00:41
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-04-24 15:18:18
-->
<template>
  <div class="table-content" id="summaryUpTable">
    <!-- 多专业的表格 -->
    <!-- <vxe-grid
      ref="xGrid"
      v-show="tableType == 'all'"
      v-bind="gridOptions"
      height="auto"
      v-on="gridEvents"
    >
      <template #remark_edit="{ row }">
        <cell-textarea
          :clearable="false"
          v-model.trim="row.remark"
          @blur="updateRemark(row, remark, 'remark')"
          placeholder="请输入备注"
          :textHeight="row.height"
        ></cell-textarea>
      </template>
    </vxe-grid> -->
    <!-- 默认的表格 -->
    <!-- @edit-closed="editClosedEvent" -->
    <vxe-table
      align="center"
      :loading="loading"
      :column-config="{ resizable: true }"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      :data="tableData"
      :keyboard-config="keyboardConfig"
      :mouse-config="mouseConfig"
      height="auto"
      ref="upTable"
      border="full"
      keep-source
      :menu-config="menuConfig"
      @menu-click="contextMenuClickEvent"
      @keydown="handleKeyDownEvent"
      :header-cell-class-name="headerCellClassName"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
      }"
      class="table-edit-common"
      @cell-click="useCellClickEvent"
      @current-change="currentChange"
      :cell-class-name="cellClassName"
    >
      <!-- <vxe-column field="index" width="60" title=""> </vxe-column> -->
      <!-- <vxe-column fixed="left" width="50" align="left">
        <template #default="{ row }">
          <div class="multiple-select">
            {{ row.index }}
          </div>
        </template>
      </vxe-column> -->
      <vxe-column
        field="sortIndex"
        :width="columnWidth(40)"
        title="序号"
        :visible="handlerColumns.find(a => a.field === 'sortIndex')?.visible"
      ></vxe-column>
      <vxe-column
        field="dispNo"
        :width="columnWidth(60)"
        title="编码"
        :visible="handlerColumns.find(a => a.field === 'dispNo')?.visible"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <span v-if="row.isSummaryLine">{{ row.dispNo }}</span>
          <vxe-input
            v-else
            :clearable="false"
            v-model.trim="row.dispNo"
            type="text"
            @blur="update(row, dispNo, 'dispNo')"
            @keyup="row.dispNo = row.dispNo.replace(/[^\u4e00-\u9fff\w.]/g, '')"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="code"
        :width="columnWidth(100)"
        title="费用代号"
        :visible="handlerColumns.find(a => a.field === 'code')?.visible"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <span v-if="row.isSummaryLine">{{ row.code }}</span>
          <vxe-input
            v-else
            :clearable="false"
            v-model.trim="row.code"
            type="text"
            @blur="editCode(row)"
            @keyup="validateAndFormatCode(row)"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="name"
        :width="columnWidth(220)"
        title="名称"
        :visible="handlerColumns.find(a => a.field === 'name')?.visible"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row }">
          <span>{{ row.name }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-input
            v-if="row.whetherTax !== 1 && !row.isSummaryLine"
            :clearable="false"
            v-model.trim="row.name"
            @blur="update(row, name, 'name')"
            @keyup="row.name = row.name.replace(/\-|\+|\*|\/|\./g, '')"
          >
          </vxe-input>
          <span v-else>{{ row.name }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="calculateFormula"
        :visible="
          handlerColumns.find(a => a.field === 'calculateFormula')?.visible
        "
        :width="columnWidth(150)"
        title="计算基数"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row }">
          {{ row.calculateFormula }}
        </template>
        <template #edit="{ row }">
          <span v-if="row.isSummaryLine">{{ row.calculateFormula }}</span>
          <vxe-input
            v-else
            :clearable="false"
            v-model.trim="row.calculateFormula"
            @blur="update(row, calculateFormula, 'calculateFormula')"
            @keyup="
              row.calculateFormula = row.calculateFormula.replace(
                /[^\w\-\+\*\/]/g,
                ''
              )
            "
          >
          </vxe-input>
          <icon-font
            v-if="row.isSummaryLine != true"
            type="icon-bianji"
            class="more-icon"
            @click="editCalc(row)"
          ></icon-font>
        </template>
      </vxe-column>
      <vxe-column
        field="instructions"
        :visible="handlerColumns.find(a => a.field === 'instructions')?.visible"
        :min-width="columnWidth(200)"
        title="基数说明"
      >
      </vxe-column>
      <vxe-column
        field="rate"
        :width="columnWidth(90)"
        title="费率（%）"
        :visible="handlerColumns.find(a => a.field === 'rate')?.visible"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row }">
          {{
            row?.isUpdateRate
              ? decimalFormat(row.rate, 'COST_SUMMARY_SUMMARY_FREERATE_PATH')
              : row.rate
          }}
        </template>
        <template #edit="{ row }">
          <span v-if="row.isSummaryLine">{{
            row?.isUpdateRate
              ? decimalFormat(row.rate, 'COST_SUMMARY_SUMMARY_FREERATE_PATH')
              : row.rate
          }}</span>
          <vxe-pulldown
            v-else
            v-model="showPull"
            :transfer="true"
            :trigger="`click`"
          >
            <template #default>
              <vxe-input
                :clearable="false"
                :modelValue="row.rate"
                @blur="editRate(row, $event)"
              ></vxe-input>
              <icon-font
                type="icon-bianji"
                class="more-icon"
                @click="toggleEvent(row)"
              ></icon-font>
            </template>
            <template #dropdown>
              <div class="pull-down-content">
                <SearchFee
                  ref="SearchFeeRef"
                  EntryType="SummaryExpense"
                  @onUse="onUseRate"
                ></SearchFee>
              </div>
            </template>
          </vxe-pulldown>
        </template>
      </vxe-column>
      <vxe-column
        field="price"
        :visible="handlerColumns.find(a => a.field === 'price')?.visible"
        :width="columnWidth(120)"
        title="金额"
      >
        <template #default="{ row }">
          {{ decimalFormat(row.price, 'COST_SUMMARY_JE_PATH') }}
        </template>
      </vxe-column>
      <!-- <vxe-column field="category" width="120" title="费用类别"> </vxe-column> -->
      <vxe-column
        field="category"
        :min-width="columnWidth(100)"
        title="费用类别"
        :visible="handlerColumns.find(a => a.field === 'category')?.visible"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <span v-if="row.isSummaryLine">{{ row.category }}</span>
          <vxe-select
            v-else
            v-model="row.category"
            placeholder="请输入费用类别"
            @change="categoryChange(row)"
          >
            <vxe-option
              :value="i"
              :label="i"
              v-for="i of categoryOptions"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column
        field="remark"
        title="备注"
        :visible="handlerColumns.find(a => a.field === 'remark')?.visible"
        :min-width="columnWidth(100)"
        :edit-render="{ autofocus: '.vxe-textarea--inner' }"
      >
        <template #edit="{ row }">
          <span v-if="row.isSummaryLine">{{ row.remark }}</span>
          <vxe-input
            v-else
            :clearable="false"
            v-model.trim="row.remark"
            @blur="update(row, remark, 'remark')"
          >
          </vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="whetherPrint"
        title="打印"
        :width="columnWidth(80)"
        :visible="handlerColumns.find(a => a.field === 'whetherPrint')?.visible"
        :cell-render="{}"
      >
        <template #default="{ row }">
          <vxe-checkbox
            :disabled="row.isSummaryLine"
            v-model="row.whetherPrint"
            size="small"
            content=""
            :checked-value="1"
            :unchecked-value="0"
            @change="update(row, 'whetherPrint', 'whetherPrint')"
          ></vxe-checkbox>
          <!-- <vxe-checkbox
            v-model="row.print"
            name="print"
            @change="CheckboxChange(row, 'print')"
          ></vxe-checkbox> -->
        </template>
      </vxe-column>
    </vxe-table>
  </div>
  <common-modal
    className="dialog-comm noMask"
    title="计算基数编辑"
    width="1200"
    height="450"
    v-model:modelValue="comModel"
    @cancel="cancelData"
    @close="comModel = false"
    :mask="false"
    style="position: releative"
    destroy-on-close
  >
    <content-down
      :isTextArea="isTextArea"
      :textValue="textValue"
      :nowConstructMajorType="nowConstructMajorType"
      ref="comArea"
    ></content-down>
    <span class="btns">
      <a-button @click="cancelData()">取消</a-button>
      <a-button type="primary" @click="sureData()">确定</a-button>
    </span>
  </common-modal>
  <!-- 汇总范围弹框 -->
  <summary-scope
    v-if="isSummaryScope"
    type="batchApplication"
    @closeDialog="isSummaryScope = false"
    @querySummary="getTableData"
    :nowConstructMajorType="nowConstructMajorType"
  ></summary-scope>
  <common-modal
    v-model:modelValue="codeNullVisible"
    className="dialog-comm"
    title="提示"
    width="400px"
  >
    <div style="margin-bottom: 20px">置空费用代号将取消引用，是否确定？</div>
    <div class="footer-btn-list">
      <a-button @click="codeNullCancel">取消</a-button>
      <a-button type="primary" @click="codeNullQuery">确定</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import {
  ref,
  onMounted,
  watch,
  reactive,
  getCurrentInstance,
  toRaw,
  nextTick,
  onActivated,
} from 'vue';
import feePro from '@gongLiaoJi/api/feePro';
import csProject from '@gongLiaoJi/api/csProject';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import { add } from 'xe-utils';
import { getUrl } from '@/utils/index';
import ContentDown from './ContentDown.vue';
import { Modal } from 'ant-design-vue';
import {
  useCellClick,
  useCellClearClickEvent,
  useCellEditDBLClickEvent,
} from '@gongLiaoJi/hooks/useCellClick';
import { pureNumber0 } from '@/utils/index';
import summaryScope from '../HumanMachineSummary/summaryScope.vue';
import SearchFee from '@gongLiaoJi/views/projectDetail/customize/FeeWithDrawalTable/SearchFee.vue';
import { useFormatTableColumns } from '@gongLiaoJi/hooks/useGljFormatTableColumns.js';
import tableColumns from './tableColumns';
const projectStore = projectDetailStore();
import operateList from '../operate';
import { recordProjectData } from '@gongLiaoJi/hooks/recordProjectOperat.js';
import csDetail from '@gongLiaoJi/api/projectDetail';
import { useDecimalPoint } from '@gongLiaoJi/hooks/useDecimalPoint.js';
const { decimalFormat } = useDecimalPoint();
import { columnWidth } from '@gongLiaoJi/hooks/useSystemConfig';
let { updateGljSelrowId } = recordProjectData();

// import { VXETable } from 'vxe-table';
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const store = projectDetailStore();
const upTable = ref();
const tableData = ref([]);
let totalFee = ref([]);
let taxMode = ref(); //1-一般计税，2-简易计税
let isCurrent = ref(0);
let comModel = ref(false); //计算基数编写弹框
let isTextArea = ref(true);
let textValue = ref('');
// let updateRate = ref(false); //修改费率弹窗
let rowValue = ref('');
// let deleteModel = ref(false);
let deleteInfo = ref();
let loading = ref(false);
let oldValue = ref('');
const comArea = ref();
const isSummaryScope = ref(false); //汇总范围
const nowConstructMajorType = ref('');
const codeNullVisible = ref(false);
const currentInfo = ref(null);
const selColumn = ref('');
let tableType = ref(''); // all ,多专业表格
const isSingleMajorFlag = ref(true);

onMounted(() => {
  getIsSingleMajorFlag();
  getTableData();
  initColumns({
    pageName: 'fyhz',
    columns: tableColumns,
  });
  // 概算不用
  // getTotalFeeCode();
  window.addEventListener('keydown', keyDownOperate);
});
const categoryOptions = [
  '无',
  '直接费',
  '人工费',
  '材料费',
  '机械费',
  '设备费',
  '主材费',
  '管理费',
  '利润',
  '独立费',
  '税金',
  '工程造价',
  '造价调整',
  '安全文明施工费',
  '人材机价差',
  '实体人工费',
  '实体材料费',
  '实体机械费',
  '实体主材费',
  '实体设备费',
];

const {
  showColumns,
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
} = useFormatTableColumns({
  type: 8,
  pageName: 'fyhz',
  initColumnsCallback: () => {
    initColumns({
      pageName: 'fyhz',
      columns: tableColumns,
    });
  },
});
// initColumns({
//   columns: tableColumns,
// });
const editRate = (row, event) => {
  if (event.value === row.rate) {
    return;
  }
  row.rate = event.value;
  const rate = pureNumber0(row.rate);
  row.rate = decimalFormat(rate, 'COST_SUMMARY_SUMMARY_FREERATE_PATH');
  update(row, 'rate', 'rate');
};
const getIsSingleMajorFlag = () => {
  csDetail
    .getIsSingleMajorFlag({
      unitId: store.currentTreeInfo?.id,
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId,
    })
    .then(res => {
      console.log('多专业取费返回值', res);
      isSingleMajorFlag.value = res.result || false;
    });
};

const handleKeyDownEvent = event => {
  let tableList = JSON.parse(JSON.stringify(tableData.value));
  let sTable = event.$table;
  let code = event.$event.code;
  let row = sTable.getCurrentRecord();
  let rowIndex = sTable.getRowIndex(row);
  let rowIsEdit = sTable.isEditByRow(row); //行是否为编辑状态
  // 回车
  if (code == 'Enter' || code == 'NumpadEnter') {
    let cell = sTable.getSelectedCell();
    let cellSel = document.querySelectorAll('.col--selected')[0];
    if (cell !== null) {
      selColumn.value = JSON.parse(JSON.stringify(cell.column));
    }
    // 如果为编辑状态则取消编辑状态执行确认修改操作，否则打开编辑状态
    if (rowIsEdit) {
      sTable.clearEdit();
      setTimeout(() => {
        sTable.setSelectCell(row, selColumn.value);
      }, 200);
    } else {
      cellSel.click();
    }
  }
  // 删除
  if (code == 'Delete' && !rowIsEdit) {
    deleteItem(row);
  }
  // 上
  if (code == 'ArrowUp' && !rowIsEdit) {
    let row = tableList[rowIndex == 0 ? 0 : rowIndex - 1];
    sTable.setCurrentRow(row);
    currentInfo.value = row;
    if (rowIndex == 0) {
      return;
    }
    setTimeout(() => {
      let cellSel = document.querySelectorAll('.col--selected')[0];
      cellSel.click();
    }, 1);
  }
  // 下
  if (code == 'ArrowDown' && !rowIsEdit) {
    let row =
      tableList[
        rowIndex == tableList.length - 1 ? tableList.length - 1 : rowIndex + 1
      ];
    sTable.setCurrentRow(row);
    currentInfo.value = row;
    setTimeout(() => {
      if (rowIndex == tableList.length - 1) {
        return;
      }
      let cellSel = document.querySelectorAll('.col--selected')[0];
      cellSel.click();
    }, 1);
  }
  // 左
  if (code == 'ArrowLeft' && !rowIsEdit) {
    setTimeout(() => {
      let cellSel = document.querySelectorAll('.col--selected')[0];
      cellSel.click();
    }, 10);
  }
  // 右
  if (code == 'ArrowRight' && !rowIsEdit) {
    setTimeout(() => {
      let cellSel = document.querySelectorAll('.col--selected')[0];
      cellSel.click();
    }, 10);
  }
};
const mouseConfig = {
  selected: true,
};
const keyboardConfig = {
  isArrow: true,
};

const keyDownOperate = event => {
  if (!upTable.value || store.tabSelectName !== '费用汇总') return;
  if (event.ctrlKey && event.code == 'KeyC') {
    // 判断是否为编辑状态
    if (!upTable.value.isEditByRow(upTable.value.getCurrentRecord())) {
      copyItem(upTable.value.getCurrentRecord());
      // message.success('已成功复制当前选中行');
    }
  }
  if (event.ctrlKey && event.code == 'KeyV') {
    if (!upTable.value.isEditByRow(upTable.value.getCurrentRecord())) {
      getCurrentIndex(upTable.value.getCurrentRecord());
      pasteItem();
      // message.success('已成功粘贴到当前选中行');
    }
  }
  if (event.ctrlKey && event.code == 'KeyD') {
    deleteItem(upTable.value.getCurrentRecord());
  }
};
const props = defineProps({
  isCharu: {
    type: Boolean,
  },
  isCharuDel: {
    type: Boolean,
  },
  isSaveTemplate: {
    type: Boolean,
  },
  isLoadTemplate: {
    type: Boolean,
  },
  isbatchApplication: {
    type: Boolean,
  },
});
watch(
  () => currentInfo.value,
  row => {
    updateGljSelrowId(row.sequenceNbr, '费用汇总', 'selRowId');
    operateList.value.forEach(item => {
      if (item.name == 'delete-subItem') {
        item.disabled = row.isSummaryLine ? true : false;
      }
    });
  }
);
watch(
  () => projectStore.asideMenuCurrentInfo,
  val => {
    if (projectStore.tabSelectName === '费用汇总') {
      if (val) {
        console.info(2342342342342, val);
        updateGljSelrowId(val.sequenceNbr, '费用汇总', 'leftTwoTreeId');
      } else {
        tableType.value = 'all';
        nowConstructMajorType.value = '';
        updateGljSelrowId('', '费用汇总', 'leftTwoTreeId');
        getTableData();
      }
    }
  }
);
watch(
  () => props.isCharu,
  () => {
    operate('insert', {});
  }
);

// 设置里面的组价方式改了，刷新页面
watch(
  () => store.pricingMethod,
  () => {
    if (
      store.tabSelectName === '费用汇总' &&
      store.currentTreeInfo.type === 3
    ) {
      getTableData(tableType.value);
    }
  }
);

watch(
  () => props.isSaveTemplate,
  () => {
    executeOperation('saveTemplate');
  }
);
watch(
  () => props.isLoadTemplate,
  () => {
    executeOperation('loadingTemplates');
  }
);
watch(
  () => props.isCharuDel,
  () => {
    deleteItem(upTable.value.getCurrentRecord());
  }
);
watch(
  () => props.isbatchApplication,
  () => {
    summaryScopeClick();
  }
);
watch(
  [
    () => projectStore.tabSelectName,
    () => tableType.value,
    () => projectStore.currentTreeInfo,
    () => isSingleMajorFlag.value,
  ],
  () => {
    if (
      projectStore.tabSelectName === '费用汇总' &&
      projectStore.currentTreeInfo.type == 3
    ) {
      isCurrent.value = 0; //切换页面选中行默认选第一行
      operateList.value.forEach(item => {
        if (item.name == 'batch-application') {
          item.visible =
            tableType.value == 'all' && !isSingleMajorFlag.value ? false : true;
        }
        if (['save-template', 'load-template'].includes(item.name)) {
          item.disabled =
            tableType.value == 'all' && !isSingleMajorFlag.value ? true : false;
        }
      });
    }
  },
  { immediate: true }
);

watch(
  () => projectStore.currentTreeInfo?.id,
  () => {
    if (projectStore.tabSelectName === '费用汇总') {
      getIsSingleMajorFlag();
    }
  }
);
// 点击汇总范围
const summaryScopeClick = () => {
  isSummaryScope.value = true;
};
const editCalc = row => {
  comModel.value = true;
  textValue.value = row;
  oldValue.value = row.calculateFormula;
  comArea.value?.getTableData();
};
const cancelData = () => {
  comArea.value.value = oldValue.value;
  textValue.value.calculateFormula = oldValue.value;
  // textValue.value.price = oldValue.value;
  comModel.value = false;
  focusTable();
};
const categoryChange = row => {
  if (row.category === '无') {
    row.category = '';
  }
  update(row, 'category', 'category');
};
//编辑计算基数弹框确认
const sureData = () => {
  let oldTableData = tableData.value;
  const value = comArea.value.value;
  const priceNum = comArea.value.priceNum;
  const newTableData = comArea.value.treeDataCopy;
  // const zz = /[+-\/*]/
  if (!value) {
    //不可以输入空
    message.warn(`输入不可为空`);
    return;
  }
  if (comArea.value.matchValue()) {
    return message.warn(`数据引用不合法，请重新编辑！`);
  }
  // let valArr=value.split(zz)
  let codeArr = [];
  for (let i in newTableData) {
    codeArr.push(newTableData[i].code);
  }
  for (let n in oldTableData) {
    codeArr.push(oldTableData[n].code);
  }
  textValue.value.calculateFormula = value;
  textValue.value.price = priceNum;
  update(textValue.value, textValue.value.field);
  focusTable();
};
// 点击左侧导航树时
bus.on('summaryExpenseData', data => {
  console.log('---summaryExpenseData', data);
  if (data !== 'all') {
    nowConstructMajorType.value = data;
    tableType.value = '';
  } else {
    tableType.value = 'all';
    nowConstructMajorType.value = '';
  }
  getTableData(data);
});

// 更改多专业/单专业取费时
bus.on('dzyhzQuery', type => {
  // if (type != 'all') {
  //   store.categoryIndex = -1;
  // }
  // tableType.value = 'all';
  // nowConstructMajorType.value = '';
  // getIsSingleMajorFlag();
  // getTableData();
  // setTimeout(() => {
  //   bus.emit('changeLeftNum');
  // }, 400);
});
// 初始获取导航树之后
// bus.on('activeKey', data => {
//   tableType.value = 'all';
//   nowConstructMajorType.value = '';
//   getTableData();
// });
// 激活表格
const focusTable = () => {
  upTable.value?.focus();
};
bus.on('costAggregation', data => {
  focusTable();
});

// 获取列表数据
const getTableData = (type = '') => {
  if (loading.value) return;
  let constructMajorType = store.currentTreeInfo?.qfMajorType; //工程专业
  if (nowConstructMajorType.value !== '') {
    constructMajorType = nowConstructMajorType.value;
  }
  if (tableType.value == 'all') {
    constructMajorType = 'TOTAL';
  }

  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    qfMajorType: constructMajorType, //工程专业
  };
  totalFee.value = [];
  console.log('获取概算费用汇总数据', apiData);
  const apiName = 'getUnitCostSummaryList';
  loading.value = true;
  tableData.value = [];
  csProject[apiName](apiData)
    .then(res => {
      console.log('获取概算费用汇总数据返回结果', res);
      if (res.status === 200) {
        // if (tableType.value == 'all') {
        //   gridOptions.data = res.result;
        //   return;
        // }
        res.result &&
          res.result.map((item, index) => {
            totalFee.value.push(item.code);
            if (
              item.type === '附加税费' ||
              item.type === '销项税额' ||
              item.type === '税金'
            ) {
              item.whetherTax = 1;
            } else {
              item.whetherTax = 0;
            }
          });
        tableData.value = res.result ? res.result : [];
        tableData.value.forEach(item => {
          if (item.isSummaryLine) {
            item.whetherPrint = 1;
          }
        });
        tableData.value.map(item => (item._X_ROW_KEY = item.sequenceNbr));
        nowConstructMajorType.value = constructMajorType;
        let gljCheckTab = projectStore.gljCheckTab;
        let upSelRow = gljCheckTab[
          projectStore.currentTreeInfo.sequenceNbr
        ]?.tabList.find(a => a.tabName == '费用汇总');
        nextTick(() => {
          if (upSelRow && upSelRow.selRowId !== '') {
            let obj = tableData.value.find(
              a => a.sequenceNbr == upSelRow.selRowId
            );
            if (obj) {
              upTable.value?.setCurrentRow(obj);
              currentInfo.value = obj;
              setTimeout(() => {
                upTable.value.scrollToRow(obj);
              }, 100);
            } else if (tableData.value.length > 0) {
              upTable.value?.setCurrentRow(tableData.value[0]);
              currentInfo.value = tableData.value[0];
            }
          } else if (tableData.value.length > 0) {
            upTable.value.setCurrentRow(tableData.value[0]);
            currentInfo.value = tableData.value[0];
          }
          focusTable();
        });
      }
    })
    .finally(() => {
      loading.value = false;
    });
  // 概算未做
  // getTotalFeeCode();
};
// watch(
//   () => [store.tabSelectName, store.currentTreeInfo],
//   ([val, oldVal], [newY, oldy]) => {
//     if (
//       store.tabSelectName === '费用汇总' &&
//       store.currentTreeInfo.type === 3
//     ) {
//       isCurrent.value = 0; //切换页面选中行默认选第一行
//       setTimeout(() => {
//         getTableData();
//       }, 600);
//     }
//   }
// );

const isHasCalculateFormula = (value, code) => {
  const valueList = value.match(/[A-Za-z0-9_]+(\.\d+)?/g);
  console.log('计算基数输入的数值', valueList);
  let flag = true;
  valueList.map(item => {
    if (!totalFee.value.includes(item) || item === code) {
      flag = false;
      return;
    }
  });
  if (valueList.length !== [...new Set(valueList)].length) flag = false;
  return flag;
};
const clear = () => {
  //清除编辑状态
  const $table = upTable.value;
  $table.clearEdit();
};
const editClosedEvent = ({ row, column }) => {
  console.log('费用汇总修改', row);
  const $table = upTable.value;
  const field = column.field;
  let value = row[field];
  const reg = /[^\d\.]/g;
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  if (field !== 'remark' && !row[field]) {
    //不可以输入空
    $table.revertData(row, field);
    message.warn(`输入不可为空`);
    return;
  }
  // 判断单元格值是否被修改
  if ((field === 'name' || field === 'remark') && value?.length > 50) {
    console.log('field', field, 'value', value);
    row[field] = value.slice(0, 50);
    message.warn(`输入字符应50个字符范围内`);
    // return;
  }
  // let feeList = [...totalFee.value];
  // feeList.map(item => (item = item.toUpperCase()));
  // if (field === 'code' && feeList.includes(value.toUpperCase())) {
  //   let list = tableData.value.filter(item => item.code === value);
  //   list && list.length > 0
  //     ? message.warn(`当前费用代号已被使用`)
  //     : message.warn(`当前费用代号与费用代码重复，请修改`);
  //   $table.revertData(row, field);
  //   return;
  // }
  if (field === 'name' && value?.length === 0) {
    $table.revertData(row, field);
    message.warn(`输入名称不可为空!`);
    return;
  }
  if (field === 'calculateFormula' && value?.length === 0) {
    message.warn(`计算基数不可为空`);
    $table.revertData(row, field);
    return;
  }
  // if (field === 'calculateFormula' && !isHasCalculateFormula(value, row.code)) {
  //   message.warn(`计算基数输入不符合规格`);
  //   $table.revertData(row, field);
  //   return;
  // }
  if (field === 'rate' && value !== '' && reg.test(value)) {
    console.log('----------,不和規則');
    //不可以输入除数字和小数点之外的
    $table.revertData(row, field);
    return;
  }
  if (field === 'rate' && value === '') {
    row[field] = '0';
  } else if ((field === 'rate' && Number(value) > 1000) || Number(value) < 0) {
    message.warn(`费率可输入数值范围：0-1000`);
    $table.revertData(row, field);
    return;
  }
  if (field === 'rate') {
    if (
      (taxMode.value === 1 &&
        (row.type === '附加税费' || row.type === '销项税额')) ||
      (taxMode.value === 0 && row.type === '税金')
    ) {
      if ($table.isUpdateByRow(row, field)) {
        // rowValue.value = { ...row };
        Modal.confirm({
          title: '是否确认修改税率？',
          content: '修改税率将会同步关联取费表中计税设置的费率，是否确认修改?',
          okText: '确定',
          cancelText: '取消',
          onOk() {
            update(row, field);
          },
          onCancel() {},
        });
        return;
      }
    }
  }
  if (field === 'code' && row.code === '') {
    codeNullVisible.value = true;
    currentInfo.value = row;
    return;
  }
  // 判断单元格值是否被修改
  if ($table.isUpdateByRow(row, field)) {
    update(row, field);
  }
};
// 修改费用代号
const editCode = row => {
  const $table = upTable.value;
  if (!$table.isUpdateByRow(row, 'code')) {
    return;
  }
  if (row.code === '' && row.adopted) {
    codeNullVisible.value = true;
    currentInfo.value = row;
    return;
  }

  update(row, 'code');
};
// 清空费用代号取消
const codeNullCancel = () => {
  console.info(234234234324324);
  const $table = upTable.value;
  codeNullVisible.value = false;
  focusTable();
  $table.revertData(currentInfo.value, 'code');
};
// 清空费用代号确认
const codeNullQuery = () => {
  codeNullVisible.value = false;
  update(currentInfo.value, 'code');
};
// 处理不能输入纯数组
const validateAndFormatCode = row => {
  const code = row.code.trim();

  const number = /^\d+$/;
  if (number.test(code)) {
    // 如果是纯数字
    row.code = '';
    return;
  }
  const codePattern = /^[a-zA-Z\u4e00-\u9fa5_][a-zA-Z0-9\u4e00-\u9fa5_]*$/;

  if (!codePattern.test(code)) {
    row.code = code.slice(0, -1);
  }
  if (row.code.match(/_/g) && row.code.match(/_/g).length > 3) {
    row.code = code.slice(0, -1);
  }
};
const getZeroCode = async () => {
  return new Promise((resolve, reject) => {
    let type = store.currentTreeInfo?.constructMajorType;
    if (nowConstructMajorType.value !== '') {
      type = nowConstructMajorType.value;
    }
    const formdata = {
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeInfo?.parentId, //单项ID
      unitId: store.currentTreeInfo?.id, //单位ID
      qfMajorType: tableType.value == 'all' ? 'TOTAL' : type, //工程专业
    };
    let zeroCode = ['0'];
    csProject.getGetUnitCostCodePrice(formdata).then(res => {
      if (res.status === 200) {
        let datas = res.result ? res.result : [];
        datas?.map((item, index) => (item.sortNo = index + 1));
        datas.map(item => {
          if (item.price === 0) {
            zeroCode.push(item.code);
          }
        });
        resolve({ zeroCode, newTableData: datas });
      }
    });
  });
};
const update = async (row, field, name) => {
  const $table = upTable.value;
  // 判断当前单元格有没有被修改
  if (name && !$table.isUpdateByRow(row, name)) {
    return;
  }
  if (name === 'calculateFormula') {
    let { zeroCode } = await getZeroCode();
    const regex = new RegExp(`/(${zeroCode.join('|')})`);
    const isValid = regex.test(row.calculateFormula);
    if (isValid) {
      $table.revertData(row, 'calculateFormula');
      return message.error('数据引用不合法，请重新编辑');
    }
  }

  if (['rate'].includes(field)) {
    row.isUpdateRate = true;
  }

  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    unitCostSummary: toRaw(row),
    qfMajorType:
      tableType.value == 'all' ? 'TOTAL' : nowConstructMajorType.value, //工程专业
  };
  console.log('费用汇总修改', apiData);
  csProject.gsSaveCostSummary(apiData).then(res => {
    console.log('费用汇总修改结果', res);
    if (res.status !== 500) {
      message.success('修改成功！');
      tableData.value.map((item, index) => {
        if (item.sequenceNbr === row.sequenceNbr) {
          isCurrent.value = index;
        }
      });
      getTableData();
      comModel.value = false;
    } else if (res.status === 500) {
      message.error(res.message);
      const $table = upTable.value;
      $table.revertData(row, field);
    }
  });
};
const pasteIsDisabled = () => {
  if (store.summaryCopyInfo && store.summaryCopyInfo.asideTitle === 'fyhz') {
    menuConfig.body.options[0][2].disabled = false;
  } else {
    menuConfig.body.options[0][2].disabled = true;
  }

  if (!store.summaryCopyInfo?.copyInfo) {
    menuConfig.body.options[0][2].disabled = true;
  }
};
const menuConfig = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          name: '插入',
          code: 'add',
          disabled: false,
          identification: 0,
        },

        {
          code: 'copy',
          name: '复制',
          disabled: false,
          identification: 7,
        },
        {
          code: 'paste',
          name: '粘贴',
          disabled: true,
          identification: 6,
        },
        {
          code: 'delete',
          name: '删除',
          className: 'redFont',
          disabled: false,
          identification: 2,
        },

        {
          code: 'saveTemplate',
          name: '保存模版',
          disabled: false,
          identification: 4,
        },
        {
          code: 'loadingTemplates',
          name: '载入模版',
          disabled: false,
          identification: 5,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    if (!row) return;
    upTable.value.setCurrentRow(row);
    currentInfo.value = row;
    if (row && row.whetherTax === 1) {
      options[0][3].disabled = true;
      options[0][1].disabled = true;
      options[0][3].className = '';
    } else {
      options[0][3].disabled = false;
      options[0][1].disabled = false;
      options[0][3].className = 'redFont';
    }

    for (const item of options[0]) {
      if (row.isSummaryLine) {
        item.disabled = true;
      } else {
        if (!row.permission.includes(item.identification)) {
          item.disabled = true;
        } else {
          item.disabled = false;
        }
      }
    }
    if (
      row.permission.includes(menuConfig.body.options[0][2]['identification'])
    ) {
      pasteIsDisabled();
    }

    return true;
  },
});
const getCurrentIndex = item => {
  if (item) {
    tableData.value.map((n, index) => {
      if (n.sequenceNbr === item.sequenceNbr) {
        isCurrent.value = index;
      }
    });
  } else {
    isCurrent.value = 0;
  }
};
const operate = (type, row) => {
  let operateType;
  switch (type) {
    case 'insert':
      // 插入
      operateType = 1;
      row ? getCurrentIndex(upTable.value.getCurrentRecord()) : '';
      break;
    case 'delete':
      // 删除
      operateType = 3;
      isCurrent.value = 0;
      break;
    case 'paste':
      // 粘贴
      operateType = 2;
      isCurrent.value += 1;
      break;
  }
  let isCurrentRow = upTable.value.getCurrentRecord();
  let targetSequenceNbr = isCurrentRow?.sequenceNbr;
  let lineNumber;
  tableData.value &&
    tableData.value.map((item, index) => {
      if (item.sequenceNbr === isCurrentRow.sequenceNbr) {
        lineNumber = index + 1;
      }
    });
  if (type === 'insert' || type === 'paste') {
    type === 'paste' ? (row.code = '') : '';
    console.log(
      '---------------------',
      store.currentTreeGroupInfo,
      store.currentTreeInfo
    );
    let addData = {
      lineNumber,
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId, //单项ID
      unitId: store.currentTreeInfo?.id, //单位ID
      unitCostSummary: type === 'paste' ? JSON.parse(JSON.stringify(row)) : {},
      qfMajorType:
        tableType.value == 'all' ? 'TOTAL' : nowConstructMajorType.value, //工程专业
    };
    console.log('费用汇总新增', addData);
    csProject.gsAddCostSummary(addData).then(res => {
      if (res.status === 200) {
        type === 'insert'
          ? message.success('插入成功')
          : message.success('粘贴成功');
        console.log('********otherProjectOperates', res.result);
        getTableData();
      }
    });
  } else if (type === 'delete') {
    clickDel();
  }
};
// 点击删除
const clickDel = () => {
  let selRow = upTable.value?.getCurrentRecord();
  let deleteData = {
    sequenceNbr: selRow?.sequenceNbr,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    qfMajorType:
      tableType.value == 'all' ? 'TOTAL' : nowConstructMajorType.value, //工程专业
  };
  console.log('费用汇总删除', deleteData);

  csProject.gsDeleteCostSummary(deleteData).then(res => {
    console.log('res.status === 200', res.result);

    if (res.result && res.result.status === 500) {
      message.error(res.result.message);
      return;
    } else {
      message.success('删除成功');
      getTableData();
    }
  });
};
const contextMenuClickEvent = ({ menu, row }) => {
  console.log('menu, row', menu, row);
  menu.code === 'delete' ? getCurrentIndex() : getCurrentIndex(row);
  switch (menu.code) {
    case 'copy':
      // 复制
      copyItem(row);
      break;
    case 'delete':
      // 删除
      deleteItem(row);
      break;
    case 'paste':
      // 粘贴
      pasteItem(row);
      break;
    case 'add':
      // 插入
      addItem(row);
      break;
    case 'saveTemplate':
      // 保存模板
      executeOperation('saveTemplate');
      break;
    case 'loadingTemplates':
      // 载入模版
      executeOperation('loadingTemplates');
      break;
  }
};
const currentChange = ({ row }) => {
  currentInfo.value = JSON.parse(JSON.stringify(row));
};
const executeOperation = type => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    unitId: store.currentTreeInfo?.id,
    qfMajorType:
      tableType.value == 'all' ? 'TOTAL' : nowConstructMajorType.value,
  };
  console.log('🚀 ~ executeOperation ~ apiData:', apiData);
  let url =
    type === 'saveTemplate' ? 'exportUnitCostSummary' : 'importUnitCostSummary';
  csProject[url](apiData)
    .then(res => {
      if (res.status !== 200) {
        return message.error(res.message);
      }
      message.success(
        type === 'saveTemplate' ? '保存模板成功！' : '载入模版成功！'
      );
      if (type !== 'saveTemplate') {
        getTableData();
      }
    })
    .catch(err => {
      console.log(err, 'err');
    });
};

const pasteItem = () => {
  if (store.summaryCopyInfo && store.summaryCopyInfo.asideTitle === 'fyhz') {
    let paste = { ...store.summaryCopyInfo.copyInfo };
    paste.category = '';
    operate('paste', paste);
  }
};
const addItem = item => {
  operate('insert', item);
};
const copyItem = item => {
  console.log('复制', item);
  if (item.whetherTax === 1) {
    message.warning('当前行不可复制');
    return;
  }
  store.SET_SUMMARY_COPYINFO({
    copyInfo: { ...item },
    asideTitle: 'fyhz',
  });
  message.success('复制成功');
  //需考虑跨单位工程之间可以复制粘贴
};
const deleteItem = item => {
  if (item.whetherTax === 1) {
    message.warning('当前行不可删除');
    return;
  }
  deleteInfo.value = { ...item };
  Modal.confirm({
    title: '是否确认删除？',
    content: item.adopted
      ? '删除操作将导致费用代号取消引用，是否确定？'
      : '是否删除当前数据行',
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      operate('delete', deleteInfo.value);
    },
    onCancel() {
      focusTable();
    },
  });
};
const headerCellClassName = ({ column }) => {
  if (column.field === 'index') {
    return 'index-bg';
  }
  return null;
};
const cellClassName = ({ $columnIndex, row, column }) => {
  const selectName = selectedClassName({ $columnIndex, row, column });
  if (column.field === 'index') {
    return 'index-bg ' + selectName;
  }
  return selectName;
};

// d多专业的列表
let xGrid = ref(null);
const gridOptions = reactive({
  border: true,
  keepSource: true,
  showOverflow: true,
  id: '',
  height: 500,
  rowConfig: {
    isCurrent: true,
  },
  editConfig: {
    trigger: 'manual',
    mode: 'cell',
  },
  customConfig: {
    storage: true,
  },
  columns: [
    { title: '序号', field: 'dispNo' },
    { title: '名称', field: 'name' },
    { title: '金额', field: 'price' },
    {
      title: '备注',
      field: 'remark',
      editRender: { autofocus: '.vxe-textarea--inner' },
      slots: { edit: 'remark_edit' },
    },
  ],
  data: [],
});

const gridEvents = {
  cellClick(cellData) {
    useCellClearClickEvent(cellData, xGrid.value, '');
  },
  cellDblclick(cellData) {
    if (
      cellData.row.children?.length &&
      ['remark'].includes(cellData.column.field)
    ) {
      return;
    }
    useCellEditDBLClickEvent(cellData, xGrid.value, '');
  },
};

// 修改备注
const updateRemark = (row, field, name) => {
  const $table = xGrid.value;
  // 判断当前单元格有没有被修改
  if (name && !$table.isUpdateByRow(row, name)) {
    return;
  }
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    costSummaryTotal: toRaw(row),
  };

  csProject.updateCostSummaryMajorsTotal(apiData).then(res => {
    console.log('多专业费用汇总修改结果', apiData, res);
    if (res.status !== 500) {
      message.success('修改成功！');
      gridOptions.data.map((item, index) => {
        if (item.sequenceNbr === row.sequenceNbr) {
          isCurrent.value = index;
        }
      });
      getTableData(tableType.value);
    } else if (res.status === 500) {
      message.error(res.message);
      $table.revertData(row, field);
    }
  });
};

// 费率
let showPull = ref(false);
let editRow = ref(null);
const toggleEvent = row => {
  editRow.value = row;
  showPull.value = !showPull.value;
};

const onUseRate = v => {
  editRow.value.rate = v;
  update(editRow.value, 'rate', 'rate');
};

defineExpose({
  getTableData,
  handlerColumns,
  updateColumns,
  getDefaultColumns,
});
</script>
<style lang="scss" scoped>
.pull-down-content {
  width: 623px;
  height: 388px;
  background: rgba(255, 255, 255, 0.39);
  box-shadow: 0px 0px 13px rgba(0, 0, 0, 0.26);
  border: 2px solid #66a2fc;
  padding: 10px 14px;
  ::v-deep(.conBox) {
    height: calc(100% - 40px);
  }
}

.btns {
  position: absolute;
  width: 200px;
  bottom: -20px;
  right: 40%;
  display: flex;
  justify-content: space-around;
}
.multiple-select {
  width: 50px;
  height: 30px;
  line-height: 30px;
  margin-left: -10px;
  text-indent: 10px;
  cursor: pointer;
}
// .content-project {
//   background: #ffffff;
//   height: 100%;
.table-content {
  // width: 100%;
  height: 100%;
  // overflow: hidden;
  background: #ffffff;
  user-select: none;
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  ::v-deep(.vxe-table .index-bg) {
    // background-color: rgba(243, 243, 243, 1);
    background-color: #fff;
  }
}

#summaryUpTable :deep(.vxe-table) {
  .row--current {
    .vxe-body--column {
      background-color: #a6c3fa !important;
    }
    .col--actived {
      background-color: #a6c3fa !important;
    }
    .vxe-input--inner {
      background-color: #a6c3fa !important;
    }
  }
}
// }
</style>
