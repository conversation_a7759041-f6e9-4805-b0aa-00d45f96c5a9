<!--
 * @Descripttion: 设置查看范围
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: wangru
 * @LastEditTime: 2025-04-29 09:40:22
-->
<template>
  <common-modal className="dialog-comm setAggreScope-dialog" @close="cancel()" v-model:modelValue="dialogVisible"
    title="设置查看范围" width="550px" height="550px" :mask="true" show-zoom :lock-view="false" destroy-on-close
    :loading="loading" :loading-config="{
      text: '正在加载中...',
    }">
    <div class="group-content-wrap">
      <div style="margin-bottom: 15px;">您可以通过设置选择查看范围</div>
      <div class="treeBox" v-if="treeData.length > 0">
        <a-tree :defaultExpandAll="true" checkable show-line multiple :tree-data="treeData" @check="importSelect"
          :fieldNames="{ children: 'children', title: 'name', key: 'id' }" :checkedKeys="importCheckedKeys">
        </a-tree>
      </div>
      <p class="footer-box">
        <a-button type="primary" ghost @click="cancel()">取消</a-button>
        <a-button type="primary" @click="handleOk()">确定</a-button>
      </p>

    </div>
  </common-modal>
</template>
<script setup>
import { ref, nextTick, onMounted, computed, reactive } from 'vue';
import jiesuanApi from '@/api/jiesuanApi';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
import xeUtils from "xe-utils";
const route = useRoute();
const emits = defineEmits(['refresh']);
const props = defineProps(['unitIdList', 'isFirstOpen']);
let dialogVisible = ref(false);
const store = projectDetailStore();
let loading = ref(false);
const treeData = ref([]);
const importCheckedKeys = ref([]);
const cancel = (refresh = false) => {
  dialogVisible.value = false;
};
const open = k => {
  dialogVisible.value = true;
  getTreeList();
};
const getTreeList = () => {
  let apiData={
    sequenceNbr:store.currentTreeGroupInfo.constructId
  }
  jiesuanApi.queryIndexViewScopeColl(apiData).then((res) => {
    let data=JSON.parse(JSON.stringify(res.result))
    for(let item of data){
      if(item.viewScopeFlag){
        importCheckedKeys.value.push(item.id)
      }
    }
    treeData.value = xeUtils.toArrayTree(
      Array.isArray(res.result) ? res.result : [res.result],
      {
        children: "children",
        id: "id",
        pid: "parentId",
        sort: "sort",
      }
    );
  });
};
const importSelect = (checkedKeys, { checked, checkedNodes, node, event }) => {
  let importArr=[]
  for(let item of checkedNodes){
    importArr.push(item.id)
  }
  importCheckedKeys.value = importArr;
};
function getAllParentIds(tree, targetId) {
  let result = [];
  
  // 辅助函数：深度优先搜索
  function dfs(nodes, path = []) {
    for (const node of nodes) {
      const currentPath = [...path, node.id];
      // 检查当前节点是否是目标节点
      if (node.id === targetId) {
        // 提取所有父节点ID（不包括自身）
        result = path
        return true; // 找到目标节点，提前返回
      }
      // 如果有子节点，递归查找
      if (node.children && node.children.length > 0) {
        if (dfs(node.children, currentPath)) {
          return true; // 如果子树中找到目标节点，提前返回
        }
      }
    }
    return false;
  }
  dfs(tree);
  return result;
}
// 将树结构数据平铺
function flattenTreeFlatMap(tree, key = 'children') {
  return tree.flatMap(node => {
    const { [key]: children, ...rest } = node;
    return children && children.length 
      ? [rest, ...flattenTreeFlatMap(children, key)] 
      : [rest];
  });
}
// 确认
const handleOk = () => {
  let parentId=[]
  for(let item of importCheckedKeys.value){
    let path=getAllParentIds(treeData.value,item)
    parentId=parentId.concat(path)
  }
  let uniqueArray = [...new Set(parentId)]
  let data=JSON.parse(JSON.stringify(treeData.value))
  let newData=flattenTreeFlatMap(data)
  for(let item of newData){
    let isSel=importCheckedKeys.value.includes(item.id)
    let isParentId=uniqueArray.includes(item.id)
    if(isSel){
      item.viewScopeFlag=true
    }else{
      item.viewScopeFlag=false
    }
    if(isParentId){
      item.halfCheckFlag=true
    }else{
      item.halfCheckFlag=false
    }
  }
  let apiData={
    constructId:store.currentTreeGroupInfo.constructId,
    data:JSON.parse(JSON.stringify(newData))
  }
  console.info('设置查看范围参数',apiData)
  jiesuanApi.setIndexViewScopeColl(apiData).then((res) => {
    if(res.code!==200){
      return message.error(res.message)
    }
    emits('refresh')
    cancel()
  });
};
defineExpose({
  open,
  cancel,
});
</script>

<style lang="scss">
.setAggreScope-dialog {
  //禁用浏览器默认选中
  -webkit-user-select: none;
  /* Safari */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;

  .group-content-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .treeBox {
    height: calc(100% - 50px);
    overflow-x: hidden;
    overflow-y: visible;
  }

  .footer-box {
    width: 100%;
    margin: 0;
    height: 35px;
    text-align: center;

    button {
      margin: 0 15px;
    }
  }
}
</style>
