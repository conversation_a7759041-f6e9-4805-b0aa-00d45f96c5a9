<!-- 预算 局部汇总 -->
<template>
  <common-modal
    className="dialog-comm gsPartialSummary"
    width="1000"
    @close="cancel"
    :mask="false"
    :lockScroll="false"
    :lockView="false"
    show-zoom
    v-model:modelValue="dialogVisible"
    :destroy-on-close="false"
    title="局部汇总"
  >
    <a-spin :spinning="loading">
      <div class="quota-dialog-content">
        <div class="nav-bar">
          <div
            class="left-box"
            v-if="!isPreview"
          >
            <div class="nav-bar-left">
              <div
                class="nav-items"
                @click="tabsChange(i.code)"
                :class="[tabKey == i.code ? 'actives' : '']"
                v-for="(i, k) of tabs"
              >
                {{ i.value }}
              </div>
            </div>
            <div class="filter-box">
              取费文件筛选<a-tooltip placement="top">
                <template #title>
                  操作提示 <br />
                  根据选中范围的取费文件，批量选中应用该取费文件的定额数据
                </template>
                <QuestionCircleOutlined
                  style="color: rgb(40, 124, 250); margin-left: 3px" /></a-tooltip
              ><a-select
                v-model:value="costMajorNameSelect[tabKey]"
                mode="multiple"
                style="width: 410px; margin-left: 6px"
                placeholder="（暂未选择）"
                max-tag-count="responsive"
                :max-tag-text-length="36"
                :max-tag-count="1"
                :allowClear="true"
                @change="dropdownChange"
                :options="costMajorNameOptions[tabKey]"
              >
                <template #maxTagPlaceholder>
                  <div>展开
                    <DownOutlined />
                  </div>
                </template>
              </a-select>
            </div>
          </div>
          <div
            class="nav-bar-left"
            v-else
          >
            <div
              class="nav-items"
              @click="previewTabsChange(i.code)"
              :class="[activeKey == i.code ? 'actives' : '']"
              v-for="(i, k) of previewTabs"
            >
              {{ i.value }}
            </div>
          </div>
          <div class="nav-bar-right" v-if="!isPreview && tabKey !== 'qtxm'">
            <a-button style="font-size: 12px" @click="handleTree(true)">展开所有子目</a-button>
            <a-button style="font-size: 12px" @click="handleTree(false)">折叠所有子目</a-button>
          </div>
        </div>
        <div class="subItem-project custom-tree-table">
          <div
            class="quota-content table-content"
            v-if="dialogVisible"
          >
            <!-- <vxe-grid
              v-show="isPreview"
              v-bind="gridOptionsPreview"
              height="400px"
            >
              <template #ifDonorMaterial_default="{ row }">
                {{ getDonorMaterialText(row.ifDonorMaterial) }}
              </template>
            </vxe-grid> -->
            <s-table
              v-if="isPreview"
              size="small"
              class="s-table"
              bordered
              rowKey="sequenceNbr"
              :columns="gridOptionsPreview.columns"
              :pagination="false"
              height="400px"
              :animateRows="false"
              :delay="200"
              :data-source="gridOptionsPreview.data"
            >
              <template
                #bodyCell="{ text, record: row, index, column, key, openEditor, closeEditor }"
              >
                <div v-if="column.field === 'ifDonorMaterial'">
                  {{ getDonorMaterialText(row.ifDonorMaterial) }}
                </div>
              </template>
            </s-table>
            <s-table
              v-show="!isPreview"
              size="small"
              ref="stableRef"
              class="s-table"
              bordered
              rowKey="sequenceNbr"
              defaultExpandAllRows
              :row-selection="rowSelection"
              :columns="gridOptions.columns"
              :pagination="false"
              height="50vh"
              :animateRows="false"
              :delay="200"
              :rangeSelection="false"
              :data-source="gridOptions.data[tabKey]"
              expand-row-by-click
              :expandedRowKeys="expandedRowKeys[tabKey]"
              @expandedRowsChange="expandedRowsChange"
              :custom-cell="customCell"
              :custom-header-cell="customHeaderCell"
              :rowClassName="(row, index) => rowClassName(row, index, gridOptions.data[tabKey])"
            >
            </s-table>
          </div>
        </div>

        <div class="quota-footer">
          <div class="tips" v-if="isPreview"></div>
          <div class="tips" v-else>
            <icon-font class="icon" type="icon-querenshanchu" style="margin-right: 5px" />
            <div>
              <div>点击预览可查看当前局部汇总结果</div>
              <div>点击生成则将所选择的局部汇总数据保存为一个新的单位工程文件</div>
            </div>
          </div>

          <div>
            <a-button type="primary" :disabled="loading" @click="preview" v-if="!isPreview"
              >预览</a-button
            >

            <a-button
              type="primary"
              @click="exportData"
              :disabled="loading"
              style="margin-left: 20px"
              >生成</a-button
            >
            <a-button style="margin-left: 20px" @click="back" v-if="isPreview">返回</a-button>
          </div>
        </div>
      </div>
    </a-spin>
  </common-modal>
</template>
<script setup>
import { message } from 'ant-design-vue';
import { ref, reactive, watch, shallowRef, toRaw, onDeactivated, computed } from 'vue';
import { useRoute } from 'vue-router';
import csProject from '@/api/projectDetail';

import api from '@/api/csProject';
import xeUtils from 'xe-utils';
import infoMode from '@/plugins/infoMode.js';
import { projectDetailStore } from '@/store/projectDetail';
import { useReversePosition } from '@/hooks/useReversePosition.js';
import { QuestionCircleOutlined, DownOutlined } from '@ant-design/icons-vue';
import {
  isDeType,
  getDonorMaterialText,
} from '@/views/projectDetail/customize/HumanMachineSummary/tableColumns.js';
import {
  customCell,
  rowClassName,
  customHeaderCell,
} from '@/views/projectDetail/customize/subItemProject/classAndStyleMethod';
const store = projectDetailStore();
const emits = defineEmits(['closeDialog']);
let loading = ref(false);
let dialogVisible = ref(false);
let activeKey = ref('unitCostSummary');
const previewTabs = ref([
  {
    code: 'unitCostSummary',
    value: '费用汇总',
  },
  {
    code: 'rcjList',
    value: '人材机汇总',
  },
]);
let tabKey = ref('fbfx');
const tabs = ref([
  {
    code: 'fbfx',
    value: '分部分项',
  },
  {
    code: 'csxm',
    value: '措施项目',
  },
  {
    code: 'qtxm',
    value: '其他项目',
  },
]);
const tabsChange = code => {
  tabKey.value = code;
  gridOptions.columns = columnsObj[code];
  // getTreeList();
};

const props = defineProps({
  deRowId: {
    type: String,
    default: '',
  },
});

const columnsObj = {
  fbfx: [
    { title: '序号', field: 'dispNo', dataIndex: 'dispNo', align: 'left' },
    {
      title: '项目编码',
      field: 'bdCode',
      dataIndex: 'bdCode',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '名称',
      field: 'name',
      dataIndex: 'name',
      autoHeight: true,
      align: 'center',
    },
    {
      title: '项目特征',
      field: 'projectAttr',
      dataIndex: 'projectAttr',
      autoHeight: true,
      align: 'center',
    },
    {
      title: '单位',
      field: 'unit',
      dataIndex: 'unit',
      width: '80px',
      align: 'center',
    },
    {
      title: '工程量',
      dataIndex: 'quantity',
      field: 'quantity',
      align: 'center',
    },
    {
      title: '综合单价',
      dataIndex: 'price',
      field: 'price',
      align: 'center',
    },
    {
      title: '综合合价',
      dataIndex: 'total',
      field: 'total',
      align: 'center',
    },
  ],
  csxm: [
    { title: '序号', field: 'dispNo', dataIndex: 'dispNo', align: 'left' },
    {
      title: '项目编码',
      field: 'fxCode',
      dataIndex: 'fxCode',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '名称',
      field: 'name',
      dataIndex: 'name',
      autoHeight: true,
      align: 'center',
    },
    {
      title: '项目特征',
      field: 'projectAttr',
      dataIndex: 'projectAttr',
      autoHeight: true,
      align: 'center',
    },
    {
      title: '单位',
      field: 'unit',
      dataIndex: 'unit',
      width: '80px',
      align: 'center',
    },
    {
      title: '工程量',
      dataIndex: 'quantity',
      field: 'quantity',
      align: 'center',
    },
    {
      title: '综合单价',
      dataIndex: 'price',
      field: 'price',
      align: 'center',
    },
    {
      title: '综合合价',
      dataIndex: 'total',
      field: 'total',
      align: 'center',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      field: 'remark',
      align: 'center',
      autoHeight: true,
    },
  ],
  qtxm: [
    { title: '序号', field: 'dispNo', dataIndex: 'dispNo', align: 'center' },
    {
      title: '名称',
      dataIndex: 'extraName',
      field: 'extraName',
      autoHeight: true,
      align: 'center',
    },
    {
      title: '计算基数',
      dataIndex: 'calculationBase',
      field: 'calculationBase',
      autoHeight: true,
      align: 'center',
    },
    {
      title: '金额',
      dataIndex: 'total',
      field: 'total',
      align: 'center',
    },
    {
      title: '费用类别',
      dataIndex: 'type',
      field: 'type',
      align: 'center',
    },
    {
      title: '备注',
      dataIndex: 'description',
      field: 'description',
      autoHeight: true,
      align: 'center',
    },
  ],
};
const gridOptions = reactive({
  border: true,
  keepSource: true,
  height: '100%',
  showOverflow: true,
  id: 'toolbar_demo_1',
  rowConfig: {
    isCurrent: true,
  },
  customConfig: {
    storage: true,
  },
  columns: columnsObj[tabKey.value],
  data: {
    fbfx: [],
    csxm: [],
    qtxm: [],
  },
});

const gridOptionsPreview = reactive({
  border: true,
  keepSource: true,
  showOverflow: true,
  height: '100%',
  id: 'toolbar_demo_2',
  rowConfig: {
    isCurrent: true,
  },
  customConfig: {
    storage: true,
  },
  columns: [
    { title: '序号', field: 'dispNo', dataIndex: 'dispNo', align: 'center' },
    { title: '费用代号', field: 'code', align: 'center' },
    { title: '名称', field: 'name', align: 'center', autoHeight: true },
    { title: '计算基数', field: 'calculateFormula', align: 'center' },
    { title: '费率（%）', field: 'rate', align: 'center' },
    { title: '金额', field: 'price', align: 'center' },
    { title: '费用类别', field: 'category', align: 'center' },
    { title: '备注', field: 'remark', align: 'center', autoHeight: true },
  ],
  data: [],
});

const selectState = reactive({
  selectedRowKeys: {
    fbfx: [],
    csxm: [],
    qtxm: [],
  },
});
let costMajorNameSelect = ref({
  fbfx: [],
  csxm: [],
});
let costMajorNameOptions = ref({
  fbfx: [],
  csxm: [],
});
let currentInfo = ref();

const stableRef = ref();
const rowSelection = computed(() => {
  const checkboxDisabled = record => {
    if (tabKey.value === 'qtxm') {
      return false;
    } else {
      if (tabKey.value === 'csxm' && csxmParams.defaultSelectKeys.includes(record.sequenceNbr)) {
        return true;
      }
      return !['0', '00', '01', '02', '03', '04'].includes(record.kind);
    }
  };
  return {
    hideSelectAll: true,
    fixed: true,
    checkStrictly: false,
    selectedRowKeys: selectState.selectedRowKeys[tabKey.value],
    getCheckboxProps: record => ({
      disabled: checkboxDisabled(record),
    }),
    onSelect: (record, selected, selectedRows, nativeEvent) => {
      currentInfo.value = record;
      if (tabKey.value === 'qtxm') {
        if (record.sequenceNbr === undefined) {
          selectState.selectedRowKeys[tabKey.value] = selected
            ? gridOptions.data[tabKey.value].map(item => item.sequenceNbr)
            : [];
          return;
        }
      }
      // // 选中定额值自动添加其下级
      const keys = collectSequenceNbrs(record.children || []);
      if (selected) {
        const newKeys = Array.from(
          new Set([...selectState.selectedRowKeys[tabKey.value], record.sequenceNbr, ...keys])
        );
        selectState.selectedRowKeys[tabKey.value] = newKeys;
      } else {
        const filterList = selectState.selectedRowKeys[tabKey.value].filter(
          item => ![record.sequenceNbr, ...keys, record.parentId].includes(item)
        );
        const newKeys = Array.from(
          new Set([...filterList, ...(tabKey.value === 'csxm' ? csxmParams.defaultSelectKeys : [])])
        );
        selectState.selectedRowKeys[tabKey.value] = newKeys;
      }
    },
  };
});

function collectSequenceNbrs(tree) {
  let sequenceNbrs = new Set();
  function collect(node) {
    if (node.sequenceNbr !== undefined) {
      sequenceNbrs.add(node.sequenceNbr);
    }
    if (node.children) {
      node.children?.forEach(child => collect(child));
    }
  }

  tree?.forEach(node => collect(node));

  return sequenceNbrs;
}

let previewDataInfo = ref({});
const previewTabsChange = code => {
  activeKey.value = code;
  if (code == 'rcjList') {
    // 人材机列表
    const columns = [
      // { title: '序号', type: 'seq' },
      { title: '序号', field: 'dispNo', dataIndex: 'dispNo', align: 'center' },
      {
        title: '材料编码',
        field: 'materialCode',
        dataIndex: 'materialCode',
        autoHeight: true,
        align: 'center',
      },
      { title: '类型', field: 'type', dataIndex: 'type', align: 'center' },
      {
        title: '名称',
        field: 'materialName',
        dataIndex: 'materialName',
        autoHeight: true,
        align: 'center',
        width: '120px',
      },
      {
        title: '规格型号',
        field: 'specification',
        dataIndex: 'specification',
        width: '80px',
        autoHeight: true,
        align: 'center',
      },
      { title: '单位', field: 'unit', dataIndex: 'unit', align: 'center' },
      {
        title: '数量',
        field: 'totalNumber',
        dataIndex: 'totalNumber',
        width: '80px',
        align: 'center',
      },
      {
        title: isDeType('12')
          ? '定额价'
          : Number(store.taxMade) === 1
          ? '不含税基期价'
          : '含税基期价',
        field: 'dePrice',
        dataIndex: 'dePrice',
        width: '80px',
        align: 'center',
      },
      {
        title: '市场价',
        field: 'marketPrice',
        dataIndex: 'marketPrice',
        width: '80px',
        align: 'center',
      },
      {
        title: '不含税市场价',
        field: 'priceMarket',
        dataIndex: 'priceMarket',
        width: '80px',
        align: 'center',
      },
      {
        title: '含税市场价',
        field: 'priceMarketTax',
        dataIndex: 'priceMarketTax',
        width: '80px',
        align: 'center',
      },
      {
        title: '市场价合计',
        dataIndex: 'total',
        field: 'total',
        width: '80px',
        align: 'center',
      },
      {
        title: '不含税市场价合计',
        dataIndex: 'priceMarketTotal',
        field: 'priceMarketTotal',
        width: '80px',
        align: 'center',
      },
      {
        title: '价差合计',
        field: 'priceDifferencSum',
        dataIndex: 'priceDifferencSum',
        width: '80px',
        align: 'center',
      },

      {
        title: '供应方式',
        field: 'ifDonorMaterial',
        width: '80px',
        dataIndex: 'ifDonorMaterial',
        align: 'center',
      },
      {
        title: '产地',
        field: 'producer',
        dataIndex: 'producer',
        autoHeight: true,
        align: 'center',
      },
      {
        title: '厂家',
        dataIndex: 'manufactor',
        field: 'manufactor',
        autoHeight: true,
        align: 'center',
      },
      {
        title: '质量等级',
        dataIndex: 'qualityGrade',
        field: 'qualityGrade',
        align: 'center',
      },
      {
        title: '品牌',
        dataIndex: 'brand',
        field: 'brand',
        autoHeight: true,
        align: 'center',
      },
    ];
    gridOptionsPreview.columns = columns.filter(item => {
      const { field } = item;
      if (['priceMarketTotal'].includes(field) && Number(store.taxMade) === 0) {
        // 'priceMarket',如果是不含税并且是简易计税的话，不显示;一般-不含税
        return false;
      }
      if (isDeType('12') && ['priceMarketTax', 'priceMarket', 'priceMarketTotal'].includes(field)) {
        // 22de 显示税率\含税市场价合计\不含税市场价合计\含税市场价\不含税市场价
        return false;
      }
      if (isDeType('22')) {
        if (['marketPrice', 'total'].includes(field)) {
          // 市场价、市场价合价、除税系数22不显示
          return false;
        }
      }
      return true;
    });
  } else {
    gridOptionsPreview.columns = [
      { title: '序号', field: 'dispNo', dataIndex: 'dispNo', align: 'center' },
      {
        title: '费用代号',
        field: 'code',
        dataIndex: 'code',
        autoHeight: true,
        align: 'center',
      },
      {
        title: '名称',
        field: 'name',
        dataIndex: 'name',
        autoHeight: true,
        align: 'center',
      },
      {
        title: '计算基数',
        dataIndex: 'calculateFormula',
        field: 'calculateFormula',
        align: 'center',
      },
      {
        title: '基数说明',
        dataIndex: 'instructions',
        field: 'instructions',
        autoHeight: true,
        align: 'center',
      },
      { title: '费率（%）', dataIndex: 'rate', field: 'rate', align: 'center' },
      { title: '金额', dataIndex: 'price', field: 'price', align: 'center' },
      {
        title: '备注',
        dataIndex: 'remark',
        field: 'remark',
        autoHeight: true,
        align: 'center',
      },
    ];
  }
  gridOptionsPreview.data = previewDataInfo.value[activeKey.value];
};

let isPreview = ref(false);
let expandedRowKeys = ref({
  fbfx: [],
  csxm: [],
  qtxm: [],
});
let defaultExpandAllRows = shallowRef({
  fbfx: [],
  csxm: [],
  qtxm: [],
});
let defaultOtherNotExpandRows = shallowRef({
  fbfx: [],
  csxm: [],
  qtxm: [],
});
let selectData = shallowRef({
  fbfx: [],
  csxm: [],
  qtxm: [],
});

// 获取分类汇总信息
let cellectType = ref({});
const loopTree = (list, callback) => {
  let data = JSON.parse(JSON.stringify(list));
  for (let item of data) {
    callback(item);
    if (item.children?.length > 0) {
      loopTree(item.children, callback);
    }
  }
};
let csxmParams = reactive({
  deMap: {},
  defaultSelectKeys: [],
  // deSelectSequenceNbr: [],
});
const getTreeList = async (refresh = false) => {
  function resultHandler(dataList, key) {
    let options = [];
    dataList.forEach(item => {
      if (['0', '01', '02'].includes(item.kind)) {
        defaultExpandAllRows.value[key].push(item.sequenceNbr);
      } else {
        defaultOtherNotExpandRows.value[key].push(item.sequenceNbr);
      }
      if (item.costMajorName && options.findIndex(t => t.value === item.costMajorName) === -1) {
        options.push({ label: item.costMajorName, value: item.costMajorName });
      }
      if (key === 'csxm' && item.kind === '04') {
        csxmParams.deMap[item.sequenceNbr] = item;
        if (item.isCostDe === 1) {
          csxmParams.defaultSelectKeys = [
            ...csxmParams.defaultSelectKeys,
            item.sequenceNbr,
            item.parentId,
          ];
          selectState.selectedRowKeys['csxm'] = JSON.parse(
            JSON.stringify(csxmParams.defaultSelectKeys)
          );
        }
      }
    });
    costMajorNameOptions.value[key] = options;

    expandedRowKeys.value[key] = xeUtils.clone(defaultExpandAllRows.value[key], true);
    selectData.value[key] = xeUtils.clone(defaultExpandAllRows.value[key], true);
    console.log('🚀 ~ resultHandler ~ dataList:', dataList);
    gridOptions.data[key] = xeUtils.toArrayTree(dataList, {
      key: 'sequenceNbr',
      parentKey: 'parentId',
    });
    // // 处理整个树
    gridOptions.data[key].forEach(node => {
      removeEmptyChildren(node);
    });
    console.log(
      '🚀 ~ csProject.getDeAllDepth ~ res.result:',
      gridOptions.data,
      costMajorNameOptions.value
    );
  }
  let formData = {
    constructId: currentNewIdObj.value.constructId,
    singleId: currentNewIdObj.value.singleId,
    unitId: currentNewIdObj.value.unitId,
    sequenceNbr: '',
    isAllFlag: false,
    pageSize: 300000,
    pageNum: 1,
    colorList: [],
    hierachy: 'none',
  };
  console.log('🌶index.vue|568====>', formData);
  csProject.queryBranchDataByFbIdV1(formData).then(res => {
    resultHandler(res.result.data, 'fbfx');
  });
  csProject.itemPage(formData).then(res => {
    resultHandler(res.result.data, 'csxm');
  });

  const params = {
    constructId: currentNewIdObj.value.constructId,
    singleId: currentNewIdObj.value.singleId,
    unitId: currentNewIdObj.value.unitId,
    levelType: store.currentTreeInfo?.levelType,
  };
  console.log('🌶index.vue|542====>', params);
  api.getOtherProjectList(params).then(res => {
    console.log('🌶index.vue|542====>', res);
    if (res.code === 200) {
      gridOptions.data['qtxm'] = res.result;
      selectData.value['qtxm'] = res.result;
      selectState.selectedRowKeys['qtxm'] = res.result.map(item => item.sequenceNbr);
    }
  });
  console.log('getTreeList', refresh);
};

const dropdownChange = xeUtils.debounce(() => {
  if (costMajorNameOptions.value[tabKey.value].length) {
    let keys =
      tabKey.value === 'csxm' ? JSON.parse(JSON.stringify(csxmParams.defaultSelectKeys)) : [];
    loopTree(gridOptions.data[tabKey.value], item => {
      if (costMajorNameSelect.value[tabKey.value].includes(item.costMajorName)) {
        keys.push(item.sequenceNbr);
        if (item.children && item.children.length) {
          keys = Array.from(new Set(keys.concat(item.children.map(t => t.sequenceNbr))));
        }
      }
    });
    selectState.selectedRowKeys[tabKey.value] = keys;
  }
}, 500);

const resetHandle = () => {
  selectState.selectedRowKeys = {
    fbfx: [],
    csxm: [],
    qtxm: [],
  };
  costMajorNameSelect.value = {
    fbfx: [],
    csxm: [],
  };
  costMajorNameOptions.value = {
    fbfx: [],
    csxm: [],
  };
  csxmParams.deMap = {};
};
// // 处理树结构
// const handleDataList = (tree) =>{
//   for(let i in tree){
//     if(tree[i].type=='05'){
//       tree.splice(i, 1)
//     }
//     if(tree[i].children>0){
//       handleDataList(tree[i].children)
//     }
//   }
// }
function removeEmptyChildren(node) {
  if (node.children && node.children.length === 0) {
    delete node.children; // 或者 node.children = undefined;
  } else if (node.children) {
    node.children.forEach(child => {
      removeEmptyChildren(child);
    });
  }
}
const getCsxmAWFDeList = csxm => {
  // 后端要求措施项目定额传编码和名称
  const csxmDeList = csxm
    .map(item => {
      const de = csxmParams.deMap[item];
      if (de) {
        return { fxCode: de.fxCode, name: de.name, isCostDe: de.isCostDe };
      }
      return null;
    })
    .filter(item => item && item.isCostDe === 1);
  return csxmDeList || [];
};
const preview = async () => {
  const { fbfx, csxm, qtxm } = toRaw(selectState.selectedRowKeys);
  const params = {
    constructId: currentNewIdObj.value.constructId,
    singleId: currentNewIdObj.value.singleId,
    unitId: currentNewIdObj.value.unitId,
    fbfxIdList: fbfx,
    csxmIdList: csxm,
    qtxmIdList: qtxm.filter(item => item),
    csxmAWFDeList: getCsxmAWFDeList(csxm),
  };
  console.log('选中的数据', params);
  loading.value = true;
  try {
    let resConfig = await csProject.previewUnitPartialSummary(params);
    console.log('🚀 ~ preview ~ resConfig:', resConfig);
    previewDataInfo.value = resConfig.result;
    activeKey.value = 'unitCostSummary';
    previewTabsChange(activeKey.value);
    isPreview.value = true;
    loading.value = false;
  } catch (error) {
    console.log('🚀 ~ preview ~ error:', error);
    loading.value = false;
  }
};

const back = async () => {
  // 关闭接口+查询接口
  await csProject.cancelUnitPartialSummary({
    constructId: currentNewIdObj.value.constructId,
    singleId: currentNewIdObj.value.singleId,
    unitId: currentNewIdObj.value.unitId,
  });
  await queryData();
};

//导出
const exportData = () => {
  const { fbfx, csxm, qtxm } = toRaw(selectState.selectedRowKeys);
  const params = {
    constructId: currentNewIdObj.value.constructId,
    singleId: currentNewIdObj.value.singleId,
    unitId: currentNewIdObj.value.unitId,
    fromConstructId: store.currentTreeGroupInfo?.constructId,
    fromSingleId: store.currentTreeGroupInfo?.singleId,
    fromUnitId: store.currentTreeInfo?.id,
    fbfxIdList: fbfx,
    csxmIdList: csxm,
    qtxmIdList: qtxm.filter(item => item),
    csxmAWFDeList: getCsxmAWFDeList(csxm),
  };
  console.log('选中的数据', params);
  csProject.createUnitPartialSummary(params).then(res => {
    console.log('🌶index.vue|663====>', res);
    if (res.result) {
      message.success('生成成功！');
    }
  });
};
const cancel = () => {
  dialogVisible.value = false;
  csProject.cancelUnitPartialSummary({
    constructId: currentNewIdObj.value.constructId,
    singleId: currentNewIdObj.value.singleId,
    unitId: currentNewIdObj.value.unitId,
  });
  resetHandle();
  emits('closeDialog');
};

const handleTree = type => {
  if (type) {
    expandedRowKeys.value[tabKey.value] = [
      ...xeUtils.clone(defaultExpandAllRows.value[tabKey.value], true),
      ...xeUtils.clone(defaultOtherNotExpandRows.value[tabKey.value], true),
    ];
  } else {
    // let arr = [];
    // handleTreeDe(gridOptions.data[tabKey.value], arr);
    expandedRowKeys.value[tabKey.value] = xeUtils.clone(
      defaultExpandAllRows.value[tabKey.value],
      true
    );
    // expandedRowKeys.value = [gridOptions.data[0]?.sequenceNbr]
  }
};

let currentNewIdObj = ref({});
const open = async data => {
  dialogVisible.value = true;
  queryData(true);
};
const queryData = async (refresh = false) => {
  const params = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
  };
  const newUnit = await csProject.unitPartialSummary(params);
  console.log('🌶index.vue|759====>', newUnit);
  currentNewIdObj.value = newUnit.result;
  isPreview.value = false;
  getTreeList(refresh);
};
const expandedRowsChange = rows => {
  expandedRowKeys.value[tabKey.value] = rows;
};

defineExpose({ open, cancel });
</script>

<style lang="scss" src="@/assets/css/subItemProject.scss" scoped></style>
<style lang="scss" scoped>
@use '../../views/projectDetail/customize/subItemProject/s-table.scss';
.subItem-project {
  :deep(.surely-table-row-expand-icon) {
    border-radius: 50%;
    border-color: #87b2f2;
    background: transparent;
    &::after {
      top: 4px;
      bottom: 4px;
      color: #87b2f2;
    }
    &::before {
      left: 4px;
      right: 4px;
      color: #87b2f2;
    }
  }
}

.gsPartialSummary {
  .vxe-icon-minus,
  .vxe-icon-add {
    color: #87b2f2;
    font-size: 9px;
    position: relative;
    left: -8px;
    top: -1px;
    border: 1px solid #87b2f2;
  }
  .surely-table {
    font-size: 12px;
  }
  .surely-table-wrapper {
    width: 100%;
    overflow: hidden;
    font-size: 12px;
  }
  .vxe-modal--content {
    padding-bottom: 15px !important;
  }

  .quota-dialog-content {
    width: 100%;
    overflow: hidden;
    .quota-header {
      margin-bottom: 12px;
      display: flex;
      align-items: center;

      span {
        color: rgba(96, 96, 96, 1);
        margin-left: 30px;
        &:first-child {
          margin-left: 0;
        }
        b {
          font-weight: 400;
          color: rgba(0, 0, 0, 1);
        }
      }
    }

    .nav-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
      .left-box {
        flex: 1;
        display: flex;
      }
      .filter-box {
        margin-left: 18px;
        display: flex;
        align-items: center;
        padding-right: 20px;
        min-width: 0;
        flex: 1;
        ::v-deep(.ant-select-selector) {
          overflow: hidden;
          .ant-select-selection-overflow {
            flex-wrap: nowrap;
          }
          .ant-select-selection-overflow-item {
            opacity: 1 !important;
            height: auto !important;
            overflow: auto !important;
            order: unset !important;
            position: unset !important;
          }
          .ant-select-selection-overflow-item-rest {
            position: absolute !important;
            right: -26px !important;
            background-color: #fff;
            .ant-select-selection-item {
              background-color: #fff;
              border: none;
            }
          }
        }
      }
      .nav-bar-left {
        display: flex;
        align-items: center;
        border-top: 1px solid rgba(217, 217, 217, 1);
        border-bottom: 1px solid rgba(217, 217, 217, 1);
        .nav-items {
          padding: 5px 10px;
          text-align: center;
          font-weight: 400;
          font-size: 12px;
          font-weight: 400;
          color: #000000;
          cursor: pointer;
          border-left: 1px solid rgba(217, 217, 217, 1);
          &:last-child {
            border-right: 1px solid rgba(217, 217, 217, 1);
          }
          &.actives {
            color: #ffffff;
            background: rgba(40, 124, 250, 1);
            border-radius: 2px 0px 0px 2px;
            border-color: rgba(40, 124, 250, 1);
          }
        }
      }
    }
  }

  .quota-content {
    width: 100%;
    overflow: hidden;
  }

  .quota-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;
    .tips {
      display: flex;
      align-items: baseline;
      .icon {
        margin-top: 3px;
      }
      div {
        font-size: 12px;
        line-height: 18px;
        font-weight: 400;
        color: #2a2a2a;
      }
    }
  }
}
</style>
