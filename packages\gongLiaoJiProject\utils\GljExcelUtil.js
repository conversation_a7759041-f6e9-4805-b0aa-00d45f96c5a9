// exceljs 所需的 polyfills
// require('core-js/modules/es.promise');
// require('core-js/modules/es.string.includes');
// require('core-js/modules/es.object.assign');
// require('core-js/modules/es.object.keys');
// require('core-js/modules/es.symbol');
// require('core-js/modules/es.symbol.async-iterator');
// require('regenerator-runtime/runtime');

// import ExcelJS from 'exceljs';
const ExcelJS = require('exceljs');
const CellVo = require("../vo/CellVo");
const SheetStyle = require("../vo/SheetStyle");
const {ObjectUtils} = require("../utils/ObjectUtils");
const GljExcelEnum = require("../enums/GljExcelEnum");
const {DateUtils} = require("../utils/DateUtils");
const XLSX = require('xlsx');
const {NumberUtil} = require("../utils/NumberUtil");
const _ = require('lodash');
const EE = require('../../../core/ee');
const DeTypeConstants = require("../constants/DeTypeConstants");
const {GljExcelOperateUtil} = require("./GljExcelOperateUtil");
const Decimal = require("decimal.js");
const BranchProjectLevelConstant = require("../constants/BranchProjectLevelConstant");
class GljExcelUtil {

    constructor() {
        this.VER_2007 = 2007;
        this.VER_2003 = 2003;
    }

    /**
     * 判断cell是否在某个合并单元格中，如果存在返回合并单元格名字
     * @param merges
     * @param cell
     * @returns fan
     */
    getMergeName(merges, cell) {
        let result = null;
        let {row, col} = cell;

        for (let r in merges) {
            let model = merges[r].model;
            if (row >= model.top && row <= model.bottom && col >= model.left && col <= model.right) {
                result = r;
                break;
            }
        }

        return result;
    }

    /**
     * 找到sheet中对应值的cell定位
     * @param merges
     * @param cell
     * @returns fan
     */
    findValueCell(worksheet, value) {
        let results = [];
        let merges = worksheet.merges;

        //定义一个存放数据值和cell的对象
        function CellValue(cell, value) {
            this.cell = cell;
            this.value = value;
        }

        worksheet.eachRow(row => {
            let rowContentMap = new Map();
            row.eachCell(cell => {
                let {isMerged, address, value, col, row} = cell;
                let mergeName = null;
                if (isMerged) {
                    //获取合并单元格的名称
                    mergeName = this.getMergeName(merges, cell);
                    //如果合并单元格的当前具体位置与合并位置不一致 则值置为null
                    if (mergeName != null && mergeName != address) {
                        value = null;
                    }
                }
                //每一行有值的内容 都放进去  上面是过滤了没有值的合并单元格
                let cellValue = new CellValue(cell, value);
                results.push(cellValue);
            })
        });
        let filter = results.filter(k => k.value == value);

        return filter[0];
    }

    /**
     * 找到sheet中对应值的cell定位
     * @param merges
     * @param cell
     * @returns fan
     */
    findValueCellList(worksheet, value) {
        let results = [];
        let merges = worksheet.merges;

        //定义一个存放数据值和cell的对象
        function CellValue(cell, value) {
            this.cell = cell;
            this.value = value;
        }

        worksheet.eachRow(row => {
            let rowContentMap = new Map();
            row.eachCell(cell => {
                let {isMerged, address, value, col, row} = cell;
                let mergeName = null;
                if (isMerged) {
                    //获取合并单元格的名称
                    mergeName = this.getMergeName(merges, cell);
                    //如果合并单元格的当前具体位置与合并位置不一致 则值置为null
                    if (mergeName != null && mergeName != address) {
                        value = null;
                    }
                }
                //每一行有值的内容 都放进去  上面是过滤了没有值的合并单元格
                let cellValue = new CellValue(cell, value);
                results.push(cellValue);
            })
        });
        let filter = results.filter(k => k.value == value);

        return filter;
    }

    /**
     * 找到sheet中对应值的cell定位
     * @param merges
     * @param cell
     * @returns fan
     */
    async findRowNumOnlySingleValue(worksheet, value) {
        let results = [];
        let merges = worksheet.merges;

        //定义一个存放数据值和cell的对象
        function CellValue(cell, value) {
            this.cell = cell;
            this.value = value;
        }

        worksheet.eachRow(row => {
            row.eachCell(cell => {
                let {isMerged, address, value, col, row} = cell;
                let mergeName = null;
                if (isMerged) {
                    //获取合并单元格的名称
                    mergeName = this.getMergeName(merges, cell);
                    //如果合并单元格的当前具体位置与合并位置不一致 则值置为null
                    if (mergeName != null && mergeName != address) {
                        value = null;
                    }
                }
                //每一行有值的内容 都放进去  上面是过滤了没有值的合并单元格
                let cellValue = new CellValue(cell, value);
                results.push(cellValue);
            })
        });

        let filter = results.filter(k => {
            let numTotal = 0;
            for (let i = 0; i < worksheet._rows[k.cell._row.number - 1]._cells.length; i++) {
                if (ObjectUtils.isNotEmpty(worksheet._rows[k.cell._row.number - 1]._cells[i].value)) {
                    numTotal++;
                }
            }
            if (numTotal == 1 && k.value == value) {
                return true;
            }
            return false;
        });

        return ObjectUtils.isNotEmpty(filter[0])?filter[0].cell._row.number:null;
    }


    /**
     * 找到sheet中对应值的cell定位
     * @param merges
     * @param cell
     * @returns fan
     */
    async findRowNumOnlySingleValueForPointRange(worksheet, value,startCol,endCol) {
        let results = [];
        let merges = worksheet.merges;

        //定义一个存放数据值和cell的对象
        function CellValue(cell, value) {
            this.cell = cell;
            this.value = value;
        }

        worksheet.eachRow(row => {
            row.eachCell(cell => {
                let {isMerged, address, value, col, row} = cell;
                if (!(col>= startCol && col<= endCol)) return;
                let mergeName = null;
                if (isMerged) {
                    //获取合并单元格的名称
                    mergeName = this.getMergeName(merges, cell);
                    //如果合并单元格的当前具体位置与合并位置不一致 则值置为null
                    if (mergeName != null && mergeName != address) {
                        value = null;
                    }
                }
                //每一行有值的内容 都放进去  上面是过滤了没有值的合并单元格
                let cellValue = new CellValue(cell, value);
                results.push(cellValue);
            })
        });

        let filter = results.filter(k => {
            let numTotal = 0;
            for (let i = startCol-1; i < endCol; i++) {
                if (ObjectUtils.isEmpty(worksheet._rows[k.cell._row.number - 1]._cells[i]._value.master) && ObjectUtils.isNotEmpty(worksheet._rows[k.cell._row.number - 1]._cells[i].value)) {
                    numTotal++;
                }
            }
            if (numTotal == 1 && k.value == value) {
                return true;
            }
            return false;
        });

        return filter[0].cell._row.number;
    }

    /**
     * 找到sheet中包含该值的cell定位
     * @param merges
     * @param cell
     * @returns fan
     */
    findContainValueCell(worksheet, containValue) {
        let results = [];
        let merges = worksheet.merges;

        //定义一个存放数据值和cell的对象
        function CellValue(cell, value) {
            this.cell = cell;
            this.value = value;
        }

        worksheet.eachRow(row => {
            let rowContentMap = new Map();
            row.eachCell(cell => {
                let {isMerged, address, value, col, row} = cell;
                let mergeName = null;
                if (isMerged) {
                    //获取合并单元格的名称
                    mergeName = this.getMergeName(merges, cell);
                    //如果合并单元格的当前具体位置与合并位置不一致 则值置为null
                    if (mergeName != null && mergeName != address) {
                        value = null;
                    }
                }
                //每一行有值的内容 都放进去  上面是过滤了没有值的合并单元格
                let cellValue = new CellValue(cell, value);
                results.push(cellValue);
            })
        });
        let filter = results.filter(k => {
            if (k.value == null || typeof k.value != "string") return false;
            try {
                return k.value.includes(containValue);
            } catch (e) {
                console.log(e.stackTrace);
            }
        });
        return filter;
    }


    getMerges(workbook, sheetName) {
        const worksheet = workbook.getWorksheet(sheetName);
        return worksheet._merges;
    }

    getSheet(workbook, sheetName) {
        return workbook.getWorksheet(sheetName);
    }

    async read(excelPath, sheetName) {
        // read from a file
        let workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(excelPath)

        // 获取第一个工作表
        const worksheet = workbook.getWorksheet(sheetName);
        return worksheet;
    }

    async readToWorkBook(excelPath, options = {}) {
        // read from a file
        if (options.version === this.VER_2003) {
            let workbook = XLSX.readFile(excelPath);
            return workbook;
        } else {
            let workbook = new ExcelJS.Workbook();
            await workbook.xlsx.readFile(excelPath)
            return workbook;
        }
    }

    async readSheetContent(excelPath, sheetName, options = {}) {
        // read from a file
        return this.readSheetContentByWorkBook(await this.readToWorkBook(excelPath, options), sheetName, options)
    }

    readSheetContentByWorkBook(workbook, sheetName, options = {}) {
        if (options.version === this.VER_2003) {
            return this.readSheetContentByWorkBook2003(workbook, sheetName);
        } else {
            return this.readSheetContentByWorkBook2007(workbook, sheetName);
        }
    }

    readSheetContentByWorkBook2003(workbook, sheetName) {
        //console.log(workbook);
        let sheetContent = workbook.Sheets[sheetName];
        if (ObjectUtils.isEmpty(sheetContent)) {
            return [];
        }
        let results = [];

        for (let k in sheetContent) {
            if (k.startsWith("!") || !sheetContent.hasOwnProperty(k)) {
                continue;
            }

            let matches = k.match(/[a-zA-Z]+/);
            if (ObjectUtils.isEmpty(matches)) {
                continue;
            }
            let colName = matches[0];
            let row = k.substring(colName.length);
            if (ObjectUtils.isEmpty(colName) || ObjectUtils.isEmpty(row)) {
                continue;
            }

            colName = colName.toLowerCase();
            let col = colName.charCodeAt(0) - 97 + 1;

            results[row] = results[row] || new Map();
            results[row].set(col, sheetContent[k].w);
        }

        return results.filter(m => ObjectUtils.isNotEmpty(m));
    }

    readSheetContentByWorkBook2007(workbook, sheetName) {
        let results = [];
        // 获取第一个工作表
        const worksheet = workbook.getWorksheet(sheetName);
        if (ObjectUtils.isEmpty(worksheet)) {
            return results;
        }

        const merges = worksheet._merges;

        worksheet.eachRow(row => {
            let rowContentMap = new Map();
            row.eachCell(cell => {
                let {isMerged, address, value, col, row} = cell;
                if (value instanceof Date) {
                    value = DateUtils.format(value);
                }
                let mergeName = null;
                if (isMerged) {
                    mergeName = this.getMergeName(merges, cell);
                    if (mergeName != null && mergeName != address) {
                        value = null;
                    }
                }
                //console.log(cell.isMerged, "  ", cell.address, ": ", cell.value)
                //console.log(isMerged, "  ", address, ": ", value, ", row: ", row, ", col: ", col)
                rowContentMap.set(col, value);
            })
            results.push(rowContentMap);
        });
        // console.log(results)
        return results;
    }


    isExcel(fileName) {
        let lower = fileName ? fileName.toLowerCase() : "";
        return lower.endsWith(".xlsx") || lower.endsWith(".xls")
    }

    /**
     * 输出sheet中每一个cell的格式列表
     */
    async findCellStyleList(workSheet) {
        let sheetStyle = new SheetStyle();

        let cellList = [];
        const merges = workSheet._merges;
        for (let i = 0; i < workSheet._rows.length; i++) {
            let rowAA = workSheet._rows[i];
            for (let j = 0; j < rowAA._cells.length; j++) {
                let cellAA = rowAA._cells[j];
                // let {isMerged, address, value, col, row, style, type, formula, model} = cellAA;
                let {value, col, row, style, type} = cellAA;
                let cellVo = new CellVo();
                //alignment 水平方向 值为2 为居中  值为1 为居左
                try {
                    cellVo.alignment = this.getHorizontalAlignment(style);
                } catch (error) {
                    console.log(error.stackTrace);
                }

                if (!ObjectUtils.isEmpty(style.border) && !ObjectUtils.isEmpty(style.border.bottom)) {
                    // cellVo.borderBottom = this.getBorderNumer(style.border.bottom.style);
                    cellVo.borderBottom = 1;
                } else {
                    cellVo.borderBottom = 0;
                }
                if (!ObjectUtils.isEmpty(style.border) && !ObjectUtils.isEmpty(style.border.left)) {
                    cellVo.borderLeft = 1;
                } else {
                    cellVo.borderLeft = 0;
                }
                if (!ObjectUtils.isEmpty(style.border) && !ObjectUtils.isEmpty(style.border.right)) {
                    cellVo.borderRight = 1;
                } else {
                    cellVo.borderRight = 0;
                }

                if (workSheet.name === "A.0.2 签署页") {
                    if (i < 4) {
                        cellVo.borderRight = 1;
                    }
                }
                if (workSheet.name === "2 签署页（适用于中介单位）") {
                    if (i < 5 && i > 0) {
                        cellVo.borderRight = 1;
                    }
                }

                if (!ObjectUtils.isEmpty(style.border) && !ObjectUtils.isEmpty(style.border.top)) {
                    cellVo.borderTop = 1;
                } else {
                    cellVo.borderTop = 0;
                }
                cellVo.cellType = type;
                cellVo.columnIndex = col - 1;
                if (value == null) {
                    cellVo.content = "";
                } else if (typeof value === 'object') {
                    cellVo.content = {};
                    cellVo.content['richText'] = value.richText[0].text;
                } else {
                    cellVo.content = value;
                }
                // cellVo.fontBold =
                if (style.font != null) {
                    cellVo.fontName = style.font.name;
                    cellVo.fontSize = style.font.size;
                }
                cellVo.rowIndex = row - 1;
                cellVo.verticalAlignment = this.getVerticalAlignment(style);
                cellList.push(cellVo);
            }
        }
        sheetStyle.cells = cellList;

        //----------merge-------------------
        function CellMerge(key, firstRow, lastRow, firstCol, lastCol) {
            this.key = key;
            this.firstRow = firstRow;
            this.lastRow = lastRow;
            this.firstCol = firstCol;
            this.lastCol = lastCol;
        }

        let mergesList = [];
        let mergeMap = new Map(Object.entries(merges))
        for (let [key, value] of mergeMap) {
            let cellMerge = new CellMerge(key, value.top - 1, value.bottom - 1, value.left - 1, value.right - 1);
            mergesList.push(cellMerge);
        }
        sheetStyle.merges = mergesList;

        //----------------------------------
        function PrintProperty(headerMerge, footerMerge, leftMerge, rightMerge, bottomMerge, topMerge, landSpace) {
            this.headerMerge = headerMerge;
            this.footerMerge = footerMerge;
            this.leftMerge = leftMerge;
            this.rightMerge = rightMerge;
            this.bottomMerge = bottomMerge;
            this.topMerge = topMerge;
            this.landSpace = landSpace;
        }

        let printProperty = new PrintProperty(workSheet.pageSetup.margins.header,
            workSheet.pageSetup.margins.footer,
            workSheet.pageSetup.margins.left,
            workSheet.pageSetup.margins.right,
            workSheet.pageSetup.margins.bottom,
            workSheet.pageSetup.margins.top,
            false
        );
        sheetStyle.print = printProperty;

        if (workSheet.name.includes("实体项目预算表(横)")
            || workSheet.name.includes("措施项目预算表(横)")
            || workSheet.name.includes("实体项目预算表(横-省站标准)")
            || workSheet.name.includes("工程项目总价表-明细")
            || workSheet.name.includes("单项工程费总价表-明细")
        ) {
            printProperty.landSpace = true;//表示为横版
        }

        //-----------row--------------------------
        // fillContentLimitHei = 1043.62;
        function RowUnit(height, rowIndex) {
            this.height = height;
            this.rowIndex = rowIndex;
        }

        let rowsList = [];
        for (let i = 0; i < workSheet._rows.length; i++) {
            let sheetRow = workSheet._rows[i];
            let rowUnit = {};
            if (printProperty.landSpace) {  //如果是横版
                // fillContentLimitHei = 680.38;
                rowUnit = new RowUnit((sheetRow.height / (GljExcelEnum.A4HeightHorizontal - GljExcelEnum.A4TopHorizontal - GljExcelEnum.A4BottomHorizontal)) * 670, sheetRow.number - 1);
            } else {
                if (workSheet.name.includes("扉页") || workSheet.name.includes("封面") || workSheet.name.includes("签署页")) {
                    rowUnit = new RowUnit(sheetRow.height, sheetRow.number - 1);
                } else {
                    rowUnit = new RowUnit((sheetRow.height / (GljExcelEnum.A4Height - GljExcelEnum.A4Top - GljExcelEnum.A4Bottom)) * 1020, sheetRow.number - 1);
                }
            }
            rowsList.push(rowUnit);
        }
        sheetStyle.rows = rowsList;

        //----------Column-----------------------
        function ColumnUnit(width, columnIndex) {
            this.width = width;
            this.columnIndex = columnIndex;
        }

        let columnList = [];
        for (let i = 0; i < workSheet.columns.length; i++) {
            let column = workSheet.columns[i];
            if (printProperty.landSpace) {  //如果是横版
                columnList.push(new ColumnUnit((column.width / GljExcelEnum.A4WidthHorizontal) * 1020, column.number - 1));
            } else {
                columnList.push(new ColumnUnit((column.width / GljExcelEnum.A4Width) * (770 - 60), column.number - 1));
            }

        }
        sheetStyle.columns = columnList;
        //--------组装pageResult----------------------------
        let pageResult = {};
        let sheetBreak = [];
        pageResult['sheetBreak'] = sheetBreak;


        for (let i = 0; i < workSheet.rowBreaks.length; i++) {
            let rowBreak = workSheet.rowBreaks[i];
            let number = rowBreak.id - 1;
            sheetBreak.push(number);
        }
        sheetStyle.pageResult = pageResult;
        // workSheet.get
        return sheetStyle;
    }

    getBorderNumer(style) {
        if (style == 'thin') {
            return 0;
        } else return 1;
    }

    getHorizontalAlignment(style) {
        if (null == style.alignment) return null;
        // 值为2 为居中  值为1 为居左
        if (style.alignment.horizontal == 'center') {
            return 2;
        } else if (style.alignment.horizontal == 'left') {
            return 1;
        } else if (style.alignment.horizontal == 'right') {
            return 3;
        }
    }

    getVerticalAlignment(style) {
        if (null == style.alignment) return null;
        //top 为3 middle 为1  bottom为2
        if (style.alignment.vertical == 'middle') {
            return 1;
        } else if (style.alignment.vertical == 'top') {
            return 3;
        } else if (style.alignment.vertical == 'bottom') {
            return 2;
        }
    }

    async removeTags(html) {
        return html.replace(/(<([^>]+)>)/gi, "").replace(/&nbsp;/g, ' ');
    }

    //行高自适应
    fitHeight(workSheet) {
        const rows = workSheet._rows;
        //console.log(rows)
        let colWidth = 200;
        let fontSize = 14; //与前端展示字体大小一致
        let rowWordNum = Math.trunc(colWidth / fontSize)
        let rowSpace = 0
        let i = 1;
        for (i = 1; i < rows.length; i++) {
            let text = rows[i].cells[2].innerText;
            if (!text) {
                continue;
            }

            let resultText = "";
            let contents = text.split("\\n"); //内容中可能包含换行符，这里以"\n"字符串为换行标识
            let minHeight = 0;
            let length = 0;
            let j = 0;
            for (j = 0; j < contents.length; j++) {
                let rowText = contents[j];
                if (!rowText && rowText.length == 0) {
                    continue;
                }
                length += rowText.length;
                resultText += rowText + "<br/>" + " " + "<br/>"; //多加一个空行，为了分隔两条特征，展示更友好
            }
            let rowNum = Math.ceil(length / rowWordNum) + 1 + contents.length;
            minHeight += (fontSize + rowSpace) * rowNum;

            //console.log(minHeight)

            const divs = rows[i].cells[2].getElementsByTagName('div')
            divs[0].style.height = minHeight + "px"
        }
    }

    async copyRowsWithIndex(rowNumber, rowInsertNum, workSheet) {
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        let headArrayValues = [];
        let row = workSheet._rows[rowNumber - 1];
        for (let i = 0; i < row._cells.length; i++) {
            let value = row._cells[i].value;
            headArrayValues.push(value);
        }
        workSheet.insertRows(rowInsertNum, [headArrayValues], 'o');
        await this.resetMerges(workSheet, rowInsertNum);
        //对下面所有的行的合并单元格进行重新 merge 目的是重置  _merges  否则会导致前端渲染出问题
        // for (let i = rowInsertNum; i < workSheet._rows.length; i++) {
        //     let row2 = workSheet._rows[i-1];//为i是插入后的下一行  i-1表示插入的当前行
        //     for (let j = 0; j < row2._cells.length; j++) {
        //         let cell = row2._cells[j];
        //         let mergeObject = mergeMaps.get(cell._address);
        //         if (mergeObject!=null){
        //             let {top,left,bottom,right} = mergeObject;
        //             workSheet.unMergeCells([top+1,left,bottom+1,right]);//由于插入了新行  所以合并单元格也需往下进行迁移
        //             workSheet.mergeCells([top+1,left,bottom+1,right]);
        //         }
        //     }
        // }
        let row1 = workSheet.getRow(rowInsertNum);
        //针对插入的这一行  做特殊的处理
        for (let i = 0; i < row1._cells.length; i++) {
            // row1._cells[i].numFmt = workSheet._rows[0]._cells[i].numFmt;
            if (ObjectUtils.isNotEmpty(row1._cells[i])) {
                row1._cells[i].style = workSheet._rows[rowNumber - 1]._cells[i].style;
            }
        }
        row1.height = workSheet._rows[rowNumber - 1].height;
        //遍历该行的所有合并单元格  按照该行的方式进行合并
        //当前插入行与模板行的行距
        let distanceRow = rowInsertNum - rowNumber;


        for (let m = 0; m < row1._cells.length; m++) {
            // let cell = row1._cells[m];
            //获取模板行的合并单元格
            let mergeName = this.getMergeName(workSheet._merges, row._cells[m]);
            if (mergeName != null) {
                let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                if (rowInsertNum == bottom + distanceRow) {
                    workSheet.unMergeCells([top + distanceRow, left, bottom + distanceRow, right]);
                    workSheet.mergeCells([top + distanceRow, left, bottom + distanceRow, right]);
                }
            }
        }

    }


    //插入本页小计     第18行 表头
    async insertPageXiaoJi(rowInsertNum, values, workSheet) {
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        workSheet.insertRows(rowInsertNum, values, 'o');
        //重置 _merges
        this.resetMerges(workSheet, rowInsertNum);
        let rowObject = workSheet.getRow(rowInsertNum);
        //针对插入的这一行  做特殊的处理
        for (let i = 0; i < rowObject._cells.length; i++) {
            rowObject._cells[i].style = workSheet._rows[rowInsertNum - 7]._cells[i].style;
            //console.log("");
        }
        rowObject.height = GljExcelEnum.xiaoJi;


        //对插入行的合并单元格格式 进行处理
        for (let m = 0; m < rowObject._cells.length; m++) {
            //获取模板行的合并单元格
            let mergeName = this.getMergeName(workSheet._merges, workSheet._rows[rowInsertNum - 7]._cells[m]);
            if (mergeName != null) {
                let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                workSheet.unMergeCells([top + 1, left, bottom + 1, right]);
                workSheet.mergeCells([top + 1, left, bottom + 1, right]);
            }
            rowObject._cells[m].style = workSheet._rows[rowInsertNum - 7]._cells[m].style;
        }
    }


    async insertBlankRowWithTemplate(rowInsertNum, values, workSheet, templateStyleList, mergeStyleList) {
        workSheet.insertRows(rowInsertNum, values, 'o');
        //重置 _merges
        await this.resetMerges(workSheet, rowInsertNum);
        let rowObject = workSheet.getRow(rowInsertNum);
        //针对插入的这一行  做特殊的处理
        for (let i = 0; i < rowObject._cells.length; i++) {
            rowObject._cells[i].style = templateStyleList[i];
        }

        for (let i = 0; i < mergeStyleList.length; i++) {
            let {top, left, bottom, right} = mergeStyleList[i];
            workSheet.unMergeCells([rowInsertNum, left, rowInsertNum, right]);
            workSheet.mergeCells([rowInsertNum, left, rowInsertNum, right]);
        }
    }


    async rowHeightPoint(workSheet,rowNum) {
        let fontSize = 10;
        let minHeight = 0;
        let mergeMap = new Map(Object.entries(workSheet._merges));
        const regex =  /[\u3000-\u303F\uFF00-\uFFEF\u2000-\u206F\u4E00-\u9FFF]+/g;
        for (let j = 0; j < workSheet.getRow(rowNum)._cells.length; j++) {
            let cell = workSheet.getRow(rowNum)._cells[j];
            //拿到当前单元格的fontSize
            if (ObjectUtils.isNotEmpty(cell.style) && ObjectUtils.isNotEmpty(cell.style.font) && ObjectUtils.isNotEmpty(cell.style.font.size)) {
                fontSize = cell.style.font.size;
            }
            let celltextValue = cell.model.value;
            if (!celltextValue) {
                continue;
            }
            if (typeof celltextValue === 'number') {
                celltextValue = String(celltextValue);
            }
            let contents;
            try {
                contents = celltextValue.split("\n"); //内容中可能包含换行符，这里以"\n"字符串为换行标识
            } catch (e) {
                console.log(e.stackTrace);
            }
            let mergeName = await this.getMergeName(workSheet._merges, cell);
            let mergeLength = 0;//得到该cell的宽度大小
            if (mergeName != null) {
                let value = mergeMap.get(mergeName).model;
                for (let m = value.left; m <= value.right; m++) {
                    mergeLength +=workSheet.getRow(rowNum)._cells[m-1]._column.width;
                }
            } else {    //17.1764092402357 （0.1英寸）== 16.55字符
                mergeLength =cell._column.width;
            }
            // 31.440588158524378  (0.1英寸) == 30.82 字符
            // let rowWordNum = Math.trunc(mergeLengthRatio * ExcelEnum.A4Width / ((fontSize / 72) * 10)) //每一列能够存放的字数
            //rowWordNum若为0  说明该列的列宽很窄  会造成递归死循环   一个字 宽度  1.39  （0.1英寸） 10个字14   总宽17.64
            //2个汉字占满 列宽为3.28字符   一个10号字体的汉字为1.62字符宽度
            let characterNum = mergeLength*(22.09/23.33);//得到该列的字符宽度
            // 60磅为 9.38字符
            // let characterNum = (9.38/60)*((mergeLength/10)*72);
            //10号字体   10个汉字   16.2     转为像素  (10/72)*96 == 1.62字符
            //12号字体   10个汉字  20.0字符
            //14号       10个汉字   24.0字符          (14/72)*96 == 2.4字符
            // 2.4*(26/14)  标准
            // let rowWordNum = Math.trunc(characterNum/ (2.4*(fontSize/14))) //每一列能够存放的字数
            // if(rowWordNum == 0) continue;
            let rowSpace = 4;//行间距  2磅
            let rowNumTotal = 0;
            for (let j = 0; j < contents.length; j++) {
                let rowText = contents[j];
                if (!rowText && rowText.length == 0) {
                    continue;
                }
                // "垂直运输费 ±0.00以下 四层以内"  类似这种问题 在excel中会展示三行 实际计算是两行  18/9 此时满除就多加一行
                // // 使用正则表达式匹配所有符合条件的子串
                let rowNum = 0;
                let left = 0;
                let right = 0;
                for (let k = left; k < rowText.length; k++) {
                    let numChinese = 0;
                    for (let l = right; l < rowText.length; l++) {
                        let match = rowText[l].match(regex);
                        if (ObjectUtils.isNotEmpty(match)) {
                            numChinese++;
                        }else {
                            numChinese+=0.54
                        }
                        //当该列可以容纳的字符数小于 已经遍历到的时
                        if (numChinese > (characterNum / (2.27 * (fontSize / 14)))) {
                            rowNum++;
                            right = l;
                            left = right;
                            break;
                        }else {
                            right = l+1;
                        }
                    }
                    if (right == rowText.length) {
                        rowNum++;
                        break;
                    }
                }
                //理论上的行高  //每列所能容纳的字符个数稍偏小 因此理论值偏大  本来一行就能够容纳的行数 可能算出来为 1.001
                rowNumTotal += rowNum;

            }
            let newMinHeight = 0;
            if (rowNumTotal == 1) {  //针对一行的高度  进行优化
                newMinHeight = ((fontSize) + rowSpace) * rowNumTotal+4;   //计算出该Cell列 的最小适应高度  加4是上下的总共边距
            }else {
                newMinHeight = ((fontSize) + rowSpace) * rowNumTotal;
            }

            if (minHeight < newMinHeight) {
                minHeight = newMinHeight; //得到该行的最大行高
            }
        }
        workSheet.getRow(rowNum).height = minHeight;

    }

    //处理一个worksheet的分页问题
    async dealWithPage(workSheet, workbook, headArgs, args) {
        let headStartNum = 0;
        let headEndNum = 0;
        if (headArgs != null) {
            headStartNum = headArgs['headStartNum'];
            headEndNum = headArgs['headEndNum'];
            if (headArgs['titlePage'] == null) {
                headArgs['titlePage'] = false;//默认为 数据页
            }
        } else {
            headArgs = {};
            headStartNum = 1;
            headEndNum = 5;
            headArgs['headStartNum'] = headStartNum;
            headArgs['headEndNum'] = headEndNum;
            headArgs['titlePage'] = false;//默认为 数据页
        }

        //1、复制表头
        //2、进行 行高自适应的处理 确定行高后  进行分页
        //10号字体
        // 在该行下方插入一个分页符
        //A4 行高 721.5   宽度
        // let marginLeft = ;//左边距
        //得到每一个cell的宽度比例 并计入map
        await this.getRatioWidthSheet(workSheet);

        const regex =  /[\u3000-\u303F\uFF00-\uFFEF\u2000-\u206F\u4E00-\u9FFFA-Z∑·\u0370-\u03FF]+/g;
        let mergeMap = new Map(Object.entries(workSheet._merges));
        let fontSize = 10;
        //行高自适应
        for (let i = headEndNum + 1; i <= workSheet._rows.length; i++) {
            let minHeight = 0;
            let fitRight = true;//这里预设为false 就会保留初始模板的空白行高度 为true针对空白行统统高度为0
            for (let j = 0; j < workSheet.getRow(i)._cells.length; j++) {
                let cell = workSheet.getRow(i)._cells[j];
                //拿到当前单元格的fontSize
                if (ObjectUtils.isNotEmpty(cell.style) && ObjectUtils.isNotEmpty(cell.style.font) && ObjectUtils.isNotEmpty(cell.style.font.size)) {
                    fontSize = cell.style.font.size;
                }
                let celltextValue = cell.model.value;
                if (!celltextValue) {
                    continue;
                }
                fitRight = true;
                if (typeof celltextValue === 'number') {
                    celltextValue = String(celltextValue);
                }
                let contents;
                try {
                    contents = celltextValue.split("\n"); //内容中可能包含换行符，这里以"\n"字符串为换行标识
                } catch (e) {
                    console.log(e.stackTrace);
                }
                let mergeName = await this.getMergeName(workSheet._merges, cell);
                let mergeLength = 0;//得到该cell的宽度大小
                if (mergeName != null) {
                    let value = mergeMap.get(mergeName).model;
                    for (let m = value.left; m <= value.right; m++) {
                        mergeLength +=workSheet.getRow(i)._cells[m-1]._column.width;
                    }
                } else {    //17.1764092402357 （0.1英寸）== 16.55字符
                    mergeLength =cell._column.width;
                }
                // 31.440588158524378  (0.1英寸) == 30.82 字符
                // let rowWordNum = Math.trunc(mergeLengthRatio * ExcelEnum.A4Width / ((fontSize / 72) * 10)) //每一列能够存放的字数
                //rowWordNum若为0  说明该列的列宽很窄  会造成递归死循环   一个字 宽度  1.39  （0.1英寸） 10个字14   总宽17.64
                //2个汉字占满 列宽为3.28字符   一个10号字体的汉字为1.62字符宽度
                let characterNum = mergeLength*(22.09/23.33);//得到该列的字符宽度
                // 60磅为 9.38字符
                // let characterNum = (9.38/60)*((mergeLength/10)*72);
                //10号字体   10个汉字   16.2     转为像素  (10/72)*96 == 1.62字符
                //12号字体   10个汉字  20.0字符
                //14号       10个汉字   24.0字符          (14/72)*96 == 2.4字符
                // 2.4*(26/14)  标准
                // let rowWordNum = Math.trunc(characterNum/ (2.4*(fontSize/14))) //每一列能够存放的字数
                // if(rowWordNum == 0) continue;
                let rowSpace = 4;//行间距  2磅
                let rowNumTotal = 0;
                for (let j = 0; j < contents.length; j++) {
                    let rowText = contents[j];
                    if (!rowText && rowText.length == 0) {
                        continue;
                    }
                    // "垂直运输费 ±0.00以下 四层以内"  类似这种问题 在excel中会展示三行 实际计算是两行  18/9 此时满除就多加一行
                    // // 使用正则表达式匹配所有符合条件的子串
                    let rowNum = 0;
                    let left = 0;
                    let right = 0;
                    for (let k = left; k < rowText.length; k++) {
                        let numChinese = 0;
                        for (let l = right; l < rowText.length; l++) {
                            let match = rowText[l].match(regex);
                            if (ObjectUtils.isNotEmpty(match)) {
                                numChinese++;
                            }else {
                                numChinese+=0.54
                            }
                            //当该列可以容纳的字符数小于 已经遍历到的时
                            if (numChinese > (characterNum / (2.27 * (fontSize / 14)))) {
                                rowNum++;
                                right = l;
                                left = right;
                                break;
                            }else {
                                right = l+1;
                            }
                        }
                        if (right == rowText.length) {
                            rowNum++;
                            break;
                        }
                    }
                    //理论上的行高  //每列所能容纳的字符个数稍偏小 因此理论值偏大  本来一行就能够容纳的行数 可能算出来为 1.001
                    rowNumTotal += rowNum;

                }
                let newMinHeight = 0;
                if (rowNumTotal == 1) {  //针对一行的高度  进行优化
                    newMinHeight = ((fontSize) + rowSpace) * rowNumTotal+4;   //计算出该Cell列 的最小适应高度  加4是上下的总共边距
                }else {
                    newMinHeight = ((fontSize) + rowSpace) * rowNumTotal;
                }

                if (minHeight < newMinHeight) {
                    minHeight = newMinHeight; //得到该行的最大行高
                }
            }
            if (fitRight) {
                workSheet.getRow(i).height = minHeight;
            }
            // if (workSheet.getRow(i).height <= 18) {
            //     workSheet.getRow(i).height = 18;
            // }
            if (workSheet.getRow(i).height > GljExcelEnum.A4HeightDataMax) {
                workSheet.getRow(i).height = GljExcelEnum.A4HeightDataMax;
            }
        }
        //分页处理
        // await workbook.xlsx.writeFile("E:\\桌面\\export\\分页前计算完行高.xlsx");
        if (!headArgs['titlePage']) {  //如果不是封面扉页
            let totalPage = await this.pageSplit(workSheet, 1, headArgs, 0);
            /*****************************************/
            //对页码显示进行处理
            let cellList = this.findContainValueCell(workSheet, "第 1 页  共 1 页");
            if (cellList.length == 0) {
                cellList = this.findContainValueCell(workSheet, "第 1 页 共 1 页");//横版是如此格式
            }
            const grouped = cellList.reduce((result, obj) => {
                const key = obj.cell._row._number;
                if (!result[key]) {
                    result[key] = [];
                }
                result[key].push(obj.cell);
                return result;
            }, {});

            if (ObjectUtils.isNotEmpty(args['totalPage'])) {
                //当页面传了总页数
                totalPage = args['totalPage'];
            }
            // let startPage = 0;
            // if (ObjectUtils.isNotEmpty(args['startPage'])) {
            //     //当页面传了开始页码
            //     startPage = args['startPage'];
            // }

            if (args['fileType'] === "pdf") {
                //pdf需要计算总页码数，计算完在最外面赋值总页数
                if (ObjectUtils.isNotEmpty(args['startPage']) && ObjectUtils.isEmpty(args['totalPage'])) {
                    args['totalPdfPageSize'] = args['totalPdfPageSize'] + new Map(Object.entries(grouped)).size;
                }
            }

            let mergeMap = new Map(Object.entries(grouped));
            let count = 0;
            for (let [key, value] of mergeMap) {
                count++;
                let str = "第 " + (count) + " 页 共 " + totalPage + " 页";

                if (ObjectUtils.isNotEmpty(args['startPage'])) {
                    str = "第 " + args['startPage'] + " 页 共 " + totalPage + " 页";
                }
                if (args['fileType'] === "pdf") {
                    if (ObjectUtils.isNotEmpty(args['startPage']) && ObjectUtils.isEmpty(args['totalPage'])) {
                        str = "第 " + args['startPage'] + GljExcelEnum.totalPdfPageSizeReplaceStr;
                    }
                }

                if (ObjectUtils.isNotEmpty(args['startPage'])) {
                    //如果传了开始页，每一页都需要累加
                    args['startPage'] = args['startPage'] + 1;
                }

                for (let i = 0; i < value.length; i++) {
                    let elementCell = value[i];
                    elementCell.value = str;
                    elementCell.style.alignment.vertical = "bottom";
                }
            }
            /*****************对空白行的处理********************************/
            //要求空白行只能是在末尾页  而不是在页中  否则逻辑出错
            await this.dealWithBlankRow(workSheet, headArgs);

        }
        //如果是扉页
        if (headArgs['titlePage']) {  //如果是扉页  按照比例调整行高
            if (!workSheet.name.includes("B.0.13 国内采购设备表")
                && !workSheet.name.includes("B.0.9 工程项目材料数量及价格表")) {
                let total = workSheet._rows.reduce((totalHigh, rowUnit) => {
                    return totalHigh + rowUnit.height;
                }, 0);
                for (let i = 0; i < workSheet._rows.length; i++) {
                    workSheet._rows[i].height = (workSheet._rows[i].height / total) * (GljExcelEnum.A4Height - GljExcelEnum.A4Top - GljExcelEnum.A4Bottom);
                }
            }

            if (ObjectUtils.isNotEmpty(args['startPage'])) {
                //如果传了开始页，就算是封面也要累加
                args['startPage'] =  args['startPage'] + 1;
            }
            if (args['fileType'] === "pdf") {
                //pdf需要计算总页码数，计算完在最外面赋值总页数
                if (ObjectUtils.isNotEmpty(args['startPage']) && ObjectUtils.isEmpty(args['totalPage'])) {
                    args['totalPdfPageSize'] = args['totalPdfPageSize'] + 1;
                }
            }

        }
        // await workbook.xlsx.writeFile("D:\\csClient\\测试\\单位工程层级.xlsx");
    }

    //获取指定行的单元格style
    async getStyleListForPointRow(row) {
        if (ObjectUtils.isEmpty(row)) return [];
        let elementStyleList = [];
        for (let i = 0; i < row._cells.length; i++) {
            elementStyleList.push(row._cells[i].style);
        }
        return elementStyleList;
    }

    //获取指定行的合并单元格信息
    async getMergeListForPointRow(row,mergeMaps) {
        if (ObjectUtils.isEmpty(row)) return [];
        let mergeElementStyle = [];
        for (let i = 0; i < row._cells.length; i++) {
            if (mergeMaps.has(row._cells[i]._address)) {
                mergeElementStyle.push(mergeMaps.get(row._cells[i]._address));
            }
        }
        return mergeElementStyle;
    }

    async dealWithBlankRow(workSheet, headArgs) {
        if (workSheet.name.includes("编制说明")
        ) return;

        //这里之所以放在删除空白行的前面  是为了保留删除之前的单元格记录 方便后续拿到删除行的单元格格式
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        let rowTemp = await this.getTemplateRow(workSheet);

        let elementStyleList = await this.getStyleListForPointRow(rowTemp);
        let mergeElementStyle = await this.getMergeListForPointRow(rowTemp,mergeMaps);

        await this.deleteBlankRow(workSheet);
        //获取要插入的行号
        let reserved = workSheet.rowDataTypelist.filter(item => item.field=="reserve");//保留行
        let rowNumInsert = workSheet.rowDataTypelist.length-reserved.length;
        //每页总高度
        let pageHeight = 0;
        if (workSheet.name.includes("实体项目预算表(横)")
            || workSheet.name.includes("措施项目预算表(横)")
            || workSheet.name.includes("实体项目预算表(横-省站标准)")
            || workSheet.name.includes("工程项目总价表-明细")
            || workSheet.name.includes("单项工程费总价表-明细")
        ) {
            pageHeight = (GljExcelEnum.A4HeightHorizontal - GljExcelEnum.A4TopHorizontal - GljExcelEnum.A4BottomHorizontal);
        } else {
            pageHeight = GljExcelEnum.A4Height - GljExcelEnum.A4Top - GljExcelEnum.A4Bottom;
        }
        //获取分页符后 对有空白行的那一页 进行补偿
        let start = 0;
        let end = 0;
        if (workSheet.rowBreaks.length != 0) {
            start = workSheet.rowBreaks[workSheet.rowBreaks.length - 1].id;
            end = workSheet._rows.length;
        } else {
            start = 1;
            end = workSheet._rows.length;
        }
        let rows = workSheet._rows.slice(start - 1, end);//当前页剔除空白行的所有行
        //得到剔除空白行页的总高度
        let totalHeight = rows.reduce((total, item) => total + item.height, 0);
        //得到平均行高
        let averageHeight = 18;//空白行高度给18
        //增加空白行
        //初始化values
        let rowValues = [[]];
        for (let i = 0; i < workSheet._rows[0]._cells.length; i++) {
            rowValues[0].push("");
        }
        let heightDiffer = pageHeight - totalHeight;
        let number = Math.trunc(heightDiffer / averageHeight);

        for (let i = 0; i < number; i++) {
            await this.insertBlankRowWithTemplate(rowNumInsert + i, rowValues, workSheet, elementStyleList, mergeElementStyle);
            //这里是在删除的第一个空白行的基础上进行增加
            workSheet._rows[rowNumInsert + i - 1].height = averageHeight;
            workSheet.rowDataTypelist.splice(rowNumInsert + i,0,{field:"blank"});
        }
        // await workSheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx");
    }

    async getTemplateDataRow(workSheet, rowNum) {
        let rowTemp = workSheet._rows[rowNum];
        let contentStat = 0;
        for (let i = 0; i < rowTemp._cells.length; i++) {
            let cell = rowTemp._cells[i];
            if (cell.value && cell._value._master == null) {
                contentStat++;
            }
        }
        if (contentStat >= 2) {  //认为是数据行
            return rowTemp;
        } else {
            return await this.getTemplateDataRow(workSheet, ++rowNum);
        }
        return null;
    }

    async getTemplateRow(workSheet) {
        let rowDatas = workSheet.rowDataTypelist.filter(item => ObjectUtils.isNotEmpty(item.field) && item.field=="data");
        if (ObjectUtils.isNotEmpty(rowDatas)) {
            for (let i = 0; i < workSheet.rowDataTypelist.length; i++) {
                if (ObjectUtils.isNotEmpty(workSheet.rowDataTypelist[i].field) && workSheet.rowDataTypelist[i].field == "data") {
                    return workSheet._rows[i-1];
                }
            }
        }
        for (let i = 0; i < workSheet.rowDataTypelist.length; i++) {
            if (ObjectUtils.isNotEmpty(workSheet.rowDataTypelist[i].field) && workSheet.rowDataTypelist[i].field == "blank") {
                return workSheet._rows[i-1];
            }
        }
    }

    //删除空白行  同时返回空白行所在行号
    async deleteBlankRow(workSheet, listNum = []) {
        for (let i = 0; i < workSheet._rows.length; i++) {
            if (ObjectUtils.isNotEmpty(workSheet.rowDataTypelist[i+1]) && ObjectUtils.isNotEmpty(workSheet.rowDataTypelist[i+1].field) &&
                (workSheet.rowDataTypelist[i+1].field=="data"||workSheet.rowDataTypelist[i+1].field=="head"||workSheet.rowDataTypelist[i+1].field=="reserve")) {
                continue;
            }
            let rowCur = workSheet._rows[i];
            let result = true;
            for (let j = 0; j < rowCur._cells.length; j++) {
                let celltextValue = rowCur._cells[j].model.value;
                if (celltextValue == null && rowCur._cells[j]._value._master != null) {  //如果有属主  则为属主的值
                    celltextValue = rowCur._cells[j]._value._master.model.value
                }
                if (ObjectUtils.isEmpty(celltextValue)) {
                    continue;
                }
                result = false;
            }
            if (result) {
                listNum.push(workSheet._rows[i]);
                workSheet._rows.splice(i, 1);
                workSheet.rowDataTypelist.splice(i+1,1);
                await this.resetMergesWhenDel(workSheet, i + 1);
                await this.resetRowNumWhenDel(workSheet);
                await this.deleteBlankRow(workSheet, listNum);
                break;
            }
        }
        return listNum;
    }

    //删除一行后需要进行同步处理
    async resetRowNumWhenDel(workSheet) {
        const regex = /([A-Za-z]+)(\d+)/;
        for (let i = 0; i < workSheet._rows.length; i++) {
            if (workSheet._rows[i]._number != i+1) {
                workSheet._rows[i]._number = i+1;
                //同时需要对cell的address进行重置
                //在空白行填充的过程中框架mergeCells方法会对后续行的address和_merges内容不对的进行重置
                //但删除一行后如果不填充 address还是原来删除前的比较大的值 就会造成excel不显示该行
                for (let j = 0; j < workSheet._rows[i]._cells.length; j++) {
                    let address = workSheet._rows[i]._cells[j]._address;
                    const matches = address.match(regex);
                    const part1 = matches[1];
                    workSheet._rows[i]._cells[j]._address = part1+workSheet._rows[i]._number;
                    workSheet._rows[i]._cells[j].model.address = part1+workSheet._rows[i]._number;
                    //对cell的master进行重置
                    if (workSheet._rows[i]._cells[j].model.master != null) {
                        let name = await this.getMergeName(workSheet._merges,workSheet._rows[i]._cells[j]);
                        workSheet._rows[i]._cells[j].model.master = name;
                    }
                }
            }
        }
    }

    async getBlankRow(workSheet, listNum = []) {
        for (let i = 0; i < workSheet._rows.length; i++) {
            if (workSheet._rows[i].height == 0) {
                listNum.push(workSheet._rows[i]._number);
                // workSheet._rows.splice(i,1);
                // await this.getBlankRow(workSheet,listNum);
                // break;
            }
        }
        return listNum;
    }

    //递归定义  如果到达第二页 就增加分页 并增加表头   最后返回总页数
    async pageSplit(workSheet, rowNum, args, totalPage) {  //从1 开始   args表头相关参数
        let differ = 0;
        if (workSheet.name.includes("实体项目预算表(横)")
            || workSheet.name.includes("措施项目预算表(横)")
            || workSheet.name.includes("实体项目预算表(横-省站标准)")
            || workSheet.name.includes("工程项目总价表-明细")
            || workSheet.name.includes("单项工程费总价表-明细")
            ){
            differ = (GljExcelEnum.A4HeightHorizontal - GljExcelEnum.A4TopHorizontal - GljExcelEnum.A4BottomHorizontal);
        } else {
            differ = GljExcelEnum.A4Height - GljExcelEnum.A4Top - GljExcelEnum.A4Bottom;
        }

        totalPage++;
        let {headStartNum, headEndNum} = args;//从1开始
        let height = 0;
        let blankRows = 0;//要求如果分页时经历了空白行  那么分页符应该置在空白行的前面那一行
        for (let i = rowNum - 1; i < workSheet._rows.length - 1; i++) {
            height += workSheet._rows[i].height;
            if (workSheet._rows[i].height == 0) {
                blankRows++;
            }
            if (height + workSheet._rows[i + 1].height > differ) {
                if (blankRows == 0) {  //表示达到分页条件时经历的空白行为0 进行正常分页
                    let rowLast = workSheet.getRow(i + 1);//应该要加小计的行
                    rowLast.addPageBreak();
                } else {    //如果该页经历了空白行，则分页符加在空白行的上一行
                    let rowBefore = workSheet._rows[i - blankRows];
                    rowBefore.addPageBreak();
                    blankRows = 0;
                }
                /***********边界情况******************/
                    //如果刚好遇到下一行是合计的这种边界情况  则插入空白行
                let result = false;//是否需要插入空白行
                if (i + 1 == workSheet._rows.length - 1) {
                    //说明当前行为空白行 下一行为合计
                    result = true;
                }
                let rowInserNum = i + 1;

                //分页后进行表头复制
                for (let j = headStartNum; j <= headEndNum; j++) {
                    await this.copyRowsWithIndex(j, rowInserNum + j, workSheet);
                    workSheet.rowDataTypelist.splice(rowInserNum + j,0,{field:"head"});
                }
                if (result) {
                    // await this.insertPageXiaoJi(rowInserNum + headEndNum + 1, [["", "", "", "", "", "", "", "", "", ""]], workSheet)
                }
                // await workSheet.workbook.xlsx.writeFile("D:\\csClient\\测试\\test.xlsx");
                totalPage = await this.pageSplit(workSheet, rowInserNum + 1, args, totalPage);
                break;
            }
        }
        return totalPage;
    }

    async pageSplitForXiaoJi(workSheet, rowNum, args, totalPage) {  // rowNum从1 开始 表示从1开始统计行高  args表头相关参数
        let differ = 0;
        if (workSheet.name.includes("B.0.3 其他费用计算表")
            || workSheet.name.includes("B.0.5 概算汇总表")
            || workSheet.name.includes("B.0.6 概算汇总表（含工程建设其他费细项）")
            || workSheet.name.includes("B.0.7 概算汇总表（金额为0不输出）")
            || workSheet.name.includes("B.0.8 概算汇总表（万元）")
            || workSheet.name.includes("B.0.10 总概算对比表")
            || workSheet.name.includes("B.0.12 进口设备材料货价及从属费用计算表")
            || workSheet.name.includes("B.0.05 综合概算表")
            || workSheet.name.includes("B.0.11 综合概算对比表")
            || workSheet.name.includes("单位工程概预算表(A4横)")) {
            differ = (GljExcelEnum.A4HeightHorizontal - GljExcelEnum.A4TopHorizontal - GljExcelEnum.A4BottomHorizontal);
        } else {
            differ = GljExcelEnum.A4Height - GljExcelEnum.A4Top - GljExcelEnum.A4Bottom;
        }

        totalPage++;
        let {headStartNum, headEndNum} = args;//从1开始
        let height = 0;
        let count = 0;
        let blankRows = 0;
        for (let i = rowNum; i <= workSheet._rows.length - 1; i++) {
            height += workSheet.getRow(i).height;
            //针对一行过高 占满一页不够的情况、及下一行过高导致本页空出大片区域的情况进行处理   对行进行拆分
            count++;
            if (count == headEndNum && height + workSheet.getRow(i + 1).height + GljExcelEnum.xiaoJi > differ) {
                //对当前行行高重新计算赋值 并对新加的一行进行计算
                let originHeight = workSheet.getRow(i + 1).height;
                workSheet.getRow(i + 1).height = differ - GljExcelEnum.xiaoJi - height - 1;//减一加一是为了后面进入分页的条件
                //得到比例 获得拆分行的内容分配及行高
                let cellCount = 4;
                if (workSheet.name.includes("【分部6】分部分项清单对比表(含关联项)")) {
                    cellCount = 7;
                }
                let spliceRowHeight = originHeight - workSheet.getRow(i + 1).height + 1;
                let spliceContentLength = Math.trunc(workSheet.getRow(i + 1)._cells[cellCount].value.length * (spliceRowHeight / originHeight));
                let contentLast = workSheet.getRow(i + 1)._cells[cellCount].value.substring(0, workSheet.getRow(i + 1)._cells[cellCount].value.length - spliceContentLength);
                let spliceContent = workSheet.getRow(i + 1)._cells[cellCount].value.substring(workSheet.getRow(i + 1)._cells[cellCount].value.length - spliceContentLength);

                workSheet.getRow(i + 1)._cells[cellCount].value = contentLast;
                //增加新增的拆分行  这里借用这个方法
                //进行行拆分后在这里添加本页小计并增加下一页的表头  这里和上面的不一样
                // await this.insertSheetPageLastRow(workSheet, i + 1 + 1, args);
                await this.insertPageXiaoJi(i + 2, await this.insertSheetBlackRowCells(workSheet, spliceContent), workSheet)


                workSheet.getRow(i + 2).height = spliceRowHeight;
                totalPage = await this.pageSplitForXiaoJi(workSheet, i - headEndNum + 1, args, totalPage - 1);
                break;
            }
            //如果本页已有较窄数据 属于下一行过高情况
            //条件  当前行加上下一行 超出一页高度
            if (count > headEndNum && height + workSheet.getRow(i + 1).height + GljExcelEnum.xiaoJi > differ
                && height + GljExcelEnum.xiaoJi < differ - 2000 //设置当前行离底部的行距高于多少时 考虑拆分下面的一行
                && workSheet.getRow(i + 1).height > 2000
                && false
            ) {
                let originHeight = workSheet.getRow(i + 1).height;
                workSheet.getRow(i + 1).height = differ - GljExcelEnum.xiaoJi - height - 1;//减一加一是为了后面进入分页的条件
                //得到比例 获得拆分行的内容分配及行高
                let cellCount = 4;
                if (workSheet.name.includes("【分部6】分部分项清单对比表(含关联项)")) {
                    cellCount = 7;
                }
                let spliceRowHeight = originHeight - workSheet.getRow(i + 1).height + 1;
                let spliceContentLength = Math.trunc(workSheet.getRow(i + 1)._cells[cellCount].value.length * (spliceRowHeight / originHeight));
                let contentLast = workSheet.getRow(i + 1)._cells[cellCount].value.substring(0, workSheet.getRow(i + 1)._cells[cellCount].value.length - spliceContentLength);
                let spliceContent = workSheet.getRow(i + 1)._cells[cellCount].value.substring(workSheet.getRow(i + 1)._cells[cellCount].value.length - spliceContentLength);

                //**********优化*******************
                if (spliceContent.length <= 10) {
                    //如果拆分之后的下一行分配的字数过少  就不进行拆分
                    //*****************************
                } else {
                    workSheet.getRow(i + 1)._cells[cellCount].value = contentLast;
                    //增加新增的拆分行  这里借用这个方法
                    await this.insertPageXiaoJi(i + 2, await this.insertSheetBlackRowCells(workSheet, spliceContent), workSheet)
                    workSheet.getRow(i + 2).height = spliceRowHeight;

                    //进行行拆分后在这里添加本页小计并增加下一页的表头  这里和上面的不一样
                    await this.insertSheetPageLastRow(workSheet, i + 2, args);

                    let rowLast = workSheet.getRow(i + 2);//应该要加小计的行
                    rowLast.addPageBreak();
                    //分页后进行表头复制
                    for (let j = headStartNum; j <= headEndNum; j++) {
                        await this.copyRowsWithIndex(j, i + 2 + j, workSheet);
                    }
                    totalPage = await this.pageSplitForXiaoJi(workSheet, i + 3, args, totalPage);
                    break;
                }
            }
            //针对边界情况  遇到分页时插入本页小计刚好分在 分隔了 原有的本页小计和合计  这一种情况
            let result = true;
            //如果进行插入本页小计并分页时 下一行也是本页小计 即已经没有了数据行 则不进行插入
            if (workSheet.getRow(i + 1)._cells[1].value == "编制人：") {
                result = false;
            }
            if (workSheet._rows[i].height == 0) {
                blankRows++;
            }

            //当前行加本页小计的行高  刚好到了这一页的末尾
            if ((height + workSheet.getRow(i + 1).height + GljExcelEnum.xiaoJi > differ
                && height + GljExcelEnum.xiaoJiTrue < differ) && result
            ) {
                if (blankRows == 0) {  //表示达到分页条件时经历的空白行为0 进行正常分页
                    let rowLast = workSheet.getRow(i + 1);//应该要加小计的行
                    rowLast.addPageBreak();
                } else {    //如果该页经历了空白行，则分页符加在空白行的上一行
                    let rowBefore = workSheet._rows[i - blankRows];
                    rowBefore.addPageBreak();
                    blankRows = 0;
                }
                //进行行拆分后在这里添加本页小计并增加下一页的表头  这里和上面的不一样
                await this.insertSheetPageLastRow(workSheet, i + 1, args);


                let result = false;//为true表示增加新页面的表头后在表头下一行插入空白行
                // 以将合计行在后续处理空白行时 移到新一页的最后一行
                if (i + 1 == workSheet._rows.length - 1) {
                    //说明当前行为空白行 下一行为合计
                    result = true;
                }
                //分页后进行表头复制
                for (let j = headStartNum; j <= headEndNum; j++) {
                    await this.copyRowsWithIndex(j, i + 1 + j, workSheet);
                }
                if (result) {
                    await this.insertPageXiaoJi(i + 1 + headEndNum + 1, await this.insertSheetBlackRowCells(workSheet, ""), workSheet)
                }
                // await workSheet.workbook.xlsx.writeFile("D:\\csClient\\测试\\单位工程层级.xlsx");
                totalPage = await this.pageSplitForXiaoJi(workSheet, i + 2, args, totalPage);
                break;
            }
        }
        return totalPage;
    }


    async insertSheetPageLastRow(workSheet, rowInserNumber, args) {
        let workSheetGeshi = args["workSheetGeshi"];


        if (workSheet.name.includes("B.0.3 其他费用计算表")) {     //工程级别
            await this.copyRowsWithOtherSheetV2(13, rowInserNumber, workSheet, workSheetGeshi, 4, 8);
        } else if (workSheet.name.includes("B.0.9 工程项目材料数量及价格表")) {
            await this.copyRowsWithOtherSheetV2(23, rowInserNumber, workSheet, workSheetGeshi, 4, 8);
        } else if (workSheet.name.includes("B.0.10 总概算对比表")) {
            await this.copyRowsWithOtherSheetV2(25, rowInserNumber, workSheet, workSheetGeshi, 7, 14);
        } else if (workSheet.name.includes("B.0.12 进口设备材料货价及从属费用计算表")) {
            await this.copyRowsWithOtherSheetV2(27, rowInserNumber, workSheet, workSheetGeshi, 7, 13);
        } else if (workSheet.name.includes("B.0.13 国内采购设备表")) {
            await this.copyRowsWithOtherSheetV2(29, rowInserNumber, workSheet, workSheetGeshi, 5, 10);
        } else if (workSheet.name.includes("附表B.0.1 总概算表")) {
            await this.copyRowsWithOtherSheet(33, rowInserNumber, workSheet, workSheetGeshi, 4, 8, 10);
        } else if (workSheet.name.includes("附表B.0.2 总概算表")) {
            await this.copyRowsWithOtherSheet(35, rowInserNumber, workSheet, workSheetGeshi, 4, 8, 11);
        } else if (workSheet.name.includes("B.0.05 综合概算表")) {      //单项级别
            await this.copyRowsWithOtherSheet(1, rowInserNumber, workSheet, workSheetGeshi, 2, 5, 7);
        } else if (workSheet.name.includes("B.0.11 综合概算对比表")) {
            await this.copyRowsWithOtherSheetV2(3, rowInserNumber, workSheet, workSheetGeshi, 5, 12);
        } else if (workSheet.name.includes("【分部6】分部分项清单对比表(含关联项)")) {
            await this.copyRowsWithOtherSheet(5, rowInserNumber, workSheet, workSheetGeshi, 4, 8, 14);
        } else if (workSheet.name.includes("【措施1】措施项目审核对比表")) {
            await this.copyRowsWithOtherSheet(7, rowInserNumber, workSheet, workSheetGeshi, 4, 7, 11);
        } else if (workSheet.name.includes("【其他1】其他项目审核对比表")) {
            await this.copyRowsWithOtherSheet(9, rowInserNumber, workSheet, workSheetGeshi, 2, 4, 6);
        } else if (workSheet.name.includes("【计日工1】计日工审核对比表")) {
            await this.copyRowsWithOtherSheet(11, rowInserNumber, workSheet, workSheetGeshi, 4, 8, 12);
        } else if (workSheet.name.includes("【人材机2】人材机审核对比表")) {
            await this.copyRowsWithOtherSheet(13, rowInserNumber, workSheet, workSheetGeshi, 5, 10, 15);
        } else if (workSheet.name.includes("【人材机3】人材机价差汇总对比表")) {
            await this.copyRowsWithOtherSheet(15, rowInserNumber, workSheet, workSheetGeshi, 5, 10, 16);
        } else if (workSheet.name.includes("【增值税1】材料、机械、设备增值税对比表")) {
            await this.copyRowsWithOtherSheet(17, rowInserNumber, workSheet, workSheetGeshi, 5, 10, 14);
        } else if (workSheet.name.includes("【规费1】规费明细对比表")) {
            await this.copyRowsWithOtherSheet(19, rowInserNumber, workSheet, workSheetGeshi, 4, 8, 11);
        } else if (workSheet.name.includes("【安全文施1】安全文明施工费明细对比表")) {
            await this.copyRowsWithOtherSheet(21, rowInserNumber, workSheet, workSheetGeshi, 4, 8, 13);
        } else if (workSheet.name.includes("【工程量1】审定工程量计算书")) {
            await this.copyRowsWithOtherSheet(23, rowInserNumber, workSheet, workSheetGeshi, 2, 5, 7);
        } else if (workSheet.name.includes("【增值税4】增值税进项税额对比表")) {
            await this.copyRowsWithOtherSheet(25, rowInserNumber, workSheet, workSheetGeshi, 2, 3, 5);
        }

        workSheet.getRow(rowInserNumber).height = 18;
        workSheet.getRow(rowInserNumber).fontSize = 9;
    }


    async insertSheetBlackRowCells(workSheet, spliceContent) {
        let resultList = [];
        if (workSheet.name.includes("B.0.3 其他费用计算表")) {
            resultList = [["", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【项1】工程审核汇总对比表")) {
            resultList = [["", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【人材机1】人材机汇总对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【单项1】单项工程审核对比表")) {
            resultList = [["", "", "", "", ""]];
        } else if (workSheet.name.includes("【费1】单位工程审核对比表")) {
            resultList = [["", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【分部1】分部分项清单对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【分部6】分部分项清单对比表(含关联项)")) {
            resultList = [["", "", "", "", "", "", "", spliceContent, "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【措施1】措施项目审核对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【其他1】其他项目审核对比表")) {
            resultList = [["", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【计日工1】计日工审核对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【人材机2】人材机审核对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【人材机3】人材机价差汇总对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【增值税1】材料、机械、设备增值税对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【规费1】规费明细对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【安全文施1】安全文明施工费明细对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【工程量1】审定工程量计算书")) {
            resultList = [["", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【增值税4】增值税进项税额对比表")) {
            resultList = [["", "", "", "", ""]];
        }
        return resultList;
    }


    async copyRowsWithOtherSheet(rowNumber, rowInsertNum, workSheet, otherWorkSheet, start, midlle, last) {
        let headArrayValues = [];
        let row = otherWorkSheet._rows[rowNumber - 1];
        headArrayValues.push(row.values);
        workSheet.insertRows(rowInsertNum, headArrayValues, 'o');
        this.resetMerges(workSheet, rowInsertNum);
        let rowLast = workSheet.getRow(rowInsertNum);
        //针对插入的这一行  做特殊的处理
        for (let i = 0; i < rowLast._cells.length; i++) {
            rowLast._cells[i].style = otherWorkSheet._rows[rowNumber - 1]._cells[i].style;
            rowLast._cells[i].style.alignment.horizontal = otherWorkSheet._rows[rowNumber - 1]._cells[i].style.alignment.horizontal;
            rowLast._cells[i].style.height = otherWorkSheet._rows[rowNumber - 1]._cells[i].style.height;
        }
        workSheet.unMergeCells(rowLast.number, 1, rowLast.number, start);
        workSheet.mergeCells(rowLast.number, 1, rowLast.number, start);
        workSheet.unMergeCells(rowLast.number, start + 1, rowLast.number, midlle);
        workSheet.mergeCells(rowLast.number, start + 1, rowLast.number, midlle);
        workSheet.unMergeCells(rowLast.number, midlle + 1, rowLast.number, last);
        workSheet.mergeCells(rowLast.number, midlle + 1, rowLast.number, last);
        rowLast.getCell(1).value = otherWorkSheet._rows[rowNumber - 1]._cells[1].value;
        rowLast.getCell(start + 1).value = otherWorkSheet._rows[rowNumber - 1]._cells[start + 1].value;
        rowLast.getCell(midlle + 1).value = otherWorkSheet._rows[rowNumber - 1]._cells[midlle + 1].value;
        rowLast.height = 12;
    }

    async copyRowsWithOtherSheetV2(rowNumber, rowInsertNum, workSheet, otherWorkSheet, start, midlle) {
        let mergeMaps = new Map(Object.entries(otherWorkSheet._merges));
        let headArrayValues = [];
        let row = otherWorkSheet._rows[rowNumber - 1];
        headArrayValues.push(row.values);
        workSheet.insertRows(rowInsertNum, headArrayValues, 'o');
        this.resetMerges(workSheet, rowInsertNum);
        let rowLast = workSheet.getRow(rowInsertNum);
        //针对插入的这一行  做特殊的处理
        for (let i = 0; i < rowLast._cells.length; i++) {
            rowLast._cells[i].style = otherWorkSheet._rows[rowNumber - 1]._cells[i].style;
        }

        workSheet.unMergeCells(rowLast.number, 1, rowLast.number, start);
        workSheet.mergeCells(rowLast.number, 1, rowLast.number, start);
        workSheet.unMergeCells(rowLast.number, start + 1, rowLast.number, midlle);
        workSheet.mergeCells(rowLast.number, start + 1, rowLast.number, midlle);
        rowLast.getCell(1).value = otherWorkSheet._rows[rowNumber - 1]._cells[1].value;
        rowLast.getCell(start + 1).value = otherWorkSheet._rows[rowNumber - 1]._cells[start + 1].value;
        rowLast.height = 12;
    }


    /**
     * 分页最后一行，合并下一行；插入编制人、审核人、审定人格式数据
     * @param rowNumber
     * @param rowInsertNum
     * @param workSheet
     * @param otherWorkSheet
     * @param start
     * @param midlle
     * @param last
     * @returns {Promise<void>}
     */
    async copyRowsWithOtherSheetHeji(rowNumber, rowInsertNum, workSheet, otherWorkSheet, start, midlle, last) {
        let row6 = workSheet.getRow(rowInsertNum - 2);      //合计上一行
        if (row6._cells[1] == "") {
            let rowHeji = workSheet.getRow(rowInsertNum - 1);    //合计行
            for (let i = 0; i < row6._cells.length; i++) {
                if (undefined != rowHeji._cells[i].value && null != rowHeji._cells[i].value) {
                    row6._cells[i].value = rowHeji._cells[i].value;
                }
                if (undefined != rowHeji._cells[i].style && null != rowHeji._cells[i].style) {
                    row6._cells[i].style = rowHeji._cells[i].style;
                }
            }

            let rowLast = workSheet.getRow(rowInsertNum - 1);
            //针对插入的这一行  做特殊的处理
            for (let i = 0; i < rowLast._cells.length; i++) {
                rowLast._cells[i].value = otherWorkSheet._rows[rowNumber - 1]._cells[i].value;
                rowLast._cells[i].style = otherWorkSheet._rows[rowNumber - 1]._cells[i].style;
            }

            workSheet.unMergeCells(rowLast.number, 1, rowLast.number, start);
            workSheet.mergeCells(rowLast.number, 1, rowLast.number, start);
            workSheet.unMergeCells(rowLast.number, start + 1, rowLast.number, midlle);
            workSheet.mergeCells(rowLast.number, start + 1, rowLast.number, midlle);
            workSheet.unMergeCells(rowLast.number, midlle + 1, rowLast.number, last);
            workSheet.mergeCells(rowLast.number, midlle + 1, rowLast.number, last);
            rowLast.getCell(1).value = otherWorkSheet._rows[rowNumber - 1]._cells[1].value;
            rowLast.getCell(start + 1).value = otherWorkSheet._rows[rowNumber - 1]._cells[start + 1].value;
            rowLast.getCell(midlle + 1).value = otherWorkSheet._rows[rowNumber - 1]._cells[midlle + 1].value;
            rowLast.height = 12;
        } else {
            let headArrayValues = [];
            let row = otherWorkSheet._rows[rowNumber - 1];
            headArrayValues.push(row.values);
            workSheet.insertRows(rowInsertNum, headArrayValues, 'o');
            this.resetMerges(workSheet, rowInsertNum);
            let rowLast = workSheet.getRow(rowInsertNum);
            //针对插入的这一行  做特殊的处理
            for (let i = 0; i < rowLast._cells.length; i++) {
                rowLast._cells[i].style = otherWorkSheet._rows[rowNumber - 1]._cells[i].style;
            }

            workSheet.unMergeCells(rowLast.number, 1, rowLast.number, start);
            workSheet.mergeCells(rowLast.number, 1, rowLast.number, start);
            workSheet.unMergeCells(rowLast.number, start + 1, rowLast.number, midlle);
            workSheet.mergeCells(rowLast.number, start + 1, rowLast.number, midlle);
            workSheet.unMergeCells(rowLast.number, midlle + 1, rowLast.number, last);
            workSheet.mergeCells(rowLast.number, midlle + 1, rowLast.number, last);
            rowLast.getCell(1).value = otherWorkSheet._rows[rowNumber - 1]._cells[1].value;
            rowLast.getCell(start + 1).value = otherWorkSheet._rows[rowNumber - 1]._cells[start + 1].value;
            rowLast.getCell(midlle + 1).value = otherWorkSheet._rows[rowNumber - 1]._cells[midlle + 1].value;
            rowLast.height = 12;
        }
    }


    async addWorkSheet(workbook,workSheet,sheetName) {
        workSheet = _.cloneDeep(workSheet);//workSheet不能引用当前workbook的内部worksheet  因此进行clone
        let newWorksheet = workbook.addWorksheet(sheetName,workSheet);
        newWorksheet._rows = _.cloneDeep(workSheet._rows);
        newWorksheet._merges = _.cloneDeep(workSheet._merges);
        newWorksheet._columns = _.cloneDeep(workSheet._columns);
        newWorksheet.rowBreaks = _.cloneDeep(workSheet.rowBreaks);
        return newWorksheet;
    }


    /**
     * 分页最后一行，合并下一行；插入编制人、审核人、审定人格式数据
     * @param rowNumber
     * @param rowInsertNum
     * @param workSheet
     * @param otherWorkSheet
     * @param start
     * @param midlle
     * @param last
     * @returns {Promise<void>}
     */
    async copyRowsWithOtherSheetHejiV2(rowNumber, rowInsertNum, workSheet, otherWorkSheet, start, last) {
        let row6 = workSheet.getRow(rowInsertNum - 2);      //合计上一行
        if (row6._cells[1] == "") {
            //合计的上一行无数据，先把合计挪到上一行，再添加编制人审核人
            let rowHeji = workSheet.getRow(rowInsertNum - 1);    //合计行
            for (let i = 0; i < row6._cells.length; i++) {
                if (undefined != rowHeji._cells[i].value && null != rowHeji._cells[i].value) {
                    row6._cells[i].value = rowHeji._cells[i].value;
                }
                if (undefined != rowHeji._cells[i].style && null != rowHeji._cells[i].style) {
                    row6._cells[i].style = rowHeji._cells[i].style;
                }
            }

            let rowLast = workSheet.getRow(rowInsertNum - 1);
            //针对插入的这一行  做特殊的处理
            for (let i = 0; i < rowLast._cells.length; i++) {
                rowLast._cells[i].value = otherWorkSheet._rows[rowNumber - 1]._cells[i].value;
                rowLast._cells[i].style = otherWorkSheet._rows[rowNumber - 1]._cells[i].style;
            }
            workSheet.unMergeCells(rowLast.number, 1, rowLast.number, start);
            workSheet.mergeCells(rowLast.number, 1, rowLast.number, start);
            workSheet.unMergeCells(rowLast.number, start + 1, rowLast.number, last);
            workSheet.mergeCells(rowLast.number, start + 1, rowLast.number, last);
            rowLast.getCell(1).value = otherWorkSheet._rows[rowNumber - 1]._cells[1].value;
            rowLast.getCell(start + 1).value = otherWorkSheet._rows[rowNumber - 1]._cells[start + 1].value;
            rowLast.height = 12;
        } else {
            //合计的上一行有数据，直接在当前行赋值

            let headArrayValues = [];
            let row = otherWorkSheet._rows[rowNumber - 1];
            headArrayValues.push(row.values);
            workSheet.insertRows(rowInsertNum, headArrayValues, 'o');
            this.resetMerges(workSheet, rowInsertNum);
            let rowLast = workSheet.getRow(rowInsertNum);
            //针对插入的这一行  做特殊的处理
            for (let i = 0; i < rowLast._cells.length; i++) {
                rowLast._cells[i].value = otherWorkSheet._rows[rowNumber - 1]._cells[i].value;
                rowLast._cells[i].style = otherWorkSheet._rows[rowNumber - 1]._cells[i].style;
            }

            workSheet.unMergeCells(rowLast.number, 1, rowLast.number, start);
            workSheet.mergeCells(rowLast.number, 1, rowLast.number, start);
            workSheet.unMergeCells(rowLast.number, start + 1, rowLast.number, last);
            workSheet.mergeCells(rowLast.number, start + 1, rowLast.number, last);
            rowLast.getCell(1).value = otherWorkSheet._rows[rowNumber - 1]._cells[1].value;
            rowLast.getCell(start + 1).value = otherWorkSheet._rows[rowNumber - 1]._cells[start + 1].value;
            rowLast.height = 12;
        }
    }

    async insertMergeLastRow(workSheet, rowNumber, start, midlle, last) {
        let rowLast = workSheet.getRow(rowNumber);
        workSheet.unMergeCells(rowLast.number, 1, rowLast.number, 3);
        workSheet.mergeCells(rowLast.number, 1, rowLast.number, 3);
        workSheet.unMergeCells(rowLast.number, 4, rowLast.number, 7);
        workSheet.mergeCells(rowLast.number, 4, rowLast.number, 7);
        workSheet.unMergeCells(rowLast.number, 8, rowLast.number, 12);
        workSheet.mergeCells(rowLast.number, 8, rowLast.number, 12);
        rowLast.getCell(start).value = "编制人：";
        rowLast.getCell(midlle).value = "审核人：";
        rowLast.getCell(last).value = "审定人：";
    }


    async getRatioWidthSheet(workSheet) {

        let orientation = "portrait";
        let top = GljExcelEnum.A4Top/72;
        let bottom = GljExcelEnum.A4Bottom/72;
        let left = GljExcelEnum.A4Left/72;
        let right = GljExcelEnum.A4Right/72;
        let isLandScape = false;

        let columnWidthTotalWidth = 0;
        for (let i = 0; i < workSheet._columns.length; i++) {
            let columnWidth = workSheet._columns[i].width;
            columnWidthTotalWidth += columnWidth;
        }
        let differ = 0;
        if (workSheet.name.includes("实体项目预算表(横)")
            || workSheet.name.includes("措施项目预算表(横)")
            || workSheet.name.includes("实体项目预算表(横-省站标准)")
            || workSheet.name.includes("工程项目总价表-明细")
            || workSheet.name.includes("单项工程费总价表-明细")
        ) {
            isLandScape = true;
            differ = (GljExcelEnum.A4WidthHorizontal - GljExcelEnum.A4LeftHorizontal - GljExcelEnum.A4RightHorizontal);
            if (isLandScape) { //如果是横版
                orientation = "landscape";//较宽  横版
                top = GljExcelEnum.A4TopHorizontal/72;
                bottom = GljExcelEnum.A4BottomHorizontal/72;
                left = GljExcelEnum.A4LeftHorizontal/72;
                right = GljExcelEnum.A4RightHorizontal/72;
            }
        } else {
            differ = (GljExcelEnum.A4Width - GljExcelEnum.A4Left - GljExcelEnum.A4Right);
        }

        for (let i = 0; i < workSheet._columns.length; i++) {
            workSheet._columns[i].width = (workSheet._columns[i].width / columnWidthTotalWidth) * differ;
        }

        let pageSetupJsonObject = {
            "fitToPage": false,
            "margins": {
                "left": left,
                "right": right,
                "top": top,
                "bottom": bottom,
                "header": 0,
                "footer": 0
            },
            "paperSize": 9,
            "orientation": orientation,
            "horizontalDpi": 4294967295,
            "verticalDpi": 4294967295,
            "pageOrder": "downThenOver",
            "blackAndWhite": false,
            "draft": false,
            "cellComments": "None",
            "errors": "displayed",
            "scale": 100,
            "fitToWidth": 1,
            "fitToHeight": 1,
            "firstPageNumber": 1,
            "useFirstPageNumber": false,
            "usePrinterDefaults": false,
            "copies": 1,
            "showRowColHeaders": false,
            "showGridLines": false,
            "horizontalCentered": true,
            "verticalCentered": false
        }
        workSheet.pageSetup = pageSetupJsonObject;

        //针对列宽
        let columnWidthTotal = 0;
        for (let i = 0; i < workSheet._columns.length; i++) {
            let columnWidth = workSheet._columns[i].width;
            columnWidthTotal += columnWidth;
        }
        let differWidth = 0;
        if (ObjectUtils.isNotEmpty(workSheet.pageSetup) && ObjectUtils.isNotEmpty(workSheet.pageSetup.orientation) && workSheet.pageSetup.orientation == "portrait") {
            differWidth = GljExcelEnum.A4Width-GljExcelEnum.A4Left-GljExcelEnum.A4Right;
        }else {
            differWidth = GljExcelEnum.A4WidthHorizontal-GljExcelEnum.A4LeftHorizontal-GljExcelEnum.A4RightHorizontal;
        }

        let columnWidthTotal2 = 0;
        for (let i = 0; i < workSheet._columns.length-1; i++) {
            workSheet._columns[i].width = Number(this._roundAndPad(((workSheet._columns[i].width/columnWidthTotal)*differWidth),2));
            columnWidthTotal2+= workSheet._columns[i].width;
        }
        workSheet._columns[workSheet._columns.length-1].width = await GljExcelOperateUtil.colWidthToFixed(differWidth-columnWidthTotal2);

    }

    async traversalRowToCellBottom(row) {
        for (let i = 0; i < row._cells.length; i++) {
            let cell = row._cells[i];
            cell.style.alignment.vertical = 'bottom';
        }
    }

    async cellHeJiMiddle(workSheet,content) {
        let result = this.findValueCellList(workSheet,content);
        if (ObjectUtils.isNotEmpty(result)) {
            for (let i = 0; i < result.length; i++) {
                let resultElement = result[i];
                let alignmentObject = _.cloneDeep(resultElement.cell.style)
                alignmentObject.alignment.horizontal = "center";
                resultElement.cell.style = alignmentObject;
            }
        }
    }

    //填充合计行
    async fillTotalContent(worksheet,functionCallback,findValue) {
        let map = functionCallback;
        let heJiCell = this.findValueCell(worksheet,findValue);
        let row = worksheet.getRow(heJiCell.cell._row._number);
        for (let [mapKeyColumnNum, value] of map) {
            if (typeof value == "number") {
                row._cells[mapKeyColumnNum-1].value = this._roundAndPad(value,2);
            }else {
                if (ObjectUtils.isNotEmpty(value.precisionReserve)) {
                    row._cells[mapKeyColumnNum-1].value = value.amount;
                }else {
                    row._cells[mapKeyColumnNum-1].value = this._roundAndPad(value.amount, 2);
                }
            }
        }
    }
    //填充合计行(保留位数)
    async fillTotalContentPrecision(worksheet,functionCallback,findValue,precision) {
        let map = functionCallback;
        let heJiCell = this.findValueCell(worksheet,findValue);
        let row = worksheet.getRow(heJiCell.cell._row._number);
        for (let [mapKeyColumnNum, value] of map) {
            if (typeof value == "number") {
                row._cells[mapKeyColumnNum-1].value = Number(value) == 0?"":this._roundAndPad(Number(value), precision);
            }else {
                if (ObjectUtils.isNotEmpty(value.precisionReserve)) {
                    row._cells[mapKeyColumnNum-1].value = Number(value.amount) == 0?"":this._roundAndPad(Number(value.amount), precision);
                }else {
                    row._cells[mapKeyColumnNum-1].value = Number(value.amount) == 0?"":this._roundAndPad(Number(value.amount), precision);
                }
            }
        }
    }

    judgeIsDeType(element) {
        if ((element.type == BranchProjectLevelConstant.de
            || element.type == DeTypeConstants.DE_TYPE_RESOURCE
            || element.type == DeTypeConstants.DE_TYPE_USER_DE
            || element.type == DeTypeConstants.DE_TYPE_USER_RESOURCE
            || element.type == DeTypeConstants.DE_TYPE_ANZHUANG_FEE
            || element.type == DeTypeConstants.DE_TYPE_ZHUANSHI_FEE)) {
            return true;
        }
        return false;
    }

    async writeDataToSheet(data, worksheet, num,copyDistanceNum,sheetId,args) {
        let headCount = 4;//表示表头行索引的最大值
        if (ObjectUtils.isNotEmpty(num)) {
            headCount = num;
        }
        worksheet.rowDataTypelist = [{},];//维护sheet页 每一行的行属性  索引为行号 值为 { field:"data"、"head"}
        for (let i = 0; i <= headCount; i++) {
            worksheet.rowDataTypelist.splice(i+1,0,{field:"head"});
        }
        let copyDistance = 0;//定义保留行数  ex:表1-6模板的最后两行进行保留   默认没有合计行,数据会将sheet页填满
        if (ObjectUtils.isNotEmpty(copyDistanceNum)) {
            copyDistance = copyDistanceNum;
        }
        for (let i = headCount+1; i < worksheet._rows.length; i++) {
            if (worksheet._rows.length - i <= copyDistance) {
                worksheet.rowDataTypelist.splice(i+1,0,{field:"reserve"});
            }else {
                worksheet.rowDataTypelist.splice(i+1,0,{field:"blank"});
            }
        }
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            headCount++;//记录当前数据插入行的索引
            let rowObject = worksheet._rows[headCount];
            let rowObjectLast = worksheet._rows[headCount-1];
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //复制当前数据插入行的格式到增加行
                for (let m = 0; m < rowObjectLast._cells.length; m++) {
                    list.push('');
                }
                rowNext = worksheet.insertRow(headCount + 1, list, 'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
                worksheet.rowDataTypelist.splice(headCount+1,0,{field:"data"});
                await this.resetMerges(worksheet, headCount + 1);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = this.getMergeName(worksheet._merges, rowObjectLast._cells[m]);
                    if (mergeName != null) {
                        let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number, left, rowNext.number, right]);
                        worksheet.mergeCells([rowNext.number, left, rowNext.number, right]);
                    }
                    rowNext._cells[m].style = rowObjectLast._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            if (copyDistance==0) rowObject = rowNext;//边界条件
            countRow = i;
            worksheet.rowDataTypelist[headCount+1].field = "data";
            //对数据写入行的格式进行调整
            if (ObjectUtils.isNotEmpty(rowObject._cells[0].style) && ObjectUtils.isNotEmpty(rowObject._cells[0].style.border)&&
                ObjectUtils.isNotEmpty(rowObject._cells[0].style.border.bottom)&& ObjectUtils.isNotEmpty(rowObject._cells[0].style.border.bottom.style)
                && rowObject._cells[0].style.border.bottom.style == "medium"
            ) {
                for (let j = 0; j < rowObject._cells.length; j++) {
                    rowObject._cells[j].style.border.bottom.style = "thin";
                }
            }
            await this.insertSheetData(data, rowObject, worksheet, countRow,sheetId,args);
        }
    }

    async updateSheetValue(worksheet,oldValue,newValue) {
        for (let i = 0; i < worksheet._rows.length; i++) {
            let row = worksheet._rows[i];
            for (let j = 0; j < row._cells.length; j++) {
                let cell = row._cells[j];
                if (cell.value == oldValue) {
                    cell.value = newValue;
                }
            }
        }
    }

    async mergeLastDataInSheet(worksheet) {
        let mergeMaps = new Map(Object.entries(worksheet._merges));
        //定位到最后一行的行号
        let dataLastRowNum = 0;
        for (let i = 0; i < worksheet.rowDataTypelist.length; i++) {
            let element = worksheet.rowDataTypelist[i];
            if (ObjectUtils.isNotEmpty(element.field) && element.field == "data") {
                dataLastRowNum = i;//索引即为行号
            }
        }
        if (dataLastRowNum == 0) return;
        let lastDataRow = worksheet._rows[dataLastRowNum-1];
        let cellValue = "";
        for (let m = 0; m < lastDataRow._cells.length; m++) {
            let cell = lastDataRow._cells[m];
            if (ObjectUtils.isNotEmpty(cell.value)) {
                cellValue = cell.value;
            };
            //获取模板行的合并单元格
            let mergeName = this.getMergeName(worksheet._merges, lastDataRow._cells[m]);
            if (mergeName != null) {
                let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
                worksheet.unMergeCells([lastDataRow.number, left, lastDataRow.number, right]);
            }
        }
        for (let m = 0; m < lastDataRow._cells.length; m++) {
            let cell = lastDataRow._cells[m];
            cell.value = cellValue;
            let alignmentObject = _.cloneDeep(cell.style);
            if (ObjectUtils.isEmpty(alignmentObject.alignment)) alignmentObject.alignment = {};
            alignmentObject.alignment.horizontal = "center";
            cell.style = alignmentObject;
        }
        worksheet.mergeCells([lastDataRow.number, 1, lastDataRow.number, lastDataRow._cells.length]);
        await this.rowHeightPoint(worksheet,lastDataRow.number);
    }

    async mergeRowsForPointNumList(worksheet,rowsListNum) {
        let mergeMaps = new Map(Object.entries(worksheet._merges));
        for (let i = 0; i < rowsListNum.length; i++) {
            let numElement = rowsListNum[i];
            if (ObjectUtils.isEmpty(numElement)) continue;
            let lastDataRow = worksheet._rows[numElement-1];
            let cellValue = "";
            for (let m = 0; m < lastDataRow._cells.length; m++) {
                let cell = lastDataRow._cells[m];
                if (ObjectUtils.isNotEmpty(cell.value)) {
                    cellValue = cell.value;
                };
                //获取模板行的合并单元格
                let mergeName = this.getMergeName(worksheet._merges, lastDataRow._cells[m]);
                if (mergeName != null) {
                    let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
                    worksheet.unMergeCells([lastDataRow.number, left, lastDataRow.number, right]);
                }
            }
            for (let m = 0; m < lastDataRow._cells.length; m++) {
                let cell = lastDataRow._cells[m];
                cell.value = cellValue;
                let alignmentObject = _.cloneDeep(cell.style);
                if (ObjectUtils.isEmpty(alignmentObject.alignment)) alignmentObject.alignment = {};
                alignmentObject.alignment.horizontal = "center";
                cell.style = alignmentObject;
            }
            worksheet.mergeCells([lastDataRow.number, 1, lastDataRow.number, lastDataRow._cells.length]);
            await this.rowHeightPoint(worksheet,numElement);
        }
    }

    async mergeRowsForPointNumListAndCol(worksheet,rowsListNum,startCol,endCol) {
        let mergeMaps = new Map(Object.entries(worksheet._merges));
        for (let i = 0; i < rowsListNum.length; i++) {
            let numElement = rowsListNum[i];
            let lastDataRow = worksheet._rows[numElement-1];
            let cellValue = "";
            for (let m = startCol-1; m < endCol; m++) {
                let cell = lastDataRow._cells[m];
                if (ObjectUtils.isNotEmpty(cell.value)) {
                    cellValue = cell.value;
                };
                //获取模板行的合并单元格
                let mergeName = this.getMergeName(worksheet._merges, lastDataRow._cells[m]);
                if (mergeName != null) {
                    let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
                    worksheet.unMergeCells([lastDataRow.number, left, lastDataRow.number, right]);
                }
            }
            for (let m = startCol-1; m < endCol; m++) {
                let cell = lastDataRow._cells[m];
                cell.value = cellValue;
            }
            worksheet.mergeCells([lastDataRow.number, startCol, lastDataRow.number, endCol]);
            await this.rowHeightPoint(worksheet,numElement);
        }
    }


    //数据表插入数据
    async insertSheetData(data, rowObject, worksheet, countRow,sheetId,args) {
        let precision = args['precision'];
        let ceping = args?.ceping;
        //--------单位层级-------------------------------
        let {service} = EE.app;
        if (worksheet.name == '单位工程费用表(多专业取费)') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;//序号
                if (cell.col == 2) {
                    cell.value = data[countRow].name;//费用名称
                }
                if (cell.col == 4) {
                    cell.value = data[countRow].instructions;//取费说明
                }
                if (cell.col == 6) {
                    if (ObjectUtils.isNotEmpty(data[countRow].rate)) {
                        if (NumberUtil.countDecimalPlaces(data[countRow].rate) > 2) {
                            cell.value = data[countRow].rate;
                        } else {
                            cell.value = ObjectUtils.isNotEmpty(data[countRow].rate) ? this._roundAndPadNoZero(Number(data[countRow].rate), precision.COST_SUMMARY.SUMMARY.freeRate) : "";//费率
                        }
                    }
                }
                if (cell.col == 7) {
                    let value = data[countRow].price;
                    if (ObjectUtils.isNumber(value)) {
                        cell.value = this._roundAndPad(Number(value), precision.COST_SUMMARY.je);//费用金额
                    } else {
                        cell.value = value;//费用金额
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }
        if (worksheet.name == '单位工程费用表') {
            if (worksheet.columns.length==7) {
                for (let j = 0; j < rowObject._cells.length; j++) {
                    let cell = rowObject._cells[j];
                    if (cell.col == 1) cell.value = data[countRow].dispNo;//序号
                    if (cell.col == 2) {
                        cell.value = data[countRow].name;//费用名称
                    }
                    if (cell.col == 4) {
                        cell.value = data[countRow].instructions;//取费说明
                    }
                    if (cell.col == 6) {
                        cell.value = ObjectUtils.isNotEmpty(data[countRow].rate)?this._roundAndPadNoZero(Number(data[countRow].rate), precision.COST_SUMMARY.SUMMARY.freeRate):"";//费率
                    }
                    if (cell.col == 7) {
                        let value = data[countRow].price;
                        if (ObjectUtils.isNumber(value)) {
                            cell.value = this._roundAndPad(Number(value), precision.COST_SUMMARY.je);//费用金额
                        } else {
                            cell.value = value;//费用金额
                        }
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                }
            }else {
                for (let j = 0; j < rowObject._cells.length; j++) {
                    let cell = rowObject._cells[j];
                    if (cell.col == 1) {
                        cell.value = data[countRow].dispNo;//序号
                    }
                    if (cell.col == 2) {
                        cell.value = data[countRow].name;//名称
                    }
                    if (cell.col == 4) {
                        let value = data[countRow].price;
                        if (ObjectUtils.isNumber(value)) {
                            cell.value = this._roundAndPad(Number(value), precision.COST_SUMMARY.je);//费用金额
                        } else {
                            cell.value = value;//费用金额
                        }
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                    if (cell.col == 6) {
                        cell.value = data[countRow].remark;//备注
                    }
                }
            }
        }
        if (worksheet.name == '实体项目预算表(竖-省站标准)'||worksheet.name == '单位工程预算表(竖)'
        ) {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                //精度设置
                let precision =  service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(data[countRow].constructId);
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].deCode;//定额编号
                if (cell.col == 3) cell.value = data[countRow].deName;//子目名称
                if (cell.col == 5) cell.value = data[countRow].unit
                if (cell.col == 6){
                    let value = data[countRow].quantity;//工程量
                    if (ObjectUtils.isNotEmpty(data[countRow].quantity)) {
                        cell.value = ObjectUtils.isNotEmpty(value) ? this._roundAndPad(Number(value), 3) : "";
                        if (data[countRow]?.type === "05") {
                            cell.value = ceping === true ? this._roundAndPadCeping(value, 4) : cell.value; //测评
                        } else {
                            cell.value = ceping === true ? this._roundAndPadCeping(value, 3) : cell.value; //测评
                        }
                    }
                }
                if (cell.col == 7){
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod?data[countRow].price:data[countRow].baseJournalPrice;//单价
                    cell.value = value;//单价
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = ObjectUtils.isNotEmpty(cell.value)?this._roundAndPad(Number(cell.value), precision.EDIT.DE.price):"";
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; //测评
                    }
                }
                if (cell.col == 8) {
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod?data[countRow].totalNumber:data[countRow].baseJournalTotalNumber;//合价
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = ObjectUtils.isNotEmpty(cell.value)?this._roundAndPad(Number(cell.value), precision.EDIT.DE.totalNumber):"";
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; //测评
                    }
                }

                if (data[countRow].type !== '05') {
                    //非主材显示
                    if (cell.col == 10) {
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? data[countRow].rTotalSum : data[countRow].rdTotalSum;//人工费
                        cell.value = this._roundAndPad(Number(value), precision.EDIT.DE.rTotalSum);
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value;
                    }
                    if (cell.col == 11) {
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? data[countRow].cTotalSum : data[countRow].cdTotalSum;//材料费
                        cell.value = this._roundAndPad(Number(value), precision.EDIT.DE.cTotalSum);
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value;
                    }
                    if (cell.col == 12) {
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? data[countRow].jTotalSum : data[countRow].jdTotalSum;//机械费
                        cell.value = this._roundAndPad(Number(value), precision.EDIT.DE.jTotalSum);
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value;
                    }
                    if (cell.col == 13) {
                        cell.value = this._roundAndPad(Number(data[countRow].工日合计), precision.EDIT.DE.price);
                    }
                }
            }
        }
        //应新的要求  这三张表的工程量保留三位  不足位补0
        if (worksheet.name == '实体项目预算表(竖)'||worksheet.name == '实体项目预算表(横)'
        ) {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                //精度设置
                let precision =  service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(data[countRow].constructId);
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].deCode;//定额编号
                if (cell.col == 3) cell.value = data[countRow].deName;//子目名称
                if (cell.col == 5) cell.value = data[countRow].unit
                if (cell.col == 6){
                    let value = data[countRow].quantity;//工程量
                    cell.value = ObjectUtils.isNotEmpty(value)? this._roundAndPad(Number(value),3):"";
                    if(data[countRow]?.type === "05"){
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 4) : cell.value; //测评
                    }else{
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 3) : cell.value; //测评
                    }
                }
                if (cell.col == 7){
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod?data[countRow].price:data[countRow].baseJournalPrice;//单价
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = ObjectUtils.isNotEmpty(cell.value)?this._roundAndPad(Number(cell.value), precision.EDIT.DE.price):"";
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 8) {
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod?data[countRow].totalNumber:data[countRow].baseJournalTotalNumber;//合价
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = ObjectUtils.isNotEmpty(cell.value)?this._roundAndPad(Number(cell.value), precision.EDIT.DE.totalNumber):"";
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }

                if (data[countRow].type !== '05') {
                    //非主材显示
                    if (cell.col == 10) {
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? data[countRow].rTotalSum : data[countRow].rdTotalSum;//人工费
                        cell.value = this._roundAndPad(Number(value), precision.EDIT.DE.rTotalSum);
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                    if (cell.col == 11) {
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? data[countRow].cTotalSum : data[countRow].cdTotalSum;//材料费
                        cell.value = this._roundAndPad(Number(value), precision.EDIT.DE.cTotalSum);
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                    if (cell.col == 12) {
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? data[countRow].jTotalSum : data[countRow].jdTotalSum;//机械费
                        cell.value = this._roundAndPad(Number(value), precision.EDIT.DE.jTotalSum);
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                    if (cell.col == 13) {
                        cell.value = this._roundAndPad(Number(data[countRow].工日合计),3);
                    }
                }
            }
        }
        if (worksheet.name == '实体项目预算表(自然单位)') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                //精度设置
                let precision =  service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(data[countRow].constructId);
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].deCode;//定额编号
                if (cell.col == 3) cell.value = data[countRow].deName;//子目名称
                if (cell.col == 5) cell.value = data[countRow].unit
                if (cell.col == 6){
                    let value = data[countRow].quantity;//工程量
                    if (data[countRow].type == DeTypeConstants.DE_TYPE_DE
                        ||data[countRow].type==DeTypeConstants.DE_TYPE_RESOURCE
                        ||data[countRow].type==DeTypeConstants.DE_TYPE_USER_DE
                        ||data[countRow].type==DeTypeConstants.DE_TYPE_USER_RESOURCE
                        ||data[countRow].type==DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                        ||data[countRow].type==DeTypeConstants.DE_TYPE_ZHUANSHI_FEE
                    ) {
                        cell.value = ObjectUtils.isNotEmpty(value)? NumberUtil.numberScale(value,5):"";
                    }else {
                        cell.value = ObjectUtils.isNotEmpty(value)? this._roundAndPad(Number(value),3):"";
                    }
                    if(data[countRow]?.type === "05"){
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 4) : cell.value; //测评
                    }else{
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 3) : cell.value; //测评
                    }
                }
                if (cell.col == 7){
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod?data[countRow].price:data[countRow].baseJournalPrice;//单价
                    if (ObjectUtils.isNotEmpty(value)) {
                        cell.value = ObjectUtils.isNotEmpty(value)?this._roundAndPad(Number(value), precision.EDIT.DE.price):"";
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 8) {
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod?data[countRow].totalNumber:data[countRow].baseJournalTotalNumber;//合价
                    if (ObjectUtils.isNotEmpty(value)) {
                        cell.value = ObjectUtils.isNotEmpty(value)?this._roundAndPad(Number(value), precision.EDIT.DE.totalNumber):"";
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }

                if (data[countRow].type !== '05') {
                    //非主材显示
                    if (cell.col == 10) {
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? data[countRow].rTotalSum : data[countRow].rdTotalSum;//人工费
                        cell.value = this._roundAndPad(Number(value), precision.EDIT.DE.rTotalSum);
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                    if (cell.col == 11) {
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? data[countRow].cTotalSum : data[countRow].cdTotalSum;//材料费
                        cell.value = this._roundAndPad(Number(value), precision.EDIT.DE.cTotalSum);
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                    if (cell.col == 12) {
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? data[countRow].jTotalSum : data[countRow].jdTotalSum;//机械费
                        cell.value = this._roundAndPad(Number(value),precision.EDIT.DE.jTotalSum);
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                    if (cell.col == 13) {
                        cell.value = this._roundAndPad(Number(data[countRow].工日合计),3);
                    }
                }
            }
        }
        if (worksheet.name == '措施项目预算表(竖)'||worksheet.name == '措施项目预算表(横)'||worksheet.name == '措施项目预算表(自然单位)') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                //精度设置
                let precision =  service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(data[countRow].constructId);
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].deCode;//定额编号
                if (cell.col == 3) cell.value = data[countRow].deName;//子目名称
                if (cell.col == 5) {
                    cell.value = data[countRow].unit;
                    if(data[countRow].type === DeTypeConstants.DE_TYPE_DELIST){
                        cell.value = "项";
                    }
                }
                if (cell.col == 6) {
                    let value = data[countRow].quantity;
                    if (ObjectUtils.isNotEmpty(value)) {
                        cell.value = this._roundAndPad(Number(value), 3);
                        if (data[countRow]?.type === "05") {
                            cell.value = ceping === true ? this._roundAndPadCeping(value, 4) : cell.value; //测评
                        } else {
                            cell.value = ceping === true ? this._roundAndPadCeping(value, 3) : cell.value; //测评
                        }
                    }
                }
                if (cell.col == 7){
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod?data[countRow].price:data[countRow].baseJournalPrice;//单价
                    if (data[countRow].type !== BranchProjectLevelConstant.qd) {
                        if (ObjectUtils.isNotEmpty(value)) {
                            cell.value = this._roundAndPad(Number(value), precision.EDIT.DE.price);
                            cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                        }
                    }
                }
                //合价及人材机 为空也要展示为0.00
                if (cell.col == 8) {
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod?data[countRow].totalNumber:data[countRow].baseJournalTotalNumber;//合价
                    cell.value = this._roundAndPad(Number(value), precision.EDIT.DE.totalNumber);
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 10){
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? data[countRow].rTotalSum : data[countRow].rdTotalSum;//人工费
                    if (ObjectUtils.isEmpty(data[countRow].bkjzcsxm)) {
                        cell.value = this._roundAndPad(Number(value), precision.EDIT.DE.rTotalSum);
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                }
                if (cell.col == 11) {
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? data[countRow].cTotalSum : data[countRow].cdTotalSum;//材料费
                    if (ObjectUtils.isEmpty(data[countRow].bkjzcsxm)) {
                        cell.value = this._roundAndPad(Number(value), precision.EDIT.DE.cTotalSum);
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                }
                if (cell.col == 12) {
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? data[countRow].jTotalSum : data[countRow].jdTotalSum;//机械费
                    if (ObjectUtils.isEmpty(data[countRow].bkjzcsxm)) {
                        cell.value = this._roundAndPad(Number(value), precision.EDIT.DE.jTotalSum);
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                }
                if (cell.col == 13) {
                    if (ObjectUtils.isEmpty(data[countRow].bkjzcsxm)) {
                        cell.value = data[countRow].工日合计;
                        if (ObjectUtils.isNotEmpty(cell.value)) {
                            cell.value = this._roundAndPad(Number(cell.value), 3);
                        }
                    }
                }
            }
        }
        if (worksheet.name == '单位工程工程量计算书(措施)'||worksheet.name == '单位工程工程量计算书(实体)') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].deCode;//定额编号
                if (cell.col == 3) cell.value = data[countRow].deName;//子目名称
                if (cell.col == 5) cell.value = data[countRow].unit;
                if (cell.col == 6){
                    let value = data[countRow].quantity;
                    cell.value = value;
                    if(data[countRow]?.type === "05"){
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 4) : cell.value; //测评
                    }else{
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 3) : cell.value; //测评
                    }
                }
                if (cell.col == 8) cell.value = data[countRow].quantityExpression;
            }
        }

        if (worksheet.name == '分项工程人材机汇总表(实体)'||worksheet.name == '分项工程人材机汇总表(措施)') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                //精度设置
                let precision =  service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(data[countRow].constructId);
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].deCode;//定额编号
                if (cell.col == 3) cell.value = data[countRow].deName;//子目名称
                if (cell.col == 5) cell.value = data[countRow].unit;
                if (cell.col == 6){
                    let value = data[countRow].resQty;//含量
                    cell.value = value;
                    cell.value = ObjectUtils.isNotEmpty(cell.value)? this._roundAndPad(Number(cell.value),3):"";
                    // if(ObjectUtils.isNotEmpty(cell.value)) {
                    //     cell.value = NumberUtil.numberScale(cell.value, precision.DETAIL.RCJ.resQty);
                    // }
                    cell.value = ceping === true && ObjectUtils.isNotEmpty(value) && value !== '' ? this._roundAndPadCeping(value, 4) : cell.value; //测评
                    if (data[countRow].type == DeTypeConstants.DE_TYPE_DE
                        ||data[countRow].type==DeTypeConstants.DE_TYPE_RESOURCE
                        ||data[countRow].type==DeTypeConstants.DE_TYPE_USER_DE
                        ||data[countRow].type==DeTypeConstants.DE_TYPE_USER_RESOURCE
                        ||data[countRow].type==DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                        ||data[countRow].type==DeTypeConstants.DE_TYPE_ZHUANSHI_FEE
                    ) {
                        cell.value = ""
                    }
                }
                if (cell.col == 7){
                    let value = data[countRow].quantity;
                    cell.value = value;
                    //     等于 Number(data[countRow].type)  === NAN  是定额   ，不然则是 人材机
                        if(data[countRow].isDeResource === 1 ||  isNaN(Number(data[countRow].type)) ){
                            //cell.value = NumberUtil.numberScale( cell.value ,precision.EDIT.DERCJ.quantity);
                            cell.value = ObjectUtils.isNotEmpty(cell.value)?this._roundAndPad(Number(cell.value), 3):"";
                        }else{
                            // cell.value = NumberUtil.numberScale( cell.value ,precision.EDIT.DE.quantity);
                            cell.value = ObjectUtils.isNotEmpty(cell.value)?this._roundAndPad(Number(cell.value), 2):"";
                        }

                    if(data[countRow]?.type === "05" || data[countRow]?.kind === 5){
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 4) : cell.value; //测评
                    }else{
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 3) : cell.value; //测评
                    }
                }
                if (cell.col == 9){
                    let value = data[countRow].price;
                    cell.value = value;
                    if(ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = this._roundAndPad(Number(cell.value), precision.EDIT.DE.price)
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; //测评
                }
                if (cell.col == 10){   //为空也展示为0.00
                    let value = data[countRow].totalNumber;
                    cell.value = value;
                    cell.value = this._roundAndPad(Number(cell.value), precision.EDIT.DE.totalNumber)
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; //测评
                }

            }
        }

        if (worksheet.name == '独立费表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                //精度设置
                let precision =  service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(data[countRow].constructId);
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].name;
                if (cell.col == 4) cell.value = data[countRow].unit;
                if (cell.col == 5) {
                    let value = data[countRow].quantity;
                    cell.value = ObjectUtils.isNotEmpty(value)?NumberUtil.numberScale( Number(value) ,5):null;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 3) : cell.value; // 评测
                }
                if (cell.col == 7) {
                    let value = data[countRow].price;
                    if (data[countRow].name === '独立费') {
                        cell.value =  ObjectUtils.isNotEmpty(value)? this._roundAndPad(Number(value), precision.UNIT_DLF.price) :null
                    } else {
                        cell.value = this._roundAndPad(Number(value), precision.UNIT_DLF.price);
                        cell.value = ceping === true ? this._roundAndPadCeping(Number(value), 2) : cell.value; // 评测
                    }
                }
                if (cell.col == 8){
                    let value = data[countRow].totalPrice;
                    cell.value = this._roundAndPad(Number(value), precision.UNIT_DLF.totalPrice);
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }
        if (worksheet.name == '单位工程人材机汇总表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                //精度设置
                let precision =  service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(data[countRow].constructId);
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].materialName;
                if (cell.col == 3) cell.value = data[countRow].unit;
                if (cell.col == 5){
                    let value = data[countRow].totalNumber;//数量
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = this._roundAndPad(Number(cell.value),4);
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 4) : cell.value; // 评测
                }
                if (cell.col == 6) {
                    let value = data[countRow].baseJournalPrice;//不含税基期价
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = this._roundAndPad(Number(cell.value), precision.RCJ_COLLECT.baseJournalPrice);
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 8){
                    let value = data[countRow].marketPrice;//不含税市场价
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = this._roundAndPad(Number(cell.value), precision.RCJ_COLLECT.marketPrice);
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 9){
                    let value = data[countRow].totalMarket;//市场价合计
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = this._roundAndPad(Number(cell.value), precision.RCJ_COLLECT.total);
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }
        if (worksheet.name == '单位工程人材机价差表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                //精度设置
                let precision =  service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(data[countRow].constructId);
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].materialName;
                if (cell.col == 4) cell.value = data[countRow].unit;
                if (cell.col == 5){
                    let value = data[countRow].totalNumber;
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = this._roundAndPad(Number(cell.value),4);
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 4) : cell.value; // 评测
                }
                if (cell.col == 6) {
                    let value = data[countRow].baseJournalPrice;
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = this._roundAndPad(Number(cell.value),precision.RCJ_COLLECT.baseJournalPrice);
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 8) {
                    let value = data[countRow].marketPrice;
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = this._roundAndPad(Number(cell.value), precision.RCJ_COLLECT.marketPrice);
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 9) {
                    let value = data[countRow].priceDiffer;
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = this._roundAndPad(Number(cell.value), precision.RCJ_COLLECT.jc);
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 10) {
                    let value = data[countRow].priceDifferTotal;
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = this._roundAndPad(Number(cell.value), precision.RCJ_COLLECT.jchj);
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }
        if (worksheet.name == '单位工程甲供材料表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                //精度设置
                let precision =  service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(data[countRow].constructId);
                if (cell.col == 1) cell.value = countRow + 1;
                if (cell.col == 2) cell.value = data[countRow].materialName;
                if (cell.col == 3) cell.value = data[countRow].unit;
                if (cell.col == 4){
                    let value = data[countRow].donorMaterialNumber;
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = this._roundAndPad(Number(cell.value), 4);
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 4) : cell.value; // 评测
                }
                if (cell.col == 5){
                    let value = data[countRow].marketPrice;//不含税市场价
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = NumberUtil.numberScale( Number(cell.value) ,precision.RCJ_COLLECT.marketPrice).toFixed(precision.RCJ_COLLECT.marketPrice);
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 6) {
                    let value = data[countRow].total;//甲供合计
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = NumberUtil.numberScale( Number(cell.value) ,precision.RCJ_COLLECT.total).toFixed(precision.RCJ_COLLECT.total);
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }
        if (worksheet.name == '单位工程三材汇总表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                //精度设置
                let precision =  service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(data[countRow].constructId);
                if (cell.col == 1) cell.value = countRow + 1;
                if (cell.col == 2) cell.value = data[countRow].name;
                if (cell.col == 4) cell.value = data[countRow].unit;
                if (cell.col == 5){
                    let value = data[countRow].scCount;
                    cell.value = value;
                    if (ObjectUtils.isNotEmpty(cell.value)) {
                        cell.value = NumberUtil.numberScale( Number(cell.value) ,precision.COST_ANALYSIS.scNumber).toFixed(precision.COST_ANALYSIS.scNumber);
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 4) : cell.value; // 评测
                }
            }
        }
        if (worksheet.name == '单位工程主材表'||worksheet.name == '单位工程设备表'||worksheet.name == '预拌砼汇总表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                //精度设置
                let precision =  service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(data[countRow].constructId);
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].materialName;
                if (cell.col == 4) cell.value = data[countRow].unit;
                if (cell.col == 5){
                    let value = data[countRow].totalNumber;
                    cell.value = value;

                    cell.value = this._roundAndPad(Number(cell.value),4);
                    // cell.value = NumberUtil.numberScale( Number(cell.value) ,precision.RCJ_COLLECT.totalNumber);
                    if ( data[countRow].materialName === '合计') {
                        cell.value =  ObjectUtils.isNotEmpty(data[countRow].totalNumber)?  data[countRow].totalNumber:null
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 4) : cell.value; // 评测

                }
                if (cell.col == 7){
                    let value = data[countRow].marketPrice;
                    cell.value = value;
                    cell.value = this._roundAndPad(Number(cell.value), precision.RCJ_COLLECT.marketPrice)
                    if ( data[countRow].materialName === '合计') {
                        cell.value =  ObjectUtils.isNotEmpty(data[countRow].marketPrice)?  cell.value:null
                    }
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 8){
                    let value = data[countRow].marketPriceTotal;
                    cell.value = value;
                    cell.value = this._roundAndPad(Number(cell.value), precision.RCJ_COLLECT.total)
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }
        if (worksheet.name == '主要材料价格表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].materialCode;
                if (cell.col == 3) cell.value = data[countRow].materialName;
                if (cell.col == 4) cell.value = data[countRow].specification;
                if (cell.col == 6) cell.value = data[countRow].unit;
                if (cell.col == 7){
                    let value = data[countRow].totalNumber;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 8){
                    let value = data[countRow].baseJournalPrice;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 10){
                    let value = data[countRow].marketPrice;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 11){
                    let value = data[countRow].marketPriceTotal;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }

        if (worksheet.name == '水电费明细表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = countRow+1;
                if (cell.col == 2) cell.value = data[countRow].projectMajor;
                if (cell.col == 3) cell.value = data[countRow].calculateBase;//计算基数
                if (cell.col == 5) cell.value = ObjectUtils.isNotEmpty(data[countRow].waterRate)?this._roundAndPad(Number(data[countRow].waterRate),2):data[countRow].waterRate;//水费扣除系数
                if (cell.col == 6) cell.value = ObjectUtils.isNotEmpty(data[countRow].electricRate)?this._roundAndPad(Number(data[countRow].electricRate),2):data[countRow].electricRate;//电费扣除系数
                if (cell.col == 7) cell.value = ObjectUtils.isNotEmpty(data[countRow].totalRate)?this._roundAndPad(Number(data[countRow].totalRate),2):data[countRow].totalRate;//水电费扣除系数
                if (cell.col == 8){
                    let value = data[countRow].waterCost;
                    cell.value = ObjectUtils.isNotEmpty(data[countRow].waterCost)?this._roundAndPad(Number(data[countRow].waterCost),2):data[countRow].waterCost;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 9){
                    let value = data[countRow].electricCost;
                    cell.value = ObjectUtils.isNotEmpty(data[countRow].electricCost)?this._roundAndPad(Number(data[countRow].electricCost),2):data[countRow].electricCost;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 10){
                    let value = data[countRow].totalCost;
                    cell.value = ObjectUtils.isNotEmpty(data[countRow].totalCost)?this._roundAndPad(Number(data[countRow].totalCost),2):data[countRow].totalCost;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }

        if (worksheet.name == '水电费明细表(独立设置)') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 8){
                    let value = data[countRow].waterCost;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 9){
                    let value = data[countRow].electricCost;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 10){
                    let value = data[countRow].totalCost;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }

        if (worksheet.name == '安全文明施工费明细表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                //精度设置
                let precision =  service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(data[countRow].constructId);
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].constructMajorTypeName;
                if (cell.col == 3) {
                    cell.value = data[countRow].qfBase;
                }
                if (cell.col == 5){
                    let value = data[countRow].calculateMoney;
                    cell.value = value;
                    cell.value = ObjectUtils.isNotEmpty(cell.value)? this._roundAndPad(Number(cell.value), precision.COST_SUMMARY.calculationBase) :null;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 7){
                    cell.value = data[countRow].rate;//不处理精度
                    if (ObjectUtils.isNotEmpty(data[countRow].rate)) {
                        if (NumberUtil.countDecimalPlaces(data[countRow].rate) > 2) {
                            cell.value = data[countRow].rate;
                        } else {
                            cell.value = ObjectUtils.isNotEmpty(data[countRow].rate) ? this._roundAndPad(Number(data[countRow].rate), precision.COST_SUMMARY.SUMMARY.freeRate) : "";//费率
                        }
                    }
                }
                if (cell.col == 8){
                    let value = data[countRow].price;
                    cell.value = value;
                    cell.value = this._roundAndPad(Number(cell.value), precision.COST_SUMMARY.awf)
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }
        //单位的省站标准
        if (worksheet.name == '单位工程造价汇总表(省站标准)' && sheetId== 10) {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].name
                if (cell.col == 3){
                    let value = data[countRow].price;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 4){
                    let value = data[countRow].rgf;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 5){
                    let value = data[countRow].clf;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 7){
                    let value = data[countRow].jxf;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 8){
                    let value = data[countRow].zcSbf;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }
        if (worksheet.name == '单位工程造价汇总表(省站标准) (2)'&& sheetId== 11) {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col==1) cell.value = data[countRow].dispNo;
                if (cell.col==2) cell.value = data[countRow].code;
                if (cell.col==4) cell.value = data[countRow].name;
                if (cell.col == 5) cell.value = data[countRow].jsjc;
                if (cell.col == 7) {
                    if (ObjectUtils.isNotEmpty(data[countRow].feeRate)) {
                        if (NumberUtil.countDecimalPlaces(data[countRow].feeRate) > 2) {
                            cell.value = data[countRow].feeRate;
                        } else {
                            cell.value = ObjectUtils.isNotEmpty(data[countRow].feeRate) ? this._roundAndPadNoZero(Number(data[countRow].feeRate), precision.COST_SUMMARY.SUMMARY.freeRate) : "";//费率
                        }
                    }
                }
                if (cell.col == 8){
                    let value = data[countRow].price;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }
        if (worksheet.name == '单位工程造价汇总表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 3) cell.value = data[countRow].name;
                if (cell.col == 5){
                    let value = data[countRow].price;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }
        if (worksheet.name == '单位工程费汇总表(省站标准)-多专业取费'|| worksheet.name == '单位工程费汇总表(省站标准)-单专业取费') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].code;
                if (cell.col == 3) cell.value = data[countRow].name;
                if (cell.col == 5) cell.value = data[countRow].instructions;
                if (cell.col == 7){
                    if (ObjectUtils.isNotEmpty(data[countRow].rate)) {
                        if (NumberUtil.countDecimalPlaces(data[countRow].rate) > 2) {
                            cell.value = data[countRow].rate;
                        } else {
                            cell.value = ObjectUtils.isNotEmpty(data[countRow].rate) ? this._roundAndPadNoZero(Number(data[countRow].rate), precision.COST_SUMMARY.SUMMARY.freeRate) : "";//费率
                        }
                    }
                }
                if (cell.col == 8){
                    let value = data[countRow].price;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }
        if (worksheet.name == '实体项目预算表(横-省站标准)') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].deCode;
                if (cell.col == 3) cell.value = data[countRow].deName;
                if (cell.col == 4) cell.value = data[countRow].unit;
                if (ObjectUtils.isNotEmpty(data[countRow].quantity)) {
                    if (cell.col == 5){
                        let value = data[countRow].quantity;
                        cell.value = this._roundAndPad(Number(value),3);
                        if(data[countRow]?.type === "05"){
                            cell.value = ceping === true ? this._roundAndPadCeping(value, 4) : cell.value; //测评
                        }else{
                            cell.value = ceping === true ? this._roundAndPadCeping(value, 3) : cell.value; //测评
                        }
                    }
                }
                if (ObjectUtils.isNotEmpty(data[countRow].price)) {
                    if (cell.col == 6){
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? (ObjectUtils.isNotEmpty(data[countRow].price)?this._roundAndPad(Number(data[countRow].price), precision.EDIT.DE.price):null) : (ObjectUtils.isNotEmpty(data[countRow].baseJournalPrice)?this._roundAndPad(Number(data[countRow].baseJournalPrice),precision.EDIT.DE.price):null);//单价
                        cell.value = value;
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                }

                if (data[countRow].type !== '05') {
                    //非主材设备显示
                    if (cell.col == 7){
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? (ObjectUtils.isNotEmpty(data[countRow].RSum)?this._roundAndPad(Number(data[countRow].RSum),precision.EDIT.DE.RSum):null) : (ObjectUtils.isNotEmpty(data[countRow].RDSum)?this._roundAndPad(Number(data[countRow].RDSum),precision.EDIT.DE.RSum):null);
                        cell.value = value;
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                    if (cell.col == 8){
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? (ObjectUtils.isNotEmpty(data[countRow].CSum)?this._roundAndPad(Number(data[countRow].CSum),precision.EDIT.DE.CSum):null) : (ObjectUtils.isNotEmpty(data[countRow].CDSum)?this._roundAndPad(Number(data[countRow].CDSum),precision.EDIT.DE.CSum):null);
                        cell.value = value;
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                    if (cell.col == 9){
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? (ObjectUtils.isNotEmpty(data[countRow].JSum)?this._roundAndPad(Number(data[countRow].JSum),precision.EDIT.DE.JSum):null) : (ObjectUtils.isNotEmpty(data[countRow].JDSum)?this._roundAndPad(Number(data[countRow].JDSum),precision.EDIT.DE.JSum):null);
                        cell.value = value;
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                }

                if (cell.col == 11){
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? (ObjectUtils.isNotEmpty(data[countRow].totalNumber)?this._roundAndPad(Number(data[countRow].totalNumber),precision.EDIT.DE.totalNumber):null) : (ObjectUtils.isNotEmpty(data[countRow].baseJournalTotalNumber)?this._roundAndPad(Number(data[countRow].baseJournalTotalNumber),precision.EDIT.DE.totalNumber):null);//合价
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (data[countRow].type !== '05') {
                    //非主材设备显示
                    if (cell.col == 12){
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? (ObjectUtils.isNotEmpty(data[countRow].rTotalSum)?this._roundAndPad(Number(data[countRow].rTotalSum),precision.EDIT.DE.rTotalSum):null) : (ObjectUtils.isNotEmpty(data[countRow].rdTotalSum)?this._roundAndPad(Number(data[countRow].rdTotalSum),precision.EDIT.DE.rTotalSum):null);//人工费
                        cell.value = value;
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                    if (cell.col == 13){
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? (ObjectUtils.isNotEmpty(data[countRow].cTotalSum)?this._roundAndPad(Number(data[countRow].cTotalSum),precision.EDIT.DE.cTotalSum):null) : (ObjectUtils.isNotEmpty(data[countRow].cdTotalSum)?this._roundAndPad(Number(data[countRow].cdTotalSum),precision.EDIT.DE.cTotalSum):null);//材料费
                        cell.value = value;
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                    if (cell.col == 14){
                        let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? (ObjectUtils.isNotEmpty(data[countRow].jTotalSum)?this._roundAndPad(Number(data[countRow].jTotalSum),precision.EDIT.DE.jTotalSum):null) : (ObjectUtils.isNotEmpty(data[countRow].jdTotalSum)?this._roundAndPad(Number(data[countRow].jdTotalSum),precision.EDIT.DE.jTotalSum):null);//机械费
                        cell.value = value;
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                }
            }
        }
        if (worksheet.name == '措施项目预算表(省站标准)'||worksheet.name == '措施项目预算表(省站标准-不含安文费)'||worksheet.name == '措施项目预算表(不含安文费)') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].deCode;
                if (cell.col == 2) cell.value = data[countRow].deName;//子目名称
                if (cell.col == 3) {
                    cell.value = data[countRow].unit;
                    if(data[countRow].type === DeTypeConstants.DE_TYPE_DELIST){
                        cell.value = "项";
                    }
                    // if(data[countRow].isFyrcj === 0){
                    //     cell.value = "元";
                    // }
                }
                if (cell.col == 5) {
                    let value = data[countRow].quantity;
                    if (ObjectUtils.isNotEmpty(data[countRow].quantity)) {
                        cell.value = value;
                        cell.value = ObjectUtils.isNotEmpty(value) ? this._roundAndPad(value, 3) : null;
                        if (data[countRow]?.type === "05") {
                            cell.value = ceping === true ? this._roundAndPadCeping(value, 4) : cell.value; //测评
                        } else {
                            cell.value = ceping === true ? this._roundAndPadCeping(value, 3) : cell.value; //测评
                        }
                    }
                }
                if (cell.col == 6) {
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? (ObjectUtils.isNotEmpty(data[countRow].price) ? this._roundAndPad(data[countRow].price, precision.EDIT.DE.price) : data[countRow].price) : (ObjectUtils.isNotEmpty(data[countRow].baseJournalPrice) ? this._roundAndPad(data[countRow].baseJournalPrice, precision.EDIT.DE.baseJournalPrice) : data[countRow].baseJournalPrice);//单价
                    if (data[countRow].type !== BranchProjectLevelConstant.qd) {
                        if (ObjectUtils.isNotEmpty(value)) {
                            cell.value = value;
                            cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                        }
                    }
                }
                if (cell.col == 7) {
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod?(this._roundAndPad(Number(ObjectUtils.isNotEmpty(data[countRow].totalNumber)?data[countRow].totalNumber:null),precision.EDIT.DE.totalNumber)):(this._roundAndPad(Number(ObjectUtils.isNotEmpty(data[countRow].baseJournalTotalNumber)?data[countRow].baseJournalTotalNumber:null),precision.EDIT.DE.baseJournalTotalNumber));//合价
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 9) {
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? (this._roundAndPad(Number(ObjectUtils.isNotEmpty(data[countRow].rTotalSum) ? data[countRow].rTotalSum : null), precision.EDIT.DE.rTotalSum)) : (this._roundAndPad(Number(ObjectUtils.isNotEmpty(data[countRow].rdTotalSum) ? data[countRow].rdTotalSum : null), precision.EDIT.DE.rdTotalSum));//人工费
                    if (ObjectUtils.isEmpty(data[countRow].bkjzcsxm)) {
                        cell.value = value;
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                }
                if (cell.col == 10) {
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? (this._roundAndPad(Number(ObjectUtils.isNotEmpty(data[countRow].cTotalSum) ? data[countRow].cTotalSum : null), precision.EDIT.DE.cTotalSum)) : (this._roundAndPad(Number(ObjectUtils.isNotEmpty(data[countRow].cdTotalSum) ? data[countRow].cdTotalSum : null), precision.EDIT.DE.cdTotalSum));//人工费
                    if (ObjectUtils.isEmpty(data[countRow].bkjzcsxm)) {
                        cell.value = value;
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                }
                if (cell.col == 11) {
                    let value = ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod ? (this._roundAndPad(Number(ObjectUtils.isNotEmpty(data[countRow].jTotalSum) ? data[countRow].jTotalSum : null), precision.EDIT.DE.jTotalSum)) : (this._roundAndPad(Number(ObjectUtils.isNotEmpty(data[countRow].jdTotalSum) ? data[countRow].jdTotalSum : null), precision.EDIT.DE.jdTotalSum));//人工费
                    if (ObjectUtils.isEmpty(data[countRow].bkjzcsxm)) {
                        cell.value = value;
                        cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                    }
                }

            }
        }
        if (worksheet.name == '措施项目计价表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].deName;//子目名称
                if (cell.col == 5){
                    let value = data[countRow].dingEPriceTotal;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }
        if (worksheet.name == '人工、材料、机械台班(用量、单价)汇总表(省站标准)') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].materialCode;
                if (cell.col == 2) cell.value = data[countRow].materialName;
                if (cell.col == 3) cell.value = data[countRow].unit;
                if (cell.col == 5){
                    let value = data[countRow].totalNumber;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 4) : cell.value; // 评测
                }
                if (cell.col == 6){
                    let value = data[countRow].baseJournalPrice;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 7){
                    let value = data[countRow].marketPrice;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 9){
                    let value = data[countRow].totalMarket;
                    cell.value = ObjectUtils.isNotEmpty(value) ? this._roundAndPad(value, 2): value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
                if (cell.col == 10){
                    let value = data[countRow].priceDifferTotal;
                    cell.value = value;
                    cell.value = ceping === true ? this._roundAndPadCeping(value, 2) : cell.value; // 评测
                }
            }
        }

        //--------工程项目层级-------------------------------
        if (worksheet.name == '工程项目总价表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].projectName;
                if (cell.col == 4) cell.value = this._roundAndPad(Number(data[countRow].projectCost),precision.COST_SUMMARY.je);
                if (cell.col == 5) cell.value = this._roundAndPad(Number(data[countRow].average),precision.COST_ANALYSIS.jzgm);
                if (cell.col == 7) cell.value = this._roundAndPad(Number(data[countRow].unitcost),precision.COST_SUMMARY.je);
                if (cell.col == 8) cell.value = this._roundAndPad(Number(data[countRow].costProportion),precision.COST_SUMMARY.je);
            }
        }
        if (worksheet.name == '工程项目总价表-明细') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].projectName;
                if (cell.col == 3) cell.value = this._roundAndPad(Number(data[countRow].projectCost),precision.COST_SUMMARY.je);
                if (cell.col == 5) cell.value = this._roundAndPad(Number(data[countRow].rgf),precision.COST_SUMMARY.je);
                if (cell.col == 6) cell.value = this._roundAndPad(Number(data[countRow].jxf),precision.COST_SUMMARY.je);
                if (cell.col == 7) cell.value = this._roundAndPad(Number(data[countRow].noPriceClf),precision.COST_SUMMARY.je);
                if (cell.col == 8) cell.value = this._roundAndPad(Number(data[countRow].sbf),precision.COST_SUMMARY.je);
                if (cell.col == 9) cell.value = this._roundAndPad(Number(data[countRow].glfLr),precision.COST_SUMMARY.je);
                if (cell.col == 11) cell.value = this._roundAndPad(Number(data[countRow].awf),precision.COST_SUMMARY.je);
                if (cell.col == 12) cell.value = this._roundAndPad(Number(data[countRow].sj),precision.COST_SUMMARY.je);
                if (cell.col == 13) cell.value = this._roundAndPad(Number(data[countRow].unitcost),precision.COST_SUMMARY.je);
            }
        }
        if (worksheet.name == '单项工程费总价表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].projectName;
                if (cell.col == 4) cell.value = this._roundAndPad(Number(data[countRow].projectCost),precision.COST_SUMMARY.je);
                if (cell.col == 5) cell.value = this._roundAndPad(Number(data[countRow].aqwmsgf),precision.COST_SUMMARY.je);
                if (cell.col == 7) cell.value = this._roundAndPad(Number(data[countRow].average),precision.COST_ANALYSIS.jzgm);
                if (cell.col == 8) cell.value = this._roundAndPad(Number(data[countRow].unitcost),precision.COST_SUMMARY.je);
            }
        }
        if (worksheet.name == '单项工程费总价表-明细') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].projectName;
                if (cell.col == 3) cell.value = this._roundAndPad(Number(data[countRow].projectCost),precision.COST_SUMMARY.je);
                if (cell.col == 5) cell.value = this._roundAndPad(Number(data[countRow].rgf),precision.COST_SUMMARY.je);
                if (cell.col == 6) cell.value = this._roundAndPad(Number(data[countRow].jxf),precision.COST_SUMMARY.je);
                if (cell.col == 7) cell.value = this._roundAndPad(Number(data[countRow].noPriceClf),precision.COST_SUMMARY.je);
                if (cell.col == 8) cell.value = this._roundAndPad(Number(data[countRow].sbf),precision.COST_SUMMARY.je);
                if (cell.col == 9) cell.value = this._roundAndPad(Number(data[countRow].glfLr),precision.COST_SUMMARY.je);
                if (cell.col == 11) cell.value = this._roundAndPad(Number(data[countRow].awf),precision.COST_SUMMARY.je);
                if (cell.col == 12) cell.value = this._roundAndPad(Number(data[countRow].sj),precision.COST_SUMMARY.je);
                if (cell.col == 13) cell.value = this._roundAndPad(Number(data[countRow].average),precision.COST_ANALYSIS.jzgm);
                if (cell.col == 14) cell.value = this._roundAndPad(Number(data[countRow].unitcost),precision.COST_SUMMARY.je);

            }
        }
        if (worksheet.name == '单项工程三材汇总表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].name;
                if (cell.col == 4) cell.value = this._roundAndPad(Number(data[countRow].钢材) == 0?"":Number(data[countRow].钢材),precision.COST_ANALYSIS.scNumber);
                if (cell.col == 5) cell.value = this._roundAndPad(Number(data[countRow].其中钢筋) == 0?"":Number(data[countRow].其中钢筋),precision.COST_ANALYSIS.scNumber);
                if (cell.col == 7) cell.value = this._roundAndPad(Number(data[countRow].木材) == 0?"":Number(data[countRow].木材),precision.COST_ANALYSIS.scNumber);
                if (cell.col == 8) cell.value = this._roundAndPad(Number(data[countRow].水泥) == 0?"":Number(data[countRow].水泥),precision.COST_ANALYSIS.scNumber);
            }
        }
        if (worksheet.name == '安全生产、文明施工费汇总表') {
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].projectName;
                if (cell.col == 5) cell.value = this._roundAndPad(Number(data[countRow].aqwmsgf),precision.COST_SUMMARY.awf);
            }
        }

    }


    //sheet表新增一行后 对原有的merges进行向下重置
    async resetMerges(workSheet, rowInsertNum) {
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        let map = new Map();
        for (let [key, value] of mergeMaps) {
            const regex = /([A-Za-z]+)(\d+)/;

            const matches = key.match(regex);
            if (matches && matches.length >= 2) {
                const part1 = matches[1];
                const part2 = matches[2];
                if (Number(part2) < rowInsertNum) {
                    map.set(key, value);
                    continue;
                }
                let after = Number(part2) + 1 + "";
                key = part1 + after;
                //console.log("Part 1:", part1);
                //console.log("Part 2:", part2);
            } else {
                console.log("Invalid string format");
            }
            value.top = value.top + 1;
            value.bottom = value.bottom + 1;
            map.set(key, value);
        }
        workSheet._merges = {};
        for (let [key, value] of map) {
            workSheet._merges[key] = value;
        }
    }

    //sheet表删除一行后 对原有的merges进行重置
    async resetMergesWhenDel(workSheet, rowDelNum) {
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        let map = new Map();
        for (let [key, value] of mergeMaps) {
            const regex = /([A-Za-z]+)(\d+)/;

            const matches = key.match(regex);
            if (matches && matches.length >= 2) {
                const part1 = matches[1];
                const part2 = matches[2];
                if (Number(part2) < rowDelNum) {
                    map.set(key, value);
                    continue;
                }
                if (Number(part2) == rowDelNum) {
                    continue;
                }
                let after = Number(part2) - 1 + "";
                key = part1 + after;
                //console.log("Part 1:", part1);
                //console.log("Part 2:", part2);
            } else {
                //console.log("Invalid string format");
            }
            value.top = value.top - 1;
            value.bottom = value.bottom - 1;
            map.set(key, value);
        }
        workSheet._merges = {};
        for (let [key, value] of map) {
            workSheet._merges[key] = value;
        }
        // let newMergeMaps = new Map(Object.entries(workSheet._merges));
        // for (let [key, value] of newMergeMaps) {
        //     const regex = /([A-Za-z]+)(\d+)/;
        //
        //     const matches = key.match(regex);
        //     if (matches && matches.length >= 2) {
        //         const part1 = matches[1];
        //         const part2 = matches[2];
        //         if (Number(part2) < rowDelNum) {
        //             continue;
        //         }
        //     } else {
        //         console.log("Invalid string format");
        //     }
        //     workSheet.unMergeCells([value.top,value.left,value.bottom,value.right]);
        //     workSheet.mergeCells([value.top,value.left,value.bottom,value.right]);
        // }
    }

    //由于框架中的style对象是共用的 所以有了该方法
    //对单个的cell对象格式进行设置
    async setStyleForCellHorizontal(originalStyle, cell, horizontal) {
        cell.style = {};
        cell.style['font'] = originalStyle.font;
        cell.style['border'] = originalStyle.border;
        cell.style['fill'] = originalStyle.fill;
        let alignment = originalStyle.alignment;
        let newAlignment = {};
        newAlignment['horizontal'] = horizontal;
        newAlignment['vertical'] = alignment.vertical;
        cell.style['alignment'] = newAlignment;
    }


    /**
     * 对字符串形式的数字进行四舍五入并补0操作
     * @params - 输入的字符串数字
     * @param {number} digits - 需要保留的小数位数
     * @returns {string} 处理后的字符串，确保有指定的小数位数
     */
    _roundAndPad(s, digits) {
        try {
            if (ObjectUtils.isNotEmpty(s) && !ObjectUtils.isNumber(s) && !ObjectUtils.isNumberStr(s)) {
                return s;
            }
            if (ObjectUtils.isEmpty(s) || isNaN(s)) {
                return new Decimal(0).toFixed(digits);
            }
            let value = parseFloat(s)
            if (isNaN(value)) {
                return s;
            }
            // 将字符串转换为数字进行四舍五入
            return new Decimal(value).toFixed(digits);
        } catch (error) {
            console.error(`输入错误: '${s}' 不是有效的数字字符串`);
            return s;
        }
    }


    /**
     * 对字符串形式的数字进行四舍五入并补0操作
     * @params - 输入的字符串数字
     * @param {number} digits - 需要保留的小数位数
     * @returns {string} 处理后的字符串，确保有指定的小数位数
     */
    _roundAndPadNoZero(s, digits) {
        try {
            if (ObjectUtils.isNotEmpty(s) && !ObjectUtils.isNumber(s) && !ObjectUtils.isNumberStr(s)) {
                return s;
            }
            if (ObjectUtils.isEmpty(s) || isNaN(s)) {
                return new Decimal(0).toFixed(digits);
            }
            let value = parseFloat(s)
            if (isNaN(value)) {
                return s;
            }
            // 将字符串转换为数字进行四舍五入
            return Number(new Decimal(value).toFixed(digits));
        } catch (error) {
            console.error(`输入错误: '${s}' 不是有效的数字字符串`);
            return s;
        }
    }


    /**
     * 对字符串形式的数字进行四舍五入并补0操作
     * @params - 输入的字符串数字
     * @param {number} digits - 需要保留的小数位数
     * @returns {string} 处理后的字符串，确保有指定的小数位数
     */
    _roundAndPadCeping(s, digits) {
        try {
            if (ObjectUtils.isEmpty(s)) {
                return s;
            }
            if (ObjectUtils.isNotEmpty(s) && !ObjectUtils.isNumber(s) && !ObjectUtils.isNumberStr(s)) {
                return s;
            }
            if (isNaN(s)) {
                return new Decimal(0).toFixed(digits);
            }
            let value = parseFloat(s)
            if (isNaN(value)) {
                return s;
            }
            // 将字符串转换为数字进行四舍五入
            return new Decimal(value).toFixed(digits);
        } catch (error) {
            console.error(`输入错误: '${s}' 不是有效的数字字符串`);
            return s;
        }
    }

}

module.exports = {
    GljExcelUtil: new GljExcelUtil()
};
