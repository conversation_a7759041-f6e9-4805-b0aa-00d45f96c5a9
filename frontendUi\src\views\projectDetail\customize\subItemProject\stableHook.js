import { computed, toRaw, ref, reactive, nextTick } from 'vue';
import api from '@/api/projectDetail.js';
import jsApi from '@/api/jiesuanApi.js';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import { re } from 'mathjs';
import { useEventListener } from '@vueuse/core';

const projectStore = projectDetailStore();

export const stableHook = (
  { selectState, stableRef, currentInfo, tableData, originalData, copyData },
  type,
  callBack,
) => {
  const sTableState = ref({
    prevDbTime: null,
  });
  const inputRefs = reactive({});
  let copyDataInUnit = null; // 复制数据行时的所属单位
  const setInputRef = (el, field) => {
    inputRefs[field + 'Input'] = el;
  };
  // 打开编辑器时自动聚焦
  let isOpenEdit = ref(false); //打开编辑状态时粘贴不涉及复制行
  const setCloseEditor = cellInfos => {
    isOpenEdit.value = false;
    if (['bdCode', 'fxCode', 'projectAttr'].includes(cellInfos.column.field)) {
      sTableState.prevDbTime = null;
    }
  };
  let isClickOut = ref(false);
  const clickOutside = (bool = true) => {
    //点击编辑区外部---用来判断粘贴是否可以使用
    isClickOut.value = bool;
  };
  // 解决在表格上，首次扩选范围时，快捷键功能会被isClickOut 状态值拦截问题
  useEventListener(stableRef, 'mousemove', () => {
    //!isClickOut.value 优化语句：优化 持续改变isClickOut值问题；
    isClickOut.value != false && clickOutside(false);
  });
  useEventListener(stableRef, 'mouseleave', () => {
    console.log('mouseleave');
    clickOutside(true);
  });

  const openEditor = cellInfos => {
    if (['bdCode', 'fxCode', 'projectAttr'].includes(cellInfos.column.field)) {
      sTableState.prevDbTime = new Date().getTime();
      console.log('sTableState.prevDbTime', sTableState.prevDbTime);
      // setTimeout(() => {
      //   // 清除sTableState.time
      //   sTableState.prevDbTime = null;
      // }, 300);
    }
    if (cellInfos.column.field === 'projectAttr') {
      callBack('projectAttr-open');
    }
    if (
      cellInfos.column.field === 'ceilingPrice' &&
      !cellInfos.record.ceilingPrice &&
      (cellInfos.record.kind === '01' ||
        cellInfos.record.kind === '02' ||
        cellInfos.record.kind === '03')
    ) {
      cellInfos.record.ceilingPrice =
        cellInfos.record.price || cellInfos.record.total;
    }
    nextTick(() => {
      const inputRef = inputRefs[cellInfos.column.field + 'Input'];
      if (inputRef) {
        console.log(inputRef, 'inputRef');
        inputRef.focus();
        if (inputRef && typeof inputRef.select === 'function') {
          inputRef?.select();
        }
        if (['name', 'projectAttr'].includes(cellInfos.column.field)) {
          document
            .querySelector('.surely-table-cell-edit-wrapper textarea')
            .select();
        }
        //输入框激活的话设置正在编辑状态
        isOpenEdit.value = true;
      }
    });
  };
  const findOnceAndManyElement = arr => {
    // 使用reduce计算每个元素出现的次数
    const countMap = arr.reduce((countMap, num) => {
      countMap[num] = (countMap[num] || 0) + 1;
      return countMap;
    }, {});
    // 使用filter过滤出只出现一次的元素
    return [
      arr.filter(num => countMap[num] === 1),
      arr.filter(num => countMap[num] > 1),
    ];
  };
  const groupConsecutiveNumbers = sortedArray => {
    let groups = [];
    for (let i = 0; i < sortedArray.length; ) {
      let start = sortedArray[i];
      let end = start;
      // 寻找组内最后一个数字
      while (sortedArray[i + 1] - 1 === end) {
        end = sortedArray[++i];
      }
      groups.push([start, end]);
      i++; // 移动到下一个数字
    }
    return groups;
  };
  const isOnlyOne = () => {
    //如果只有一条数据时可设置选中
    let selectRow = stableRef.value.getSelectedRange()
      ? stableRef.value.getSelectedRange()[0]
      : null;
    let startIndex = selectRow?.startRow?.rowIndex || -1;
    let endIndex = selectRow?.endRow?.rowIndex || -1;
    let flag = selectRow && startIndex > 0 && startIndex === endIndex;
    return flag;
  };
  // 拖选批量选中数据
  const cellMouseup = e => {
    // debugger;
    console.log(
      '🚀 ~ stableRef.value.getSelectedRange ~ stableRef.value.getSelectedRange():',
      stableRef.value.getSelectedRange(),
      e,
    );
    // 放开此行-未选中数据，右键操作可将当前行设置为选中数据
    // if (e.button === 2 && !isOnlyOne()) return; // 右键不触发，不然会导致选中的多个数据之选中一条
    if (e.button === 2) return;
    if (e.target.type === 'checkbox') return;
    const selectedRange = stableRef.value.getSelectedRange();
    if (
      e.target.querySelector('.surely-table-checkbox-input') &&
      selectedRange.length === 1 &&
      selectedRange[0].startRow.rowIndex === selectedRange[0].endRow.rowIndex
    ) {
      // 当前选中的是checkbox列，并且只有一条数据，则直接选中这条数据，由于s-table升级后点击checkbox列也会框选，老版本不会框选，单击该列会触发该方法和onSelect，导致无法选中数据的问题。
      const record =
        selectedRange[0].flattenData[selectedRange[0].startRow.rowIndex]
          ?.record;
      selectRow(record, e);
      return;
    }

    let selectArray = [];
    let selectRangeInfo = {};
    selectedRange.map((a, index) => {
      selectArray[index] = [a.startRow.rowIndex, a.endRow.rowIndex];
      selectRangeInfo[selectArray[index]] = [
        a.startColumn.dataIndex,
        a.columns[a.columns.length - 1].dataIndex,
      ];
    });
    console.log(
      '🚀 ~ stableRef.value.getSelectedRange',
      selectedRange,
      selectArray,
      selectRangeInfo
    );
    const selectAllIndexList = selectArray.flatMap(([start, end]) =>
      Array.from(
        {
          length: end > start ? end - start + 1 : start - end + 1,
        },
        (_, i) => (end > start ? start : end) + i,
      ),
    );
    let [selectData, manyList] = findOnceAndManyElement(selectAllIndexList);

    if (manyList.length && selectData.length && e.ctrlKey) {
      customAppendCellToSelectedRange(selectData, selectRangeInfo, manyList);
    }
    if (selectData.length) {
      selectState.selectedRowKeys = Array.from(
        new Set([
          ...selectData.map(a => {
            return tableData.value[a].sequenceNbr;
          }),
        ]),
      );
    }
  };
  const customAppendCellToSelectedRange = (
    selectData,
    selectRangeInfo,
    manyList,
  ) => {
    selectData.sort((a, b) => a - b);
    const groupList = groupConsecutiveNumbers(selectData);
    stableRef.value.clearAllSelectedRange();

    for (let groupItem of groupList) {
      const [rowStartIndex, rowEndIndex] = groupItem;

      let selectRange = {
        rowStartIndex,
        rowEndIndex,
        columnStartKey: '' || 'checkbox',
        columnEndKey: '' || 'checkbox',
      };
      for (let column in selectRangeInfo) {
        const [start, end] = column.split(',');
        if (
          rowStartIndex >= start &&
          rowStartIndex <= end &&
          !manyList.includes(rowStartIndex)
        ) {
          const [columnStartKey, columnEndKey] = selectRangeInfo[column];
          selectRange.columnStartKey = columnStartKey;
          selectRange.columnEndKey = columnEndKey;
        }
      }
      stableRef.value.appendCellToSelectedRange(selectRange);
      console.log('🚀 ~ selectRange:', selectRange, stableRef.value);
    }
  };
  const cellClickEvent = (event, params) => {
    console.log('record, column2222:', event, params);
    const { column, record } = params;
    if (!['bdCode', 'fxCode'].includes(column.dataIndex)) {
      record.nextClickEditField = '';
    }
    if (
      record.sequenceNbr !== currentInfo.value?.sequenceNbr &&
      ['bdCode', 'fxCode'].includes(column.dataIndex)
    ) {
      record.nextClickEditField = '';
    }
  };
  // 单击进入编辑字段模式，需要下一次点击同一列单元格时直接进入编辑状态处理
  const nextClickEditHandler = (column, record) => {
    if (['bdCode', 'fxCode'].includes(column.dataIndex)) {
      if (!['bdCode', 'fxCode'].includes(record.nextClickEditField)) {
        record.nextClickEditField = column.dataIndex;
        // 当单元格编辑模式为单击进入编辑状态时，当前单元格单击第一次时不可进入编辑
        return false;
      }
    } else {
      record.nextClickEditField = '';
    }
    return true;
  };
  // 选中框选数据
  const selectRow = (record, e) => {
    // if (e.target.type === 'checkbox') return;
    if (e.ctrlKey) {
      // const selectedRowKeys = [...selectState.selectedRowKeys];
      // if (selectedRowKeys.indexOf(record.key) >= 0) {
      //   selectedRowKeys.splice(selectedRowKeys.indexOf(record.key), 1);
      // } else {
      //   selectedRowKeys.push(record.key);
      // }
      // selectState.selectedRowKeys = selectedRowKeys;
    } else if (!record.isClickAfter) {
      record.isClickAfter = true;
      // 选中值极其下级
      let data = findDescendants(tableData.value, record.sequenceNbr).map(a => {
        return a.sequenceNbr;
      });
      currentInfo.value = record;
      selectState.selectedRowKeys = Array.from(
        new Set([record.sequenceNbr, ...data]),
      );
      // customAppendCellToSelectedRange(selectState.selectedRowKeys, {}, []);
    } else {
      currentInfo.value = record;
      selectState.selectedRowKeys = [record.key || record.sequenceNbr];
    }
  };
  const rowSelection = computed(() => {
    return {
      hideSelectAll: true,
      fixed: true,
      selectedRowKeys: selectState.selectedRowKeys,
      hideSelectAll: true,
      onSelect: (record, selected, selectedRows, nativeEvent) => {
        console.log('onSelect', record, selected, nativeEvent);
        stableRef.value.clearAllSelectedRange();
        currentInfo.value = record;
        // 选中值极其下级
        let data = findDescendants(tableData.value, record.sequenceNbr).map(
          a => {
            return a.sequenceNbr;
          },
        );
        if (!nativeEvent.ctrlKey) {
          // selected = true;
          selectState.selectedRowKeys = Array.from(
            new Set([record.sequenceNbr, ...data]),
          );
          if (!selectState.selectedRowKeys.length) {
            selectState.selectedRowKeys = [record.sequenceNbr];
          }
          // if (selected) {
          //   selectState.selectedRowKeys = Array.from(
          //     new Set([record.sequenceNbr, ...data]),
          //   );
          //   // if (!record.isClickAfter) {
          //   //   record.isClickAfter = true;

          //   // } else {
          //   //   currentInfo.value = record;
          //   //   selectState.selectedRowKeys = [record.key || record.sequenceNbr];
          //   // }
          // } else {
          //   selectState.selectedRowKeys = Array.from(
          //     new Set(
          //       selectState.selectedRowKeys.filter(
          //         item => ![record.sequenceNbr, ...data].includes(item),
          //       ),
          //     ),
          //   );
          // }
        } else {
          if (selected) {
            selectState.selectedRowKeys = Array.from(
              new Set([
                ...selectState.selectedRowKeys,
                record.sequenceNbr,
                ...data,
              ]),
            );
          } else {
            selectState.selectedRowKeys = Array.from(
              new Set(
                selectState.selectedRowKeys.filter(
                  item => ![record.sequenceNbr, ...data].includes(item),
                ),
              ),
            );
          }
          if (!selectState.selectedRowKeys.length) {
            selectState.selectedRowKeys = [record.sequenceNbr];
          }
        }
        const indexList = selectState.selectedRowKeys.map(key => {
          return tableData.value.findIndex(item => item.sequenceNbr === key);
        });
        customAppendCellToSelectedRange(indexList, {}, []);
      },
    };
  });

  function findDescendants(items, parentId) {
    const descendants = [];
    // 遍历数组中的每个元素
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      // 如果当前元素的父ID等于给定的parentId
      if (item.parentId === parentId) {
        // 将当前元素添加到后代数组中
        descendants.push(item);
        // 递归查找当前元素的所有后代
        const childDescendants = findDescendants(items, item.sequenceNbr);
        // 将后代的后代也添加到后代数组中
        descendants.push(...childDescendants);
      }
    }

    return descendants;
  }

  const customRow = record => {
    return {
      onClick: e => {
        isClickOut.value = false;
        if (!e.ctrlKey) {
          currentInfo.value = record;
        }
        // selectRow(record, e);
      },
    };
  };

  const pasteRowVisible = async code => {
    //复制数据最高层级行
    // const row = originalData.value.reduce((max, item) => {
    //   if (parseInt(item.kind) < parseInt(max)) {
    //     return item.kind;
    //   } else {
    //     return max;
    //   }
    // });
    // //查询子节点
    // let res = await api.queryDeChildType({
    //   constructId: projectStore.currentTreeGroupInfo?.constructId,
    //   unitId: projectStore.currentTreeInfo?.id,
    //   singleId: '',
    //   deRowId: currentInfo.value.deRowId,
    // });
    // let children = res.result;
    // // 如果复制是分部行，判断子节点是否有定额
    // let unitHasDe = false
    // if(row.kind === '00'){
    //   let unitRes = await api.queryDeChildType({
    //     constructId: projectStore.currentTreeGroupInfo?.constructId,
    //     unitId: row?.unitId,
    //     singleId: '',
    //     deRowId: row.deRowId,
    //   });
    //   unitHasDe = !unitRes.result.some(item => ['00', '01', '02'].includes(item))
    // }
    // if (code === 'paste') {
    //   console.log(
    //     'rowcode ',
    //     children.length === 0 ||
    //       !children.some(item => ['00', '01', '02'].includes(item))
    //   );
    //   // 最高层级行是定额
    //   if (deMapFun.isDe(row.kind)) {
    //     // 目标层级行是定额
    //     if (deMapFun.isDe(currentInfo.value.kind)) {
    //       if (
    //         currentInfo.value.kind === '05' ||
    //         currentInfo.value.kind === '07'
    //       ) {
    //         return true;
    //       } else {
    //         return false;
    //       }
    //     } else if (!deMapFun.isDe(currentInfo.value.kind)) {
    //       return true;
    //     } else {
    //       return true;
    //     }
    //   }
    //   // 最高层级行是分部
    //   if (['01', '02'].includes(row.kind)) {
    //     // 如果没有子节点
    //     if (deMapFun.isDe(currentInfo.value.kind)) {
    //       return true;
    //     } else if (currentInfo.value.kind === '00') {
    //       return true;
    //     } else {
    //       return false;
    //     }
    //   }
    //   // 最高层级行是项目
    //   if (['00'].includes(row.kind)) {
    //     return true
    //     // if (!deMapFun.isDe(currentInfo.value.kind) ) {
    //     //   return false;
    //     // } else {
    //     //   return true;
    //     // }
    //   }
    // }
    // if (code === 'pasteChild') {
    //   // 选中行为定额
    //   if (deMapFun.isDe(row.kind)) {
    //     // 目标层级行是定额
    //     if (
    //       deMapFun.isDe(currentInfo.value.kind) &&
    //       currentInfo.value.kind !== '03'
    //     ) {
    //       return true;
    //     } else if (children.length === 0 &&  ['00', '01', '02'].includes(currentInfo.value.kind)) {
    //       return false;
    //     } else if (children.length === 0) {
    //       return true;
    //     } else if (!children.some(item => ['00', '01', '02'].includes(item))) {
    //       return false;
    //     } else {
    //       return true;
    //     }
    //   }
    //   // 最高层级行是分部
    //   if (['01', '02'].includes(row.kind)) {
    //     // 如果没有子节点
    //     if (children.length === 0 && !deMapFun.isDe(currentInfo.value.kind)) {
    //       return false;
    //     } else if (children.some(item => ['00', '01', '02'].includes(item))) {
    //       return false;
    //     } else {
    //       return true;
    //     }
    //   }
    //   if (['00'].includes(row.kind)) {
    //     console.log('000', unitHasDe,children);
    //     // 如果没有子节点
    //     if(unitHasDe && children.some(item => ['00', '01', '02'].includes(item))){
    //       return true;
    //     }else if(unitHasDe && !children.some(item => ['00', '01', '02'].includes(item))){
    //       return false;
    //     }else if(!unitHasDe && children.some(item => ['00', '01', '02'].includes(item))){
    //       return false;
    //     }else if(!unitHasDe && !children.some(item => ['00', '01', '02'].includes(item))){
    //       return true;
    //     }
    //   }
    // }
  };
  const cellKeydown = (event, { cellPosition, isEditing }) => {
    if (event.code === 'ArrowUp' || event.code === 'ArrowDown') {
      let rowIndex =
        event.code === 'ArrowUp'
          ? cellPosition.rowIndex - 1
          : cellPosition.rowIndex + 1;
      currentInfo.value = tableData.value[rowIndex];
    }
    if (
      (event.code === 'Enter' || event.code === 'NumpadEnter') &&
      cellPosition.column.editable
    ) {
      nextTick(() => {
        if (tableData.value[cellPosition.rowIndex].isLocked) return;
        // 如果当前单元格处于编辑状态
        if (!isEditing) {
          // 打开编辑器
          console.log(
            'openEditor',
            cellPosition,
            cellPosition.column?.editable({
              record: tableData.value[cellPosition.rowIndex],
            }),
            {
              columnKey: cellPosition.column.dataIndex,
              rowKey: tableData.value[cellPosition.rowIndex].key,
            },
          );
          stableRef.value.openEditor([
            {
              columnKey: cellPosition.column.dataIndex,
              rowKey: tableData.value[cellPosition.rowIndex].key,
            },
          ]);
          setTimeout(() => {
            // 获取输入框的焦点
            const inputRef = inputRefs[cellPosition.column.dataIndex + 'Input'];
            console.log(inputRef);
            if (inputRef) {
              inputRef.focus();
              if (inputRef && typeof inputRef.select === 'function') {
                inputRef?.select();
              }
              if (
                ['name', 'projectAttr'].includes(cellPosition.column.dataIndex)
              ) {
                document
                  .querySelector('.surely-table-cell-edit-wrapper textarea')
                  .select();
              }
            }
          }, 50);
        } else {
          // 当选择“工程量”单元格或已经跳转到“工程量”单元格后，
          // 单击回车进入编辑状态，并全选当前单元格内所有字段，
          // 再次单击回车结束编辑状态，当从编辑状态结束后，
          // 再次单击回车，将跳转到下一行“工程量”单元格位置。
          // debugger;
          if (
            cellPosition.column.title === '工程量' &&
            !['00', '01', '02'].includes(
              tableData.value[cellPosition.rowIndex + 1]?.kind
            )
          ) {
            cellPosition.rowIndex = cellPosition.rowIndex + 1;
            currentInfo.value = tableData.value[cellPosition.rowIndex];
          }
          if (cellPosition.column.dataIndex === 'unit') {
            inputRefs[cellPosition.column.dataIndex + 'Input']?.blur();
            setTimeout(() => {
              intoCell(cellPosition);
            }, 50);
          } else if (
            !['name', 'projectAttr'].includes(cellPosition.column.dataIndex)
          ) {
            stableRef.value.closeEditor();
            intoCell(cellPosition);
          }
        }
      });
      return false;
    }
    if (
      (event.code === 'Enter' || event.code === 'NumpadEnter') &&
      !cellPosition.column.editable
    ) {
      console.log('cellPosition', cellPosition);
      // intoCell(cellPosition, 'quantity');
      stableRef.value.clearAllSelectedRange();
      stableRef.value.appendCellToSelectedRange({
        rowStartIndex: cellPosition.rowIndex,
        rowEndIndex: cellPosition.rowIndex,
        columnStartKey: 'quantityEdit',
        columnEndKey: 'quantityEdit',
      });
      stableRef.value.scrollTo(
        {
          columnKey: 'quantityEdit',
        },
        'auto',
      );
      return false;
    }
    if (
      (event.ctrlKey && event.code === 'KeyC') ||
      (event.ctrlKey && event.code === 'KeyV') ||
      (event.ctrlKey && event.code === 'KeyX')
    ) {
	    console.log(event.ctrlKey, event.code, 2123123123);
      if(type !== 'fbfx' && projectStore.currentTreeInfo?.originalFlag) return
      copyAndPaste(event);
      return false;
    } else if (event.code === 'Delete') {
      callBack('delete');
      return false;
    } else {
      return true;
    }
  };
  /**
   * @description: 获取对应点击的单元格
   * @param {Object} cellPosition
   * @param {String} dataIndex
   * @return {void}
   */
  const intoCell = (cellPosition, dataIndex) => {
    // 获取对应点击的单元格
    setTimeout(() => {
      Array.from(document.querySelectorAll('.surely-table-row')).map(a => {
        if (a.dataset.rowKey === tableData.value[cellPosition.rowIndex].key) {
          Array.from(a.querySelectorAll('.surely-table-cell')).map(b => {
            // 如果当前单元格处于编辑状态
            if (
              b.dataset.columnKey ===
              (dataIndex || cellPosition.column.dataIndex)
            ) {
              // 注册点击事件，否则表格自动失去焦点，注：click事件不生效
              let event = new MouseEvent('mousedown', {
                view: window,
                bubbles: true,
                cancelable: true,
              });
              let event2 = new MouseEvent('mouseup', {
                view: window,
                bubbles: true,
                cancelable: true,
              });

              b.querySelector('.surely-table-cell-content').dispatchEvent(
                event,
              );
              b.querySelector('.surely-table-cell-content').dispatchEvent(
                event2,
              );
              stableRef.value.scrollTo(
                {
                  columnKey: dataIndex || cellPosition.column.dataIndex,
                },
                'auto',
              );
            }
          });
        }
      });
    }, 50);
  };
  /**
   *
   * @param {*} event
   * @param {*} isHandCopy 是否手动执行复制操作
   */
  const copyAndPaste = async (event, isHandCopy = false) => {
    //需要判断ctrl时候鼠标位置
    if (isClickOut.value) return; //点击分部分项/措施项目编辑区外部不进行复制操作
    if (event.ctrlKey && event.code === 'KeyX') {
      console.log('copyAndPaste----KeyX', event);
      cutFun();
    }
    if (event.ctrlKey && event.code === 'KeyC') {
      console.log('copyAndPaste', stableRef.value);
      copyFun();
    }
    if (event.ctrlKey && event.code === 'KeyV') {
      if (!copyData.value || copyData.value?.length == 0) return; //
      console.log('pasteFun----', isClickOut, event);
      pasteFun();
    }
  };
  // 表格中 剪切 能力调用
  const cutFun = async () => {
    console.log(selectState.selectedRowKeys, 123123123);

    if (!selectState.selectedRowKeys.length === 0) {
      return message.error('暂无选中数据');
    }
    copyData.value = selectState.selectedRowKeys;
    copyDataInUnit = JSON.parse(JSON.stringify(projectStore.currentTreeInfo));
    console.log(stableRef.value.$el.classList, 'stableRef');

    //  这期不要，所以逻辑未写
    // if (isAllSelectedZCSB()) {
    //   zcsbCut();
    //   return;
    // }
    const selectRows = getSelectedRows();
    const { sequenceNbrList, contractChildren } =
      await getExpandAndContractSequenceNbrList(selectRows);

    type === 'fbfx'
      ? fxCut([...selectRows, ...contractChildren], sequenceNbrList)
      : csCut([...selectRows, ...contractChildren], sequenceNbrList);
  };

  const copyFun = async () => {
    console.log(selectState.selectedRowKeys);
    if (!selectState.selectedRowKeys.length === 0) {
      return message.error('暂无选中数据');
    }
    copyData.value = selectState.selectedRowKeys;
    copyDataInUnit = JSON.parse(JSON.stringify(projectStore.currentTreeInfo));
    const selectRows = getSelectedRows();
    if (isAllSelectedZCSB(selectRows)) {
      zcsbCopy();
      return;
    }
    const { sequenceNbrList, contractChildren } =
      await getExpandAndContractSequenceNbrList(selectRows);

    type === 'fbfx' ? fxCopy(sequenceNbrList) : csCopy(sequenceNbrList);
  };
  // 剪切 主材设备方法 20 期 暂时不做
  const zcsbCut = () => {
    console.log('剪切：主材设备', selectState.selectedRowKeys);
    api
      .copyZcSb({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeGroupInfo?.singleId,
        unitId: projectStore.currentTreeInfo?.id,
        sequenceNbrs: toRaw(selectState.selectedRowKeys),
      })
      .then(res => {
        if (res.status == 200) {
          message.success('剪切成功');
        } else {
          message.error(res.message);
        }
      });
  };

  const zcsbCopy = () => {
    console.log('复制：主材设备', selectState.selectedRowKeys);
    api
      .copyZcSb({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeGroupInfo?.singleId,
        unitId: projectStore.currentTreeInfo?.id,
        sequenceNbrs: toRaw(selectState.selectedRowKeys),
      })
      .then(res => {
        if (res.status === 200) {
          message.success('复制成功');
        }
      });
  };
  /**
   * 是否全部选中主材设备
   * @returns
   */
  const isAllSelectedZCSB = rows => {
    if (!rows) {
      rows = getSelectedRows();
    }
    if (!rows.length) return false;
    return rows.every(item => [94, 95].includes(item.kind));
  };
  /**
   * 选中行数据
   * @returns
   */
  const getSelectedRows = () => {
    // const selectRange = stableRef.value.getSelectedRange()
    // console.log(selectState.selectedRowKeys, selectRange)
    // let selectedRows = []
    // for (let select of selectRange) {
    //   const { startRow, endRow} = select;
    //   for (let i = startRow.rowIndex; i <= endRow.rowIndex; i++) {
    //     selectedRows.push(tableData.value[i])
    //   }
    // }
    let selectedRows = tableData.value.filter(i =>
      selectState.selectedRowKeys.includes(i.sequenceNbr),
    );
    return selectedRows;
  };
  // 表格上任意地方点击时捕获，目前控制 移除cut 时的样式
  const mousedownHandle = e => {
    console.log(e);
    if (e.button == 0 && stableRef.value) {
      DataProcessor.removeClassName(); // 剪切样式的移除
    }
  };
  //费用定额 不可剪切时，状态判断和数据过滤
  class DataProcessor {
    constructor(tableData = tableData.value, copyData = copyData.value) {
      this.tableData = tableData; // 假设这是一个包含多个对象的数组
      this.copyData = new Set(copyData); // 将copyData转换为一个Set以提高查找效率
    }
    /**
     * 是否费用定额
     * 12的垂运，22的装饰超高、垂运不属于费用定额
     * isCostDe是否是费用定额 0不是  1 安文费 2 总价措施 3 超高 4 垂运 5 安装费'
     * return 返回 false 是普通定额； true  费用定额
     */
    static isNotCostDe(currentInfo = {}) {
      const { kind, isCostDe } = currentInfo;
      return (
        kind === '04' &&
        (!isCostDe ||
          isCostDe === 4 ||
          (projectStore.deStandardReleaseYear === '22' &&
            currentInfo.isCostDe === 3))
      );
    }
    /**
     * 移除这个样式
     * $el： ref返回的dom元素
     * className： 需要添加的样式名
     */
    static removeClassName(
      $el = stableRef?.value.$el,
      className = 'dashed-table',
    ) {
      if ($el.classList.contains(className)) {
        $el.classList.remove(className);
      }
    }
    /**
     * 是否存在这个样式
     * $el： ref返回的dom元素
     * className： 需要添加的样式名
     */
    static isThereClassName(
      $el = stableRef?.value.$el,
      className = 'dashed-table',
    ) {
      DataProcessor.removeClassName($el, className);
      $el.classList.add(className);
    }
    //记取的费用定额不可剪切, 检查是否存在至少一个满足条件
    isCostDeFound() {
      //item.kind === '03' || (item.kind === '04' && isNotCostDe(item)) 满足这个条件的都是要被能剪切的
      return this.tableData.some(item => {
        if (this.copyData.has(item.sequenceNbr)) {
          return (
            item.kind === '03' ||
            (item.kind === '04' && DataProcessor.isNotCostDe(item))
          );
        }
        return false;
      });
    }
    // 获取满足条件的序列号列表
    getMatchingSequenceNbrs() {
      return this.tableData
        .filter(item => {
          console.log(
            'getMatchingSequenceNbrs',
            this.copyData.has(item.sequenceNbr),
            item.kind,
            DataProcessor.isNotCostDe(item),
          );
          return (
            this.copyData.has(item.sequenceNbr) &&
            (item.kind === '03' ||
              (item.kind === '04' && DataProcessor.isNotCostDe(item)))
          );
        })
        .map(item => item.sequenceNbr);
    }
  }

  // 剪切 函数中内部调用的逻辑方法
  const fxCut = async (selectRows, sequenceNbrList) => {
    //记取的费用定额不可剪切
    // 用selectRows代替tableData.value， 因为tableData.value不包含收起状态下的子集数据
    const processor = new DataProcessor(selectRows, sequenceNbrList);
    if (!processor.isCostDeFound()) {
      return null;
    }
    //过滤合适的数据
    const sequenceNbrsList = processor.getMatchingSequenceNbrs();
    console.log('剪切sequenceNbrsList', sequenceNbrsList);
    console.log(
      '剪切selectState.selectedRowKeys',
      toRaw(selectState.selectedRowKeys),
    );

    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      sequenceNbrs: Array.from(new Set(sequenceNbrsList)),
    };
    console.log(
      '剪切数据',
      apiData,
      selectState,
      stableRef.value.getSelectedRange(),
    );

    DataProcessor.isThereClassName(); // 剪切样式的添加

    api.cutQdDeFbData(apiData).then(res => {
      if (res.status == 200) {
        message.success('剪切成功');
      } else {
        message.error(res.message);
      }
    });
  };
  // 剪切 函数中内部调用的逻辑方法
  const csCut = async (selectRows, sequenceNbrList) => {
    let isAwfData = false;
    let awfObj = tableData.value
      .filter(x => x.isAwfData)
      .map(a => a.sequenceNbr);
    sequenceNbrList?.forEach(item => {
      awfObj.forEach(child => {
        if (item === child) {
          isAwfData = true;
        }
      });
    });
    if (isAwfData) {
      message.error('安全生产、文明施工费下挂数据不可进行剪切操作~');
      return;
    }

    //记取的费用定额不可剪切
    const processor = new DataProcessor(selectRows, sequenceNbrList);
    if (!processor.isCostDeFound()) {
      return null;
    }
    //过滤合适的数据
    const sequenceNbrsList = processor.getMatchingSequenceNbrs();

    console.log('剪切sequenceNbrsList', sequenceNbrsList);
    console.log(
      '剪切selectState.selectedRowKeys',
      toRaw(selectState.selectedRowKeys),
    );
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      sequenceNbrs: Array.from(new Set(sequenceNbrsList)),
    };

    DataProcessor.isThereClassName(); // 剪切样式的添加

    console.log('剪切csCut', apiData);
    api.cutBranchDataQdDe(apiData).then(res => {
      console.log('res', res);
      if (res.status == 200) {
        message.success('剪切成功');
      } else {
        message.error(res.message);
      }
    });
  };

  // 根据选中行获取收起来的子集所有数据
  const getContractChildrenByRows = async selectRows => {
    const contractRows = selectRows.filter(row => row.displaySign === 2);
    let contractChildren = [];
    if (contractRows.length > 0) {
      for (let i = 0; i < contractRows.length; i++) {
        const childRes = await api[
          type === 'fbfx' ? 'searchPonitAndChild' : 'csSearchPonitAndChild'
        ]({
          constructId: projectStore.currentTreeGroupInfo?.constructId,
          singleId: projectStore.currentTreeGroupInfo?.singleId,
          unitId: projectStore.currentTreeInfo?.id,
          sequenceNbr: contractRows[i].sequenceNbr,
        });
        if (childRes.result) {
          contractChildren = [...contractChildren, ...childRes.result];
        }
      }
    }
    return contractChildren;
  };

  // 获取选中数据行展开状态和收起状态下的子集所有的sequenceNbr
  const getExpandAndContractSequenceNbrList = async selectRows => {
    // 过滤分部
    const expandSequenceNbrList = selectRows
      .filter(row => !['0', '01', '02'].includes(row.kind))
      .map(i => i.sequenceNbr);
    const contractChildren = await getContractChildrenByRows(selectRows);
    // 过滤收起分部
    const contractSequenceNbrList = contractChildren
      .filter(i => !['0', '01', '02'].includes(i.kind))
      .map(i => i.sequenceNbr);
    const sequenceNbrList = Array.from(
      new Set([...expandSequenceNbrList, ...contractSequenceNbrList]),
    );
    return { sequenceNbrList, contractChildren };
  };
  const fxCopy = async sequenceNbrList => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      sequenceNbrs: sequenceNbrList,
    };
    console.log(
      '复制数据',
      apiData,
      selectState,
      stableRef.value.getSelectedRange(),
    );

    DataProcessor.removeClassName(); // 剪切样式的移除

    api.copyQdDeFbData(apiData).then(async res => {
      if (res.status === 200) {
        if(projectStore.type == 'jieSuan'){
          await jsApi.updatePasteLineHtDataColl({
            constructId: projectStore.currentTreeGroupInfo?.constructId,
            singleId: projectStore.currentTreeGroupInfo?.singleId,
            unitId: projectStore.currentTreeInfo?.id,
            type: 1,
          });
        }
        message.success('复制成功');
        // args?.hidePopup();
      }
    });
  };

  const csCopy = async sequenceNbrList => {
    // 原有逻辑需要过滤分部

    let isAwfData = false;

    let awfObj = tableData.value
      .filter(x => x.isAwfData)
      .map(a => a.sequenceNbr);
    sequenceNbrList?.forEach(item => {
      awfObj.forEach(child => {
        if (item === child) {
          isAwfData = true;
        }
      });
    });
    if (isAwfData) {
      message.error('安全生产、文明施工费下挂数据不可进行复制操作~');
      return;
    }
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      sequenceNbrs: sequenceNbrList,
    };
    console.log(
      '复制数据',
      apiData,
      selectState,
      stableRef.value.getSelectedRange(),
    );
    DataProcessor.removeClassName(); // 剪切样式的移除

    api.copyBranchDataQdDe(apiData).then(async res => {
      console.log('res', res);
      if (res.status === 200) {
        if(projectStore.type == 'jieSuan'){
          await jsApi.updatePasteLineHtDataColl({
            constructId: projectStore.currentTreeGroupInfo?.constructId,
            singleId: projectStore.currentTreeGroupInfo?.singleId,
            unitId: projectStore.currentTreeInfo?.id,
            type: 2,
          });
        }
        message.success('复制成功');
      }
    });
  };
  const pasteFun = async () => {
    console.log('pasteFun', isOpenEdit.value);
    // console.log('document.execCommand()', await navigator.clipboard.readText());
    if (isOpenEdit.value) {
      //单元格编辑激活的时候不粘贴数据行只粘贴内容
      if (await navigator.clipboard.readText()) {
        // navigator.clipboard.writeText('');
      } else {
        message.error('暂无复制数据');
      }
      return;
    }
    if (!copyData.value || copyData.value?.length === 0) {
      if (await navigator.clipboard.readText()) {
        // navigator.clipboard.writeText('');
      } else {
        message.error('暂无复制数据');
      }
    } else {
      if (
        copyDataInUnit &&
        copyDataInUnit.deStandardReleaseYear !==
          projectStore.deStandardReleaseYear
      ) {
        message.error('非同一定额标准的单位工程无法执行粘贴操作');
        return;
      }
      let copyDataOrigin = tableData.value.filter(i =>
        copyData.value.includes(i.sequenceNbr),
      );
      if (
        projectStore.standardGroupOpenInfo.isOpen &&
        copyDataOrigin.find(i => i.kind === '03')
      ) {
        message.error('标准组价不可粘贴包含清单行数据');
        return;
      }
      if (
        copyDataOrigin.length &&
        copyDataOrigin.every(item => [94, 95].includes(item.kind))
      ) {
        // 主材设备粘贴
        if (!isZcsbPasteRow()) {
          message.error('不可粘贴至该行');
          return;
        }
        zcsbPasteData();
        return;
      }
      // 结算原始数据不可复制到原始合同内数据下
      if(currentInfo.value?.originalFlag){
        console.log('adc')
        return;
      }
      batchPasteQdDeData();
    }
  };
  // 当前是否能粘贴到该行
  const isZcsbPasteRow = () => {
    if (type === 'fbfx' && !currentInfo.value?.bdCode) {
      return false;
    }
    if (type === 'csxm' && !currentInfo.value?.fxCode) {
      return false;
    }
    if (currentInfo.value?.rcjFlag === 1) {
      return false;
    }
    return true;
  };
  const zcsbPasteData = () => {
    const kind = currentInfo.value?.kind;
    if (![94, 95, '04'].includes(kind)) {
      message.warning('主材设备只能粘贴在定额行');
      return;
    }
    const pointLine = [94, 95].includes(kind)
      ? currentInfo.value?.customParent
      : currentInfo.value;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(pointLine)),
    };
    console.log('zhucai粘贴：', apiData);
    api.pasteLineZcSb(apiData).then(res => {
      console.log('zhucai返回值：', res);
      if (res.status === 200) {
        message.success('粘贴成功');

        DataProcessor.removeClassName(); // 剪切成功类名移除

        callBack('refresh');
      } else {
        message.error('粘贴失败');
      }
    });
  };
  // 批量粘贴数据
  const batchPasteQdDeData =async () => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    };
    let apiList = type === 'fbfx' ? 'fbBatchPaste' : 'batchPasteQdDeData';
    if(projectStore.type == 'jieSuan'){
      await jsApi.updatePasteDataColl({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeGroupInfo?.singleId,
        unitId: projectStore.currentTreeInfo?.id,
        type: type === 'fbfx' ? 1: 2,
      });
    }
    console.log('粘贴数据', apiData, apiList);
    api[apiList](apiData).then(res => {
      console.log('zhucai返回值：', res);
      if (res.status === 200) {
        message.success('粘贴成功');
        DataProcessor.removeClassName(); // 剪切成功类名移除

        callBack('refresh');
        // args?.hidePopup();
      } else {
        message.error('粘贴失败');
      }
    });
  };
  // const pasteFun = async (args, code) => {
  //   let apiData = {
  //     constructId: projectStore.currentTreeGroupInfo?.constructId,
  //     unitId: projectStore.currentTreeInfo?.id,
  //     oUnitId: projectStore.currentTreeInfo?.id,
  //     prevDeRowId: currentInfo.value.sequenceNbr,
  //     type: code === 'pasteChild' ? 'child' : '',
  //     idList: toRaw(copyData.value).filter(
  //       a => a != tableData.value[0].sequenceNbr
  //     ),
  //   };
  //   api.pasteDe(apiData).then(res => {
  //     if (res.status === 200) {
  //       stableRef.value.clearAllSelectedRange();
  //       copyData.value = null;
  //       message.success('粘贴成功');
  //       // queryBranchDataById();
  //       callBack('refresh');
  //     }
  //   });
  //   args?.hidePopup();
  // };
  return {
    sTableState,
    inputRefs,
    setInputRef,
    openEditor,
    setCloseEditor,
    clickOutside,
    cellMouseup,
    rowSelection,
    customRow,
    pasteRowVisible,
    cellKeydown,
    copyAndPaste,
    cutFun,
    copyFun,
    pasteFun,
    isAllSelectedZCSB,
    mousedownHandle,
    cellClickEvent,
    nextClickEditHandler,
    getSelectedRows
  };
};
