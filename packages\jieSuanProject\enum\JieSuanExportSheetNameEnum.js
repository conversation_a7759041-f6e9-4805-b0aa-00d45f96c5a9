const JieSuanExportSheetNameEnum = Object.freeze(Object.fromEntries([



        //htFlag  1 合同内    0 合同外
        //jsfs    1 一般计税  0 简易计税   如果没有标识  则说明 一般和简易计税通用
        ['常用报表',[
            {
                headLine:"表1-1 建设项目费用汇总表",
                projectLevel:"project",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-2 建设项目费用汇总表(沧州)",
                projectLevel:"project",
                city:"cz",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-3 工程项目竣工结算汇总表",
                projectLevel:"project",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-4 工程项目竣工结算汇总表(沧州)",
                projectLevel:"project",
                city:"cz",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-1 单项工程竣工结算汇总表",
                projectLevel:"single",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-1 单位工程竣工结算汇总表",
                projectLevel:"unit",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-2 分部分项合同清单工程量及结算工程量对比表",
                projectLevel:"unit",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-3 分部分项工程和单价措施项目清单与计价表",
                projectLevel:"unit",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-4 总价措施项目清单与计价表",
                projectLevel:"unit",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-5 其他项目清单与计价汇总表",
                projectLevel:"unit",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-6 材料暂估单价及调整表",
                projectLevel:"unit",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-7 计日工表",
                projectLevel:"unit",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-8 单位工程人材机汇总表",
                projectLevel:"unit",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-9 工程议价材料表",
                projectLevel:"unit",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-10 人材机调整明细表-1",
                projectLevel:"unit",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-11 人材机调整明细表-2",
                projectLevel:"unit",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-12 主材汇总表",
                projectLevel:"unit",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-13 甲方供应材料表",
                projectLevel:"unit",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-14 甲供材料汇总表",
                projectLevel:"unit",
                htFlag:1,
                de:["12","22"],
            },
            {
                headLine:"表1-1 单位工程人材机汇总表",
                projectLevel:"unit",
                htFlag:0,
                de:["12","22"],
            },
            {
                headLine:"表1-2 单位工程人材机价差汇总表",
                projectLevel:"unit",
                htFlag:0,
                de:["12","22"],
            },
            {
                headLine:"表1-3 人材机价差调整表",
                projectLevel:"unit",
                htFlag:0,
                de:["12","22"],
            },
            {
                headLine:"表1-4 主材汇总表",
                projectLevel:"unit",
                htFlag:0,
                de:["12","22"],
            },
        ]],

    ['13规范报表',[

        {
            headLine:"表1-1 总说明",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-2 建设项目竣工结算汇总表",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-3 建设项目竣工结算汇总表(沧州)",
            projectLevel:"project",
            city:"cz",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-1 竣工结算总价扉页",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-1 竣工结算书封面",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-1 单项工程竣工结算汇总表",
            projectLevel:"single",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-1 合同外单项工程竣工结算汇总表",
            projectLevel:"single",
            htFlag:0,
            de:["12","22"],
        },
        {
            headLine:"表1-1 单位工程竣工结算汇总表（含价差）",
            projectLevel:"unit",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-2 分部分项工程和单价措施项目清单与计价表",
            projectLevel:"unit",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-3 综合单价分析表",
            projectLevel:"unit",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-4 总价措施项目清单与计价表",
            projectLevel:"unit",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-5 其他项目清单与计价汇总表",
            projectLevel:"unit",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-6 材料(工程设备)暂估价及调整表",
            projectLevel:"unit",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-7 专业工程结算价表",
            projectLevel:"unit",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-8 计日工表",
            projectLevel:"unit",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-9 总承包服务费计价表",
            projectLevel:"unit",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-10 规费、税金项目结算表（不含价差）",
            projectLevel:"unit",
            htFlag:1,
            de:["12",],
        },
        {
            headLine:"表1-11 规费、税金项目结算表（含价差）",
            projectLevel:"unit",
            htFlag:1,
            de:["12",],
        },
        {
            headLine:"表1-12 发包人提供材料和工程设备一览表",
            projectLevel:"unit",
            htFlag:1,
            de:["12",],
        },
        {
            headLine:"表1-13 承包人提供主要材料和工程设备一览表",
            projectLevel:"unit",
            htFlag:1,
            de:["12",],
        },
        {
            headLine:"表1-14 承包人提供主要材料和工程设备一览表",
            projectLevel:"unit",
            htFlag:1,
            de:["12",],
        },
        {
            headLine:"表1-15 材料、机械、设备增值税计算表",
            projectLevel:"unit",
            htFlag:1,
            de:["12",],
            jsfs:1,
        },
        {
            headLine:"表1-16 增值税进项税额计算汇总表",
            projectLevel:"unit",
            htFlag:1,
            de:["12",],
            jsfs:1,  //如果是简易计税  需要过滤掉
        },

        //合同外
        {
            headLine:"表1-1 单位工程费用汇总表",
            projectLevel:"unit",
            htFlag:0,
            de:["12","22"],
        },
        {
            headLine:"表1-2 分部分项工程和单价措施项目清单与计价表",
            projectLevel:"unit",
            htFlag:0,
            de:["12","22"],
        },
        {
            headLine:"表1-3 综合单价分析表",
            projectLevel:"unit",
            htFlag:0,
            de:["12","22"],
        },
        {
            headLine:"表1-4 总价措施项目清单与计价表",
            projectLevel:"unit",
            htFlag:0,
            de:["12","22"],
        },
        {
            headLine:"表1-5 综合单价调整表",
            projectLevel:"unit",
            htFlag:0,
            de:["12","22"],
        },
        {
            headLine:"表1-6 其他项目清单与计价汇总表",
            projectLevel:"unit",
            htFlag:0,
            de:["12","22"],
        },
        {
            headLine:"表1-7 暂列金额表",
            projectLevel:"unit",
            htFlag:0,
            de:["12","22"],
        },
        {
            headLine:"表1-8 专业工程结算价表",
            projectLevel:"unit",
            htFlag:0,
            de:["12","22"],
        },
        {
            headLine:"表1-9 计日工表",
            projectLevel:"unit",
            htFlag:0,
            de:["12","22"],
        },
        {
            headLine:"表1-10 总承包服务费计价表",
            projectLevel:"unit",
            htFlag:0,
            de:["12","22"],
        },
        {
            headLine:"表1-11 规费、税金项目清单与计价表",
            projectLevel:"unit",
            htFlag:0,
            de:["12",],
        },
        {
            headLine:"表1-12 材料、机械、设备增值税计算表",
            projectLevel:"unit",
            htFlag:0,
            de:["12",],
            jsfs:1,  //如果是简易计税  需要过滤掉
        },
        {
            headLine:"表1-13 增值税进项税额计算汇总表",
            projectLevel:"unit",
            htFlag:0,
            de:["12",],
            jsfs:1,  //如果是简易计税  需要过滤掉
        },
        {
            headLine:"表1-10 发包人提供材料和工程设备一览表",
            projectLevel:"unit",
            htFlag:1,
            de:["22",],
        },
        {
            headLine:"表1-11 承包人提供主要材料和工程设备一览表",
            projectLevel:"unit",
            htFlag:1,
            de:["22",],
        },
        {
            headLine:"表1-12 承包人提供主要材料和工程设备一览表",
            projectLevel:"unit",
            htFlag:1,
            de:["22",],
        }



    ]],

    ['指标',[
        {
            headLine:"表1-1工程特征分析表",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-2工程项目信息表",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-3主要工程量指标-按项目结构分析指标",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-4主要工程量指标-按专业分析指标",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-5主要工料分析指标-按项目结构分析指标",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-6主要工料分析指标-按专业分析指标",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-7主要经济指标分析表-按费用分析指标(单体)",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-8主要经济指标分析表-按费用分析指标",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-9主要经济指标分析表-按项目结构分析指标",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-10主要经济指标分析表-按专业分析指标（单体）",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
        {
            headLine:"表1-11主要经济指标分析表-按专业分析指标",
            projectLevel:"project",
            htFlag:1,
            de:["12","22"],
        },
    ]],

]));

module.exports = JieSuanExportSheetNameEnum;
