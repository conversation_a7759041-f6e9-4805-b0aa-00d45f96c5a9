<!--
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2023-05-23 14:43:34
 * @LastEditors: sunchen
 * @LastEditTime: 2025-04-08 15:47:45
-->
<template>
  <div class="tab-menus">
    <!-- {{isSC()}} -->
    <tab-menu @getActiveKey="getActiveKey" ref="tabMenuRef"></tab-menu>
    <div>
      <!-- ,'foreignProcurement','summary' -->
      <!-- v-if="
        ((componentId === 'subItemProject' || componentId === 'measuresItem') &&
          currentInfo?.kind === '04') ||
        componentId === 'humanMachineSummary'
      " -->
      <!-- :disabled="!currentInfo.isUpFb"
          :disabled="!currentInfo.isDownFb" -->

      <template
        v-if="
          componentId === 'subItemProject' &&
          (currentInfo?.kind === '01' || currentInfo?.kind === '02')
        "
      >
        <a-button
          type="text"
          :disabled="!currentInfo?.isUp"
          @click="levelDeData('up')"
          ><icon-font
            type="icon-shangyi"
            :class="!currentInfo.isUp ? 'disabled-icon' : ''"
          ></icon-font
          >升级</a-button
        >
        <a-button
          type="text"
          :disabled="!currentInfo?.isDown"
          @click="levelDeData('down')"
          ><icon-font
            type="icon-xiayi"
            :class="!currentInfo.isDown ? 'disabled-icon' : ''"
          ></icon-font
          >降级</a-button
        >
      </template>
      <template v-if="showMoveBtn">
        <a-button
          type="text"
          @click="moveDeData('up')"
          :disabled="isMove?.isFirst"
          ><icon-font
            type="icon-shangyi"
            :class="isMove?.isFirst ? 'disabled-icon' : ''"
          ></icon-font
          >上移</a-button
        >
        <a-button
          type="text"
          @click="moveDeData('down')"
          :disabled="isMove.isLast"
          ><icon-font
            type="icon-xiayi"
            :class="isMove.isLast ? 'disabled-icon' : ''"
          ></icon-font
          >下移</a-button
        >
      </template>
      <template v-if="showPageBtn">
        <a-button type="text" @click="openColumnSetting"
          ><icon-font type="icon-xianshilieshezhi"></icon-font
        ></a-button>
      </template>
    </div>
  </div>
  <div class="main-content">
    <AsideMenuList
      ref="asideMenuRef"
      :title="asideTitle"
      :menuList="asideMenuList"
      :treeData="asideMenuList"
      :isTreeData="isTreeData"
      @currentMenu="currentMenu"
      v-model:updateStatus="updateStatus"
      :style="`display:${isDisplay}`"
    />
    <!-- :style="`height:${contentHeight}px`" -->
    <div class="content" ref="content">
      <keep-alive>
        <component
          :is="components.get(componentId)"
          :key="componentId"
          :menuList="asideMenuList"
          :componentId="componentId"
          @updateMenuList="updateMenuList"
          ref="childComponentRef"
          @vue:updated="onMountedChange"
          @getCurrentInfo="getCurrentInfo"
        ></component>
      </keep-alive>
      <combined-search></combined-search>
    </div>
  </div>

  <gljSelfCheckDialog v-model:checkVisible="checkVisible"></gljSelfCheckDialog>
  <!-- 页面列设置 -->
  <PageColumnSettingGs
    v-if="childComponentRef"
    :columnOptions="
      !isSC()
        ? childComponentRef.handlerColumns
        : childComponentRef.threeMaterialsRef?.handlerColumns
    "
    ref="columnSettingRef"
    title="页面显示列设置"
    :componentId="componentId"
    @save="save"
    :getDefaultColumns="
      !isSC()
        ? childComponentRef.getDefaultColumns
        : childComponentRef.threeMaterialsRef?.getDefaultColumns
    "
  />
</template>

<script setup>
import {
  defineAsyncComponent,
  markRaw,
  onBeforeUnmount,
  ref,
  toRaw,
  onMounted,
  provide,
  createApp,
  watch,
  inject,
  reactive,
  nextTick,
  getCurrentInstance,
  onUnmounted,
  computed,
  watchEffect,
} from 'vue';
import TabMenu from './TabMenu.vue';
import AsideMenuList from './AsideMenuList.vue';
import csProject from '../../../api/csProject';
import { projectDetailStore } from '@/store/projectDetail';
import CombinedSearch from '@/views/projectDetail/customize/measuresItem/combinedSearch.vue';
import api from '@/api/projectDetail.js';
import xeUtils from 'xe-utils';
import { message } from 'ant-design-vue';
import deMapFun from './deMap';
import infoMode from '@/plugins/infoMode.js';
import operateList, { updateOperateByName } from './operate';

const emits = defineEmits(['updateMenuList', 'getCurrentInfo', 'resetMenu']);

const childComponentRef = ref();
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const providedMethod = inject('providedMethod');
const shouldRenderComponent = ref(true);
const components = markRaw(new Map());
const childMethod = ref();

provide('mainData', {
  currentInfo: computed(() => currentInfo.value),
  componentId: computed(() => componentId.value),
  isMove: computed(() => isMove.value),
});
const isSC = () => {
  return (
    (projectStore.tabSelectName === '人材机汇总' &&
      projectStore.asideMenuCurrentInfo?.code == '99') ||
    projectStore.tabSelectName == '费用汇总'
  );
};
components.set(
  'basicInfo',
  defineAsyncComponent(() => import('./ProjectOverview/BasicInfo.vue'))
);
// 概算调整
components.set(
  'estimateEdit',
  defineAsyncComponent(() => import('./estimateEdit/index.vue'))
);
// 国内采购设备
components.set(
  'domesticProcurement',
  defineAsyncComponent(() =>
    import('./equipmentProcurementCost/domesticProcurement.vue')
  )
);
//国外采购设备
components.set(
  'foreignProcurement',
  defineAsyncComponent(() =>
    import('./equipmentProcurementCost/foreignProcurement.vue')
  )
);
// 设备购置费汇总
components.set(
  'summary',
  defineAsyncComponent(() => import('./equipmentProcurementCost/summary.vue'))
);
components.set(
  'engineerFeature',
  defineAsyncComponent(() => import('./ProjectOverview/engineerFeature.vue'))
);
// 预算书
components.set(
  'subItemProject',
  defineAsyncComponent(() => import('./subItemProject/index.vue'))
);

// 措施项目
components.set(
  'measuresItem',
  defineAsyncComponent(() => import('./measuresItem/index.vue'))
);

components.set(
  'qtxmStatistics',
  defineAsyncComponent(() => import('./OtherProject/qtxmStatistics.vue'))
);
components.set(
  'qtxmZlje',
  defineAsyncComponent(() => import('./OtherProject/zlje.vue'))
);
components.set(
  'qtxmClzgj',
  defineAsyncComponent(() => import('./OtherProject/clzgj.vue'))
);
components.set(
  'qtxmSbzgj',
  defineAsyncComponent(() => import('./OtherProject/sbzgj.vue'))
);
components.set(
  'qtxmZygczgj',
  defineAsyncComponent(() => import('./OtherProject/zygczgj.vue'))
);
components.set(
  'qtxmZcbfwf',
  defineAsyncComponent(() => import('./OtherProject/zcbfwf.vue'))
);
components.set(
  'qtxmJrg',
  defineAsyncComponent(() => import('./OtherProject/jrg.vue'))
);
components.set(
  'qtxmZyclsb',
  defineAsyncComponent(() => import('./OtherProject/zyclsb.vue'))
);
components.set(
  'qtxmJgclsb',
  defineAsyncComponent(() => import('./OtherProject/jgclsb.vue'))
);
components.set(
  'feeWithDrawalTable', //费率总览+说明+政策文件
  defineAsyncComponent(() => import('./FeeWithDrawalTable/index.vue'))
);
components.set(
  'PreparationOfInstructions', //编制说明
  defineAsyncComponent(() => import('./PreparationOfInstructions/index.vue'))
);
components.set(
  'CostAnalysis', //造价分析
  defineAsyncComponent(() => import('./CostAnalysis/index.vue'))
);
components.set(
  'IndependentFee', //造价分析
  defineAsyncComponent(() => import('./IndependentFee/index.vue'))
);
components.set(
  'summaryExpense', //费用汇总
  defineAsyncComponent(() => import('./SummaryExpense/index.vue'))
);
components.set(
  'estimateExpense', //概算汇总
  defineAsyncComponent(() => import('./estimateExpense/index.vue'))
);
components.set(
  'humanMachineSummary', //人材机汇总
  defineAsyncComponent(() => import('./HumanMachineSummary/index.vue'))
);
components.set(
  'otherConstructionCosts', //建设其他费
  defineAsyncComponent(() => import('./OtherConstructionCosts/index.vue'))
);
// let componentId = ref("feeWithDrawalTable");
let componentId = ref('subItemProject'); //根据tab栏选中的tab加载对应页面
const content = ref();
let contentHeight = ref();
const projectStore = projectDetailStore();
const activeKey = ref(1);
let isTreeData = ref(false);
let isDisplay = ref('block');
let asideMenuList = ref([]);
let asideTitle = ref('工程概况');
// let asideTitle = ref('');
let tabSelectName = ref();
let featureData = ref([]);
let updateStatus = ref(false);
let currentInfo = ref(); // 预算书措施项目点击的当前数据

// onMounted(() => {
//   alert('1234')

// }),

bus.on('vertical-transport', ({ event, name }) => {
  // if (name === 'subItemProject') copyAndPaste(event);
});

let checkVisible = ref(false);
// 项目自检
bus.on('onSelfCheck', ({ event, name }) => {
  checkVisible.value = true;
});

watch(
  () => componentId.value,
  val => {
    projectStore.moveRow = {
      tableData: [],
      useRowList: [],
    };
    projectStore.SET_COMPONENT_ID(val);
  },
  { immediate: true }
);
const onMountedChange = () => {
  if (
    childComponentRef.value &&
    componentId.value === 'subItemProject' &&
    !projectStore.subItemProjectAutoPosition
  ) {
    projectStore.subItemProjectAutoPosition = childComponentRef.value;
  }

  if (childComponentRef.value && componentId.value === 'measuresItem') {
    projectStore.measuresItemProjectAutoPosition = childComponentRef.value;
  }
};
const save = data => {
  console.log(data, childComponentRef.value.threeMaterialsRef);
  !isSC()
    ? childComponentRef.value.updateColumns(data)
    : childComponentRef.value.threeMaterialsRef?.updateColumns(data);
};
watch(
  () => childComponentRef.value,
  (val, oldValue) => {
    console.info('点击上方tab切换跳转的页面');
    console.info(componentId.value);
    if (
      val &&
      componentId.value === 'subItemProject' &&
      !projectStore.subItemProjectAutoPosition
    ) {
      projectStore.subItemProjectAutoPosition = val;
    }

    if (
      val &&
      componentId.value === 'measuresItem' &&
      !projectStore.measuresItemProjectAutoPosition
    ) {
      projectStore.measuresItemProjectAutoPosition = val;
    }
    if (
      val &&
      componentId.value === 'qtxmStatistics' &&
      !projectStore.otherProjectAutoPosition
    ) {
      projectStore.otherProjectAutoPosition = val;
    }

    if (
      val &&
      componentId.value === 'qtxmJrg' &&
      !projectStore.dailyWorkProjectAutoPosition
    ) {
      projectStore.dailyWorkProjectAutoPosition = val;
    }

    if (
      val &&
      componentId.value === 'qtxmZcbfwf' &&
      !projectStore.serviceProjectAutoPosition
    ) {
      projectStore.serviceProjectAutoPosition = val;
    }

    if (
      val &&
      componentId.value === 'humanMachineSummary' &&
      !projectStore.summaryProjectAutoPosition
    ) {
      projectStore.summaryProjectAutoPosition = val;
    }

    if (
      val &&
      [
        'domesticProcurement',
        'foreignProcurement',
        'summary',
        'basicInfo',
        'engineerFeature',
      ].includes(componentId.value) &&
      !projectStore.AsideMenuExpose[componentId.value]
    ) {
      projectStore.AsideMenuExpose[componentId.value] = val;
    }
  },
  { deep: true }
);

let tabMenuRef = ref(null);
watch(
  () => tabMenuRef.value,
  val => {
    projectStore.tabMenuRef = val;
  },
  { deep: true }
);
let asideMenuRef = ref(null);
watch(
  () => asideMenuRef.value,
  val => {
    projectStore.asideMenuRef = val;
  },
  { deep: true }
);
watch(
  () => projectStore.subCurrentInfo,
  value => {
    console.log('🚀 ~ value:', value);
    currentInfo.value = value;
  }
);

//编制说明
let bzsm = ref({});
const handleCopyEvent = event => {
  const activeComponent = components.get(componentId.value);
  if (activeComponent) {
    // 调用活动组件的复制操作
    // childComponentRef.value.copyAndPaste(event);
    bus.emit('handleCopyEvent', { event, name: componentId.value });
  }
};
onMounted(() => {
  window.addEventListener('keydown', handleCopyEvent);
  contentHeight.value = content.value.clientHeight;
  queryUnit();
});

const queryUnit = () => {
  let apiData = {
    dictCode: 'UNIT_DICT',
  };
  csProject.otherProjectSysDictionary(apiData).then(res => {
    let list = [];
    let unitListString = [];
    if (res.status === 200) {
      res.result &&
        res.result.map(item => {
          list.push(item.entryValue);
        });
      console.log('unitListString', res.result);
      unitListString = list ? String(list) : '';
      projectStore.SET_UNITLISTSTRING(unitListString);
    }
  });
};
// 处理tab默认数据
const handleGljTablist = (tabName, gljSelData) => {
  gljSelData.tabList.push({
    tabName, //当前上方tab名称
    leftTwoTreeId: '', //左侧二级树选中id
    selRowId: '', //上方列表选中行id
    bottomTabName: '', //下方tab选中名称
    bottomSelRowId: '', //下方列表选中行id
    bottomSelLeftTreeId: '', //下方左侧树选中id
  });
  let obj = {
    selLeftTreeId: projectStore.currentTreeInfo.sequenceNbr,
  };
  obj[projectStore.currentTreeInfo.sequenceNbr] = gljSelData;
  projectStore.SET_GLJ_CHECK_TAB(obj);
};
onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleCopyEvent);
});
const getActiveKey = (key, tabName) => {
  console.info('点击上方tab切换22222', key, tabName);
  let gljSelData =
    projectStore.gljCheckTab[projectStore.currentTreeInfo.sequenceNbr];
  if (gljSelData && gljSelData.selTabName) {
    gljSelData.selTabName = tabName;
    let nowTab = gljSelData.tabList.find(a => a.tabName == tabName);
    if (!nowTab) {
      handleGljTablist(tabName, gljSelData);
    }
  }
  tabSelectName.value = tabName;
  asideTitle.value = tabName;
  projectStore.SET_TAB_SELECT_NAME(tabName);
  if (tabSelectName.value === '造价分析') {
    projectStore.SET_Cost_Analysis_Code(key);
  } else {
    projectStore.SET_Cost_Analysis_Code(null);
  }
  if (tabSelectName.value === '费用汇总') {
    componentId.value = 'summaryExpense';
  }

  if (tabSelectName.value === '人材机汇总') {
    componentId.value = 'humanMachineSummary';
  }
  activeKey.value = key;
  if (projectStore.currentTreeInfo.type !== 2) {
    if (
      projectStore.currentTreeInfo.type === 5 &&
      tabSelectName.value === '独立费'
    ) {
      componentId.value = 'IndependentFee';
      isDisplay.value = 'none';
    } else {
      getMenuData(); //根据选中tab栏获取项目概况侧边栏
    }
  } else {
    componentId.value = 'CostAnalysis';
    isDisplay.value = 'none';
    getMenuData();
  }
};
let columnSettingRef = ref();
const openColumnSetting = () => {
  columnSettingRef.value.open();
};
const updateMenuList = type => {
  updateStatus.value = true;
  getMenuData(1, type);
};
bus.on('dzyhzQuery', () => {
  getMenuData();
});

bus.on('focusTableData', () => {
  switch (tabSelectName.value) {
    case '项目概况':
    case '工程概况':
      if (projectStore.asideMenuCurrentInfo.name == '基本工程信息') {
        bus.emit('basicEngineeringInformation');
      } else {
        bus.emit('engineeringCharacteristics');
      }
      break;
    case '措施项目':
    case '预算书':
      bus.emit(projectStore.lastClickTableRef);
      break;
    case '独立费':
      bus.emit('independentFee');
      break;
    case '费用汇总':
      bus.emit('costAggregation');
      break;
  }
});
bus.on('changeLeftNum', () => {
  let arr = JSON.parse(JSON.stringify(asideMenuList.value));
  projectStore.categoryIndex = arr.length - 1;
});
const getCurrentInfo = value => {
  currentInfo.value = value;
};

const getMenuData = (status = 0, type = false) => {
  let apiData = {
    type: 1, // 编制
    levelType: projectStore.currentTreeInfo?.type,
    code: activeKey.value,
    unitId: [1, 2].includes(projectStore.currentTreeInfo?.type)
      ? ''
      : projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: [1, 3].includes(projectStore.currentTreeInfo?.type)
      ? ''
      : projectStore.currentTreeGroupInfo?.singleId,
  };
  if (!apiData.levelType) {
    //levelType不存在不掉接口，这个是偶现，一次性调好多次
    return;
  }
  csProject.getMenuData(apiData).then(res => {
    if (res.status !== 200) {
      message.error(res.message);
      return false;
    }
    console.log('getMenuData--', res.result, apiData);
    // return;
    // debugger;
    // asideMenuList.value = res.result
    //项目概况侧边栏第一项设置为选中
    // projectStore.SET_Fee_With_Drawal_Info(asideMenuList.value[0]);
    // currentMenu(asideMenuList.value[0]);
    if (res.result && res.result.itemList) {
      isTreeData.value = false;
      let tempList = [];
      res.result.itemList.forEach(item => {
        let obj = {
          name: Object.values(item)[0],
          key: Object.keys(item)[0],
          defaultFeeFlag: Object.values(item)[1] ? Object.values(item)[1] : '0',
        };
        tempList.push(obj);
      });
      if (projectStore.tabSelectName === '人材机汇总') {
        tempList.map(item => {
          if (item.name.trim() === '主要材料表') {
            item.name = '主要材料、设备表';
          }
        });
      }
      asideMenuList.value = tempList;
    } else if (res.result && res.result.treeModel) {
      isTreeData.value = true;
      asideMenuList.value = [res.result.treeModel];
      // 如果为人材机返回的内容
    } else if (res.result && res.result.rcjList) {
      isTreeData.value = false;
      let arr = ['人工', '材料', '机械', '设备', '主材', '预拌混凝土'];
      res.result.rcjList.map(item => {
        if (arr.includes(item.name)) {
          item.parentCode = '0';
        } else {
          item.parentCode = null;
        }
      });
      asideMenuList.value = xeUtils.toArrayTree(res.result.rcjList, {
        key: 'code',
        children: 'children',
        parentKey: 'parentCode',
      });
      console.log('asideMenuList.value', asideMenuList.value);
      //如果返回为空，列表展示空
    } else if (
      res.result &&
      res.result.length > 0 &&
      projectStore.tabSelectName === '预算书'
    ) {
      console.log('aaaagetMenuData--', res, projectStore);
      isTreeData.value = true;
      if (res.result[0].type == '0') {
        res.result[0].kind = '00';
        res.result[0].deName = '单位工程';
      }

      res.result = res.result.map(a => {
        return { bdName: a.deName || '---', childTreeModel: [], ...a };
      });
      asideMenuList.value = addLevelToTree(
        xeUtils.toArrayTree(res.result, {
          key: 'sequenceNbr',
          children: 'childTreeModel',
        })
      );
      console.log('asideMenuList.value', asideMenuList.value);
    } else if (
      !(res.result && res.result.itemList) &&
      !(res.result && res.result.treeModel)
    ) {
      asideMenuList.value = [];
    }
    let menuList = JSON.parse(JSON.stringify(asideMenuList.value));
    if (menuList.length > 0) {
      switch (projectStore.tabSelectName) {
        case '项目概况':
        case '工程概况':
        case '费用汇总':
          for (let item of menuList) {
            item['sequenceNbr'] = item.key;
          }
          break;
        case '人材机汇总':
          for (let item of menuList) {
            item['sequenceNbr'] = item.code;
            item['childTreeModel'] = item.children;
            item['parentId'] = item.parentCode;
            if (item.children.length > 0) {
              for (let child of item.children) {
                child['sequenceNbr'] = child.code;
                child['childTreeModel'] = child.children;
              }
            }
          }
          break;
      }
      asideMenuList.value = menuList;
    }
    if (!status) {
      currentContent(); //加载对应页面
    }
    if (type) {
      projectStore.selectedKeyshuman = 1;
    } else {
      projectStore.selectedKeyshuman = 0;
    }

    if (tabSelectName.value === '费用汇总') {
      setTimeout(() => {
        bus.emit('activeKey', asideMenuList.value);
      }, 100);
    }
    console.log('asideMenuList---', asideMenuList.value, projectStore);
  });
};
const addLevelToTree = (data, parentLevel = 0) => {
  return data.map(node => ({
    ...node,
    customLevel: parentLevel + 1,
    childTreeModel:
      (node.childTreeModel || []).length > 0
        ? addLevelToTree(node.childTreeModel, parentLevel + 1)
        : [],
  }));
};
//点击tab获取相应页面
const currentContent = () => {
  console.info(11111111111, tabSelectName.value);
  isDisplay.value = 'block';
  shouldRenderComponent.value = true;
  switch (tabSelectName.value) {
    case '造价分析':
      componentId.value = 'CostAnalysis';
      isDisplay.value = 'none';
      break;
    case '取费表':
      isDisplay.value = 'none';
      componentId.value = 'feeWithDrawalTable';
      //projectStore.SET_Fee_With_Drawal_Info(asideMenuList.value[0]);
      // currentMenu(asideMenuList.value[0]);
      break;
    case '建设其他费':
      isDisplay.value = 'none';
      componentId.value = 'otherConstructionCosts';
      //projectStore.SET_Fee_With_Drawal_Info(asideMenuList.value[0]);
      // currentMenu(asideMenuList.value[0]);
      break;
    case '费用汇总':
      // isDisplay.value = 'none';
      componentId.value = 'summaryExpense';
      break;
    case '调整概算':
      isDisplay.value = 'none';
      componentId.value = 'estimateEdit';
      break;
    case '概算汇总':
      isDisplay.value = 'none';
      componentId.value = 'estimateExpense';
      break;
    case '项目概况':
    case '工程概况':
      componentId.value = 'basicInfo';
      break;
    case '设备购置费':
      componentId.value = 'domesticProcurement';
      break;
    case '独立费':
      isDisplay.value = 'none';
      componentId.value = 'IndependentFee';
      break;
    case '其他项目':
      componentId.value = 'qtxmStatistics';
      break;
    case '人材机汇总':
      componentId.value = 'humanMachineSummary';
      break;
    case '预算书':
      componentId.value = 'subItemProject';
      break;
    case '措施项目':
      isDisplay.value = 'none';
      componentId.value = 'measuresItem';
      break;
  }
  shouldRenderComponent.value = false;
};
const currentMenu = item => {
  shouldRenderComponent.value = true;
  switch (item.key) {
    case '11':
      componentId.value = 'basicInfo';
      break;
    case '12':
      componentId.value = 'PreparationOfInstructions';
      break;
    case '13':
      componentId.value = 'engineerFeature';
      break;
    case 'qtxm00':
      componentId.value = 'qtxmStatistics';
      break;
    case 'qtxm01':
      componentId.value = 'qtxmZlje';
      break;
    case 'qtxm02':
      componentId.value = 'qtxmClzgj';
      break;
    case 'qtxm03':
      componentId.value = 'qtxmSbzgj';
      break;
    case 'qtxm04':
      componentId.value = 'qtxmZygczgj';
      break;
    case 'qtxm05':
      componentId.value = 'qtxmZcbfwf';
      break;
    case 'qtxm06':
      componentId.value = 'qtxmJrg';
      break;
    case 'qtxm07':
      componentId.value = 'qtxmZyclsb';
      break;
    case 'qtxm08':
      componentId.value = 'qtxmJgclsb';
      break;
    case 'sbgzf00':
      componentId.value = 'domesticProcurement';
      break;
    case 'sbgzf01':
      componentId.value = 'foreignProcurement';
      break;
    case 'sbgzf02':
      componentId.value = 'summary';
      break;
  }
  shouldRenderComponent.value = false;
};

/**
 * 查找父级元素
 * @param {*} tree
 * @param {*} parentId
 */
const findNodeByParentId = (tree, parentId) => {
  function search(node, parentID) {
    if (node.sequenceNbr === parentID) {
      // Return all direct children of the node
      return node;
    }

    for (const child of node.children) {
      const result = search(child, parentID);
      if (result) {
        return result;
      }
    }

    return null;
  }

  return search(tree, parentId);
};

// 移动
let isMove = ref(false);
watchEffect(() => {
  console.log('🚀 ~ moveDeData ~ componentId:', componentId.value);
  let moveObj = {
    isLast: false,
    isFirst: false,
  };
  if (
    componentId.value != 'subItemProject' &&
    componentId.value != 'humanMachineSummary'
  ) {
    if (
      projectStore.moveRow.useRowList.length &&
      projectStore.moveRow.tableData?.length
    ) {
      // 检查有没有选中的，并且有列表数据的，现在做单选，不用考虑多个
      let tableList = projectStore.moveRow.isTree
        ? projectStore.moveRow.tableData
        : xeUtils.toArrayTree(projectStore.moveRow.tableData, {
            key: 'sequenceNbr',
            children: 'children',
          });
      let currentRow = projectStore.moveRow.useRowList[0];
      let nodes = {};
      if (currentRow?.parentId) {
        nodes = findNodeByParentId(
          { id: 0, children: tableList },
          currentRow.parentId
        );
      } else {
        // 自己是父级
        nodes.children = tableList;
      }
      if (nodes?.children) {
        const index = nodes.children.findIndex(
          child => child.sequenceNbr === currentRow.sequenceNbr
        );
        moveObj = {
          isFirst:
            componentId.value !== 'summary'
              ? index == 0
              : index === 0 || index === 1,
          isLast:
            componentId.value !== 'summary'
              ? index === nodes.children.length - 1
              : index === 0 || index === nodes.children.length - 1,
        };
        console.log(
          'index',
          index,
          moveObj,
          index === 0 || index === nodes.children.length - 1
        );
      }
      if (nodes?.name == '工程费用') {
        moveObj = {
          isLast: true,
          isFirst: true,
        };
      }
    } else {
      console.log('aa', projectStore.moveRow.useRowList);
      moveObj = {
        isLast: false,
        isFirst: true,
      };
    }
  } else {
    moveObj = {
      isLast: false,
      isFirst: false,
    };
  }

  isMove.value = moveObj;
});
// 升降级
const levelDeData = type => {
  let apiData = {
    unitId:
      projectStore.currentTreeInfo?.type === 1
        ? ''
        : projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: '',
    selectId: currentInfo.value.sequenceNbr,
    operateAction: type,
  };
  csProject.fbDataUpAndDownController(apiData).then(res => {
    if (res.status != 200) {
      return message.error(res.message);
    }
    childComponentRef.value?.getTableData(
      'position',
      '',
      projectStore.moveRow.useRowList.map(i => i.sequenceNbr)
    );
    updateMenuList();
  });
};
// 移动的接口
const moveDeData = moveType => {
  let apiName = {
    otherConstructionCosts: 'moveUpAndDownProjectCost',
    estimateExpense: 'moveUpAndDownEstimateSummary',
    domesticProcurement: 'moveUpAndDownEquipmentCosts',
    foreignProcurement: 'moveUpAndDownEquipmentCosts',
    summary: 'moveUpAndDownEquipmentCosts',
    engineerFeature: 'moveUpAndDownOverview',
    basicInfo: 'moveUpAndDownOverview',
    humanMachineSummary: 'saveRcjCellectMenuSortShare',
    subItemProject: 'yssMoveUpAndDown',
    zGHumanMachineSummary: 'moveUpAndDownZgRcjCellect',
  };
  if (!componentId.value || !apiName[componentId.value]) {
    message.error('配置问题，稍后');
    return;
  }
  let postData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    projectId: projectStore.currentTreeGroupInfo?.constructId,
    sequenceNbrArray: projectStore.moveRow.useRowList.map(i => i.sequenceNbr),
    moveType,
  };

  if (projectStore.currentTreeInfo.type == 3) {
    postData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    postData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }

  if (['basicInfo', 'engineerFeature'].includes(componentId.value)) {
    // 基础信息，工程概况
    let deviceObj = {
      basicInfo: '11',
      engineerFeature: '13',
    };
    postData.type = deviceObj[componentId.value];
  }
  // 人材机汇总
  if (['humanMachineSummary'].includes(componentId.value)) {
    postData.levelType = projectStore.currentTreeInfo.type;
    postData.kind = projectStore.asideMenuCurrentInfo.code;
    postData.sequenceNbrArray = JSON.parse(
      JSON.stringify(projectStore.moveRow.useRowList)
    );

    let useRwoIndex = projectStore.moveRow.tableData.findIndex(
      i => i.sequenceNbr == projectStore.moveRow.useRowList[0].sequenceNbr
    );

    postData.upOrDownLine =
      projectStore.moveRow.tableData[
        moveType == 'up' ? useRwoIndex - 1 : useRwoIndex + 1
      ]?.sequenceNbr;

    if (
      projectStore.humanUpdataData &&
      projectStore.humanUpdataData.isEdit &&
      [1, 2].includes(projectStore.currentTreeInfo?.type)
    ) {
      saveHumanData('', () => {
        moveDeData(moveType);
      });
      return;
    }
  }

  if (
    ['domesticProcurement', 'foreignProcurement', 'summary'].includes(
      componentId.value
    )
  ) {
    // 概算汇总
    let deviceObj = {
      domesticProcurement: 'sbgzf00',
      foreignProcurement: 'sbgzf01',
      summary: 'sbgzf02',
    };
    postData.type = deviceObj[componentId.value];
  }
  if (['subItemProject'].includes(componentId.value)) {
    // 预算书
    postData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      deRowIds: toRaw(childComponentRef.value?.selectState.selectedRowKeys), //连续同级定额，
      type: moveType, //  up 上 down 下
    };
  }

  if (projectStore.currentTreeInfo.type == 2) {
    postData.singleId = projectStore.currentTreeGroupInfo?.singleId;
  }

  if (projectStore.currentTreeInfo.type == 3) {
    postData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
  }

  let apiPathName = apiName[componentId.value];
  csProject[apiPathName](postData).then(res => {
    console.log('上下移动接口返回结果', res, postData);
    if (res.status != 200) {
      return message.error(res.message);
    }
    if (projectStore.AsideMenuExpose[componentId.value]?.getTableData) {
      projectStore.AsideMenuExpose[componentId.value]?.getTableData('position');
    } else {
      childComponentRef.value?.getTableData(
        ['humanMachineSummary'].includes(componentId.value) ? 1 : 'position',
        '',
        !['subItemProject'].includes(componentId.value)
          ? projectStore.moveRow.useRowList.map(i => i.sequenceNbr)
          : -1
      );
    }
    // 如果是预算书页面则移动后需要更新左侧树 同步分部位置
    if (componentId.value == 'subItemProject') {
      updateMenuList();
    }
  });
};

let unifyData = ref();
const saveHumanData = (oldVal, callback = null) => {
  unifyData.value = operateList.value.find(
    item => item.name === projectStore.humanUpdataData.name
  );
  let infoText = '人材机数据已修改，是否应用整个工程项目?';
  if (projectStore.currentTreeInfo.type == 2) {
    infoText = '人材机数据已修改，是否应用整个单项工程?';
  }
  infoMode.show({
    isSureModal: false,
    iconType: 'icon-querenshanchu',
    infoText,
    confirm: () => {
      isUse(oldVal, callback);
      infoMode.hide();
    },
    close: () => {
      setTimeout(() => {
        callback();
      }, 100);
      projectStore.humanUpdataData.isEdit = false;
      unifyData.value.disabled = true;
      infoMode.hide();
    },
  });
};

const isUse = (type, callback = null) => {
  //点击统一应用按钮
  console.log(projectStore.humanUpdataData);
  if (!projectStore.humanUpdataData) {
    return;
  }
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    levelType: +projectStore.currentTreeInfo.type,
    constructProjectRcjList: JSON.parse(
      JSON.stringify(projectStore.humanUpdataData.updataData)
    ),
  };
  console.log('apiData111', apiData);
  //只是清除载价就传空值，清除载价+改市场价传修改数据
  csProject.changeRcjConstructProject(apiData).then(res => {
    if (res.status == 200) {
      message.success('应用成功！');
      projectStore.SET_HUMAN_UPDATA_DATA(null);
      if (callback) {
        nextTick(() => {
          setTimeout(() => {
            callback();
          }, 100);
        });
      }
      unifyData.disabled = true;
      let obj = {
        constructId: projectStore.currentTreeGroupInfo?.constructId,
      };
      csProject.temporaryDataDel(obj).then(res => {
        if (res.status !== 200) {
          return false;
        }
      });
    }
  });
};

const showMoveBtn = computed(() => {
  if (componentId.value === 'subItemProject') {
    return currentInfo.value?.kind != '00' && currentInfo.value?.kind != '05';
  } else if (componentId.value === 'basicInfo') {
    return (
      projectStore.asideMenuCurrentInfo?.code !== '99' &&
      projectStore.currentSelectRow
    );
  } else {
    return (
      [
        'otherConstructionCosts',
        'estimateExpense',
        'domesticProcurement',
        'foreignProcurement',
        'summary',
        'engineerFeature',
        'humanMachineSummary',
        'subItemProject',
      ].includes(componentId.value) &&
      ![99].includes(+projectStore.asideMenuCurrentInfo?.code)
    );
  }
});
const showPageBtn = computed(() => {
  // 'humanMachineSummary',

  let arr = [
    'CostAnalysis',
    'subItemProject',
    'estimateExpense',
    'IndependentFee',
    'humanMachineSummary',
    'measuresItem',
    'summaryExpense',
  ];
  return arr.includes(componentId.value);
});

//  ------- 切换计税方式start ------
let showRateList = ref(false);
const refreshTableList = () => {
  if (componentId.value === 'subItemProject') {
    projectStore?.subItemProjectAutoPosition?.queryBranchDataById();
  }
  if (componentId.value === 'measuresItem') {
    projectStore?.measuresItemProjectAutoPosition?.queryBranchDataById();
  }
  if (componentId.value === 'humanMachineSummary') {
    console.log(projectStore.summaryProjectAutoPosition);
    if (projectStore?.type === 'jieSuan') {
      projectStore?.summaryProjectAutoPosition?.getInitList();
    } else {
      console.info(2344234234234, projectStore);
      projectStore?.summaryProjectAutoPosition?.getTableData();
    }
  }
  if (componentId.value === 'feeWithDrawalTable') {
    projectStore?.feeWithDrawalAutoPosition?.refresh();
  }
  if (componentId.value === 'CostAnalysis') {
    projectStore?.costAnalysisComponentRef?.refreshList();
  }

  if (componentId.value === 'summaryExpense') {
    projectStore.summaryExpenseGetList();
  }
};
//  ------- 切换计税方式end ------

defineExpose({
  componentId,
  childComponentRef,
  refreshTableList,
});
</script>
<style lang="scss" scoped>
.tab-menus {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 42px;
  border-bottom: 2px solid #dcdfe6;
}
.main-content {
  display: flex;
  //height: calc(100vh - 185px);
  height: calc(100vh - 197px);
}
.content {
  flex: 1;
  max-height: 100%;
  overflow: hidden;
  background: #ffffff;
}
.disabled-icon {
  opacity: 0.5;
}
</style>
