const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils.js")
const {ObjectUtil} = require("../../../common/ObjectUtil.js");
const {LifeFactory, ExtCell, Organ, Gene} = require("@valuation/rules-engine");
const {getMethod1Rule} = require("./adjustRules/method1Rules");
const {getMethod2Rule} = require("./adjustRules/method2Rules");
const JieSuanRcjDifferenceEnum = require("../enum/JieSuanRcjDifferenceEnum");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {JieSuanRcjStageUtils} = require("../utils/JieSuanRcjStageUtils");
const {NumberUtil} = require("../../../common/NumberUtil");
const {getMethod3Rule} = require("./adjustRules/method3Rules");
const {getMethod4Rule} = require("./adjustRules/method4Rules");
const ConstantUtil = require("../../../electron/enum/ConstantUtil");
const {JieSuanDataUtils} = require("../utils/JieSuanDataUtils");


class  RcjHandleFactory extends LifeFactory{
    constructor(constructId, singleId, unitId,arg) {
        super({});
        //this.originalFlag = this.getOriginalFlag(arg);
        if (arg.levelType == 3){
            this.unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            this.obj = this.unit;
            this.is2022 = PricingFileFindUtils.is22UnitById(constructId,singleId,unitId);
            this.isStage = JieSuanRcjStageUtils.isStage(this.unit);
        }
        if (arg.levelType == 2){
            this.obj = PricingFileFindUtils.getSingleProject(constructId, singleId);
        }
        if (arg.levelType == 1){
            this.obj = PricingFileFindUtils.getProjectObjById(constructId);
            this.is2022 = this.obj.deStandardReleaseYear == ConstantUtil.DE_STANDARD_22;
        }
        this.arg = arg;
        this.constructId = constructId;
        this.singleId = singleId;
        this.unitId = unitId;
        this.rcjGroupMap = null;
        this.unitList = [];
        this.constructProjectRcjs = [];
        this.rcjDetailList = [];
        this.itemBillProjects = [];
        this.measureProjectTables = [];
        this.calculateRcjList = [];//需要计算的人材机数据
        this.dependRcjList = [];//计算时需要依赖的人材机数据
        this.rcj = null;//当前计算的人材机
        this.calculateField = {};//计算字段

        this.rules = {};
        this.calculateRules = {};
        this.conditionCalculatedField =[];//条件计算字段

    }


    getOriginalFlag(arg){

        let {constructId, singleId, unitId,levelType} = arg;

        let originalFlag = false;
        if (levelType == 3){
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            if (ObjectUtils.isNotEmpty(unit.originalFlag)){
                originalFlag = unit.originalFlag;
            }
        }

        if (levelType == 2) {
            let single = PricingFileFindUtils.getSingleProject(constructId, singleId);
            if (ObjectUtils.isNotEmpty(single.originalFlag)){
                originalFlag = single.originalFlag;
            }
        }

        if (levelType == 1){
            //this.originalFlag = this.unit.originalFlag;
        }
        return originalFlag;
    }


    static getInstance(constructId, singleId, unitId,arg) {
        return new RcjHandleFactory(constructId, singleId, unitId,arg);
    }

    //跳过计算
    isSkip(field){
        if (ObjectUtils.isNotEmpty(this.conditionCalculatedField)){
            if (this.conditionCalculatedField.includes(field)){
                return false;
            }
            return true;
        }
    }

    initialize(exts) {
        if (!exts) {
            exts = this.exts;
        }
        const validateRules = {};
        Object.entries(exts).forEach(([key, rule]) => {
            const ruleRef = this.addRule(key, rule);
            if (ruleRef) {
                validateRules[key] = ruleRef;
            }
        });
    }
    getRules(method){
        switch (method) {
            case 1:
                getMethod1Rule(this);
                break;
            case 2:
                getMethod2Rule(this);
                break;
            case 3:
                getMethod3Rule(this)
                break;
            case 4:
                getMethod4Rule(this);
                break;
            default:
                getMethod1Rule(this);
                break;

        }
    }


    //如果当前为单项和工程项目级别的时候才会触发
    sumRcj(dependRcjList){

        //加载人材机数据
        let {levelType,kind} = this.arg;
        dependRcjList.forEach(k =>{
            this.arg.adjustMethod=this.getRcjAdjustMethod(levelType,k.rcj.kind);
            this.getRules(kind=="20"?k.adjustMethod:this.arg.adjustMethod);
            let loadRules = [];
            Object.keys(this.calculateRules).forEach(cloumn =>{
                this.rules[k.sequenceNbr+"_"+cloumn] = this.calculateRules[cloumn](k);
                loadRules.push(...this.rules);
            });
            this.addRulesAndInitialize(loadRules);
        });
    }




    getRcjAdjustMethod(rcj){
        let {constructId, singleId, unitId,kind} = rcj;
        // if (kind == 0){
        //     return 1;
        // }
        //levelType,this.rcj.kind
        // let obj = null;
        // if (levelType == 1){
        //     obj = PricingFileFindUtils.getProjectObjById(this.constructId);
        // }
        // if (levelType == 2){
        //     obj = PricingFileFindUtils.getSingleProject(this.constructId,this.singleId);
        // }
        // if (levelType == 3){
        //     obj = PricingFileFindUtils.getUnit(this.constructId,this.singleId,this.unitId);
        // }
        let obj = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        if (ObjectUtils.isEmpty(obj)){
            return ;
        }
        return JieSuanRcjStageUtils.rcjAdjustMethodType(obj,kind);
    }


    transform(field, value) {
        if (!isNaN(value) && !isNaN(parseFloat(value))){
            return NumberUtil.numberScale(parseFloat(value), (this.calculateField[field.split("_")[1]])||5);
        }
        return value;
    }
    prepare(rcj){
        //加载人材机数据
        let {levelType,kind} = this.arg;
        //this.arg.adjustMethod=this.getRcjAdjustMethod(levelType,this.rcj.kind);
        //this.rcj.adjustMethod=this.getRcjAdjustMethod(this.rcj);
        //rcj.adjustMethod=this.getRcjAdjustMethod(rcj);
        //this.getRules(kind=="20"?this.rcj.adjustMethod:this.arg.adjustMethod);
        let rcjList = [rcj]
        if (levelType == 1 || levelType == 2){
            rcjList.push(...this.rcjGroupMap.get(rcj))
        }
        let loadRules = {};
        rcjList.forEach(k =>{
            k.adjustMethod=this.getRcjAdjustMethod(k);
            this.getRules(k.adjustMethod);
            Object.keys(this.calculateRules).forEach(cloumn =>{
                this.rules[k.sequenceNbr+"_"+cloumn] = this.calculateRules[cloumn](k.sequenceNbr);
            });
            Object.assign(loadRules,this.rules)
            //loadRules.push(...this.rules);
        })
        this.rules = loadRules;
        this.addRulesAndInitialize(this.rules);
    }
    cupmute(){
        this.getRcjList();
        //循环计算赋值
        this.calculateRcjList.forEach(k =>{
            this.rcj = k;
            this.dependRcjList = this.rcjGroupMap.get(k);
            this.prepare(k);
            for (const cloumn in this.calculateField) {
                k[cloumn] = this.create(k.sequenceNbr+"_"+cloumn);
                if (["jieSuanPriceDifferencSum","settlementPriceDifferencInputTax"].includes(cloumn)){
                    if (ObjectUtils.isEmpty(this.arg.num) && this.arg.kind != 0){
                        let scopeList = JieSuanRcjStageUtils.rcjAdjustMethod(this.obj,k.kind,this.rcj.adjustMethod,this.rcj.kind);
                        if (ObjectUtils.isNotEmpty(scopeList)){
                            let num = 0;
                            for (let i = 0; i < scopeList.length; i++) {
                                this.arg.num = i+1;
                                num +=ObjectUtils.isEmpty(this.createNoCache(k.sequenceNbr+"_"+cloumn))?0:this.createNoCache(k.sequenceNbr+"_"+cloumn);
                            }
                            this.arg.num = null;
                            k[cloumn] =  num;
                        }

                    }
                    if ((this.arg.levelType == 1 || this.arg.levelType == 2) && "jieSuanPriceDifferencSum" == cloumn){
                        let num = 0;
                        this.dependRcjList.forEach(i =>{
                            this.rcj = i;
                            this.calculateSumFlag = true;
                            num = this.createNoCache(i.sequenceNbr+"_"+cloumn) +num;
                            this.calculateSumFlag = false;
                        })
                        k[cloumn] =  NumberUtil.numberScale(num,this.calculateField[cloumn]);
                    }

                }

            }
        });
        //人材机排序
        this.sortRcj();
        //排序
        if (!ObjectUtils.isEmpty(this.arg.sort) && !ObjectUtils.isEmpty(this.calculateRcjList)){
            let field = this.arg.sort.field;
            let order = this.arg.sort.order;
            /*if (order == "asc"){
                ts6.sort((a, b) => a[field] - b[field]);
            }else {
                ts6.sort((a, b) => b[field] - a[field]);
            }*/
            this.calculateRcjList = JieSuanDataUtils.rcjCollectSort(this.calculateRcjList,this.arg.sort)
        }


        return this.calculateRcjList;
    }
    freeCupmute(calculateRcjList){
        this.calculateRcjList = calculateRcjList;



        //加载人材机数据
        let {levelType,kind} = this.arg;
        let loadRules = {};
        this.calculateRcjList.forEach(k =>{
            k.adjustMethod=this.getRcjAdjustMethod(k);
            this.getRules(k.adjustMethod);
            Object.keys(this.calculateRules).forEach(cloumn =>{
                this.rules[k.sequenceNbr+"_"+cloumn] = this.calculateRules[cloumn](k.sequenceNbr);
            });
            Object.assign(loadRules,this.rules)
        })
        this.rules = loadRules;
        this.addRulesAndInitialize(this.rules);

        //循环计算赋值
        this.calculateRcjList.forEach(k =>{
            this.rcj = k;
            this.dependRcjList = [k];
            //this.prepare(k);
            for (const cloumn in this.calculateField) {
                k[cloumn] = this.create(k.sequenceNbr+"_"+cloumn);
                if (["jieSuanPriceDifferencSum","settlementPriceDifferencInputTax"].includes(cloumn)){
                    if (ObjectUtils.isEmpty(this.arg.num) && this.arg.kind != 0){
                        let scopeList = JieSuanRcjStageUtils.rcjAdjustMethod(this.obj,k.kind,this.rcj.adjustMethod,this.rcj.kind);
                        if (ObjectUtils.isNotEmpty(scopeList)){
                            let num = 0;
                            for (let i = 0; i < scopeList.length; i++) {
                                this.arg.num = i+1;
                                num +=this.createNoCache(k.sequenceNbr+"_"+cloumn);
                            }
                            this.arg.num = null;
                            k[cloumn] =  num;
                        }

                    }
                }

            }
        });
        //人材机排序
        return calculateRcjList;
    }
    createNoCache(field) {
        // 如果跳过计算返回0
        if (this.isSkip(field)) {
            return 0;
        }
        if (typeof  this.rules[field]=="function" ) {
            return this.values[field];
        }
        const rule = this.rules[field];
        if (!rule) {
            throw new Error(`规则字段没有找到: ${field}`);
        }
        const paramsObj = {};
        rule.params.forEach(paramKey => {
            paramsObj[paramKey] = this.createNoCache(paramKey);
        });
        return this.call(paramsObj, rule, field);
    }

    getRuntimeValue(cell, param){
        let {from:type, kind, column:cloumn}=cell
        let sequenceNbr = param.split("_")[0];
        let item = this.calculateRcjList.find(item => item.sequenceNbr == sequenceNbr);
        let value = 0;
        if (typeof cloumn == "function") {
            value = cloumn(this);
        } else {
            value = item[cloumn];
        }
        return value;
    }

    getCellValue({name,from,column}) {
        let value ="";
          switch (from) {
              case "page":{
                  if (typeof column =="function"){
                      value=  column(this);
                  }else {
                      value =this.page[column]||"";
                  }
              }
              case "rcj":{
                  if (typeof column =="function"){
                      value=  column(this);
                  }else {
                      value =this.rcj[column]||"";
                  }

              }
          }
        return value;
    }

    /**
     * 获取人材机汇总数据
     */
    getRcjList(){
        let {levelType,type} = this.arg;
        //let {constructProjectRcjs,rcjDetailList} = this.unit;
        let constructProjectRcjs = [];
        let rcjDetailList = [];
        let itemBillProjects = [];
        let measureProjectTables = [];
        let unitList = [];
        if (levelType == 1){
            unitList = PricingFileFindUtils.getUnitList(this.constructId);
            if (type == 1){
                unitList = unitList.filter(k =>k.originalFlag == true);
            }
            if (type == 2){
                unitList = unitList.filter(k =>k.originalFlag != true);
            }
        }
        if (levelType == 2){
            unitList = PricingFileFindUtils.getUnitListBySingle(this.constructId,this.singleId);
        }
        if (levelType == 3){
            let unit = PricingFileFindUtils.getUnit(this.constructId,this.singleId,this.unitId);
            unitList.push(unit);
        }

        unitList.forEach(k =>{
            constructProjectRcjs.push(...k.constructProjectRcjs);
            rcjDetailList.push(...k.rcjDetailList);
            itemBillProjects.push(...k.itemBillProjects.getAllNodes());
            measureProjectTables.push(...k.measureProjectTables.getAllNodes());

        })
        this.itemBillProjects = itemBillProjects;
        this.measureProjectTables = measureProjectTables;
        this.unitList = unitList;
        this.constructProjectRcjs = constructProjectRcjs;
        this.rcjDetailList = rcjDetailList;

        //材料父级
        let copyParentRcjs = ObjectUtil.cloneDeep(constructProjectRcjs);
        if (ObjectUtil.isEmpty(copyParentRcjs)) {
            return copyParentRcjs;
        }
        for (const rcj of copyParentRcjs) {
            //是否二次解析（如果二次解析需要将子级材料也填充进来）
            if (rcj.markSum === 1 && ObjectUtil.isNotEmpty(rcjDetailList)) {
                let childRcjs = rcjDetailList.filter(i => i.rcjId === rcj.sequenceNbr);
                copyParentRcjs.push(...ObjectUtil.cloneDeep(childRcjs));
            }
        }
        //根据类型过滤人材机数据
        copyParentRcjs = this.rcjTypeFilter(copyParentRcjs);

        copyParentRcjs.forEach(k =>{
            if (k.kind == 1){
                k.isDifference = true;
                //k.isGray = true;
                let constructProjectRcj = constructProjectRcjs.find(a =>a.sequenceNbr ==k.sequenceNbr);
                if (ObjectUtils.isNotEmpty(constructProjectRcj))constructProjectRcj.isDifference = true;
                let rcjDetailRcj = rcjDetailList.find(a =>a.sequenceNbr ==k.sequenceNbr);
                if (ObjectUtils.isNotEmpty(rcjDetailRcj))rcjDetailRcj.isDifference = true;
            }
            //JieSuanRcjStageUtils.getCostDeIdList(this.unit)
            if (JieSuanRcjStageUtils.getCostDeIdList(unitList).includes(k.deId)){
                k.isGray = true;
            }
            if (['QTCLFBFB', '34000001-2', 'J00004', 'J00031', 'J00031', 'C11384', 'C00007', 'C000200','C11408',"C11388","J00006","J00008"].includes(k.materialCode)){
                k.isGray = true;

            }




        })



        //根据 材料编码、类型、名称、规格型号、单位、合同预算价去重
        //分组
        this.rcjGroupMap =  this._getRcjGroup(copyParentRcjs,levelType);
        const keysIterator = this.rcjGroupMap.keys();
        this.calculateRcjList = Array.from(keysIterator);
        //return this.calculateRcjList;
    }


    /**
     * 人材机排序
     */
    sortRcj(){
        /**
         * 人材机排序规则：
         * 1. 整体【非置灰数据】排在【置灰数据】前面
         * 2.【 非置灰数据】内排序顺序：人工、材料、主材、机械
         * 3.【 置灰数据】内排序顺序：人工、材料、主材、机械、二次解析父级材料
         */
        let sortRcjList = new Array();
        //非置灰
        let noGray = this.calculateRcjList.filter(i => !i.isGray).sort((a, b) => {
            if (a.kind === b.kind) {
                return a.materialCode.localeCompare(b.materialCode)
            }
            return a.kind - b.kind;
        });

        //置灰
        let ercijiexi = this.calculateRcjList.filter(i => i.isGray).filter(i => i.markSum === 1 && (i.levelMark === 1 || i.levelMark === 2));//二次解析
        let feiercijiexi = this.calculateRcjList.filter(i => i.isGray).filter(i => !(i.markSum === 1 && (i.levelMark === 1 || i.levelMark === 2))).sort((a, b) => {
            return a.kind - b.kind;
        });//非二次解析
        sortRcjList.push(...noGray);
        sortRcjList.push(...feiercijiexi);
        sortRcjList.push(...ercijiexi);

        let rcjDetailList =this.rcjDetailList;


        if(!ObjectUtils.isEmpty(sortRcjList)){
            //暂估置灰
            for (let sortRcjListElement of sortRcjList) {
                // if (sortRcjListElement.ifProvisionalEstimate ==1 || sortRcjListElement.ifDonorMaterial  ==1){
                //     sortRcjListElement.isDifference = true;
                // }
                let find = rcjDetailList.find(k =>k.rcjId == sortRcjListElement.sequenceNbr);
                if (ObjectUtils.isNotEmpty(find)){
                    sortRcjListElement.isGray = true;
                }
                if (sortRcjListElement.outputToken != 1){
                    sortRcjListElement.outputToken =2;
                }
            }
        }
        //过滤 临时删除
        sortRcjList = sortRcjList.filter(i=>{
            if( !(!ObjectUtils.isEmpty(i.tempDeleteFlag) && i.tempDeleteFlag)){
                if (ObjectUtils.isEmpty(i.ifDonorMaterial)) {
                    i.ifDonorMaterial = 0;
                }
                return true;
            }
        });
        this.calculateRcjList = sortRcjList;
    }

    /**
     * 人材机类型过滤
     */
    rcjTypeFilter(rcjList){
        let {kind}=this.arg;
        let originalFlag = false;

        this.unitList.forEach(k=>{
            if (k.originalFlag){
                originalFlag = true;
            }
        });


        if (kind == 0)return rcjList;
        //根据类型筛选
        //   【材料调差】：所有类型为“材料”、“主材”、“设备”、“商砼”、“浆”、“商浆”、“配比”且是否调差为"是"、是否暂估为"否"且是否甲供为"否"的所有人材机数据行
        if (JieSuanRcjDifferenceEnum.CAILIAO.code == kind) {
            //人材机类型
            let rcjType = [2, 4, 5, 6, 8, 9, 10];
            rcjList = rcjList.filter(i => rcjType.includes(i.kind));
        } else if (JieSuanRcjDifferenceEnum.ZANGUJIA.code == kind) {
            // 【暂估价调差】所有是否暂估为"是"的人材机数据行 （包含暂估甲供材）；
            rcjList = rcjList.filter(i => i.ifProvisionalEstimate == 1 && i.ifDonorMaterial !=1);
        } else if (20 == kind) {
            //   【价差】
            rcjList = rcjList.filter(i => i.isDifference && !JieSuanRcjStageUtils.getCostDeIdList(this.unitList).includes(i.deId));
        } else if(21 != kind){
            //合同外
            rcjList = rcjList.filter(i => i.kind == kind&&!JieSuanRcjStageUtils.getCostDeIdList(this.unitList).includes(i.deId));
        }
        if (originalFlag) {
            if (JieSuanRcjDifferenceEnum.CAILIAO.code == kind) {
                //人材机类型
                rcjList = rcjList.filter(i => i.isDifference && i.ifProvisionalEstimate != 1 && i.ifDonorMaterial != 1);
            }
            //  【人工调差】：所有类型 为“人工”且是否调差为"是"、是否暂估为"否"、是否甲供为"否"的所有人材机数据行
            if (JieSuanRcjDifferenceEnum.RENGONG.code == kind) {
                //copyRcjs = copyRcjs.filter(i => i.isDifference && i.ifProvisionalEstimate != 1 && i.ifDonorMaterial != 1);
                rcjList = rcjList.filter(i=>!JieSuanRcjStageUtils.getCostDeIdList(this.unitList).includes(i.deId));
            }
            //  【机械调差】所有类型为“机械”且是否调差为"是"、是否暂估为"否"的所有人材机数据行；
            if (JieSuanRcjDifferenceEnum.JIXIE.code == kind) {
                rcjList = rcjList.filter(i => i.isDifference && i.ifProvisionalEstimate != 1);
            }
        }
        return rcjList;
    }

    /**
     * 人材机数组分组
     */
    _getRcjGroup(arr,levelType) {
        //拼接相同材料
        for (let arrayElement of arr) {
            arrayElement.tempcol =JieSuanRcjStageUtils.indexJoint(levelType,arrayElement,this.arg.kind,this.arg.type);
        }
        const map = new Map();
        arr.forEach((item) => {
            const keyValue = item["tempcol"];
            let foundKey = null;
            for (const [key] of map.entries()) {
                if (key["tempcol"] === keyValue) {
                    foundKey = key;
                    break;
                }
            }
            if (!foundKey) {
                map.set(item, []);
                foundKey = item;
            }
            map.get(foundKey).push(item);
        });
        return map;
    }

}
module.exports = {
    RcjHandleFactory: RcjHandleFactory
}