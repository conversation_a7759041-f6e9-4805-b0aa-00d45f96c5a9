/*
 * @Descripttion: 表格表头列显示隐藏处理
 * @Author: renmingming
 * @Date: 2024-03-04 14:49:02
 * @LastEditors: renmingming
 * @LastEditTime: 2024-03-20 17:58:24
 * vxe-table需要添加trends-table-column类名
 * 添加:header-cell-class-name="setHeaderCellClassName"
 */
import {
  ref
} from 'vue';
import { projectDetailStore } from '@/store/projectDetail';

const projectStore = projectDetailStore();
export const useJsFormatTableColumns = ({type= 'js'} = {}) => {
  let handlerColumns = ref([]);
  /**
   * 结算初始化
   * @param {*} param0
   */
  const initColumns = async ({columns}) => {
    // columns.reverse();
    handlerColumns.value=columns
  }
  /**
   * 保存更新
   * @param {*} checkedList
   */
  const updateColumns = (checkedList,oldData) => {
    let columnArr=[]
    let handleArr=JSON.parse(JSON.stringify(oldData))
    for(let i in handleArr){
      if(handleArr[i].field==='jsje'||handleArr[i].field==='rcjTz'){
        let childArr=handleArr[i].children
        let newTreeArr=JSON.parse(JSON.stringify(handleArr[i].children))
        let delChildNum=0
        for(let n in childArr){
          if(!checkedList.includes(childArr[n].field)){
            newTreeArr.splice(n-delChildNum, 1);
            delChildNum++
          }
        }
        // newTreeArr.reverse();
        handleArr[i].children = newTreeArr
        columnArr.push(handleArr[i])
      }else if(checkedList.includes(handleArr[i].field)){
        columnArr.push(handleArr[i])
      }
    }
    // columnArr.reverse();
    handlerColumns.value=columnArr
  }
  return {
    handlerColumns,
    initColumns,
    updateColumns
  };
};
