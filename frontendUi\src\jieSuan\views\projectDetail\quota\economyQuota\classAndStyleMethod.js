/*
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-07-03 14:46:02
 * @LastEditors: kong<PERSON>qiang
 * @LastEditTime: 2025-04-18 14:58:04
 */
// import { projectDetailStore } from '@/store/projectDetail';
// const projectStore = projectDetailStore();

export const customCell = ({ rowIndex, column , record:row }) => {
  let className = ''
  let style = {}
  if (['name'].includes(column.field)) {
    style = {
      textAlign: 'left'
    }
  }
  if (['name'].includes(column.field)) {
    className += ' code-color ' + `Virtual-pdLeft-s${row.customLevel} `;
  } else if (column.field === 'index') {
    className += ' index-bg';
  }
  if (['name'].includes(column.field) && row.kind !== '04') {
    const line = row.maxLine || 1;
    className += ` cell-line-break-${line}`;
  }
  return { style: style,class: className};
};
export const rowClassName = (row, index, data,originalFlag) => {
  let ClassStr = 'normal-info';
  if (row.kind === '0') {
    ClassStr = 'row-unit';
  } else if (row.kind === '01' || row.kind === '02') {
    ClassStr = 'row-sub';
  } else if (row.kind === '03') {
    ClassStr = 'row-qd';
  }
  if (row.color) {
    ClassStr += ' ' + row.color;
  }
  if (row.tempDeleteFlag) {
    ClassStr = 'temp-delete';
  }
  if ([94, 95].includes(row.kind)) {
    ClassStr += ' zcsb-color';
  }

  if (row.sequenceNbr == data[0]?.sequenceNbr) {
    ClassStr += ' first-row';
  }
  if(!((row.parentProjectId||row.originalFlag)&&!(row.markSum === 1 && (row.levelMark === 1 || row.levelMark === 2))&&row.isFyrcj!=0) ){
    ClassStr += ' no-edit';
  }
  ClassStr += originalFlag && !row.originalFlag
  ? ' original-data-bg'
  : '';
  return ClassStr;
};
export const customHeaderCell = column => {
  let className = { class: '' }
  if(column.editable) className.class+='fix-not-can-edit'
  if(['bdCode','fxCode'].includes(column.field)) className.class+='deCode-center'
  return className
};