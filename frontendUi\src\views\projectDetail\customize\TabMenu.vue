<!--
 * @Descripttion: tab顶部菜单
 * @Author: renmingming
 * @Date: 2023-05-17 10:10:08
 * @LastEditors: k<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-03-31 17:10:27
-->
<template>
  <div class="tab-menu">
    <a-tabs
      v-model:activeKey="activeKey"
      type="card"
      @change="tabsChange"
    >
      <a-tab-pane
        :key="tab.code"
        :tab="tab.value"
        v-for="tab in tabs"
      ></a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import csProject from '@/api/csProject';
import { onMounted, ref, watch, markRaw } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { proModelStore } from '@/store/proModel.js';
import { message, Modal } from 'ant-design-vue';
import infoMode from '@/plugins/infoMode.js';
import jiesuanApi from '@/api/jiesuanApi';
const store = projectDetailStore();
const ModelStore = proModelStore();
import operateList from './operate';
const tabs = ref([]);
import feePro from '../../../api/feePro';
const activeKey = ref(1);
let unifyData = ref(); //统一应用按钮是否禁用
import { setGlobalLoading } from '@/hooks/publicApiData';

const emit = defineEmits(['getActiveKey', 'getTitle']);

const authTab = ref([]); // 权限tab,单位工程，调用查询是否单位工程完善. 如果为空，则默认全部都可以查看，

const fefxActive = markRaw({
  activeKey: 1,
});

watch(
  () => [store.currentTreeInfo, store.currentTreeGroupInfo],
  () => {
    if (store.currentTreeInfo || store.currentTreeGroupInfo) {
      getMenuList();
    }
  }
);
const childPageStandard = ['分部分项', '措施项目', '人材机汇总'];
watch(
  () => store.standardGroupOpenInfo.isOpen,
  () => {
    console.log('进入store.standardGroupOpenInfo-tab', tabs.value);
    getFinallyTab();
  }
);
watch(
  () => store.updateSS,
  () => {
    if (store.updateSS) {
      activeKey.value = '4';
    }
  }
);
const getFinallyTab = () => {
  if (store.currentTreeInfo.levelType !== 3) return;
  if (store.standardGroupOpenInfo.isOpen) {
    tabs.value = storeTabsVal.value.filter(i =>
      childPageStandard.includes(i.value)
    );
    console.log('进入store.standardGroupOpenInfo-tab', tabs.value);
  } else {
    tabs.value = storeTabsVal.value;
  }
};
onMounted(() => {
  if (store.currentTreeInfo?.levelType) {
    getMenuList();
  }
});
let loading = ref(false);
let storeTabsVal = ref([]);
const getMenuList = async () => {
  const levelType = store.currentTreeInfo?.levelType;
  if (!levelType) {
    //levelType不存在不掉接口，这个是偶现，一次性调好多次
    return;
  }
  // 没有设置专业工程的，单位只能查看分部分项，工程项目只能查看项目概况
  if (levelType === 1 && !(await checkUnitOfPro())) {
    authTab.value = ['项目概况'];
  } else if (levelType === 3 && !(await checkUnit())) {
    authTab.value = ['分部分项'];
  }
  // else if (levelType === 3 && !(await checkUnit())) {
  //   authTab.value = ['造价分析'];
  // }
  else {
    authTab.value = [];
  }
  loading.value = true;

  try {
    const apiData = {
      type: 1,
      levelType: levelType,
    };
    const res = await csProject.getMenuList(apiData);
    if (res.status !== 200) {
      return;
    }
    console.info('上方tab数据：',res.result)
    if(levelType==1){
      res.result[0].value="项目概况"
    }
    if (store.type === 'jieSuan') {
      //结算单位工程无造价分析    单位/工程项目无取费表
      // debugger;
      let filterList = levelType === 3 ? ['取费表', '造价分析'] : ['取费表'];
      res.result = res.result.filter(i => !filterList.includes(i.value));
      res.result.forEach(item => {
        if (item.value === '人材机汇总') {
          item.value = '人材机调整';
        }
      });
      if(!store.currentTreeInfo?.originalFlag){
        res.result = res.result.filter(i => i.value !== '工程概况');
        activeKey.value = res.result[0].code;
        emit('getActiveKey',  res.result[0].code,  res.result[0].value);
        // emit('getActiveKey',  res.result[0].code,  res.result[0].value);
      }
      tabs.value = res.result;
      console.log('结算上方-tabs', tabs.value);
      console.log(activeKey.value,'activeKey.value',res.result)
    } else {
      if (
        levelType === 2 &&
        store.currentTreeInfo.parentId !==
          store.currentTreeGroupInfo.constructId
      ) {
        //非一级子单项不展示人材机汇总和取费表
        tabs.value = res.result.filter(a => !['2', '3'].includes(a.code));
      } else {
        tabs.value = res.result;
      }
    }
    storeTabsVal.value = res.result;
    getFinallyTab();
    let constructId = store.currentTreeGroupInfo?.constructId;
    const tabSelect = store.proCheckTab?.find(
      item => item.id === store.currentTreeInfo.id
    );
    let flag =
      store.type === 'ys' &&
      levelType === 2 &&
      store.currentTreeInfo?.parentId === constructId &&
      tabSelect; //一及子单项
    if (levelType !== 2 || flag) {
      // 如果单位不完整，则默认分部分项，看其他提示
      const tabSelectName = authTab.value.length
        ? authTab.value[0]
        : tabSelect && tabSelect.clickTab;
      let tabSelectList =
        tabSelectName === '项目概况'
          ? ['项目概况', '工程概况']
          : [tabSelectName];
      for (let item of tabs.value) {
        if (tabSelectList.includes(item.value)) {
          activeKey.value = item.code;
          emit('getActiveKey', item.code, item.value);
        }

        if (item.value === '分部分项' || item.value === '项目概况') {
          fefxActive.activeKey = item.code;
        }
      }
    } else {
      activeKey.value = '1';
      emit('getActiveKey', tabs.value[0].code, tabs.value[0].value);
    }
    if (store.updateSS) {
      // 修改送审 切换tab
      const key = getKeyByName(store.tabSelectName);
      if (key !== activeKey.value) {
        setTimeout(() => {
          emit('getActiveKey', key, store.tabSelectName);
        }, 1000);
      }
    }
    console.log('store.proCheckTab----------------', store.proCheckTab);
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.error(error);
  }
};
const checkUnitOfPro = async () => {
  try {
    const { id, levelType } = store.currentTreeInfo;
    if (levelType !== 1) return;
    const res = await csProject.getConstructUnitListTypeByConstructId({
      constructId: id,
    });
    ModelStore.proInfoModel = !res.result;
    return res.result;
  } catch (error) {
    return false;
  }
};
/**
 * 检查单位是否完整，是否有专业工程
 */
const checkUnit = async () => {
  try {
    const { id } = store.currentTreeInfo;
    let postData = {
      unitId: id,
    };
    const { singleId, constructId } = store.currentTreeGroupInfo;
    if (store.currentTreeGroupInfo) {
      postData = { ...postData, singleId, constructId };
    }
    if (store.currentTreeInfo.levelType === 2) return;
    // if (store.currentTreeInfo.levelType === 3 && (!singleId || !id)) return;
    const res = await csProject.getConstructMajorTypeByUnitId(postData);
    return !!res.result;
  } catch (error) {
    return false;
  }
};

const showInfo = () => {
  ModelStore.onInfoModal = true;
};
const saveHumanData = oldVal => {
  unifyData.value = operateList.value.find(
    item => item.name === store.humanUpdataData.name
  );
  let infoText =
    store.humanUpdataData.name === 'unify'
      ? '当前取费设置中存在费率或政策文件变更，是否统一修改并应用至所有单位工程？'
      : `人材机数据已修改，是否应用整个${
          store.currentTreeInfo.levelType === 1 ? '工程项目' : '单项工程'
        }?`;
  infoMode.show({
    isSureModal: false,
    iconType: 'icon-querenshanchu',
    infoText,
    confirm: () => {
      store.type === 'jieSuan'?jiesuanHumanSave(oldVal):
      store.humanUpdataData?.name === 'unify'
        ? feeTotalSave(oldVal)
        : humanSave(oldVal);
        
      infoMode.hide();
    },
    close: () => {
      if(store.type=="jieSuan"){
        jiesuanApi.cancelUnifiedApplication({constructId:store.currentTreeGroupInfo?.constructId})
      }
      unifyData.value.disabled = true;
      infoMode.hide();
      setTimeout(()=>{
        resetHumanData(oldVal,false);
      },500)
    },
  });
  // Modal.confirm({
  //   title: `${infoText}`,
  //   onOk() {
  //     store.humanUpdataData.name === 'unify'
  //       ? feeTotalSave(oldVal)
  //       : humanSave(oldVal);
  //   },
  //   onCancel() {
  //     resetHumanData(oldVal);
  //     unifyData.value.disabled = true;
  //   },
  // });
};
const getParamsData = data => {
  let apiData = { ...data };
  apiData.constructId =
    store.currentTreeInfo.levelType === 1
      ? store.currentTreeInfo?.id
      : store.currentTreeGroupInfo?.constructId;
  if (store.currentTreeInfo.levelType === 2) {
    apiData.singleId = store.currentTreeInfo?.id; //单项ID
  }
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  return apiData;
};
const jiesuanHumanSave = async (oldVal) => {
  let apiData = {
    levelType: store.currentTreeInfo.levelType,
    constructId: store.currentTreeGroupInfo?.constructId
  };
  jiesuanApi.unifyUse(apiData).then((res) => {
    console.log("统一应用接口返回结果", res);
    resetHumanData(oldVal);
    unifyData.value.disabled = true;
  });
}
const humanSave = async oldVal => {
  if (!store.humanUpdataData) {
    return;
  }
  setGlobalLoading(true, '统一应用中，请稍后...');
  if (!store.humanUpdataData?.unitIdList) store.humanUpdataData.unitIdList = [];
  let postData = getParamsData({});
  if (store.humanUpdataData.adjustFactor?.isEdit) {
    let apiData = {
      ...postData,
      coefficient: store.humanUpdataData.adjustFactor.marcketFactor * 1,
      rcjList: JSON.parse(
        JSON.stringify(store.humanUpdataData.adjustFactor.selectRows)
      ),
      unitIdList: store.humanUpdataData.unitIdList?[...store.humanUpdataData.unitIdList]:[],
    };
    await csProject.constructAdjustmentCoefficient(apiData).then(res => {
      console.log('统一应用系数', res);
    });
  }
  if (store.humanUpdataData.updataData) {
    setGlobalLoading(true);
    let apiData = {
      ...postData,
      constructProjectRcjList: JSON.parse(
        JSON.stringify(store.humanUpdataData.updataData)
      ),
      unitIdList: store.humanUpdataData.unitIdList?[...store.humanUpdataData.unitIdList]:[],
    };
    let apiFunName =
      store.currentTreeInfo.levelType === 1
        ? 'changeRcjConstructProject'
        : 'changeRcjSingleProject';
    await csProject[apiFunName](apiData).then(res => {
      console.log('统一应用接口返回结果', res);
    });
  }
  if (store.humanUpdataData.sourcePriceData) {
    let apiData = {
      ...postData,
      constructProjectRcjList: JSON.parse(
        JSON.stringify(store.humanUpdataData.sourcePriceData)
      ),
      unitIdList: store.humanUpdataData.unitIdList?[...store.humanUpdataData.unitIdList]:[],
    };
    console.log('统一应用接口参数', apiData);
    await feePro.rcjFromUnitUpdate(apiData).then(res => {
      console.log('统一应用接口返回结果', res);
    });
  }
  if (store.humanUpdataData.rcjwjcList?.length > 0) {
    let apiData = {
      ...postData,
      excludeRcjIdList: [...store.humanUpdataData.rcjwjcList],
    };
    let apiFunName =
      store.currentTreeInfo.levelType === 2
        ? 'singleRcjWjc'
        : 'constructRcjWjc';
    console.log('统一应用接口参数', apiData);
    await feePro[apiFunName](apiData).then(res => {
      console.log('统一应用接口返回结果', res);
    });
  }
  resetHumanData(oldVal);
  unifyData.value.disabled = true;
  setGlobalLoading(false);
};
const feeTotalSave = async oldVal => {
  setGlobalLoading(true, '统一应用中，请稍后...');
  // debugger;
  console.log(store.humanUpdataData.updataData);
  if (store.humanUpdataData.updataData.policy) {
    await feePro
      .checkPolicyDocument(
        JSON.parse(JSON.stringify(store.humanUpdataData.updataData.policy))
      )
      .then(res => {});
  }
  if (store.humanUpdataData.updataData.feeTotal) {
    await feePro
      .unifiedUse(
        JSON.parse(JSON.stringify(store.humanUpdataData.updataData.feeTotal))
      )
      .then(res => {});
  }
  if (store.humanUpdataData.updataData.calEditData) {
    let apiData = {
      constructId:
        store.currentTreeInfo.levelType === 1
          ? store.currentTreeInfo?.id
          : store.currentTreeGroupInfo?.constructId,
      feeCalculateBaseList: JSON.parse(
        JSON.stringify(store.humanUpdataData.updataData.calEditData)
      ),
    };
    await feePro.updateProjectUnitCalculateBaseApply(apiData).then(res => {
      if (res.status === 200) {
        console.log('updateProjectUnitCalculateBaseApply3', res, apiData);
      }
    });
  }
  setGlobalLoading(false, '统一应用中，请稍后...');
  resetHumanData(oldVal);
  unifyData.value.disabled = true;
};
const resetHumanData = (oldVal,type=true) => {
  store.SET_HUMAN_UPDATA_DATA(null);
  tabsChange(oldVal,type);
  activeKey.value = oldVal;
};
const tabsChange = async (val,type=true) => {
  if (store.humanUpdataData && store.humanUpdataData.isEdit) {
    activeKey.value = '4';
    saveHumanData(val);
    return;
  }
  if (store.isOpenIndexModal.open) {
    console.log('tabs', tabs.value, store.isOpenIndexModal);
    let oldTab =
      store.isOpenIndexModal.tab === 'fbfx' ? '分部分项' : '措施项目';
    activeKey.value = tabs.value.find(i => i.value === oldTab).code;
    return;
  }
  const selectTabName = tabs.value.filter(item => item.code === val)[0].value;
  if(store.type === 'jieSuan'&&store.tabSelectName=='人材机调整'&&selectTabName!='人材机调整'&&type){
    jiesuanApi.proJectBackups({constructId:store.currentTreeGroupInfo?.constructId,isFlag:false})
  }
  await checkUnitOfPro();
  // 没有设置单位专业的，不能查看其他的
  if (authTab.value.length && !authTab.value.includes(selectTabName)) {
    showInfo();
    activeKey.value = fefxActive.activeKey;
    return;
  }
  emit('getActiveKey', val, selectTabName);
  //切换tab栏更新保存的左侧树对应的tab
  let list = [...store.proCheckTab];
  list.map(item => {
    if (item.id === store.currentTreeInfo.id) {
      item.clickTab = selectTabName;
    }
  });
  store.SET_PRO_CHECK_TAB(list);
  tabs.value.forEach(item => {
    if (item.code === activeKey) {
      emit('getTitle', item.value);
    }
  });
  console.log('切换tab----change', list);
};

const tabChangeByValue = (val, callBack = null) => {
  console.log('val66666666', val);
  if (loading.value) {
    setTimeout(() => {
      tabChangeByValue(val, callBack);
    }, 1000);
    return;
  }
  const selectTab = tabs.value.find(item => {
    console.log('item333333333', item, val);
    return item.value === val;
  });
  if (!selectTab) {
    return;
  }
  activeKey.value = selectTab.code;
  tabsChange(selectTab.code);
  if (callBack) callBack();
};
/**
 * 根据名称获取codeKey
 * @param {*} val
 */
const getKeyByName = val => {
  const selectTab = tabs.value.find(item => {
    console.log('item', item, val);
    return item.value === val;
  });
  return selectTab?.code;
};

defineExpose({
  tabChangeByValue,
  getKeyByName,
});
</script>
<style lang="scss" scoped>
.tab-menu :deep(.ant-tabs) {
  height: var(--project-detail-main-content-tabs-menu-height);
  position: relative;
  top: 1px;
  box-sizing: border-box;
  font-size: 12px;
  .ant-tabs-nav {
    margin-bottom: 0;
    // padding: 0 11px;
    height: 100%;
    .ant-tabs-tab {
      padding: 5px 20px;
      border: none;
      border-radius: 4px;
      background-color: transparent;
      font-size: 12px;
      border-right: 1px solid #d6d6d6;
      margin-left: 0;
    }
    .ant-tabs-nav-more {
      display: none !important;
    }
    .ant-tabs-tab-active {
      background-color: #deeaff;
      .ant-tabs-tab-btn {
        color: #333333;
      }
    }
  }
}
</style>
