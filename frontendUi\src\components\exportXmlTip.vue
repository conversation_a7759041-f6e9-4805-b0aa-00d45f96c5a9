<!--
 * @Descripttion: 导出xml不符合层级限制弹框
 * @Author: sunchen
 * @Date: 2023-12-28 17:33:47
 * @LastEditors: wangru
 * @LastEditTime: 2025-04-18 09:36:59
-->
<template>
  <common-modal
    className="dialog-comm"
    title="提示"
    width="150px"
    v-model:modelValue="tipsModal"
  >
    <div class="tipsClass">
      <icon-font
        class="icon"
        type="icon-tishineirong"
        style="font-size: 30px;"
      />
      <span style="margin-left:15px;">
        提示：电子招投标系统要求项目结构为三级或三级以上，当前工程项目结构无法导入系统，请重新修改后导出
      </span>
    </div>
    <div class="footer-btn-list">
      <a-button
        @click="tipsModal=false"
        style="float:right"
      >返回修改</a-button>

    </div>
  </common-modal>
</template>
<script setup>
import { ref } from 'vue';
import { constructLevelTreeStructureList } from '@/api/csProject';
import { message } from 'ant-design-vue';
import { useRoute } from 'vue-router';
const route = useRoute();

let tipsModal = ref(false);
const getFlag = async () => {
  let list = null;
  let res = null;
  let flag = true;
  res = await constructLevelTreeStructureList(route.query.constructSequenceNbr);
  console.log('🚀 ~ file: exportFile.vue:69 ~ getTreeList ~ res:', res.result);
  if (res.status === 200) {
    list =
      res.result &&
      res.result.map(a => {
        return a.levelType;
      });
    let levelList = Array.from(new Set(list));
    if (levelList?.length < 3) flag = false;
  } else {
    message.error(res.message);
    flag = false;
  }
  return flag;
};
const open = () => {
  tipsModal.value = true;
};
defineExpose({
  open,
  getFlag,
});
</script>

<style lang="scss" scoped>
.tipsClass {
  margin-bottom: 20px;
  display: flex;
  font-size: 14px;
  justify-content: space-between;
}
</style>
