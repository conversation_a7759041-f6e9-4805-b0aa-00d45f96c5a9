<!--
 * @Descripttion: 分期工程量明细
 * @Author: liuxia
 * @Date: 2024-03-06 19:45:41
 * @LastEditors: liuxia
 * @LastEditTime: 2024-07-17 19:27:23
-->
<template>
  <div class="stage-quantities-table">
    <div
      class="head"
      v-show="
        props.currentInfo?.kind !== '0' &&
        props.currentInfo?.kind !== '01' &&
        props.currentInfo?.kind !== '02'
      "
    >
      <vxe-select
        v-model="stageType"
        :transfer="true"
        @change="rcjStageSwitch"
        :disabled="
          !isDisabled ||
          props.currentInfo?.kind === '04' ||
          !isCanStageAdjustment()
        "
      >
        <vxe-option
          v-for="item of optionList"
          :key="item.code"
          :value="item.code"
          :label="item.name"
        ></vxe-option>
      </vxe-select>
      <a-button
        type="text"
        :disabled="
          props.currentInfo.stageType === 2 ||
          !isDisabled ||
          props.currentInfo?.kind === '04' ||
          !isCanStageAdjustment()
        "
        @click="updateTypeVisible = true"
        >分期比例应用到其他</a-button
      >
    </div>
    <div class="content">
      <vxe-grid ref="vexTable" v-bind="gridOptions" v-on="gridEvents">
        <template #stageRatio_edit="{ row }">
          <vxe-input
            v-if="isCanStageAdjustment() && isDisabled"
            v-model="row.stageRatio"
            type="text"
            placeholder="请输入分期比例"
            @keyup="row.stageRatio = stageNumberFormat(row.stageRatio)"
          ></vxe-input>
          <span v-else>{{ row.stageRatio }}</span>
        </template>
        <template #stageQuantity_edit="{ row }">
          <vxe-input
            v-if="isCanStageAdjustment() && isDisabled"
            v-model="row.stageQuantity"
            type="text"
            placeholder="请输入分期量"
          ></vxe-input>
          <span v-else>{{ row.stageQuantity }}</span>
        </template>
      </vxe-grid>
    </div>
    <common-modal
      title="分期比例应用到"
      width="600"
      height="200"
      className="dialog-comm"
      v-model:modelValue="updateTypeVisible"
    >
      <a-radio-group v-model:value="useType" style="width: 100%">
        <p class="radioP">
          <a-radio :value="1">应用到当前分部</a-radio>
        </p>
        <p class="radioP">
          <a-radio :value="2">应用到分部分项所有清单</a-radio>
        </p>
      </a-radio-group>

      <div class="footer-btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="stageRatioBatchUse">确定</a-button>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import api from '@/api/jiesuanApi';
import { isNumericExpression, everyNumericHandler } from '@/utils/index';
import { message } from 'ant-design-vue';
import { reactive, ref, watch, nextTick, computed } from 'vue';
import { projectDetailStore } from '@/store/projectDetail.js';
import infoMode from '@/plugins/infoMode.js';
import { useCellClick } from '@/hooks/useCellClick';
const { useCellClickEvent, selectedClassName, cellBeforeEditMethod } =
  useCellClick();
const props = defineProps(['type', 'currentInfo']);
const emits = defineEmits(['updateData']);
const projectStore = projectDetailStore();

const originalData = ref([]);
let infoVisible = ref(false);
let infoText = ref('计算式输入非法，请重新输入标准四则运算表达式或数值');
let isSureModal = ref(false);
let vexTable = ref();
let currentInfo = ref();
let copyObj = ref(); // 复制数据
let loading = ref(false); // 数据加载loading
let isEditEnabled = ref(true); // 是否可编辑行
const stageType = ref(props.currentInfo.stageType); // 分期方式
const updateTypeVisible = ref(false); // 分期比例应用弹框是否显示
const useType = ref(); // 分期比例应用到  1 应用到当前分部   2 应用到分部分项下所有清单
const optionList = reactive([
  {
    name: '按分期比例输入',
    code: 1,
  },
  {
    name: '按分期工程量输入',
    code: 2,
  },
]);
const columnList1 = [
  {
    field: 'stage',
    title: '分期',
    minWidth: 60,
  },
  {
    field: 'stageRatio',
    title: '分期比例',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'stageRatio_edit' },
  },
  {
    field: 'stageQuantity',
    title: '结果',
    minWidth: 80,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
];
const columnList2 = [
  {
    field: 'stage',
    title: '分期',
    minWidth: 60,
  },
  {
    field: 'stageQuantity',
    title: '分期量',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'stageQuantity_edit' },
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
];

watch(
  () => props.currentInfo,
  () => {
    nextTick(() => {
      if (props.currentInfo) {
        stageType.value = props.currentInfo.stageType;
        setEditEnabled();
        gridOptions.value.columns =
          props.currentInfo?.stageType === 1 ? columnList1 : columnList2;
        gridOptions.value.data = props.currentInfo.stageQuantitiesList;
      }
    });
  },
  { deep: true }
);
let isDisabled = computed(
  () =>
    !projectStore.checkCgZsIdList.includes(props.currentInfo.standardId) &&
    ((props.type === 2 &&
      (props.currentInfo.zjcsClassCode === null ||
        props.currentInfo.zjcsClassCode === '' ||
        typeof props.currentInfo.zjcsClassCode == 'undefined')) ||
      props.type === 1)
);

// 选中单条分部分项数据
const currentChangeEvent = ({ row }) => {
  currentInfo.value = row;
  setEditEnabled();
};

const rcjStageSwitch = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    qdId: props.currentInfo.sequenceNbr,
    stageType: stageType.value,
  };
  api.rcjStageSwitch(apiData).then(res => {
    console.log('人材机分期方式切换', res);
    if (res.status === 200 && res.result) {
      message.success('人材机分期方式切换成功');
      emits('updateData', 1);
      gridOptions.value.columns =
        props.currentInfo?.stageType === 1 ? columnList1 : columnList2;
    }
  });
};

const stageRatioBatchUse = () => {
  if (!useType.value) {
    message.error('请选择应用范围');
    return;
  }
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    useType: useType.value,
    qdId: props.currentInfo.sequenceNbr,
  };
  console.log('应用范围参数', apiData);
  api.stageRatioBatchUse(apiData).then(res => {
    console.log('=========', res);
    if (res.status === 200 && res.result) {
      message.success('分期比例应用成功');
      useType.value = null;
      updateTypeVisible.value = false;
      emits('updateData', 1);
    }
  });
};

const cancel = () => {
  updateTypeVisible.value = false;
  useType.value = null;
};

const qdRcjStageUpdate = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    qdId: props.currentInfo.sequenceNbr,
    stageQuantitiesList: JSON.parse(
      JSON.stringify(props.currentInfo.stageQuantitiesList)
    ),
  };
  console.log('apiData', apiData);
  api.qdRcjStageUpdate(apiData).then(res => {
    console.log('res111111111', res);
    if (res.status === 200 && res.result) {
      message.success('修改成功');
      emits('updateData', 1);
      gridOptions.value.data = props.currentInfo.stageQuantitiesList;
    }
  });
};

const editClosedEvent = ({ row, column }) => {
  const $table = vexTable.value;
  if (!$table.isUpdateByRow(row, column.field)) return;
  
  const toNumber = (value) => {
    if (value === null || value === undefined) return 0;
    return Number(value) || 0;
  };

  if (props.currentInfo.stageType === 1) {
    // 保存原始第一期比例用于比较
    const originalFirstRatio = toNumber(props.currentInfo.stageQuantitiesList[0].stageRatio);
    
    props.currentInfo.stageQuantitiesList.forEach(item => {
      item.stageRatio = toNumber(item.stageRatio);
    });

    const updateIndex = props.currentInfo.stageQuantitiesList.findIndex(
      child => child.stage === row.stage
    );
    
    const total = props.currentInfo.stageQuantitiesList.reduce(
      (sum, item) => sum + toNumber(item.stageRatio), 0
    );
    
    if (total !== 100) {
      const diff = total - 100;
      const isIncrease = diff > 0;
      
      if (isIncrease) {
        let remainingDiff = Math.abs(diff);
        let nextIndex = updateIndex + 1;
        
        while (remainingDiff > 0 && nextIndex < props.currentInfo.stageQuantitiesList.length) {
          const nextItem = props.currentInfo.stageQuantitiesList[nextIndex];
          const currentRatio = toNumber(nextItem.stageRatio);
          
          if (currentRatio > 0) {
            const deductAmount = Math.min(currentRatio, remainingDiff);
            nextItem.stageRatio = currentRatio - deductAmount;
            remainingDiff -= deductAmount;
          }
          nextIndex++;
        }
        
        if (remainingDiff > 0) {
          for (let i = 0; i < props.currentInfo.stageQuantitiesList.length && remainingDiff > 0; i++) {
            if (i !== updateIndex) {
              const currentRatio = toNumber(props.currentInfo.stageQuantitiesList[i].stageRatio);
              if (currentRatio > 0) {
                const deductAmount = Math.min(currentRatio, remainingDiff);
                props.currentInfo.stageQuantitiesList[i].stageRatio = currentRatio - deductAmount;
                remainingDiff -= deductAmount;
              }
            }
          }
        }
      } else {
        const nextIndex = updateIndex + 1;
        const adjustValue = Math.abs(diff);
        if (nextIndex < props.currentInfo.stageQuantitiesList.length) {
          props.currentInfo.stageQuantitiesList[nextIndex].stageRatio = 
            toNumber(props.currentInfo.stageQuantitiesList[nextIndex].stageRatio) + adjustValue;
        } else {
          props.currentInfo.stageQuantitiesList[0].stageRatio = 
            toNumber(props.currentInfo.stageQuantitiesList[0].stageRatio) + adjustValue;
        }
      }

      // 检测第一期比例是否变化
      const currentFirstRatio = toNumber(props.currentInfo.stageQuantitiesList[0].stageRatio);
      if (currentFirstRatio !== originalFirstRatio) {
        return infoMode.show({
          iconType: 'icon-qiangtixing',
          infoText: '修改导致第一期比例变化，是否确认修改?',
          confirm: () => {
            qdRcjStageUpdate();
            infoMode.hide();
          },
          close: () => {
            $table.revertData();
            $table.reloadData(props.currentInfo.stageQuantitiesList);
            infoMode.hide();
          },
        });
      }
    }
  }
  qdRcjStageUpdate();
};

// 是否编辑处理
const setEditEnabled = () => {
  if (props.currentInfo.isLocked) {
    isEditEnabled.value = false;
    return;
  } else if (
    props.currentInfo.kind === '04' &&
    props.currentInfo.originalFlag
  ) {
    isEditEnabled.value = false;
    return;
  }
  isEditEnabled.value = true;
};

const gridOptions = ref({
  align: 'center',
  loading: loading.value,
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
    keyField: 'sequenceNbr',
  },
  editConfig: {
    trigger: 'click',
    mode: 'cell',
  },
  data: props.currentInfo.stageQuantitiesList,
  height: 'auto',
  keepSource: true,
  class: 'table-edit-common',
  cellClassName: selectedClassName,
  columns: props.currentInfo?.stageType === 1 ? columnList1 : columnList2,
});

const gridEvents = ref({
  editClosed: editClosedEvent,
  cellClick: useCellClickEvent,
  currentChange: currentChangeEvent,
});

const isCanStageAdjustment = () => {
  // 结算方式实际发生3、可调措施1
  // 并且总价措施、单价措施下可应用分期调整
  // zjcsClassCode: '0'安文费 没有值单价措施，有值不等于总价措施
  return props.currentInfo.settlementMethodValue
    ? [1, 3].includes(props.currentInfo.settlementMethodValue) &&
        props.currentInfo.zjcsClassCode !== '0'
    : true;
};

/**
 * 校验比例是否合法.
 * @param {string|number} value
 * @returns {string}
 */
const stageNumberFormat = value => {
  if (value === null || value === undefined) return '';
  
  // 转换为字符串处理
  const strValue = String(value);
  
  // 检查数值范围
  if (Number(strValue) > 100) {
    return '';
  }
  
  // 使用正则匹配有效数字格式
  const matched = strValue.match(/^\d{0,3}(\.\d{0,2})?$|^100$/);
  return matched ? matched[0] : '';
};
</script>

<style lang="scss" scoped>
.stage-quantities-table {
  height: 100%;
  .content {
    height: calc(100% - 40px);
  }
}
.radioP {
  margin-bottom: 10px;
}
</style>
