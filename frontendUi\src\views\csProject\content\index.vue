<template>
  <div class="content">
    <div class="content-item w60 h60">
      <div class="header">
        <p>
          <img
            class="header-icon"
            :src="getUrl('newCsProject/content-near.png')"
            alt=""
          />最近使用项目
        </p>
        <!-- <p
          class="herf"
          @click="getAllProjectList"
        >查看所有</p> -->
      </div>
      <div class="center">
        <a-empty
          v-if="projectContent.recentProList?.length === 0"
          :imageStyle="{ width: '271px', height: '100%', margin: '0 auto' }"
          :image="getUrl('newCsProject/none.png')"
          description=""
        />
        <p
          v-show="projectContent.recentProList?.length > 0"
          v-for="item in projectContent.recentProList"
          :key="item.id"
          @click="runProDetail(item)"
        >
          <span class="text"><img
              :src="getFileOpenByPath(item.type)"
              alt=""
            />{{
              item.fileName
            }}</span>
          <span class="time">{{ item.openTime }}</span>
        </p>
      </div>
    </div>
    <div class="content-item w40 h60">
      <div class="header">
        <p>
          <img
            class="header-icon"
            :src="getUrl('newCsProject/content-price.png')"
            alt=""
          />信息价动态
        </p>
        <p
          class="herf"
          @click="
            runDetail(single, 'https://www.yunsuanfang.com/materialPrice')
          "
        >
          更多
        </p>
      </div>
      <div class="center">
        <p
          v-for="item in projectContent.industryInfo"
          :key="item.id"
        >
          <span>
            <img
              style="width: 16px"
              :src="getUrl('newCsProject/msg.png')"
              alt=""
            />
            <span @click="runDetail('item', item)">{{ item.title }}</span>
          </span>
        </p>
      </div>
    </div>
    <div class="content-item w60 h40">
      <div class="header">
        <p>
          <img
            class="header-icon"
            :src="getUrl('newCsProject/content-help.png')"
            alt=""
          />帮助中心
        </p>
        <!-- <p class="herf">更多</p> -->
      </div>
      <div class="center">
        <div class="help">
          <div
            class="help-item"
            v-for="item in helpList.filter(a => a.isShow)"
            :key="item.name"
            @click="runHelpInfo('help', item)"
          >
            <div class="help-item-img">
              <img
                class="help-item-img-img1"
                :src="item.img"
                alt=""
              />
              <img
                class="help-item-img-img2"
                :src="item.isPdf"
                alt=""
                v-if="item.isPdf"
              />
              <icon-font
                v-if="item.hasCircle"
                type="icon-bofang"
                class="circle"
              ></icon-font>
            </div>
            <div class="help-item-title">{{ item.name }}</div>
            <div class="play"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-item w40 h40">
      <div
        class="content-item-img"
        @click="runHelpInfo('img')"
      >
        <div class="head-operate">
          <operateList></operateList>
        </div>
        <div
          class="img"
          :style="imgShow"
        ></div>
      </div>
    </div>
    <common-modal
      className="dialog-comm"
      v-model:modelValue="allProVisible"
      title="项目列表"
      width="1000"
      height="700"
      @close="cancel"
    >
      <allProModelContent></allProModelContent>
    </common-modal>
    <common-modal
      className="dialog-comm"
      v-model:modelValue="videoModal"
      title="视频学习"
      width="700"
      height="500"
      @close="closeVedio"
      :isNoClose="chickItem"
      show-zoom
    >
      <!-- 视频列表 -->
      <div
        class="videoContent"
        v-if="!chickItem"
      >
        <div
          class="videoContent-item"
          v-for="item in videoList"
          :key="item.name"
          @click="runVideo(item.url)"
        >
          <div class="videoContent-item-img">
            <img
              :src="item.img"
              alt=""
            />
          </div>
          <div>{{ item.name }}</div>
          <div class="play"></div>
        </div>
      </div>
      <div
        class="itemVedio"
        v-if="chickItem"
      >
        <iframe
          :src="videoUrl"
          width="100%"
          height="98%"
        ></iframe>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import { onMounted, reactive, getCurrentInstance, watch, ref } from 'vue';
import csProject from '../../../api/csProject';
import allProModelContent from '@/components/SelfModel/allProModelContent.vue';
import { getUrl } from '@/utils/index';
import { useRouter } from 'vue-router';
import { proModelStore } from '@/store/proModel';
import { ipc } from '@/utils/ipcRenderer';
import { message } from 'ant-design-vue';
const { shell } = require('electron');
import { ipcApiRoute } from '../../../api/main';
import { getPeriodical, getmateriallist } from '@/api/auth';
import * as aes from '@/utils/aes/public.js';
import loginModal from '../header/login-modal.vue';
import operateList from './operateList.vue';
import infoMode from '@/plugins/infoMode.js';
const globalProperties = getCurrentInstance().appContext.config.globalProperties; // 获取全局挂载
const $ipc = globalProperties.$ipc;
const workUrl = getUrl('document.png');
const store = proModelStore();
const router = useRouter();
const emits = defineEmits(['openPro']);
let allProVisible = ref(false); //线上项目弹框visibile
const imgShow = {
  backgroundImage: 'url(' + getUrl('gchdImg.jpg') + ')',
  backgroundSize: '100% 100%',
};
const helpList = [
  {
    img: getUrl('newCsProject/help-img1.png'),
    name: '云算房操作详解',
    id: 1,
    ossUrl:
      'https://ysf.oss.yunsuanfang.com/publics/operation_manual/%E4%BA%91%E7%AE%97%E6%88%BF%E5%B9%B3%E5%8F%B0%E7%94%A8%E6%88%B7%E6%93%8D%E4%BD%9C%E6%89%8B%E5%86%8C.pdf',
    isShow: false,
    hasCircle: true,
  },
  {
    img: getUrl('newCsProject/help-img2.png'),
    isShow: false,
    hasCircle: true,
    id: 2,
    name: '云算房功能介绍',
  },
  {
    img: getUrl('newCsProject/help-img3.png'),
    isShow: false,
    hasCircle: true,
    id: 3,
    name: '云算房产品宣传',
  },
  {
    img: getUrl('newCsProject/gsOperate.png'),
    name: '概算操作手册',
    isShow: true,
    id: 4,
    ossUrl:
      'https://ysf.oss.yunsuanfang.com/publics/operation_manual/%E8%AE%A1%E4%BB%B7%E8%BD%AF%E4%BB%B6%E6%93%8D%E4%BD%9C%E6%89%8B%E5%86%8C-%E6%A6%82%E7%AE%97.pdf',
    isPdf: getUrl('newCsProject/pdfOperate.png'),
    url: 'https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/YZJXPT/video/video1.mp4',
  },
  {
    img: getUrl('newCsProject/ysOperate.png'),
    name: '预算操作手册',
    isShow: true,
    ossUrl:
      'https://ysf.oss.yunsuanfang.com/publics/operation_manual/%E8%AE%A1%E4%BB%B7%E8%BD%AF%E4%BB%B6%E6%93%8D%E4%BD%9C%E6%89%8B%E5%86%8C-%E9%A2%84%E7%AE%97.pdf',
    isPdf: getUrl('newCsProject/pdfOperate.png'),
    id: 5,
    url: 'https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/YZJXPT/video/video1.mp4',
  },
];
const videoList = [
  {
    img: getUrl('newCsProject/help-img1.png'),
    name: '云算房操作详解',
    id: 1,
    url: 'https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/YZJXPT/video/video1.mp4',
  },
  {
    img: getUrl('newCsProject/help-img2.png'),
    id: 2,
    url: 'https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/YZJXPT/video/video2.mp4',
    name: '云算房功能介绍',
  },
  {
    img: getUrl('newCsProject/help-img3.png'),
    id: 3,
    url: 'https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/YZJXPT/video/video3.mp4',
    name: '云算房产品宣传',
  },
];
const projectContent = reactive({
  recentProList: [],
  industryInfo: [
    {
      title: '信息价查询',
      id: 1,
      url: 'https://www.yunsuanfang.com/materialPrice',
    },
  ],
});

const getIconUrl = ({ type }) => {
  if (!type && type !== 0) {
    return workUrl;
  }
  return getUrl(`document-icon${type}.png`);
};
onMounted(() => {
  // getRecentlyProgectList();
  getInfoList();
});
const getInfoList = () => {
  csProject.getSfbPeriodical({}).then(res => {
    if (res.status === 200) {
      const newdata = JSON.parse(aes.decrypt(res.result));
      console.log('getInfoList', newdata);
      projectContent.industryInfo = newdata;
    }
  });
  // getPeriodical(code).then(res => {
  //   console.log('getInfoList', res);
  //   projectContent.industryInfo = res;
  //   if (res.status === 200) {
  //     const newdata = JSON.parse(aes.decrypt(res.result));
  //     console.log('getInfoList', res);
  //   }
  // });
};
watch(
  () => store.isRefresh,
  newVal => {
    // if (newVal) {
      // store.SET_Refresh(false);
      getRecentlyProgectList();
    // }
  }
);
watch(
  () => store.isClickOpen,
  newVal => {
    if (newVal) {
      getAllProjectList();
    }
  }
);
const getRecentlyProgectList = () => {
  $ipc.invoke(ipcApiRoute.recentlyOpenedProjectList).then(response => {
    console.log('最近打开项目列表', response);
    if (response.status === 200) {
      projectContent.recentProList =
        response.result &&
        response.result.map(item => {
          const list = item.path.split('\\');
          item.fileName = list[list.length - 1];
          return item;
        });
    }
  });
};
const getFileOpenByPath = type => {
  const map = {
    0: 'ysfz-64.png',
    1: 'ysf-64.png',
    2: 'ysfd-64.png',
    3: 'yjs-64.png',
    4: 'ygs-64.png',
    5: 'ysh-64.png',
    6: 'jsh-64.png',
    7: 'ysfg-64.png',
  };
  // const icon = map[type] ? `icon/${map[type]}` : 'document.png';
  return getUrl(`icon/${map[type]}`);
};

const getAllProjectList = () => {
  // 打开所有项目列表弹窗
  allProVisible.value = true;
  store.SET_IS_Click_Open(false);
};
const isNextOpen = async path => {
  return new Promise(async resolve => {
    const judgeRes = await $ipc.invoke(ipcApiRoute.judgeSuffix, {
      path,
    });
    if (judgeRes.code === 200) {
      if (!judgeRes.result) {
        resolve(true);
      } else {
        infoMode.show({
          iconType: 'icon-qiangtixing',
          infoText: judgeRes.result,
          isSureModal: true,
          confirm: () => {
            resolve(true);
            infoMode.hide();
          },
        });
      }
    } else {
      infoMode.show({
        iconType: 'icon-qiangtixing',
        infoText: judgeRes.message,
        isSureModal: true,
        confirm: () => {
          resolve(false);
          infoMode.hide();
        },
      });
    }
    console.log('🚀 ~ judgeRes:', judgeRes);
  });
};
//最近使用项目点击跳转到详情页
const runProDetail = async proItem => {
  let name = proItem.path.substring(proItem.path.lastIndexOf('.') + 1);
  console.log('proItemname', name);
  emits('openPro', true);
  try {
    // if (!(await isNextOpen(proItem.path))) return;
    let response;
    // if (name.toUpperCase() === 'YSF' || name.toUpperCase() === 'YGS') {
    console.log('proItemname-ysf');
    response = await $ipc.invoke(ipcApiRoute.openProject, {
      path: proItem.path,
      sequenceNbr: proItem.sequenceNbr,
    });
    // }
    // else if (name.toUpperCase() === 'YJS') {
    //   console.log('proItemname-YJS');
    //   $ipc.invoke(ipcApiRoute.jieSuanOpenProJect, {
    //     path: proItem.path,
    //     sequenceNbr: proItem.sequenceNbr,
    //   });
    // }
    getRecentlyProgectList(); //更新排序
    if (response?.status !== 200) {
      if (response?.message) {
        message.error(response.message);
      }
    }
  } catch (err) {
    console.log('🚀 ~ err:', err);
    emits('openPro', false);
  } finally {
    emits('openPro', false);
  }
  // $ipc
  //   .invoke(ipcApiRoute.openProject, {
  //     path: proItem.path,
  //     sequenceNbr: proItem.sequenceNbr,
  //   })
  //   .then(response => {
  //     if (response?.status === 200) {
  //       getRecentlyProgectList(); //更新排序
  //     } else {
  //       getRecentlyProgectList();
  //       if (response?.message) {
  //         message.error(response.message);
  //       }
  //     }
  //   })
  // .catch(err => {
  //   // debugger;
  //   emits('openPro', false);
  // })
  // .finally(() => {
  //   emits('openPro', false);
  // });
};
let videoModal = ref(false); //帮助中心视频弹框
let chickItem = ref(false);
let videoUrl = ref(null);
const closeVedio = () => {
  if (chickItem.value) {
    chickItem.value = false;
  } else {
    videoModal.value = false;
  }
};
const runVideo = url => {
  chickItem.value = true;
  videoUrl.value = url;
};
const runHelpInfo = (type, item = null) => {
  //帮助中心跳转及优惠券跳转
  // shell.openExternal('https://www.yunsuanfang.com/feedback');
  if (type === 'help' && [1, 4, 5].includes(item.id)) {
    https: shell.openExternal(item.ossUrl);
  } else if (type === 'help' && item.id !== 1) {
    videoModal.value = true;
  }
};
const runDetail = (type, data) => {
  // message.info('功能建设中...');
  // 信息价时查询
  if (type === 'single') {
    shell.openExternal(data);
  } else {
    console.log('runDetail', data);
    shell.openExternal('https://www.yunsuanfang.com/materialPrice');
    // getmateriallist({
    //   areaCode: data.areaCode,
    //   yearMonths: data.yearMonths,
    // }).then(res => {
    //   console.log('getInfoList', res);
    //   projectContent.industryInfo = res;
    //   if (res.status === 200) {
    //     const newdata = JSON.parse(aes.decrypt(res.result));
    //     console.log('getInfoList', res);
    //   }
    // });
  }

  // router.push({
  //   path: '/projectDetail',
  //   query: {
  //     constructSequenceNbr: proItem.sequenceNbr,
  //   },
  // });
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  height: 100%;
  margin: auto;
  background-color: none;
  .header {
    display: flex;
    justify-content: space-between;
    font-size: 18px;
    color: #000000;
    height: 57px;
    line-height: 57px;
    border-bottom: 1px solid #e0e0e0;

    p {
      padding-left: 26px;
      padding-right: 32px;
      height: 100%;
      margin-bottom: 0;
    }
    &-icon {
      width: 20px;
      height: 20px;
      display: inline-block;
      margin-right: 6px;
      margin-top: -5px;
    }
    .herf {
      cursor: pointer;
      font-size: 14px;
      color: #187ec0;
    }
  }
  .w60 {
    flex-basis: calc(60% - 10px);
    overflow: hidden;
  }
  .w40 {
    flex-basis: calc(40% - 10px);
    overflow: hidden;
  }
  .h60 {
    height: calc(56% - 10px);
  }
  .h40 {
    height: calc(44% - 10px);
  }
  &-item {
    background-color: white;
    flex-basis: calc(50% - 10px);
    // padding: 20px;
    margin: 5px;

    .center {
      padding: 28px 32px 10px 47px;
      height: calc(100% - 56px);
      overflow-x: scroll;
      overflow-y: scroll;
      box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      .time {
        width: 15%;
        min-width: 140px;
        margin-left: 5px;
      }
      .text {
        width: 85%;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
      }
      p {
        cursor: pointer;
        max-height: 50px;
        margin-bottom: 10px;
        // line-height: 48px;
        display: flex;
        justify-content: space-between;
        color: #606266;
        font-size: 14px;
        img {
          margin-right: 7px;
          margin-top: -3px;
          width: 19px;
          height: 21px;
          object-fit: contain;
        }
      }
      .link {
        color: #287cfa;
        text-decoration-line: underline;
      }
      .help {
        display: flex;
        flex-direction: row;
        &-item {
          margin-right: 28px;
          box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.08);
          width: 30%;
          height: auto;
          cursor: pointer;
          &-img {
            position: relative;
            &-img1 {
              display: block;
              width: 100%;
            }
            &-img2 {
              position: absolute;
              right: 10px;
              bottom: 20px;
            }
            .circle {
              font-size: 20px;
              border-radius: 50%;
              right: 20px;
              top: 70%;
              position: absolute;
            }
          }
          div {
            // font-size: 13px;
            padding: 0 0 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          &-title {
            margin-left: 15px;
            color: #333333;
            font-size: 12px;
          }
        }
      }
    }
    &-img {
      // margin: 10px auto 0;
      padding: 16px;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      cursor: pointer;
      position: relative;
      .img {
        width: 100%;
        height: 100%;
      }
      .head-operate {
        position: absolute;
        top: 16px;
        left: 16px;
        right: 16px;
        z-index: 2;
      }
    }
  }
}
.videoContent {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  &-item {
    margin-right: 28px;
    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.08);
    width: 45%;
    height: auto;
    cursor: pointer;
    &-img {
      position: relative;
      img {
        display: block;
        width: 100%;
      }
      .circle {
        font-size: 20px;
        border-radius: 50%;
        right: 20px;
        top: 70%;
        position: absolute;
      }
    }
    div {
      font-size: 13px;
      padding: 10px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.itemVedio {
  width: 100%;
  height: 100%;
}
@media (max-width: 1366px) and (max-height: 768px) {
  .content {
    zoom: 0.85;
  }
  .help {
    &-item {
      width: 24%;
    }
  }
}
</style>
