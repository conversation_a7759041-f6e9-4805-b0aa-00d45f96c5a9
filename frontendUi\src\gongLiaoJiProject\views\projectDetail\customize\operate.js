/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-08-24 15:23:17
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-06-23 10:14:40
 */
import { ref } from 'vue';
import { getUrl } from '@/utils/index';

const operateList = ref([
  {
    label: '费用查看',
    name: 'costView',
    levelType: [1, 2, 3],
    icon: '',
    iconType: 'icon-feiyongchakan', //icon-font的type
    provideFun: 'onCostView',
    parameter: null,
    isAll: true,
    components: [],
    visible: true,
  },
  {
    label: '项目自检',
    name: 'onSelfCheck',
    levelType: [1, 2, 3],
    provideFun: 'onSelfCheck',
    iconType: 'icon-xiangmuzijian',
    icon: getUrl('operate/icon-fees.png'),
    isAll: true,
    components: [],
    visible: true,
  },
  // 项目概况类插入
  {
    label: '插入',
    name: 'insert',
    levelType: [1, 2, 3],
    icon: getUrl('operate/icon-insert.png'),
    iconType: 'icon-cs-charu', //icon-font的type
    iconStyle: {
      width: '28px',
      position: 'relative',
      left: '-4px',
    },
    provideFun: 'insertHandle',
    parameter: null,
    components: [
      'basicInfo',
      'engineerFeature',
      'qtxmZlje',
      'qtxmZygczgj',
      'summaryExpense',
      'qtxmStatistics',
      'domesticProcurement',
      'foreignProcurement',
      'summary',
      'otherConstructionCosts',
    ],
    visible: true,
  },
  {
    label: '锁定',
    name: 'lock',
    levelType: [1],
    icon: getUrl('operate/icon-lock.png'),
    iconType: 'icon-cs-suodingzhaobiaoxinxi',
    provideFun: 'resetLockHandle',
    parameter: null,
    components: [],
    visible: true,
  },
  {
    label: '删除',
    name: 'delete',
    levelType: [1, 2, 3],
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-cs-shanchu',
    provideFun: 'deleteHandle',
    components: [
      'basicInfo',
      'engineerFeature',
      'qtxmStatistics',
      'domesticProcurement',
      'foreignProcurement',
      'summary',
      'otherConstructionCosts',
    ],
    visible: true,
  },
  // 其他项目插入
  {
    label: '插入',
    name: 'insert-op',
    levelType: [3],
    iconType: 'icon-cs-charu',
    options: [
      {
        name: '数据行',
        kind: '01',
        isValid: true,
      },
      {
        name: '标题行',
        kind: '02',
        isValid: true,
      },
    ],
    type: 'select',
    icon: getUrl('operate/icon-insert.png'),
    iconStyle: {
      width: '28px',
      position: 'relative',
      left: '-4px',
    },
    provideFun: 'insertHandle',
    parameter: null,
    components: ['qtxmZcbfwf', 'qtxmJrg'],
    visible: true,
  },
  {
    label: '统一调价',
    name: 'unifiedPriceAdjustmentGLJ',
    levelType: [1, 2, 3],
    type: 'select',
    options: [
      {
        name: '造价系数调整',
        kind: '01',
        isValid: true,
      },
    ],
    iconType: 'icon-chakanguanlian',
    icon: '',
    public: true,
    components: [
      'subItemProject',
      'measuresItem',
      'CostAnalysis',
      'basicInfo',
      'engineerFeature',
      'feeWithDrawalTable',
      'humanMachineSummary',
      'summaryExpense',
      'qtxmStatistics',
    ],
    visible: true,
  },
  {
    label: '费率表',
    name: 'feeExcel',
    levelType: [1, 2, 3],
    icon: getUrl('operate/icon-export-table.png'),
    iconType: 'icon-biaodan-feishuaibiao',
    provideFun: 'feeExcel',
    iconStyle: {
      fontSize: '19px',
      height: '22px',
      marginTop: '2px',
    },
    labelStyle: {
      marginTop: '3px',
    },
    components: ['qtxmZcbfwf', 'qtxmJrg', 'qtxmZlje', 'qtxmZygczgj'],
    visible: true,
  },

  {
    label: '导入excel文件',
    name: 'importExcelFile',
    levelType: [1],
    iconType: 'icon-daoru',
    components: ['domesticProcurement', 'foreignProcurement'],
    visible: true,
  },
  {
    label: '保存',
    name: 'save',
    levelType: [1, 2, 3],
    icon: getUrl('operate/icon-save.png'),
    iconType: 'icon-cs-baocun',
    components: ['PreparationOfInstructions'],
    visible: true,
  },
  {
    label: '其他费用计算器',
    name: 'orther-cost-calculator',
    levelType: [1, 2, 3],
    iconType: 'icon-cs-zidongjisuancuoshifeiyong',
    icon: getUrl('operate/icon-calculation-measures.png'),
    components: ['otherConstructionCosts'],
    iconStyle: {
      width: '28px',
    },
    visible: true,
  },

  {
    label: '进口设备计算器',
    name: 'calculation-jksb',
    levelType: [1, 2, 3],
    iconType: 'icon-cs-zidongjisuancuoshifeiyong',
    icon: getUrl('operate/icon-calculation-measures.png'),
    components: ['foreignProcurement'],
    iconStyle: {
      width: '28px',
    },
    visible: true,
  },
  // {
  //   label: '导出报表',
  //   name: 'export-table',
  //   levelType: [1, 2, 3],
  //   icon: getUrl('operate/icon-export-table.png'),
  //   iconType: 'icon-cs-daochubaobiao',
  //   components: ['CostAnalysis', 'humanMachineSummary'],
  //   setIndex: {
  //     humanMachineSummary: 3,
  //   },
  // visible:true,
  // },
  {
    label: '统一应用',
    name: 'unify',
    disabled: false,
    levelType: [1, 2],
    iconType: 'icon-cs-tongyiyingyong',
    icon: getUrl('operate/icon-unify.png'),
    components: ['feeWithDrawalTable'],
    visible: true,
  },
  {
    label: '切换计税方式',
    name: 'changeTaxation',
    levelType: [1, 2, 3],
    public: true,
    iconType: 'icon-cs-jishuifangshi',
    icon: getUrl('operate/icon-taxation.png'),
    infoDec: '便捷用户切换当前工程项目的计税方式【一般计税/简易计税】',
    components: [
      'CostAnalysis',
      'measuresItem',
      'subItemProject',
      'IndependentFee',
      'summaryExpense',
      'estimateExpense',
      'humanMachineSummary',
      'otherConstructionCosts',
      'basicInfo',
      'engineerFeature',
      'feeWithDrawalTable',
    ],
    visible: true,
  },
  // {
  //   label: '计税方式',
  //   name: 'taxation',
  //   levelType: [1, 2, 3],
  //   iconType: 'icon-cs-jishuifangshi',
  //   icon: getUrl('operate/icon-taxation.png'),
  //   components: ['feeWithDrawalTable'],
  // visible:true,
  // },
  // 预算书，措施项目操作
  {
    label: '导入',
    name: 'Import-project',
    type: 'select',
    options: [
      // {
      //   type: 0,
      //   name: '导入excel文件',
      //   kind: '01',
      //   isValid: true,
      // },
      {
        type: 1,
        name: '导入工程文件',
        kind: '02',
        isValid: true,
      },
    ],
    levelType: [3],
    icon: getUrl('operate/icon-insert.png'),
    iconType: 'icon-daoru',
    iconStyle: {
      width: '28px',
      position: 'relative',
    },
    parameter: null,
    components: ['subItemProject', 'measuresItem'],
    visible: true,
  },
  {
    label: '插入',
    name: 'insert-subItem',
    type: 'select',
    options: [
      {
        type: 0,
        name: '添加标题',
        kind: '01',
        isValid: true,
      },
      {
        type: 1,
        name: '添加子项',
        kind: '02',
        isValid: false,
      },
      {
        type: 2,
        name: '添加子目',
        kind: '-1',
        isValid: false,
      },
    ],
    levelType: [3],
    icon: getUrl('operate/icon-insert.png'),
    iconType: 'icon-cs-charu',
    iconStyle: {
      width: '28px',
      position: 'relative',
      left: '-4px',
    },
    parameter: null,
    components: ['subItemProject', 'measuresItem'],
    visible: true,
  },
  {
    label: '补充',
    name: 'supplement',
    type: 'select',
    options: [
      // {
      //   type: 2,
      //   name: '补充清单',
      //   kind: '03',
      //   isValid: false,
      // },
      {
        type: 3,
        name: '补充子目',
        kind: '04',
        isValid: false,
      },
      {
        type: 3,
        name: '补充人材机',
        kind: '05',
        isValid: false,
      },
    ],
    levelType: [3],
    icon: getUrl('operate/icon-supplement.png'),
    iconType: 'icon-cs-buchong',
    parameter: null,
    components: ['subItemProject', 'measuresItem'],
    visible: true,
  },
  {
    label: '删除',
    name: 'delete-subItem',
    levelType: [3],
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-cs-shanchu',
    components: ['subItemProject', 'measuresItem', 'summaryExpense'],
    visible: true,
  },
  // {
  //   label: '整体锁定',
  //   name: 'lock-subItem',
  //   levelType: [3],
  //   icon: getUrl('operate/icon-delete.png'),
  //   iconType: 'icon-cs-suodingzhaobiaoxinxi',
  //   components: ['measuresItem'],
  //   visible: true,
  // },
  // {
  //   label: '编码重刷',
  //   name: 'code-reset',
  //   levelType: [3],
  //   disabled: false,
  //   iconType: 'icon-cs-bianmazhongshua',
  //   icon: getUrl('operate/icon-code-reset.png'),
  //   components: ['measuresItem'],
  //   visible: true,
  // },
  {
    label: '装饰垂运',
    name: 'vertical-transport',
    windows: ['parentPage'],
    levelType: [3],
    iconType: 'icon-cs-zairumoban',
    infoDec: '装饰装修工程垂直运输费业务计取',
    icon: getUrl('operate/icon-vertical-transport.png'),
    components: ['subItemProject', 'measuresItem'],
    visible: true,
  },
  {
    label: '装饰超高',
    name: 'superelevation',
    windows: ['parentPage'],
    levelType: [3],
    iconType: 'icon-cs-zhuangshichaogao',
    infoDec: '装饰装修工程超高费业务计取',
    icon: getUrl('operate/icon-superelevation.png'),
    components: ['subItemProject', 'measuresItem'],
    visible: true,
  },
  {
    label: '安装费用',
    name: 'installation-costs',
    levelType: [3],
    iconType: 'icon-cs-anzhuangfeiyong',
    parameter: null,
    components: ['subItemProject', 'measuresItem'],
    visible: true,
  },
  {
    label: '房修土建费用',
    name: 'civil-construction-costs-of-houses-JQ',
    type: 'select',
    levelType: [3],
    public: true,
    options: [
      {
        type: 1,
        name: '超高费',
        kind: 1,
        isValid: true,
      },
      {
        type: 2,
        name: '垂直运输费',
        kind: 2,
        isValid: true,
      },
      {
        type: 3,
        name: '中小型机械使用费',
        kind: 3,
        isValid: true,
      },
      {
        type: 4,
        name: '工程水电费',
        kind: 4,
        isValid: true,
      },
    ],
    iconType: 'icon-fangxiutujianfeiyongjiqu',
    components: ['subItemProject', 'measuresItem'],
    visible: true,
    iconStyle: {
      fontSize: '18px',
      padding: '4px',
    },
  },
  {
    label: '计算商品砼泵送增加费',
    name: 'calculate-commercial',
    windows: ['parentPage'],
    levelType: [3],
    iconType: 'icon-cs-zidongjisuancuoshifeiyong',
    icon: getUrl('operate/icon-calculation-measures.png'),
    components: ['subItemProject'],
    iconStyle: {
      width: '28px',
      position: 'relative',
    },
    visible: true,
  },
  {
    label: '局部汇总',
    name: 'partial-summary-glj',
    levelType: [3],
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-cs-shanchu',
    components: ['subItemProject', 'measuresItem'],
    visible: true,
  },
  {
    label: '整理子目',
    name: 'organize-subitems',
    levelType: [3],
    iconType: 'icon-zhenglizimu',
    iconStyle: {
      fontSize: '20px',
      marginBottom: '6px',
      position: 'relative',
      top: '2px',
    },
    type: 'select',
    options: [
      {
        type: 0,
        name: '分部整理',
        kind: 'fb',
        isValid: true,
      },
      {
        type: 1,
        name: '子目排序',
        kind: 'zm',
        isValid: true,
      },
    ],
    components: ['subItemProject'],
    visible: true,
  },
  // {
  //   label: '数据替换',
  //   name: 'dataReplacement',
  //   windows: ['childPage', 'parentPage'],
  //   levelType: [3],
  //   disabled: true,
  //   iconType: 'icon-tihuanshuju',
  //   icon: getUrl('operate/icon-fees.png'),
  //   public: true,
  //   // components: ['subItemProject', 'measuresItem'],
  //   components: ['subItemProject'],
  //   visible: true,
  // },
  {
    label: '修改未计价材料',
    name: 'unpriced',
    levelType: [3],
    disabled: true,
    iconType: 'icon-qingdankuaisuzujia',
    icon: getUrl('operate/icon-fees.png'),
    public: true,
    components: ['subItemProject'],
    visible: true,
  },
  {
    label: '展开到',
    name: 'openData',
    type: 'select',
    options: [
      {
        type: 0,
        name: '展开所有',
        kind: 'ALL',
        isValid: false,
      },
      {
        type: 1,
        name: '一级分部',
        kind: 'ONE',
        isValid: false,
      },
      {
        type: 2,
        name: '二级分部',
        kind: 'TWO',
        isValid: false,
      },
      {
        type: 3,
        name: '三级分部',
        kind: 'THREE',
        isValid: false,
      },
      {
        type: 4,
        name: '四级分部',
        kind: 'FOUR',
        isValid: false,
      },
      {
        type: 5,
        name: '子目',
        kind: 'DE',
        isValid: false,
      },
      {
        type: 6,
        name: '主材/设备',
        kind: 'ZHUCAI',
        isValid: false,
      },
    ],
    levelType: [3],
    iconType: 'icon-zhankaidao',
    icon: getUrl('operate/icon-insert.png'),
    components: ['subItemProject'],
    visible: true,
  },
  // 措施项目展开到
  {
    label: '展开到',
    name: 'expandTo',
    type: 'select',
    options: [
      {
        type: 0,
        name: '展开所有',
        kind: 'ALL',
        isValid: false,
      },
      {
        type: 1,
        name: '一级标题',
        kind: 'ONE',
        isValid: false,
      },
      {
        type: 2,
        name: '二级标题',
        kind: 'TWO',
        isValid: false,
      },
      {
        type: 3,
        name: '措施项',
        kind: 'THREE',
        isValid: false,
      },
      {
        type: 4,
        name: '子目',
        kind: 'DE',
        isValid: false,
      },
      {
        type: 5,
        name: '主材/设备',
        kind: 'ZHUCAI',
        isValid: false,
      },
    ],
    levelType: [3],
    iconType: 'icon-zhankaidao',
    icon: getUrl('operate/icon-insert.png'),
    components: ['measuresItem'],
    visible: true,
  },
  {
    label: '自动计算措施费用',
    name: 'calculation-measures',
    levelType: [1, 2, 3],
    iconType: 'icon-cs-zidongjisuancuoshifeiyong',
    icon: getUrl('operate/icon-calculation-measures.png'),
    infoDec: '自动计算单位工程中其他总价措施费用',
    components: ['measuresItem'],
    iconStyle: {
      width: '28px',
    },
    labelStyle:{
      color:'black',
    },
    visible: true,
  },

  {
    label: '载价',
    name: 'batch-loadprice',
    levelType: [1, 2, 3],
    disabled: true,
    iconType: 'icon-cs-zaijia',
    icon: getUrl('operate/icon-carrying-price.png'),
    components: ['humanMachineSummary'],
    visible: true,
  },
  {
    label: '设置主要材料',
    name: 'set-main-materials',
    levelType: [],
    iconStyle: {
      fontSize: '16px',
      padding: '5px',
    },
    disabled: false,
    iconType: 'icon-shezhizhuyaocailiao',
    components: ['humanMachineSummary'],
    visible: true,
  },
  // {
  //   label: '载价报告',
  //   name: 'loadprice-report',
  //   levelType: [1, 3],
  //   disabled: true,
  //   icon: getUrl('operate/icon-carrying-price.png'),
  //   components: ['humanMachineSummary'],
  // visible:true,

  // },
  {
    label: '调整市场价系数',
    name: 'market-price',
    levelType: [1, 2, 3],
    iconType: 'icon-cs-tiaozhengshichangjiaxishu',
    icon: getUrl('operate/icon-market-price.png'),
    components: ['humanMachineSummary'],
    visible: true,
  },
  {
    label: '统一应用',
    name: 'unify-humanMachineSummary',
    disabled: true,
    levelType: [1, 2],
    iconType: 'icon-cs-tongyiyingyong',
    icon: getUrl('operate/icon-unify.png'),
    components: ['humanMachineSummary'],
    visible: true,
  },
  {
    label: '人材机无差价',
    name: 'rcj-no-margin',
    levelType: [3],
    iconType: 'icon-rencaijiwujiacha',
    icon: getUrl('operate/rcj-no-margin.png'),
    components: [],
    visible: true,
  },
  {
    label: '新建汇总分类',
    name: 'rcj-newAdd-classify',
    levelType: [3],
    iconType: 'icon-xinjianhuizongfenlei',
    icon: getUrl('operate/rcj-newAdd-classify.png'),
    components: ['humanMachineSummary'],
    visible: true,
  },
  // {
  //   label: '合价排序',
  //   name: 'rcj-totalPrice-sort',
  //   levelType: [3],
  //   iconType: 'icon-hejiapaixu',
  //   icon: getUrl('operate/rcj-totalPrice-sort.png'),
  //   components: ['humanMachineSummary'],
  // visible:true,
  //
  // },
  {
    label: '导出当前汇总表',
    name: 'rcj-exportNow-summaryTable',
    levelType: [1, 2, 3],
    iconType: 'icon-daochudangqianhuizongbiao',
    icon: getUrl('operate/rcj-exportNow-summaryTable.png'),
    components: ['humanMachineSummary'],
    visible: true,
  },
  {
    label: '人材机无价差',
    name: 'no-price-difference',
    levelType: [1, 2, 3],
    iconType: 'icon-rencaijiwujiacha',
    icon: getUrl('operate/rcj-exportNow-summaryTable.png'),
    components: ['humanMachineSummary'],
    visible: true,
  },
  {
    label: '颜色标记',
    name: 'rcj-color-sign',
    type: 'select',
    disabled: false,
    options: [
      {
        type: 2,
        name: '默认',
        kind: 'default',
        color: 'transparent',
        isValid: true,
      },
      {
        type: 3,
        name: '红色',
        kind: 'red',
        color: '#F99595',
        isValid: true,
      },
      {
        type: 3,
        name: '绿色',
        kind: 'green',
        color: '#E2FADA',
        isValid: true,
      },
      {
        type: 3,
        name: '橙色',
        kind: 'orange',
        color: '#F38059',
        isValid: true,
      },
      {
        type: 3,
        name: '黄色',
        kind: 'yellow',
        color: '#FDFF9B',
        isValid: true,
      },
      {
        type: 3,
        name: '深蓝色',
        kind: 'deep-blue',
        color: '#8BAAFF',
        isValid: true,
      },
      {
        type: 3,
        name: '淡紫色',
        kind: 'light-purple',
        color: '#D9A6E6',
        isValid: true,
      },
      {
        type: 3,
        name: '亮蓝色',
        kind: 'light-blue',
        color: '#9ED7F1',
        isValid: true,
      },
      {
        type: 3,
        name: '深黄色',
        kind: 'deep-yellow',
        color: '#FFDF7A',
        isValid: true,
      },
    ],
    levelType: [1, 2, 3],
    iconType: 'icon-yansebiaoji',
    icon: getUrl('operate/rcj-color-sign.png'),
    components: ['subItemProject', 'humanMachineSummary', 'measuresItem'],
    visible: true,
  },
  {
    label: '汇总范围',
    name: 'summary-scope',
    levelType: [1],
    iconType: 'icon-huizongfanwei',
    components: ['humanMachineSummary'],
    visible: true,
  },
  {
    label: '查询关联定额',
    name: 'quota-popup',
    levelType: [1, 2, 3],
    disabled: false,
    iconType: 'icon-chaxunguanliandinge',
    components: ['humanMachineSummary'],
    visible: true,
  },
  {
    label: '查找',
    name: 'lookup',
    levelType: [1, 2, 3],
    disabled: false,
    iconType: 'icon-chazhao',
    components: ['subItemProject', 'humanMachineSummary', 'measuresItem'],
    visible: true,
  },
  {
    label: '过滤',
    name: 'filterate',
    levelType: [1, 2, 3],
    disabled: false,
    iconType: 'icon-guolv',
    components: ['subItemProject', 'humanMachineSummary', 'measuresItem'],
    visible: true,
  },
  {
    label: '合并相似材料',
    name: 'Merge-similar-materials',
    levelType: [1, 2, 3],
    disabled: false,
    iconType: 'icon-guolv',
    components: ['humanMachineSummary'],
    visible: true,
  },
  {
    label: '存价',
    name: 'Deposit-price',
    levelType: [1, 2, 3],
    disabled: false,
    iconType: 'icon-guolv',
    components: ['humanMachineSummary'],
    visible: true,
  },
  {
    label: '多专业取费',
    name: 'multi-disciplinary-summary',
    levelType: [3],
    iconType: 'icon-duozhuanyehuizong',
    components: ['summaryExpense'],
    visible: true,
  },

  {
    label: '计价文件查询',
    name: 'pricing-document-query',
    levelType: [1, 2, 3],
    iconType: 'icon-jijiawenjianchaxun',
    components: ['otherConstructionCosts'],
    iconStyle: {
      width: '28px',
    },
    visible: true,
  },
  {
    label: '安、文明细',
    name: 'anwen-fee',
    levelType: [3],
    iconType: 'icon-baocunmoban',
    components: ['summaryExpense'],
    iconStyle: {
      width: '28px',
    },
    visible: true,
  },
  {
    label: '保存模板',
    name: 'save-template',
    levelType: [1, 2, 3],
    iconType: 'icon-baocunmoban',
    components: [
      'otherConstructionCosts',
      'summaryExpense',
      'estimateExpense',
      'summary',
    ],
    iconStyle: {
      width: '28px',
    },
    visible: true,
  },
  {
    label: '载入模板',
    name: 'load-template',
    levelType: [1, 2, 3],
    iconType: 'icon-cs-zairumoban',
    components: [
      'otherConstructionCosts',
      'summaryExpense',
      'estimateExpense',
      'summary',
    ],
    iconStyle: {
      width: '28px',
    },
    visible: true,
  },

  {
    label: '当前汇总表批量应用',
    name: 'batch-application',
    levelType: [1, 2, 3],
    iconType: 'icon-cs-zidongjisuancuoshifeiyong',
    icon: getUrl('operate/icon-calculation-measures.png'),
    components: ['summaryExpense'],
    iconStyle: {
      width: '28px',
    },
    visible: true,
  },
  {
    label: '计取水、电费',
    name: 'utility-bills',
    levelType: [3],
    iconType: 'icon-bs-guifeimingxi',
    components: ['summaryExpense'],
    iconStyle: {
      width: '28px',
    },
    visible: true,
  },
  // {
  //   label: '统一汇总表格式',
  //   name: 'unified-summary-table-display',
  //   type: 'select',
  //   options: [
  //     {
  //       type: 0,
  //       name: '统一所有专业汇总表格式',
  //       kind: '01',
  //       isValid: true,
  //     },
  //     {
  //       type: 1,
  //       name: '统一当前专业汇总表格式',
  //       kind: '02',
  //       isValid: true,
  //     },
  //   ],
  //   levelType: [3],
  //   iconType: 'icon-tongyihuizongbiaogeshi',
  //   components: ['summaryExpense'],
  // visible:true,

  // },
  // 预算书，措施项目操作
  // {
  //   label: '组价方案匹配',
  //   name: 'component-matching',
  //   type: 'select',
  //   options: [
  //     {
  //       type: 0,
  //       name: '方案匹配',
  //       kind: '01',
  //       isValid: false,
  //     },
  //     {
  //       type: 1,
  //       name: '筛选组价数据',
  //       kind: '02',
  //       isValid: false,
  //     },
  //   ],
  //   levelType: [3],
  //   iconType: 'icon-zujiafanganpipei',
  //   icon: getUrl('operate/icon-insert.png'),
  //   iconStyle: {
  //     width: '28px',
  //   },
  //   parameter: null,
  //   components: ['measuresItem'],
  //   visible: true,
  // },
  {
    label: '导出报表',
    name: 'export-table',
    levelType: [1, 2, 3],
    iconType: 'icon-cs-daochubaobiao',
    components: ['CostAnalysis'], //暂时隐藏'CostAnalysis', 'humanMachineSummary'
    visible: true,
  },
]);

export const updateOperateByName = (name, callback) => {
  const info = operateList.value.find(item => item.name === name);
  callback(info);
};

export default operateList;
