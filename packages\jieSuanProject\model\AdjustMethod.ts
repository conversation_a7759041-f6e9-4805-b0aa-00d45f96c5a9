/**
 * 调差法设置
 */
export class AdjustMethod {



    public kind : number;//材料类别

    public adjustMethod : number;//调整法类型


    public batchType : number;//是否单期:1  多期：2


    public frequencyList : Array<any>;//次数明细  数据内容 {"num": 1 , scope: }


    constructor(kind: number, adjustMethod: number, batchType: number, frequencyList: Array<any>) {
        this.kind = kind;
        this.adjustMethod = adjustMethod;
        this.batchType = batchType;
        this.frequencyList = frequencyList;
    }
}