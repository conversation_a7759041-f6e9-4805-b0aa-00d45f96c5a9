const { ResponseData } = require('../../../common/ResponseData');
const FxtjCostMatchController = require("../../../electron/controller/fxtjCostMatchController");
const JieSuanFxtjCostMatchContext = require("../service/costCalculation/fxtjCostMatch/jieSuanFxtjCostMatchContext");

/**
 * 房修土建费用记取
 */
class JieSuanFxtjCostMatchController extends FxtjCostMatchController {


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 记取
     */
    async fxtjCostMatch(args) {
        // feeType代表费用类型
        const { feeType } = args;
        return ResponseData.success(await new JieSuanFxtjCostMatchContext(feeType).fxtjCostMatch(args));
    }


}

JieSuanFxtjCostMatchController.toString = () => '[class JieSuanFxtjCostMatchController]';
module.exports = JieSuanFxtjCostMatchController;