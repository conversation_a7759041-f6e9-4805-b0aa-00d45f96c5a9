<!--
 * @Descripttion: 
 * @Author: sunchen
 * @Date: 2024-08-01 09:19:19
 * @LastEditors: sunchen
 * @LastEditTime: 2025-03-07 11:40:12
-->
<template>
  <div class="content">
    <!-- <div class="topBox">
      <span>当前费用汇总专业：</span>
      <span class="topInner1">{{nowProject}}</span>
    </div> -->
    <div class="bottomBox" style="margin-bottom: 25px">
      <div style="margin-bottom: 10px; font-size: 14px">多专业取费</div>
      <a-radio-group
        v-model:value="isSingleMajorFlag"
        name="radioGroup"
        style="margin-left: 15px"
      >
        <a-radio :value="true">单专业取费</a-radio>
        <a-radio :value="false">多专业取费</a-radio>
      </a-radio-group>
    </div>
    <div class="bottomBox" v-if="isSingleMajorFlag">
      <span>默认取费专业：</span>
      <a-select
        ref="select"
        v-model:value="qfMajorType"
        style="width: 180px"
        @focus="focus"
      >
        <a-select-option
          v-for="(item, index) in selData"
          :key="index"
          :value="item.code"
          >{{ item.desc }}</a-select-option
        >
      </a-select>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import feePro from '@gongLiaoJi/api/feePro';
import { projectDetailStore } from '@/store/projectDetail';
import csProject from '@gongLiaoJi/api/csProject';
import csDetail from '@gongLiaoJi/api/projectDetail';
const store = projectDetailStore();
const checked = ref(false);
const nowProject = ref('');
const qfMajorType = ref('');
const isSingleMajorFlag = ref(null);
const selData = ref([]);
const props = defineProps({
  isPartFlag: {
    type: Boolean,
    default: false,
  },
  QfMajorType: {
    type: String,
    default: '',
  },
  // true局部汇总弹框，false费用汇总弹框
});
onMounted(() => {
  getInfoData();
  getInit();
});

const getInit = () => {
  csDetail
    .getIsSingleMajorFlag({
      unitId: store.currentTreeInfo?.id,
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId,
      isPartFlag: props.isPartFlag,
    })
    .then(res => {
      isSingleMajorFlag.value =
        typeof res.result === 'boolean' ? res.result : '';
    });
};

// 获取基础数据
const getInfoData = () => {
  let leftTree = store.currentTreeInfo;
  csDetail.queryBaseFeeFileProjectData().then(res => {
    if (res.status !== 200) {
      message.error(res.message);
      return false;
    }
    let data = res.result.map(item => {
      return {
        desc: item.qfName,
        code: item.qfCode,
      };
    });
    selData.value = data;

    if (props.isPartFlag) {
      qfMajorType.value = props.QfMajorType;
    } else {
      getQfMajorType();
    }
  });
};

const getQfMajorType = () => {
  csProject
    .getQfMajorTypeByUnit({
      unitId: store.currentTreeInfo?.id,
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId,
      isPartFlag: props.isPartFlag,
    })
    .then(res => {
      console.log('💡 ~ getQfMajorType ~ res:', res);

      for (let i in selData.value) {
        if (selData.value[i].code === res.result) {
          nowProject.value = selData.value[i].desc;
          qfMajorType.value = selData.value[i].code;
          break;
        }
      }
    });
};

defineExpose({
  qfMajorType,
  isSingleMajorFlag,
});
</script>
<style lang="scss" scoped>
.content {
  color: black;
}
.topBox {
  margin-bottom: 20px;
}
.topInner1 {
  padding: 5px 10px;
}
.bottomBox {
  margin-top: 10px;
}
.topTitleBox {
  width: 130px;
  display: inline-block;
  text-align: right;
}
</style>
