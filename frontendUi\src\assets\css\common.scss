:root {
  --surely-table-border-color: rgba(0, 0, 0, 0.25) !important;
  --surely-table-background-color: #f3f3f3 !important;
  --vxe-table-row-height-mini: 30px !important;
  --project-detail-header-height: 56px;
  //--project-detail-functional-area-height: 54px;
  --project-detail-functional-area-height: 60px;
  --project-detail-main-content-tabs-menu-height: 41px;
  --project-detail-footer: 33px;
  --project-detail-table-font-size: 12px;
  --table-webkit-scrollbar-thumb-hover: rgba(144, 176, 227, 1);
  --table-webkit-scrollbar-thumb: rgba(
    181,
    200,
    234,
    1
  ); //--table-webkit-scrollbar-thumb-hover
}

#pricing_body {
  .annotations-pop {
    .ant-popover-inner-content {
      // padding: 0px;
    }
  }
  .ant-select-dropdown .ant-select-item {
    font-size: 12px;
    padding: 3px 5px !important;
  }
  .ant-select-dropdown .ant-select-item-option-content {
    // text-align: center;
  }
}
.zcsb-color {
  color: #7d1dff;
  .code-color {
    color: #7d1dff !important;
  }
  .surely-table-cell-inner {
    color: #7d1dff !important;
  }
}

.vxe-table--render-default .vxe-table--border-line {
  z-index: 8 !important;
}
// 没有滚动条时去除表格再有滚动条时的外部边框
.table-no-outer-border,
.table-clear-scroll-outer-border {
  &:not(.is--scroll-y) {
    .vxe-table--header-wrapper {
      background-color: #ffff !important;
      .vxe-table--header {
        background-color: #f3f3f3;
        .vxe-header--column:nth-child(1) {
          border-left: 1px solid var(--vxe-table-border-color) !important;
        }
      }
    }
    .vxe-table--body .vxe-body--column:nth-child(1) {
      border-left: 1px solid var(--vxe-table-border-color) !important;
    }
    .vxe-table--border-line {
      border-right: none !important;
      border-bottom: none !important;
      border-left: none !important;
      width: calc(100% - 10px) !important;
    }
    .vxe-header--gutter {
      background: #fff !important;
    }
  }
}
.table-no-outer-border {
  &:not(.is--scroll-y) {
    .vxe-table--border-line {
      width: 100% !important;
    }
  }
}
.quota-content {
  height: 100%;
}
.association-popover .ant-popover-inner-content {
  padding: 5px 5px;
}
button:focus,
button:focus-visible {
  outline: none !important;
}
.agreeIndex {
  z-index: 1100 !important;
}
.dialog-comm {
  .vxe-modal--content {
    padding: 20px 20px 30px 20px !important;
    // padding: 12px 18px !important;//后续需要修改
  }
  .vxe-modal--box {
    max-height: 100vh;
    //overflow-y: hidden;
    overflow: hidden;
    min-width: 400px;
    max-width: 100%;
    border: 0 !important;
    //&:hover {
    //  overflow-y: auto;
    //}
  }
  .vxe-modal--header {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 24px;
    height: 50px;
    -webkit-app-region: no-drag;
    background: linear-gradient(
      90deg,
      #3f78ce 0%,
      rgba(51, 131, 252, 0.75) 100%
    );

    &::after {
      content: '';
      position: absolute;
      right: 50px;
      top: 0;
      height: 100%;
      max-width: 322px;
      width: 100%;
      z-index: 0;
      background: url(../img/dialog-title-bg.png) no-repeat 100% 0;
      background-size: 322px 50px;
    }
    .vxe-modal--header-title {
      position: relative;
      color: #fff;
      text-align: left;
      line-height: 16px;
      font-size: 14px;
      padding: 0 0 0 9px;
      border-radius: 2px;
      &::before {
        position: absolute;
        content: '';
        width: 2px;
        height: 100%;
        left: 0;
        top: 0;
        background-color: #fff;
        border-radius: 1px;
      }
    }
  }
  .vxe-modal--header-right {
    height: 50px;
    color: #fff;
    line-height: 40px;
  }
  .vxe-modal--close-btn,
  .vxe-modal--zoom-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    &:hover {
      color: #fff !important;
    }
  }
  .vxe-modal--zoom-btn {
    transform: translate(-150%, -50%);
  }
  .vxe-modal--footer,
  .footer-btn-list {
    text-align: center;
    .ant-btn {
      margin: 0 13px;
    }
    .vxe-button {
      margin: 0 13px;
    }
  }
}
.qdQuickPricing-dialog {
  .vxe-modal--box {
    overflow: hidden !important;
  }
}
.resizeClass {
  .vxe-modal--box {
    //overflow-y: visible !important;
  }
}
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.note-tips {
  position: relative;
  &::after {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid rgba(167, 61, 61, 1);
    top: -2px;
    right: -5px;
    z-index: 1;
    cursor: pointer;
    transform: rotate(44deg);
    position: absolute;
  }
}

.confirm-dialog {
  .vxe-modal--content {
    padding: 0.8em 1em !important;
  }
}
.dialog-self {
  .vxe-modal--box {
    max-height: 70vh;
    min-width: 400px;
    max-width: 80%;
    border: 0 !important;
    // overflow-y: hidden !important;
  }
  .vxe-modal--content {
    overflow: hidden !important;
  }
  .vxe-modal--header {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 24px;
    height: 50px;
    background-color: white !important;
    border: none !important;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      max-width: 322px;
      width: 30%;
      z-index: 0;
      background: #3f78ce;
    }
    &::after {
      content: '';
      position: absolute;
      right: 40px;
      top: 0;
      height: 100%;
      max-width: 322px;
      width: 100%;
      z-index: 1;
      background-size: 322px 50px;
    }
    .vxe-modal--header-title {
      position: relative;
      color: #fff;
      text-align: left;
      line-height: 16px;
      font-size: 14px;
      padding: 0 0 0 9px;
      border-radius: 2px;
      &::before {
        position: absolute;
        content: '';
        width: 2px;
        height: 100%;
        left: 0;
        top: 0;
        background-color: #fff;
        border-radius: 1px;
      }
    }
  }
  .vxe-modal--close-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: #999999;
  }
  .vxe-modal--content {
    padding: 0 !important;
  }
}
.dialog-selfMax {
  .vxe-modal--box {
    max-height: 100vh !important;
    max-width: 100vw !important;
    top: 0vh !important;
    border: 0 !important;
    // overflow-y: hidden !important;
  }
  .vxe-modal--header::before {
    width: 100%;
    z-index: 0;
    max-width: none;
    background: #3f78ce;
  }
  .vxe-modal--close-btn {
    color: #ffffff;
  }
}
.titleNoColor .vxe-modal--header {
  background-color: white !important;
  height: 30px !important;
  font-size: 17px !important;
}
.titleNoColor .vxe-modal--body {
  font-size: 14px !important;
}
.noHeader .vxe-modal--header,
.noHeaderHasclose .vxe-modal--header {
  border: none !important;
}
.noHeader .vxe-modal--close-btn {
  display: none !important;
}

.betaHeader .vxe-modal--header {
  display: none !important;
}
.betaHeader .vxe-modal--content {
  padding: 0 !important;
}
.noMask {
  width: 225px !important;
  height: 100% !important;
}
.noMask.lock--view:before,
.noMask.is--mask:before {
  width: 100% !important;
  height: 150px !important;
}
.redFont {
  .vxe-context-menu--link {
    color: rgba(220, 56, 56, 1) !important;
  }
}
.ant-modal-content .ant-btn-dangerous {
  color: #fff !important;
  border-color: #ff4d4f !important;
  background: rgba(222, 63, 63, 1) !important;
}
// /*滚动条整体部分*/
// .table-scrollbar ::-webkit-scrollbar {
//   width: 10px;
//   height: 10px;
// }
// /*滚动条的轨道*/
// .table-scrollbar ::-webkit-scrollbar-track {
//   background-color: #FFFFFF;
// }
// /*滚动条里面的小方块，能向上向下移动*/
// .table-scrollbar ::-webkit-scrollbar-thumb {
//   background-color: #bfbfbf;
//   border-radius: 5px;
//   border: 1px solid #F1F1F1;
//   box-shadow: inset 0 0 6px rgba(0,0,0,.3);
// }
// .table-scrollbar ::-webkit-scrollbar-thumb:hover {
//   background-color: #A8A8A8;
// }
// .table-scrollbar ::-webkit-scrollbar-thumb:active {
//   background-color: #787878;
// }
// /*边角，即两个滚动条的交汇处*/
// .table-scrollbar ::-webkit-scrollbar-corner {
//   background-color: #FFFFFF;
// }

.common-flex-upAndDown {
  display: flex;
  flex-direction: column;
  height: 100%;
  .flex-auto {
    flex: auto;
    overflow: hidden;
  }
}

.table-top-h {
  height: 55%;
}

.table-bottom-h {
  height: 45%;
}

.table-content-flex-column {
  display: flex;
  flex-direction: column;
  .flex-auto {
    flex: auto;
  }
  .flex-bottom {
    height: 45%;
  }
}

.cell-line-break {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

$padding-values: 20px, 28px, 36px, 44px, 52px, 60px, 68px, 76px, 84px, 92px;
$item-count: length($padding-values); // 获取列表的长度

@for $i from 1 through $item-count {
  .Virtual-pdLeft-s#{$i} {
    .surely-table-cell-content {
      padding-left: nth($padding-values, $i) !important;
    }
    .vxe-tree--line {
      left: nth($padding-values, $i) !important;
    }
  }
}
@for $i from 1 to 20 {
  .Virtual-pdLeft-s#{$i} {
    position: relative;
    .surely-table-cell-content::after {
      position: absolute;
      top: 0%;
      transform: translateY(-50%);
      content: '';
      height: 100%;
      width: 1px;
      left: ($i - 1) * 8px;
      transform: scaleX(0.8);
      background: #87b2f2;
    }
    .surely-table-cell-content::before {
      position: absolute;
      bottom: 48%;
      content: '';
      height: 1px;
      width: 10px;
      left: ($i - 1) * 8px;
      transform: scale(0.8);
      background: #87b2f2;
    }
  }
}
@for $i from 1 to 30 {
  .cell-line-break-#{$i} {
    .cell-line-break-el {
      @extend .cell-line-break;
      -webkit-line-clamp: #{$i};
    }
  }
}

@for $i from 1 to 8 {
  .Virtual-pdLeft#{$i} {
    position: relative;
    &::after {
      position: absolute;
      // top: 50%;
      top: 0%;
      content: '';
      height: 100%;
      width: 1px;
      left: $i * 8px;
      transform: translateY(-50%) scaleX(0.8);
      background: #87b2f2;
    }
    &::before {
      position: absolute;
      // bottom: -50%;
      bottom: 50%;
      content: '';
      height: 1px;
      width: 10px;
      left: $i * 8px;
      transform: scale(0.8);
      background: #87b2f2;
    }
  }
}
$padding-values: 20px, 28px, 36px, 44px, 52px, 60px, 68px, 76px, 84px, 92px;
$item-count: length($padding-values); // 获取列表的长度

@for $i from 1 through $item-count {
  .Virtual-gsPdLeft#{$i} {
    .surely-table-cell-content {
      padding-left: nth($padding-values, $i) !important;
    }
    .vxe-tree--line {
      left: nth($padding-values, $i) !important;
    }
  }
}
@for $i from 1 to 20 {
  .Virtual-gsPdLeft#{$i} {
    position: relative;
    .surely-table-cell-content::after {
      position: absolute;
      top: 0%;
      transform: translateY(-50%);
      content: '';
      height: 100%;
      width: 1px;
      left: ($i - 1) * 8px;
      transform: scaleX(0.8);
      background: #87b2f2;
    }
    .surely-table-cell-content::before {
      position: absolute;
      bottom: 48%;
      content: '';
      height: 1px;
      width: 10px;
      left: ($i - 1) * 8px;
      transform: scale(0.8);
      background: #87b2f2;
    }
  }
}
//安装费用弹框连接线
@for $i from 0 to 20 {
  .Virtual-pdLeft-ag-noChild-s#{$i} {
    position: relative;
    .surely-table-cell-content {
      .surely-table-append-node {
        display: inline-block !important;
        margin-right: 5px;
        @for $i from 1 to 99 {
          .indent-level-#{$i} {
            padding-left: if($i>4, 25px + $i * 5px, 25px) !important;
          }
        }
      }
    }
    .surely-table-cell-content::after {
      position: absolute;
      top: 0;
      content: '';
      height: 100%;
      bottom: 0;
      width: 1px;
      left: $i * 10px + 2px;
      background: #87b2f2;
    }
    .surely-table-cell-content::before {
      position: absolute;
      bottom: 48%;
      content: '';
      height: 1px;
      // width: $i * 5px;//最子级左侧横线不定长
      width: 10px;
      left: $i * 10px + 2px;
      // transform: scale(0.8);
      background: #87b2f2;
    }
  }
}

@for $i from 0 to 20 {
  .Virtual-pdLeft-ag-noExpand-s#{$i},
  .Virtual-pdLeft-ag-expand-s#{$i} {
    position: relative;
    .surely-table-cell-content {
      height: 100%;
      padding: 0px 0 0px if($i>1, 15px, 20px) !important;
      line-height: 35px;
      .surely-table-append-node {
        display: inline-block !important;
        margin-right: 5px;
        @for $i from 1 to 9 {
          .indent-level-#{$i} {
            padding-left: if($i < 6, $i * 14px, 25px) !important;
          }
        }
      }
    }
    .surely-table-cell-content::before {
      //移动圆圈右边的横线
      position: absolute;
      bottom: 48%;
      content: '';
      height: 1px;
      width: 8px;
      left: $i * 10px + 5px;
      // transform: scale(0.8);
      background: #87b2f2;
    }
    &-nochild {
      width: $i * 5px+2px;
    }
    .ag-vxe-icon-caret-down,
    .ag-vxe-icon-caret-right {
      width: 12px;
      height: 12px;
      display: inline-block;
      text-align: center;
      line-height: 8px;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      transform: translate(-50%, -50%);
      // left: ($i - 1) * 2 * 6px+5px;
      left: $i * 10px;
      border: 1px solid #87b2f2;
      color: #87b2f2;
      font-size: 12px;
    }
    .ag-vxe-icon-caret-down::after,
    .ag-vxe-icon-caret-right::after {
      position: absolute;
      top: -15px;
      transform: translateY(-50%);
      content: '';
      height: 15px;
      bottom: 0;
      width: if($i>1, 1px, 0px);
      left: 7px;
      transform: scaleX(0.8);
      background: #87b2f2;
    }
    .ag-vxe-icon-caret-down:before {
      content: '-';
    }
    .ag-vxe-icon-caret-right:before {
      content: '+';
    }
  }
  //安装展开按钮下册连接线
  .Virtual-pdLeft-ag-expand-s#{$i} {
    .surely-table-cell-content::after {
      //移动圆圈下边的竖线
      position: absolute;
      top: 16px;
      content: '';
      height: 18px;
      bottom: 0;
      width: 1px;
      left: $i * 10px + 12px;
      background: #87b2f2;
    }
  }
  //安装收起按钮下册连接线
  .Virtual-pdLeft-ag-noExpand-s#{$i} {
    .surely-table-cell-content::after {
      //移动圆圈下边的竖线
      position: absolute;
      top: 22px;
      content: '';
      height: 18px;
      bottom: 0;
      width: 1px;
      left: $i * 10px + 2px;
      background: #87b2f2;
    }
  }
}
// @for $i from 0 to 99999{
//   .Virtual-ag-row-s#{$i}{
//     .surely-table-extra-cell {
//     width: 0px;
//     left: ($i + 2)*2 * 10px + 10px !important;
//     z-index:9999999
//     }
//   }
//   .Virtual-ag-row-noChild-s#{$i}{

//     .surely-table-extra-cell {
//     width: 0px;
//     left: ($i + 2)*2 * 10px  !important ;
//     z-index:9999999
//     }
//   }
// }
.first-row {
  .code-color {
    &::after,
    &::before {
      display: none;
    }
  }
}
.zcsb-color {
  color: #7d1dff;
  .code-color {
    color: #7d1dff !important;
  }
  .surely-table-cell-inner {
    color: #7d1dff !important;
  }
}

.confirm-dialog {
  .vxe-modal--body {
    padding: 20px 0 !important;
  }
}

body {
  .vxe-table--render-default.border--full .vxe-table--header-wrapper {
    background-color: #f3f3f3;
  }
  .vxe-table--context-menu-wrapper {
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.16);
    border-radius: 4px;
    border: 0;
    .vxe-context-menu--link {
      color: #000;
      width: 130px;
      padding: 4px 3px;
      border-bottom: 1px solid rgba(213, 213, 213, 1);
    }
    .vxe-context-menu--option-wrapper li {
      border: 0 !important;
      padding: 0 10px;
      &:last-child {
        .vxe-context-menu--link {
          border-bottom: 0;
        }
      }
      &.link--active {
        background: #ececec;
      }
    }
  }
}
/*全局---滚动条样式*/
::-webkit-scrollbar-track-piece {
  //滚动条凹槽的颜色，还可以设置边框属性
  background-color: #eaedf0;
}
::-webkit-scrollbar {
  //滚动条的宽度
  width: 12px; //纵向滚动条的宽度
  height: 10px; //横向滚动条的高度
  //background-color: transparent;
  background: rgba(234, 237, 240, 1);
}
/* 滚动条上下箭头按钮 */
::-webkit-scrollbar-button {
  display: block; /* 必须设为 block */
  height: 8px;
  width: 8px;
  background-color: #eaedf0;
  background-repeat: no-repeat;
}
/* 自定义向上箭头*/
::-webkit-scrollbar-button:single-button:vertical:decrement {
  background-image: url('@/assets/img/jiantoushang.png');
  background-size: 10px 6px;
  background-position: bottom;
}
/* 自定义向下箭头*/
::-webkit-scrollbar-button:single-button:vertical:increment {
  background-image: url('@/assets/img/jiantouxia.png');
  background-size: 10px 6px;
  background-position: top;
}
/* 水平滚动条左右箭头 */
::-webkit-scrollbar-button:single-button:horizontal:decrement {
  background-image: url('@/assets/img/jiantouleft.png');
  background-size: 6px 10px;
  background-position: left;
}
::-webkit-scrollbar-button:single-button:horizontal:increment {
  background-image: url('@/assets/img/jiantouright.png');
  background-size: 6px 10px;
  background-position: right;
}

//:hover::-webkit-scrollbar-thumb {
//  background-color: rgba(24, 144, 255, 0.2);
//  background-clip: padding-box;
//  min-height: 28px;
//  border-radius: 5px;
//}
::-webkit-scrollbar-thumb:hover {
  background-color: var(
    --table-webkit-scrollbar-thumb-hover
  ); //rgba(144, 176, 227, 1)
}
// 评测后要求，滚动条常显, 功能区例外，单独处理
::-webkit-scrollbar-thumb {
  background-color: var(
    --table-webkit-scrollbar-thumb
  ); //rgba(24, 144, 255, 0.2) 变为 rgba(144, 176, 227, 1)
  background-clip: padding-box;
  min-height: 28px;
  border-radius: 5px;
}
// 不需要滚动条常显的能力，加这个样式
.hover-scrollbar-thumb {
  ::-webkit-scrollbar-thumb {
    display: none;
    background-color: transparent;
  }
  :hover::-webkit-scrollbar-thumb {
    background-color: var(--table-webkit-scrollbar-thumb-hover);
    background-clip: padding-box;
    min-height: 28px;
    border-radius: 5px;
    display: block;
  }
}

// 表格编辑样式
.table-edit-common .col--edit {
  .vxe-input--inner {
    border: none !important;
  }
  .vxe-input {
    height: 18px;
    line-height: 1.5;
  }
  .vxe-textarea {
    min-height: 18px;
  }
  .more-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    & + * {
      padding-right: 15px;
    }
  }
  &.col--actived {
    padding: 7px 0;
    background: #fff !important;
    .vxe-cell {
      padding: 0 !important;
      .vxe-input,
      .vxe-textarea {
        padding: 0 10px !important;
        .vxe-input--suffix {
          right: 0.8em !important;
        }
      }
      .ant-picker {
        width: 100%;
        padding: 0 10px !important;
        border: none;
        box-shadow: none;
        background: transparent;
      }
    }
  }
  &.drag-fill-cell {
    background-color: rgba(38, 125, 245, 0.1);
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      width: 100%;
      height: 100%;
      border-left: 2px solid rgba(38, 125, 245, 0.8);
      border-right: 2px solid rgba(38, 125, 245, 0.8);
    }
  }
  &.drag-fill-cell-first {
    &::after {
      border-top: 2px solid rgba(38, 125, 245, 0.8);
    }
  }
  &.drag-fill-cell-last {
    &::after {
      border-bottom: 2px solid rgba(38, 125, 245, 0.8);
    }
  }
  &.cell-selected {
    // padding: 7px 0 !important;
    box-shadow: inset 0px 0px 0px 2px #267df5;
    background: #fff !important;
    &:focus-within {
      // padding: 7px 0 !important;
      box-shadow: inset 0px 0px 0px 1px rgb(38, 125, 245);
    }
  }
}
.priceSelect {
  //载价编辑价格选择弹框需要设置最高
  z-index: 9999 !important;
}
// .table-scrollbar .vxe-table--body-wrapper {
// //表格滚动条默认隐藏，鼠标划过区域展示
//   overflow: hidden !important;
//   &:hover{
//     overflow: auto !important;
//   }
// }

.my-menus-subItem
  .vxe-table--context-menu-clild-wrapper
  .vxe-context-menu--link {
  width: 160px !important;
}

.trends-table-column {
  .vxe-header--column {
    cursor: pointer;
    &:hover {
      opacity: 0.8;
      .icon-close {
        top: 50%;
      }
    }
    .vxe-cell {
      width: 100%;
    }
  }
}

.custom-header {
  .icon-close {
    position: absolute;
    top: -250%;
    transform: translateY(-50%);
    right: 4px;
  }
}
.fix-not-can-edit .vxe-cell--edit-icon {
  display: none;
}

.spin-yyy {
  height: 100% !important;
  // font-size: 12px !important;
  // z-index: 9999 !important;
  .ant-spin-container {
    height: 100% !important;
    // opacity: 1 !important;
  }
  .ant-spin-show-text {
    max-height: 1000px !important;
    font-size: 12px !important;
  }
  .ant-spin-text {
    width: 200px !important;
    height: 30px !important;
    left: 50%;
    transform: translate(-50%, 5px);
    font-size: 12px !important;
    // background-color: rgba(255, 255, 255,.9)
  }

  .ant-spin-blur::after {
    // opacity: 0 !important;
  }
}
.spin-noMask {
  height: 100% !important;
  // font-size: 12px !important;
  .ant-spin-container {
    height: 100% !important;
    opacity: 1 !important;
  }
  .ant-spin-show-text {
    max-height: 1000px !important;
    font-size: 12px !important;
  }
  .ant-spin-text {
    width: 200px !important;
    height: 30px !important;
    left: 50%;
    transform: translate(-50%, 5px);
    font-size: 12px !important;
    background-color: rgba(255, 255, 255, 0.9);
  }

  .ant-spin-blur::after {
    opacity: 0 !important;
  }
}
$padding-values: 0px, 15px, 30px, 45px, 60px, 75px, 90px, 105px;
$item-count: length($padding-values); // 获取列表的长度

@for $i from 1 through $item-count {
  .Virtual-pdLeft#{$i} {
    .vxe-cell--tree-node {
      padding-left: nth($padding-values, $i) !important;
    }
    .vxe-tree--line {
      left: nth($padding-values, $i) !important;
    }
  }
}

.Vertical-line {
  content: '';
  position: absolute;
  bottom: 0;
  // width: .8em;
  width: 1px;
  height: 100%;
  border-width: 0 0 1px 1px;
  border-style: var(--vxe-table-tree-node-line-style);
  border-color: var(--vxe-table-tree-node-line-color);
  pointer-events: none;
}

.Horizontal-line {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  width: 1px;
  border-width: 0 0 1px 1px;
  border-style: var(--vxe-table-tree-node-line-style);
  border-color: var(--vxe-table-tree-node-line-color);
  pointer-events: none;
}

.custom-tree-table {
  .vxe-table {
    .vxe-pulldown {
      width: 100%;
      .vxe-textarea--inner {
        width: 100%;
        height: 100%;
        border: 0;
        outline: none;
      }
    }
    .single-item {
      .vxe-cell {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .projectAttr-item {
      .vxe-cell {
        padding-right: 20px;
      }
    }
    .vxe-cell {
      width: auto !important;
    }
    .column-unit,
    .column-sub,
    .column-qd,
    .column-de {
      position: relative;
      &::after {
        // @extend .Vertical-line;
      }

      .vxe-cell {
        position: relative;
        padding-left: 15px;
        &::after {
          // @extend .Horizontal-line;
        }
      }
    }
  }
}
.custom-vxe-table-tree {
  .vxe-table {
    .vxe-cell--tree-node {
      .vxe-tree--btn-wrapper + .vxe-tree-cell {
        padding-left: 2.5em;
      }
    }
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .vxe-tree--btn-wrapper {
      .vxe-icon-caret-down,
      .vxe-icon-caret-right {
        width: 12px;
        height: 12px;
        display: inline-block;
        text-align: center;
        line-height: 8px;
        border-radius: 50%;
        position: relative;
        top: -4px;
        left: 20px;
        border: 1px solid #87b2f2;
        color: #87b2f2;
        font-size: 12px;
      }
      .vxe-icon-caret-down:before {
        content: '-';
      }
      .vxe-icon-caret-right:before {
        content: '+';
      }
    }
    .row-unit {
      background: #e6dbeb;
    }
    .row-sub {
      background: #efe9f2;
    }
    .row-qd {
      background: #dce6fa;
    }
  }
  .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column {
    background-image:
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5)),
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5));
  }
  .vxe-table--render-default.is--tree-line .vxe-header--column {
    background-image:
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5)),
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5));
  }
  .vxe-table--body {
    border-collapse: collapse;
  }
}
// 审核表格留痕样式
.sh-add {
  color: #287cfa;
}
.sh-del {
  color: #d15add;
}
.sh-modify {
  color: #de3f3f;
}
.row-add {
  color: #287cfa;
}
.row-del {
  color: #d15add;
  text-decoration: line-through;
  .content-tip-box {
    text-decoration: line-through;
  }
}
.row-del-text-decoration {
  text-decoration: line-through;
  .content-tip-box {
    text-decoration: line-through;
  }
}
.row-modify {
  color: #de3f3f;
}
del {
  user-select: none;
}

.original-data-bg {
  background-color: #f8edd4 !important;
}

.dialog-EditExcelDialog {
  .vxe-modal--content {
    padding: 0 2px 4px 2px !important;
  }
}

.project-nosave {
  .vxe-modal--header-right {
    display: flex;
    justify-content: center;
    font-size: 14px;
  }
  .vxe-modal--box {
    top: 40vh !important;
  }
}
.h40-detailed-area {
  height: calc(100% - 40px);
}
.h100-detailed-area {
  height: calc(100% - 5px);
}
.glj-h40-detailed-area {
  height: calc(100% - 40px);
}
.glj-h100-detailed-area {
  height: calc(100% - 0px);
}