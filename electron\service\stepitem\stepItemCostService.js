const {Service} = require('../../../core');
const Log = require('../../../core/log');
const {BaseFeeFileRelation} = require("../../model/BaseFeeFileRelation");
const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const StepItemCostLevelConstant = require("../../enum/StepItemCostLevelConstant");
const BranchProjectDisplayConstant = require("../../enum/BranchProjectDisplayConstant");
const BranchProjectOptionMenuConstant = require("../../enum/BranchProjectOptionMenuConstant");
const {Snowflake} = require("../../utils/Snowflake");
const {MeasureProjectTable} = require("../../model/MeasureProjectTable");
const BranchProjectLevelConstant = require("../../enum/BranchProjectLevelConstant");
const UnitConversion = require("../../enum/UnitConversion");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const {NumberUtil} = require("../../utils/NumberUtil");
const {ParamUtils} = require("../../../core/core/lib/utils/ParamUtils");
const _ = require("lodash");
const ConstantUtil = require('../../enum/ConstantUtil');
const { BaseDe2022 } = require('../../model/BaseDe');
const { BaseSpecialtyMeasures } = require('../../model/BaseSpecialtyMeasures');
const PolicyDocumentTypeEnum = require('../../enum/PolicyDocumentTypeEnum');
const { BaseList } = require('../../model/BaseList');
const { In } = require('typeorm');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const { dialog } = require('electron');
const os = require('os');
const fs = require('fs');
const ConstructionMeasureTypeConstant = require('../../enum/ConstructionMeasureTypeConstant');
const {Tree,TreeNode} = require("../../main_editor/tree");
const InsertStrategy = require('../../main_editor/insert/insertStrategy');
const RemoveStrategy = require("../../main_editor/remove/removeStrategy");
const ReplaceStrategy = require("../../main_editor/replace/replaceStrategy");
const OptionMenuHandler = require("../../main_editor/optionMenuHandler");
const CalculationTool = require("../../unit_price_composition/compute/CalculationTool");
const {UPCContext} = require("../../unit_price_composition/core/UPCContext");
/**
 * 措施项目service
 */
class stepItemCostService extends Service {

    Empt_Line = () => {
        return {
            "kind": StepItemCostLevelConstant.qd,
            "name": "",
            "adjustmentCoefficient" : 1,
            isEmpData: StepItemCostLevelConstant.emptData
        }
    };
    DEFAULT_MEASURE_TYPES = ["安全生产、文明施工费", "其他总价措施项目", "单价措施项目"];
    DEFAULT_MEASURE_TYPES_ENUM = {
        "单价措施项目": 1,
        "安全生产、文明施工费": 2,
        "其他总价措施项目": 3
    }

    Default_Frist_Line = () => { // 安文费
        return {
            "kind": StepItemCostLevelConstant.bt,
            "name": this.DEFAULT_MEASURE_TYPES[0],
            "itemCategory": this.DEFAULT_MEASURE_TYPES[0],
            "constructionMeasureType": this.DEFAULT_MEASURE_TYPES_ENUM[this.DEFAULT_MEASURE_TYPES[0]],
            "measureType": "1",
            "defaultLine": 1,
        }
    };
    Default_SEC_Line = () => { // 其他总价
        return {
            "kind": StepItemCostLevelConstant.bt,
            "name": this.DEFAULT_MEASURE_TYPES[1],
            "itemCategory": this.DEFAULT_MEASURE_TYPES[1],
            "constructionMeasureType": this.DEFAULT_MEASURE_TYPES_ENUM[this.DEFAULT_MEASURE_TYPES[1]],
            "measureType": "2",
            "defaultLine": 2,
        }
    }
    Default_THR_Line = () => {   // 单价措施
        return {
            "kind": StepItemCostLevelConstant.bt,
            "name": this.DEFAULT_MEASURE_TYPES[2],
            "itemCategory": this.DEFAULT_MEASURE_TYPES[2],
            "constructionMeasureType": this.DEFAULT_MEASURE_TYPES_ENUM[this.DEFAULT_MEASURE_TYPES[2]],
            "measureType": "3",
            "defaultLine": 3,
        }
    };

    disPlayType = {
        "0": " ",
        "01": "部",
        "02": "部",
        "03": "清",
        "04": "定"
    }

    constructor(ctx) {
        super(ctx);
        this._baseBranchProjectOptionService = this.service.baseBranchProjectOptionService;
        this._rcjProcess = this.service.rcjProcess;
        this._listFeatureProcess = this.service.listFeatureProcess;
        this._baseFeeFileService = this.service.baseFeeFileService;
        this._unitPriceServices = this.service.unitPriceService;
        this._baseQdDeProcess = this.service.baseQdDeProcess;
        this.rcjProcess = this.service.rcjProcess;
        this.quantitiesService = this.service.quantitiesService;
    }

    itemBillProjectProcess = this.service.itemBillProjectProcess;

    lockQd(constructId, singleId, unitId, qdId) {
        let allDatas = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        this._baseBranchProjectOptionService.lockLine(allDatas, qdId);
        return true;
    }

    unLockQd(constructId, singleId, unitId, qdId) {
        let allDatas = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        this._baseBranchProjectOptionService.unLockLine(allDatas, qdId);
        return true;
    }

    /**
     * 初始化措施项目
     * 生成如下结构：
     * =====================
     *    ·措施项目
     *    ·单价措施项目
     *    ·安全生成、文明施工费
     *    ·其他总价措施项目
     * =====================
     * @param constructId
     * @param singleId
     * @param unitId
     * @param templateName
     */
    async initItemCost(constructId, singleId, unitId,templateName) {
        // 生成第一行数据 措施项目
        let tree = new Tree();
        let item = this._initItem(unitId, "措施项目", StepItemCostLevelConstant.top);
        let root =  new TreeNode(item.sequenceNbr,item);
        tree.addNode(root)
        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //unitProject.measureProjectTables = tree;
        Object.defineProperty(unitProject, 'measureProjectTables', {
            value: tree,
            writable: true, // 禁止重新赋值
            configurable: false, // 可选，进一步防止属性被删除或修改描述符
            enumerable: true // 可选，决定该属性是否可枚举（如在`for...in`循环中出现）
        });
        // 依次插入  安全生成、文明施工费 ; 其他总价措施项目; 单价措施项目 ;
        let insertStrategy = new InsertStrategy({constructId, singleId, unitId,pageType:"csxm"});
        let fri= await insertStrategy.execute({pointLine:item,newLine:this.Default_Frist_Line(),option:"save"});
        let sec= await insertStrategy.execute({pointLine:item,newLine:this.Default_SEC_Line(),option:"save"});
        let thr= await insertStrategy.execute({pointLine:item,newLine:this.Default_THR_Line(),option:"save"});
        // 单价措施项目 ; 安全生成、文明施工费 下插入空清单
        await insertStrategy.execute({pointLine:thr,newLine:this.Empt_Line(),option:"save"});
        // await insertStrategy.execute({pointLine:sec,newLine:this.Empt_Line(),option:"save"});
        // 其他总价措施项目有一批默认清单需要在初始化时候默认插入
        await this._saveDefaultList(unitProject, sec, fri,templateName);
        // //排序
        // let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        // let allData = unit["measureProjectTables"];
        // allData.sortChildren(allData.getNodeById(item.sequenceNbr));
    }

    searchForsequenceNbr(constructId, singleId, unitWorkId, sequenceNbr) {
        return this._baseBranchProjectOptionService.searchForsequenceNbr(sequenceNbr,
          PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId));
    }

    async save(constructId, singleId, unitId, pointLine, newLine, bzhs,option="save") {
        let insertStrategy = new InsertStrategy({constructId, singleId, unitId,pageType:"csxm"});
       let newNode = await insertStrategy.execute({pointLine,newLine,option:option});
        let row = _.cloneDeep(newNode)
        delete row.parent;
        delete row.children;
        delete row.prev;
        delete row.next;
        // return row;
        return {
            "data": row,
            "index": row.index
        };
    }
    /**
     * 新增行
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine
     * @param newLine
     * @returns {*}
     */
    saveOld(constructId, singleId, unitId, pointLine, newLine, bzhs) {
        //措施项目数据
        let csxmDatas = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        // 特殊处理  单价措施项目  安全生成、文明施工费  其他总价措施项目
        csxmDatas = this._dealEmpData(csxmDatas, pointLine, newLine);

        let {
            allDatas,
            newData,
            insertIndex,
            displayIndex
        } = this._baseBranchProjectOptionService.insertLine(pointLine, newLine, null, csxmDatas, null, bzhs);
        newData.adjustmentCoefficient = 1;// 调整系数默认给 1
        // 如果新增的是清单或者定额对当前块数据重新排序
        if (newData.kind === BranchProjectLevelConstant.qd || newData.kind === BranchProjectLevelConstant.de) {
            this._reSortBlockDisplayNum(allDatas, insertIndex, true);
        }
        PricingFileFindUtils.getUnit(constructId, singleId, unitId).measureProjectTables = allDatas;
        // 如果新增是分部 / 子分部 计算单价构成
        if (newData.kind === BranchProjectLevelConstant.fb || newData.kind === BranchProjectLevelConstant.zfb) {
            this._unitPriceServices.caculateQDUnitPrice(constructId, singleId, unitId, newLine.sequenceNbr, true, allDatas);
        }
        // 新数据挂工程量明细
        if (!_.isEmpty(newLine.quantities)) {
            this.quantitiesService.updateDatas(newLine.quantities, newData);
        } else {
            this.quantitiesService.initDatas(newData);
        }
        this.addDeTempDel(constructId, singleId, unitId,newData)
        return {
            "data": newData,
            "index": displayIndex
        };
    }


    addDeTempDel(constructId, singleId, unitWorkId,de){
        //处理临时锁定数据  新增的是定额需要处理
        if(de.kind=="04"){
            let allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId);
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId);
            //获取父级清单
            let itemBillProject = allData.filter(qd=>qd.sequenceNbr==de.parentId)[0];
            if(itemBillProject.tempDeleteFlag){
                de.tempDeleteFlag=true;
                //处理定额数据
                this.service.itemBillProjectOptionService.setDeOrQdTempDelStatust(constructId, singleId, unitWorkId,allData,de,true,unit);
            }
        }
    }

    /**
     * 由列表中更新行内元素
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine
     * @param kind
     * @param indexId
     * @param unit
     */
    async upDateOnList(constructId, singleId, unitWorkId, pointLineId, upDateInfo) {
        return await this._baseBranchProjectOptionService.updateByList(constructId, singleId, unitWorkId, pointLineId, upDateInfo, "csxm");
    }
    searchPonitAndChild(args){
        let {constructId, singleId, unitId, sequenceNbr} =args;
        let csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        return  this._baseBranchProjectOptionService.searchPonitAndChild(sequenceNbr,csxm);
    }
    /**
     * 分页查找数据
     * @param constructId
     * @param singleId
     * @param unitId
     * @param sequenceNbr
     * @param pageNum
     * @param pageSize
     * @returns {*}
     */
    pageSearch(constructId, singleId, unitId, sequenceNbr, pageNum, pageSize, isAllFlag,screenCondition,colorList) {
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let memoryDatas = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        let pageData = this._baseBranchProjectOptionService.pageSearch(pageNum, pageSize, sequenceNbr, memoryDatas, this.disPlayType, isAllFlag,screenCondition);
        this.setReplaceStatus(pageData.data);
        //对数据进行颜色过滤
        if(ObjectUtils.isNotEmpty(colorList)){
            if(!colorList.includes(null)){
                this.getCsxmFilter(pageData,constructId, singleId, unitId,colorList);
            }

        }
        //处理定额收起展开标识
        pageData.data.forEach(item => {
            if (!item.rcjFlag && item.kind == BranchProjectLevelConstant.de) {//编码 materialCode 名称materialName 类别 type 市场价 marketPrice 合价total 合计数量 totalNumber
                let rcj = unit.constructProjectRcjs.filter(itemrcj => itemrcj.deId == item.sequenceNbr && (itemrcj.kind == 4 || itemrcj.kind == 5));
                if (rcj && rcj.length > 0) {
                    if(item.displaySign==0){
                        item.displaySign=1;
                    }
                }else {
                    item.displaySign=0;
                }
            }
        });
        //挂人材机
        let rcjs = [];
        pageData.data.forEach(item => {
            if (!item.rcjFlag &&item.kind == BranchProjectLevelConstant.de) {
                let rcj = unit.constructProjectRcjs.filter(itemrcj => itemrcj.deId == item.sequenceNbr && (itemrcj.kind == 4 || itemrcj.kind == 5));
                if (rcj && rcj.length > 0) {
                     if(item.displaySign==1){
                         rcjs = rcjs.concat(rcj.map(rcj => {
                             let refData = {
                                 type: rcj.type, name: rcj.materialName,
                                 fxCode: rcj.materialCode,
                                 bdCode: rcj.materialCode,
                                 quantity: rcj.totalNumber,
                                 zjfPrice: rcj.marketPrice,
                                 zjfTotal: rcj.total,
                                 zcfee: rcj.kind == 5 ? rcj.marketPrice : 0,
                                 totalZcfee: rcj.kind == 5 ? rcj.total : 0,
                                 sbfPrice: rcj.kind == 4 ? rcj.marketPrice : 0,
                                 sbfTotal: rcj.kind == 4 ? rcj.total : 0
                             }
                             return {...rcj, kind: 90 + Number(rcj.kind), parentId: item.sequenceNbr, ...refData,optionMenu: [BranchProjectOptionMenuConstant.removeLine]};
                         }));
                     }
                }
            }
        });
        return {...pageData, data: pageData.data.concat(rcjs)};
    }

    //查询全量数据  而非只查询 displaySign 状态为open的数据
    pageSearchForAll(constructId, singleId, unitId, sequenceNbr, pageNum, pageSize, isAllFlag,screenCondition) {
        let memoryDatas = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        let pageData = this._baseBranchProjectOptionService.pageSearchForAllData(pageNum, pageSize, sequenceNbr, memoryDatas, this.disPlayType, isAllFlag,screenCondition);
        this.setReplaceStatus(pageData.data);
        return pageData;//this._pageDataAdaptor(pageData, memoryDatas);
    }

    /**
     * 颜色过滤
     */
    getCsxmFilter(result,constructId, singleId, unitWorkId,colorList){
        let data = result.data;
        //获取所有的数据颜色数据
        let allNodes = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId).getAllNodes();
        let mea=[];
        mea = allNodes.filter(m=>colorList.includes(m.color));
        //如果包含none标识需要没有颜色的
        if(colorList.includes("none") ){
            mea.push(...allNodes.filter(m=>ObjectUtils.isEmpty(m.color)));
        }
        //获取每一个数据的父级数据
        let set =new Set();
        mea.forEach(item=>{
            getParnetNode(item,set);
            //如果是清单需要获取他的子集定额数据    如果是定额需要获取他的同级别其他数据
            if(item.kind==BranchProjectLevelConstant.qd){
                let filter = allNodes.filter(de=> de.parentId==item.sequenceNbr);
                filter.forEach(f=> set.add(f.sequenceNbr));
            }
            if(item.kind==BranchProjectLevelConstant.de){
                let filter = allNodes.filter(de=> de.parentId==item.parentId);
                filter.forEach(f=> set.add(f.sequenceNbr));
            }
        });

        //过滤分页查询数据
        let filter1 = data.filter(d=> set.has(d.sequenceNbr));
        result.data=filter1;

        function getParnetNode(node,set) {
            set.add(node.sequenceNbr);
            if(ObjectUtils.isEmpty(node.parentId)){
                return;
            }
            let filter = allNodes.find(n=>n.sequenceNbr==node.parentId);
            getParnetNode(filter,set);
        }

    }


    /**
     * 设置措施项目总数据是否可以替换
     */
    setReplaceStatus(measureProjectTables){
        let measure = measureProjectTables.filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.DJCS);
        for (const item of measure) {
            let { datas } = this.service.baseBranchProjectOptionService._getInfluence(measureProjectTables, item);
            if (!ObjectUtils.isEmpty(datas)) {
               for(const mp of datas){
                   if(mp.kind==BranchProjectLevelConstant.qd || mp.kind==BranchProjectLevelConstant.de){
                         //费用定额不可以替换
                        if(ObjectUtils.isEmpty(mp.isCostDe) || mp.isCostDe==0 ||  mp.isCostDe==4){
                             mp.replaceFlag=true;
                        }
                   }
               }
            }
        }
    }

    /**
     * 展开
     * @param constructId
     * @param singleId
     * @param unitId
     *
     * @param pointLine
     * @returns {boolean}
     */
    open(constructId, singleId, unitId, pointLine) {
        let memoryDatas = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        this._baseBranchProjectOptionService.openLine(pointLine, memoryDatas);
        return true;
    }

    /**
     * 收起
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine
     * @returns {boolean}
     */
    close(constructId, singleId, unitId, pointLine) {
        let memoryDatas = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        this._baseBranchProjectOptionService.closeLine(pointLine, memoryDatas);
        return true;
    }

    /**
     * 批量删
     * @param constructId
     * @param singleId
     * @param unitWorkId
     * @param sequenceNbrs
     */
    async batchDelete(constructId, singleId, unitWorkId, sequenceNbrs) {
        let self = this;
        let allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId);
        let lines = allData.filter((item) => _.includes(sequenceNbrs, item.sequenceNbr));
        let removeStrategy = new RemoveStrategy({constructId, singleId, unitId: unitWorkId, pageType: "csxm"})
        let pset = new Set();
        for (let i = 0; i < lines.length; i++) {
            let line = lines[i];
            if (allData.hasNodeById(line.sequenceNbr)) {
                let parent = await removeStrategy.execute({
                    pointLine: line,
                    isBlock: line.kind != BranchProjectLevelConstant.de
                });
                await removeStrategy.after();
                pset.add(parent.sequenceNbr);
                //将定额的子定额数据进行批量删除
                if (line.kind == BranchProjectLevelConstant.de) {
                    let measureProjectTables = allData.filter(de=>de.parentDeId==line.sequenceNbr);
                    if(ObjectUtils.isNotEmpty(measureProjectTables)){
                        let sequenceNbrs = measureProjectTables.map(de=>de.sequenceNbr);
                        await this.batchDelete(constructId, singleId, unitWorkId,sequenceNbrs);
                    }
                }
            }
        }
        let arr = [...pset];
        let calculationTool = new CalculationTool({constructId, singleId, unitId: unitWorkId, allData: allData});
        arr.forEach(id => {
            if (allData.hasNodeById(id)) {
                calculationTool.calculationChian({sequenceNbr: id})
            }
        })

        return true;
    }

    deepFindMeasureProject(mapMeasureProjectTables, measureProject) {
        let item = mapMeasureProjectTables[measureProject.parentId];
        if (_.isEmpty(item)) {
            return true;
        }
        if (item.kind == BranchProjectLevelConstant.fb && item.constructionMeasureType == 2) {
            return false;
        } else {
            return this.deepFindMeasureProject(mapMeasureProjectTables, item);
        }
    }


    /**
     * 复制
     * @param sequenceNbrs
     */
    copyLine(sequenceNbrs) {
        let constructId = ParamUtils.getPatram("commonParam").constructId;
        let singleId = ParamUtils.getPatram("commonParam").singleId;
        let unitId = ParamUtils.getPatram("commonParam").unitId;
        let { measureProjectTables } = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let filter = (list) => {
            return _.filter(list, (item) => _.includes(sequenceNbrs, item.sequenceNbr));
        }

        let mapMeasureProjectTables = _.mergeWith({}, ..._.map(measureProjectTables, (item) => {
            return { [item.sequenceNbr]: item };
        }), (objValue, srcValue) => {
            return srcValue;
        })
        let copeMeasureProjectTables = filter(measureProjectTables);
        for (let i = 0; i < copeMeasureProjectTables.length; i++) {
            if (!this.deepFindMeasureProject(mapMeasureProjectTables, copeMeasureProjectTables[i])) {
                return false;
            }
        }
        this._baseBranchProjectOptionService.copyLine(sequenceNbrs, "csxm");
        PricingFileFindUtils.setProjectBeforeOption();
        return true;
    }

    cut(sequenceNbrs) {
        let constructId = ParamUtils.getPatram("commonParam").constructId;
        let singleId = ParamUtils.getPatram("commonParam").singleId;
        let unitId = ParamUtils.getPatram("commonParam").unitId;
        let { measureProjectTables } = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let filter = (list) => {
            return _.filter(list, (item) => _.includes(sequenceNbrs, item.sequenceNbr));
        }

        let mapMeasureProjectTables = _.mergeWith({}, ..._.map(measureProjectTables, (item) => {
            return { [item.sequenceNbr]: item };
        }), (objValue, srcValue) => {
            return srcValue;
        })
        let copeMeasureProjectTables = filter(measureProjectTables);
        for (let i = 0; i < copeMeasureProjectTables.length; i++) {
            if (!this.deepFindMeasureProject(mapMeasureProjectTables, copeMeasureProjectTables[i])) {
                return false;
            }
        }
        this._baseBranchProjectOptionService.copyLine(sequenceNbrs, "csxm");
        PricingFileFindUtils.setProjectBeforeOption(false);
        return true;
    }

    /**
     *
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine 粘贴的当前行
     * @returns {Promise<boolean>}
     */
    async pasteLine(constructId, singleId, unitId, pointLine) {
        let copeUnitProject = PricingFileFindUtils.getProjectBuffer();
       let data =  await this.addDataByQdDe(copeUnitProject, constructId, singleId, unitId, pointLine);
        if(!PricingFileFindUtils.getProjectBeforeOption()){
            let {
                measureProjectTables,
            } = copeUnitProject;
            let ids =  measureProjectTables.map(item=>item.sequenceNbr)
            await this.batchDelete(copeUnitProject.constructId, copeUnitProject.spId, copeUnitProject.sequenceNbr,ids);
        }
        return ;
    }

    _getMaxQdCodeNum(baseCode, constructId, singleId, unitId) {
        //这里的 itemBillProjects 和measureProjectTables都是树形结构
        let {
            itemBillProjects,
            measureProjectTables
        } = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let codeList = [];
        let matchListA = itemBillProjects.getAllNodes().filter(item => item.bdCode && item.bdCode.startsWith(baseCode));
        let max = null;
        //条件获取到相对应的清单集合
        //根据清单编码排序
        if (matchListA && matchListA.length > 0) {
            max = _.maxBy(matchListA, function (o) {
                return o.bdCode;
            }).bdCode;
        }
        let matchListB = measureProjectTables.getAllNodes().filter(item => item.fxCode && item.fxCode.startsWith(baseCode));
        if (matchListB && matchListB.length > 0) {
            let b = _.maxBy(matchListB, function (o) {
                return o.fxCode;
            }).fxCode;
            if (max && max < b) max = b;
            if (!max) max = b;
        }
        if (!max) return 0;
        return max.length > 9 ? Number.parseInt(max.substring(9)) : 0;
    }
    /**
     * 抽取公共方法（粘贴、复用组价公用）
     * @param copeUnitProject
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine
     * @returns {Promise<boolean>}
     */
    async addDataByQdDe(copeUnitProject, constructId, singleId, unitId, pointLine, resultMap = {}) {
        if (_.isEmpty(copeUnitProject)) return false;
        let {
            measureProjectTables,
            feeFiles,
            listFeatureList,
            constructProjectRcjs,
            rcjDetailList,
            rcjRules
        } = copeUnitProject;

        if (_.isEmpty(measureProjectTables)) return false;
        //取费文件
        let {
            feeFiles: oldFeefles,
            listFeatureList: listFeatureListold
        } = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let diffFeeFiles = [];
        _.forEach(feeFiles, (item) => {
            let isOk = false;
            for (let i = 0; i < oldFeefles.length; i++) {
                let oItem = oldFeefles[i];
                if (item.feeFileCode == oItem.feeFileCode) {
                    isOk = true;
                }
            }
            if (!isOk) {
                diffFeeFiles.push({...item});
            }
        });
        if (!_.isEmpty(diffFeeFiles)) {
            PricingFileFindUtils.getUnit(constructId, singleId, unitId).feeFiles = oldFeefles.concat(diffFeeFiles);
        }
        //分组
        let groupMeasureProjectTables = _.groupBy(measureProjectTables, (item) => item.kind);
        //清单列表
        let qdlist = groupMeasureProjectTables[BranchProjectLevelConstant.qd];
        //定额列表
        let delist = groupMeasureProjectTables[BranchProjectLevelConstant.de];
        let qdKeyMap = new Map();
        if (!_.isEmpty(qdlist)) {
            //for(let i = qdlist.length - 1; i >= 0; i--){
            for (const item of qdlist) {
                //let item = qdlist[i];
                let fxCode = item.fxCode;
                if (fxCode&&item.fxCode.length > 9) {
                    let baseCode = item.fxCode.substring(0, 9);
                    let maxCodeNum = this._getMaxQdCodeNum(item.fxCode.substring(0, 9), constructId, singleId, unitId);
                    let newCode = maxCodeNum + 1;
                    fxCode = baseCode + _.padStart(newCode, 3, '0');
                }
                let newLine = {...item, sequenceNbr: "", fxCode, bdCode: fxCode};
                let key = item.sequenceNbr + "";
                newLine.sequenceNbr = "";
                //添加清单
                let {data} =await this.save(constructId, singleId, unitId, pointLine, newLine);
                qdKeyMap.set(key, data);
                try {
                    resultMap[data.sequenceNbr] = data;
                } catch (e) {
                }
                //更新清单特征集合
                let itemRcjDetailList = _.filter(listFeatureList, (i) => i.qdId == key);
                for (let i = 0; i < itemRcjDetailList.length; i++) {
                    itemRcjDetailList[i].qdId = data.sequenceNbr;
                }
                if (_.isEmpty(listFeatureListold)) {
                    PricingFileFindUtils.getUnit(constructId, singleId, unitId).listFeatureList = itemRcjDetailList;
                } else {
                    PricingFileFindUtils.getUnit(constructId, singleId, unitId).listFeatureList = listFeatureListold.concat(itemRcjDetailList);
                }

            }
        }
        if (!_.isEmpty(delist)) {
            //拿到人材机明细
            let before = null;//处理顺序
            let beforeItem = null;
            let beforeQdItem = null;
            let fromFeeFileMap = UPCContext.getfeeFilebyPath(constructId + "," + copeUnitProject.sequenceNbr);

            let fromIncrementTemplateListMap = UPCContext.getTemplateListbyPath(constructId, copeUnitProject.sequenceNbr);
            //单价构成下拉列表
            let tagterFeeFileMap = UPCContext.getfeeFilebyPath(constructId + "," + unitId);
            //单价构成模板
            let tagterIncrementTemplateListMap = UPCContext.getTemplateListbyPath(constructId, unitId);
            //拿到人材机明细
            for (let i = 0; i < delist.length; i++) {
                let item = delist[i];
                let newLine = { ...item, sequenceNbr: '', isAutoCost: false };
                let key = item.sequenceNbr + "";
                let qdItem = qdKeyMap.get(item.parentId);
                //如果清单不存在
                let res = {};
                //清单不存在的情况下 当前行就是传进来的行
                if (_.isEmpty(qdItem)) {
                    res =await this.save(constructId, singleId, unitId, pointLine, newLine);

                } else {
                    //清单存在 则插入的当前行是 清单行
                    res =await this.save(constructId, singleId, unitId, qdItem, newLine);
                }
                try {
                    resultMap[res.data.sequenceNbr] = res.data;
                } catch (e) {
                }
                if (!res.data.bdCode) {
                    continue;
                }
                //处理单价构成模板
                if (res.data.qfCode && res.data.qfCode.includes("_")) {
                    let t = _.cloneDeep(fromFeeFileMap.get(res.data.qfCode));
                    //如果目标单位里不存在 则添加
                    if (!tagterFeeFileMap.has(res.data.qfCode)) {
                        UPCContext.qfCodeMap.set(constructId + "," + unitId + res.data.qfCode, res.data.costFileCode);
                        tagterFeeFileMap.set(res.data.qfCode, t);
                        let t1 = _.cloneDeep(fromIncrementTemplateListMap.get(res.data.qfCode));
                        tagterIncrementTemplateListMap.set(res.data.qfCode, t1);
                    }
                }
                //处理人材机
                if (!_.isEmpty(constructProjectRcjs)) {
                    //获取人材机
                    let itemConstructProjectRcjs = _.filter(constructProjectRcjs, (i) => i.deId == key) || [];
                    //获取人材机明细
                    let itemRcjDetailList = _.filter(rcjDetailList, (i) => i.deId == key) || [];
                    let result = await this.service.rcjProcess.pasteRcj(_.cloneDeep(itemConstructProjectRcjs), _.cloneDeep(itemRcjDetailList), constructId, singleId, unitId, res.data.sequenceNbr);
                    let map = new Map();
                    if (ObjectUtils.isNotEmpty(result)) {
                        map = result.map;
                    }
                    //如果复制的存在标准换算信息
                    if (!_.isEmpty(rcjRules)) {
                        let copeRcjRules = {};
                        _.forEach(itemConstructProjectRcjs, (item) => {
                            let copeRules = rcjRules[item.sequenceNbr];
                            if (!_.isEmpty(copeRules)) {
                                copeRcjRules[map.get(item.sequenceNbr).sequenceNbr] = _.cloneDeep(copeRules);
                            }
                        })
                        let rcnewjRules = PricingFileFindUtils.getUnit(constructId, singleId, unitId).rcjRules;
                        if (_.isEmpty(rcnewjRules)) {
                            PricingFileFindUtils.getUnit(constructId, singleId, unitId).rcjRules = copeRcjRules;
                        } else {
                            PricingFileFindUtils.getUnit(constructId, singleId, unitId).rcjRules = {...rcnewjRules, ...copeRcjRules};
                        }
                    }
                }
                //处理换算记录
                if (!_.isEmpty(copeUnitProject.conversionInfoList)) {
                    //换算信息数据
                    let itemConversionInfoList = _.filter(copeUnitProject.conversionInfoList, (i) => i.deId == key);
                    for (let i = 0; i < itemConversionInfoList.length; i++) {
                        itemConversionInfoList[i].deId = res.data.sequenceNbr;
                    }
                    let conversionInfoList = PricingFileFindUtils.getUnit(constructId, singleId, unitId).conversionInfoList;
                    if (_.isEmpty(conversionInfoList)) {
                        PricingFileFindUtils.getUnit(constructId, singleId, unitId).conversionInfoList = itemConversionInfoList;
                    } else {
                        PricingFileFindUtils.getUnit(constructId, singleId, unitId).conversionInfoList = conversionInfoList.concat(itemConversionInfoList);
                    }
                }

                if (!_.isEmpty(copeUnitProject.defaultConcersions)) {
                    if (copeUnitProject.defaultConcersions[key]) {
                        let item = copeUnitProject.defaultConcersions[key];
                        let defaultConcersions = PricingFileFindUtils.getUnit(constructId, singleId, unitId).defaultConcersions;
                        if (_.isEmpty(defaultConcersions)) {
                            PricingFileFindUtils.getUnit(constructId, singleId, unitId).defaultConcersions = {[res.data.sequenceNbr]: item};
                        } else {
                            PricingFileFindUtils.getUnit(constructId, singleId, unitId).defaultConcersions[res.data.sequenceNbr] = item;
                        }
                    }
                }
            }
        }
        //处理复制粘贴后的 QDL关联
        let newitemBillProjects = PricingFileFindUtils.getUnit(constructId, singleId, unitId).measureProjectTables;
        let alldata = newitemBillProjects.getAllNodes()
        if (!_.isEmpty(newitemBillProjects)) {
            //处理清单 QDL
            for (let i = 0; i < alldata.length; i++) {
                let item = alldata[i];
                if (item.kind === BranchProjectLevelConstant.qd) {
                    await this._baseBranchProjectOptionService.updateQdToDeQDL(constructId, singleId, unitId, newitemBillProjects, item.sequenceNbr);
                    this.service.unitPriceService.caculateQDUnitPrice(constructId, singleId, unitId, item.sequenceNbr,
                        true, newitemBillProjects);
                }
            }
        }

        await this.service.autoCostMathService.autoCostMath({
            constructId,
            singleId,
            unitId
        });
        return true;
    }
   async removeLine(constructId, singleId, unitWorkId, pointLine, isBlock){
       if(pointLine.kind==BranchProjectLevelConstant.de){
           pointLine = this.clearQdData(pointLine,constructId, singleId, unitWorkId);
           if(ObjectUtils.isEmpty(pointLine)){
               return ;
           }
       }
       let  removeStrategy =new RemoveStrategy({constructId, singleId, unitId: unitWorkId, pageType: "csxm"})
       await removeStrategy.execute({pointLine,isBlock});
       await removeStrategy.after();
       //获取parentNode
       let csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId);
       OptionMenuHandler.fillUpDown(csxm.getNodeById(pointLine.parentId));

       //如果删除的是定额 将定额的子定额数据进行批量删除
       if (pointLine.kind == BranchProjectLevelConstant.de) {
           let measureProjectTables = csxm.filter(de=>de.parentDeId==pointLine.sequenceNbr);
           if(ObjectUtils.isNotEmpty(measureProjectTables)){
               let sequenceNbrs = measureProjectTables.map(de=>de.sequenceNbr);
               await this.batchDelete(constructId, singleId, unitWorkId,sequenceNbrs);
           }
       }
       return true;
   }

    clearQdData(pointLineDe,constructId, singleId, unitWorkId){
        let allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId);
        //获取定额对应清单
        let pointLine =allData.getNodeById(pointLineDe.parentId);
        if(ObjectUtils.isEmpty(pointLine)){
            return pointLine;
        }
        if(pointLine.tempDeleteFlag){
            let children = pointLine.children;
            if(children.length==1){
                return pointLine;
            }
        }
        return pointLineDe;

    }

    /**
     * 删除
     * @param constructId
     * @param singleId
     * @param unitWorkId
     * @param pointLine
     * @param isBlock
     * @returns {boolean}
     */
    async removeLineOld(constructId, singleId, unitWorkId, pointLine, isBlock) {
        let allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId);
        let removedRes
        let args = {};
        args["constructId"] = constructId;
        args["singleId"] = singleId;
        args["unitId"] = unitWorkId;
        if (!isBlock) {

            removedRes =  await this._baseBranchProjectOptionService.removeLine(pointLine, allData, args);
            // 删除 措施项目处理单独行 后续逻辑
            this.itemBillProjectProcess.delLineRelevantData(pointLine, allData, constructId, singleId, unitWorkId);
            this._unitPriceServices.deleteFee(constructId, singleId, unitWorkId, pointLine.sequenceNbr);
        } else {
            removedRes = await this._baseBranchProjectOptionService.removeLineBlock(pointLine, allData, influenceLines => {
                // 删除 措施项目处理多行 后续逻辑
                this.itemBillProjectProcess.delBlockRelevantData(pointLine, allData, constructId, singleId, unitWorkId);
                for (let i = 0; i < influenceLines.length; ++i) {
                    this._unitPriceServices.deleteFee(constructId, singleId, unitWorkId, influenceLines[i].sequenceNbr);
                }
            }, args);
        }
        if (pointLine.kind === BranchProjectLevelConstant.qd || pointLine.kind === BranchProjectLevelConstant.de) {
            let parentLine = this._findLine(allData, pointLine.parentId);
            if (parentLine) {
                if (parentLine.kind === BranchProjectLevelConstant.qd) {
                    this._unitPriceServices.caculateQDUnitPrice(constructId, singleId, unitWorkId, parentLine.sequenceNbr, true, allData);
                } else {
                    this._unitPriceServices.caculateFBUnitPrice(constructId, singleId, unitWorkId, parentLine.sequenceNbr, true, allData);
                }
            }
        }
        this._reSortBlockDisplayNum(removedRes, 0, true);
        PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId).measureProjectTables = removedRes;

        // 单价构成
        if (pointLine.kind === BranchProjectLevelConstant.qd || pointLine.kind === BranchProjectLevelConstant.de) {
            let parentLine = this._findLine(allData, pointLine.parentId);
            if (parentLine) {
                if (parentLine.kind === BranchProjectLevelConstant.qd) {
                    this._unitPriceServices.caculateQDUnitPrice(constructId, singleId, unitWorkId, parentLine.sequenceNbr, true, removedRes);
                } else {
                    this._unitPriceServices.caculateFBUnitPrice(constructId, singleId, unitWorkId, parentLine.sequenceNbr, true, removedRes);
                }
            }
        }
        return true;
    }

    updateQdFeature(constructId, singleId, unitId, pointLine, updateStr) {
        pointLine = this._findLine(PricingFileFindUtils.getCSXM(constructId, singleId, unitId), pointLine.sequenceNbr);
        this.service.baseBranchProjectOptionService.updateQdFeature(constructId, singleId, unitId, pointLine, updateStr);
    }
    async replaceFromIndexPage(constructId, singleId, unitWorkId, selectId, replaceId, type, conversionCoefficient, kind, unit, libraryCode) {
        let replaceStrategy = new ReplaceStrategy({constructId, singleId, unitId:unitWorkId,pageType:"csxm"})
        let newNode = await  replaceStrategy.execute({selectId,replaceId,unit,kind,libraryCode});
        let row = _.cloneDeep(newNode)
        delete row.parent;
        delete row.children;
        delete row.prev;
        delete row.next;
        return row;
    }
    /**
     * 索引界面点击 替换
     * @param constructId
     * @param singleId
     * @param unitWorkId
     * @param selectId 索引
     * @param replaceId 选中行
     * @param type      type 接口位置（1编辑区 2明细区）
     * @param conversionCoefficientk 人材机转换系数 只有替换人材机时候才有
     * @param kind      替换类型  03清单 04定额
     * @return {Promise<void>}
     */
    async replaceFromIndexPageOld(constructId, singleId, unitWorkId, selectId, replaceId, type, conversionCoefficient, kind, unit, libraryCode) {
        let is2022 = libraryCode.startsWith(ConstantUtil.YEAR_2022);
        // 替换 清单 定额
        if (type === 1) { // 操作区
            let allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId);
            let pointLine = this._findLine(allData, replaceId);
            if (!pointLine.rcjFlag || pointLine.rcjFlag != 1) {
                // 操作区 清单 定额 替换
                let res = await this._baseBranchProjectOptionService.replaceFronIndexPage(allData, constructId, singleId, unitWorkId, selectId, replaceId, type, conversionCoefficient, kind, unit, is2022);
                return res;
            } else {
                // 替换操作区人材机
                let res = await this._baseBranchProjectOptionService.upDateRcjLine(PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId), replaceId,
                  constructId, singleId, unitWorkId,
                  selectId, is2022);
                return res;
            }
        } else { // 替换明细区人材机
            let {
                deLine,
                rcj
            } = await this.rcjProcess.upDateRcj(PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId)
              , constructId, singleId, unitWorkId,
              selectId, replaceId);
            // 根据人材机变化，修改状态 换 定
            let baseRcjs = this.rcjProcess.getBaseRcjInfoByDeId(deLine.standardId);
            let rcjs = this.rcjProcess.queryRcjDataByDeId(deLine.sequenceNbr, constructId, singleId, unitWorkId);
            if (!deLine.appendType) {
                deLine.appendType = [];
            }
            deLine.appendType = deLine.appendType.filter(a => a !== "换");
            if (!this.rcjProcess.isSameRcj(baseRcjs, rcjs)) {
                deLine.appendType.push("换");
            }

            return rcj.sequenceNbr;
        }
    }

    /**
     * 从清单定额索引中点替换-完整版，包含插入数据后触发的事件
     * @param args 详见stepItemCostController.replaceFromIndexPage的args
     */
    async replaceFromIndexPageFullEdition(args) {
        let {
            constructId,
            singleId,
            unitWorkId,
            unitId,
            selectId,
            replaceId,
            type,
            conversionCoefficient,
            kind,
            unit,
            libraryCode
        } = args;
        if (!unitWorkId) {
            unitWorkId = unitId;
        }
        let res = await this.replaceFromIndexPage(constructId, singleId, unitWorkId, selectId, replaceId, type, conversionCoefficient, kind, unit, libraryCode);
        await this.service.management.sycnTrigger("unitDeChange");
        return res;
    }
    async fillDataFromIndexPage(constructId, singleId, unitWorkId, pointLine, createType, indexId, unit, rcjFlag, bzhs, type, isInvokeCountCostCodePrice, libraryCode) {
        let newLine = {"kind": createType};
        let insertStrategy = new InsertStrategy({constructId, singleId, unitId:unitWorkId,pageType:"csxm"});
        let line = await insertStrategy.execute({pointLine,indexId,newLine, libraryCode, rcjFlag, unit,option:"insert"});
        let  row = _.cloneDeep(line)
        delete row.parent;
        delete row.children;
        delete row.prev;
        delete row.next;
        return {
            "data": row,
            "index": row.index,
        };
    }
    /**
     * 从索引界面添加数据
     * @param unitId
     * @param name
     * @param kind
     * @param isInvokeCountCostCodePrice  是否调用费用汇总计算的方法
     * @private
     */
    async fillDataFromIndexPageOld(constructId, singleId, unitWorkId, pointLine, createType, indexId, unit, rcjFlag, bzhs, type, isInvokeCountCostCodePrice, libraryCode) {
        let res;
        // 1. 如果是新增情况，先新增数据
        if (pointLine.kind !== createType || pointLine.fxCode /*|| (rcjFlag && pointLine.rcjFlag !== rcjFlag)*/) {
            let saveRes = this.save(constructId, singleId, unitWorkId, pointLine, { "kind": createType }, bzhs);
            pointLine = saveRes.data;
            pointLine.rcjFlag = rcjFlag;
            res = saveRes;
        } else {
            // 如果是前端传入数据，将pointLine 转变为内存数据
            let webIndex = pointLine.index;
            pointLine = this._findLine(PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId), pointLine.sequenceNbr);
            pointLine.index = webIndex;
            pointLine.rcjFlag = rcjFlag;
        }
        // 2. 修改数据
        await this._baseBranchProjectOptionService.updateFromIndexPage(PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId),
          constructId, singleId, unitWorkId, pointLine,
          {
              "indexId": indexId,
              "is2022": libraryCode.startsWith(ConstantUtil.YEAR_2022),
              "unit": unit
          }, type, isInvokeCountCostCodePrice);
        // 新数据挂工程量明细
        this.quantitiesService.initDatas(pointLine);
        if (!res) {
            res = {
                "data": pointLine,
                "index": pointLine.index
            }
        }

        this.service.conversionDeService.initDef(constructId, singleId, unitWorkId, pointLine.sequenceNbr);
        //处理定额临时状态数据
        this.addDeTempDel(constructId, singleId, unitWorkId,res.data);
        return res;
    }

    /**
     * 从清单定额索引中点击插入-完整版，包含插入数据后触发的事件
     * @param args 详见stepItemCostController.fillFromIndexPage的args
     */
    async fillDataFromIndexPageFullEdition(args) {
        let { constructId, singleId, unitId, pointLine, kind, indexId, unit, rcjFlag, libraryCode } = args;
        let res = await this.fillDataFromIndexPage(constructId, singleId, unitId, pointLine, kind, indexId, unit, rcjFlag, null, null, null, libraryCode);
        await this.service.management.sycnTrigger("unitDeChange");
        return res;
    }

    _initItem(unitId, name, kind) {
        let initData = new MeasureProjectTable();
        initData.sequenceNbr = Snowflake.nextId();
        initData.name = name;
        initData.kind = kind;
        initData.unitId = unitId;
        initData.displayStatu = BranchProjectDisplayConstant.displayMax;
        initData.displaySign = BranchProjectDisplayConstant.open;
        return initData;
    }

    /**
     * 分页查询结果适配
     * 措施项目中，子分部下不能新建子分部
     * @param pageData
     * @returns {undefined}
     * @private
     */
    _pageDataAdaptor(pageData, allData) {
        let datas = pageData.data;
        let parents = {};
        if (!datas || datas.length === 0) {
            return pageData;
        }
        for (let index = 0; index < datas.length; ++index) {
            let lineData = datas[index];
            let nextLine = datas[index + 1];
            if (lineData.kind === StepItemCostLevelConstant.top) {
                lineData.optionMenu = lineData.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.addListItem);
            }
            if (lineData.kind === StepItemCostLevelConstant.bt) {
                if (nextLine && nextLine.parentId === lineData.sequenceNbr) {
                    lineData.optionMenu = lineData.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.removeLine);
                }
            }
            if (lineData.kind === StepItemCostLevelConstant.zx) {
                lineData.optionMenu = lineData.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.addChildDivision
                  && menu !== BranchProjectOptionMenuConstant.addDivision);
            }
            if (lineData.kind === StepItemCostLevelConstant.qd) {
                let parent = parents[lineData.parentId];
                if (!parent) {
                    parent = this._findLine(allData, lineData.parentId);
                    parents[lineData.parentId] = parent;
                }
                if (parent.kind === StepItemCostLevelConstant.bt) {
                    lineData.optionMenu.push(BranchProjectOptionMenuConstant.addChildDivision);
                }
            }
            if (lineData.measureType === "1") {
                lineData.optionMenu = lineData.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.removeLine
                  && menu !== BranchProjectOptionMenuConstant.removeBlock);
            }
        }
        return pageData;
    }

    /**
     * 对数据重新排序号
     * @param res
     * @param beginListItemNum
     * @private
     */
    _reSortBlockDisplayNum(res, beginIndex, rebuildAll) {
        let lastListItemNum = 0;
        let lastQuotaLastNum = 0;
        let loopBegin = beginIndex;
        if (beginIndex !== 0) {
            for (; loopBegin > 0; --loopBegin) {
                if (res[loopBegin].kind === StepItemCostLevelConstant.bt) {
                    break;
                }
            }
        }
        if (!res[++loopBegin]) return;

        for (let index = loopBegin; index < res.length; ++index) {
            let lineData = res[index];
            if (lineData.kind === StepItemCostLevelConstant.bt) {
                if (!rebuildAll) {
                    break
                }
                lastListItemNum = 0;
                lastQuotaLastNum = 0;
            }
            if (lineData.kind === StepItemCostLevelConstant.qd) {
                lastListItemNum += 1;
                lineData.dispNo = lastListItemNum + "";
                lastQuotaLastNum = 0
                continue;
            }
            if (lineData.kind === StepItemCostLevelConstant.de) {
                lastQuotaLastNum += 1;
                lineData.dispNo = lastListItemNum + "." + lastQuotaLastNum;
            }
        }
    }

    _dealEmpData(memoryDatas, pointLine, newLine) {
        return memoryDatas;
    }

    getInsertPointLineByCurrentName(name) {
        let constructId = ParamUtils.getPatram("commonParam").constructId;
        let singleId = ParamUtils.getPatram("commonParam").singleId;
        let unitId = ParamUtils.getPatram("commonParam").unitId;
        let allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);

        let index = {
            "安全生产、文明施工费": 0,
            "其他总价措施项目": 1,
            "单价措施项目": 2,
        }
        let findLineNameIndex = index[name] - 1;
        if (findLineNameIndex < 0) {
            return allData[0];
        }

        let qds = allData.filter(f => f.kind === "01");
        return qds.filter(f => f.name === this.DEFAULT_MEASURE_TYPES[index[name] - 1])[0];
    }

    async _saveDefaultList(unitProject, thr, sec,templateName) {
        let measureList;
        let constructId = unitProject.constructId;
        let singleId = unitProject.spId;
        let unitId = unitProject.sequenceNbr;
        let constructMajorType = unitProject.constructMajorType;
        if(ObjectUtils.isEmpty(templateName)) {
            let condition = {
                specialtyCode: constructMajorType,
                quotaStandard: PricingFileFindUtils.is22UnitById(constructId, singleId, unitId) ? ConstantUtil.DE_STANDARD_22 : ConstantUtil.DE_STANDARD_12
            }
            // 园林工程 和绿化工程 使用的是同一套模版
            /*if("园林绿化工程" == constructMajorType){
                condition["cslbName"] = unitProject.secondInstallationProjectName;
            }*/
            measureList = await this.app.appDataSource.getRepository(BaseSpecialtyMeasures).find({
                where: condition,
                order: {measuresType: "ASC", dataOrder: "ASC"}
            });
        }
        else {
            measureList = await this.app.appDataSource.getRepository(BaseSpecialtyMeasures).find({
                where: {
                    quotaStandard: PricingFileFindUtils.is22UnitById(constructId, singleId, unitId) ? ConstantUtil.DE_STANDARD_22 : ConstantUtil.DE_STANDARD_12,
                    templateName: templateName,
                },
                order: {measuresType: "ASC", dataOrder: "ASC"}
            });
        }
        let bdCodes = measureList.map(obj => obj.bdCode);
        let sqlres = await this.app.appDataSource.getRepository(BaseList).find({ where: { bdCodeLevel04: In(bdCodes) } });
        //排序:由于后面的save方法是插入方式，此处排序实现倒叙排列
        // sqlres.sort((a, b)=> bdCodes.indexOf(b.zjcsClassCode) - bdCodes.indexOf(a.zjcsClassCode));
        sqlres.sort((a, b)=> a.zjcsClassCode -b.zjcsClassCode);
        let hasZero = false;
        let insertStrategy = new InsertStrategy({constructId, singleId, unitId,pageType:"csxm"});
        for (let index = 0; index < sqlres.length; ++index) {
            let rowData = sqlres[index];
            let lineData = {
                "name": rowData.bdNameLevel04,
                "kind": StepItemCostLevelConstant.qd,
                "fxCode": rowData.bdCodeLevel04,
                "standardId": rowData.sequenceNbr,
                "zjcsClassCode": rowData.zjcsClassCode,
                "zjcsClassName": rowData.zjcsClassName,
                "unit": "项",
                "adjustmentCoefficient" : 1
            }
            if (rowData.zjcsClassCode === "0") { // 安文费下的那一行数据
                await insertStrategy.execute({pointLine:sec,newLine:lineData,indexId:rowData.sequenceNbr,option:"insert"});
                //await this.save(constructId, singleId, unitId, sec, lineData,null,"insert");
                hasZero = true;
                continue;
            }
            await insertStrategy.execute({pointLine:thr,newLine:lineData,indexId:rowData.sequenceNbr,option:"insert"});
            //await this.save(constructId, singleId, unitId, thr, lineData,null,"insert");
        }
        if (!hasZero) {

            await insertStrategy.execute({pointLine:sec,newLine:this.Empt_Line(),option:"save"});
            //await this.save(constructId, singleId, unitId, sec, this.Empt_Line(),null,"save");
        }
    }
    async _saveDefaultListOld(unitProject, thr, sec,templateName) {

        let measureList;
        let constructId = unitProject.constructId;
        let singleId = unitProject.spId;
        let unitId = unitProject.sequenceNbr;
        let constructMajorType = unitProject.constructMajorType;

        if(ObjectUtils.isEmpty(templateName)) {
            let condition = {
                specialtyCode: constructMajorType,
                quotaStandard: PricingFileFindUtils.is22De(constructId) ? ConstantUtil.DE_STANDARD_22 : ConstantUtil.DE_STANDARD_12,
            }
            if("园林绿化工程" == constructMajorType){
                condition["cslbName"] = unitProject.secondInstallationProjectName;
            }
            measureList = await this.app.appDataSource.getRepository(BaseSpecialtyMeasures).find({
                where: condition,
                order: {measuresType: "ASC", dataOrder: "ASC"}
            });
        }
        else {
            measureList = await this.app.appDataSource.getRepository(BaseSpecialtyMeasures).find({
                where: {
                    quotaStandard: PricingFileFindUtils.is22De(constructId) ? ConstantUtil.DE_STANDARD_22 : ConstantUtil.DE_STANDARD_12,
                    templateName: templateName,
                },
                order: {measuresType: "ASC", dataOrder: "ASC"}
            });
        }

        let bdCodes = measureList.map(obj => obj.bdCode);
        let sqlres = await this.app.appDataSource.getRepository(BaseList).find({ where: { bdCodeLevel04: In(bdCodes) } });
        //排序:由于后面的save方法是插入方式，此处排序实现倒叙排列
        sqlres.sort((a, b)=> bdCodes.indexOf(b.bdCodeLevel04) - bdCodes.indexOf(a.bdCodeLevel04));
        // let sql = "select sequence_nbr, library_code, zjcs_class_code, bd_name_level04, bd_code_level04, zjcs_label_name, zjcs_class_code,unit\n" +
        //     "from base_list\n" +
        //     "where zjcs_label_name is not null\n" +
        //     "  and zjcs_label_name like ? || ',%' or zjcs_label_name like '%,'||? or zjcs_label_name = ? \n" +
        //     "order by zjcs_class_code + 0 desc";
        // let sqlres = this.app.betterSqlite3DataSource.prepare(sql).all(constructMajorType, constructMajorType, constructMajorType);
        //console.log(sqlres);
        let hasZero = false;
        for (let index = 0; index < sqlres.length; ++index) {
            let rowData = sqlres[index];
            let lineData = {
                "name": rowData.bdNameLevel04,
                "kind": StepItemCostLevelConstant.qd,
                "fxCode": rowData.bdCodeLevel04,
                "standardId": rowData.sequenceNbr,
                "zjcsClassCode": rowData.zjcsClassCode,
                "zjcsClassName": rowData.zjcsClassName,
                "unit": "项"
            }
            if (rowData.zjcsClassCode === "0") { // 安文费下的那一行数据
                let newData = await this.save(constructId, singleId, unitId, sec, lineData);
                newData.fxCode = this._baseBranchProjectOptionService.getQdCode(constructId, singleId, unitId, newData.fxCode);
                hasZero = true;
                continue;
            }
            let newData = this.save(constructId, singleId, unitId, thr, lineData).data;
            newData.fxCode = this._baseBranchProjectOptionService.getQdCode(constructId, singleId, unitId, newData.fxCode);
            await this.service.listFeatureProcess.saveBatchToFbFxQdFeature(rowData.libraryCode, rowData.bdCodeLevel04,
              newData.sequenceNbr,
              constructId, singleId, unitId);
        }
        if (!hasZero) {
            let newData = this.save(constructId, singleId, unitId, sec, this.Empt_Line()).data;
            newData.fxCode = this._baseBranchProjectOptionService.getQdCode(constructId, singleId, unitId, newData.fxCode);
        }
    }

    _findLine(allData, id) {
        if (!allData) {
            return
        }
        return  allData.getNodeById(id);
        /*for (let i = 0; i < allData.length; ++i) {
            if (allData[i].sequenceNbr === id) {
                return allData[i];
            }
        }*/
    }

    async getMeasureTemplates(args) {
        let { constructId,singleId, unitId } = args;

        let quota_standard = '12'
        if(PricingFileFindUtils.is22UnitById(constructId,singleId, unitId)){
            quota_standard = '22'
        }


        let result = this.app.betterSqlite3DataSource
          .prepare('SELECT template_name FROM base_specialty_measures where quota_standard = ? group by template_name')
          .all(quota_standard);
        return result;
    }

    async getBaseListByTemplate(args)
    {
        let {constructId,singleId, unitId, templateName } = args;
        let measureBaseMap = new Map();
        measureBaseMap.set(ConstructionMeasureTypeConstant.AWF,await this._getMeasuresBaseList(constructId,singleId, unitId, templateName,ConstructionMeasureTypeConstant.AWF));
        measureBaseMap.set(ConstructionMeasureTypeConstant.ZJCS,await this._getMeasuresBaseList(constructId,singleId, unitId, templateName,ConstructionMeasureTypeConstant.ZJCS));
        return measureBaseMap;

    }
    async _getMeasuresBaseList(constructId,singleId, unitId, templateName,type)
    {
        let measureList = await this.app.appDataSource.getRepository(BaseSpecialtyMeasures).find({
            where: {
                quotaStandard: PricingFileFindUtils.is22UnitById(constructId,singleId, unitId) ? ConstantUtil.DE_STANDARD_22 : ConstantUtil.DE_STANDARD_12,
                templateName: templateName,
                measuresType: type,
            }
        });
        let bdCodes = measureList.map(obj => obj.bdCode);
        let sqlres = await this.app.appDataSource.getRepository(BaseList).find({ where: { bdCodeLevel04: In(bdCodes) } });
        //排序
        sqlres.sort((a, b)=> bdCodes.indexOf(a.bdCodeLevel04) - bdCodes.indexOf(b.bdCodeLevel04));
        return sqlres;
    }
    /**
     * 打开系统级保存文件位置选择框
     */
    async openSaveDialog(defaultPath) {

    }

    async getTemplateBaseDir()
    {
        const baseDataDir = `${os.homedir()}\\.xilidata\\userHistory.json`;
        const data = fs.readFileSync(baseDataDir, 'utf8');
        const userHistoryData = JSON.parse(data);
        let result = {content:userHistoryData.DEF_SAVE_PATH}
        if (!fs.existsSync(directoryPath)) {
            await fs.mkdir(directoryPath, { recursive: true });
        }
        return `${templateDir}\\template`;
    }

    /**
     * to-do 本方法来源自 : commonService.getSetStoragePath(constructName);
     *          在时间允许的情况下 需要重构
     * 获取默认存储路径
     */
    async getSetStoragePath(fileName) {

        let templateDir = this.getTemplateBaseDir();

        //读取数据

        if (!fs.existsSync(templateDir)) {
            await fs.mkdir(templateDir, { recursive: true });
        }
        //此处需要判断是否登录 正常登录  离线登录  未登录
        if(ObjectUtils.isEmpty(global.idInformation)){
            //未登录
            if (ObjectUtils.isEmpty(fileName)){
                return `${templateDir}`;
            }else {
                return `${templateDir}\\${fileName}.ysc`;
            }
        }else {
            //获取用户信息
            let sequenceNbr  = global.idInformation.sequenceNbr;
            let identity  = global.idInformation.identity;
            if (ObjectUtils.isEmpty(fileName)){
                return `${templateDir}\\${sequenceNbr}\\${identity}`;
            }else {
                return `${templateDir}\\${sequenceNbr}\\${identity}\\${fileName}.ysc`;
            }
        }


    }

}



stepItemCostService.toString = () => "[class stepItemCostService]"
module.exports = stepItemCostService
