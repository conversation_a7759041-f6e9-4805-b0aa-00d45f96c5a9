const {Service} = require('../../../core');
const {BaseRcj2022} = require("../models/BaseRcj2022");
const {GsBaseClpb} = require('../models/GsBaseClpb');
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ArrayUtil} = require("../utils/ArrayUtil");
const ResourceConstants = require('../constants/ResourceConstants');
const { GsBaseJxpb } = require('../models/GsBaseJxpb');
const {GljRcj} = require("../models/GljRcj");
const {ResponseData} = require("../utils/ResponseData");
const {SqlUtils} = require("../../../electron/utils/SqlUtils");
const QdDeStandardEnum = require("../../../electron/enum/QdDeStandardEnum");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const UnitConstructMajorTypeConstants = require("../constants/UnitConstructMajorTypeConstants");


/**
 * 定额册 service
 * @class
 */
class GljBaseRcjService extends Service{

    constructor(ctx) {
        super(ctx);
        this.baseRcj2022Dao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseRcj2022);


    }

    async getRcjBySequenceNbr(sequenceNbr)
    {
        return await this.baseRcj2022Dao.findOne({
            where: {
                sequenceNbr: sequenceNbr
            }
        })
    }

    /**
     *
     * @param rcj
     * @returns {Promise<void>}
     */
    async getSubRCJs(rcj)
    {
        return await this.baseRcj2022Dao.find({
            where: {
                libraryCode: rcj.libraryCode,
                materialCode: rcj.materialCode,
                levelMark: ResourceConstants.LEVEL_MARK_NONE_PB
            },
            order: { sortNo: "ASC" }
        })
    }

    /**
     *
     * @param rcj
     * @returns {Promise<void>}
     */
    async getJxpb(rcj)
    {
        return await this.app.db.gongLiaoJiProject.manager.getRepository(GsBaseJxpb).find({
            where: {
                libraryCode: rcj.libraryCode,
                rcjId: rcj.rcjId,
            }
        })
    }

    async getOneJxpb(rcj)
    {
        return await this.app.db.gongLiaoJiProject.manager.getRepository(GsBaseJxpb).findOne({
            where: {
                libraryCode: rcj.libraryCode,
                sequenceNbr: rcj.sequenceNbr
            }
        })
    }

    async getClpb(rcj)
    {
        return await this.app.db.gongLiaoJiProject.manager.getRepository(GsBaseClpb).find({
            where: {
                libraryCode: rcj.libraryCode,
                rcjId: rcj.rcjId,
            }
        })
    }

    async getOneClpb(rcj)
    {
        return await this.app.db.gongLiaoJiProject.manager.getRepository(GsBaseClpb).findOne({
            where: {
                libraryCode: rcj.libraryCode,
                sequenceNbr: rcj.sequenceNbr
            }
        })
    }


    async getOneJxpbByName(rcj)
    {
        return await this.app.db.gongLiaoJiProject.manager.getRepository(GsBaseJxpb).findOne({
            where: {
                rcjId: rcj.rcjId,
                materialName : rcj.materialName
            }
        })
    }

    async getOneClpbByName(rcj)
    {
        return await this.app.db.gongLiaoJiProject.manager.getRepository(GsBaseClpb).findOne({
            where: {
                rcjId: rcj.rcjId,
                materialName: rcj.materialName
            }
        })
    }

    async getOneRcjByCode(rcj)
    {
        return await this.app.db.gongLiaoJiProject.manager.getRepository(BaseRcj2022).findOne({
            where: {
                materialCode: rcj.materialCode,
                libraryCode: rcj.libraryCode
            }
        })
    }
    /**
     * 根据额库编码 查询人才机
     * @param libraryCode
     * @returns {Promise<BaseRcj2022[]|Error>}
     */
    async listDeByLibraryCode(libraryCode) {
        if (null == libraryCode) {
            throw new Error("必传参数定额库code为空");
        }

        return await this.baseRcj2022Dao.find({
            where: {libraryCode: libraryCode},
            order: {sortNo: "ASC"}
        });
    }

    /**
     * 根据额库编码 查询人才机
     * @param libraryCode
     * @returns {Promise<BaseRcj2022[]|Error>}
     */
    async getRcjByCode(constructId, unitId, materialCode) {
        if (ObjectUtils.isEmpty(materialCode)) {
            throw new Error("基础人材机materialCode为空.");
        }
        let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId)
        let libraryCode = unitProject.constructMajorType;

        let baseRcjList = await this.baseRcj2022Dao.find({
            where: {
                materialCode: materialCode
            }
        });

        // 建筑安装人材机查询
        if (ObjectUtil.isEmpty(baseRcjList.find(item => item.libraryCode === libraryCode))) {
            let libraryCode2;
            switch (libraryCode) {
                case UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZGC: {
                    libraryCode2 = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZGC
                    break;
                }
                case UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_ZSGC: {
                    libraryCode2 = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_ZSGC
                    break;
                }
                case UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZGC: {
                    libraryCode2 = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZGC
                    break;
                }
                case UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_SZGC: {
                    libraryCode2 = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_SZGC
                    break;
                }
                case UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_YLLH: {
                    libraryCode2 = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_YLLH
                    break;
                }
                case UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_FXJZ: {
                    libraryCode2 = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_FXJZ
                    break;
                }
                case UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_FXAZ: {
                    libraryCode2 = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_FXAZ
                    break;
                }
                default:
            }
            baseRcjList = baseRcjList.filter(item => item.libraryCode === libraryCode2);
        } else {
            baseRcjList = baseRcjList.filter(item => item.libraryCode === libraryCode);
        }

        let rcjList = [];
        baseRcjList.forEach(item=>{rcjList.push(ObjectUtils.copyProp(item, new GljRcj()))});
        return rcjList;
    }

    /**
     * 查询某条人材机
     * @param params
     * @returns {Promise<ResponseData>}
     */
    async queryRcjById(params) {
        let {standardId, libraryCode, materialCode} = params;
        let querySql = "select a.price_base_journal_tax as price,\n" +
            "a.price_base_journal as baseJournalPrice,\n" +
            "a.price_base_journal_tax as baseJournalTaxPrice,\n" +
            "a.price_market_tax as marketTaxPrice,\n" +
            "a.price_market as marketPrice,\n" +
            "a.tax_rate as taxRemoval,\n" +
            " a.* from base_rcj_2022 a where sequence_nbr = ?";
        let sqlRes = this.app.gljSqlite3DataSource.prepare(querySql).all(standardId);
        let convertRes = SqlUtils.convertToModel(sqlRes);
        if(convertRes.length !== 0) {
            let gljRcj = ObjectUtils.copyProp(convertRes[0], new GljRcj());
            gljRcj.rcjId=gljRcj.sequenceNbr  ;
            return gljRcj;
        }
        // 配比
        if(convertRes.length === 0) {
            querySql = "select a.price_base_journal_tax as price,\n" +
                "a.price_base_journal as baseJournalPrice,\n" +
                "a.price_base_journal_tax as baseJournalTaxPrice,\n" +
                "a.price_market_tax as marketTaxPrice,\n" +
                "a.price_market as marketPrice,\n" +
                "a.tax_rate as taxRemoval,\n" +
                "a.* from base_rcj_2022 a where material_code = ? and library_code = ?";
            sqlRes = this.app.gljSqlite3DataSource.prepare(querySql).all(materialCode, libraryCode);
            convertRes = SqlUtils.convertToModel(sqlRes);
            if(convertRes.length !== 0) {
                let gljRcj = ObjectUtils.copyProp(convertRes[0], new GljRcj());
                gljRcj.rcjId = gljRcj.sequenceNbr;
                return gljRcj;
            }
        }
        return null
    }


    async queryAllRcjId() {
        let querySql = "select a.sequence_nbr as sequenceNbr , " +
          "a.kind as kind  from base_rcj_2022 a where kind = 4 or kind = 5";
        let sqlRes = this.app.gljSqlite3DataSource.prepare(querySql).all();
        let convertRes = SqlUtils.convertToModel(sqlRes);
        if (convertRes.length !== 0) {
            return convertRes;
        }
        return   null;
    }
    /**
     * 查询某条人材机
     * @param params
     * @returns {Promise<ResponseData>}
     */
    async queryRcjByCode(params) {
        let {materialCode} = params;
        let querySql = "select a.price_base_journal_tax as price,\n" +
            "a.price_base_journal as baseJournalPrice,\n" +
            "a.price_base_journal_tax as baseJournalTaxPrice,\n" +
            "a.price_market_tax as marketTaxPrice,\n" +
            "a.price_market as marketPrice," +
            "a.tax_rate as taxRemoval," +
            "a.* from base_rcj_2022 a where material_code = ? limit 1";
        let sqlRes = this.app.gljSqlite3DataSource.prepare(querySql).all(materialCode);
        let convertRes = SqlUtils.convertToModel(sqlRes);
        if(convertRes.length === 0) {
            return null;
        }
        let gljRcj = ObjectUtils.copyProp(convertRes[0], new GljRcj());
        gljRcj.rcjId=gljRcj.sequenceNbr  ;
        return gljRcj;
    }

    /**
     * 查询某条人材机
     * @param params
     * @returns {Promise<ResponseData>}
     */
    async queryRcjByCodeAndLib(params) {
        let {materialCode,libraryCode} = params;
        let querySql = "select a.price_base_journal_tax as price,\n" +
            "a.price_base_journal as baseJournalPrice,\n" +
            "a.price_base_journal_tax as baseJournalTaxPrice,\n" +
            "a.price_market_tax as marketTaxPrice,\n" +
            "a.price_market as marketPrice," +
            "a.tax_rate as taxRemoval," +
            "a.* from base_rcj_2022 a where material_code = ? and library_code = ?  limit 1";
        let sqlRes = this.app.gljSqlite3DataSource.prepare(querySql).all(materialCode,libraryCode);
        let convertRes = SqlUtils.convertToModel(sqlRes);
        if(convertRes.length === 0) {
            return null;
        }
        let gljRcj = ObjectUtils.copyProp(convertRes[0], new GljRcj());
        gljRcj.rcjId=gljRcj.sequenceNbr  ;
        return gljRcj;
    }

    /**
     * 根据条件查询人材机
     * @param args
     * @returns {Promise<*>}
     */
    async queryListByParam(args) {
        let {libraryCode, name} = args
        // 判断入参是否为空，不为空拼接sql
        let whereSql = "";
        let whereParams = {};
        // 清单册
        if (libraryCode) {
            // 拼接and
            if (whereSql.length !== 0) whereSql += " and ";
            // 拼接sql和参数
            whereSql += "baseList.libraryCode = :libraryCode"
            whereParams.libraryCode = libraryCode;
        }
        // 人材机名称
        if (name) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "( baseList.material_name like :name or " +
                "baseList.material_code like :code )"
            whereParams.name = "%" + name + "%";
            whereParams.code = name + "%";
        }

        if (!whereSql) {
            console.log("error,参数为空");
        }

        let baseLists = await this.baseRcj2022Dao
            .createQueryBuilder("baseList")
            .where(whereSql, whereParams)
            .orderBy("baseList.material_name", "ASC")
            .getMany();

        return baseLists;
    }

    /**
     * 人材机目录树
     * @param libraryCode 定额册编码
     */
    async listTreeByLibraryCode(libraryCode) {
        /*// 自测参数
        libraryCode = "2012-JZGC-DEY";*/
        // console.time("查库1");
        // let rcjList = await this.baseRcjDao.findBy({libraryCode: libraryCode});
        // console.timeEnd("查库1");
        let selectSql = "SELECT\n" +
            "\tsequence_nbr sequenceNbr,\n" +
            "\tlevel1,\n" +
            "\tlevel2,\n" +
            "\tlevel3,\n" +
            "\tlevel4,\n" +
            "\tlevel5,\n" +
            "\tlevel6,\n" +
            "\tlevel7 \n" +
            "FROM\n" +
            "\t\"base_rcj_2022\" \n" +
            "WHERE\n" +
            "\tlibrary_code = ?;";
        let rcjList = await this.baseRcj2022Dao.find({
            where: {libraryCode: libraryCode}
        });
        if (ObjectUtils.isEmpty(rcjList)) {
            return [];
        }

        // 将人材机数据分层转为树结构
        let resultTree = [];
        let sequenceNbr = 1;
        let groupLevel1Map = ArrayUtil.group(rcjList, "level1");
        groupLevel1Map.forEach((level2List, level1) => {
            if (ObjectUtils.isEmpty(level1)) {
                // 过滤分组后key为null的元素
                return;
            }
            let level1RcjModel = this._packageRcjModel(1, level2List[0], sequenceNbr++);
            resultTree.push(level1RcjModel);
            // level2
            let groupLevel2Map = ArrayUtil.group(level2List, "level2");
            groupLevel2Map.forEach((level3List, level2) => {
                if (ObjectUtils.isEmpty(level2)) {
                    // 过滤分组后key为null的元素
                    return;
                }
                let level2RcjModel = this._packageRcjModel(2, level3List[0], sequenceNbr++);
                level1RcjModel.childrenList.push(level2RcjModel);
                // level3
                let groupLevel3Map = ArrayUtil.group(level3List, "level3");
                groupLevel3Map.forEach((level4List, level3) => {
                    if (ObjectUtils.isEmpty(level3)) {
                        // 过滤分组后key为null的元素
                        return;
                    }
                    let level3RcjModel = this._packageRcjModel(3, level4List[0], sequenceNbr++);
                    level2RcjModel.childrenList.push(level3RcjModel);
                    // level4
                    let groupLevel4Map = ArrayUtil.group(level4List, "level4");
                    groupLevel4Map.forEach((level5List, level4) => {
                        if (ObjectUtils.isEmpty(level4)) {
                            // 过滤分组后key为null的元素
                            return;
                        }
                        let level4RcjModel = this._packageRcjModel(4, level5List[0], sequenceNbr++);
                        level3RcjModel.childrenList.push(level4RcjModel);
                        // level5
                        let groupLevel5Map = ArrayUtil.group(level5List, "level5");
                        groupLevel5Map.forEach((level6List, level5) => {
                            if (ObjectUtils.isEmpty(level5)) {
                                // 过滤分组后key为null的元素
                                return;
                            }
                            let level5RcjModel = this._packageRcjModel(5, level6List[0], sequenceNbr++);
                            level4RcjModel.childrenList.push(level5RcjModel);
                            // level6
                            let groupLevel6Map = ArrayUtil.group(level6List, "level6");
                            groupLevel6Map.forEach((level7List, level6) => {
                                if (ObjectUtils.isEmpty(level6)) {
                                    // 过滤分组后key为null的元素
                                    return;
                                }
                                let level6RcjModel = this._packageRcjModel(6, level7List[0], sequenceNbr++);
                                level5RcjModel.childrenList.push(level6RcjModel);
                                //level7
                                let groupLevel7Map = ArrayUtil.group(level7List, "level7");
                                groupLevel7Map.forEach((crjList, level7) => {
                                    if (ObjectUtils.isEmpty(level7)) {
                                        // 过滤分组后key为null的元素
                                        return;
                                    }
                                    let level7RcjModel = this._packageRcjModel(7, crjList[0], sequenceNbr++);
                                    level6RcjModel.childrenList.push(level7RcjModel);
                                });
                            });
                        });
                    });
                });
            });
        });
        // 自定义排序规则
        let order = ["人工", "材料", "机械"];
        // 排序函数
        resultTree.sort((a, b) => {
            const indexA = order.indexOf(a.level1);
            const indexB = order.indexOf(b.level1);
            // 如果都在列表中，按顺序排序；否则按原顺序
            return indexA - indexB;
        });
        return resultTree;
    }

    /**
     * 组装人材机
     * @param level 1~7
     * @param sourceRcjModel 来源人材机model
     * @param sequenceNbr 唯一
     * @private
     */
    _packageRcjModel(level, sourceRcjModel, sequenceNbr) {
        if (ObjectUtils.isEmpty(level)) {
            throw new Error("level有误");
        }

        let resultRcjModel = new GljRcj();
        resultRcjModel.sequenceNbr = sequenceNbr;
        // 初始化子集
        resultRcjModel.childrenList = [];
        switch (level) {
            case 1:
                // level1
                resultRcjModel.level1 = sourceRcjModel.level1;
                resultRcjModel.materialName = sourceRcjModel.level1;
                break;
            case 2:
                // level2
                resultRcjModel.level1 = sourceRcjModel.level1;
                resultRcjModel.level2 = sourceRcjModel.level2;
                resultRcjModel.materialName = sourceRcjModel.level2;
                break;
            case 3:
                // level3
                resultRcjModel.level1 = sourceRcjModel.level1;
                resultRcjModel.level2 = sourceRcjModel.level2;
                resultRcjModel.level3 = sourceRcjModel.level3;
                resultRcjModel.materialName = sourceRcjModel.level3;
                break;
            case 4:
                // level4
                resultRcjModel.level1 = sourceRcjModel.level1;
                resultRcjModel.level2 = sourceRcjModel.level2;
                resultRcjModel.level3 = sourceRcjModel.level3;
                resultRcjModel.level4 = sourceRcjModel.level4;
                resultRcjModel.materialName = sourceRcjModel.level4;
                break;
            case 5:
                // level5
                resultRcjModel.level1 = sourceRcjModel.level1;
                resultRcjModel.level2 = sourceRcjModel.level2;
                resultRcjModel.level3 = sourceRcjModel.level3;
                resultRcjModel.level4 = sourceRcjModel.level4;
                resultRcjModel.level5 = sourceRcjModel.level5;
                resultRcjModel.materialName = sourceRcjModel.level5;
                break;
            case 6:
                // level6
                resultRcjModel.level1 = sourceRcjModel.level1;
                resultRcjModel.level2 = sourceRcjModel.level2;
                resultRcjModel.level3 = sourceRcjModel.level3;
                resultRcjModel.level4 = sourceRcjModel.level4;
                resultRcjModel.level5 = sourceRcjModel.level5;
                resultRcjModel.level6 = sourceRcjModel.level6;
                resultRcjModel.materialName = sourceRcjModel.level6;
                break;
            case 7:
                // level7
                resultRcjModel.level1 = sourceRcjModel.level1;
                resultRcjModel.level2 = sourceRcjModel.level2;
                resultRcjModel.level3 = sourceRcjModel.level3;
                resultRcjModel.level4 = sourceRcjModel.level4;
                resultRcjModel.level5 = sourceRcjModel.level5;
                resultRcjModel.level6 = sourceRcjModel.level6;
                resultRcjModel.level7 = sourceRcjModel.level7;
                resultRcjModel.materialName = sourceRcjModel.level7;
                break;
            default:
                // ...
                break;
        }
        return resultRcjModel;
    }

    /**
     * 模糊查询人材机 - zxz版本
     * @param baseRcj 查询条件
     * @param page
     * @param limit
     * @return {BaseRcj[]|BaseRcj2022[]}
     */
    async listLikeZxz(baseRcj, page, limit) {
        if (ObjectUtils.isEmpty(baseRcj)) {
            throw new Error("人材机搜索条件为空");
        }
        if (ObjectUtils.isEmpty(baseRcj.libraryCode)) {
            throw new Error("人材机libraryCode为空");
        }

        return await this.listLike2(baseRcj, page, limit);
    }

    async listLike2(baseRcjModel, page, limit) {
        let sql = "select " +
            "material_code = ? as materialCode,\n" +
            "material_name = ? as materialName,\n" +
            "price_base_journal_tax as price,\n" +
            "price_base_journal as baseJournalPrice,\n" +
            "price_base_journal_tax as baseJournalTaxPrice,\n" +
            "price_market_tax as marketTaxPrice,\n" +
            "price_market as marketPrice,\n" +
            "tax_rate as taxRemoval,\n" +
            "a.*\n" +
            "from base_rcj_2022 a\n" +
            "where library_code = ? and (" +
            "material_name like '%' || ? || '%'\n" +
            "or material_code like '%' || ? || '%')\n" +
            "order by materialCode desc,materialName desc\n" +
            "limit ?, ?";
        let sqlRes = this.app.gljSqlite3DataSource.prepare(sql).all(baseRcjModel.materialName, baseRcjModel.materialName,baseRcjModel.libraryCode,baseRcjModel.materialName, baseRcjModel.materialName, (page-1)*limit, limit);
        let res = SqlUtils.convertToModel(sqlRes);


        let selectSqlCount = "select COUNT(1) as total from (select " +
            "material_code = ? as materialCode,\n" +
            "material_name = ? as materialName,\n" +
            "price_base_journal_tax as price,\n" +
            "price_base_journal as baseJournalPrice,\n" +
            "price_base_journal_tax as baseJournalTaxPrice,\n" +
            "price_market_tax as marketTaxPrice,\n" +
            "price_market as marketPrice,\n" +
            "tax_rate as taxRemoval,\n" +
            "a.*\n" +
            "from base_rcj_2022 a\n" +
            "where library_code = ? and (" +
            "material_name like '%' || ? || '%'\n" +
            "or material_code like '%' || ? || '%')\n" +
            "order by materialCode desc,materialName desc)";

        let cnt = this.app.gljSqlite3DataSource.prepare(selectSqlCount).all(baseRcjModel.materialName, baseRcjModel.materialName,baseRcjModel.libraryCode, baseRcjModel.materialName, baseRcjModel.materialName);

        return {
            "list": res,
            "total": cnt[0].total
        };

    }

    /**
     * 根据材料idList查材料
     * @param rcjIdList 人材机ids
     * @return {Promise<BaseRcj[]>}
     */
    async getRcjListByRcjIdList(rcjIdList) {
        if (ObjectUtils.isEmpty(rcjIdList)) {
            console.error("getRcjListByRcjIdList入参人材机idList为空");
            return [];
        }

        let sql = `SELECT a.price_base_journal_tax as price,
                          a.price_base_journal as baseJournalPrice,
                          a.price_base_journal_tax as baseJournalTaxPrice,
                          a.price_market_tax as marketTaxPrice,
                          a.price_market as marketPrice,
                          a.tax_rate as taxRemoval,
                          a.* FROM base_rcj_2022 a WHERE sequence_nbr IN (${rcjIdList.map(() => '?').join(',')})`
        let sqlRes = this.app.gljSqlite3DataSource.prepare(sql).all(rcjIdList);
        let res = SqlUtils.convertToModel(sqlRes);

        return res;
    }

    /**
     *  模糊搜索
     * @param baseRcjModel 材料名称（模糊搜索）、定额册编码、level1~7
     * @param baseRcjInfo
     * @param page
     * @param limit
     * @return {Promise<BaseRcj[]>}
     */
    async listLike(baseRcjModel, baseRcjInfo, page, limit) {
        // 表的别名
        let tableAlias = "baseRcj";

        // 判断入参是否为空，不为空拼接sql
        let whereSql = "";
        let whereParams = {};
        // 定额册
        if (baseRcjModel.libraryCode) {
            // 拼接and
            if (whereSql.length !== 0) whereSql += " and ";
            // 拼接sql和参数
            whereSql += tableAlias + ".libraryCode = :libraryCode"
            whereParams.libraryCode = baseRcjModel.libraryCode;
        }
        // 层级1
        if (baseRcjModel.level1) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".level1 = :level1"
            whereParams.level1 = baseRcjModel.level1;
        }
        // 层级2
        if (baseRcjModel.level2) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".level2 = :level2"
            whereParams.level2 = baseRcjModel.level2;
        }
        // 层级3
        if (baseRcjModel.level3) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".level3 = :level3"
            whereParams.level3 = baseRcjModel.level3;
        }
        // 层级4
        if (baseRcjModel.level4) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".level4 = :level4"
            whereParams.level4 = baseRcjModel.level4;
        }
        // 层级5
        if (baseRcjModel.level5) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".level5 = :level5"
            whereParams.level5 = baseRcjModel.level5;
        }
        // 层级6
        if (baseRcjModel.level6) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".level6 = :level6"
            whereParams.level6 = baseRcjModel.level6;
        }
        // 层级7
        if (baseRcjModel.level7) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".level7 = :level7"
            whereParams.level7 = baseRcjModel.level7;
        }
        // 材料名称模糊搜索
        // if (baseRcjModel.materialName) {
        //     if (whereSql.length !== 0) whereSql += " and ";
        //     whereSql += tableAlias + ".materialName like :materialName"
        //     whereParams.materialName = "%" + baseRcjModel.materialName + "%";
        // }
        // // 材料名称编码
        // if (baseRcjModel.materialCode) {
        //     if (whereSql.length !== 0) whereSql += " and ";
        //     whereSql += tableAlias + ".materialCode = :materialCode"
        //     whereParams.materialCode = baseRcjModel.materialCode;
        // }

        if (!whereSql) {
            console.log("error,参数为空");
        }

        let baseRcjList;
        if (ObjectUtils.isNotEmpty(baseRcjInfo) && ObjectUtils.isNotEmpty(baseRcjInfo.materialCode)) {
            let data = await this.baseRcj2022Dao
                .createQueryBuilder(tableAlias)
                .where(whereSql, whereParams)
                .orderBy(tableAlias + ".sort_no")
                .getMany();

            let index = data.findIndex(obj => obj.materialName === baseRcjInfo.materialName && obj.materialCode === baseRcjInfo.materialCode) + 1;
            let oldPage = page;
            page = Math.ceil(index / limit);
            limit = page * limit;
            let startIndex = parseInt((oldPage - 1) * limit);
            let endIndex = startIndex + limit;
            baseRcjList = data.slice(startIndex, endIndex);
        } else {
            // 分页结果
            baseRcjList = await this.baseRcj2022Dao
                .createQueryBuilder(tableAlias)
                .where(whereSql, whereParams)
                .orderBy(tableAlias + ".sort_no")
                .skip((page - 1) * limit)  // 跳过的记录数量
                .take(limit)  // 要获取的记录数量
                .getMany();
        }

        //给基期价赋值  避免在22定额时选择12定额数据
        for(const rcj of baseRcjList){
            rcj.priceBaseJournal=rcj.dePrice;
            rcj.priceBaseJournalTax=rcj.dePrice;
        }
        // 总行数
        const count = await this.baseRcj2022Dao.createQueryBuilder(tableAlias)
            .where(whereSql, whereParams)
            .getCount();

        // 封装分页结果
        return {
            "list": baseRcjList,
            "total": count,
            "page": page
        };
    }

    /**
     * 根据人材机编码获取人材机
     * @param rcjCode
     * @returns {Promise<void>}
     */
    async getByRcjCode(rcjCode) {

    }

}

GljBaseRcjService.toString = () => '[class GljBaseRcjService]';
module.exports = GljBaseRcjService;
