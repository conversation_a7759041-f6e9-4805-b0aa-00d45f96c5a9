<!--
 * @Descripttion: 载价报告
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-12-20 16:23:38
-->
<template>
  <div class="content">
    <p class="headSelect">
      <a-radio-group
        button-style="solid"
        v-model:value="selectVal"
        @change="radioChange"
      >
        <a-radio-button value="detailTable">明细列表</a-radio-button>
        <a-radio-button value="picList">指标查看</a-radio-button>
      </a-radio-group>
    </p>
    <div class="tableAndfigs">
      <div
        class="table"
        v-show="selectVal === 'detailTable'"
      >
        <vxe-table
          ref="tableDetail"
          align="center"
          :column-config="{ resizable: true }"
          :data="tableData"
          height="100%"
          :row-config="{
            isCurrent: true,
            keyField: 'sequenceNbr',
          }"
          @current-change="currentChangeEvent"
          :scroll-y="{ scrollToTopOnChange: true }"
        >
          <!-- <vxe-column type="checkbox" width="50" title=""></vxe-column> -->
          <vxe-column
            type="seq"
            width="60"
            title="序号"
          > </vxe-column>
          <vxe-column
            field="materialCode"
            width="100"
            title="材料编码"
          >
          </vxe-column>
          <vxe-column
            field="type"
            width="auto"
            title="类型"
          > </vxe-column>
          <vxe-column
            field="materialName"
            width="auto"
            title="名称"
          >
          </vxe-column>
          <vxe-column
            field="specification"
            width="80"
            title="规格型号"
          >
          </vxe-column>

          <vxe-column
            field="unit"
            width="80"
            title="单位"
          > </vxe-column>
          <vxe-column
            field="totalNumber"
            width="auto"
            title="数量"
          >
          </vxe-column>
          <vxe-column
            field="marketPrice"
            width="auto"
            title="载价后价格"
          >
          </vxe-column>
          <!--  结算中 税率添加  && store.deStandardReleaseYear === '22'-->
          <vxe-column  field="taxRate"  width="auto" title="税率" v-if="store.type === 'jieSuan'">
            <template #default="{ row, column }">
              <span v-if="taxList.includes('taxRate') && store.deStandardReleaseYear === '22'">{{ row.taxRate }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="sourcePrice"
            width="120"
            title="价格来源"
          >
          </vxe-column>
        </vxe-table>
        <p>
          <span><icon-font
              class="icon"
              type="icon-querenshanchu"
              style="margin-right: 5px"
            />该列表仅展示价格来源为批量载价相关的数据</span>
          <!-- <a-button type="primary" danger @click="clearLoad">清除载价</a-button> -->
        </p>
      </div>
      <div
        class="figs"
        v-show="selectVal === 'picList'"
      >
        <div
          ref="piePicture"
          style="width: 450px; height: 360px"
        ></div>
        <div
          ref="barPicture"
          style="width: 450px; height: 360px"
        ></div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, watch, onMounted, getCurrentInstance } from 'vue';
import api from '@/api/loadPrice';
import jsApi from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
let selectVal = ref('detailTable');
import { Modal } from 'ant-design-vue';
let tableData = ref([]);
let figsData = ref([]);
const piePicture = ref();
const barPicture = ref();
const props = defineProps(['propsData', 'isOriginalFlag', 'priceType']);
const { proxy } = getCurrentInstance();
let tableDetail = ref();
let taxList = ref([]);
import { message } from 'ant-design-vue';
const getnewData = data => {
  let pic = [];
  let bar = [];
  let newdata = {
    pic: [],
    bar: {
      beforeData: [],
      afterData: [],
    },
    typeList: [],
  };
  data.pic.map(item => {
    newdata.pic.push({ value: item.total, name: item.type });
  });
  data.bar.map(item => {
    if (item.type) {
      newdata.typeList.push(item.type);
      newdata.bar.beforeData.push(item.beforePrice);
      newdata.bar.afterData.push(item.afterPrice);
    }
  });
  return newdata;
};
const radioChange = async e => {
  selectVal.value = e.target.value;
  if (e.target.value === 'detailTable') {
    // tableData.value = [];
  } else {
    let pictureData = await getPicsData();
    let newData = getnewData(pictureData);
    console.log('pictureData', newData);

    let pirchart = proxy.$echarts.init(piePicture.value);
    let barchart = proxy.$echarts.init(barPicture.value);
    pirchart.setOption({
      title: {
        text: '不同类型价格占比',
        left: 'left',
        top: 10,
        left: 10,
        textStyle: {
          fontSize: 15,
          fontWeight: 'bolder',
          color: '#333', // 主标题文字颜色
        },
      },
      tooltip: {
        trigger: 'item',
      },
      legend: {
        orient: 'vertical',
        right: 20,
        bottom: 10,
      },
      series: [
        {
          label: {
            show: false,
          },
          name: '访问来源',
          type: 'pie', // 设置图表类型为饼图
          radius: ['30%', '70%'],
          avoidLabelOverlap: false, // 饼图的半径，外半径为可视区尺寸（容器高宽中较小一项）的 55% 长度。
          center: ['40%', '50%'],
          data: newData.pic,
        },
      ],
    });
    barchart.setOption({
      title: {
        text: '本次载价后价格对比',
        left: 'left',
        top: 10,
        left: 20,
        textStyle: {
          fontSize: 15,
          fontWeight: 'bolder',
          color: '#333', // 主标题文字颜色
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {
        y: 'bottom',
        x: 'center',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '8%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: newData.typeList,
        },
      ],
      yAxis: [
        {
          type: 'value',
        },
      ],
      series: [
        {
          name: '载入前',
          type: 'bar',
          emphasis: {
            focus: 'series',
          },
          data: newData.bar.beforeData,
        },
        {
          name: '载入后',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: newData.bar.afterData,
        },
      ],
    });
  }
};
// window.addEventListener('resize', function () {
// 	if (selectVal.value === 'picList') {
// 		pirchart.resize();
// 		barchart.resize();
// 	}
// });
const clearLoad = () => {
  //清除载价
  let list = tableDetail.value.getCheckboxRecords();
  if (list.length === 0) {
    message.info('请选择要清除载价的数据');
    return;
  }
  Modal.confirm({
    title: '是否确定清除选中数据的载价数据？删除后无法撤销恢复',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      let formData = {
        type: store.currentTreeInfo.levelType,
      };
      formData = getParamsData(formData);
      let sequenceNbrList = [];
      list.map(item => {
        sequenceNbrList.push(item.sequenceNbr);
      });
      formData.sequenceNbrList = sequenceNbrList;
      let apiFun = api.clearLoadPrice;
      if (props.isOriginalFlag) {
        apiFun = jsApi.clearLoadPriceOriginal;
      }
      apiFun(formData).then(res => {
        if (res.status === 200 && res.result) {
          console.log('清除载价', res.status, formData);
          getTableData();
        }
      });
    },
    onCancel() {},
  });
};
onMounted(async () => {
  if(store.type === "jieSuan"){
    taxList.value = props.propsData.taxList || [];
  }
  await getTableData();
  // await getPicsData();
});
const getParamsData = data => {
  let apiData = { ...data };
  apiData.constructId =
    store.currentTreeInfo.levelType === 1
      ? store.currentTreeInfo?.id
      : store.currentTreeGroupInfo?.constructId;
  if (store.currentTreeInfo.levelType === 2) {
    apiData.singleId = store.currentTreeInfo?.id; //单项ID
  }
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  return apiData;
};
const getTableData = async () => {
  let formData = {
    type: store.currentTreeInfo.levelType,
  };
  formData = getParamsData(formData);
  let apiFun = api.loadPriceList;
  if (props.isOriginalFlag) {
    apiFun = jsApi.jsLoadPriceList;
    const { num = null } = store.currentStageInfo || {};
    const {
      key,
      defaultFeeFlag: { frequencyList },
    } = store.asideMenuCurrentInfo || {};
    Object.assign(formData, {
      priceType: props.priceType,
      num: frequencyList && frequencyList.length ? num : null,
      kind: Number(key),
    });
  }
  //  && store.deStandardReleaseYear === '22'
  if(store.type === "jieSuan"){
    formData.priceMethod = {
      tax: taxList.value.includes('tax'),
      noTax: taxList.value.includes('noTax'),
      taxRate: taxList.value.includes('taxRate')
    }
  }
  await apiFun(formData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('-------------getTableData', res);
      tableData.value = res.result;
      tableDetail.value.clearCheckboxRow();
    }
  });
};
const getPicsData = async () => {
  let formData = {
    type: store.currentTreeInfo.levelType,
  };
  formData = getParamsData(formData);
  let pictureData = {};

  //饼状图
  let apiFun = api.queryLoadPriceReportRcj;
  if (props.isOriginalFlag) {
    formData['priceType']=props.priceType
    apiFun = jsApi.jsQueryLoadPriceReportRcj;
  }
  console.info(32423423442222,formData)
  await apiFun(formData).then(res => {
    if (res.status === 200 && res.result) {
      pictureData.pic = res.result;
      console.log('++++++++++++', res.result);
    }
  });
  //条形图
  let apiFunction = api.queryLoadPriceReportTarget;
  if (props.isOriginalFlag) {
    apiFun = jsApi.jsQueryLoadPriceReportTarget;
  }
  await apiFunction(formData).then(res => {
    if (res.status === 200 && res.result) {
      pictureData.bar = res.result;
    }
  });
  return pictureData;
};
watch([() => store.currentTreeInfo, () => store.tabSelectName], value => {});
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  .headSelect {
    text-align: left;
  }
  .tableAndfigs {
    height: calc(100% - 32px);
    .table {
      position: relative;
      height: 320px;
      p {
        position: absolute;
        bottom: -60px;
        display: flex;
        justify-content: space-between;
        width: 100%;
      }
    }
    .figs {
      height: 100%;
      width: 100%;
      display: flex;
      justify-content: space-between;
      div {
        border: 1px solid #e0e0e0;
      }
    }
  }
}
</style>
