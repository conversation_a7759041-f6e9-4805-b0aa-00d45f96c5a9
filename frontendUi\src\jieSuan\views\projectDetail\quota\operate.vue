<!--
 * @Descripttion: 
 * @Author: 
 * @Date: 2024-07-05 15:09:05
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-25 15:31:12
-->
<template>
  <div class="operate-container">
    <div class="operate" :style="operateStyle">
      <div class="operate-scroll">
        <template v-for="(item, index) in filterOperateList" :key="index">
          <div
            class="operate-item"
            v-if="item.components.includes(projectStore.componentId?projectStore.componentId:'keyInfo')"
            :class="{ disabled: item.disabled }"
            @click="!item.disabled ? setEmit(item) : ''"
          >
            <a-tooltip
              placement="bottom"
              v-model:visible="item.decVisible"
              @visibleChange="val => infoVisibleChange(val, item)"
            >
              <template #title>
                <span style="font-size: 12px; text-decoration: underline">{{
                  item.label
                }}</span>
                <p v-if="item.infoDec" style="font-size: 10px">
                  {{ item.infoDec }}
                </p>
              </template>
              <OperateItem
                :item="item"
                @setSelectEmit="
                  itemInfo => setSelectEmit(itemInfo.item, itemInfo.data)
                "
                @setEmit="setEmit"
              ></OperateItem>
            </a-tooltip>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script setup>
import {
  getCurrentInstance,
  watch,
  ref,
  computed,
  onActivated,
  onMounted,
} from 'vue';
import operateList, { updateOperateByName } from './operate';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import { systemConfigStore } from '@/store/systemConfig';
import csProject from '@/api/csProject';
import apiObj from '@/api/projectDetail.js';
import jieSuanApi from '@/api/jiesuanApi';
import { useRoute } from 'vue-router';
import OperateItem from './OperateItem.vue';
import OperateItemTitle from './OperateItemTitle.vue';
import XEUtils from 'xe-utils';
const systemConfig = systemConfigStore();
const projectStore = projectDetailStore();
const props = defineProps(['projectComponentType']);
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const emit = defineEmits(['showLoadMould']);
const route = useRoute();
let checkColorList = ref([]); // 颜色筛选项
let visible = ref(false); // 过滤下拉菜单判断是否关闭

const operateStyle = computed(() => {
  return {
    height: systemConfig.functionalArea.height,
    lineHeight: systemConfig.functionalArea.height,
  };
});
const filterOperateList = computed(() => {
  let list = [];
  for (let item of operateList.value) {
      list.push(item);
  }
  return list;
});
const infoVisibleChange = (val, item) => {
  if (val) {
    item.decVisible = true;
  } else {
    item.decVisible = false;
  }
};
onMounted(async () => {
  
});
const setSelectEmit = (item, data, parentItem = null) => {
  // 父级菜单为聚合显示菜单时，其子菜单不为下拉时，关闭父菜单下拉
  if (
    parentItem &&
    parentItem.type === 'menuList' &&
    !['select', 'selectRadio'].includes(item.type)
  ) {
    parentItem.dropdownVisible = false;
  }
  visible.value = false;
  bus.emit(data.name + item.kind, data);
};
const setEmit = (item, parentItem = null) => {
  item.decVisible = false;
  bus.emit(item.name, item);
};
</script>
<style lang="scss" scoped>
.menu-list-content {
  padding: 5px 10px;
  display: flex;
  background-color: #fff;
  .menu-list {
    margin: 0 4px;
    text-align: center;
    cursor: pointer;
    &.disabled {
      opacity: 0.5;
    }
  }
}
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.operate {
  // flex-direction: row;
  box-sizing: border-box;
  background: #f3f6f9;
  width: calc(100% - 30px);
  overflow-x: hidden;
  overflow-y: hidden;
  height: var(--project-detail-functional-area-height);
  line-height: var(--project-detail-functional-area-height);
  user-select: none;
  &:hover {
    overflow-x: auto;
  }
  &-scroll {
    display: flex;
    min-width: fit-content; /* 设置最小宽度为子元素的总宽度 */
    padding: 0 5px;
  }
  &-item {
    display: flex;
    flex-direction: column;
    justify-content: center; /* 垂直居中 */
    //align-items: center; /* 水平居中 */
    min-width: 50px;
    padding: 0 4px;
    text-align: center;
    height: var(--project-detail-functional-area-height);
    cursor: pointer;
    div {
      height: auto;
      line-height: initial;
      text-align: center;
    }
    .iconType {
      font-size: 26px;
    }
    .icon {
      width: 28px;
      img {
        width: 100%;
      }
    }
    .label {
      font-size: 12px;
      margin-top: 2px;
    }
  }
}
.operate-container {
  position: relative;
  border-bottom: 1px solid #d6d6d6;
}
</style>
