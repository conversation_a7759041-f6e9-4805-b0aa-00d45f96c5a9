'use strict';

const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const ConstructionMeasureTypeConstant = require("../../../electron/enum/ConstructionMeasureTypeConstant");
const { ObjectUtils } = require("../../../electron/utils/ObjectUtils");
const InsertStrategy = require("../../../electron/main_editor/insert/insertStrategy");
const ZsProjectCgCostMathService = require("../../../electron/service/ZsProjectCgCostMathService");
const JieSuanConstructCostMathService = require("./jieSuanConstructCostMathService");

/**
 * 装饰工程超高费用记取
 * @class
 */
class JieSuanZsProjectCgCostMathService extends ZsProjectCgCostMathService {

  constructor(ctx) {
    super(ctx);
    this.jieSuanConstructCostMathService =
        ObjectUtils.isNotEmpty(this.service.jieSuanConstructCostMathService)?this.service.jieSuanConstructCostMathService :new JieSuanConstructCostMathService(ctx);

    // this.jieSuanConstructCostMathService = new JieSuanConstructCostMathService(ctx);
   }



  /**
   * 超高清单下挂上超高定额
   * @param qdList 原始的清单数据，一定是从分部分项或者措施项目中取出来的数据
   * @param deList 定额数据
   */
  async cgQdAddDe(unit, qd, deList, constructionMeasureType, groupByUp, is22De) {
    let { constructId, spId, sequenceNbr } = unit;

    if (ObjectUtils.isEmpty(qd)) {
      return;
    }
    if (ObjectUtils.isEmpty(deList)) {
      return;
    }
    //获取分组后的费用定额编码集合
    const coseDeCodeList = Array.from(groupByUp.keys());
    //循环前端分组
    for (const deCode of coseDeCodeList) {
      //获取费用定额数据
      let costDe = deList.find(k => k.deCode === deCode);
      // deCode对应的页面传来的 基数定额
      const baseDeByPage = groupByUp.get(deCode);
      let titleDeList = null;

      // 构建基础的要添加的定额数据
      let addDe = this.dataHandler(costDe, is22De, baseDeByPage, unit);
      //循环基数定额
      let baseDeList = groupByUp.get(deCode);

      //获取单位下所有人材机数据
      let rcjList = PricingFileFindUtils.getRcjList(constructId, spId, sequenceNbr);
      //重新人材机计算合计数量以及合价
      let deMathBase = this.updateTotalNumber(baseDeList, unit, rcjList, addDe);
      //赋值计算基数
      addDe.formula = deMathBase;
      addDe.caculatePrice = 1;
      addDe.baseNum = { def: deMathBase };
      if (is22De) {
        addDe.baseNum = 1;
      }

      if (constructionMeasureType === ConstructionMeasureTypeConstant.FBFX) {
        // 检查要添加的定额所属清单是不是临时删除的  如果是临时删除的就需要恢复这个清单和清单下的所有数据
        this.service.itemBillProjectOptionService.cleanQdDelTempStatus({
          constructId: constructId,
          singleId: spId,
          unitId: sequenceNbr,
          id: qd.sequenceNbr,
          modelType: 1,
          tempDeleteFlag: false
        });
        let insertStrategy = new InsertStrategy({
          constructId,
          singleId: spId,
          unitId: sequenceNbr,
          pageType: 'fbfx'
        });
        addDe = await insertStrategy.execute({
          pointLine: qd,
          newLine: addDe,
          indexId: costDe.sequenceNbr,
          libraryCode: costDe.libraryCode,
          option: 'insert',
          skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
          overwriteColumn: false
        });
        titleDeList = PricingFileFindUtils.getUnit(constructId, spId, sequenceNbr).itemBillProjects;
      } else {
        // 检查要添加的定额所属清单是不是临时删除的  如果是临时删除的就需要恢复这个清单和清单下的所有数据
        this.service.itemBillProjectOptionService.cleanQdDelTempStatus({
          constructId: constructId,
          singleId: spId,
          unitId: sequenceNbr,
          id: qd.sequenceNbr,
          modelType: 2,
          tempDeleteFlag: false
        });
        //插入定额
        let insertStrategy = new InsertStrategy({
          constructId,
          singleId: spId,
          unitId: sequenceNbr,
          pageType: 'csxm'
        });
        addDe = await insertStrategy.execute({
          pointLine: qd,
          newLine: addDe,
          indexId: costDe.sequenceNbr,
          libraryCode: costDe.libraryCode,
          option: 'insert',
          skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
          overwriteColumn: false
        });
        titleDeList = PricingFileFindUtils.getUnit(constructId, spId, sequenceNbr).measureProjectTables;
      }
      await this.jieSuanConstructCostMathService.initRcj(unit, addDe)

      //费用定额的人材机数据
      let deRcj = rcjList.filter(k => k.deId === addDe.sequenceNbr);

      // 在超高的工程量发生变化后  重新计算其下的人材机的合计数量和合价
      await this.computeCgRcjTotalNumberAndTotal(is22De, deRcj, addDe, deMathBase);

      await this.service.cszRgfAdjustProcess.checkDeRgfAdjustAndAddIfNecessary(constructId, spId, sequenceNbr, addDe);

      //计算单价构成
      this.service.unitPriceService.caculataDEUnitPrice(constructId, spId, sequenceNbr,
        addDe.sequenceNbr, true, titleDeList, false);
    }
  }



}

JieSuanZsProjectCgCostMathService.toString = () => '[class JieSuanZsProjectCgCostMathService]';
module.exports = JieSuanZsProjectCgCostMathService;
