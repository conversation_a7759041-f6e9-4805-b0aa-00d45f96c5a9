<!--
 * @@Descripttion:
 * @Author: wangru
 * @Date: 2023-06-21 10:53:30
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-27 17:36:01
-->
<template>
  <vxe-modal
    v-bind="getBindValue"
    :before-hide-method="beforeHideMethod"
    ref="modalRef"
  >
    <template v-if="slots.header" #header>
      <slot name="header"></slot>
    </template>
    <template v-if="slots.corner" #corner>
      <slot name="corner"></slot>
    </template>
    <a-spin
      :spinning="loadingModal"
      :tip="loadingTip"
      wrapperClassName="spin-noMask dialog-modal"
    >
      <!-- :show-zoom="props.maxOrMin" -->

      <template v-if="slots.default" #default>
        <slot name="default"></slot>
      </template>

      <template v-if="slots.footer" #footer>
        <slot name="footer"></slot>
      </template>
    </a-spin>
  </vxe-modal>
</template>
<script>
export default {
  name: 'commonModal',
};
</script>
<script setup>
import XEUtils from 'xe-utils';
import { propTypes } from '@/utils/propTypes';
import { computed, useAttrs, useSlots, ref, watch } from 'vue';
const slots = useSlots();

const props = defineProps({
  id: propTypes.string.def('model_1'),
  modelValue: propTypes.bool.def(false),
  fullscreen: propTypes.bool.def(false),
  loading: propTypes.bool.def(false),
  title: propTypes.string.def('弹窗'),
  width: propTypes.string.def('800'),
  isNoClose: propTypes.bool.def(false),
  position: propTypes.object.def({
    top: '10vh',
  }),
  loadingModal: propTypes.bool.def(false),
  loadingTip: propTypes.string.def('数据加载中,请稍后...'),
  mask: propTypes.bool.def(true), //遮罩层是否展示
  lockView: propTypes.bool.def(true), // 是否锁住页面，不允许窗口之外的任何操作
  lockScroll: propTypes.bool.def(true), // 是否锁住滚动条，不允许页面滚动
  // maxOrMin: propTypes.bool.def(false), //是否有放大缩小窗口功能
  destroyOnClose: propTypes.bool.def(false), // 是否销毁弹框内容
});

const getBindValue = computed(() => {
  const attrs = useAttrs();
  const obj = { ...attrs, ...props };
  return obj;
});
const beforeHideMethod = () => {
  console.log('beforeHideMethod', props.isNoClose);
  if (props.isNoClose) {
    return new Error();
  } else {
    emits('update:modelValue', false);
  }
};
const emits = defineEmits(['update:modelValue']);
const close = () => {
  emits('update:modelValue', false);
};
let modalRef = ref();
const resizeUpdatePosition = () => {
  const dom = modalRef.value?.getBox();
  if (dom) {
    const { width, height } = dom.getBoundingClientRect();
    const bodyRect = document.body.getBoundingClientRect();
    const left = (bodyRect.width - width) / 2;
    const top = (bodyRect.height - height) / 2;
    modalRef.value.setPosition(top, left);
    console.log('监听到了变化', width, height, bodyRect, top, left);
  }
};
const resizeFun = XEUtils.debounce(() => {
  console.log('监听到了变化resizeFun');
  resizeUpdatePosition();
}, 500);
watch(
  () => props.modelValue,
  val => {
    if (val) {
      window.addEventListener('resize', resizeFun);
    } else {
      window.removeEventListener('resize', resizeFun);
      console.log('removeEventListener resizeFun');
    }
  }
);
</script>

<style lang="scss"></style>
