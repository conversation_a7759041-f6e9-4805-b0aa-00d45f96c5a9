const _ = require('lodash');
const EE = require('../../core/ee');
const OtherProjectCalculationBaseConstant = require("../enum/OtherProjectCalculationBaseConstant");
const {NumberUtil} = require("../utils/NumberUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");

const CheckListKey= {
    QD_CODE_REPEAT: {checkType: 1, checkTypeName: "检查项目清单编码重复"},
    QD_UNIT_INCONSISTENT: {checkType: 2, checkTypeName: "相同清单单位不一致"},
    QD_PRICE_INCONSISTENT: {checkType: 3, checkTypeName: "检查清单综合单价是否一致"},
    QD_NAME_EMPTY: {checkType: 4, checkTypeName: "清单名称为空"},
    QD_CODE_EMPTY: {checkType: 5, checkTypeName: "清单项目编码为空"},
    QD_ATTR_EMPTY: {checkType: 6, checkTypeName: "清单项目特征为空"},
    QD_UNIT_EMPTY: {checkType: 7, checkTypeName: "清单单位为空"},
    QD_QUANTITY_EMPTY: {checkType: 8, checkTypeName: "清单工程量为空或零"},
    QD_PRICE_ZERO: {checkType:9, checkTypeName: "清单综合单价为零"},
    DE_QUANTITY_ZERO: {checkType: 10, checkTypeName: "定额工程量为零"},
    DE_PRICE_ZERO: {checkType: 11, checkTypeName: "定额单价为零"},
    RCJ_PRICE_INCONSISTENT: {checkType: 12, checkTypeName: "同一人材机有多个价格"},
    RCJ_QUANTITY_ZERO: {checkType: 13, checkTypeName: "人材机消耗量为零"},
    RCJ_PRICE_ZERO: {checkType: 14, checkTypeName: "人材机单价为零"},//
    DAY_WORK_TITLE_SERIAL_NUM_EMPTY: {checkType: 15, checkTypeName: "计日工标题序号为空"},
    DAY_WORK_TITLE_NAME_EMPTY: {checkType: 16, checkTypeName: "计日工标题名称为空"},
    DAY_WORK_SERIAL_NUM_EMPTY: {checkType: 17, checkTypeName: "计日工序号为空"},
    DAY_WORK_NAME_EMPTY: {checkType: 18, checkTypeName: "计日工名称为空"},
    SERVICE_COST_TITLE_SERIAL_NUM_EMPTY: {checkType: 19, checkTypeName: "总承包服务费标题序号为空"},
    SERVICE_COST_TITLE_NAME_EMPTY: {checkType: 20, checkTypeName: "总承包服务费标题名称为空"},
    SERVICE_COST_SERIAL_NUM_EMPTY: {checkType: 21, checkTypeName: "总承包服务费序号为空"},
    SERVICE_COST_NAME_EMPTY: {checkType: 22, checkTypeName: "总承包服务费名称为空"},
    QD_QUANTITY_DECIMAL_POINT_ERROR: {checkType: 23, checkTypeName: "清单工程量小数超过6位（河北省接口）"},
    OTHER_SUM_PRICE_CONSISTENCY: {checkType: 24, checkTypeName: "其他项目汇总、明细金额一致性（河北省接口）"},
    DAY_WORK_TITLE_UNIQUENESS: {checkType: 25, checkTypeName: "计日工明细标题（人工、材料、机械）唯一性"},
    LIMITED_PRICE_QD: {checkType: 27, checkTypeName: "清单综合单价>最高限价"},
    LIMITED_PRICE_FB: {checkType: 28, checkTypeName: "分部综合合价>最高限价"},
    ZGRCJ_ASSOCIATION: {checkType: 29, checkTypeName: "暂估材料是否关联"},
    WITHOUT_GROUP_PRICE_QD: {checkType: 26, checkTypeName: "未组价清单"},


}
const ExtendCheckListKey = {
    QD_CODE_REPEAT_BC: {checkType: 94, checkTypeName: "检查项目补充清单编码重复或不规范"},
    QD_UNIT_INCONSISTENT_BC: {checkType: 95, checkTypeName: "相同补充清单单位不一致"},
    QD_PRICE_INCONSISTENT_BC: {checkType: 96, checkTypeName: "检查补充清单综合单价是否一致"},
}


const resultTitle = {
    [CheckListKey.QD_CODE_REPEAT.checkType]: "标准清单编码重复",
    [ExtendCheckListKey.QD_CODE_REPEAT_BC.checkType]: "补充清单编码重复或不规范",
    [CheckListKey.QD_UNIT_INCONSISTENT.checkType]: "相同标准清单单位不一致",
    [ExtendCheckListKey.QD_UNIT_INCONSISTENT_BC.checkType]: "相同补充清单单位不一致",
    [CheckListKey.QD_PRICE_INCONSISTENT.checkType]: "标准清单综合单价不一致",
    [ExtendCheckListKey.QD_PRICE_INCONSISTENT_BC.checkType]: "补充清单综合单价不一致",
}

/*
标准清单编码重复、
相同标准清单单位不一致、
补充清单编码重复或不规范、
相同补充清单单位不一致、
清单名称为空、
清单项目编码为空、
清单项目特征为空、
清单单位为空、
清单工程量为空或零、
清单综合单价为零、
清单工程量小数超过6位（河北省接口）、
未组价清单
* */
function transformErrorList(errorList) {
    errorList = errorList.map(item => {
        return {
            uniqueStr:item.sequenceNbr,
            code: item.bdCode,//清单编码
            name: item.name,//清单名称
            unit: item.unit,//单位
            projectAttr: item.projectAttr,//项目特征
            //price: item.price?((Number(item.price).toFixed(2))+""):"0.00",//综合单价
            price: item.price,//综合单价
            //total: item.total?((Number(item.total).toFixed(2))+""):"0.00",//综合合价
            total: item.total,//综合合价
            //ceilingPrice: item.ceilingPrice?((Number(item.ceilingPrice).toFixed(2))+""):"0.00",//限价
            ceilingPrice: item.ceilingPrice?((Number(item.ceilingPrice).toFixed(2))+""):"0",//限价
            //quantity: (Number(item.quantity).toFixed(6))+"",//工程量
            quantity: item.quantity,//工程量
            sequenceNbr: item.sequenceNbr,//序号
            remark: item.description,//备注
            spId: item.spId,
            upId: item.upId,
            constructId : item.constructId,
            bizType: item.bizType,
            isLocked:item.isLocked,
        };
    });
    return errorList;
}

//检查项目清单编码重复；
async function qd_code_repeat() {
    let errorListAll = [];
    let count = 0;
    let allInOne = this.getAllQD();
    for (let qd of allInOne) {
        if (ObjectUtils.isEmpty(qd.bdCode) && !ObjectUtils.isEmpty(qd.fxCode)){
            qd.bdCode = qd.fxCode
        }

    }
    //是否有锁定清单
    let qdIsLocked = false;
    allInOne = allInOne.filter(item => item.standardId && item.bdCode);
    let froupAllInone = _.groupBy(allInOne, "bdCode");
    for (const froupAllInoneKey in froupAllInone) {
        if (_.isEqual("undefined", froupAllInoneKey)) continue;
        let errorList = froupAllInone[froupAllInoneKey];


        if (errorList.length > 1) {
            let qdFuIsLocked = false;
            errorList = transformErrorList(errorList);
            count+=errorList.length;
            for (let errorListElement of errorList) {
                if (errorListElement.isLocked == 1){
                    qdIsLocked = true;
                    qdFuIsLocked = true;
                }
            }

            errorListAll.push({uniqueStr:froupAllInoneKey+"1",code:froupAllInoneKey,childrenList: errorList,qdFuIsLocked:qdFuIsLocked});
        }
    }
    //错误信息和结果收集 安需求文档 不同返回的 对象类型不同
    return {count,error:errorListAll,qdIsLocked:qdIsLocked};
}


/**
 * 检查清单编码 不规范 ; 返回 true 表示为不规范
 * @param str
 * @returns {Promise<boolean>}
 */
function isStringInvalid(str) {
    const length = str.length;
    // 检查长度是否为9或12位
    const isLengthValid = length === 12;
    // 检查第七位是否是'B'（注意索引是6）
    const isSeventhB = length >= 7 && str.charAt(6) === 'B';
    // 返回结果：当长度无效 或 第七位无效时返回 true
    return !isLengthValid || !isSeventhB;
}


/**
 * 判断 单位是否一致  返回 false 为单位不一致
 * @param list
 * @param propertyName
 * @returns {Promise<boolean|*>}
 */
function isPropertyConsistent(list, propertyName) {
    if (!Array.isArray(list)) return false;
    if (list.length === 0) return false;

    const firstItem = list[0];
    if (!(propertyName in firstItem)) return false;

    const firstValue = firstItem[propertyName];

    return list.every(item => {
        // 确保每个元素都有该属性
        return (propertyName in item) && (item[propertyName] === firstValue);
    });
}

//检查项目补充清单编码重复或不规范；
async function qd_code_repeat_bc() {
    let errorListAll = [];
    let allInOne = this.getAllQD();
    let count = 0;
    allInOne = allInOne.filter(item => !item.standardId && item.bdCode);
    let froupAllInone = _.groupBy(allInOne, "bdCode");
    let set = new Set();
    //单位重复
    let isUnitCf = false;
    //编码重复
    let isCodeCf = false;
    //编码规范
    let isCodeGf = false;
    //是否有锁定清单
    let qdIsLocked = false;
    //重复检查
    for (const froupAllInoneKey in froupAllInone) {
        if (_.isEqual("undefined", froupAllInoneKey)) continue;
        let errorList = froupAllInone[froupAllInoneKey];
        if (errorList.length > 1) {
            let qdFuIsLocked = false;
            isCodeCf = true;
            set.add(froupAllInoneKey);
            let isInvalid ;
            isInvalid =  isStringInvalid(froupAllInoneKey);
            if (isInvalid){
                isCodeGf = true;
            }
            let isConsistent;
            isConsistent =   isPropertyConsistent(errorList,"unit");
            if (!isConsistent){
                isUnitCf = true;
            }

            for (let errorListElement of errorList) {
                if (errorListElement.isLocked == 1){
                    qdIsLocked = true;
                    qdFuIsLocked = true;
                }
            }
            errorList = transformErrorList(errorList);
            count+=errorList.length;
            errorListAll.push({code:froupAllInoneKey,childrenList: errorList,qdFuIsLocked:qdFuIsLocked});
        }
    }
    //规范检查
    for (let qd of allInOne) {
        if (!ObjectUtils.isEmpty(qd.bdCode) && !set.has(qd.bdCode)){
            let isInvalid;
            isInvalid =  isStringInvalid(qd.bdCode);
           if (isInvalid){
               let qdFuIsLocked = false;
               if (qd.isLocked == 1){
                   qdIsLocked = true;
                   qdFuIsLocked =true;
               }
               isCodeGf = true;
               let qdList = [qd];
               qdList = transformErrorList(qdList);
               count+=1;
               errorListAll.push({code:qd.bdCode,childrenList: qdList,qdFuIsLocked:qdFuIsLocked});
           }
        }
    }


    //错误信息和结果收集 安需求文档 不同返回的 对象类型不同
    return {count,error:errorListAll,isUnitCf:isUnitCf,isCodeCf:isCodeCf,isCodeGf:isCodeGf,qdIsLocked:qdIsLocked};
}



//相同清单单位不一致
//首先获取所有清单 截取清单的前9为 判断是否相同 如果相同 则判断单位是否一致
//并返回单位不同的清单

async function qd_unit_inconsistent() {
    // 所有清单
    const allInOne = this.getAllQD();
    // 过滤掉fxCode是undefined的数据
    const filteredData = allInOne.filter(item => !_.isEmpty(item.bdCode) && item.standardId);
    // 根据fxCode的前9位截取进行分组
    const groupedData = filteredData.reduce((acc, item) => {
        const key = item.bdCode.substring(0, 9);
        if (!acc[key]) {
            acc[key] = [];
        }
        acc[key].push(item);
        return acc;
    }, {});
    // 遍历分组后的数据，查看单位是否一致，并返回单位不一致的清单
    const errorListAll = [];
    let count = 0;
    for (const froupAllInoneKey in groupedData) {
        //获取分组后的数据
        let items = groupedData[froupAllInoneKey];
        //如果items的长度小于2 则不需要进行比较
        if (items.length < 2) continue;
        let firstUnit = items[0].unit;
        //判断items unit不一致的情况
        let arrError = items.filter((item, index) => index > 0 && item.unit != firstUnit);
        if (arrError.length > 0) {
            //有一个不一样就直接把全部的都返回
            count+=items.length;
            items = transformErrorList(_.cloneDeep(items));
            errorListAll.push({code:froupAllInoneKey,childrenList: items});
        }
    }
    return {count,error:errorListAll};
}

//相同补充清单单位不一致
async function qd_unit_inconsistent_bc() {
    // 所有清单
    const allInOne = this.getAllQD();
    let count = 0;
    // 过滤掉fxCode是undefined的数据
    const filteredData = allInOne.filter(item => !_.isEmpty(item.bdCode) && !item.standardId);
    // 根据fxCode的前9位截取进行分组
    const groupedData = filteredData.reduce((acc, item) => {
        const key = item.bdCode.substring(0, 9);
        if (!acc[key]) {
            acc[key] = [];
        }
        acc[key].push(item);
        return acc;
    }, {});
    // 遍历分组后的数据，查看单位是否一致，并返回单位不一致的清单
    const errorListAll = [];
    for (const froupAllInoneKey in groupedData) {
        //获取分组后的数据
        let items = groupedData[froupAllInoneKey];
        //如果items的长度小于2 则不需要进行比较
        if (items.length < 2) continue;
        let firstUnit = items[0].unit;
        //判断items unit不一致的情况
        let arrError = items.filter((item, index) => index > 0 && item.unit !== firstUnit);
        if (arrError.length > 0) {
            //有一个不一样就直接把全部的都返回
            count+=items.length;
            items = transformErrorList(_.cloneDeep(items));
            errorListAll.push({code:froupAllInoneKey,childrenList: items});
        }
    }
    return {count,error:errorListAll};
}

//清单名称为空
async function qd_name_empty() {
    let allInOne = this.getAllQD();

    let nameEmptyList = _.filter(allInOne, (item) => !_.isEmpty(item.bdCode)&&_.isEmpty(item.name));
    nameEmptyList = transformErrorList(_.cloneDeep(nameEmptyList));
    return nameEmptyList;
}

//清单项目编码为空
async function qd_code_empty() {
    let allInOne = this.getAllQD();
    let fxCodeEmptyList = _.filter(allInOne, (item) => _.isEmpty(item.bdCode));
    fxCodeEmptyList = transformErrorList(_.cloneDeep(fxCodeEmptyList));
    return fxCodeEmptyList;
}

//清单项目特征为空
async function qd_attr_empty() {
    let allInOne = this.getAllQD();
    let projectAttrEmptyList = _.filter(allInOne, (item) => !_.isEmpty(item.bdCode)&&_.isEmpty(item.projectAttr));
    projectAttrEmptyList = transformErrorList(_.cloneDeep(projectAttrEmptyList));
    return projectAttrEmptyList;
}

//清单单位为空
async function qd_unit_empty() {
    let allInOne = this.getAllQD();
    let projectAttrEmptyList = _.filter(allInOne, (item) => !_.isEmpty(item.bdCode)&&_.isEmpty(item.unit));
    projectAttrEmptyList = transformErrorList(_.cloneDeep(projectAttrEmptyList));
    return projectAttrEmptyList;
}

//清单工程量为空或零
async function qd_quantity_empty() {
    let allInOne = this.getAllQD();
    let projectAttrEmptyList = _.filter(allInOne, (item) => !_.isEmpty(item.bdCode)&&(_.isEmpty(item.quantity + "") || item.quantity == 0));
    projectAttrEmptyList = transformErrorList(_.cloneDeep(projectAttrEmptyList));
    return projectAttrEmptyList;
}

//清单综合单价为零
async function qd_price_zero() {
    let allInOne = this.getAllQD();
    let priceEmptyList = _.filter(allInOne, (item) => item.bdCode&&(item.price&&(_.isEmpty(item.price + "") || item.price == 0)));
    priceEmptyList = transformErrorList(_.cloneDeep(priceEmptyList));
    return priceEmptyList;
}

//定额工程量为零
async function de_quantity_zero() {
    let allInOne = this.getAllDE();
    let quantityEmptyList = _.filter(allInOne, (item) => _.isEmpty(item.quantity + "") || item.quantity == 0);
    quantityEmptyList =quantityEmptyList.map(item=>{
        item.uniqueStr=item.sequenceNbr;
        item.code = item.bdCode;
        item.remark = item.description;
        item.price = item.zjfPrice?((Number(item.zjfPrice).toFixed(2))+""):"0.00";
        return item;});
    return quantityEmptyList;
}

//定额单价为零
async function de_price_zero() {
    let allInOne = this.getAllDE();
    let zjfPriceEmptyList = _.filter(allInOne, (item) => _.isEmpty(item.zjfPrice + "") || item.zjfPrice == 0)
    zjfPriceEmptyList =zjfPriceEmptyList.map(item=>{
        item.uniqueStr=item.sequenceNbr;
        item.code = item.bdCode;
        item.remark = item.description;
        item.price = item.zjfprice?((Number(item.zjfPrice).toFixed(2))+""):"0.00";
        return item;});
    return zjfPriceEmptyList;
}

//同一人材机有多个价格
async function rcj_price_inconsistent() {
    let allRrjSummary = this.getAllRrjSummary();
    // 根据多个字段分组
    const groupedData = allRrjSummary.reduce((result, item) => {
        // 生成分组的键，
        const key = item.materialName + '-' + item.kind + '-' + item.specification + '-' + item.unit /*+ '-' + item.marketPrice*/;
        // 如果该分组尚不存在，则创建一个新的分组
        if (!result[key]) {
            result[key] = [];
        }
        // 将当前项添加到对应的分组中
        result[key].push(item);
        return result;
    }, {});
    // 筛选分组后有两条或更多记录的数据
    const filteredData = Object.values(groupedData).filter(group => {
        const marketPrices = new Set(group.map(item => item.marketPrice));
        return marketPrices.size > 1;  // 如果分组中的 marketPrice 有不一致的结果，保留该分组
    });
  let count = 0;
    let res = [];
    //材料编码、材料名称、单位、消耗量、单价、备注
    if (!ObjectUtils.isEmpty(filteredData)) {

        for (let i = 0; i < filteredData.length; i++) {
            let childrenList = {};
            let data = filteredData[i];
            childrenList.name = data[0].materialName;
            childrenList.code = data[0].materialCode;
            childrenList.uniqueStr = 'TYRCJYDGJG-'+i;
            let sons = [];
            for (let j = 0; j < data.length; j++) {
                let item = data[j];
                let child = {};
                child.code = item.materialCode;
                child.name = item.materialName;
                child.unit = item.unit;
                child.quantity = item.resQty;
                child.price = item.marketPrice;
                child.remark = item.description;
                child.sequenceNbr = item.sequenceNbr;
                child.constructId = item.constructId;
                child.spId = item.spId;
                child.upId = item.upId;
                child.bizType = 'rcj_summary_cp';
                child.uniqueStr = item.sequenceNbr;
                sons.push(child);
            }
            count+=sons.length;
            childrenList.childrenList = sons;
            res.push(childrenList);
        }
    }
    return {count,error:res};
}

//人材机消耗量为零

async function rcj_quantity_zero() {
    let {service} = EE.app;
    let allDE = this.getAllDE();
    //需要考虑到
    let allQD = this.getAllQD();
    let childrenList =[];
    if(!ObjectUtils.isEmpty(allDE)){
        for (let i = 0; i < allDE.length; i++) {
            let de = allDE[i];
            let rcjList = service.rcjProcess.queryRcjDataByDeId(de.sequenceNbr,de. constructId, de.spId, de.upId);
            if(!ObjectUtils.isEmpty(rcjList)){
                for (let j = 0; j < rcjList.length; j++) {
                    let rcj = rcjList[j];
                    // if(de.rcjFlag === 1){
                        //定额类型的人材机

                        if(ObjectUtils.isEmpty(rcj.resQty) ||rcj.resQty==0 ){
                            let child = {};
                            child.code = rcj.materialCode;
                            child.name = rcj.materialName;
                            child.unit = rcj.unit;
                            child.quantity = rcj.resQty;
                            child.price = rcj.marketPrice;
                            child.remark = de.description;
                            child.sequenceNbr = rcj.sequenceNbr;
                            child.constructId = de.constructId;
                            child.spId = de.spId;
                            child.upId = de.upId;
                            child.bizType = 'rcj';
                            child.uniqueStr = rcj.sequenceNbr;
                            child.deId = de.sequenceNbr;
                            child.moduleType = de.moduleType;
                            childrenList.push(child);
                        }
                        if(!ObjectUtils.isEmpty(rcj.rcjDetailsDTOs)){
                            for (let k = 0; k < rcj.rcjDetailsDTOs.length; k++) {
                                let rcjDetails = rcj.rcjDetailsDTOs[k];
                                if(ObjectUtils.isEmpty(rcjDetails.resQty) ||rcjDetails.resQty==0 ){
                                    let child = {};
                                    child.code = rcjDetails.materialCode;
                                    child.name = rcjDetails.materialName;
                                    child.unit = rcjDetails.unit;
                                    child.quantity = rcjDetails.resQty;
                                    child.price = rcjDetails.marketPrice;
                                    child.remark = de.description;
                                    child.sequenceNbr = rcjDetails.sequenceNbr;
                                    child.constructId = de.constructId;
                                    child.spId = de.spId;
                                    child.upId = de.upId;
                                    child.bizType = 'rcj';
                                    child.uniqueStr = rcjDetails.sequenceNbr;
                                    child.deId = de.sequenceNbr;
                                    child.moduleType = de.moduleType;
                                    childrenList.push(child);
                                }
                            }
                        }
                    // }else {
                    //     if(ObjectUtils.isEmpty(rcj.resQty) ||rcj.resQty==0 ){
                    //         let child = {};
                    //         child.code = rcj.materialCode;
                    //         child.name = rcj.materialName;
                    //         child.unit = rcj.unit;
                    //         child.quantity = rcj.resQty;
                    //         child.price = rcj.marketPrice;
                    //         child.remark = de.description;
                    //         child.sequenceNbr = rcj.sequenceNbr;
                    //         child.constructId = rcj.constructId;
                    //         child.spId = de.spId;
                    //         child.upId = de.upId;
                    //         child.bizType = 'rcj';
                    //         child.uniqueStr = rcj.sequenceNbr;
                    //         child.deId = de.sequenceNbr;
                    //         child.moduleType = de.moduleType;
                    //         childrenList.push(child);
                    //
                    //         if(!ObjectUtils.isEmpty(rcj.rcjDetailsDTOs)){
                    //             for (let k = 0; k < rcj.rcjDetailsDTOs.length; k++) {
                    //                 let rcjDetails = rcj.rcjDetailsDTOs[k];
                    //                 if(ObjectUtils.isEmpty(rcjDetails.resQty) ||rcjDetails.resQty==0 ){
                    //                     let child = {};
                    //                     child.code = rcjDetails.materialCode;
                    //                     child.name = rcjDetails.materialName;
                    //                     child.unit = rcjDetails.unit;
                    //                     child.quantity = rcjDetails.resQty;
                    //                     child.price = rcjDetails.marketPrice;
                    //                     child.remark = de.description;
                    //                     child.sequenceNbr = rcjDetails.sequenceNbr;
                    //                     child.constructId = rcjDetails.constructId;
                    //                     child.spId = de.spId;
                    //                     child.upId = de.upId;
                    //                     child.bizType = 'rcj';
                    //                     child.uniqueStr = rcjDetails.sequenceNbr;
                    //                     child.deId = de.sequenceNbr;
                    //                     child.moduleType = de.moduleType;
                    //                     childrenList.push(child);
                    //                 }
                    //             }
                    //         }
                    //
                    //     }
                    // }
                }
            }
        }
    }

    return childrenList;


}

//人材机单价为零

async function rcj_price_zero() {
    let allDE = this.getAllDE();
    let {service} = EE.app;
    let childrenList =[];
    if(!ObjectUtils.isEmpty(allDE)){
        for (let i = 0; i < allDE.length; i++) {
            let de = allDE[i];
            let rcjList = service.rcjProcess.queryRcjDataByDeId(de.sequenceNbr,de. constructId, de.spId, de.upId);
            if(!ObjectUtils.isEmpty(rcjList)){
                for (let j = 0; j < rcjList.length; j++) {
                    let rcj = rcjList[j];
                    // if(de.rcjFlag === 1) {
                        //定额类型的人材机
                        if(ObjectUtils.isEmpty(rcj.marketPrice) ||rcj.marketPrice==0 ){
                            let child = {};
                            child.code = rcj.materialCode;
                            child.name = rcj.materialName;
                            child.unit = rcj.unit;
                            child.quantity = rcj.resQty;
                            child.price = rcj.marketPrice;
                            child.remark = de.description;
                            child.sequenceNbr = rcj.sequenceNbr;
                            child.constructId = de.constructId;
                            child.spId = de.spId;
                            child.upId = de.upId;
                            child.bizType = 'rcj';
                            child.uniqueStr = rcj.sequenceNbr;
                            child.deId = de.sequenceNbr;
                            child.moduleType = de.moduleType;
                            childrenList.push(child);
                        }
                        if(!ObjectUtils.isEmpty(rcj.rcjDetailsDTOs)){
                            for (let k = 0; k < rcj.rcjDetailsDTOs.length; k++) {
                                let rcjDetails = rcj.rcjDetailsDTOs[k];
                                if(ObjectUtils.isEmpty(rcjDetails.marketPrice) ||rcjDetails.marketPrice==0 ){
                                    let child = {};
                                    child.code = rcjDetails.materialCode;
                                    child.name = rcjDetails.materialName;
                                    child.unit = rcjDetails.unit;
                                    child.quantity = rcjDetails.resQty;
                                    child.price = rcjDetails.marketPrice;
                                    child.remark = de.description;
                                    child.sequenceNbr = rcjDetails.sequenceNbr;
                                    child.constructId = de.constructId;
                                    child.spId = de.spId;
                                    child.upId = de.upId;
                                    child.bizType = 'rcj';
                                    child.uniqueStr = rcjDetails.sequenceNbr;
                                    child.deId = de.sequenceNbr;
                                    child.moduleType = de.moduleType;
                                    childrenList.push(child);
                                }
                            }
                        }


                    // }else {
                    //     if(ObjectUtils.isEmpty(rcj.marketPrice) ||rcj.marketPrice==0 ){
                    //         let child = {};
                    //         child.code = rcj.materialCode;
                    //         child.name = rcj.materialName;
                    //         child.unit = rcj.unit;
                    //         child.quantity = rcj.resQty;
                    //         child.price = rcj.marketPrice;
                    //         child.remark = de.description;
                    //         child.sequenceNbr = rcj.sequenceNbr;
                    //         child.constructId = rcj.constructId;
                    //         child.spId = de.spId;
                    //         child.upId = de.upId;
                    //         child.bizType = 'rcj';
                    //         child.uniqueStr = rcj.sequenceNbr;
                    //         child.deId = de.sequenceNbr;
                    //         child.moduleType = de.moduleType;
                    //         childrenList.push(child);
                    //
                    //         if(!ObjectUtils.isEmpty(rcj.rcjDetailsDTOs)){
                    //             for (let k = 0; k < rcj.rcjDetailsDTOs.length; k++) {
                    //                 let rcjDetails = rcj.rcjDetailsDTOs[k];
                    //                 if(ObjectUtils.isEmpty(rcjDetails.marketPrice) ||rcjDetails.marketPrice==0 ){
                    //                     let child = {};
                    //                     child.code = rcjDetails.materialCode;
                    //                     child.name = rcjDetails.materialName;
                    //                     child.unit = rcjDetails.unit;
                    //                     child.quantity = rcjDetails.resQty;
                    //                     child.price = rcjDetails.marketPrice;
                    //                     child.remark = de.description;
                    //                     child.sequenceNbr = rcjDetails.sequenceNbr;
                    //                     child.constructId = rcjDetails.constructId;
                    //                     child.spId = de.spId;
                    //                     child.upId = de.upId;
                    //                     child.bizType = 'rcj';
                    //                     child.uniqueStr = rcjDetails.sequenceNbr;
                    //                     child.deId = de.sequenceNbr;
                    //                     child.moduleType = de.moduleType;
                    //                     childrenList.push(child);
                    //                 }
                    //             }
                    //         }
                    //     }
                    // }

                }
            }
        }
    }

    return childrenList;

}

//计日工标题序号为空
async function day_work_title_serial_num_empty() {
    let allDayWorks = this.getAllDayWorks();
    let childrenList = [];
    if (!ObjectUtils.isEmpty(allDayWorks)) {
        //过滤标题行
        let bt = allDayWorks.filter(item => item.dataType === 1);
        if (!ObjectUtils.isEmpty(bt)) {
            //筛选序号为空的数据
            let filter = bt.filter(item => ObjectUtils.isEmpty(item.dispNo));

            if (ObjectUtils.isEmpty(filter)) {
                return childrenList
            } else {
                // 序号、名称、规格型号、单位、数量、备注
                let childrenList = [];
                for (let i = 0; i < filter.length; i++) {
                    let item = filter[i];
                    let child = {};

                    child.sortNo = item.dispNo;
                    child.name = item.worksName;
                    child.specification = item.specification;
                    child.unit = item.unit;
                    child.amount = item.tentativeQuantity;
                    child.remark = item.description;
                    child.sequenceNbr = item.sequenceNbr;
                    child.constructId = item.constructId;
                    child.spId = item.spId;
                    child.upId = item.upId;
                    child.bizType = 'jrg';
                    child.uniqueStr = item.sequenceNbr;
                    childrenList.push(child)
                }

                return childrenList
            }

        }
    }
    return childrenList;
}

//计日工标题名称为空
async function day_work_title_name_empty() {
    let allDayWorks = this.getAllDayWorks();
    let childrenList = [];
    if (!ObjectUtils.isEmpty(allDayWorks)) {
        //过滤标题行
        let bt = allDayWorks.filter(item => item.dataType === 1);
        if (!ObjectUtils.isEmpty(bt)) {
            //筛选名称为空的数据
            let filter = bt.filter(item => ObjectUtils.isEmpty(item.worksName));

            if (ObjectUtils.isEmpty(filter)) {
                return childrenList
            } else {
                // 序号、名称、规格型号、单位、数量、备注
                let childrenList = [];
                for (let i = 0; i < filter.length; i++) {
                    let item = filter[i];
                    let child = {};
                    child.sortNo = item.dispNo;
                    child.name = item.worksName;
                    child.specification = item.specification;
                    child.unit = item.unit;
                    child.amount = item.tentativeQuantity;
                    child.remark = item.description;
                    child.sequenceNbr = item.sequenceNbr;
                    child.constructId = item.constructId;
                    child.spId = item.spId;
                    child.upId = item.upId;
                    child.bizType = 'jrg';
                    child.uniqueStr = item.sequenceNbr;
                    childrenList.push(child)
                }
                return childrenList
            }

        }
    }
    return childrenList
}

//计日工序号为空
async function day_work_serial_num_empty() {
    let allDayWorks = this.getAllDayWorks();
    let childrenList = [];
    if (!ObjectUtils.isEmpty(allDayWorks)) {
        //过滤标题行
        let bt = allDayWorks.filter(item => item.dataType === 2);
        if (!ObjectUtils.isEmpty(bt)) {
            //筛选名称为空的数据
            let filter = bt.filter(item => ObjectUtils.isEmpty(item.dispNo));

            if (ObjectUtils.isEmpty(filter)) {
                return childrenList
            } else {
                // 序号、名称、规格型号、单位、数量、备注
                let childrenList = [];
                for (let i = 0; i < filter.length; i++) {
                    let item = filter[i];
                    let child = {};
                    child.sortNo = item.dispNo;
                    child.name = item.worksName;
                    child.specification = item.specification;
                    child.unit = item.unit;
                    child.amount = item.tentativeQuantity;
                    child.remark = item.description;
                    child.sequenceNbr = item.sequenceNbr;
                    child.constructId = item.constructId;
                    child.spId = item.spId;
                    child.upId = item.upId;
                    child.bizType = 'jrg';
                    child.uniqueStr = item.sequenceNbr;
                    childrenList.push(child)
                }
                return childrenList
            }

        }
    }
    return childrenList
}

//计日工名称为空
async function day_work_name_empty() {
    let allDayWorks = this.getAllDayWorks();
    let childrenList = [];
    if (!ObjectUtils.isEmpty(allDayWorks)) {
        //过滤标题行
        let bt = allDayWorks.filter(item => item.dataType === 2);
        if (!ObjectUtils.isEmpty(bt)) {
            //筛选名称为空的数据
            let filter = bt.filter(item => ObjectUtils.isEmpty(item.worksName));

            if (ObjectUtils.isEmpty(filter)) {
                return childrenList
            } else {
                // 序号、名称、规格型号、单位、数量、备注
                let childrenList = [];
                for (let i = 0; i < filter.length; i++) {
                    let item = filter[i];
                    let child = {};
                    child.sortNo = item.dispNo;
                    child.name = item.worksName;
                    child.specification = item.specification;
                    child.unit = item.unit;
                    child.amount = item.tentativeQuantity;
                    child.remark = item.description;
                    child.sequenceNbr = item.sequenceNbr;
                    child.constructId = item.constructId;
                    child.spId = item.spId;
                    child.upId = item.upId;
                    child.bizType = 'jrg';
                    child.uniqueStr = item.sequenceNbr;
                    childrenList.push(child)
                }
                return childrenList
            }

        }
    }
    return childrenList
}

//总承包服务费标题序号为空
async function service_cost_title_serial_num_empty() {
    let allProjectServiceCosts = this.getAllProjectServiceCosts();
    let childrenList = [];
    if (!ObjectUtils.isEmpty(allProjectServiceCosts)) {
        //过滤标题行
        let bt = allProjectServiceCosts.filter(item => item.dataType === 1);
        if (!ObjectUtils.isEmpty(bt)) {
            //筛选序号为空的数据
            let filter = bt.filter(item => ObjectUtils.isEmpty(item.dispNo));

            if (ObjectUtils.isEmpty(filter)) {
                return childrenList
            } else {
                // 序号、名称、规格型号、单位、数量、备注
                let childrenList = [];
                for (let i = 0; i < filter.length; i++) {
                    let item = filter[i];
                    let child = {};
                    child.sortNo = item.dispNo;
                    child.name = item.fxName;
                    child.serviceContent = item.serviceContent;
                    child.unit = item.unit;
                    child.amount = item.amount;
                    child.total = item.fwje;
                    child.remark = item.description;
                    child.sequenceNbr = item.sequenceNbr;
                    child.constructId = item.constructId;
                    child.spId = item.spId;
                    child.upId = item.upId;
                    child.bizType = 'zcbfwf';
                    child.uniqueStr = item.sequenceNbr;
                    childrenList.push(child)
                }
                return childrenList
            }

        }
    }
    return childrenList
}

//总承包服务费标题名称为空
async function service_cost_title_name_empty() {
    let allProjectServiceCosts = this.getAllProjectServiceCosts();
    let childrenList = [];
    if (!ObjectUtils.isEmpty(allProjectServiceCosts)) {
        //过滤标题行
        let bt = allProjectServiceCosts.filter(item => item.dataType === 1);
        if (!ObjectUtils.isEmpty(bt)) {
            //筛选名称为空的数据
            let filter = bt.filter(item => ObjectUtils.isEmpty(item.fxName));

            if (ObjectUtils.isEmpty(filter)) {
                return childrenList
            } else {
                // 序号、名称、规格型号、单位、数量、备注
                let childrenList = [];
                for (let i = 0; i < filter.length; i++) {
                    let item = filter[i];
                    let child = {};
                    child.sortNo = item.dispNo;
                    child.name = item.fxName;
                    child.serviceContent = item.serviceContent;
                    child.unit = item.unit;
                    child.amount = item.amount;
                    child.total = item.fwje;
                    child.remark = item.description;
                    child.sequenceNbr = item.sequenceNbr;
                    child.constructId = item.constructId;
                    child.spId = item.spId;
                    child.upId = item.upId;
                    child.bizType = 'zcbfwf';
                    child.uniqueStr = item.sequenceNbr;
                    childrenList.push(child)
                }
                return childrenList
            }

        }
    }
    return childrenList
}

//总承包服务费序号为空
async function service_cost_serial_num_empty() {
    let allProjectServiceCosts = this.getAllProjectServiceCosts();
    let childrenList = [];
    if (!ObjectUtils.isEmpty(allProjectServiceCosts)) {
        //过滤数据行
        let bt = allProjectServiceCosts.filter(item => item.dataType === 2);
        if (!ObjectUtils.isEmpty(bt)) {
            //筛选名称为空的数据
            let filter = bt.filter(item => ObjectUtils.isEmpty(item.dispNo));

            if (ObjectUtils.isEmpty(filter)) {
                return childrenList
            } else {
                // 序号、名称、规格型号、单位、数量、备注
                let childrenList = [];
                for (let i = 0; i < filter.length; i++) {
                    let item = filter[i];
                    let child = {};
                    child.sortNo = item.dispNo;
                    child.name = item.fxName;
                    child.serviceContent = item.serviceContent;
                    child.unit = item.unit;
                    child.amount = item.amount;
                    child.total = item.fwje;
                    child.remark = item.description;
                    child.sequenceNbr = item.sequenceNbr;
                    child.constructId = item.constructId;
                    child.spId = item.spId;
                    child.upId = item.upId;
                    child.bizType = 'zcbfwf';
                    child.uniqueStr = item.sequenceNbr;
                    childrenList.push(child)
                }
                return childrenList
            }

        }
    }
    return childrenList
}

//总承包服务费名称为空
async function service_cost_name_empty() {
    let allProjectServiceCosts = this.getAllProjectServiceCosts();
    let childrenList = [];
    if (!ObjectUtils.isEmpty(allProjectServiceCosts)) {
        //过滤数据行
        let bt = allProjectServiceCosts.filter(item => item.dataType === 2);
        if (!ObjectUtils.isEmpty(bt)) {
            //筛选名称为空的数据
            let filter = bt.filter(item => ObjectUtils.isEmpty(item.fxName));

            if (ObjectUtils.isEmpty(filter)) {
                return childrenList
            } else {
                // 序号、名称、规格型号、单位、数量、备注
                let childrenList = [];
                for (let i = 0; i < filter.length; i++) {
                    let item = filter[i];
                    let child = {};
                    child.sortNo = item.dispNo;
                    child.name = item.fxName;
                    child.serviceContent = item.serviceContent;
                    child.unit = item.unit;
                    child.amount = item.amount;
                    child.total = item.fwje;
                    child.remark = item.description;
                    child.sequenceNbr = item.sequenceNbr;
                    child.constructId = item.constructId;
                    child.spId = item.spId;
                    child.upId = item.upId;
                    child.bizType = 'zcbfwf';
                    child.uniqueStr = item.sequenceNbr;
                    childrenList.push(child)
                }
                return childrenList
            }

        }
    }
    return childrenList
}

function countDecimalPlaces(num) {
    var str = num.toString();
    var index = str.indexOf('.');
    if (index >= 0) {
        return str.length - index - 1;
    } else {
        return 0;
    }
}

//清单工程量小数超过6位（河北省接口）
async function qd_quantity_decimal_point_error() {
    let allQD = this.getAllQD();
    let errorList = _.filter(allQD, (item) => !_.isEmpty(item.bdCode)&&item.quantity && countDecimalPlaces(item.quantity) > 6);
    errorList = transformErrorList(_.cloneDeep(errorList));
    return errorList;
}

//其他项目汇总、明细金额一致性（河北省接口）
async function other_sum_price_consistency() {
    let unitProjects = this.unitProjects;
    let childrenList =[];
    for (let i = 0; i < unitProjects.length; i++) {
        let unitProject = unitProjects[i];
        //获取其他项目总表
        let otherProjects  = unitProject.otherProjects;
        this.settingPublic(otherProjects,unitProject);

        //其他项目表暂列金额
        let zljr =    otherProjects.find(item => item.type === OtherProjectCalculationBaseConstant.zljr);
        //其他项目专业工程暂估价
        let zygczgj = otherProjects.find(item => item.type === OtherProjectCalculationBaseConstant.zygczgj);
        //其他项目表总承包服务费
        let zcbfwf =  otherProjects.find(item => item.type === OtherProjectCalculationBaseConstant.zcbfwf);
        //其他项目表计日工
        let jrg =     otherProjects.find(item => item.type === OtherProjectCalculationBaseConstant.jrg);



        let zljrSum = 0;
        let otherProjectProvisionals = unitProject.otherProjectProvisionals;//暂列金额
        if(!ObjectUtils.isEmpty(otherProjectProvisionals)){
            let filter = otherProjectProvisionals.filter(item =>item.dataType === 1);

            if(!ObjectUtils.isEmpty(filter)){
                zljrSum = filter.reduce((accumulator, item) => {
                    return NumberUtil.add( accumulator , item.provisionalSum);
                }, 0)
            }


        }

        let zygczgjSum= 0
        let otherProjectZygcZgjs = unitProject.otherProjectZygcZgjs;//专业工程暂估价
        if(!ObjectUtils.isEmpty(otherProjectZygcZgjs)){
            // let filter = otherProjectZygcZgjs.filter(item =>item.dataType === 1);

            // if(!ObjectUtils.isEmpty(filter)) {
                zygczgjSum = otherProjectZygcZgjs.reduce((accumulator, item) => {
                    return NumberUtil.add(accumulator, item.total);
                }, 0)
            // }
        }

        let zcbfwfSum =0
        let otherProjectServiceCosts = unitProject.otherProjectServiceCosts;//总承包服务费
        if(!ObjectUtils.isEmpty(otherProjectServiceCosts)){
            let filter = otherProjectServiceCosts.filter(item =>item.dataType === 1);

            if(!ObjectUtils.isEmpty(filter)) {
                zcbfwfSum = filter.reduce((accumulator, item) => {
                    return NumberUtil.add(accumulator, item.fwje);
                }, 0)
            }
        }

        let jrgSum =0
        let otherProjectDayWorks = unitProject.otherProjectDayWorks;//计日工
        if(!ObjectUtils.isEmpty(otherProjectDayWorks)){
            let filter = otherProjectDayWorks.filter(item =>item.dataType === 1);

            if(!ObjectUtils.isEmpty(filter)) {
                jrgSum = filter.reduce((accumulator, item) => {
                    return NumberUtil.add(accumulator, item.total);
                }, 0)
            }
        }

        if(zljr.total!=zljrSum){
            let child = {};
            child.sortNo          = zljr.dispNo;
            child.name            = zljr.extraName;
            child.serviceContent  = zljr.serviceContent;
            child.unit            = zljr.unit;
            child.amount          = zljr.amount;
            child.total           = zljr.total;
            child.remark          = zljr.description;
            child.sequenceNbr     = zljr.sequenceNbr;
            child.constructId     = zljr.constructId;
            child.spId            = zljr.spId;
            child.upId            = zljr.upId;
            child.bizType         = 'qtxm';
            childrenList.push(child)
        }

        if(zygczgj.total!=zygczgjSum){
            let child = {};
            child.sortNo          = zygczgj.dispNo;
            child.name            = zygczgj.extraName;
            child.serviceContent  = zygczgj.serviceContent;
            child.unit            = zygczgj.unit;
            child.amount          = zygczgj.amount;
            child.total           = zygczgj.total;
            child.remark          = zygczgj.description;
            child.sequenceNbr     = zygczgj.sequenceNbr;
            child.constructId     = zygczgj.constructId;
            child.spId            = zygczgj.spId;
            child.upId            = zygczgj.upId;
            child.bizType         = 'qtxm';
            childrenList.push(child)
        }

        if(zcbfwf.total!=zcbfwfSum){
            let child = {};
            child.sortNo          = zcbfwf.dispNo;
            child.name            = zcbfwf.extraName;
            child.serviceContent  = zcbfwf.serviceContent;
            child.unit            = zcbfwf.unit;
            child.amount          = zcbfwf.amount;
            child.total           = zcbfwf.total;
            child.remark          = zcbfwf.description;
            child.sequenceNbr     = zcbfwf.sequenceNbr;
            child.constructId     = zcbfwf.constructId;
            child.spId            = zcbfwf.spId;
            child.upId            = zcbfwf.upId;
            child.bizType         = 'qtxm';
            childrenList.push(child)
        }

        if(jrg.total!=jrgSum){
            let child = {};
            child.sortNo          = jrg.dispNo;
            child.name            = jrg.extraName;
            child.serviceContent  = jrg.serviceContent;
            child.unit            = jrg.unit;
            child.amount          = jrg.amount;
            child.total           = jrg.total;
            child.remark          = jrg.description;
            child.sequenceNbr     = jrg.sequenceNbr;
            child.constructId     = jrg.constructId;
            child.spId            = jrg.spId;
            child.upId            = jrg.upId;
            child.bizType         = 'qtxm';
            childrenList.push(child)
        }

    }

    return childrenList;
}

//计日工明细标题（人工、材料、机械）唯一性
async function day_work_title_uniqueness() {
    let unitProjects = this.unitProjects;
    let childrenList =[];
    for (let i = 0; i < unitProjects.length ; i++) {

        let otherProjectDayWorks = unitProjects[i].otherProjectDayWorks;

        if(!ObjectUtils.isEmpty(otherProjectDayWorks)){
            let bt = otherProjectDayWorks.filter(item => item.dataType === 1 );
            if(!ObjectUtils.isEmpty(bt)){
                let flag = true;
                let rg = bt.filter(item =>item.worksName ==='人工' );
                let cl = bt.filter(item =>item.worksName ==='材料' );
                let jx = bt.filter(item =>item.worksName ==='机械' );

                //其他标题
                let qt = bt.filter(item => item.worksName !== '人工' && item.worksName !== '材料' && item.worksName !== '机械');

                if(!ObjectUtils.isEmpty(qt)){
                    flag = false
                }
                if(!ObjectUtils.isEmpty(rg) && rg.length!==1){
                    flag = false
                }
                if(ObjectUtils.isEmpty(rg)){
                    flag = false
                }
                if(!ObjectUtils.isEmpty(cl) && cl.length!==1){
                    flag = false
                }
                if(ObjectUtils.isEmpty(cl)){
                    flag = false
                }
                if(!ObjectUtils.isEmpty(jx) && jx.length!==1){
                    flag = false
                }
                if(ObjectUtils.isEmpty(jx)){
                    flag = false
                }

                if (!flag){
                    for (let j = 0; j < bt.length; j++) {
                        let item = bt[j];
                        let child = {};
                        child.sortNo = item.dispNo;
                        child.name = item.worksName;
                        child.specification = item.specification;
                        child.unit = item.unit;
                        child.amount = item.tentativeQuantity;
                        child.remark = item.description;
                        child.sequenceNbr = item.sequenceNbr;
                        child.constructId = item.constructId;
                        child.spId = item.spId;
                        child.upId = item.upId;
                        child.bizType = 'jrg';
                        childrenList.push(child)
                    }
                }
            }

        }


    }


    return childrenList;
}
async function limited_price_fb() {
    //获得所有分部 price
    let allFb = this.getAllFb();
    let errorList = [];
    allFb = allFb.filter(item => !_.isEmpty(item.ceilingPrice)&&item.ceilingPrice<item.total);
    if(allFb.length){
        errorList = transformErrorList(_.cloneDeep(allFb));
    }
    return errorList;
}

//暂估材料是否关联
async function zgrcj_association(){
    let unitProjects = this.unitProjects;

    let errorList = [];
    for (let unitProject of unitProjects) {
        if (!ObjectUtils.isEmpty(unitProject.zgjRcjList)){
            let zgjRcjList = unitProject.zgjRcjList;
            zgjRcjList.forEach(i=>{
                if (ObjectUtils.isEmpty(i.relevancySequenceNbrList)){
                    errorList.push(i);
                }
            })
        }
    }

    return errorList;


}
//最高限价验证
async function limited_price_qd() {
    let errorList = [];
    //获得所有清单 price
    let allQD = this.getAllQD();
    allQD = allQD.filter(item => !_.isEmpty(item.ceilingPrice)&&item.ceilingPrice<item.price);
    if(allQD.length){
        errorList = errorList.concat(transformErrorList(_.cloneDeep(allQD))) ;
    }
    return errorList;
}

//未组价清单
async function without_group_price_qd() {
    let allQD = this.getAllQD();
    const alllDE = this.getAllDE();
    allQD = allQD.filter(item => !_.isEmpty(item.bdCode));
    let errorList = [];
    for (let i = 0; i < allQD.length; i++) {
        let qdItem = allQD[i];
        let f = alllDE.filter(item => item.parentId === qdItem.sequenceNbr);
        if (!f.length) {

            errorList.push(qdItem);
        }
    }
    errorList = transformErrorList(_.cloneDeep(errorList));
    return errorList;
}

//检查清单综合单价是否一致
async function qd_price_inconsistent() {
    const allItems = this.getAllQD();
    // 过滤掉没有 bdCode 的数据
    const filteredItems = allItems.filter(item => item.standardId &&!_.isEmpty(item.bdCode));
    // 根据指定属性的前9个字符进行分组
    const groupedItems = filteredItems.reduce((groups, item) => {
        const key = item.bdCode ? item.bdCode.substring(0, 9) : '';
        const groupKey = `${key}_${item.name}_${item.unit}_${item.projectAttr}`;
        if (!groups[groupKey]) {
            groups[groupKey] = [];
        }
        groups[groupKey].push(item);
        return groups;
    }, {});

    const errorGroups = [];
    let count = 0;
    for (const groupKey in groupedItems) {
        const items = groupedItems[groupKey];
        // 如果项目数量小于2个，则不需要比较
        if (items.length < 2) {
            continue;
        }
        // 第一个项目的 price 作为参照
         let first =items[0];
        const referencePrice = items[0].price;
        // 找到所有价格不一致的项
        let inconsistencies = items.filter((item, index) => index > 0 && item.price !== referencePrice);
        if (inconsistencies.length > 0) {
            inconsistencies = [first].concat(inconsistencies);
            inconsistencies =inconsistencies.map(item=>{
                item.uniqueStr=item.sequenceNbr;
                item.code = item.bdCode;
                item.price = item.price?((Number(item.price).toFixed(2))+""):"0.00";
                item.remark=item.description;//备注
                return item;
            });
            count+=inconsistencies.length;
            errorGroups.push({code:groupKey.split("_")[0],childrenList: inconsistencies});
        }
    }
    return {count,error:errorGroups};
}
async function qd_price_inconsistent_bc() {
    const allItems = this.getAllQD();
    // 过滤掉没有 bdCode 的数据
    const filteredItems = allItems.filter(item => !item.standardId &&!_.isEmpty(item.bdCode));
    // 根据指定属性的前9个字符进行分组
    const groupedItems = filteredItems.reduce((groups, item) => {
        const key = item.bdCode ? item.bdCode.substring(0, 9) : '';
        const groupKey = `${key}_${item.name}_${item.unit}_${item.projectAttr}`;
        if (!groups[groupKey]) {
            groups[groupKey] = [];
        }
        groups[groupKey].push(item);
        return groups;
    }, {});
   let count = 0;
    const errorGroups = [];
    for (const groupKey in groupedItems) {
        let items = groupedItems[groupKey];
        // 如果项目数量小于2个，则不需要比较
        if (items.length < 2) {
            continue;
        }
        // 第一个项目的 price 作为参照
        const referencePrice = items[0].price;
        // 找到所有价格不一致的项
        let inconsistencies = items.filter((item, index) => index > 0 && item.price !== referencePrice);
        if (inconsistencies.length > 0) {
            items =items.map(item=>{
                item.uniqueStr=item.sequenceNbr;
                item.code = item.bdCode;
                item.price = item.price?((Number(item.price).toFixed(2))+""):"0.00";
                item.remark=item.description;//备注
                return item;});
            count+=inconsistencies.length;
            errorGroups.push({code:groupKey.split("_")[0],childrenList: items});
        }
    }
    return {count,error:errorGroups};
}

/*
*
    QD_CODE_REPEAT:1,//检查项目清单编码重复"
    QD_UNIT_INCONSISTENT:2,//相同清单单位不一致
    QD_NAME_EMPTY:3,//清单名称为空
    QD_CODE_EMPTY:4,//清单项目编码为空
    QD_ATTR_EMPTY:5,//清单项目特征为空
    QD_UNIT_EMPTY:6,//清单单位为空
    QD_QUANTITY_EMPTY:7,//清单工程量为空或零
    QD_PRICE_ZERO:8,//清单综合单价为零
    DE_QUANTITY_ZERO:9,//定额工程量为零
    DE_PRICE_ZERO:10,//定额单价为零
    RCJ_PRICE_INCONSISTENT:11,//同一人材机有多个价格
    RCJ_QUANTITY_ZERO:12,//人材机消耗量为零
    RCJ_PRICE_ZERO:13,//人材机单价为零
    DAY_WORK_TITLE_SERIAL_NUM_EMPTY:14,//计日工标题序号为空
    DAY_WORK_TITLE_NAME_EMPTY:15,//计日工标题名称为空
    DAY_WORK_SERIAL_NUM_EMPTY:16,//计日工序号为空
    DAY_WORK_NAME_EMPTY:17,//计日工名称为空
    SERVICE_COST_TITLE_SERIAL_NUM_EMPTY:18,//总承包服务费标题序号为空
    SERVICE_COST_TITLE_NAME_EMPTY:19,//总承包服务费标题名称为空
    SERVICE_COST_SERIAL_NUM_EMPTY:20,//总承包服务费序号为空
    SERVICE_COST_NAME_EMPTY:21,//总承包服务费名称为空
    QD_QUANTITY_DECIMAL_POINT_ERROR:22,//清单工程量小数超过6位（河北省接口）
    OTHER_SUM_PRICE_CONSISTENCY:23,//其他项目汇总、明细金额一致性（河北省接口）
    DAY_WORK_TITLE_UNIQUENESS:24,//计日工明细标题（人工、材料、机械）唯一性
    QD_PRICE_INCONSISTENT:25,//检查清单综合单价是否一致
    WITHOUT_GROUP_PRICE_QD:26//未组价清单
    * */
//1. 招标项目、投标项目公共部分
const pubCheckList = () => {
    return {
        [CheckListKey.QD_CODE_REPEAT.checkType]: qd_code_repeat,
        [CheckListKey.QD_UNIT_INCONSISTENT.checkType]: qd_unit_inconsistent,
        [CheckListKey.QD_PRICE_INCONSISTENT.checkType]: qd_price_inconsistent,
        [CheckListKey.QD_NAME_EMPTY.checkType]: qd_name_empty,
        [CheckListKey.QD_CODE_EMPTY.checkType]: qd_code_empty,
        [CheckListKey.QD_ATTR_EMPTY.checkType]: qd_attr_empty,
        [CheckListKey.QD_UNIT_EMPTY.checkType]: qd_unit_empty,
        [CheckListKey.QD_QUANTITY_EMPTY.checkType]: qd_quantity_empty,
        [CheckListKey.QD_PRICE_ZERO.checkType]: qd_price_zero,
        [CheckListKey.DE_QUANTITY_ZERO.checkType]: de_quantity_zero,
        [CheckListKey.DE_PRICE_ZERO.checkType]: de_price_zero,
        [CheckListKey.RCJ_PRICE_INCONSISTENT.checkType]: rcj_price_inconsistent,
        [CheckListKey.RCJ_QUANTITY_ZERO.checkType]: rcj_quantity_zero,
        [CheckListKey.RCJ_PRICE_ZERO.checkType]: rcj_price_zero,
        [CheckListKey.DAY_WORK_TITLE_SERIAL_NUM_EMPTY.checkType]: day_work_title_serial_num_empty,
        [CheckListKey.DAY_WORK_TITLE_NAME_EMPTY.checkType]: day_work_title_name_empty,
        [CheckListKey.DAY_WORK_SERIAL_NUM_EMPTY.checkType]: day_work_serial_num_empty,
        [CheckListKey.DAY_WORK_NAME_EMPTY.checkType]: day_work_name_empty,
        [CheckListKey.SERVICE_COST_TITLE_SERIAL_NUM_EMPTY.checkType]: service_cost_title_serial_num_empty,
        [CheckListKey.SERVICE_COST_TITLE_NAME_EMPTY.checkType]: service_cost_title_name_empty,
        [CheckListKey.SERVICE_COST_SERIAL_NUM_EMPTY.checkType]: service_cost_serial_num_empty,
        [CheckListKey.SERVICE_COST_NAME_EMPTY.checkType]: service_cost_name_empty,
        [CheckListKey.QD_QUANTITY_DECIMAL_POINT_ERROR.checkType]: qd_quantity_decimal_point_error,
        [CheckListKey.OTHER_SUM_PRICE_CONSISTENCY.checkType]: other_sum_price_consistency,
        [CheckListKey.DAY_WORK_TITLE_UNIQUENESS.checkType]: day_work_title_uniqueness,
        [CheckListKey.WITHOUT_GROUP_PRICE_QD.checkType]: without_group_price_qd,
        [CheckListKey.LIMITED_PRICE_QD.checkType]: limited_price_qd,
        [CheckListKey.LIMITED_PRICE_FB.checkType]: limited_price_fb,
        [CheckListKey.ZGRCJ_ASSOCIATION.checkType]: zgrcj_association,

    };
}

// 单位工程项目 独有
const unitProjectPrivateCheckList = () => {
    let pub = pubCheckList();
    //TODO 如果有私有校验逻辑的情况 直接覆盖
    return {...pub};
}

// 招标 独有
const zbProjectPrivateCheckList = () => {
    let pub = pubCheckList();
    return {...pub};
}

// 投标 独有
const tbProjectPrivateCheckList = () => {
    let pub = pubCheckList();
    return {
        ...pub,
        [CheckListKey.WITHOUT_GROUP_PRICE_QD.checkType]: without_group_price_qd
    };
}


module.exports = {
    resultTitle,
    CheckListKey,
    ExtendCheckListKey,
    pubCheckList,
    zbProjectPrivateCheckList,
    tbProjectPrivateCheckList,
    unitProjectPrivateCheckList,
    qd_code_repeat_bc,
    qd_unit_inconsistent_bc,
    qd_price_inconsistent_bc,
    qd_unit_inconsistent
}
