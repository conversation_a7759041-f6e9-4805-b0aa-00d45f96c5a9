<!--
 * @Descripttion: 参考指标设置
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: wangru
 * @LastEditTime: 2025-04-29 09:40:22
-->
<template>
  <common-modal className="dialog-comm setAggreScope-dialog" @close="cancel()" v-model:modelValue="dialogVisible"
    title="参考指标设置" width="550px" height="600px" :mask="true" show-zoom :lock-view="false" destroy-on-close
    :loading="loading" :loading-config="{
      text: '正在加载中...',
    }">
    <div class="group-content-wrap">
      <div class="treeBox">
        <a-form ref="formRef" :model="formData" :label-col="{ span: 7 }" :wrapper-col="{ span: 15 }" autocomplete="off"
          @finish="handleOk">
          <a-form-item label="工程类型：" name="projectType">
            <a-select size="small" v-model:value="formData.projectType" :options="queryGCLXListList" :fieldNames="{
                label: 'name',
                value: 'name',
              }">
            </a-select>
          </a-form-item>
          <a-form-item label="建筑分类：" name="buildType">
            <a-select size="small" v-model:value="formData.buildType" :options="queryJZFLListList" :fieldNames="{
                label: 'name',
                value: 'name',
              }">
            </a-select>
          </a-form-item>
          <a-form-item label="造价类别：" name="costType">
            <a-select size="small" v-model:value="formData.costType" :options="queryZJLBListList" :fieldNames="{
                label: 'name',
                value: 'name',
              }">
            </a-select>
          </a-form-item>
          <a-form-item label="结构类型：" name="strType">
            <a-select size="small" v-model:value="formData.strType" :options="queryJGLXListList" :fieldNames="{
                label: 'name',
                value: 'name',
              }">
            </a-select>
          </a-form-item>
          <a-form-item label="编制时间：" name="madeTime">
            <a-select size="small" v-model:value="formData.madeTime" :options="bzTime" :fieldNames="{
                label: 'name',
                value: 'name',
              }">
            </a-select>
          </a-form-item>
          <a-form-item label="建设单位：" name="buildUnit">
            <a-input :maxlength="50" size="small" v-model:value.trim="formData.buildUnit" @input="
              () => {
                formData.buildUnit = removeSpecialChars(formData.buildUnit);
              }
            " />
          </a-form-item>
          <a-form-item label="计算口径（m²）：" name="indexCountCaliber">
            <a-input :maxlength="50" size="small" v-model:value.trim="formData.indexCountCaliber" @input="
              () => {
                formData.indexCountCaliber = removeSpecialChars(formData.indexCountCaliber);
              }
            " />
          </a-form-item>
          <a-form-item label="建筑高度（m）：" name="buildHeight">
            <a-input :maxlength="50" size="small" v-model:value.trim="formData.buildHeight" @input="
              () => {
                formData.buildHeight = removeSpecialChars(formData.buildHeight);
              }
            " />
          </a-form-item>
        </a-form>
      </div>
      <p class="footer-box">
        <a-button type="primary" ghost @click="cancel()">取消</a-button>
        <a-button type="primary" @click="handleOk()">确定</a-button>
      </p>

    </div>
  </common-modal>
</template>
<script setup>
import { ref, nextTick, onMounted, computed, reactive } from 'vue';
import jiesuanApi from '@/api/jiesuanApi';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail.js';
import { removeSpecialChars } from '@/utils/index';
import { message } from 'ant-design-vue';
import xeUtils from "xe-utils";
const route = useRoute();
const emits = defineEmits(['refresh']);
const props = defineProps(['unitIdList', 'isFirstOpen']);
let dialogVisible = ref(false);
const store = projectDetailStore();
let loading = ref(false);
const formRef = ref(null);
const treeData = ref([]);
const importCheckedKeys = ref([]);
const queryGCLXListList = ref([]);
const queryJZFLListList = ref([]);
const queryZJLBListList = ref([]);
const queryJGLXListList = ref([]);
const bzTime = ref([
  {name:'最近一个月'},
  {name:'最近三个月'},
  {name:'最近六个月'},
  {name:'不限'},
]);
// 表单
let formData = ref({
  projectType: '居住建筑',
  buildType: '住宅',
  costType: '结算价',
  strType: '框架剪力墙',
  madeTime: '最近六个月',
  buildUnit: null,
  indexCountCaliber: null,
  buildHeight: null,
});
const cancel = (refresh = false) => {
  dialogVisible.value = false;
};
const open = k => {
  dialogVisible.value = true;
  // getTreeList();
  queryGCLXListColl();
  queryJZFLListColl();
  queryZJLBListColl();
  queryJGLXListColl();
  getDataList()
};
const queryGCLXListColl = () => {
  jiesuanApi.queryGCLXListColl().then((res) => {
    queryGCLXListList.value=res.result
  })
}
const queryJZFLListColl = () => {
  jiesuanApi.queryJZFLListColl().then((res) => {
    queryJZFLListList.value=res.result
  })
}
const queryZJLBListColl = () => {
  jiesuanApi.queryZJLBListColl().then((res) => {
    queryZJLBListList.value=res.result
  })
}
const queryJGLXListColl = () => {
  jiesuanApi.queryJGLXListColl().then((res) => {
    queryJGLXListList.value=res.result
  })
}
const getDataList = () => {
  let apiData={
    constructId: store.currentTreeGroupInfo?.constructId,
  }
  console.info('参考指标设置获取数据参数',apiData)
  jiesuanApi.queryReferIndex(apiData).then((res) => {
    console.info('参考指标设置获取数据返回值',res)
    formData.value=res.result
  });
};
// 确认
const handleOk = () => {
  let apiData={
    constructId: store.currentTreeGroupInfo?.constructId,
    ...formData.value
  }
  console.info('参考指标设置传参',apiData)
  jiesuanApi.saveReferIndex(apiData).then((res) => {
    console.info('参考指标设置返回值',res)
    if(res.code!==200){
      return message.error(res.message)
    }
    message.success('设置成功！')
    cancel()
  });
};
defineExpose({
  open,
  cancel,
});
</script>

<style lang="scss">
.setAggreScope-dialog {
  //禁用浏览器默认选中
  -webkit-user-select: none;
  /* Safari */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;

  .group-content-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .treeBox {
    height: calc(100% - 50px);
    overflow-x: hidden;
    overflow-y: visible;
  }

  .footer-box {
    width: 100%;
    margin: 0;
    height: 35px;
    text-align: center;

    button {
      margin: 0 15px;
    }
  }
}
</style>
