<!--
 * @Descripttion: 编辑项目结构
 * @Author: renmingming
 * @Date: 2023-05-22 15:36:24
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-24 15:50:49
-->
<template>
  <common-modal
    className="dialog-comm"
    v-model:modelValue="props.visible"
    :title="dialogTitle"
    width="auto"
    @cancel="cancel"
    @close="cancel"
    :loadingModal="loadingModal"
  >
    <div class="edit-wrap">
      <div
        class="head"
        v-if="props.config.showMenu"
      >
        <a-button
          type="primary"
          :disabled="item.disabled"
          v-for="item of menus"
          :key="item.menuLevel"
          @click="menuClick(item)"
        >{{ item.name }}</a-button>
      </div>
      <div class="table-wrap">
        <vxe-table
          ref="vexTable"
          border="full"
          height="100%"
          align="center"
          :loading="loading"
          show-overflow="tooltip"
          :scroll-y="{ gt: 0 }"
          :column-config="{ resizable: true }"
          :row-config="{ isCurrent: true, keyField: 'id' }"
          :edit-config="{
            trigger: 'dblclick',
            mode: 'cell',
            showIcon: false,
            showStatus: false,
          }"
          :tree-config="{
            children: 'children',
            expandAll: true,
            transform: true,
          }"
          :data="treeList"
          :row-class-name="
            ({ row }) => {
              return `level-${row.levelType}`;
            }
          "
          @current-change="currentChangeEvent"
        >
          <vxe-column
            width="100"
            tree-node
          ></vxe-column>
          <vxe-column
            field="name"
            title="名称"
            :edit-render="{
              autofocus: '.my-input',
              autoselect: true,
            }"
          >
            <template #edit="{ row }">
              <a-input
                :disabled="row.originalFlag"
                :placeholder="getPlaceholder(row.levelType, row)"
                v-model:value="row.name"
                type="text"
                ref="editInputRef"
                class="my-input"
                @change="inputChange(row, $event)"
                @blur="changeRepeat(row, row.name)"
              ></a-input>
            </template>
          </vxe-column>
          <vxe-column
            field="constructMajorType"
            title="工程专业"
          >
            <template #default="{ row }">
              <!-- :open="true" -->
              <a-cascader
                v-if="row.levelType === 3 && engineerMajorList.length > 0"
                :disabled="!row.isNew && !props.config.isEdit"
                v-model:value="row.constructMajorType"
                placeholder="请选择工程专业"
                :options="engineerMajorList"
              />
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button
          @click="afterSave(false, 'later')"
          :loading="afterSubmitLoading"
          :disabled="submitLoading"
          v-if="!props.config.showMenu"
        >稍后设置</a-button>
        <a-button
          type="primary"
          :disabled="afterSubmitLoading"
          @click="handleOk"
          :loading="submitLoading"
        >确定</a-button>
      </div>

      <info-modal
        v-model:infoVisible="showInfoStatus"
        infoText="当前存在未设置专业的单位工程，是否确认提交？"
        descText="您可在后续工作台页面进行设置"
        :isSureModal="false"
        @updateCurrentInfo="afterSave()"
      ></info-modal>
    </div>
  </common-modal>
</template>

<script setup>
import { message } from 'ant-design-vue';
import { ref, reactive, watch, nextTick, toRaw, onMounted } from 'vue';
import xeUtils from 'xe-utils';
import { ConstructMenuOperator } from './ConstructMenuOperator';
import csProject from '@/api/csProject';
import jiesuanApi from '@/api/jiesuanApi';
import { getJsAsideTreeList } from '@/api/jiesuanApi';
import { inputName } from '@/utils/index';
import { useRoute } from 'vue-router';
import { proModelStore } from '@/store/proModel.js';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();

const ModelStore = proModelStore();

const showInfoStatus = ref(false);

const emit = defineEmits(['update:visible', 'success', 'editClose']);

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  config: {
    type: Object,
    default: {
      showMenu: true, //隐藏编辑按钮
      constructObj: null, //项目树
      isEdit: false, // 是否可以编辑工程专业
    },
  },
  majorList: {
    type: Array,
    default: () => [],
  },
});

let treeList = ref([]);
const route = useRoute();

const getTreeList = async () => {
  let list = null;
  if (props.config.constructObj) {
    // 直接传递树过来的
    list = props.config.constructObj;
  } else {
    const res = await getJsAsideTreeList(route.query.constructSequenceNbr);
    if (res.status === 200) {
      list = res.result;
    } else {
      message.error(res.message);
      return;
    }
  }
  console.log(list);
  treeList.value = list.map(i => {
    i.isNew = !i.constructMajorType;
    i.constructMajorType = i.constructMajorType
      ? i.secondInstallationProjectName
        ? [i.constructMajorType, i.secondInstallationProjectName]
        : [i.constructMajorType]
      : [];
    return i;
  });
  init();
};

const inputChange = (row, e) => {
  const value = inputName(e.target.value);
  let handleValue = value;
  if (xeUtils.trim(value).length > 50) {
    handleValue = value.slice(0, 50);
    message.warn('名称过长，请输入50个字符范围内');
  }
  row.name = handleValue;
};
let addItenNewName = ref();
const changeRepeat = (row, name) => {
  if (!name.trim()) return;
  if (isRepeat(row, name)) {
    row.name = addItenNewName.value;
  }
};
const isRepeat = (row, name) => {
  //判断统计添加的单位/单项名称是否重复出现
  let flag = treeList.value.some(
    item =>
      (item.levelType === row.levelType ||
        item.levelType === row.levelType - 0.5 ||
        item.levelType === row.levelType + 0.5) &&
      item.parentId === row.parentId &&
      item.id !== row.id &&
      item.name.trim() === name.trim()
  );
  if (flag) {
    let siblingsNodes = treeList.value.filter(
      item =>
        (item.levelType === row.levelType ||
          item.levelType === row.levelType - 0.5 ||
          item.levelType === row.levelType + 0.5) &&
        item.parentId === row.parentId &&
        item.id !== row.id
    );
    const test = handleName([...siblingsNodes], {
      type: 'add',
      checkName: name,
      name: name,
    });
    addItenNewName.value = test.name;
    console.log('test', test, addItenNewName.value);
  }
  return flag; //true--名称重复，flse---名称不重复
};
const handleName = (initList, current) => {
  //initList---同级列表数据   current--增加的名称数据
  function getNameCount(namesMap, name) {
    return (namesMap.get(name) || 0) + 1;
  }

  function setName(item) {
    let NAME = '';
    let count = 0;
    let nameList = [];
    for (let [k, v] of namesMap) {
      nameList.push(k.trim());
    }

    for (let [index, name] of nameList.entries()) {
      let lastName = index > 0 ? `${item.name}_${index + 1}` : `${item.name}`;
      let currentName = index > 0 ? `${item.name}_${index}` : `${item.name}`;
      if (
        !nameList.includes(lastName.trim()) &&
        nameList.includes(currentName.trim())
      ) {
        NAME = lastName.trim();
        namesMap.set(lastName.trim(), 0);
        break;
      } else if (
        nameList.includes(lastName.trim()) &&
        !nameList.includes(currentName.trim())
      ) {
        NAME = currentName.trim();
        namesMap.set(currentName.trim(), 0);
        break;
      }
    }

    if (namesMap.has(item.checkName)) {
      NAME = NAME || `${item.name}_${nameList.length}`;
    } else {
      NAME = item.name;
    }

    return NAME;
  }

  // 初始化一个映射存储每个名称出现的次数
  let namesMap = new Map();

  initList.forEach(item => {
    const count = getNameCount(namesMap, item.name);
    namesMap.set(item.name, count);
  });
  const currentList = [current];
  const renamedItems = currentList.reduce((acc, item, index) => {
    const count = getNameCount(namesMap, item.checkName);

    const newName =
      count > 1 ? `${item.checkName}_${count - 1}` : `${item.checkName}`;

    let newItem = { ...item, name: newName, num: count };
    if (namesMap.has(newName)) {
      const handleName = setName({ ...item });
      newItem = { ...item, name: handleName, num: count };
    }

    namesMap.set(item.checkName, count);
    acc.push(newItem);

    return acc;
  }, []);

  for (let i of renamedItems) {
    const count = getNameCount(namesMap, i.checkName);
    i.num = count;
  }
  return renamedItems[0];
};
const getPlaceholder = (levelType, row) => {
  let lastLevel = row.parent.levelType;
  const map = {
    4: '单项工程',
    5: '新建变更',
    12: '新建签证',
    13: '新建漏项',
    14: '新建索赔',
    11: '新建其他',
    16: '新建单位',
    //单项
    18: '新建变更',
    19: '新建签证',
    20: '新建漏项',
    21: '新建索赔',
    22: '新建其他',
    24: '子单项工程',
  };
  console.log('levelType, row', levelType, row);
  // debugger;
  // return `请输入${
  //   map[clickItem.value.menuLevel] ? map[clickItem.value.menuLevel] : ''
  // }名称`;
  return `请输入名称`;
};

let cMenuOper = new ConstructMenuOperator();
let menus = ref([]);
let loading = ref(false);
let submitLoading = ref(false);
let afterSubmitLoading = ref(false);
let dialogTitle = ref('编辑项目结构');

let vexTable = ref();

let insertRowIndex = ref(0);

const editInputRef = ref(null);

// 单选
const currentChangeEvent = async ({ row, $rowIndex }) => {
  if (!row?.parent || !Object.keys(row.parent).length) {
    let parent = vexTable.value?.getParentRow(row) || {};
    row.parent = parent;
  }
  insertRowIndex.value = $rowIndex;
  await cMenuOper.resetMenus(row, treeList.value);
  cMenuOper.menusJson();
  let list = cMenuOper.menus;
  getNewMenuList(row, list);
};
const getNewMenuList = (row, list) => {
  const filterList = [
    ConstructMenuOperator.importFile,
    ConstructMenuOperator.exportFile,
    ConstructMenuOperator.importUnit,
    ConstructMenuOperator.importVisa,
    ConstructMenuOperator.importOmission,
    ConstructMenuOperator.importClaimant,
    ConstructMenuOperator.importOther,
    ConstructMenuOperator.importUnity,
    ConstructMenuOperator.projectBelong,
  ];
  list.forEach(item => {
    item.disabled = item.isValid === 4 ? true : false;
    if (item.isValid === 1) {
      item.visible = true;
    } else if (item.isValid === 2) {
      item.visible = !row.originalFlag;
    } else if (item.isValid === 3) {
      item.visible = false;
    }
    if (item.isValid === 4) {
      item.disabled = true;
      item.visible = !row.originalFlag;
    }
  });
  let newList = list.filter(
    i => i.visible && !filterList.includes(i.menuLevel)
  );
  if (row.levelType === 1) {
    newList = [...newList[0].children];
  }
  menus.value = newList;
  console.log('menus.value', menus.value);
};

// 添加单项单位
let clickItem = ref(null);
const menuClick = i => {
  // console.log('menuLevel', menuLevel);
  clickItem.value = i;
  vexTable.value.clearEdit();
  cMenuOper.addLocation(
    vexTable,
    treeList.value,
    i.menuLevel,
    insertRowIndex.value
  );

  if ([9999].includes(i.menuLevel)) {
    // 删除操作
    const clickNode = vexTable.value?.getCurrentRecord();
    cMenuOper.removeData(clickNode).then(res => {
      if (res) {
        handleMenus();
      }
    });
    return;
  } else {
    handleMenus();
  }
};

//更新按钮状态
const handleMenus = () => {
  if (cMenuOper.newRecord) {
    // cMenuOper.resetMenus(cMenuOper.newRecord, treeList.value);
    // let list = cMenuOper.menusJson();
    // getNewMenuList(cMenuOper.newRecord, list);
    let row = cMenuOper.newRecord;
    let $rowIndex = treeList.value.findIndex(i => i.id === row.id);
    currentChangeEvent({ row, $rowIndex });
  }
};
/**
 *
 * @param {*} isComplete 单位是否完整
 */
const afterSave = (isComplete = false, fr = '') => {
  if (!isComplete && !checkData(true, false)) {
    return message.error('请补全名称后提交');
  }
  showInfoStatus.value = false;
  save('import', isComplete, fr);
};

// 点击其他过来的
const beforeSet = () => {
  if (!checkData(true, false)) {
    message.error('请补全名称后提交');
    return;
  }

  if (!checkData()) {
    showInfoStatus.value = true;
    return;
  }
  afterSave(true);
};

const handleOk = () => {
  if (!props.config.showMenu) {
    // 首页导入项目过来的
    beforeSet();
    return;
  }

  if (!checkData()) {
    return message.error('请补全信息后提交');
  }

  save();
};

/**
 * 保存数据
 * @param {*} type edit   编辑项目结构
 *                 import   导入过来的
 */
let loadingModal = ref(false);
const save = async (type = 'edit', isComplete = false, fr = '') => {
  if (submitLoading.value || afterSubmitLoading.value) return;
  const list = await handleList();
  const isUnitComplete = isComplete || (await checkData());
  const apiName = props.config.constructObj
    ? 'editImportProjectAfter'
    : 'postEditStructure';
  let postData = xeUtils.toArrayTree(list)[0];
  if (fr === 'later') {
    postData.laterSet = 1;
    afterSubmitLoading.value = true;
  } else {
    submitLoading.value = true;
  }
  console.log('🚀保存的数据:', postData, 'apiName', apiName);
  loadingModal.value = true;
  setTimeout(() => {
    csProject[apiName](postData)
      .then(res => {
        if (type === 'edit') {
          //编辑项目结构成功增加handleSingleProject接口
          console.log('123243124', { param: postData });
          jiesuanApi.handleSingleProject({ param: postData });
        }
        if (type === 'import') {
          // 设置是否是完整的结构
          ModelStore.setUnitStatus(isUnitComplete);
        }
        ModelStore.setUpdateProjectStructure(true);
        if (apiName == 'editImportProjectAfter') {
          if (res.result) {
            message.success('保存成功');
            emit('success');
          }
          cancel(res.result ? 'noBack' : 'back');
        } else {
          emit('success');
          cancel();
        }
      })
      .finally(() => {
        submitLoading.value = false;
        afterSubmitLoading.value = false;
        loadingModal.value = false;
        handleStore();
      });
  }, 0);
};

// 如果编辑弹窗还是自动打开，则关闭
const handleStore = () => {
  if (ModelStore.openEditModalStatus) {
    ModelStore.onEditModal(false);
  }
};

// 处理chenglist
const unliMenuList = [
  ConstructMenuOperator.unitLevel,
  ConstructMenuOperator.addVisa,
  ConstructMenuOperator.addClaimant,
  ConstructMenuOperator.addOmission,
  ConstructMenuOperator.addOther,
  ConstructMenuOperator.importUnity,
];
const handleList = () => {
  return toRaw(treeList.value).map(
    ({ id, parentId, name, levelType, constructMajorType, children, type }) => {
      let unitAfterSet = false;
      if (
        unliMenuList.includes(levelType) &&
        (!constructMajorType || !constructMajorType[0])
      ) {
        unitAfterSet = true;
      }
      if (levelType === ConstructMenuOperator.singleChildLevel) {
        levelType = 2;
      }
      return {
        id,
        parentId,
        name,
        levelType,
        constructMajorType: constructMajorType ? constructMajorType[0] : null,
        secondInstallationProjectName: constructMajorType
          ? constructMajorType[1]
          : null,
        children,
        unitAfterSet,
        type,
      };
    }
  );
};

/**
 *
 * @param {*} list 平铺的数组树
 */
const handlePost = list => {
  let initData = {};
  list.map(i => {
    delete i.parent;
    delete i._X_ROW_CHILD;
    i.children = null;

    if (!initData[i.id]) {
      initData[i.id] = toRaw(i);
    }
  });
  return Object.values(initData);
};

/**
 *
 * @param {*} checkName 是否检查名字
 * @param {*} checkType 是否检查专业
 */
const checkData = (checkName = true, checkType = true) => {
  for (let item of treeList.value) {
    if (!xeUtils.trim(item.name).length && checkName) {
      return false;
    }
    if (
      cMenuOper.unitLevelList.includes(item.levelType) &&
      !item.originalFlag &&
      (!item.constructMajorType || !item.constructMajorType[0]) &&
      checkType
    ) {
      return false;
    }
  }
  return true;
};
// 是否返回上一页
const cancel = (type = 'back') => {
  // 处理全局打开编辑弹窗状态
  handleStore();
  emit('editClose', type);
  emit('update:visible');
};
const init = () => {
  nextTick(() => {
    setTimeout(() => {
      vexTable.value.setCurrentRow(treeList.value[0]);
      currentChangeEvent({ row: treeList.value[0] });
    }, 10);
  });
};
onMounted(() => {
  queryEngineerMajorList();
});
let engineerMajorList = ref([]);
//获取专业列表下拉列表
const queryEngineerMajorList = () => {
  engineerMajorList.value = [];
  csProject
    .getEngineerMajorList({ deStandard: store.deType })
    .then(async response => {
      if (response.status === 200) {
        response.result.map(async (item, index) => {
          let obj = {
            label: item.unitProjectName,
            value: item.unitProjectName,
          };
          obj.children = await getSecondProNme(item.unitProjectName);
          engineerMajorList.value.push(obj);
        });
      }
    });
};
const getSecondProNme = async firstName => {
  let childrenList = [];
  const list = await csProject.getSecondInstallationProjectName({
    constructMajorType: firstName,
    deStandard: store.deType,
  });

  if (list.status === 200) {
    list.result.map(i => {
      childrenList.push({
        label: i.cslbName,
        value: i.cslbName,
      });
    });
  }
  return childrenList;
};
watch(
  () => [props.visible, props.majorList],
  ([val, oldVal], [listVal, listOldVal]) => {
    treeList.value = [];
    if (val) {
      getTreeList();
    }

    // 多个业务调用，没必要外部传递列表
    queryEngineerMajorList();
    // if (!listVal.length) {

    // } else {
    // 	engineerMajorList.value = props.majorList;
    // }
  }
);
</script>
<style lang="scss" scoped>
.edit-wrap {
  width: 60vw;
  max-width: 800px;
  height: 60vh;
  display: flex;
  flex-direction: column;
}

.head {
  .ant-btn {
    margin-right: 10px;
  }
}
.table-wrap {
  flex: 1;
  margin: 15px 0 30px 0;
  overflow: hidden;
}
.table-wrap :deep(.vxe-table) {
  .level-1 {
    background-color: #d7d7d7;
  }
  .level-2 {
    background-color: #ececec;
  }
  .level-3 {
    background-color: #e9eefa;
  }
  .ant-select-selector {
    border: none !important;
    background-color: transparent !important;
  }
  .vxe-body--column,
  .vxe-header--column,
  .vxe-footer--column {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
  .row--current {
    background-color: #a6c3fa;
    .ant-select,
    .ant-select-selection-placeholder,
    .ant-select-arrow {
      color: var(--primary-color);
    }
  }
}

:deep(.ant-select-clear) {
  border-radius: 50%;
}
</style>
