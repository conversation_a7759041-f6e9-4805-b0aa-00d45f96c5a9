class JieSuanIndexFenXiEnum {

    static METHOD1 = {code:1,desc:"按工程分析"};
    static METHOD2 = {code:2,desc:"按专业分析"};
    static METHOD3 = {code:3,desc:"按费用分析"};


    // 静态方法：根据code获取枚举对象
   static getEnumByCode(code) {
        for (let key in JieSuanIndexFenXiEnum) {
            if (JieSuanIndexFenXiEnum.hasOwnProperty(key) && typeof JieSuanIndexFenXiEnum[key] === 'object' && JieSuanIndexFenXiEnum[key].code === code) {
                return JieSuanIndexFenXiEnum[key];
            }
        }
        return null; // 如果找不到匹配的code，返回null
    }
}
module.exports = JieSuanIndexFenXiEnum
