<!--
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2024-04-22 09:34:55
 * @LastEditors: sunchen
 * @LastEditTime: 2025-04-11 10:33:01
-->
<template>
  <vxe-table
    align="center"
    v-if="handlerColumns.length > 0"
    :column-config="{ resizable: true }"
    :row-config="{ isHover: true, isCurrent: true, keyField: 'sequenceNbr' }"
    :data="tableData"
    :edit-config="{
      trigger: 'click',
      mode: 'cell',
      beforeEditMethod: cellBeforeEditMethod,
    }"
    @cell-click="useCellClickEvent"
    ref="unitTable"
    keep-source
    height="100%"
    :tree-config="{
      transform: true,
      expandAll: true,
      rowField: 'sequenceNbr',
      parentField: 'parentId',
      reserve: true,
    }"
    :cell-class-name="selectedClassName"
    :menu-config="menuConfig"
    @menu-click="contextMenuClickEvent"
    @keydown="handleKeyDownEvent"
    @current-change="currentChange"
    class="table-edit-common table-content"
  >
    <vxe-column
      field="dispNo"
      :width="columnWidth(120)"
      title="序号"
      :visible="handlerColumns.find(a => a.field === 'dispNo').visible"
      tree-node
      :edit-render="{ autofocus: '.vxe-input--inner' }"
    >
      <template #edit="{ row }">
        <vxe-input
          v-if="row.levelType !== 1"
          :clearable="false"
          placeholder="请输入"
          type="text"
          v-model="row.editSort"
          @blur="inputDispNoFinish(row, $event, 'dispNo')"
        ></vxe-input>
        <span v-else>
          {{ row.customIndex ? row.customIndex : row.dispNo }}
        </span>
      </template>
      <template #default="{ row }">
        {{ row.customIndex ? row.customIndex : row.dispNo }}
      </template>
    </vxe-column>

    <vxe-column
      field="name"
      title="名称"
      headerAlign="center"
      :min-width="columnWidth(100)"
      :visible="handlerColumns.find(a => a.field === 'name').visible"
      :edit-render="{ autofocus: '.vxe-input--inner' }"
    >
      <template #edit="{ row }">
        <vxe-input
          :clearable="false"
          v-if="row.levelType !== 1"
          placeholder="请输入"
          v-model="row.name"
          type="text"
          @blur="inputFinish(row, $event, 'name')"
        ></vxe-input>
        <span v-else>
          {{ row.name }}
        </span>
      </template>
      <template #default="{ row }">
        {{ row.name }}
      </template>
    </vxe-column>
    <vxe-column
      field="unit"
      title="单位"
      headerAlign="center"
      :width="columnWidth(70)"
      :visible="handlerColumns.find(a => a.field === 'unit').visible"
      :edit-render="{ enabled: true, autofocus: '.vxe-input--inner' }"
    >
      <template #edit="{ row }">
        <vxe-select
          v-if="row.levelType !== 1 && row.editConfig"
          v-model="row.unit"
          @change="inputFinish(row, $event, 'unit')"
          :transfer="true"
        >
          <vxe-option
            v-for="item of store.unitListString.split(',')"
            :key="item"
            :value="item"
            :label="item"
          ></vxe-option>
        </vxe-select>
        <span v-else>
          {{ row.unit }}
        </span>
      </template>
    </vxe-column>
    <vxe-column
      field="quantity"
      title="数量"
      headerAlign="center"
      :width="columnWidth(70)"
      :visible="handlerColumns.find(a => a.field === 'quantity').visible"
      :edit-render="{ autofocus: '.vxe-input--inner' }"
    >
      <template #default="{ row }">
        {{ decimalFormat(row.quantity, 'UNIT_DLF_QUANTITY_PATH') }}
      </template>
      <template #edit="{ row }">
        <vxe-input
          :clearable="false"
          v-if="row.levelType !== 1 && row.editConfig"
          placeholder="请输入"
          v-model="row.quantity"
          type="number"
          @blur="inputFinish(row, $event, 'quantity')"
        ></vxe-input>
        <span v-else>
          {{ decimalFormat(row.quantity, 'UNIT_DLF_QUANTITY_PATH') }}
        </span>
      </template>
    </vxe-column>
    <vxe-column
      field="price"
      title="单价"
      headerAlign="center"
      :width="columnWidth(70)"
      :visible="handlerColumns.find(a => a.field === 'price').visible"
      :edit-render="{ autofocus: '.vxe-input--inner' }"
    >
      <template #default="{ row }">
        {{ decimalFormat(row.price, 'UNIT_DLF_PRICE_PATH') }}
      </template>
      <template #edit="{ row }">
        <vxe-input
          :clearable="false"
          v-if="row.levelType !== 1 && row.editConfig"
          placeholder="请输入"
          v-model="row.price"
          type="number"
          @blur="inputFinish(row, $event, 'price')"
        ></vxe-input>
        <span v-else>
          {{ decimalFormat(row.price, 'UNIT_DLF_PRICE_PATH') }}
        </span>
      </template>
    </vxe-column>
    <vxe-column
      field="totalPrice"
      :visible="handlerColumns.find(a => a.field === 'totalPrice').visible"
      title="合价"
      :width="columnWidth(70)"
      headerAlign="center"
    >
      <template #default="{ row }">
        {{ decimalFormat(row.totalPrice, 'UNIT_DLF_TOTALPRICE_PATH') }}
      </template>
    </vxe-column>
    <vxe-column
      field="costMajorName"
      title="取费专业"
      :width="columnWidth(110)"
      headerAlign="center"
      :visible="handlerColumns.find(a => a.field === 'costMajorName').visible"
      :edit-render="{ autofocus: '.vxe-input--inner' }"
    >
      <template #edit="{ row }">
        <vxe-select
          v-if="row.levelType === 2"
          v-model="row.costMajorName"
          @change="inputFinish(row, $event, 'costMajorName')"
          :transfer="true"
        >
          <vxe-option
            v-for="item in costMajorData"
            :key="item.qfCode"
            :value="item.qfName"
            :label="item.qfName"
          ></vxe-option>
        </vxe-select>
        <span v-else>
          {{ row.costMajorName }}
        </span>
      </template>
    </vxe-column>
    <vxe-column
      field="remark"
      title="备注"
      :min-width="columnWidth(100)"
      :visible="handlerColumns.find(a => a.field === 'remark').visible"
      headerAlign="center"
      :edit-render="{ autofocus: '.vxe-input--inner' }"
    >
      <template #edit="{ row }">
        <vxe-input
          v-if="row.levelType !== 1"
          :clearable="false"
          placeholder="请输入"
          v-model="row.remark"
          type="text"
          @blur="inputFinish(row, $event, 'remark')"
        ></vxe-input>
        <span v-else>
          {{ row.remark }}
        </span>
      </template>
    </vxe-column>
    <vxe-column
      field="isCalculateAwf"
      :width="columnWidth(130)"
      title="记取安全文明施工费"
      :visible="handlerColumns.find(a => a.field === 'isCalculateAwf').visible"
    >
      <template #default="{ row }">
        <vxe-checkbox
          @change="inputFinish(row, $event, 'isCalculateAwf')"
          v-if="row.levelType == 2"
          v-model="row.isCalculateAwf"
        ></vxe-checkbox
      ></template>
    </vxe-column>
  </vxe-table>
  <material-machine-index
    v-model:indexVisible="indexVisible"
    :currentMaterialInfo="currentMaterialInfo"
    :isDlf="true"
    :indexLoading="indexLoading"
    @currentRcjInfo="currentRcjInfo"
  ></material-machine-index>
</template>

<script setup>
import xeUtils from 'xe-utils';
import { onMounted, ref, watch, nextTick, getCurrentInstance } from 'vue';
import MaterialMachineIndex from '../materialMachineIndex/index.vue';
import feePro from '@gongLiaoJi/api/feePro';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@gongLiaoJi/hooks/useCellClick';
import { message, Modal } from 'ant-design-vue';
import { useFormatTableColumns } from '@gongLiaoJi/hooks/useGljFormatTableColumns.js';
import tableColumns from './tableColumns';
import { useDecimalPoint } from '@gongLiaoJi/hooks/useDecimalPoint.js';
import { recordProjectData } from '@gongLiaoJi/hooks/recordProjectOperat.js';
import {columnWidth} from "@gongLiaoJi/hooks/useSystemConfig.js";
const { decimalFormat } = useDecimalPoint();
let { updateGljSelrowId } = recordProjectData();

const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const store = projectDetailStore();
let editIndex = ref(); //设置只有建筑面积内容可编辑-----------单位工程
let tableData = ref([]);
let costMajorData = ref([]);
const unitTable = ref(null);
const indexVisible = ref(false);
let currentMaterialInfo = ref(null);
let indexLoading = ref(false); // 索引页面loading
const {
  showColumns,
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
} = useFormatTableColumns({
  type: 6,
  initColumnsCallback: () => {
    initColumns({
      columns: tableColumns,
    });
  },
});
// 激活表格
const focusTable = () => {
  unitTable.value.focus();
};
bus.on('independentFee', data => {
  focusTable();
});
const getTableData = row => {
  let apiData = {
    levelType: store.currentTreeInfo.type,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeInfo?.parentId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  console.log('获取单位独立费', apiData);
  if (!apiData.levelType) {
    //levelType不存在不掉接口，这个是偶现，一次性调好多次
    return;
  }
  feePro.getIndependentFeeData(apiData).then(res => {
    console.log('单位独立费获取返回数据', res);
    if (res.status === 200) {
      tableData.value = res.result.list.map(i => {
        i.editSort = i.customIndex || i.dispNo;
        return i;
      });

      let currentData = tableData.value[0];
      let gljCheckTab = store.gljCheckTab;
      let upSelRow = gljCheckTab[
        store.currentTreeInfo?.sequenceNbr
      ].tabList.find(a => a.tabName == '独立费');
      if (upSelRow && upSelRow.selRowId !== '') {
        let obj = tableData.value.find(a => a.sequenceNbr == upSelRow.selRowId);
        if (!obj) {
          obj = tableData.value[0];
        }
        currentData = obj;
      } else if (row) {
        currentData =
          tableData.value.find(item => item.sequenceNbr == row[0]) ||
          currentData;
      }
      //需要确定这里的建筑面积是否可以修改
      setTimeout(() => {
        unitTable.value?.setCurrentRow(currentData);
        unitTable.value?.scrollToRow(currentData);
        focusTable();
      }, 100);
    } else {
      message.error('获取数据失败！' + res.message);
    }
  });

  feePro.queryBaseFeeData().then(res => {
    if (res.status === 200 && res.result) {
      costMajorData.value = res.result;
    }
  });
};
watch(
  () => store.currentTreeInfo,
  () => {
    if (store.tabSelectName === '独立费' && store.currentTreeInfo.type === 3) {
      getTableData();
      initColumns({
        columns: tableColumns,
      });
    }
  }
);
watch(
  () => store.tabSelectName,
  () => {
    if (store.tabSelectName === '独立费' && store.currentTreeInfo.type === 3) {
      getTableData();
      initColumns({
        columns: tableColumns,
      });
    }
  }
);
onMounted(() => {
  if (store.tabSelectName === '独立费' && store.currentTreeInfo.type === 3) {
    getTableData();
    initColumns({
      columns: tableColumns,
    });
  }
});

const menuConfig = ref({
  body: {
    options: [
      [
        { code: 'insertRow', name: '新增行', disabled: false },
        { code: 'insertchildRow', name: '新增下级', disabled: false },
        { code: 'deleteRow', name: '删除行', disabled: false },
        { code: 'searchRcjObj', name: '查询人材机数据', disabled: false },
      ],
    ],
  },
  visibleMethod({ row, type, options }) {
    const $table = unitTable.value;
    if ($table) {
      if (type === 'body') {
        options.forEach(list => {
          list.forEach(item => {
            item.disabled = true;
            if (row && row.levelType === 1 && item.code === 'insertchildRow') {
              item.disabled = false;
            }
            if (row && row.levelType === 2) {
              item.disabled = false;
            }
            if (
              row &&
              row.levelType === 3 &&
              (item.code === 'insertRow' || item.code === 'deleteRow')
            ) {
              item.disabled = false;
            }

            if (
              row &&
              row.children.length === 0 &&
              item.code === 'searchRcjObj' &&
              row.levelType !== 1
            ) {
              item.disabled = false;
            }
            if (
              row &&
              row.children.length !== 0 &&
              item.code === 'searchRcjObj' &&
              row.levelType == 1
            ) {
              item.disabled = true;
            }
          });
        });
      }
    }
    return true;
  },
});

const contextMenuClickEvent = ({ menu, row, column }) => {
  const $table = unitTable.value;
  if ($table) {
    let data = ['name', 'quantity', 'price', 'remark'];
    let newRow = xeUtils.clone(row, true);
    switch (menu.code) {
      case 'insertRow':
        data.map(a => {
          newRow[a] = '';
        });
        insertHandle(newRow, 'insert');
        break;
      case 'insertchildRow':
        data.map(a => {
          newRow[a] = '';
        });
        insertHandle(newRow, 'insertChild');
        break;
      case 'deleteRow':
        deleteHandle(row);
        break;
      case 'searchRcjObj':
        indexVisible.value = true;
        currentMaterialInfo.value = row;
        break;
    }
  }
};
const handleKeyDownEvent = event => {
  let sTable = event.$table;
  let code = event.$event.code;
  let row = sTable.getCurrentRecord();
  let isEditing = sTable.isEditByRow(row);
  // 删除
  if (code == 'Delete' && !isEditing) {
    if (row && row.levelType !== 1) {
      deleteHandle(row);
    }
  }
};
async function deleteHandle(row, operateType) {
  const selectRecord = row || basicInfoTable.value?.getCurrentRecord();
  const status = await modalTip('确定要删除选中行？');
  focusTable();
  if (!status) {
    return;
  }

  deleteIndependent(selectRecord.sequenceNbr, operateType);
}
const inputDispNoFinish = (row, e, attr) => {
  if (!row || row.levelType === 1) {
    return;
  }
  row[attr] = e.value;
  console.log('row-----' + e);
  saveIndependentFees({ data: xeUtils.clone(row, true) }, false, 'edit');
};
const inputFinish = (row, e, attr) => {
  if (!row || row.levelType === 1) {
    return;
  }
  if (attr === 'costMajorName') {
    let data = costMajorData.value.find(
      item => item['qfName'] === row['costMajorName']
    );
    row['costMajorCode'] = costMajorData.value.find(
      item => item['qfName'] === row['costMajorName']
    )?.qfCode;
    console.log('row-----' + JSON.stringify(e.currentValue));
  }
  saveIndependentFees({ data: xeUtils.clone(row, true) }, false, 'edit');
};
const deleteIndependent = (sequenceNbr, operateType) => {
  let levelType = store.currentTreeInfo?.type;
  let apiData = {
    levelType: levelType,
    // code: activeKey.value,
    type: 'sbgzf00',
    constructId: store.currentTreeGroupInfo?.constructId,
    // singleId: store.currentTreeGroupInfo?.singleId,
    unitId: levelType === 3 ? store.currentTreeInfo?.id : null,
    sequenceNbr: sequenceNbr,
    operateType: operateType,
  };

  feePro.deleteIndependentFee(apiData).then(res => {
    if (res.status === 200) {
      getTableData();
    }
  });
};

async function insertHandle(posRow, type) {
  const selectRecord = posRow || basicInfoTable.value?.getCurrentRecord();
  if (!selectRecord) {
    await modalTip('请选中要插入的位置');
    return;
  }
  saveIndependentFees({ data: xeUtils.clone(selectRecord, true) }, false, type);
}

function modalTip(content) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      title: '',
      content: content,
      onOk: () => {
        resolve(true);
      },
      onCancel() {
        resolve(false);
      },
    });
  });
}

const saveIndependentFees = (param, isUpdate = false, operateType) => {
  let levelType = store.currentTreeInfo?.type;
  // let mapData = ['name', 'quantity', 'price', 'remark'];
  // mapData.forEach(item => {
  //   if (param.data[item]) {
  //     param.data[item] = '';
  //   }
  // })
  let apiData = {
    levelType: levelType,
    type: store.asideMenuCurrentInfo?.key,
    constructId: store.currentTreeGroupInfo?.constructId,
    unitId: levelType === 3 ? store.currentTreeInfo?.id : null,
    iCosts: param.data,
    operateType: operateType,
  };
  console.log('-----------', apiData);
  feePro.saveIndependentFee(apiData).then(res => {
    if (res.status === 200 && !isUpdate) {
      getTableData();
    }
  });
};

const currentRcjInfo = (row, type) => {
  let levelType = store.currentTreeInfo?.type;
  let apiData = {
    levelType: levelType,
    type: store.asideMenuCurrentInfo?.key,
    constructId: store.currentTreeGroupInfo?.constructId,
    unitId: levelType === 3 ? store.currentTreeInfo?.id : null,
    iCosts: JSON.parse(JSON.stringify(currentMaterialInfo.value)),
    operateType: type == 1 ? 'insert' : 'edit',
    rcj: JSON.parse(JSON.stringify(row)),
  };
  feePro.insertRcj(apiData).then(res => {
    if (res.status !== 200) {
      return message.error(res, msg);
    }
    indexVisible.value = false;
    getTableData();
    message.success('操作成功！');
  });
};

let currentInfo = ref(null);

const currentChange = ({ row }) => {
  currentInfo.value = row;
};

watch(
  () => currentInfo.value,
  newVal => {
    if (newVal) {
      updateGljSelrowId(newVal.sequenceNbr, '独立费', 'selRowId');
    }
  },
  {
    deep: true,
    immediate: true,
  }
);

defineExpose({
  handlerColumns,
  updateColumns,
  getDefaultColumns,
});
</script>
<style lang="scss" scoped>
.table-content {
  //max-width: 925px;
  overflow-x: scroll;
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
}
</style>
