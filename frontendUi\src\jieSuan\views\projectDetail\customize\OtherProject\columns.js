import { ref,computed } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import {columnWidth} from "@/hooks/useSystemConfig.js";
// 固定列配置 其它项目
const fixedColumns = {
  dispNo: {
    field: 'dispNo',
    // width: 60,
    width: columnWidth(60),
    title: '序号',
    classType: 1,
    initialize: true,
  },
  extraName: {
    field: 'extraName',
    //width: 180,
    width: columnWidth(180),
    title: '名称',
    classType: 1,
    initialize: true,
  },
  extraNameEdit: {
    field: 'extraName',
    //width: 180,
    width: columnWidth(180),
    title: '名称',
    classType: 1,
    initialize: true,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'extraName_edit' },
  },
  unit:{
    field: 'unit',
    // width: 100,
    width: columnWidth(100),
    title: '单位',
    classType: 1,
    initialize: true,
    // originalFlag: false,
  },
  calculationBase: {
    field: 'calculationBase',
    // width: 150,
    minWidth: columnWidth(150),
    title: '计算基数',
    classType: 1,
    initialize: true,
  },
  calculationBaseEdit: {
    field: 'calculationBase',
    // width: 150,
    minWidth: columnWidth(150),
    title: '计算基数',
    classType: 1,
    initialize: true,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'calculationBase_edit' },
  },
  jieSuanTotal:{
    field: 'jieSuanTotal',
    // width: 100,
    width: columnWidth(100),
    title: '合同金额',
    classType: 1,
    initialize: true,
    // originalFlag: false,
  },
  jieSuanAqwmsgf:{
    field: 'jieSuanAqwmsgf',
    // width: 100,
    width: columnWidth(155),
    title: '合同安全生产、文明施工费',
    classType: 1,
    initialize: true,
    // originalFlag: false,
  },
  aqwmsgf:{
    field: 'aqwmsgf',
    // width: 100,
    width: columnWidth(155),
    title: '结算安全生产、文明施工费',
    classType: 1,
    initialize: true,
    // originalFlag: false,
  },
  jieSuanJxTotal:{
    field: 'jieSuanJxTotal',
    // width: 180,
    minWidth: columnWidth(180),
    title: '合同进项合计',
    classType: 1,
    initialize: true,
    // originalFlag: false,
  },
  jieSuanCsTotal:{
    field: 'jieSuanCsTotal',
    // width: 180,
    minWidth: columnWidth(180),
    title: '合同除税合计',
    classType: 1,
    initialize: true,
    // originalFlag: false,
  },
  rate:{
    field: 'rate',
    title: '费率(%)',
    // minWidth: 80,
    minWidth: columnWidth(80),
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'rate_edit' },
    classType: 1,
    initialize: true,
  },
  rateNotEdit:{
    field: 'rate',
    title: '费率(%)',
    // minWidth: 80,
    minWidth: columnWidth(100),
    classType: 1,
    initialize: true,
  },
  type:{
    field: 'type',
    // width: 100,
    minWidth: columnWidth(100),
    title: '费用类别',
    classType: 1,
    initialize: true,
    // originalFlag: false,
  },
  type_edit:{
    field: 'type',
    // width: 100,
    minWidth: columnWidth(100),
    title: '费用类别',
    classType: 1,
    initialize: true,
    slots: { edit: 'editType' },
    editRender: { autofocus: '.vxe-input--inner' },
    // originalFlag: false,
  },
  isMarkSafe:{
    // field: 'isMarkSafe',
    field: 'markSafa',
    // width: 100,
    width: columnWidth(100),
    title: '计取安文费',
    fixed: 'right',
    slots: { default: 'isMarkSafe' },
    classType: 1,
    initialize: true,
  },
  // 'markSj':{
  //   field: 'markSj',
  //   width: 100,
  //   title: '计取税金',
  //   fixed: 'right',
  //   slots: { default: 'markSj' },
  //   classType: 1,
  //   initialize: true,
  // },
  putOntotalFlag:{
    field: 'putOntotalFlag',
    // width: 100,
    width: columnWidth(100),
    title: '计入合价',
    fixed: 'right',
    slots: { default: 'putOntotalFlag' },
    classType: 1,
    initialize: true,
  },
  isMarkSj:{
    // field: 'isMarkSj',
    field: 'markSj',
    // width: 100,
    width: columnWidth(100),
    title: '计取税金',
    fixed: 'right',
    slots: { default: 'isMarkSj' },
    classType: 1,
    initialize: true,
  },


  taxRemoval:{
    field: 'taxRemoval',
    // width: 100,
    width: columnWidth(100),
    title: '费率(%)',
    // fixed: 'right',
    // slots: { default: 'putOntotalFlag' },
    classType: 1,
    initialize: true,
  },




// {
//   field: 'amount',
//   width: 180,
//   title: '结算数量',
//   editRender: { autofocus: '.vxe-input--inner' },
//   slots: { edit: 'jiesuanTotal_edit' },
//   classType: 1,
//   initialize: true,
// },
  total:{
    field: 'total',
    // width: 180,
    // width: columnWidth(180),
    width: columnWidth(180),
    title: '结算金额',
    classType: 1,
    initialize: true,
  },
  jxTotal:{
    field: 'jxTotal',
    // width: 180,
    minWidth: columnWidth(180),
    title: '结算进项税额',
    classType: 1,
    initialize: true,
  },
  csTotal:{
    field: 'csTotal',
    // width: 180,
    minWidth: columnWidth(180),
    title: '结算除税合计',
    classType: 1,
    initialize: true,
  },


  instructions:{
    field: 'instructions',
    // width: 100,
    minWidth: columnWidth(100),
    title: '基数说明',
    classType: 1,
    initialize: true,
  },
  description:{
    field: 'description',
    // width: 100,
    minWidth: columnWidth(180),
    title: '备注',
    slots: { edit: 'description_edit' },
    editRender: { autofocus: '.vxe-input--inner' },
    classType: 1,
    initialize: true,
  },
};
// 固定列配置 暂列金额
const zljeColumns = {
  dispNo:{
    field: 'dispNo',
    title: '序号',
    minWidth: 60,
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'dispNo_edit' },
  },
  name:{
    field: 'name',
    title: '项目名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'name_edit' },
  },
  unit:{
    field: 'unit',
    title: '单位',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'unit_edit' },
  },
  jieSuanAmount:{
    field: 'jieSuanAmount',
    title: '合同数量',
    minWidth: 100,
  },
  jieSuanPrice:{
    field: 'jieSuanPrice',
    title: '合同单价',
    minWidth: 100,
  },
  jieSuanProvisionalSum:{
    field: 'jieSuanProvisionalSum',
    title: '合同暂定金额',
    minWidth: 130,
  },
  amount:{
    field: 'amount',
    minWidth: 100,
    title: '结算数量',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanTotal_edit' },
  },
  price:{
    field: 'price',
    minWidth: 100,
    title: '结算单价',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanPrice_edit' },
  },
  provisionalSum:{
    field: 'provisionalSum',
    minWidth: 100,
    title: '结算暂定金额',
  },
  description:{
    field: 'description',
    title: '备注',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'dec_edit' },
  },
};
// 固定列配置 专业工程暂估价
const zygczgjColumns = {
  dispNo:{
    field: 'dispNo',
    title: '序号',
    minWidth: 60,
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'dispNo_edit' },
  },
  name:{
    field: 'name',
    title: '工程名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'name_edit' },
  },
  content:{
    field: 'content',
    title: '工程内容',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'content_edit' },
  },
  unit:{
    field: 'unit',
    title: '单位',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'unit_edit' },
  },
  jieSuanTotal:{
    field: 'jieSuanTotal',
    title: '合同数量',
    minWidth: 80,
  },
  jieSuanPrice:{
    field: 'jieSuanPrice',
    title: '合同单价',
    minWidth: 100,
  },
  jieSuanAmount:{
    field: 'jieSuanAmount',
    minWidth: 100,
    title: '合同金额',
  },
  // jieSuanTaxRemoval:{
  //   field: 'jieSuanTaxRemoval',
  //   title: '合同除税系数(%)',
  //   minWidth: 180,
  // },

  // jieSuanJxTotal:{
  //   field: 'jieSuanJxTotal',
  //   minWidth: 100,
  //   title: '合同进项合计',
  // },
  // jieSuanCsPrice:{
  //   field: 'jieSuanCsPrice',
  //   minWidth: 100,
  //   title: '合同除税单价',
  // },
  // jieSuanCsTotal:{
  //   field: 'jieSuanCsTotal',
  //   minWidth: 100,
  //   title: '合同除税合价',
  // },
  amount:{
    field: 'amount',
    minWidth: 100,
    title: '结算数量',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanTotal_edit' },
  },
  price:{
    field: 'price',
    minWidth: 100,
    title: '结算单价',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanPrice_edit' },
  },
  total:{
    field: 'total',
    minWidth: 100,
    title: '结算金额',
    // editRender: { autofocus: '.vxe-input--inner' },
    // type: 'text',
    // slots: { edit: 'jiesuanAmount_edit' },
  },
  // taxRemoval:{
  //   field: 'taxRemoval',
  //   minWidth: 130,
  //   title: '结算除税系数(%)',
  //   editRender: { autofocus: '.vxe-input--inner' },
  //   type: 'text',
  //   slots: { edit: 'jiesuanTaxRemoval_edit' },
  // },
  // jxTotal:{
  //   field: 'jxTotal',
  //   minWidth: 130,
  //   title: '结算进项合计',
  // },
  // csPrice:{
  //   field: 'csPrice',
  //   minWidth: 130,
  //   title: '结算除税单价',
  // },
  // csTotal:{
  //   field: 'csTotal',
  //   minWidth: 130,
  //   title: '结算除税合计',
  // },
  description:{
    field: 'description',
    title: '备注',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'dec_edit' },
  }
}
// 固定列配置 总承包服务费
const zcbfwfColumns = {
  dispNo:{
    field: 'dispNo',
    title: '序号',
    minWidth: 60,
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'dispNo_edit' },
  },
  fxName:{
    field: 'fxName',
    title: '项目名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'fxName_edit' },
  },
// {
//   field: 'amount',
//   title: '数量',
//   minWidth: 80,
//   editRender: { autofocus: '.vxe-input--inner' },
//   slots: { edit: 'amount_edit' },
// },
  xmje:{
    field: 'xmje',
    title: '项目价值',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'xmje_edit' },
  },
  serviceContent:{
    field: 'serviceContent',
    title: '服务内容',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'service_edit' },
  },
  rate:{
    field: 'rate',
    title: '费率(%)',
    minWidth: 80,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'rate_edit' },
  },
  fwje:{
    //field: 'fwje',
    field: 'jieSuanFwje',
    minWidth: 100,
    title: '合同金额',
  },
  jiesuanModeName:{
    field: 'jiesuanModeName',
    minWidth: 100,
    title: '结算方式',
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { default: 'jiesuanMode_default', edit: 'jiesuanMode_edit' },
  },
  jieSuanFwje:{
    //field: 'jieSuanFwje',
    field: 'fwje',
    minWidth: 100,
    title: '结算金额',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jieSuanFwje_edit',default: 'jieSuanFwje_default', },
  },
  description:{
    field: 'description',
    title: '备注',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'dec_edit' },
  }
}
// 固定列配置 计日工
const jrgColumns = {
  dispNo:{
    field: 'dispNo',
    title: '序号',
    minWidth: 60,
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'dispNo_edit' },
  },
  worksName: {
    field: 'worksName',
    title: '名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'worksName_edit' },
  },
  specification: {
    field: 'specification',
    title: '规格型号',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'spec_edit' },
  },
  unit: {
    field: 'unit',
    title: '单位',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'unit_edit' },
  },
  // tentativeQuantity:{
  //   field: 'tentativeQuantity',
  //   minWidth: 100,
  //   title: '结算数量',
  //   editRender: { autofocus: '.vxe-input--inner' },
  //   type: 'text',
  //   slots: { edit: 'jiesuanTotal_edit' },
  // },
  jieSuanTentativeQuantity:{
    field: 'jieSuanTentativeQuantity',// jieSuanTentativeQuantity  暂定数量  结算叫 合同数量
    minWidth: 80,
    title: '合同数量',
  },
  // tentativeQuantity:{
  //   field: 'tentativeQuantity',//jieSuanTentativeQuantity
  //   minWidth: 80,
  //   title: '暂定数量',
  //   editRender: { autofocus: '.vxe-input--inner' },
  //   slots: { edit: 'tentativeQuantity_edit' },
  // },
  jieSuanPrice:{
    field: 'jieSuanPrice',
    title: '合同综合单价',
    minWidth: 130,
  },
  jieSuanTotal:{
    field: 'jieSuanTotal',
    minWidth: 80,
    title: '合同合价',
  },
  jieSuanTaxRemoval:{
    field: 'jieSuanTaxRemoval',
    title: '合同除税系数(%)',
    minWidth: 180,
  },
  jieSuanJxTotal:{
    field: 'jieSuanJxTotal',
    minWidth: 100,
    title: '合同进项合计',
  },
  // jieSuanCsPrice:{
  //   field: 'jieSuanCsPrice',
  //   minWidth: 100,
  //   title: '合同除税单价',
  // },
  jieSuanCsTotal:{
    field: 'jieSuanCsTotal',
    minWidth: 100,
    title: '合同除税合价',
  },
  // quantitativeExpression:{
  //   field: 'quantitativeExpression',
  //   title: '结算数量表达式',
  //   minWidth: 180,
  //   type: 'text',
  //   editRender: { autofocus: '.vxe-input--inner' },
  //   slots: { edit: 'jiesuanQuant_edit' },
  // },
  jieSuanAmount:{
    //field: 'jieSuanAmount',
    field: 'tentativeQuantity',//tentativeQuantity  结算数量
    minWidth: 130,
    title: '结算数量',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'tentativeQuantity_edit' },
  },
  price:{
    field: 'price',
    minWidth: 130,
    title: '结算综合单价',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanPrice_edit' },
  },
  total:{
    field: 'total',
    minWidth: 100,
    title: '结算合价',
    // editRender: { autofocus: '.vxe-input--inner' },
    // type: 'text',
    // slots: { edit: 'jiesuanAmount_edit' },
  },
  taxRemoval:{
    field: 'taxRemoval',
    minWidth: 130,
    title: '结算除税系数(%)',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanTaxRemoval_edit' },
  },
  jxTotal:{
    field: 'jxTotal',
    minWidth: 130,
    title: '结算进项合计',
  },
  // csPrice:{
  //    field: 'csPrice',
  //    minWidth: 130,
  //   title: '结算除税单价',
  // },
  csTotal: {
    field: 'csTotal',
    minWidth: 130,
    title: '结算除税合价',
  },
  description:{
    field: 'description',
    title: '备注',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'dec_edit' },
  }
}
/**
 * 12/22定额:{{store.constructConfigInfo.deStandardReleaseYear}}
 * 1. 一般计税/ 0. 简易计税 : {{store.constructConfigInfo.taxMode}}
 * 合同内 / undefined 合同外: {{store.currentTreeInfo.originalFlag}}
 */
// 配置策略 其它项目
function qtxmStatistics() {
  let p1 = [fixedColumns.dispNo, fixedColumns.extraName, fixedColumns.unit, fixedColumns.calculationBase, fixedColumns.jieSuanTotal,fixedColumns.jieSuanAqwmsgf,fixedColumns.aqwmsgf,
    fixedColumns.jieSuanJxTotal,fixedColumns.jieSuanCsTotal,fixedColumns.rateNotEdit,fixedColumns.type,fixedColumns.isMarkSafe,fixedColumns.putOntotalFlag,fixedColumns.isMarkSj,
    fixedColumns.total,fixedColumns.jxTotal,fixedColumns.csTotal,fixedColumns.instructions,fixedColumns.description];
  let p2 = [fixedColumns.dispNo, fixedColumns.extraNameEdit, fixedColumns.calculationBaseEdit,fixedColumns.aqwmsgf,fixedColumns.rate, fixedColumns.type_edit,fixedColumns.isMarkSafe,fixedColumns.putOntotalFlag,fixedColumns.isMarkSj,
    fixedColumns.total,fixedColumns.instructions,fixedColumns.description];
  let p3 = [fixedColumns.dispNo, fixedColumns.extraName, fixedColumns.unit, fixedColumns.calculationBase, fixedColumns.jieSuanTotal,fixedColumns.jieSuanAqwmsgf,fixedColumns.aqwmsgf,fixedColumns.rateNotEdit,
    fixedColumns.type,fixedColumns.isMarkSafe,fixedColumns.putOntotalFlag,fixedColumns.isMarkSj,fixedColumns.total,fixedColumns.instructions,fixedColumns.description];
  return {
    '12': {
      '1': { // 一般计税
        original: p1,
        nonOriginal: [fixedColumns.dispNo, fixedColumns.extraNameEdit, fixedColumns.calculationBaseEdit,fixedColumns.aqwmsgf,fixedColumns.rate, fixedColumns.type_edit,fixedColumns.isMarkSafe,fixedColumns.putOntotalFlag,fixedColumns.isMarkSj,
                      fixedColumns.total,fixedColumns.jxTotal,fixedColumns.csTotal,fixedColumns.instructions,fixedColumns.description],
      },
      '0': { // 简易计税
        original: p3,
        nonOriginal: p2,
      }
    },
    '22': {
      '1': { // 一般计税
        original:  p3,
        nonOriginal: p2
      },
      '0': { // 简易计税
        original:  p3,
        nonOriginal: p2
      }
    }
  };
}

// 配置策略 暂列金额
function zlje(){
  const p1 = [zljeColumns.dispNo, zljeColumns.name, zljeColumns.unit, zljeColumns.jieSuanAmount, zljeColumns.jieSuanPrice,
    zljeColumns.jieSuanProvisionalSum, zljeColumns.amount, zljeColumns.price, zljeColumns.provisionalSum, zljeColumns.description];
  const p2 = [zljeColumns.dispNo, zljeColumns.name, zljeColumns.unit,zljeColumns.amount, zljeColumns.price, zljeColumns.provisionalSum, zljeColumns.description];
  return {
    '12': {
      '1': { // 一般计税
        original: p1,
        nonOriginal: p2,
      },
      '0': { // 简易计税
        original: p1,
        nonOriginal: p2
      }
    },
    '22': {
      '1': { // 一般计税
        original: p1,
        nonOriginal: p2
      },
      '0': { // 简易计税
        original: p1,
        nonOriginal: p2
      }
    }
  };
}

// 配置策略 专业工程暂估价
function zygczgj(){
  const p1 = [zygczgjColumns.dispNo, zygczgjColumns.name ,zygczgjColumns.content,zygczgjColumns.unit, zygczgjColumns.jieSuanTotal,
              zygczgjColumns.jieSuanPrice,zygczgjColumns.jieSuanAmount, zygczgjColumns.amount,zygczgjColumns.price,zygczgjColumns.total,zygczgjColumns.description];
  const p2 = [zygczgjColumns.dispNo, zygczgjColumns.name ,zygczgjColumns.content,zygczgjColumns.unit,  zygczgjColumns.amount,
              zygczgjColumns.price,zygczgjColumns.total];
  return {
    '12': {
      '1': { // 一般计税
        original: p1,
        nonOriginal: p2,
      },
      '0': { // 简易计税
        original: p1,
        nonOriginal: p2
      }
    },
    '22': {
      '1': { // 一般计税
        original: p1,
        nonOriginal: p2
      },
      '0': { // 简易计税
        original: p1,
        nonOriginal: p2
      }
    }
  };
}

// 配置策略 总承包服务费
function zcbfwf(){
  const p1 = [zcbfwfColumns.dispNo, zcbfwfColumns.fxName ,zcbfwfColumns.xmje,zcbfwfColumns.serviceContent,zcbfwfColumns.rate, zcbfwfColumns.fwje,
              zcbfwfColumns.jiesuanModeName,zcbfwfColumns.jieSuanFwje,zcbfwfColumns.description];
  // const p2 = [zcbfwfColumns.dispNo, zcbfwfColumns.fxName ,zcbfwfColumns.xmje,zcbfwfColumns.serviceContent,zcbfwfColumns.rate, zcbfwfColumns.jiesuanModeName,zcbfwfColumns.jieSuanFwje,
  //             zcbfwfColumns.description];
  const p2 = [zcbfwfColumns.dispNo, zcbfwfColumns.fxName ,zcbfwfColumns.xmje,zcbfwfColumns.serviceContent,zcbfwfColumns.rate, zcbfwfColumns.jieSuanFwje,
    zcbfwfColumns.description];
  return {
    '12': {
      '1': { // 一般计税
        original: p1,
        nonOriginal: p2,
      },
      '0': { // 简易计税
        original: p1,
        nonOriginal: p2
      }
    },
    '22': {
      '1': { // 一般计税
        original: p1,
        nonOriginal: p2
      },
      '0': { // 简易计税
        original: p1,
        nonOriginal: p2
      }
    }
  };
}

// 配置策略 计日工
function jrg(){
  const p1 = [jrgColumns.dispNo,jrgColumns.worksName,jrgColumns.specification,jrgColumns.unit,jrgColumns.jieSuanTentativeQuantity,jrgColumns.jieSuanPrice,
              jrgColumns.jieSuanTotal,jrgColumns.jieSuanTaxRemoval,jrgColumns.jieSuanJxTotal,jrgColumns.jieSuanCsTotal,jrgColumns.jieSuanAmount,jrgColumns.price,
              jrgColumns.total,jrgColumns.taxRemoval,jrgColumns.jxTotal,jrgColumns.csTotal,jrgColumns.description];
  const p2 = [jrgColumns.dispNo,jrgColumns.worksName,jrgColumns.specification,jrgColumns.unit,jrgColumns.jieSuanTentativeQuantity,jrgColumns.jieSuanPrice,jrgColumns.jieSuanTotal,
              jrgColumns.jieSuanAmount,jrgColumns.price,jrgColumns.total,jrgColumns.description];
  const p3 = [jrgColumns.dispNo,jrgColumns.worksName,jrgColumns.specification,jrgColumns.unit,jrgColumns.jieSuanAmount,jrgColumns.price,jrgColumns.total,jrgColumns.description];
  return {
    '12': {
      '1': { // 一般计税
        original: p1,
        nonOriginal: [jrgColumns.dispNo,jrgColumns.worksName,jrgColumns.specification,jrgColumns.unit,jrgColumns.jieSuanAmount,jrgColumns.price,
          jrgColumns.total,jrgColumns.taxRemoval,jrgColumns.jxTotal,jrgColumns.csTotal,jrgColumns.description],
      },
      '0': { // 简易计税
        original: p2,
        nonOriginal: p3
      }
    },
    '22': {
      '1': { // 一般计税
        original: p2,
        nonOriginal: p3
      },
      '0': { // 简易计税
        original: p2,
        nonOriginal: p3
      }
    }
  };
}


// 每个界面配置的策略函数的名字
const typeFn = { qtxmStatistics, zlje, zygczgj , zcbfwf, jrg };

// 创建响应式列配置
export const createListHooks = (type) => {
  console.log(type,'strategy');
  const store = projectDetailStore();
  const createList = () => {
    // const year = String(store.constructConfigInfo.deStandardReleaseYear);
    const year = String(store.deStandardReleaseYear);
    const taxMode = String(store.constructConfigInfo.taxMode);
    const isOriginal = store.currentTreeInfo.originalFlag;

    const handleFn = typeFn[type]();// 根据每个界面的传入的标识，生成列表
    const strategy = handleFn[year]?.[taxMode];
    if (!strategy) return [];
    return isOriginal ? strategy.original : strategy.nonOriginal;
  };
  return {
    createList,
  }
}

