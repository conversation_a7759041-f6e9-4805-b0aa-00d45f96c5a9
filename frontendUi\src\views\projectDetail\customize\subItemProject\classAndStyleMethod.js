/*
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-07-03 14:46:02
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2025-04-18 14:58:04
 */
// import { projectDetailStore } from '@/store/projectDetail';
// const projectStore = projectDetailStore();

export const customCell = ({ rowIndex, column , record:row }) => {
  let className = ''
  let style = {}
  if (['bdCode','fxCode'].includes(column.field)) {
    style = {
      // textIndent: row.customLevel * 12 + 'px'
      textAlign: 'left'
    }
  }

  if (['bdCode','fxCode'].includes(column.field)) {
    className += ' code-color ' + `Virtual-pdLeft-s${row.customLevel} `;
  } else if (column.field === 'index') {
    className += ' index-bg';
  }

  // 批注提示
  if (column.field == 'name' && row?.annotations) {
    className += ' note-tips';
  }

  if (['bdCode','fxCode'].includes(column.field) && row.kind !== '04') {
    const line = row.maxLine || 1;
    className += ` cell-line-break-${line}`;
  }

  // 添加默认两行类名
  if (
    [
      'qfCode',
      'measureType',
      'description',
      'costMajorName',
      'projectAttr',
    ].includes(column.field) ||
    (['bdCode','fxCode'].includes(column.field) && row.kind === '04')
  ) {
    const line = row.maxLine || 2;
    className += ` cell-line-break-${line}`;
  }

  if (['projectAttr'].includes(column.field)) {
    className += ` projectAttr-item `;
  }
  return { style: style,class: className};
};
export const rowClassName = (row, index, data,originalFlag) => {
  let ClassStr = 'normal-info';
  if (row.kind === '0') {
    ClassStr = 'row-unit';
  } else if (row.kind === '01' || row.kind === '02') {
    ClassStr = 'row-sub';
  } else if (row.kind === '03') {
    ClassStr = 'row-qd';
  }
  if (row.color) {
    ClassStr += ' ' + row.color;
  }
  if (row.tempDeleteFlag) {
    ClassStr = 'temp-delete';
  }
  if ([94, 95].includes(row.kind)) {
    ClassStr += ' zcsb-color';
  }

  if (row.sequenceNbr == data[0]?.sequenceNbr) {
    ClassStr += ' first-row';
  }
  // if (
  //   row.sequenceNbr ==
  //   data[data.length - 1]?.sequenceNbr
  // ) {
  //   ClassStr += ' last-row';
  // }
  ClassStr += originalFlag && !row.originalFlag
  ? ' original-data-bg'
  : '';
  return ClassStr;
};
export const customHeaderCell = column => {
  let className = { class: '' }
  if(column.editable) className.class+='fix-not-can-edit'
  if(['bdCode','fxCode'].includes(column.field)) className.class+='deCode-center'
  return className
};
