<!--
 * @Descripttion: 报表
 * @Author: sunchen
 * @Date:2023-08-25 11:36:44
 * @LastEditors: sunchen
 * @LastEditTime: 2025-04-23 11:02:39
-->
<template>
  <section>
    <split
      :horizontal="false"
      ratio=" 1/8"
      :minHorizontalTop="225"
      :maxHorizontalTop="400"
      :isDrop="biddingType !== 2 ? true : false"
      :onlyPart="biddingType === 2 ? 'Bootom' : 'all'"
      style="height: 100%"
      mode="vertical"
    >
      <template #one>
        <aside v-if="treeListCache && treeListCache[0].biddingType !== 2">
          <aside-tree
            v-if="treeData"
            :isExpand="true"
            :treeData="treeData"
            :treeListCache="treeListCache"
            @getTreeList="getTreeList"
            @upOrDown="upOrDown"
          />
        </aside>
      </template>
      <template #two>
        <main>
          <main-content />
        </main>
      </template>
    </split>
    <footer><PricePanel :priceList="priceList" /></footer>
  </section>
</template>

<script setup>
import AsideTree from '@/views/projectDetail/customize/AsideTree.vue';
import MainContent from './mainContent.vue';
import PricePanel from '@/views/projectDetail/customize/PricePanel.vue';
import { projectDetailStore } from '@/store/projectDetail';
import csProject from '@/api/csProject';
import { getJsAsideTreeList } from '@/api/jiesuanApi';
import xeUtils from 'xe-utils';
import { useRoute } from 'vue-router';
import { proModelStore } from '@/store/proModel.js';
import { ref, reactive, watchEffect, onMounted, onBeforeUnmount } from 'vue';
import { constructLevelTreeStructureList } from '@/api/csProject';
import split from '@/components/split/index.vue';

import { message } from 'ant-design-vue';
const projectStore = projectDetailStore();
const ModelStore = proModelStore();
let biddingType = ref();

let treeData = ref();
let treeListCache = ref();
const route = useRoute();
console.log('🚀 ~ route.query?.type:', route.query?.type);
projectStore.SET_TYPE(route.query?.type || 'ys');
const showInfoStatus = ref(false);
let priceList = ref([]); //底部数据列表

watchEffect(() => {
  showInfoStatus.value = ModelStore.onInfoModal;
});
//获取底部数据
const getFooterValues = () => {
  let apiData = {
    unitId:
      projectStore.currentTreeInfo?.levelType === 3
        ? projectStore.currentTreeInfo?.id
        : '',
    constructId: route.query.constructSequenceNbr,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  csProject.getBottomSummary( apiData ).then(res => {
    if (res && res.status === 200 && res.result) {
      priceList.value = [
        {
          name: '工程造价（含设备费及税金）',
          value: res.result.zzj,
        },
        {
          name: '人工费',
          value: res.result.rgf,
        },
        {
          name: '机械费',
          value: res.result.jxf,
        },
        {
          name: '主材费',
          value: res.result.zcf,
        },
        {
          name: '材料费',
          value: res.result.clf,
        },
        {
          name: '管理费',
          value: res.result.glf,
        },
        {
          name: '利润',
          value: res.result.lr,
        },
        {
          name: '直接工程费',
          value: res.result.zjgcf,
        },
        {
          name: '设备费及税金',
          value: res.result.sbfsj,
        },
      ];
    } else {
      priceList.value = [];
    }
  });
};
onMounted(() => {
  getTreeList();
  getConstructConfigByConstructId();
  getFooterValues();
  window.addEventListener('keydown', save);
});
onBeforeUnmount(() => {
  window.removeEventListener('keydown', save);
});
const save = event => {
  if (event.ctrlKey && event.code == 'KeyS') {
    csProject.saveShfFile(route.query.constructSequenceNbr).then(res => {
      message.success('保存成功');
    });
  }
};
let moveList = reactive([]); //单项上下移动，被替换目标
let dataList = reactive([]);
let tarList = reactive([]);
const getIsFlag = type => {
  let flag = true;
  let sameLevel = dataList.filter(
    item => item.parentId === projectStore.currentTreeInfo.parentId
  );
  if (
    (sameLevel[0].id === projectStore.currentTreeInfo.id && type === 'up') ||
    (sameLevel[sameLevel.length - 1].id === projectStore.currentTreeInfo?.id &&
      type === 'down')
  ) {
    flag = false;
  }
  return flag;
};
const upOrDown = type => {
  //点击上下移动操作
  //同级列表
  if (!getIsFlag(type)) return;
  let index = dataList.findIndex(
    item => item.id === projectStore.currentTreeInfo.id
  );
  if (projectStore.currentTreeInfo.levelType === 3) {
    //单位之间直接上下移动
    let other;
    if (type === 'up') {
      other = dataList[index - 1];
      dataList[index - 1] = dataList[index];
      dataList[index] = other;
    } else {
      other = dataList[index + 1];
      dataList[index + 1] = dataList[index];
      dataList[index] = other;
    }
  } else if (projectStore.currentTreeInfo.levelType === 2) {
    //单项移动需要带着子级一起
    moveList = [];
    tarList = [];
    getSelfList(projectStore.currentTreeInfo);
    let tarInfo =
      type === 'up' ? dataList[index - 1] : dataList[index + tarList.length]; //上下移动的位置目标
    getMoveList(tarInfo, dataList, type);
    let nextIndex = dataList.findIndex(item => item.id === moveList[0].id);
    if (type === 'up') {
      dataList.splice(index, tarList.length);
      dataList.splice(nextIndex, 0, ...tarList);
    } else {
      dataList.splice(nextIndex, moveList.length);
      dataList.splice(index, 0, ...moveList);
    }
  }
  treeData.value = [...dataList];
};
const getSelfList = tar => {
  tarList.push(tar);
  tar.children.map(i => {
    getSelfList(i);
  });
};
const getMoveList = (tar, list, type) => {
  if (type === 'up') {
    if (tar.parentId === projectStore.currentTreeInfo.parentId) {
      //同级移动切没有子级
      moveList.unshift(tar);
    } else {
      list.map(item => {
        if (item.id === tar.id || item.parentId === tar.parentId) {
          moveList.push(item);
        }
      });
      let nextTar = list.find(i => i.id === tar.parentId);
      getMoveList(nextTar, list);
    }
  } else {
    moveList.push(tar);
    list.map(i => {
      if (i.parentId === tar.id) {
        getMoveList(i, list, type);
      }
    });
  }
};
const getTreeList = () => {
  let apiName = getJsAsideTreeList;
  apiName(route.query.constructSequenceNbr).then(res => {
    console.log('res-getTreeList', res, '设置定额类型', projectStore.deType);
    if (res.status === 200) {
      if (res.result) treeData.value = res.result;
      treeListCache.value = xeUtils.clone(res.result, true);
      let proCheckTab;
      if (projectStore.type === 'ys') {
        projectStore.SET_GLOBAL_SETTING_INFO({
          mainRcjShowFlag: res.result[0].mainRcjShowFlag,
          standardConversionShowFlag: res.result[0].standardConversionShowFlag,
        });
      }
      //最初projectStore.proCheckTab没有数据时重置
      if (res.result[0].biddingType === 2) {
        proCheckTab =
          treeListCache.value &&
          treeListCache.value.filter(
            item => Number(item.levelType) !== 2 && item.biddingType !== 2
          );
        projectStore.SET_CURRENT_TREE_INFO(treeListCache.value[1]);
        projectStore.SET_PRO_BIDDINGTYPE(2);
        projectStore.SET_PROJECT_NAME(treeListCache.value[1]?.name);
        projectStore.SET_CURRENT_TREE_GROUP_INFO({
          constructId: res.result[0].id,
          name: res.result[0].name,
        });
      } else {
        proCheckTab =
          treeListCache.value &&
          treeListCache.value.filter(item => Number(item.levelType) !== 2);
        res.result.forEach(item => {
          if (
            item.id === projectStore.currentTreeInfo?.id &&
            projectStore.currentTreeInfo.id
          ) {
            projectStore.SET_CURRENT_TREE_INFO(item);
          }
        });
        // if (!projectStore.isRefreshProjectTree && !nofresh) {
        //   projectStore.SET_CURRENT_TREE_INFO(treeListCache.value[0]);
        // }
        projectStore.SET_PRO_BIDDINGTYPE(1);
        projectStore.SET_PROJECT_NAME(treeListCache.value[0]?.name);
      }
      projectStore.SET_IS_REFRESH_PROJECT_TREE(false);
      let type; //打开项目类型
      switch (res.result[0].biddingType) {
        case 0:
          type = 'ZB';
          break;
        case 1:
          type = 'TB';
          break;
        case 2:
          type = 'DW';
          break;
      }
      projectStore.projectType = type;
      biddingType.value = res.result[0].biddingType;

      console.log('projectStore', projectStore.$state);
      dataList = xeUtils.clone(treeData.value, true);
    }
  });
};

const getConstructConfigByConstructId = () => {
  csProject
    .getConstructConfigByConstructId(route.query.constructSequenceNbr)
    .then(res => {
      console.log('res', res);
      if (res.status === 200) {
        console.log('55555555555', res);
        projectStore.SET_CONSTRUCT_CONFIG_INFO(res.result);
      }
    });
};
</script>
<style lang="scss" scoped>
section {
  display: flex;
  flex-wrap: wrap;
  height: calc(100vh - 89px);
}
footer {
  width: 100%;
  height: 33px;
  background-color: #d9e1ef;
}
aside {
  // width: 225px;
  width: 100%;
  height: calc(100% - 33px);
  border-right: 2px solid #dcdfe6;
  background: #f8fbff;
  text-align: left;
}
main {
  flex: 1;
  // width: calc(100% - 225px);
}
</style>
