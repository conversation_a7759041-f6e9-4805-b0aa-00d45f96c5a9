// const {ResponseData} = require("../../../electron/utils/ResponseData");
// const {Controller} = require("../../../core");
// const JieSuanOtherProjectProvisionalService = require("../service/jieSuanOtherProjectProvisionalService");
// class JieSuanOtherProjectProvisionalController extends Controller{
//
//
//     /**
//      * 构造函数
//      * @param ctx
//      */
//     constructor(ctx) {
//         super(ctx);
//         this.jieSuanOtherProjectProvisionalService = new JieSuanOtherProjectProvisionalService(ctx);
//     }
//
//
//     /**
//      * 暂列金 操作
//      * @param arg 清单册code
//      * @returns {Promise<*>}
//      */
//     async otherProjectProvisional(arg) {
//
//          await this.jieSuanOtherProjectProvisionalService.otherProjectProvisional(arg);
//
//         if (arg.operateType !==1){
//
//             await this.service.management.sycnTrigger("unitDeChange");
//             await this.service.management.trigger("itemChange");
//         }
//         return ResponseData.success(null);
//     }
//
// }
//
// JieSuanOtherProjectProvisionalController.toString = () => '[class JieSuanOtherProjectProvisionalController]';
// module.exports = JieSuanOtherProjectProvisionalController;