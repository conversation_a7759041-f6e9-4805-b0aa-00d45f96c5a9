<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:00:41
 * @LastEditors: wangru
 * @LastEditTime: 2025-06-15 15:07:29
-->
<template>
  <div class="feeTable">
    <div class="leftContent">
      <div class="decTable">
        <p class="title">
          <span class="textTitle">
            <img
              src="../../../../assets/img/detailImg/feeshuoming.png"
              alt="费率说明"
            />
            <span>费率说明</span>
          </span>
        </p>
        <vxe-table
          align="center"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          :data="decData"
          ref="leftTable"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            beforeEditMethod: cellBeforeEditMethod,
          }"
          class="table-edit-common table-no-outer-border"
          @cell-click="
            cellData => {
              useCellClickEvent(cellData, null, ['contextT']);
            }
          "
          :scroll-y="{ enabled: false }"
          :cell-class-name="selectedClassName"
        >
          <vxe-column
            field="sortNo"
            :width="columnWidth(50)"
            title="序号"
          > </vxe-column>
          <vxe-column
            field="name"
            title="名称"
            :width="columnWidth(150)"
          > </vxe-column>
          <vxe-column
            field="contextT"
            :width="columnWidth(170)"
            title="内容"
            :edit-render="{ enabled: !store.updateSS }"
          >
            <template #edit="{ row }">
              <vxe-select
                v-model="row.contextT"
                :transfer="true"
                @change="selectChange(row, row.contextT)"
              >
                <vxe-option
                  v-for="item of row.optionList"
                  :key="item.code"
                  :value="item.value"
                  :label="item.value"
                ></vxe-option>
              </vxe-select>
            </template>
          </vxe-column>
          <template #empty>
            <span style="
                color: #898989;
                font-size: 14px;
                display: block;
                margin: 25px 0;
              ">
              <icon-font
                style="font-size: 22px"
                type="icon-zanwushuju"
              ></icon-font>
              暂无数据
            </span>
          </template>
        </vxe-table>
      </div>
      <div class="decTable">
        <p class="title">
          <span class="textTitle">
            <icon-font
              class="icon"
              type="icon-jishuifangshi"
              style="
                font-size: 18px;
                vertical-align: text-bottom;
                margin: 0 3px;
              "
            />
            <span>计税方式</span>
          </span>
        </p>
        <vxe-table
          align="center"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          :data="rateData"
          ref="leftRateTable"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            beforeEditMethod({ rowIndex }) {
              if (rowIndex === 0) {
                //第一行不可编辑
                return false;
              }
              return true;
            },
          }"
          class="table-edit-common table-no-outer-border"
          @cell-click="
            cellData => {
              useCellClickEvent(cellData);
            }
          "
          @edit-closed="changeRateEdit"
          :row-style="rowStyle"
          :cell-class-name="selectedClassName"
          :scroll-y="{ enabled: false }"
          keep-source
        >
          <vxe-column
            field="sortNo"
            :width="columnWidth(50)"
            title="序号"
          > </vxe-column>
          <vxe-column
            field="rowName"
            title="名称"
            :width="columnWidth(150)"
          > </vxe-column>
          <vxe-column
            field="contextT"
            :width="columnWidth(170)"
            title="内容"
            :edit-render="{ enabled: !store.updateSS }"
          >
            <template #edit="{ row, column }">
              <span v-if="!row.isEdit">{{ row.contextT }}</span>
              <vxe-select
                v-if="row.isEdit && row.contentype === 'select'"
                v-model="row.contextT"
                :transfer="true"
              >
                <!-- @blur="changeRateSelect(row.key, row)" -->
                <vxe-option
                  v-for="item of row.selectList"
                  :key="item.code"
                  :value="item.value"
                  :label="item.value"
                ></vxe-option>
              </vxe-select>
              <vxe-input
                v-if="row.isEdit && row.contentype === 'edit'"
                :clearable="false"
                v-model="row.contextT"
                :maxlength="10"
                @blur="
                  (row.contextT = pureNumber(row.contextT, 2)), clearRate()
                "
              ></vxe-input>
            </template>
          </vxe-column>
          <template #empty>
            <span style="
                color: #898989;
                font-size: 14px;
                display: block;
                margin: 25px 0;
              ">
              <icon-font
                style="font-size: 22px"
                type="icon-zanwushuju"
              ></icon-font>
              暂无数据
            </span>
          </template>
        </vxe-table>
      </div>
      <div class="decTable">
        <p class="title">
          <span class="textTitle">
            <icon-font
              class="icon"
              type="icon-jishuifangshi"
              style="
                font-size: 18px;
                vertical-align: text-bottom;
                margin: 0 3px;
              "
            />
            <a-tooltip
              placement="rightTop"
              color="white"
            >
              <template #title>
                <div
                  style="color: #000000; font-size: 12px"
                  class="tooltip"
                >
                  <p> 费用标准：</p>
                  <p>
                    企业管理费：直接费中的人工费+机械费
                  </p>
                  <span>
                    利润：直接费中的人工费+机械费
                  </span>
                </div>
              </template>
              费用计算基数设置<icon-font
                type="icon-bangzhu"
                style="margin:0 5px;"
              ></icon-font>
            </a-tooltip>
          </span>
        </p>
        <vxe-table
          align="center"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          :data="calData"
          ref="leftCalTable"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
          }"
          class="table-edit-common table-no-outer-border"
          @cell-click="
            cellData => {
              useCellClickEvent(cellData);
            }
          "
          @edit-closed="changeCalEdit"
          :cell-class-name="cellClassNameCum"
          :scroll-y="{ enabled: false }"
          keep-source
        >
          <vxe-column
            type="seq"
            :width="columnWidth(50)"
            title="序号"
          > </vxe-column>
          <vxe-column
            field="rowName"
            title="名称"
            :width="columnWidth(150)"
          > </vxe-column>
          <vxe-column
            field="code"
            :width="columnWidth(170)"
            title="内容"
            :edit-render="{ enabled: !store.updateSS }"
          >
            <template #default="{ row, column }">
              <span>{{ calculateBaseDropDownList.find(a=>a.code===row.code)?.name }}</span>
            </template>
            <template #edit="{ row, column }">
              <vxe-select
                v-model="row.code"
                :transfer="true"
              >
                <vxe-option
                  v-for="item of calculateBaseDropDownList"
                  :value="item.code"
                  :label="item.code === 'RGF_DEJ+JXF_DEJ'? item.name + '(默认)':item.name"
                ></vxe-option>
              </vxe-select>
            </template>
          </vxe-column>
          <template #empty>
            <span style="
                color: #898989;
                font-size: 14px;
                display: block;
                margin: 25px 0;
              ">
              <icon-font
                style="font-size: 22px"
                type="icon-zanwushuju"
              ></icon-font>
              暂无数据
            </span>
          </template>
        </vxe-table>
      </div>
    </div>

    <p class="line"></p>
    <div class="totalTable">
      <p class="title">
        <span class="textTitle">
          <img
            src="../../../../assets/img/detailImg/feezonglan.png"
            alt="费率总览"
          />
          <a-tooltip
            placement="rightTop"
            color="white"
          >
            <template #title>
              <div
                style="color: #000000; font-size: 12px"
                class="tooltip"
              >
                <p>
                  <span class="tooltip-red scale"></span>字体标红：工程项目中相同取费文件费率值不同时，字体会标红处理
                </p>
                <p v-if="store.currentTreeInfo.levelType<3">
                  <span class="tooltip-yellow scale"></span>底色标黄：修改费率值后，未统一应用前会底色标识
                </p>
                <p>
                  <span class="tooltip-green scale"></span>底色标绿：应用的费率值同费率说明对应的默认值不同时，底色标绿展示
                </p>
              </div>
            </template>
            费率总览<icon-font
              type="icon-bangzhu"
              style="margin:0 5px;"
            ></icon-font>
          </a-tooltip>
          <!-- <span> 费率总览 </span> -->
        </span>
        <span
          class="resetFee"
          @click="resetFee"
        ><icon-font
            type="icon-huifumorenfeishuai"
            style="margin-right: 5px"
          />恢复默认费率</span>
      </p>

      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        :data="totalData"
        ref="rightTable"
        max-height="90%"
        keep-source
        @edit-closed="editClosedEvent"
        :cell-style="cellStyle"
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          beforeEditMethod: cellBeforeEditMethod,
        }"
        class="table-edit-common table-no-outer-border"
        @cell-click="useCellClickEvent"
        :cell-class-name="cellClassName"
      >
        <vxe-column
          type="seq"
          :width="columnWidth(50)"
          title="序号"
        > </vxe-column>
        <vxe-column
          field="feeCode"
          :width="columnWidth(60)"
          title="取费编码"
        > </vxe-column>
        <vxe-column
          field="feeFileName"
          :width="columnWidth(120)"
          title="取费名称"
        >
        </vxe-column>
        <vxe-column
          field="managementFee"
          :width="columnWidth(100)"
          title="管理费（%）"
          :edit-render="{
            autofocus: '.vxe-input--inner',
            enabled: !store.updateSS,
          }"
        >
          <template #edit="{ row }">
            <vxe-input
              :clearable="false"
              v-model="row.managementFee"
              :maxlength="10"
              @blur="
                (row.managementFee = pureNumber(row.managementFee, 2)), clear()
              "
            ></vxe-input>
          </template>
        </vxe-column>
        <vxe-column
          field="profit"
          :width="columnWidth(90)"
          title="利润（%）"
          :edit-render="{
            autofocus: '.vxe-input--inner',
            enabled: !store.updateSS,
          }"
        >
          <template #edit="{ row }">
            <vxe-input
              :clearable="false"
              v-model="row.profit"
              :maxlength="10"
              @blur="(row.profit = pureNumber(row.profit, 2)), clear()"
            ></vxe-input>
          </template>
        </vxe-column>
        <vxe-column
          field="deStandardReleaseYear"
          :width="columnWidth(150)"
          title="定额标准"
          fixed="right"
          v-if="isShowDetype && store.currentTreeInfo.levelType <3"
        >
          <template #default="{ row }">
            <span v-if="row.deStandardReleaseYear === '12'">12定额标准单位工程应用</span>
            <span v-if="row.deStandardReleaseYear === '22'">22定额标准单位工程应用</span>
          </template>
        </vxe-column>
        <vxe-column
          field="fees"
          :width="columnWidth(90)"
          title="规费（%）"
          :edit-render="{
            autofocus: '.vxe-input--inner',
            enabled: !store.updateSS,
          }"
          v-if="totalData?.find(a => a.deStandardReleaseYear === '12')"
        >
          <template #edit="{ row }">
            <vxe-input
              v-if="row.deStandardReleaseYear !== '22'"
              :clearable="false"
              v-model="row.fees"
              :maxlength="10"
              @blur="(row.fees = pureNumber(row.fees, 2)), clear()"
            ></vxe-input>
            <span v-if="row.deStandardReleaseYear === '22'">/</span>
          </template>
          <template #default="{ row }">
            <span v-if="row.deStandardReleaseYear === '12'">{{
              row.fees
            }}</span>
            <span v-if="row.deStandardReleaseYear === '22'">/</span>
          </template>
        </vxe-column>
        <vxe-column
          field="anwenRateBase"
          min-width="180"
          title="安全生产、文明施工费（%）"
          :edit-render="{
              autofocus: '.vxe-input--inner',
              enabled: !store.updateSS,
            }"
        >
          <template #edit="{ row }">
            <vxe-input
              :clearable="false"
              v-model="row.anwenRateBase"
              :maxlength="10"
              @blur="
                  (row.anwenRateBase =removeExtraZerosAndDot ( pureNumber(row.anwenRateBase, 4))),
                    clear()
                "
            ></vxe-input></template></vxe-column>
        <!-- <vxe-colgroup title="安全生产、文明施工费（%）">
          <vxe-column
            field="anwenRateBase"
            width="85"
            title="基本费"
            :edit-render="{
              autofocus: '.vxe-input--inner',
              enabled: !store.updateSS,
            }"
          >
            <template #edit="{ row }">
              <vxe-input
                :clearable="false"
                v-model="row.anwenRateBase"
                :maxlength="10"
                @blur="
                  (row.anwenRateBase =removeExtraZerosAndDot ( pureNumber(row.anwenRateBase, 4))),
                    clear()
                "
              ></vxe-input></template></vxe-column>
          <vxe-column
            field="anwenRateAdd"
            :width="columnWidth(85)"
            title="增加费"
            :edit-render="{
              autofocus: '.vxe-input--inner',
              enabled: !store.updateSS,
            }"
          >
            <template #edit="{ row }">
              <vxe-input
                :clearable="false"
                v-model="row.anwenRateAdd"
                :maxlength="10"
                @blur="
                  (row.anwenRateAdd =removeExtraZerosAndDot( pureNumber(row.anwenRateAdd, 4))), clear()
                "
              ></vxe-input>
            </template>
          </vxe-column>
        </vxe-colgroup> -->
        <template #empty>
          <span style="
              color: #898989;
              font-size: 14px;
              display: block;
              margin: 25px 0;
            ">
            <icon-font
              style="font-size: 22px"
              type="icon-zanwushuju"
            ></icon-font>
            暂无数据
          </span>
        </template>
      </vxe-table>
    </div>
  </div>
</template>

<script setup>
import { constructLevelTreeStructureList } from '@/api/csProject';
import feePro from '@/api/feePro';
import { getProData, setGlobalLoading } from '@/hooks/publicApiData';
import { useCellClick } from '@/hooks/useCellClick';
import { columnWidth } from '@/hooks/useSystemConfig';
import { projectDetailStore } from '@/store/projectDetail';
import { pureNumber } from '@/utils/index';
import { message } from 'ant-design-vue';
import { defineEmits, nextTick, onMounted, reactive, ref, watch } from 'vue';
import xeUtils from 'xe-utils';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } = useCellClick();
const lodash = require('lodash');
const props = defineProps(['isUpdateDocBol']);
const emit = defineEmits(['isConfirm', 'getfeeTotalData']);
const store = projectDetailStore();
const leftTable = ref();
const rightTable = ref();
let decData = ref([]);
const leftRateTable = ref();
let rateData = ref([]); //计税方式表格数据
let calData = ref([]); //计算基数编辑表格
const lists = reactive({
  taxMathodList: [], //计税方式列表
  locationList: [], //区市列表
});
let taxMode = ref();
let saveRateData = reactive({
  //计税方式修改接口参数
  additionalTaxRate: '',
  feeFileId: '',
  levelType: '',
  outputTaxRate: '',
  simpleRate: '',
  taxRate: '',
  taxCalculationMethod: null,
  taxPayingRegion: null,
  taxReformDocumentsId: '',
});
const reset = () => {
  lists.taxMathodList = [];
  lists.locationList = [];
};
const rowStyle = ({ row }) => {
  if (!row.isEdit) {
    return {
      backgroundColor: '#f3f3f3',
    };
  }
};
const removeExtraZerosAndDot = numStr => {
  return numStr.toString().replace(/(\.0*)$/, '');
};
const list = { glf: '管理费', lr: '利润', gf: '规费' };
let leftCalTable = ref();
const getCalData = async isFirst => {
  //获取计算基数表格数据
  let apiData = getParamsData({});
  await feePro.queryProjectUnitCalculateBaseList(apiData).then(res => {
    console.log('queryProjectUnitCalculateBaseList', res, apiData);
    if (res.status === 200) {
      res.result.map(i => {
        i.rowName = list[i.type];
        i.codeName = calculateBaseDropDownList.value?.find(a => a.code === i.code)?.name;
        i.sequenceNbr = i.rowName;
      });
      calData.value = res.result;
      if (isFirst) oldTablesData.calData = [...JSON.parse(JSON.stringify(res.result))];
    }
  });
};
let calcEditData = ref([]);
let feeCalculateBaseList = ref([]);
const changeCalEdit = ({ row, column }) => {
  const $table = leftCalTable.value;
  const field = column.field;
  let value = row[field];
  let oldValue = row['oldValue'];
  if (value == oldValue && store.currentTreeInfo.levelType === 3) {
    return;
  }
  row['oldValue'] = row[field];
  console.log('changeCalEdit', field, row);
  feeCalculateBaseList.value = [];
  calData.value.map(i => {
    feeCalculateBaseList.value.push({ code: i.code, type: i.type });
  });
  if (!$table.isUpdateByRow(row, field)) {
    calcEditData.value = [];
  } else {
    calcEditData.value = feeCalculateBaseList.value;
  }
  let apiData = {
    feeCalculateBaseList: JSON.parse(JSON.stringify(feeCalculateBaseList.value)),
  };
  apiData = getParamsData(apiData);
  feePro
    .updateProjectUnitCalculateBaseList(apiData)
    .then(res => {
      console.log('updateProjectUnitCalculateBaseList', res, apiData);
      if (res.status === 200) {
        getTotalAndDesData(false);
      } else {
        $table.revertData(row, field);
      }
    })
    .finally(() => {
      if (store.currentTreeInfo.levelType < 3) {
        // emit('saveCalData', feeCalculateBaseList);
        // emit('isConfirm', false); //修改费率总览统一应用按钮也可点击
        setUseDisabled(true, false);
      }
    });
};
const getRateData = isFirst => {
  let apiData = {
    levelType: store.currentTreeInfo.levelType,
  };
  apiData = getParamsData(apiData);

  let key_value;
  let rateTable = [];
  reset();
  if (store.currentTreeInfo.levelType < 3 && store.deType / 1 === 22) {
    //22工程项目只含有12单位的时候计税方式表格数据不展示数据
    let treeList = xeUtils.toTreeArray([store.currentTreeInfo]);
    let unitList = treeList.filter(a => a.levelType === 3) || [];
    if (unitList.length > 0 && unitList.every(a => a.deStandardReleaseYear / 1 == 12)) {
      rateData.value = [];
      return;
    }
  }
  // debugger;
  feePro.getTaxCalculation(apiData).then(res => {
    if (res.status === 200) {
      res.result &&
        res.result.taxPayingRegionOption &&
        res.result.taxPayingRegionOption.map(item => {
          key_value = getKeyValue(item);
          lists.locationList.push(key_value);
        });
      for (let key in saveRateData) {
        if (res.result.hasOwnProperty(key)) {
          saveRateData[key] = res.result[key];
        }
      }
      taxMode.value = res.result.taxCalculationMethod;
      rateTable = [
        {
          sortNo: 1,
          rowName: '计税方式',
          contextT:
            Number(res.result.taxCalculationMethod) === 1 ? '增值税一般计税' : '增值税简易计税',
          // contentype: 'select',
          // selectList: lists.taxMathodList,
          isEdit: false,
          sequenceNbr: 1,
          key: 'taxMade',
        },
        {
          sortNo: 2,
          rowName: '区市',
          contextT: res.result.taxPayingRegion,
          contentype: 'select',
          selectList: lists.locationList,
          isEdit: true,
          sequenceNbr: 2,
          key: 'taxPayingRegion',
        },
      ];
      if (isShowSome.value && Number(res.result.taxCalculationMethod) === 1) {
        rateTable.push(
          {
            sortNo: 3,
            rowName: '附加税费税率',
            contextT: res.result.additionalTaxRate,
            contentype: 'edit',
            isEdit: true,
            sequenceNbr: 4,
            key: 'additionalTaxRate',
          },
          {
            sortNo: 4,
            rowName: '销项税费率',
            contextT: res.result.outputTaxRate,
            contentype: 'edit',
            isEdit: true,
            sequenceNbr: 5,
            key: 'outputTaxRate',
          }
        );
      } else if (store.deType === '22') {
        rateTable.push({
          sortNo: 3,
          rowName: '税率',
          contextT: res.result.taxRate,
          contentype: 'edit',
          isEdit: true,
          sequenceNbr: 3,
          key: 'taxRate',
        });
      } else {
        rateTable.push({
          sortNo: 3,
          rowName: '税率',
          contextT: res.result.simpleRate,
          contentype: 'edit',
          isEdit: true,
          sequenceNbr: 3,
          key: 'simpleRate',
        });
      }
      rateData.value = [...rateTable];
      if (isFirst) oldTablesData.rateData = [...JSON.parse(JSON.stringify(rateTable))];
      console.log('获取计税方式表格数据', rateData.value, res, store.deType);
    }
  });
};
const clearRate = () => {
  //清除编辑状态
  const $table = leftRateTable.value;
  $table.clearEdit();
};

//选中地区更新税率
const selectLocation = row => {
  let formData = {
    method: taxMode.value,
    region: row.contextT,
    constructId: store.currentTreeGroupInfo?.constructId,
  };
  feePro.getRateByMethodAndLocation(formData).then(res => {
    if (res.status === 200) {
      console.log('getRateByMethodAndLocation', formData, res.result);
      rateData.value.map(item => {
        if (item.key !== 'taxMade' && item.key !== 'taxPayingRegion')
          item.contextT = res.result[item.key] ? res.result[item.key] : null;
        if (saveRateData.hasOwnProperty(item.key)) {
          if (item.key !== 'taxMade' && item.key !== 'taxPayingRegion')
            saveRateData[item.key] = item.contextT;
        }
      });
      if (store.currentTreeInfo.levelType === 3) {
        saveTaxCalculation();
      }
    }
  });
};
const saveTaxCalculation = () => {
  let apiData = { ...saveRateData, ...getProData() };
  feePro.saveTaxCalculation(apiData).then(res => {
    console.log('保存计税方式数据saveRateChange-apiData', apiData, res);
    if (res.status === 200) {
      message.success('修改成功');

      getTotalAndDesData();
    }
  });
};
const changeRateEdit = ({ row, column }) => {
  // debugger;
  const $table = leftRateTable.value;
  const field = column.field;
  let value = row[field];
  if (!$table.isUpdateByRow(row, field) && store.currentTreeInfo.levelType === 3) {
    return;
  }
  if (row.contentype === 'edit' && (Number(value) > 1000 || Number(value) < 0)) {
    //数值超过限制还原
    message.warn(`可输入数值范围：0-1000`);
    leftRateTable.value.revertData(row, field);
    return;
  }
  if (row.contentype === 'edit') {
    value = value / 1;
    row[field] = value;
  }
  saveRateData[row.key] = value;
  console.log('changeRateEdit');
  saveRateChange(row);
};

const saveRateChange = row => {
  if (row.contentype === 'select') {
    selectLocation(row);
  } else if (row.contentype === 'edit' && store.currentTreeInfo.levelType === 3) {
    saveTaxCalculation();
  }
  if (store.currentTreeInfo.levelType < 3) {
    // emit('isConfirm', false); //修改费率总览统一应用按钮也可点击
    setUseDisabled(false, true);
    // emit('getfeeTotalData', getData());
  }
};
let totalData = ref([]);
const clear = () => {
  //清除编辑状态
  const $table = rightTable.value;
  $table.clearEdit();
};
const resetFee = () => {
  // message.info('功能建设中...');
  let unitFeeDescriptionList = [];
  leftTable.value.data.map(item => {
    unitFeeDescriptionList.push({ name: item.name, context: item.context });
  });
  let apiData = {
    levelType: store.currentTreeInfo.levelType,
    sequenceNbr: store.feeWithDrawalInfo.key,
    unitFeeDescriptionList: unitFeeDescriptionList,
  };
  apiData = getParamsData(apiData);
  console.log('resetFee', apiData, store.feeWithDrawalInfo);
  feePro.restoreDefaultFee(apiData).then(res => {
    console.log('恢复默认费率', apiData, res);
    if (res.status === 200) {
      message.success('操作成功');
      if (store.currentTreeInfo.levelType < 3) {
        totalData.value = res.result.costOverview;
        // emit('isConfirm', false); //修改费率总览统一应用按钮也可点击
        // setUseDisabled(false, true);
        // emit('getfeeTotalData', getData());
        console.log(totalData.value, oldTablesData.totalData);
        emit('getfeeTotalData', getData());
        emit('isConfirm', false); //修改费率总览统一应用按钮也可点击
      } else {
        getTotalAndDesData(); //刷新表格数据
      }
    }
  });
};
const editClosedEvent = ({ row, column }) => {
  const $table = rightTable.value;
  const field = column.field;
  let value = row[field];
  if (Number(value) > 1000 || Number(value) < 0) {
    //数值超过限制还原
    message.warn(`可输入数值范围：0-1000`);
    $table.revertData(row, field);
    return;
  }
  if (store.currentTreeInfo.levelType === 3 && $table.isUpdateByRow(row, field)) {
    singleSaveFeeData(row, true);
  } else if (store.currentTreeInfo.levelType < 3) {
    let tar = oldTablesData.totalData?.find(a => a.sequenceNbr === row.sequenceNbr);
    if (tar) {
      // debugger;
      if (tar[field] / 1 != value / 1) {
        row.isBgCulumn = row.isBgCulumn?.length > 0 ? [field, ...row.isBgCulumn] : [field];
      } else {
        if (row.isBgCulumn?.length > 0) {
          let index = row.isBgCulumn?.indexOf(field);
          if (index !== -1) row.isBgCulumn.splice(index, 1);
        }
      }
    }
    console.log(oldTablesData, decData.value, rateData.value, calData.value);
    setUseDisabled(false, true);
    // emit('getfeeTotalData', getData());
  }
};
const getNewList = arr => {
  let list = [];
  arr.map(a => {
    let b = { ...a };
    delete b['_X_ROW_KEY'];
    delete b['height'];
    list.push(b);
  });
  return list;
};
const setUseDisabled = (isSaveCal = false, isGetFee = false) => {
  // console.log(
  //   'setUseDisabled---费率总览',
  //   totalData.value.some(a => a?.isBgCulumn?.length > 0)
  // );
  // console.log(
  //   'setUseDisabled----费率说明',
  //   // oldTablesData.decData,
  //   // JSON.parse(JSON.stringify(getNewList(decData.value))),
  //   !lodash.isEqual(
  //     oldTablesData.decData,
  //     JSON.parse(JSON.stringify(getNewList(decData.value)))
  //   )
  // );
  // console.log('rateData.value', rateData.value);
  // debugger;
  // console.log(
  //   'setUseDisabled----计税方式',
  //   oldTablesData.rateData,
  //   JSON.parse(JSON.stringify(getNewList(rateData.value))),
  //   !lodash.isEqual(
  //     oldTablesData.rateData,
  //     JSON.parse(JSON.stringify(getNewList(rateData.value)))
  //   )
  // );
  // console.log(
  //   'setUseDisabled-----费用计算基数设置',
  //   // oldTablesData.calData,
  //   // JSON.parse(JSON.stringify(getNewList(calData.value, '_X_ROW_KEY'))),
  //   !lodash.isEqual(
  //     oldTablesData.calData,
  //     JSON.parse(JSON.stringify(getNewList(calData.value)))
  //   ),
  //   '政策文件',
  //   props.isUpdateDocBol
  // );
  if (
    totalData.value.some(a => a?.isBgCulumn?.length > 0) ||
    !lodash.isEqual(oldTablesData.decData, JSON.parse(JSON.stringify(getNewList(decData.value)))) ||
    !lodash.isEqual(
      oldTablesData.rateData,
      JSON.parse(JSON.stringify(getNewList(rateData.value)))
    ) ||
    (!lodash.isEqual(
      oldTablesData.calData,
      JSON.parse(JSON.stringify(getNewList(calData.value)))
    ) &&
      calcEditData.value?.length > 0) ||
    props.isUpdateDocBol
  ) {
    if (isSaveCal) emit('saveCalData', JSON.parse(JSON.stringify(feeCalculateBaseList.value)));
    if (isGetFee) emit('getfeeTotalData', getData());
    emit('isConfirm', false); //修改费率总览统一应用按钮也可点击
  } else {
    emit('isConfirm', true); //修改费率总览统一应用按钮也可点击
    store.SET_HUMAN_UPDATA_DATA(null);
    console.log(store.humanUpdataData);
    // debugger;
  }
  console.log('setUseDisabled----SET_HUMAN_UPDATA_DATA', store.humanUpdataData);
};
const singleSaveFeeData = (row, hasFiled) => {
  const $table = rightTable.value;
  //单次修改费率总览
  let formData = {
    sequenceNbr: row.sequenceNbr,
    managementFee: Number(row.managementFee),
    profit: Number(row.profit),
    fees: Number(row.fees),
    anwenRateBase: Number(row.anwenRateBase),
    anwenRateAdd: Number(row.anwenRateAdd),
    levelType: store.currentTreeInfo.levelType,
    feeFileId: store.feeWithDrawalInfo?.key,
  };
  formData = getParamsData(formData);
  setGlobalLoading(true, '数据计算中，请稍后...');
  feePro
    .saveCostOverview(formData)
    .then(function (response) {
      // console.log('单次保存费率总览', response);
      if (response.status === 200) {
        message.success('修改成功');
        if (hasFiled) {
          getTotalAndDesData();
        } else if (!hasFiled) {
          // getTotalAndDesData();
        }
      } else {
        message.error(response.message);
      }
    })
    .finally(() => {
      setGlobalLoading(false, '数据计算中，请稍后...');
    });
};
const getData = () => {
  //获取保存接口两个表格参数
  let unitFeeDescriptionList = [];
  let costOverviewList = [];
  leftTable.value.data.map(item => {
    unitFeeDescriptionList.push({ name: item.name, context: item.context });
  });
  // rateData.value.map(item => {
  totalData.value.map(item => {
    costOverviewList.push({
      feeFileCode: item.feeFileCode,
      managementFee: item.managementFee,
      profit: item.profit,
      fees: item.fees,
      anwenRateBase: item.anwenRateBase,
      anwenRateAdd: item.anwenRateAdd,
      deStandardReleaseYear: item.deStandardReleaseYear,
    });
  });
  let formData = {
    ...saveRateData,
    levelType: store.currentTreeInfo.levelType,
    feeFileId: store.feeWithDrawalInfo?.key,
    unitFeeDescription: unitFeeDescriptionList,
    costOverview: costOverviewList,
    isDecChange: isDecChange.value,
  };
  formData = getParamsData(formData);
  return formData;
};
const projectUpdate = () => {
  //工程项目级别修改
  let formData = getData();
  formData = JSON.parse(JSON.stringify(formData));
  console.log('---------projectUpdate', formData);
  setGlobalLoading(true, '统一应用中，请稍后...');
  feePro
    .unifiedUse(formData)
    .then(res => {
      if (res.status === 200) {
        message.success('统一应用成功!');
        emit('isConfirm', true);
        getTotalAndDesData();
        store.SET_HUMAN_UPDATA_DATA(null);
      }
    })
    .finally(() => {
      setGlobalLoading(false, '统一应用中，请稍后...');
    });
  if (calcEditData.value?.length > 0) {
    let apiData = {
      feeCalculateBaseList: JSON.parse(JSON.stringify(calcEditData.value)),
    };
    apiData = getParamsData(apiData);
    console.log('updateProjectUnitCalculateBaseApply', apiData);
    feePro.updateProjectUnitCalculateBaseApply(apiData).then(res => {
      if (res.status === 200) {
        console.log('updateProjectUnitCalculateBaseApply', res);
      }
    });
  }
};
let calculateBaseDropDownList = ref([]);
const queryCalculateBaseDropDownList = async () => {
  let apiData = {
    type: 'fee',
    is22de: +store.deStandardReleaseYear === 22,
  };
  await feePro.queryCalculateBaseDropDownList(apiData).then(res => {
    console.log('queryCalculateBaseDropDownList', res, apiData);
    if (res.status === 200) {
      calculateBaseDropDownList.value = res.result;
    }
  });
};
let isShowDetype = ref(false);
let isShowSome = ref(false);
let oldTablesData = reactive({
  //取费表页面各个表格数据初始数据保存
  decData: [],
  rateData: [],
  calData: [],
  totalData: [],
});
//getTotalAndDesData防止重复调用
let firstSingle = null;
const getTreeList = async () => {
  const res = await constructLevelTreeStructureList(store.currentTreeGroupInfo.constructId);
  let treeData = res.result;
  getParentItem(store.currentTreeInfo, treeData);
};

const getParentItem = (tar, treeList) => {
  //获取目标平铺树结构最外层单项父级
  let parent = treeList?.find(i => i.id === tar.parentId);
  parent && parent?.levelType !== 1 ? getParentItem(parent, treeList) : (firstSingle = { ...tar });
};
const getTotalAndDesData = xeUtils.debounce(async (isRefshCal = true, isFirst = false) => {
  console.log('------------进来了');
  if (store.tabSelectName !== '取费表') return;
  setGlobalLoading(true, '加载中，请稍后...');
  await queryCalculateBaseDropDownList();
  let apiData = {
    levelType: store.currentTreeInfo.levelType,
    feeFileId: store.feeWithDrawalInfo?.key,
  };
  if (
    apiData.levelType === 3 &&
    store.currentTreeInfo.parentId !== store.currentTreeGroupInfo.constructId
  ) {
    await getTreeList();
    apiData.firstSingle = firstSingle?.id;
  }
  apiData = getParamsData(apiData);
  // console.log('getFeeCollectionData', apiData);
  let key_value;
  isShowSome.value =
    store.deType === '12' ||
    (store.deType === '22' &&
      store.currentTreeInfo.levelType === 3 &&
      store.currentTreeInfo.deStandardReleaseYear === '12');
  getRateData(isFirst); //获取计税方式表格数据
  if (isRefshCal) await getCalData(isFirst);
  await feePro
    .getFeeCollectionData(apiData)
    .then(res => {
      if (res.status === 200 && res.result) {
        console.log('getFeeCollectionData', apiData, res.result);
        res.result.unitFeeDescriptionList?.map(item => {
          let optionList = [];

          item.optionList?.map(listData => {
            key_value = getKeyValue(listData);
            optionList.push(key_value);
          });
          item.optionList = optionList;

            item.optionList.map(option => {
              if (option.code === item.context) {
                item.contextT = option.value;
              }
            });
            // if (item.name === '二级取费专业') item.name = '主专业';
          });
          decData.value = res.result.unitFeeDescriptionList;
          //可编辑费率取两位小数
          res.result.costOverview &&
            res.result.costOverview.map(item => {
              item.managementFee = item.managementFee
                ? Number(item.managementFee).toFixed(2)
                : '0.00';
              item.profit = item.profit
                ? Number(item.profit).toFixed(2)
                : '0.00';
              // item.fees = item.fees ? Number(item.fees).toFixed(2) : '0.00';
              // item.anwenRateBase = item.anwenRateBase
              //   ? Number(item.anwenRateBase).toFixed(2)
              //   : '0.00';
              // item.anwenRateAdd = item.anwenRateAdd
              //   ? Number(item.anwenRateAdd).toFixed(2)
              //   : '0.00';
            });
          totalData.value = res.result.costOverview;
          if (isFirst) {
            oldTablesData.totalData = [
              ...JSON.parse(JSON.stringify(res.result?.costOverview || [])),
            ];
            oldTablesData.decData = [
              ...JSON.parse(
                JSON.stringify(res.result?.unitFeeDescriptionList || [])
              ),
            ];
          }

        let deList = [];
        if (store.currentTreeInfo.levelType < 3) {
          deList = res.result?.costOverview?.map(a => {
            return a.deStandardReleaseYear;
          });
          if (deList?.length > 0) {
            isShowDetype.value = Array.from(new Set(deList)).length === 1 ? false : true;
          }
        }
      } else {
        decData.value = [];
        totalData.value = [];
        message.error(response.message);
      }
    })
    .finally(() => {
      nextTick(() => {
        setGlobalLoading(false, '加载中，请稍后...');
      });
    });
}, 300);

const getKeyValue = item => {
  let key = Object.keys(item)[0];
  let value = Object.values(item)[0];
  return { value: value, code: key };
};
watch(
  () => [store.feeWithDrawalInfo, store.currentTreeInfo],
  () => {
    if (store.tabSelectName === '取费表') {
      console.log('watch');
      getTotalAndDesData(true, true);
    }
  }
);
let isDecChange = ref(false);

const upDateDescription = row => {
  let unitFeeDescriptionList = [];
  leftTable.value.data.map(item => {
    unitFeeDescriptionList.push({ name: item.name, context: item.context });
  });
  //保存修改的费率说明传参
  let apiData = {
    // sequenceNbr: row.sequenceNbr,
    name: row.name,
    context: row.context,
    levelType: store.currentTreeInfo.levelType,
    feeFileId: store.feeWithDrawalInfo?.key,
  };
  apiData = getParamsData(apiData);
  //获取修改费率说明后费率总览变化的接口传参
  let formData = {
    costOverview: JSON.parse(JSON.stringify(totalData.value)),
    unitFeeDescriptionList: unitFeeDescriptionList,
    name: row.name,
    context: row.context,
    levelType: store.currentTreeInfo.levelType,
    feeFileId: store.feeWithDrawalInfo?.key,
  };
  formData = getParamsData(formData);
  if (store.currentTreeInfo.levelType === 3) {
    feePro.saveFeeDescription(apiData).then(res => {
      console.log('修改费率说明-----获取', 'apiData', apiData, 'res', res);

      if (res.status === 200) {
        //修改费率说明成功后更改左侧树
        message.success('修改成功');
        getTotalAndDesData();
        if (['主专业', '二级取费专业'].includes(row.name)) {
          store.SET_IS_REFRESH_PROJECT_TREE(true);
        }
      }
    });
  } else if (store.currentTreeInfo.levelType < 3) {
    console.log('saveFeeDescription---工程', formData);
    feePro.getCostOverview(formData).then(res => {
      console.log('saveFeeDescription---工程', res);
      // res.result && changeOverData(res.result.costOverview);
      if (res.result) {
        totalData.value = res.result.costOverview;
        // emit('isConfirm', false); //修改费率总览统一应用按钮也可点击
        setUseDisabled(false, true);
        // emit('getfeeTotalData', getData());
      }
    });
  }
};
const changeOverData = list => {
  let changeList = []; //list 中change-sequenceNbrList
  let feeTotal = [];
  let changeIndex = []; //totalData.value 中change-sequenceNbrList-序号
  let totalIndex = []; //totalData.value 中 sequenceNbrList
  list.map(item => {
    changeList.push(item.sequenceNbr);
  });
  totalData.value.map(item => totalIndex.push(item.sequenceNbr));
  changeList.map(item => {
    changeIndex.push(totalIndex.indexOf(item));
  });
  changeIndex.map((item, index) => {
    totalData.value.splice(
      item,
      1,
      list.find(item => item.sequenceNbr === changeList[index])
    );
  });
  totalData.value.map((item, index) => {
    item.managementFee = Number(item.managementFee).toFixed(2);
    item.profit = Number(item.profit).toFixed(2);
    item.fees = Number(item.fees).toFixed(2);
    item.anwenRateBase = Number(item.anwenRateBase).toFixed(2);
    item.anwenRateAdd = Number(item.anwenRateAdd).toFixed(2);
    item.sortNum = index + 1;
  });
  const $table = rightTable.value;
  $table.reloadData(totalData.value);
  console.log('list', list);
  console.log('totalData.value', totalData.value);
  if (store.currentTreeInfo.levelType === 3) {
    list.map(item => {
      singleSaveFeeData(item, false);
    });
  }
};
const selectChange = (row, contextT) => {
  const rowContext = row?.optionList.filter(item => item.value === contextT);
  row.context = rowContext[0].code;
  //清除编辑状态
  const $table = leftTable.value;
  $table.clearEdit();
  isDecChange.value = oldTablesData.decData.some(a =>
    decData.value.find(b => b.sequenceNbr === a.sequenceNbr && b.contextT != a.contextT)
  );
  upDateDescription(row);
};
const cellClassNameCum = ({ column, row, $columnIndex }) => {
  let className = selectedClassName({ $columnIndex, row, column });
  if (['code'].includes(column.field) && row.code !== 'RGF_DEJ+JXF_DEJ') {
    className += ' redColor';
  }
  return className;
};
const cellClassName = ({ column, row, $columnIndex }) => {
  let className = selectedClassName({ $columnIndex, row, column });
  if (['managementFee', 'profit', 'fees', 'anwenRateBase', 'anwenRateAdd'].includes(column.field)) {
    if (getBg(row, column.field)) className += ' yellowBg';
  }
  // console.log('className', className);
  return className;
};
const getBg = (row, feild) => {
  let bgFeild = `${feild}FlagBg`;
  return Number(row[bgFeild]) === 1;
};
const cellStyle = ({ row, column }) => {
  if (row.isBgCulumn?.length > 0) {
    if (row.isBgCulumn.includes(column.field)) {
      return {
        backgroundColor: '#F5FFDB !important',
      };
    }
  }
  if (['managementFee', 'profit', 'fees', 'anwenRateBase', 'anwenRateAdd'].includes(column.field)) {
    if (+row[`${column.field}MarkFlag`] === 1) {
      return {
        color: 'red',
      };
    }
  }
};
const getParamsData = data => {
  let apiData = { ...data };
  apiData.constructId =
    store.currentTreeInfo.levelType === 1
      ? store.currentTreeInfo?.id
      : store.currentTreeGroupInfo?.constructId;
  if (store.currentTreeInfo.levelType === 2) {
    apiData.singleId = store.currentTreeInfo?.id; //单项ID
  }
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  return apiData;
};
onMounted(() => {
  if (store.tabSelectName === '取费表') {
    console.log('onMounted-取费表');
    getTotalAndDesData(true, true);
  }
});
defineExpose({
  projectUpdate,
  getTotalAndDesData,
});
</script>
<style lang="scss" scoped>
.feeTable {
  width: 100%;
  height: 100%;
  display: flex;
  // justify-content: space-between;
  // flex-direction: row;
  overflow: auto;
  .title {
    // width: 102px;
    display: inline-block;
    height: 30px;
    line-height: 30px;
    box-sizing: border-box;
    padding: 1px;
    margin: 0;
    border-radius: 0px 17px 0px 0px;
    position: relative;

    background-image: linear-gradient(180deg, rgba(129, 162, 240, 1), rgba(220, 223, 230, 1));
    .textTitle {
      display: block;
      width: 100%;
      height: 100%;
      border-radius: 0px 17px 0px 0px;
      background: #fff;
      font-size: 12px;
      color: #287cfa;
      img {
        margin: -3px 4px 0;
      }
      span {
        margin-right: 3px;
      }
    }
    .resetFee {
      position: absolute;
      width: 150px;
      left: 150px;
      top: 0px;
      background: rgba(250, 250, 250, 0.39);
      border: none;
      cursor: pointer;
    }
  }
  .line {
    width: 4px;
    max-height: 480px;
    background: linear-gradient(
      180deg,
      rgba(66, 158, 252, 0) 0%,
      rgba(58, 148, 251, 0.55) 28%,
      #348cfb 51%,
      rgba(46, 133, 250, 0.55) 73%,
      rgba(40, 124, 250, 0) 100%
    );
    opacity: 0.52;
    margin: 0 15px 0 5px;
  }
  .leftContent {
    // float: left;
    display: flex;
    // justify-content: space-between;
    flex-direction: column;
    // flex-wrap: wrap;
    // width: 35%;
    height: 100%;
    //overflow-y: scroll;
  }
  .decTable {
    width: 100%;
    margin: 5px 0;
  }
  .totalTable {
    max-width: 63%;
  }
}
.tooltip {
  .scale {
    display: inline-block;
    width: 11px;
    height: 11px;
    border: 1px solid gray;
    margin-right: 2px;
  }
  &-red {
    background: red;
  }
  &-yellow {
    background: #f5ffdb;
  }
  &-green {
    background: rgba(75, 193, 182, 0.4);
  }
  p {
    margin: 0;
  }
}
::v-deep(.vxe-table .redColor) {
  color: red !important;
}
::v-deep(.vxe-table .yellowBg) {
  background-color: rgba(75, 193, 182, 0.4) !important;
}
</style>
