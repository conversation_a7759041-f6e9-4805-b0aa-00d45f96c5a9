const OtherProjectCalculationBaseConstant = require("../enum/OtherProjectCalculationBaseConstant");
const {Snowflake} = require("../utils/Snowflake");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {NumberUtil} = require("../utils/NumberUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {Service} = require("../../core");
const {OtherProjectProvisional} = require("../model/OtherProjectProvisional");
const {ConvertUtil} = require("../utils/ConvertUtils");

const { getConnection ,getRepository,getManager  } =require('typeorm');
class OtherProjectProvisionalService extends Service{


    //默认数量
    static defaultAmount = 1.0;
    //默认单价
    static defaultPrice  = 0;
    //默认暂定金额
    static defaultProvisionalSum = 0;
    //默认除税系数
    static defaultTaxRemoval = 3;
    //默认进项合计
    static defaultJxTotal = 0;
    //默认除税单价
    static defaultCsPrice  = 0 ;
    //默认除税合价
    static defaultCsTotal = 0;
    //默认单位
    static defaultUnit  = "项";

    //计算保留小数位
    static decimalPlaces = 2;



    constructor(ctx) {
        super(ctx);

    }
    //暂列金操作
    otherProjectProvisional(arg){

        //操作 类型  1:插入 2:粘贴 3删除 4 修改
        let operateType = arg.operateType;

        switch (operateType) {
            case 1:
                this.addProjectProvisional(arg);
                break;
            case 2:
                this.pasteProjectProvisional(arg);
                break;
            case 3:
                this.delectProjectProvisional(arg);
                break;
            case 4:
                this.updateProjectProvisional(arg);
                break;
        }

    }

    //导入初始化
    importInitProjectProvisional(){
        let otherProjectProvisional = new OtherProjectProvisional();
        otherProjectProvisional.sequenceNbr = Snowflake.nextId();
        otherProjectProvisional.amount = Number(1);
        otherProjectProvisional.price = OtherProjectProvisionalService.defaultPrice;
        otherProjectProvisional.provisionalSum = OtherProjectProvisionalService.defaultProvisionalSum;
        otherProjectProvisional.taxRemoval = OtherProjectProvisionalService.defaultTaxRemoval;
        otherProjectProvisional.jxTotal = OtherProjectProvisionalService.defaultJxTotal;
        otherProjectProvisional.csPrice = OtherProjectProvisionalService.defaultCsPrice;
        otherProjectProvisional.csTotal = OtherProjectProvisionalService.defaultCsTotal;
        otherProjectProvisional.unit = OtherProjectProvisionalService.defaultUnit;
        let  otherProjectProvisionals = new Array();
        otherProjectProvisionals.push(otherProjectProvisional);
        return  otherProjectProvisionals;
    }
    //插入
    addProjectProvisional(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectProvisionals;

        let number;
        if (!ObjectUtils.isEmpty(targetSequenceNbr) && ObjectUtils.isNotEmpty(list)) {
            number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);
        }else {
            number = null;
        }
        let otherProjectProvisional = new OtherProjectProvisional();
        otherProjectProvisional.sequenceNbr = Snowflake.nextId();
        otherProjectProvisional.amount = Number(1);
        otherProjectProvisional.price = OtherProjectProvisionalService.defaultPrice;
        otherProjectProvisional.provisionalSum = OtherProjectProvisionalService.defaultProvisionalSum;
        otherProjectProvisional.taxRemoval = OtherProjectProvisionalService.defaultTaxRemoval;
        otherProjectProvisional.jxTotal = OtherProjectProvisionalService.defaultJxTotal;
        otherProjectProvisional.csPrice = OtherProjectProvisionalService.defaultCsPrice;
        otherProjectProvisional.csTotal = OtherProjectProvisionalService.defaultCsTotal;
        otherProjectProvisional.unit = OtherProjectProvisionalService.defaultUnit;

        if ( number !== null) {
            list.splice(number+1, 0, otherProjectProvisional);
        }else {
            if (ObjectUtils.isEmpty(list)){
                unit.otherProjectProvisionals = new Array();
                unit.otherProjectProvisionals.push(otherProjectProvisional);

            }else {
                list.push(otherProjectProvisional) ;
            }
        }

    }
    //粘贴
    pasteProjectProvisional(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;
        //let pasteSequenceNbr = arg.pasteSequenceNbr;
        let projectProvisional = arg.projectProvisional;
        let otherProjectProvisional1 = new OtherProjectProvisional();
        ConvertUtil.setDstBySrc(projectProvisional,otherProjectProvisional1);
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectProvisionals;

        //let pasteProjectProvisional = list.find(obj => obj['sequenceNbr'] === pasteSequenceNbr);
        let number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);
        //let newProjectProvisional = lodash.cloneDeep(pasteProjectProvisional);
        otherProjectProvisional1.sequenceNbr = Snowflake.nextId();
        list.splice(number +1,0,otherProjectProvisional1);

        // this.updateOtherProjectProvisional(arg);
        this.service.otherProjectService.updateAllOtherProjects(arg);

    }

    //删除
    delectProjectProvisional(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectProvisionals;
        let number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);
        list.splice(number,1);

        // this.updateOtherProjectProvisional(arg);
        this.service.otherProjectService.updateAllOtherProjects(arg);
    }

    //编辑
     updateProjectProvisional(arg){
         let constructId = arg.constructId;
         let singleId = arg.singleId;
         let unitId = arg.unitId;
         let targetSequenceNbr = arg.targetSequenceNbr;

         let name = arg.projectProvisional.name;
         let price = arg.projectProvisional.price;
         let amount =  NumberUtil.qtxmAmountFormat(arg.projectProvisional.amount);
         let taxRemoval = arg.projectProvisional.taxRemoval;
         let dispNo = arg.projectProvisional.dispNo;
         let description = arg.projectProvisional.description;
         let unitBj = arg.projectProvisional.unit;


         let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
         let list = unit.otherProjectProvisionals;
         let projectProvisional = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
         if (!ObjectUtils.isEmpty(name)){
             projectProvisional.name = name;
         }

         if (!ObjectUtils.isEmpty(price)){
             projectProvisional.price = price;
         }

         if (!ObjectUtils.isEmpty(amount)){
             projectProvisional.amount = amount;
         }

         if (!ObjectUtils.isEmpty(taxRemoval)){
             projectProvisional.taxRemoval = taxRemoval;
         }

         if (!ObjectUtils.isEmpty(dispNo)){
             projectProvisional.dispNo = dispNo;
         }

         if (arg.projectProvisional.hasOwnProperty('description')){
             projectProvisional.description = description;
         }

         if (!ObjectUtils.isEmpty(unitBj)){
             projectProvisional.unit = unitBj;
         }


         //计算
         //计算暂列金额 a.暂列金额】=【单价】*【数量】
         projectProvisional.provisionalSum = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(projectProvisional.price,projectProvisional.amount));

         //除税系数默认=3%，【进项税合计】=暂列金额*除税系数%；【除税单价】=单价*（1-除税系数%）；【除税合价】=【暂列金额】*（1-除税系数%）
         let multiply = NumberUtil.multiply(projectProvisional.taxRemoval,0.01);
         //进项税合计
         projectProvisional.jxTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(multiply,projectProvisional.provisionalSum));
         let subtract = NumberUtil.subtract(1,multiply);
         //除税单价
         projectProvisional.csPrice =  NumberUtil.costPriceAmountFormat(NumberUtil.multiply(projectProvisional.price,subtract));
         //除税合价
         projectProvisional.csTotal =  NumberUtil.costPriceAmountFormat(NumberUtil.multiply(projectProvisional.provisionalSum,subtract));

         this.service.otherProjectService.updateAllOtherProjects(arg);
         // this.updateOtherProjectProvisional(arg);

     }

     //修改暂列金汇总表金额
    updateOtherProjectProvisional(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectProvisionals;

        let total = 0;
        let jxTotal = 0;
        let csTotal = 0;
        if (!ObjectUtils.isEmpty(list)) {
            for (let otherProjectProvisional1 of list) {
                total = NumberUtil.add(total, otherProjectProvisional1.provisionalSum);
                jxTotal = NumberUtil.add(jxTotal, otherProjectProvisional1.jxTotal);
                csTotal = NumberUtil.add(csTotal, otherProjectProvisional1.csTotal);
            }

            let otherProjects = unit.otherProjects;
            let t = otherProjects.find(j => j.type === OtherProjectCalculationBaseConstant.zljr);
            t.total =  NumberUtil.costPriceAmountFormat(NumberUtil.multiply(total, t.amount));
            t.jxTotal =  NumberUtil.costPriceAmountFormat(NumberUtil.multiply(jxTotal, t.amount));
            t.csTotal =  NumberUtil.costPriceAmountFormat(NumberUtil.multiply(csTotal, t.amount));
        }else {
            let otherProjects = unit.otherProjects;
            let t = otherProjects.find(j => j.type === OtherProjectCalculationBaseConstant.zljr);

            t.total = OtherProjectProvisionalService.defaultJxTotal.toFixed(2);
            t.jxTotal = OtherProjectProvisionalService.defaultJxTotal.toFixed(2);
            t.csTotal = OtherProjectProvisionalService.defaultJxTotal.toFixed(2);
        }

    }



}
OtherProjectProvisionalService.toString = () => '[class OtherProjectProvisionalService]';
module.exports = OtherProjectProvisionalService;