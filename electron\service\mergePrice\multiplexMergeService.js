'use strict';

const {Service} = require("../../../core");
const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const ConstructionMeasureTypeConstant = require("../../enum/ConstructionMeasureTypeConstant");
const ConstantUtil = require("../../enum/ConstantUtil");
const BranchProjectLevelConstant = require("../../enum/BranchProjectLevelConstant");
const DePropertyTypeConstant = require("../../enum/DePropertyTypeConstant");
const {IpcWinUtils} = require("../../utils/IpcWinUtils");
const {ResponseData} = require("../../utils/ResponseData");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const {ConvertUtil} = require("../../utils/ConvertUtils");
const {NumberUtil} = require("../../utils/NumberUtil");
const {HttpUtils} = require("../../utils/HttpUtils");
const {get2022BY2012Cslb} = require("../../model/Map2022And2012");
const _ = require("lodash");
const {arrayToTree,treeToArray}= require("../../main_editor/tree.js");
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const Fixs = require("../../fixs");
/**
 * 示例服务
 * @class
 */
class MultiplexMergeService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    //复用组价弹窗左侧工程项目结构树查询
    async projectConstructQuery(args) {
        let {constructId,type,selectHistory} = args;
        //如果是本工程
        if (type === "current") {
            let result = await this.service.constructProjectService.generateLevelTreeNodeStructure({"sequenceNbr":constructId});
            return {"treeNode":result};
        }
        //如果是历史工程
        let projectObjById =await PricingFileFindUtils.getProjectObjById(constructId);
        //没有主动选择上传并且 缓存数据不为空 就返回缓存数据结构，否则进行上传
        if (!selectHistory && ObjectUtils.isNotEmpty(global.multiplexMergeImportProject) && global.multiplexMergeImportProject[constructId] != null) {
            let result = await this.service.constructProjectService.generateLevelTreeNode(global.multiplexMergeImportProject[constructId]);
            //增加文件路径
            let path = global.multiplexMergeImportProject[constructId].path;
            let returnObject = {};
            returnObject["treeNode"] = result;
            returnObject["path"] = path;
            return returnObject;
        }
        let defaultStoragePath =  await  this.service.commonService.getSetStoragePath(null);

        const options ={
            properties: ['openFile'],
            //defaultPath: defaultStoragePath.toString(), // 默认保存路径
            defaultPath: defaultStoragePath, // 默认保存路径
            filters: [
                {name: '云算房', extensions:  [ConstantUtil.YUSUAN_FILE_SUFFIX_Z,ConstantUtil.YUSUAN_FILE_SUFFIX_D,ConstantUtil.YUSUAN_FILE_SUFFIX]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)){
            console.log("未选中任何文件");
            return ResponseData.fail("");
        }
        //获取选中的路径
        let filePath = result[0];
        if (!await this.service.constructProjectService.checkFileExistence(filePath)){
            console.log("路径有误");
            return ResponseData.fail('路径有误');
        }
        //导入后的
        let importProjectObj =await PricingFileFindUtils.getProjectObjByPath(filePath);
        await  new Fixs(importProjectObj,importProjectObj.version).fix();
        //12定额标准下 无法上传22定额标准的项目
        if(importProjectObj.deStandardReleaseYear !== projectObjById.deStandardReleaseYear && projectObjById.deStandardReleaseYear == ConstantUtil.DE_STANDARD_12){
            return ResponseData.fail('定额标准不一致，无法导入');
        }

        if((importProjectObj.projectTaxCalculation.taxCalculationMethod+'') !== (projectObjById.projectTaxCalculation.taxCalculationMethod+'')){
            return ResponseData.fail('计税方式不一致，无法导入');
        }
        //导入后的项目临时存放在当前项目中
        if (ObjectUtils.isEmpty(global.multiplexMergeImportProject)) {
            global.multiplexMergeImportProject = {};
        }
        global.multiplexMergeImportProject[constructId] = importProjectObj;
        let importRes = await this.service.constructProjectService.generateLevelTreeNode(importProjectObj);
        //增加文件路径
        let returnObject = {};
        returnObject["treeNode"] = importRes;
        returnObject["path"] = importProjectObj.path;
        return returnObject;
    }

    /**
     * 根据选择的单位工程查询清单定额组价方案
     * containDe true:组价的清单定额 false：组价的清单定额+没有组价的清单
     * @param args
     * @returns {Promise<any[]>}
     */
    async unitProjectQdQuery(args) {
        let {constructId,singleId,unitId,type,searchKey,containDe} = args;
        let constructObj = null;
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        if (type === "history") {
            constructObj = global.multiplexMergeImportProject[constructId];
        }else {
            constructObj = projectObjById;
        }
        let result = new Array();
        let unit = PricingFileFindUtils.getUnitByConstructObj(constructObj,singleId,unitId);
        let fbfx = await this.getRequiredQdDeSchemeByUnit(unit,ConstructionMeasureTypeConstant.FBFX,containDe);
        let djcs = await this.getRequiredQdDeSchemeByUnit(unit,ConstructionMeasureTypeConstant.DJCS,containDe);
        if (ObjectUtils.isNotEmpty(searchKey)) {
            let fbfxNeedQdIds = fbfx.filter(item => item.kind == "03" && ((ObjectUtils.isNotEmpty(item.name) && item.name.includes(searchKey))||(ObjectUtils.isNotEmpty(item.fxCode) && item.fxCode.includes(searchKey))||(ObjectUtils.isNotEmpty(item.projectAttr) && item.projectAttr.includes(searchKey))))
                .map(item => item.sequenceNbr);
            fbfx = fbfx.filter(item => fbfxNeedQdIds.includes(item.sequenceNbr)||fbfxNeedQdIds.includes(item.parentId));
            let djcsNeedQdIds = djcs.filter(item => item.kind == "03" && ((ObjectUtils.isNotEmpty(item.name) && item.name.includes(searchKey))||(ObjectUtils.isNotEmpty(item.fxCode) && item.fxCode.includes(searchKey))||(ObjectUtils.isNotEmpty(item.projectAttr) && item.projectAttr.includes(searchKey))))
                .map(item => item.sequenceNbr);
            djcs = djcs.filter(item => djcsNeedQdIds.includes(item.sequenceNbr)||djcsNeedQdIds.includes(item.parentId));
        }
        //过滤关键字
        //增加分部分项单位工程行
        result.push(unit.itemBillProjects.filter(item => item.kind=="0")[0])
        result.push(fbfx);
        //增加措施项目行
        result.push(unit.measureProjectTables.filter(item => item.kind=="0")[0]);
        result.push(djcs);
        return result;
    }

    /**
     * 批量复用 根据右侧应用的单位工程和匹配条件进行组价
     * is12BitQdCode    不选置false  选择后置true  是否是12位编码
     * is9BitQdCode     不选置false  选择后置true  是否是9位编码
     * isQdName         不选置false  选择后置true
     * isProjectAttr    不选为null 值为"precise" 表示精准  "similar" 表示近似
     * isMerge  false   表示未组价   true表示所有清单
     * constructId      工作台的工程项目id
     * singleId         左侧选中的单位工程对应的单项工程id
     * unitId           左侧选中的单位工程id
     * unitIdsApply     右侧选中的单位id集合
     * type             本工程还是历史工程   "current"/"history"
     * qdDeIds          中间区选中的清单定额id集合
     */
    async unitProjectSelectedMatchMerge(args) {
        let {is12BitQdCode,is9BitQdCode,isQdName,isProjectAttr,isMerge,constructId,singleId,unitId,unitIdsApply,type,qdDeIds} = args;
        //拿到中间选中的清单定额数据
        let qdDesQuery =await this.unitProjectQdQuery({"constructId":constructId,"singleId":singleId,"unitId":unitId,"type":type,"containDe":false});
        let required = (qdDesQuery[1].concat(qdDesQuery[3])).filter(item => qdDeIds.includes(item.sequenceNbr));
        //得到要应用的单位工程  考虑到兼容标准组价的功能
        let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
        let isStandardMerge = ObjectUtils.isNotEmpty(projectObj.bzUnitProject);
        let allUnits = PricingFileFindUtils.getUnitList(constructId);
        if (isStandardMerge) { //如果是标准组价
            allUnits = [projectObj.bzUnitProject];
        }
        let unitList = allUnits.filter(item => unitIdsApply.includes(item.sequenceNbr));
        //根据匹配条件筛选要应用的清单数据
        let qdArray = [];
        for (let i = 0; i < unitList.length; i++) {
            let unitElement = unitList[i];
            let djcsQd =(await this.getUnitDjcsData(unitElement)).filter(item => item.kind =="03" && ObjectUtils.isNotEmpty(item.fxCode)).map(item => {item.moduleType = ConstructionMeasureTypeConstant.DJCS;return item});
            let fbfxQd = unitElement.itemBillProjects.filter(item => item.kind =="03").map(item => {item.moduleType = ConstructionMeasureTypeConstant.FBFX;return item});
            //如果是单位自己复用自己 目标单位的清单只取未组价清单
            let isMergeBakParam = isMerge;
            if (unitId == unitElement.sequenceNbr) {
                isMerge = false;
            }
            if (ObjectUtils.isNotEmpty(isMerge) && !isMerge) { //如果是未组价
                //清单为非空清单并且没有下挂定额
                for (let i = 0; i < djcsQd.length; i++) {
                    let djcsQdElement = djcsQd[i];
                    if (djcsQdElement.isEmpData != -1) {  //清单非空
                        let filter = unitElement.measureProjectTables.filter(item => item.parentId==djcsQdElement.sequenceNbr);
                        if (filter.length == 0) { //没有下挂定额
                            qdArray.push(djcsQdElement);
                        }
                    }
                }
                for (let j = 0; j < fbfxQd.length; j++) {
                    let fbfxQdElement = fbfxQd[j];
                    let filter = unitElement.itemBillProjects.filter(item => item.parentId==fbfxQdElement.sequenceNbr);
                    if (filter.length == 0) { //没有下挂定额
                        qdArray.push(fbfxQdElement);
                    }
                }
            }else { //匹配所有清单
                qdArray.push(...djcsQd);
                qdArray.push(...fbfxQd);
            }
            isMerge = isMergeBakParam;
        }
        //对要应用的清单数据  逐一和中间选择的数据进行匹配
        let successMergeQdNum = 0;
        if (qdArray.length == 0) {
            await IpcWinUtils.childWinSendCount(constructId,0,qdArray.length);//为0也需要给前端推送一下 使监听结束掉
        }
        for (let i = 0; i < qdArray.length; i++) {
            //调用计数接口
            await IpcWinUtils.childWinSendCount(constructId,i+1,qdArray.length);
            let qdElement = qdArray[i];
            let middleMatched = required;
            if (is9BitQdCode && ObjectUtils.isNotEmpty(qdElement.fxCode)) {  //为true表示清单编码一致
                middleMatched = required.filter(item => item.kind=="03" && item.fxCode.substring(0,9)===qdElement.fxCode.substring(0,9));
            }
            if (is12BitQdCode && ObjectUtils.isNotEmpty(qdElement.fxCode)) {
                middleMatched = required.filter(item => item.kind=="03" && item.fxCode===qdElement.fxCode);
            }
            if (isQdName) {
                middleMatched = middleMatched.filter(item => item.kind=="03" && item.name===qdElement.name);
            }
            if (ObjectUtils.isNotEmpty(isProjectAttr)) {
                if (isProjectAttr == "precise") {
                    middleMatched = middleMatched.filter(item => item.kind=="03" && item.projectAttr==qdElement.projectAttr);
                }
                if (isProjectAttr == "similar") {
                    //将匹配的中间清单数据项目特征进行过滤（换行、特殊符号） 同时过滤该条清单的项目特征
                    let qdFiltered = await this.getSpecialSymbolFiltered(qdElement.projectAttr);
                    const asyncFilter = async (array, predicate) => {
                        const results = await Promise.all(array.map(predicate));
                        return array.filter((_, index) => results[index]);
                    };
                    const filterAsync = async (qdItem) => {
                        // 模拟异步操作
                        return qdItem.kind=="03" && await this.getSpecialSymbolFiltered(qdItem.projectAttr)== qdFiltered;
                    };
                    //执行异步过滤
                    middleMatched = await asyncFilter(middleMatched,filterAsync);
                }
            }

            if (middleMatched.length == 0) {
                continue;
            }else if (middleMatched.length >= 1) {  //如果符合条件的引用数据有多组  取第一个
                middleMatched = middleMatched[0];
            }
            successMergeQdNum++;
            //拿到清单其下的定额
            let des = required.filter(item => item.parentId==middleMatched.sequenceNbr);
            let deIdsSelected = des.map(item => item.sequenceNbr);
            //对该条清单进行定额替换
            //获取单项id
            let unit = allUnits.filter(item => item.sequenceNbr===qdElement.unitId)[0];
            if (qdElement.moduleType === ConstructionMeasureTypeConstant.FBFX) {
                let deIds = unit.itemBillProjects.filter(item => item.parentId==qdElement.sequenceNbr).map(item => item.sequenceNbr);
                await this.service.itemBillProjectOptionService.batchDelete(constructId, unit.spId, unit.sequenceNbr, deIds);
                //插入定额
                let args = {
                    "constructId":constructId,
                    "singleId":unit.spId,
                    "unitId":qdElement.unitId,//目标单位工程 id
                    "newUnitId":middleMatched.unitId,//源单位工程
                    "type":type,
                    "sequenceNbrArray":deIdsSelected,//源定额id
                    "pointLine":qdElement,  //复制到哪一行下
                    "dataType":"qdDe",
                    "qdType":"fbfx"         //复制到的目标编辑区
                }
                try {
                    await this.insertQdDe(args);
                } catch (e) {
                    console.error(e);
                }
            }else if (qdElement.moduleType === ConstructionMeasureTypeConstant.DJCS) {
                let deIds = unit.measureProjectTables.filter(item => item.parentId==qdElement.sequenceNbr).map(item => item.sequenceNbr);
                await this.service.stepItemCostService.batchDelete(constructId, unit.spId, unit.sequenceNbr, deIds);
                let args = {
                    "constructId":constructId,
                    "singleId":unit.spId,
                    "unitId":qdElement.unitId,
                    "newUnitId":middleMatched.unitId,
                    "type":type,
                    "sequenceNbrArray":deIdsSelected,
                    "pointLine":qdElement,
                    "dataType":"qdDe",
                    "qdType":"csxm"
                }
                try {
                    await this.insertQdDe(args);
                } catch (e) {
                    console.error(e);
                }
            }
            if (!isStandardMerge){  //不是标准组价的话  要进行计算汇总
                try {
                    await this.service.autoCostMathService.autoCostMath({
                        constructId:unit.constructId,
                        singleId:unit.spId,
                        unitId:unit.sequenceNbr});
                    await this.service.unitCostCodePriceService.countCostCodePrice({
                        constructId: unit.constructId,
                        singleId: unit.spId,
                        unitId: unit.sequenceNbr,
                    });
                } catch (e) {
                    console.error(e);
                }
            }
        }
        return {"successMergeQdNum":successMergeQdNum,"notExecute":qdArray.length-successMergeQdNum};
    }

    //逐条应用模块根据清单选中行查询符合条件的清单定额数据
    /**
     * constructId       工作台工程项目id
     * singleId          工作台选中行对应的单项工程id
     * unitId            工作台选中行对应的单位工程id
     * qdId              工作台编辑区   选中行的清单id
     * fbfxOrCsxm        分部分项还是措施项目编辑区  "fbfx"/"csxm"
     * type              本工程还是历史工程   "current"/"history"
     * is12BitQdCode     不选置false  选择后置true  是否是12位编码
     * is9BitQdCode      不选置false  选择后置true  是否是9位编码
     * isQdName          不选置false
     * isProjectAttr     不选为null  值为"precise" 表示精准  "similar" 表示近似
     * searchKey         输入关键字
     * selectHistory     在有缓存的情况下如果主动选择文件 置为true，否则为false
     */
    async matchQueryBySelected(args) {
        let {constructId,singleId,unitId,qdId,fbfxOrCsxm,type,is12BitQdCode,is9BitQdCode,isQdName,isProjectAttr,searchKey,selectHistory} = args;
        if (ObjectUtils.isEmpty(qdId)) return {"data":[[],[]] };
        //拿到选中行的清单
        let qd = {};
        if (fbfxOrCsxm == "fbfx") {
            qd = PricingFileFindUtils.getUnit(constructId,singleId,unitId).itemBillProjects.filter(item => item.sequenceNbr == qdId)[0];
        }else if (fbfxOrCsxm == "csxm") {
            qd = PricingFileFindUtils.getUnit(constructId,singleId,unitId).measureProjectTables.filter(item => item.sequenceNbr == qdId)[0];
        }

        let constructObj = null;
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        if (type == "history") { //如果是历史工程
            if (ObjectUtils.isEmpty(global.multiplexMergeImportProject)) {
                global.multiplexMergeImportProject = {};
            }

            if (selectHistory || ObjectUtils.isEmpty(global.multiplexMergeImportProject[constructId])) {  //缓存数据为空打开弹框去上传解析
                let defaultStoragePath =  await  this.service.commonService.getSetStoragePath(null);

                const options ={
                    properties: ['openFile'],
                    //defaultPath: defaultStoragePath.toString(), // 默认保存路径
                    defaultPath: defaultStoragePath, // 默认保存路径
                    filters: [
                        {name: '云算房', extensions:[ConstantUtil.YUSUAN_FILE_SUFFIX_Z,ConstantUtil.YUSUAN_FILE_SUFFIX_D,ConstantUtil.YUSUAN_FILE_SUFFIX]} // 可选的文件类型
                    ]
                };
                let result = dialog.showOpenDialogSync(null, options);
                if (ObjectUtils.isEmpty(result)){
                    console.log("未选中任何文件");
                    return ResponseData.fail("");
                }
                //获取选中的路径
                let filePath = result[0];
                if (!await this.service.constructProjectService.checkFileExistence(filePath)){
                    console.log("路径有误");
                    return ResponseData.fail('路径有误');
                }
                //导入后的
                let importProjectObj =await PricingFileFindUtils.getProjectObjByPath(filePath);
                await  new Fixs(importProjectObj,importProjectObj.version).fix();
                //12定额标准下 无法上传22定额标准的项目
                if(importProjectObj.deStandardReleaseYear !== projectObjById.deStandardReleaseYear && projectObjById.deStandardReleaseYear == ConstantUtil.DE_STANDARD_12){
                    return ResponseData.fail('定额标准不一致，无法导入');
                }

                if((importProjectObj.projectTaxCalculation.taxCalculationMethod+'') !== (projectObjById.projectTaxCalculation.taxCalculationMethod+'')){
                    return ResponseData.fail('计税方式不一致，无法导入');
                }
                //导入后的项目临时存放在当前项目中
                global.multiplexMergeImportProject[constructId] = importProjectObj;
            }
            constructObj = global.multiplexMergeImportProject[constructId];
        }else {
            constructObj = PricingFileFindUtils.getProjectObjById(constructId);
        }
        //拿到所有单位
        let unitList = PricingFileFindUtils.getUnitListByConstructObj(constructObj);
        let array = new Array();
        let fbfxArray = new Array();
        let djcsArray = new Array();
        for (let i = 0; i < unitList.length; i++) {
            let fbfxList = await this.getRequiredQdDeSchemeByUnit(unitList[i],ConstructionMeasureTypeConstant.FBFX,true);
            fbfxArray.push(...(_.cloneDeep(fbfxList)));
            let djcsList = await this.getRequiredQdDeSchemeByUnit(unitList[i],ConstructionMeasureTypeConstant.DJCS,true);
            djcsArray.push(...(_.cloneDeep(djcsList)));
        }
        //过滤匹配条件
        let fbfxArrayNeedIds = [];
        let djcsArrayNeedIds = [];
        //先把自己过滤掉
        if (fbfxOrCsxm == "fbfx") {
            fbfxArrayNeedIds = fbfxArray.filter(item => (item.kind=="03" && item.sequenceNbr!=qd.sequenceNbr)).map(item => item.sequenceNbr);
            fbfxArray = fbfxArray.filter(item => fbfxArrayNeedIds.includes(item.sequenceNbr)||fbfxArrayNeedIds.includes(item.parentId));
        }else if (fbfxOrCsxm == "csxm") {
            djcsArrayNeedIds = djcsArray.filter(item => (item.kind=="03" && item.sequenceNbr!=qd.sequenceNbr)).map(item => item.sequenceNbr);
            djcsArray = djcsArray.filter(item => djcsArrayNeedIds.includes(item.sequenceNbr)||djcsArrayNeedIds.includes(item.parentId));
        }
        if (is9BitQdCode && ObjectUtils.isNotEmpty(qd.fxCode)) {  //为true表示清单编码一致
            fbfxArrayNeedIds = fbfxArray.filter(item => (item.kind=="03" && item.fxCode.substring(0,9)===qd.fxCode.substring(0,9))).map(item => item.sequenceNbr);
            fbfxArray = fbfxArray.filter(item => fbfxArrayNeedIds.includes(item.sequenceNbr)||fbfxArrayNeedIds.includes(item.parentId));
            djcsArrayNeedIds = djcsArray.filter(item => (item.kind=="03" && item.fxCode.substring(0,9)===qd.fxCode.substring(0,9))).map(item => item.sequenceNbr);
            djcsArray = djcsArray.filter(item => djcsArrayNeedIds.includes(item.sequenceNbr)||djcsArrayNeedIds.includes(item.parentId));
        }
        if (is12BitQdCode) {
            fbfxArrayNeedIds = fbfxArray.filter(item => (item.kind=="03" && item.fxCode===qd.fxCode)).map(item => item.sequenceNbr);
            fbfxArray = fbfxArray.filter(item => fbfxArrayNeedIds.includes(item.sequenceNbr)||fbfxArrayNeedIds.includes(item.parentId));
            djcsArrayNeedIds = djcsArray.filter(item => (item.kind=="03" && item.fxCode===qd.fxCode)).map(item => item.sequenceNbr);
            djcsArray = djcsArray.filter(item => djcsArrayNeedIds.includes(item.sequenceNbr)||djcsArrayNeedIds.includes(item.parentId));
        }
        if (isQdName) {
            fbfxArrayNeedIds = fbfxArray.filter(item => (item.kind=="03" && item.name===qd.name)).map(item => item.sequenceNbr);
            fbfxArray = fbfxArray.filter(item => fbfxArrayNeedIds.includes(item.sequenceNbr)||fbfxArrayNeedIds.includes(item.parentId));
            djcsArrayNeedIds = djcsArray.filter(item => (item.kind=="03" && item.name===qd.name)).map(item => item.sequenceNbr);
            djcsArray = djcsArray.filter(item => djcsArrayNeedIds.includes(item.sequenceNbr)||djcsArrayNeedIds.includes(item.parentId));
        }
        if (ObjectUtils.isNotEmpty(isProjectAttr)) {
            if (isProjectAttr == "precise") {
                fbfxArrayNeedIds = fbfxArray.filter(item => (item.kind=="03" && item.projectAttr==qd.projectAttr)).map(item => item.sequenceNbr);
                fbfxArray = fbfxArray.filter(item => fbfxArrayNeedIds.includes(item.sequenceNbr)||fbfxArrayNeedIds.includes(item.parentId));
                djcsArrayNeedIds = djcsArray.filter(item => (item.kind=="03" && item.projectAttr==qd.projectAttr)).map(item => item.sequenceNbr);
                djcsArray = djcsArray.filter(item => djcsArrayNeedIds.includes(item.sequenceNbr)||djcsArrayNeedIds.includes(item.parentId));
            }
            if (isProjectAttr == "similar") {
                //将匹配的中间清单数据项目特征进行过滤（换行、特殊符号） 同时过滤该条清单的项目特征
                let qdFiltered = await this.getSpecialSymbolFiltered(qd.projectAttr);
                const asyncFilter = async (array, predicate) => {
                    const results = await Promise.all(array.map(predicate));
                    return array.filter((_, index) => results[index]);
                };
                const filterAsync = async (qdItem) => {
                    // 模拟异步操作
                    return qdItem.kind=="03" && await this.getSpecialSymbolFiltered(qdItem.projectAttr)== qdFiltered;
                };
                //执行异步过滤
                fbfxArrayNeedIds = (await asyncFilter(fbfxArray,filterAsync)).map(item => item.sequenceNbr);
                fbfxArray = fbfxArray.filter(item => fbfxArrayNeedIds.includes(item.sequenceNbr)||fbfxArrayNeedIds.includes(item.parentId));
                djcsArrayNeedIds = (await asyncFilter(djcsArray,filterAsync)).map(item => item.sequenceNbr);
                djcsArray = djcsArray.filter(item => djcsArrayNeedIds.includes(item.sequenceNbr)||djcsArrayNeedIds.includes(item.parentId));
            }
        }
        //搜索匹配  编码 名称  项目特征
        if (ObjectUtils.isNotEmpty(searchKey)) {
            fbfxArrayNeedIds = fbfxArray.filter(item => item.fxCode.includes(searchKey)|| item.name.includes(searchKey)||item.projectAttr.includes(searchKey)).map(item => item.sequenceNbr);
            fbfxArray = fbfxArray.filter(item => fbfxArrayNeedIds.includes(item.sequenceNbr)||fbfxArrayNeedIds.includes(item.parentId));
            djcsArrayNeedIds = djcsArray.filter(item => item.fxCode.includes(searchKey)|| item.name.includes(searchKey)||item.projectAttr.includes(searchKey)).map(item => item.sequenceNbr);
            djcsArray = djcsArray.filter(item => djcsArrayNeedIds.includes(item.sequenceNbr)||djcsArrayNeedIds.includes(item.parentId));
        }
        //赋值单位工程归属
        for (let i = 0; i < fbfxArray.length; i++) {
            if (fbfxArray[i].kind == "03") {
                let arrayElement = fbfxArray[i];
                //拿到清单对应单位
                let unit = unitList.filter(item => item.sequenceNbr == arrayElement.unitId)[0];
                let array = [];
                try {
                    if (2 == constructObj.biddingType) {  //如果工程项目是单位工程
                        array.push(unit.upName);
                    }else {
                        await this.getQdProjectPath(unit.sequenceNbr, constructObj, null, array);
                    }
                    await this.getQdEditRegionPath(treeToArray(unit.itemBillProjects),arrayElement,array);
                } catch (e) {
                }
                //拼接归属路径
                let belong = "";
                for (let j = 0; j < array.length-1; j++) {
                    belong+=array[j]+"/"
                }
                belong+=array[array.length-1];
                arrayElement.belongsPath = belong;
            }
        }
        for (let i = 0; i < djcsArray.length; i++) {
            if (djcsArray[i].kind == "03") {
                let arrayElement = djcsArray[i];
                let unit = unitList.filter(item => item.sequenceNbr == arrayElement.unitId)[0];
                let array = [];
                try {
                    if (2 == constructObj.biddingType) {  //如果工程项目是单位工程
                        array.push(unit.upName);
                    }else {
                        await this.getQdProjectPath(unit.sequenceNbr, constructObj, null, array);
                    }
                    await this.getQdEditRegionPath(treeToArray(unit.measureProjectTables),arrayElement,array);
                } catch (e) {
                }
                //拼接归属路径
                let belong = "";
                for (let j = 0; j < array.length-1; j++) {
                    belong+=array[j]+"/"
                }
                belong+=array[array.length-1];
                arrayElement.belongsPath = belong;
            }
        }
        array.push(fbfxArray);
        array.push(djcsArray);

        //增加文件路径
        let returnObject = {};
        returnObject["data"] = array;
        if (type == "history") {
            returnObject["path"] = constructObj.path;
        }
        return returnObject;
    }

    //逐条应用模块进行添加组价 或替换组价
    async addOrReplaceMerge(args) {
        let {constructId,singleId,unitId,qdId,fbfxOrCsxm,qdDeIds,addOrReplace,type} = args;
        //选中行对应的单位工程
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        //拿到选中行的清单
        let qd = {};
        if (fbfxOrCsxm == "fbfx") {
            qd = PricingFileFindUtils.getUnit(constructId,singleId,unitId).itemBillProjects.filter(item => item.sequenceNbr == qdId)[0];
        } else if (fbfxOrCsxm == "csxm") {
            qd = PricingFileFindUtils.getUnit(constructId,singleId,unitId).measureProjectTables.filter(item => item.sequenceNbr == qdId)[0];
        }

        let constructObj = null;
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        if (type === "history") {
            constructObj = global.multiplexMergeImportProject[constructId];
        }else {
            constructObj = projectObjById;
        }
        //拿到弹框选中的定额集合
        let allUnits = PricingFileFindUtils.getUnitListByConstructObj(constructObj);
        let selectedDes = [];
        let unitWant = {};//选中组价方案对应的单位工程
        for (let i = 0; i < allUnits.length; i++) {
            let unit = allUnits[i];
            let measures = unit.measureProjectTables.filter(item => qdDeIds.includes(item.sequenceNbr) && item.kind=="04");
            let items = unit.itemBillProjects.filter(item => qdDeIds.includes(item.sequenceNbr) && item.kind=="04");
            selectedDes.push(...measures);
            selectedDes.push(...items);
            if (selectedDes.length > 0) {
                unitWant = unit;
                break;
            }
        }

        let deIdsSelected = selectedDes.map(item => item.sequenceNbr);
        if (addOrReplace == "replace") { //如果是替换  先删除原来的定额
            let deIds = unit.itemBillProjects.filter(item => item.parentId==qd.sequenceNbr).map(item => item.sequenceNbr);
            await this.service.itemBillProjectOptionService.batchDelete(constructId, singleId, unitId, deIds);
        }
        //走复制逻辑
        let argsCopy = {
            "constructId":constructId,
            "singleId":singleId,
            "unitId":unitId,
            "newUnitId":unitWant.sequenceNbr,
            "type":type,
            "sequenceNbrArray":deIdsSelected,
            "pointLine":qd,
            "dataType":"qdDe",
            "qdType":fbfxOrCsxm
        }
        await this.insertQdDe(argsCopy);
        try {
            await this.service.autoCostMathService.autoCostMath({
                constructId:constructId,
                singleId:singleId,
                unitId:unitId});
            await this.service.unitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
            });
        } catch (e) {
            console.error(e);
        }
    }

    async getUnitDjcsData(unitProject) {
        let filter = unitProject.measureProjectTables.filter(item=>!ObjectUtils.isEmpty(item.constructionMeasureType) && item.constructionMeasureType === ConstructionMeasureTypeConstant.DJCS);
        let djcsQdAndDe = [];
        if (filter.length > 0) {
            for (let i = 0; i < filter.length; i++) {

                let measureProjectTables;
                if(Array.isArray(unitProject.measureProjectTables)){
                    measureProjectTables = unitProject.measureProjectTables;
                }else {
                    measureProjectTables = treeToArray(unitProject.measureProjectTables)
                }
                let djcsData = this.service.exportQueryService.getCSXMQdAndDe(measureProjectTables, filter[i], "").datas.filter(item => (item.constructionMeasureType != ConstructionMeasureTypeConstant.DJCS));
                djcsQdAndDe.push(...djcsData);
            }
        }
        return djcsQdAndDe;
    }

    //判断清单是否下挂定额
    judgeQdContainDe(qd,allDatas) {
        for (let i = 0; i < allDatas.length; i++) {
            let data = allDatas[i];
            if (data.parentId == qd.sequenceNbr) {
                return true;
            }
        }
        return false;
    }

    //拿到单位下符合要求的清单定额组价方案
    async getRequiredQdDeSchemeByUnit(unit,type,containDe) {
        if (type == ConstructionMeasureTypeConstant.DJCS) {
            let djcsQdDe = (await this.getUnitDjcsData(unit)).filter(item => item.kind === BranchProjectLevelConstant.qd ||
                (item.kind === BranchProjectLevelConstant.de && item.isCostDe==DePropertyTypeConstant.NON_COST_DE));
            let notRequireDjcsQd;
            if(containDe){
                //过滤空清单(没有编码) 及没有定额的清单
                notRequireDjcsQd = djcsQdDe.filter(item => (item.kind=="03" && ObjectUtils.isEmpty(item.fxCode)) || (item.kind=="03" && !this.judgeQdContainDe(item,djcsQdDe))).map(item => item.sequenceNbr);
            }else {
                ////过滤空清单(没有编码)
                notRequireDjcsQd = djcsQdDe.filter(item => (item.kind=="03" && ObjectUtils.isEmpty(item.fxCode)) ).map(item => item.sequenceNbr);
            }

            djcsQdDe = djcsQdDe.filter(item => !(notRequireDjcsQd.includes(item.sequenceNbr)||notRequireDjcsQd.includes(item.parentId)));
            //过滤空定额
            djcsQdDe = djcsQdDe.filter(item => !(item.kind == "04" && ObjectUtils.isEmpty(item.fxCode)));
            return djcsQdDe;
        }
        if (type == ConstructionMeasureTypeConstant.FBFX) {
            let fbfxQdDe = unit.itemBillProjects.filter(item => item.kind === BranchProjectLevelConstant.qd ||
                (item.kind === BranchProjectLevelConstant.de && item.isCostDe==DePropertyTypeConstant.NON_COST_DE)
            );
            let notRequireFbfxQd ;
            if(containDe){
                //过滤空清单(没有编码) 及没有定额的清单
                notRequireFbfxQd = fbfxQdDe.filter(item => (item.kind=="03" && ObjectUtils.isEmpty(item.fxCode)) || (item.kind=="03" && !this.judgeQdContainDe(item,fbfxQdDe))).map(item => item.sequenceNbr);
            }else {
                notRequireFbfxQd = fbfxQdDe.filter(item => (item.kind=="03" && ObjectUtils.isEmpty(item.fxCode)) ).map(item => item.sequenceNbr);
            }

            fbfxQdDe = fbfxQdDe.filter(item => !(notRequireFbfxQd.includes(item.sequenceNbr)||notRequireFbfxQd.includes(item.parentId)));
            //过滤空定额
            fbfxQdDe = fbfxQdDe.filter(item => !(item.kind == "04" && ObjectUtils.isEmpty(item.fxCode)));
            return fbfxQdDe;
        }
        return [];
    }



    /**
     * 插入数据（批量提取已有清单）
     * @param args
     * type 当前项目/历史文件
     * sequenceNbrArray 页面选择的清单定额sequenceNbrArray
     * pointLine 选中行
     * dataType  (qd/qdDe)       清单/清单+定额
     * constructId, singleId, unitId 选中清单对应的工作台主键
     * newUnitId 弹窗中 中间清单数据对应的单位工程
     * @returns {Promise<void>}
     */
    async insertQdDe(args){
        let {constructId, singleId, unitId, newUnitId,type,sequenceNbrArray,pointLine,dataType,qdType} = args;
        let projectObjById =await PricingFileFindUtils.getProjectObjById(constructId);
        let constructObj = null;
        if (type === "history") {
            //内存中获取上传文件
            constructObj = global.multiplexMergeImportProject[constructId];
        }else {
            constructObj = projectObjById;
        }
        let constructProject = _.cloneDeep(constructObj)
        let unitLists =await PricingFileFindUtils.getUnitListByConstructObj(constructProject);



        //根据选择页面选择的清单定额主键筛选

        let coverData = {
            itemBillProjects: [],//分部分项
            measureProjectTables: [],//措施项目
            otherProjects: [],//其他项目
            unitCostSummarys: []
        }
        let unit =  unitLists.find(i =>i.sequenceNbr == newUnitId );

        if( dataType ==='qd'){
            //过滤定额
            unit.itemBillProjects =unit.itemBillProjects.filter(i =>i.kind !=='04')
            unit.measureProjectTables =unit.measureProjectTables.filter(i =>i.kind !=='04')
        }
        let filterItemBillProjects =unit.itemBillProjects.filter(obj =>sequenceNbrArray.includes(obj.sequenceNbr))

        //拼接单价措施清单定额数据
        let filterMeasureProjectTables =unit.measureProjectTables.filter(obj =>sequenceNbrArray.includes(obj.sequenceNbr))

        let filterArray = [...filterItemBillProjects,...filterMeasureProjectTables]

        //如果当前工程是22定额工程项目  引用的是12定额标准的定额 需要对定额的相关属性进行重新赋值
        let de22Standard = (PricingFileFindUtils.getConstructDeStandard(constructId)== ConstantUtil.DE_STANDARD_22);
        //判断复用的工程项目是否为22定额标准的
        let historyProjectIs22 = (constructObj.deStandardReleaseYear=="22");
        //拿到历史工程项目
        if (de22Standard && type == "history" && !historyProjectIs22) {
            //对复用的定额进行处理
            let filterDes = filterArray.filter(item => item.kind=="04");
            for (let i = 0; i < filterDes.length; i++) {
                let de = filterDes[i];
                let baseQdOrDe = await this.service.baseQdDeProcess.selectBaseQdOrDe(de.standardId, BranchProjectLevelConstant.de, historyProjectIs22);
                let cslbCode = baseQdOrDe.cslbCode;
                //12和22d的映射
                cslbCode = get2022BY2012Cslb(cslbCode);
                let baseFeeFileRelationDTO = await this.service.baseFeeFileRelationService.queryFeeFileRelationByRateCodeGroupByQfCode(cslbCode, de22Standard);
                let unitFeeFileDTO = await this.service.baseFeeFileService.handleUnitAddFeeFile(constructId, singleId, unitId, baseQdOrDe.sequenceNbr, historyProjectIs22);
                //对12的原有定额的相关属性进行重新赋值
                de.qfName = unitFeeFileDTO ? unitFeeFileDTO.feeFileName : "随主工程";
                de.costMajorName = unitFeeFileDTO ? unitFeeFileDTO.feeFileName : "随主工程";
                de.rateName = baseFeeFileRelationDTO ? baseFeeFileRelationDTO.cslbName : "随主工程"
                de.measureType = baseFeeFileRelationDTO ? baseFeeFileRelationDTO.cslbName : "随主工程";
                de.costFileCode = unitFeeFileDTO ? unitFeeFileDTO.costFileCode:"";
                de.feeFileId = unitFeeFileDTO ? unitFeeFileDTO.feeFileId:"";
                de.qfCode = unitFeeFileDTO ? unitFeeFileDTO.qfCode:"";

                //同时处理原来12定额工程的取费文件  适配为当前的22工程
                unit.feeFiles = [];
                unit.feeFiles.push(unitFeeFileDTO);
            }
        }

        let filterArrayKey = _.map(filterArray, (item) => {
            return item.sequenceNbr;
        });
        for (let i = 0; i < filterArray.length; i++) {
            let item = filterArray[i];
            //如果是 HSGCL 额外处理
            if (item.quantityExpression == "HSGCL" && !_.includes(filterArrayKey, item.relationDeId)) {
                item.quantityExpression = item.quantityExpressionNbr + "";
            }
        }
        //filterArray 根据传参只会有一个数组 所以这里两个都进行赋值不是很合适
        coverData.itemBillProjects = filterArray
        coverData.measureProjectTables = filterArray
        // PricingFileFindUtils.setInsertQdDe(Object.assign({}, unit, coverData));

        let copeUnitProject = Object.assign({}, unit, coverData);
        //取消源单位工程的主取费文件标识
        for (let i = 0; i < copeUnitProject.feeFiles.length; i++) {
            let feeFile = copeUnitProject.feeFiles[i];
            if (feeFile.defaultFeeFlag == 1) {
                feeFile.defaultFeeFlag = 0;
            }
        }
        if(qdType=='csxm'){
            //调用插入新增
            await this.service.stepItemCostService.addDataByQdDe(copeUnitProject,constructId, singleId, unitId,pointLine)
        }else {
            await this.service.itemBillProjectOptionService.addDataByQdDe(copeUnitProject,constructId, singleId, unitId,pointLine)
        }

        //编码重刷
        // await this.service.commonService.batchRefresh(args); xhc让去掉

    }

    //获取清单项的工程归属路径   递归定义:遍历到指定的单位后返回true
    async getQdProjectPath(unitId,construct,singleProject,array) {
        if (ObjectUtils.isNotEmpty(construct)) {
            for (let i = 0; i < construct.singleProjects.length; i++) {
                let singleProject = construct.singleProjects[i];
                array.push(singleProject.projectName);
                let result = await this.getQdProjectPath(unitId,null,singleProject,array);
                if (!result) {
                    array.pop();
                }else {
                    return ;
                }
            }
        }
        if (ObjectUtils.isNotEmpty(singleProject.subSingleProjects) && singleProject.subSingleProjects.length>0) {
            for (let j = 0; j < singleProject.subSingleProjects.length; j++) {
                array.push(singleProject.subSingleProjects[j].projectName);
                let result = await this.getQdProjectPath(unitId,null,singleProject.subSingleProjects[j],array);
                if (result) {
                    return result;
                }else {
                    array.pop();
                }
            }
        }
        if (ObjectUtils.isNotEmpty(singleProject.unitProjects) && singleProject.unitProjects.length > 0) {
            for (let j = 0; j < singleProject.unitProjects.length; j++) {
                let unitProject = singleProject.unitProjects[j];
                if (unitProject.sequenceNbr == unitId) {
                    array.push(unitProject.upName);
                    return true;
                }
            }
        }
        return false;
    }

    //获取清单编辑区的归属路径
    async getQdEditRegionPath(allData,qd,array) {
        for (let i = 0; i < allData.length; i++) {
            if (qd.parentId == allData[i].sequenceNbr) {
                await this.getQdEditRegionPath(allData,allData[i],array);
                array.push(allData[i].name);//放入父级名称
            }
        }
    }

    async getSpecialSymbolFiltered(str) {
        let special = "\t,\r,\n, ,`,~,!,@,#,$,%,^,&,*,(,),-,_,=,+,[,],{,},;,:,',\",\,|,,,<,>,/,?,~,！,@,#,￥,%,……,&,*,（,）,——,-,+,=,【,】,|,、,：,；,’,“,《,》,/,？";
        let strings = special.split(",");
        if (ObjectUtils.isNotEmpty(str)) {
            for (let i = 0; i < strings.length; i++) {
                let string = strings[i];
                str = str.replace(string,"");
            }
        }
        return str;
    }


}

MultiplexMergeService.toString = () => '[class MultiplexMergeService]';
module.exports = MultiplexMergeService;
