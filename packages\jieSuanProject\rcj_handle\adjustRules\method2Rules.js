const {Organ, Gene} = require("@valuation/rules-engine");
const {JieSuanRcjStageUtils} = require("../../utils/JieSuanRcjStageUtils");
const {PricingFileFindUtils} = require("../../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../../electron/utils/ObjectUtils");
const {NumberUtil} = require("../../../../common/NumberUtil");
const JieSuanRcjDifferenceEnum = require("../../enum/JieSuanRcjDifferenceEnum");
const {baseRules, getjieSuanStageDifferenceQuantityRule, getjieSuanBasePrice, getjieSuanPrice,
	getSettlementPriceDifferencInputTax, getmarketPrice, getBasePriceFloatRate, gettaxRemoval,
	getpriceMarketTax,
	getpriceMarket, getjieSuanPriceSource, getJieSuanPriceDifferencSum, getjxTotal, getjieSuanJxTotal, gettotal
} = require("./baseRules");

//结算价与基期价差额法

//单位价差
let getjieSuanPriceDifferenc=(seq)=>{
	return Organ.create({name:seq+"_jieSuanPriceDifferenc",description:"单位价差",gene:Gene.from([seq+"_DWJC_ZDF"],(ctx)=>{
			return ctx[seq+"_DWJC_ZDF"].jieSuanPriceDifferenc;
		})})
};
//涨跌幅以及单位价差计算
let getDWJCZDF=(seq)=>{
	return Organ.create({name:seq+"_DWJC_ZDF",description:"涨跌幅以及单位价差计算",gene:Gene.from(["CONTEXT",seq+"_marketPrice"],
			(ctx)=>{
				const result = {
					jieSuanPriceDifferenc:0,
					jieSuanPriceLimit:0
				};
				let {rcj} = ctx.CONTEXT;
				if (rcj.ifProvisionalEstimate == 1 || rcj.ifDonorMaterial ==1){
					//暂估价调差无调差方法及风险范围，其单位价差=结算单价-合同价
					result.jieSuanPriceDifferenc =NumberUtil.subtract(ctx[seq+"_marketPrice"],rcj.jieSuanMarketPrice);
					return result;
				}

				// 		1. 单位价差计算方法：
				// 		a. 涨幅：（结算价-基期价）/基期价＞5%，单位价差=结算价-基期价*（1+5%)
				// 		b. 跌幅：（结算价-基期价）/基期价＜-5%，单位价差=结算价-基期价*（1-5%)
				// 		c. 不符合前两种情况的，其单位价差=0
				if (NumberUtil.divide(NumberUtil.subtract(ctx[seq+"_marketPrice"], rcj.jieSuanBasePrice), rcj.jieSuanBasePrice) > NumberUtil.divide(rcj.riskAmplitudeRangeMax,100)) {
					result.jieSuanPriceDifferenc = NumberUtil.subtract(ctx[seq+"_marketPrice"], NumberUtil.multiply(rcj.jieSuanBasePrice, NumberUtil.add(1 , NumberUtil.divide(rcj.riskAmplitudeRangeMax,100))));
					result.jieSuanPriceLimit=NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(ctx[seq+"_marketPrice"],rcj.jieSuanBasePrice),rcj.jieSuanBasePrice),100);

				} else if (NumberUtil.divide(NumberUtil.subtract(ctx[seq+"_marketPrice"], rcj.jieSuanBasePrice), rcj.jieSuanBasePrice) <NumberUtil.divide(rcj.riskAmplitudeRangeMin,100)) {
					result.jieSuanPriceDifferenc = NumberUtil.subtract(ctx[seq+"_marketPrice"], NumberUtil.multiply(rcj.jieSuanBasePrice, NumberUtil.add(1 , NumberUtil.divide(rcj.riskAmplitudeRangeMin,100))));
					result.jieSuanPriceLimit=NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(ctx[seq+"_marketPrice"], rcj.jieSuanBasePrice), rcj.jieSuanBasePrice),100);
				}
				return result;
			})});
};
//涨跌幅
let getjieSuanPriceLimit=(seq)=>{
	return Organ.create({name:seq+"_jieSuanPriceLimit",description:"涨跌幅",gene:Gene.from([seq+"_DWJC_ZDF"],
			(cxt)=>{
				return cxt[seq+"_DWJC_ZDF"].jieSuanPriceLimit;
			})})
}
let  calculateRules = {
	jieSuanPriceDifferencSum:getJieSuanPriceDifferencSum,
	jieSuanStageDifferenceQuantity:getjieSuanStageDifferenceQuantityRule,
	jieSuanPriceDifferenc:getjieSuanPriceDifferenc,
	jieSuanPriceLimit:getjieSuanPriceLimit,
	DWJC_ZDF:getDWJCZDF,
	settlementPriceDifferencInputTax:getSettlementPriceDifferencInputTax,
	basePriceFloatRate:getBasePriceFloatRate,
	jieSuanBasePrice:getjieSuanBasePrice,
	marketPrice:getmarketPrice,
	taxRemoval:gettaxRemoval,
	priceMarketTax:getpriceMarketTax,
	priceMarket:getpriceMarket,
	jieSuanPriceSource:getjieSuanPriceSource,
	// jieSuanJxTotal:getjieSuanJxTotal,
	// jxTotal:getjxTotal,
	// total:gettotal,
	//jieSuanTaxRemoval:getjieSuanTaxRemoval,

};

//字段
let method2RulesField_12 = {
	"DWJC_ZDF":2,
	"jieSuanStageDifferenceQuantity":4,//调差工程量
	//"jieSuanBasePrice":2,// 	基期价
	// 	基期价来源
	"basePriceFloatRate":2,//基期价浮动率(%)
	"marketPrice":2,// 结算单价
	"jieSuanPriceSource":2,// 结算单价来源
	// 风险幅度范围(%)
	"jieSuanPriceLimit":2,// 单价涨/跌幅(%)
	"jieSuanPriceDifferenc":2,// 单位价差
	"jieSuanPriceDifferencSum":2,	// 价差合计
	// 结算除税系数(%)
	"settlementPriceDifferencInputTax":2,// 结算价差进项税额
	"priceMarketTax":2,//第n期含税单价
	"priceMarket":2,//第n期不含税单价
	// 取费
	//备注
}












/**
 * 根据类型获取到具体的列表数据
 */
let getMethod2Rule=(ctx)=>{

	let {kind} = ctx.arg;

	let {is2022} = ctx;
	let rulesField = method2RulesField_12;
	// if (is2022){
	// 	rulesField = method2RulesField_22;
	// }
	ctx.calculateField = rulesField;
	ctx.rules = {...baseRules};
	ctx.calculateRules = calculateRules;

}


module.exports = {getMethod2Rule};
