'use strict';

const {Service} = require('../../../core');
const {BaseRcj} = require("../../model/BaseRcj");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const {ArrayUtil} = require("../../utils/ArrayUtil");
const {In} = require("typeorm");
const RcjTypeEnum = require("../../enum/RcjTypeEnum");
const {SqlUtils} = require("../../utils/SqlUtils");

/**
 * base人材机 service
 */
class BaseRcjService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    baseRcjDao = this.app.appDataSource.manager.getRepository(BaseRcj);

    /**
     * 人材机目录树
     * @param libraryCode 定额册编码
     */
    async listTreeByLibraryCode(libraryCode) {
        /*// 自测参数
        libraryCode = "2012-JZGC-DEY";*/
        // console.time("查库1");
        // let rcjList = await this.baseRcjDao.findBy({libraryCode: libraryCode});
        // console.timeEnd("查库1");
        let selectSql = "SELECT\n" +
            "\tsequence_nbr sequenceNbr,\n" +
            "\tlevel1,\n" +
            "\tlevel2,\n" +
            "\tlevel3,\n" +
            "\tlevel4,\n" +
            "\tlevel5,\n" +
            "\tlevel6,\n" +
            "\tlevel7,\n" +
            "\tsort_no as sortNo \n" +
            "FROM\n" +
            "\t\"base_rcj\" \n" +
            "WHERE\n" +
            "\tlibrary_code = ?" +
            " ORDER BY sort_no;";
        let rcjList = this.app.betterSqlite3DataSource.prepare(selectSql).all(libraryCode);
        rcjList.sort((a, b) => a.sortNo - b.sortNo);
        if (ObjectUtils.isEmpty(rcjList)) {
            return [];
        }

        // 将人材机数据分层转为树结构
        let resultTree = [];
        let sequenceNbr = 1;
        let groupLevel1Map = ArrayUtil.group(rcjList, "level1");
        groupLevel1Map.forEach((level2List, level1) => {
            if (ObjectUtils.isEmpty(level1)) {
                // 过滤分组后key为null的元素
                return;
            }
            let level1RcjModel = this._packageRcjModel(1, level2List[0], sequenceNbr++);
            resultTree.push(level1RcjModel);
            // level2
            let groupLevel2Map = ArrayUtil.group(level2List, "level2");
            groupLevel2Map.forEach((level3List, level2) => {
                if (ObjectUtils.isEmpty(level2)) {
                    // 过滤分组后key为null的元素
                    return;
                }
                let level2RcjModel = this._packageRcjModel(2, level3List[0], sequenceNbr++);
                level1RcjModel.childrenList.push(level2RcjModel);
                // level3
                let groupLevel3Map = ArrayUtil.group(level3List, "level3");
                groupLevel3Map.forEach((level4List, level3) => {
                    if (ObjectUtils.isEmpty(level3)) {
                        // 过滤分组后key为null的元素
                        return;
                    }
                    let level3RcjModel = this._packageRcjModel(3, level4List[0], sequenceNbr++);
                    level2RcjModel.childrenList.push(level3RcjModel);
                    // level4
                    let groupLevel4Map = ArrayUtil.group(level4List, "level4");
                    groupLevel4Map.forEach((level5List, level4) => {
                        if (ObjectUtils.isEmpty(level4)) {
                            // 过滤分组后key为null的元素
                            return;
                        }
                        let level4RcjModel = this._packageRcjModel(4, level5List[0], sequenceNbr++);
                        level3RcjModel.childrenList.push(level4RcjModel);
                        // level5
                        let groupLevel5Map = ArrayUtil.group(level5List, "level5");
                        groupLevel5Map.forEach((level6List, level5) => {
                            if (ObjectUtils.isEmpty(level5)) {
                                // 过滤分组后key为null的元素
                                return;
                            }
                            let level5RcjModel = this._packageRcjModel(5, level6List[0], sequenceNbr++);
                            level4RcjModel.childrenList.push(level5RcjModel);
                            // level6
                            let groupLevel6Map = ArrayUtil.group(level6List, "level6");
                            groupLevel6Map.forEach((level7List, level6) => {
                                if (ObjectUtils.isEmpty(level6)) {
                                    // 过滤分组后key为null的元素
                                    return;
                                }
                                let level6RcjModel = this._packageRcjModel(6, level7List[0], sequenceNbr++);
                                level5RcjModel.childrenList.push(level6RcjModel);
                                //level7
                                let groupLevel7Map = ArrayUtil.group(level7List, "level7");
                                groupLevel7Map.forEach((crjList, level7) => {
                                    if (ObjectUtils.isEmpty(level7)) {
                                        // 过滤分组后key为null的元素
                                        return;
                                    }
                                    let level7RcjModel = this._packageRcjModel(7, crjList[0], sequenceNbr++);
                                    level6RcjModel.childrenList.push(level7RcjModel);
                                });
                            });
                        });
                    });
                });
            });
        });
        return resultTree;
    }

    /**
     * 组装人材机
     * @param level 1~7
     * @param sourceRcjModel 来源人材机model
     * @param sequenceNbr 唯一
     * @private
     */
    _packageRcjModel(level, sourceRcjModel, sequenceNbr) {
        if (ObjectUtils.isEmpty(level)) {
            throw new Error("level有误");
        }

        let resultRcjModel = new BaseRcj();
        resultRcjModel.sequenceNbr = sequenceNbr;
        // 初始化子集
        resultRcjModel.childrenList = [];
        switch (level) {
            case 1:
                // level1
                resultRcjModel.level1 = sourceRcjModel.level1;
                resultRcjModel.materialName = sourceRcjModel.level1;
                break;
            case 2:
                // level2
                resultRcjModel.level1 = sourceRcjModel.level1;
                resultRcjModel.level2 = sourceRcjModel.level2;
                resultRcjModel.materialName = sourceRcjModel.level2;
                break;
            case 3:
                // level3
                resultRcjModel.level1 = sourceRcjModel.level1;
                resultRcjModel.level2 = sourceRcjModel.level2;
                resultRcjModel.level3 = sourceRcjModel.level3;
                resultRcjModel.materialName = sourceRcjModel.level3;
                break;
            case 4:
                // level4
                resultRcjModel.level1 = sourceRcjModel.level1;
                resultRcjModel.level2 = sourceRcjModel.level2;
                resultRcjModel.level3 = sourceRcjModel.level3;
                resultRcjModel.level4 = sourceRcjModel.level4;
                resultRcjModel.materialName = sourceRcjModel.level4;
                break;
            case 5:
                // level5
                resultRcjModel.level1 = sourceRcjModel.level1;
                resultRcjModel.level2 = sourceRcjModel.level2;
                resultRcjModel.level3 = sourceRcjModel.level3;
                resultRcjModel.level4 = sourceRcjModel.level4;
                resultRcjModel.level5 = sourceRcjModel.level5;
                resultRcjModel.materialName = sourceRcjModel.level5;
                break;
            case 6:
                // level6
                resultRcjModel.level1 = sourceRcjModel.level1;
                resultRcjModel.level2 = sourceRcjModel.level2;
                resultRcjModel.level3 = sourceRcjModel.level3;
                resultRcjModel.level4 = sourceRcjModel.level4;
                resultRcjModel.level5 = sourceRcjModel.level5;
                resultRcjModel.level6 = sourceRcjModel.level6;
                resultRcjModel.materialName = sourceRcjModel.level6;
                break;
            case 7:
                // level7
                resultRcjModel.level1 = sourceRcjModel.level1;
                resultRcjModel.level2 = sourceRcjModel.level2;
                resultRcjModel.level3 = sourceRcjModel.level3;
                resultRcjModel.level4 = sourceRcjModel.level4;
                resultRcjModel.level5 = sourceRcjModel.level5;
                resultRcjModel.level6 = sourceRcjModel.level6;
                resultRcjModel.level7 = sourceRcjModel.level7;
                resultRcjModel.materialName = sourceRcjModel.level7;
                break;
            default:
                // ...
                break;
        }
        return resultRcjModel;
    }

    listLike2(baseRcjModel, page, limit) {
        let sql = "select " +
            "material_code = ? as materialCode,\n" +
            "material_name = ? as materialName,\n" +
            "       sequence_nbr,\n" +
            "       level1,\n" +
            "       level2,\n" +
            "       level3,\n" +
            "       level4,\n" +
            "       level5,\n" +
            "       level6,\n" +
            "       level7,\n" +
            "       material_code,\n" +
            "       kind,\n" +
            "       material_name,\n" +
            "       specification,\n" +
            "       unit,\n" +
            "       de_price,\n" +
            "       market_price,\n" +
            "       tax_removal,\n" +
            "       library_code,\n" +
            "       level_mark,\n" +
            "       rec_user_code,\n" +
            "       rec_status,\n" +
            "       rec_date,\n" +
            "       extend1,\n" +
            "       extend2,\n" +
            "       extend3,\n" +
            "       is_fyrcj,\n" +
            "       description\n" +
            "from base_rcj\n" +
            "where library_code = ? and (" +
            "material_name like '%' || ? || '%'\n" +
            "or material_code like '%' || ? || '%')\n" +
            "order by materialCode desc,materialName desc\n" +
            "limit ?, ?";
        let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).all(baseRcjModel.materialName, baseRcjModel.materialName,baseRcjModel.libraryCode,baseRcjModel.materialName, baseRcjModel.materialName, page-1, limit);
        let res = SqlUtils.convertToModel(sqlRes);


        let selectSqlCount = "select COUNT(1) as total from (select " +
            "material_code = ? as materialCode,\n" +
            "material_name = ? as materialName,\n" +
            "       sequence_nbr,\n" +
            "       level1,\n" +
            "       level2,\n" +
            "       level3,\n" +
            "       level4,\n" +
            "       level5,\n" +
            "       level6,\n" +
            "       level7,\n" +
            "       material_code,\n" +
            "       kind,\n" +
            "       material_name,\n" +
            "       specification,\n" +
            "       unit,\n" +
            "       de_price,\n" +
            "       market_price,\n" +
            "       tax_removal,\n" +
            "       library_code,\n" +
            "       level_mark,\n" +
            "       rec_user_code,\n" +
            "       rec_status,\n" +
            "       rec_date,\n" +
            "       extend1,\n" +
            "       extend2,\n" +
            "       extend3,\n" +
            "       is_fyrcj,\n" +
            "       description\n" +
            "from base_rcj\n" +
            "where library_code = ? and (" +
            "material_name like '%' || ? || '%'\n" +
            "or material_code like '%' || ? || '%')\n" +
            "order by materialCode desc,materialName desc)";

        let cnt = this.app.betterSqlite3DataSource.prepare(selectSqlCount).all(baseRcjModel.materialName, baseRcjModel.materialName,baseRcjModel.libraryCode, baseRcjModel.materialName, baseRcjModel.materialName);

        return {
            "list": res,
            "total": cnt
        };

    }

    /**
     *  模糊搜索
     * @param baseRcjModel 材料名称（模糊搜索）、定额册编码、level1~7
     * @param page
     * @param limit
     * @return {Promise<BaseRcj[]>}
     */
    async listLike(baseRcjModel, page, limit) {


        // 表的别名
        let tableAlias = "baseRcj";

        // 判断入参是否为空，不为空拼接sql
        let whereSql = "";
        let whereParams = {};
        // 定额册
        if (baseRcjModel.libraryCode) {
            // 拼接and
            if (whereSql.length !== 0) whereSql += " and ";
            // 拼接sql和参数
            whereSql += tableAlias + ".libraryCode = :libraryCode"
            whereParams.libraryCode = baseRcjModel.libraryCode;
        }
        // 层级1
        if (baseRcjModel.level1) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".level1 = :level1"
            whereParams.level1 = baseRcjModel.level1;
        }
        // 层级2
        if (baseRcjModel.level2) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".level2 = :level2"
            whereParams.level2 = baseRcjModel.level2;
        }
        // 层级3
        if (baseRcjModel.level3) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".level3 = :level3"
            whereParams.level3 = baseRcjModel.level3;
        }
        // 层级4
        if (baseRcjModel.level4) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".level4 = :level4"
            whereParams.level4 = baseRcjModel.level4;
        }
        // 层级5
        if (baseRcjModel.level5) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".level5 = :level5"
            whereParams.level5 = baseRcjModel.level5;
        }
        // 层级6
        if (baseRcjModel.level6) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".level6 = :level6"
            whereParams.level6 = baseRcjModel.level6;
        }
        // 层级7
        if (baseRcjModel.level7) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".level7 = :level7"
            whereParams.level7 = baseRcjModel.level7;
        }
        // 材料名称模糊搜索
        if (baseRcjModel.materialName) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += tableAlias + ".materialName like :materialName"
            whereParams.materialName = "%" + baseRcjModel.materialName + "%";
        }
        if (!whereSql) {
            console.log("error,参数为空");
        }

        // 分页结果
        let baseRcjList = await this.baseRcjDao
            .createQueryBuilder(tableAlias)
            .where(whereSql, whereParams)
            .orderBy(tableAlias + ".sort_no")
            .skip((page - 1) * limit)  // 跳过的记录数量
            .take(limit)  // 要获取的记录数量
            .getMany();

        //给基期价赋值  避免在22定额时选择12定额数据
        for(const rcj of baseRcjList){
            rcj.priceBaseJournal=rcj.dePrice;
            rcj.priceBaseJournalTax=rcj.dePrice;
        }
        // 总行数
        const count = await this.baseRcjDao.createQueryBuilder(tableAlias)
            .where(whereSql, whereParams)
            .getCount();

        // 封装分页结果
        return {
            "list": baseRcjList,
            "total": count
        };
    }

    /**
     * 根据材料idList查材料
     * @param rcjIdList 人材机ids
     * @return {Promise<BaseRcj[]>}
     */
    async getRcjListByRcjIdList(rcjIdList) {
        if (ObjectUtils.isEmpty(rcjIdList)) {
            console.error("getRcjListByRcjIdList入参人材机idList为空");
            return [];
        }

        //let sql = "select * from base_rcj where sequence_nbr in (?)"
        let sql = `SELECT * FROM base_rcj WHERE sequence_nbr IN (${rcjIdList.map(() => '?').join(',')})`
        let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).all(rcjIdList);
        let res = SqlUtils.convertToModel(sqlRes);

        return res;
    }

    /**
     * 根据code获取RcjTypeEnum的desc
     * @param rcjTypeEnumCode
     * @return {RcjTypeEnum.desc|null}
     */
    getRcjTypeEnumDescByCode(rcjTypeEnumCode) {
        for (let enumKey in RcjTypeEnum) {
            if (RcjTypeEnum[enumKey].code === rcjTypeEnumCode) {
                return RcjTypeEnum[enumKey].desc;
            }
        }
        return null;
    }

    getRcjTypeEnumDescByDesc(rcjTypeEnumDesc) {
        for (let enumKey in RcjTypeEnum) {
            if (enumKey.includes("TYPE")){
                if (RcjTypeEnum[enumKey].desc === rcjTypeEnumDesc) {
                    return RcjTypeEnum[enumKey].code;
                }
            }
        }
        return null;
    }

    /**
     * 根据code获取RcjTypeEnum的desc, 把配比当做材料(code6~10改为2)
     * @param rcjTypeEnumCode
     * @return {RcjTypeEnum.desc|null}
     */
    getRcjTypeEnumDescByCodeUseForDeDescribe(rcjTypeEnumCode) {
        // 把配比当做材料
        if ([6, 7, 8, 9, 10].includes(rcjTypeEnumCode)) {
            rcjTypeEnumCode = 2;
        }
        for (let enumKey in RcjTypeEnum) {
            if (RcjTypeEnum[enumKey].code === rcjTypeEnumCode) {
                return RcjTypeEnum[enumKey].desc;
            }
        }
        return null;
    }

    async listBaseRcjArray(libraryCode, materialCodeArray) {
        // 在当前定额册下，找编码为materialCodeArray中的人材机数据
        if (ObjectUtils.isEmpty(libraryCode)) {
            throw new Error("libraryCode is empty");
        }
        if (ObjectUtils.isEmpty(materialCodeArray)) {
            throw new Error("materialCodeArray is empty");
        }

        let rcjList = await this.baseRcjDao.findBy({libraryCode: libraryCode, materialCode: In(materialCodeArray)});
        if (ObjectUtils.isEmpty(rcjList)) {
            return [];
        }
        return rcjList;
    }

}

BaseRcjService.toString = () => '[class BaseRcjService]';
module.exports = BaseRcjService;
