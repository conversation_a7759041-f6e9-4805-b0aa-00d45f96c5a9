<!-- 概算，预算书，局部汇总 -->
<template>
  <common-modal
    className="dialog-comm gsPartialSummary"
    width="1000"
    @close="cancel"
    :mask="false"
    :lockScroll="false"
    :lockView="false"
    show-zoom
    resize
    v-model:modelValue="dialogVisible"
    title="局部汇总"
    destroy-on-close
  >
    <div class="quota-dialog-content">
      <div class="nav-bar">
        <div class="nav-bar-left" v-if="!isPreview">
          <!-- <div class="nav-items actives">
            {{ props.type == 'yss' ? '预算书' : '措施项目' }}
          </div> -->
          <div
            class="nav-items"
            :class="[useNav == i.type ? 'actives' : '']"
            @click="navChange(i.type)"
            v-for="(i, k) of navList"
          >
            {{ i.label }}
          </div>
        </div>
        <div class="nav-bar-left" v-show="isPreview && showSubNav">
          <div
            class="nav-items"
            @click="tabsChange(i.code)"
            :class="[activeKey == i.code ? 'actives' : '']"
            v-for="(i, k) of tabs"
          >
            {{ i.value }}
          </div>
        </div>
        <div class="nav-bar-right" v-if="!isPreview">
          <a-button style="margin-right: 20px" @click="openDzyHz" type="primary"
            >多专业设置</a-button
          >
          <a-button @click="handleTree(true)">展开所有子目</a-button>
          <a-button @click="handleTree(false)">折叠所有子目</a-button>
        </div>
      </div>

      <div class="quota-content" v-if="dialogVisible">
        <vxe-grid
          v-show="isPreview"
          ref="xGrid"
          v-bind="gridOptionsPreview"
          height="auto"
        >
          <template #ifDonorMaterial_default="{ row }">
            {{
              cellectType?.supplyType?.find(a => a.value == row.ifDonorMaterial)
                ?.label
            }}
          </template>
        </vxe-grid>

        <s-table
          v-show="!isPreview"
          size="small"
          ref="stableRef"
          class="s-table"
          bordered
          rowKey="sequenceNbr"
          defaultExpandAllRows
          :row-selection="rowSelection"
          :columns="gridOptions.columns"
          :pagination="false"
          height="50vh"
          :animateRows="false"
          :delay="200"
          :rangeSelection="true"
          :data-source="gridOptions.data"
          expand-row-by-click
          :expandedRowKeys="expandedRowKeys"
          @expandedRowsChange="expandedRowsChange"
        >
          <!-- :expandedRowKeys="expandedRowKeys" -->
          <!--自定义内容 -->
          <template
            #bodyCell="{
              text,
              record: row,
              index,
              column,
              key,
              openEditor,
              closeEditor,
            }"
          >
            <div v-if="column.dataIndex === 'deName'">
              <span v-if="row.deName == undefined || row.deName == ''"
                >&nbsp;</span
              >
              <span v-else>{{ row.deName }}</span>
            </div>
          </template>
        </s-table>
      </div>

      <div class="quota-footer">
        <div class="tips" v-if="isPreview"></div>
        <div class="tips" v-else>
          <icon-font
            class="icon"
            type="icon-querenshanchu"
            style="margin-right: 5px"
          />
          <span>请选择要汇总的项，点击“预览”可以查看汇总的结果</span>
        </div>

        <div>
          <a-button type="primary" @click="preview" v-if="!isPreview"
            >预览</a-button
          >

          <a-button type="primary" @click="exportData" v-if="isPreview"
            >导出Excel</a-button
          >
          <a-button style="margin-left: 20px" @click="back" v-if="isPreview"
            >返回</a-button
          >
        </div>
      </div>
    </div>
  </common-modal>

  <common-modal
    className="dialog-comm gsPartialSummary"
    title="多专业取费"
    width="460"
    height="300"
    v-model:modelValue="isDzyHz"
    @cancel="cancelData"
    @close="isDzyHz = false"
    :show-zoom="true"
    destroy-on-close
  >
    <dzy-hz ref="dzyHzRef" isPartFlag :QfMajorType="QfMajorType"></dzy-hz>
    <span class="btns">
      <a-button @click="cancelData()">取消</a-button>
      <a-button type="primary" @click="sureData()">确定</a-button>
    </span>
  </common-modal>
</template>
<script setup>
import { message } from 'ant-design-vue';
import {
  ref,
  reactive,
  watch,
  shallowRef,
  toRaw,
  onDeactivated,
  computed,
} from 'vue';
import { useRoute } from 'vue-router';
import csProject from '@gongLiaoJi/api/projectDetail';
import DzyHz from '@gongLiaoJi/views/projectDetail/customize/SummaryExpense/dzyHz.vue';

import api from '@gongLiaoJi/api/csProject';
import xeUtils from 'xe-utils';
import { DownOutlined } from '@ant-design/icons-vue';
import infoMode from '@/plugins/infoMode.js';
import { projectDetailStore } from '@/store/projectDetail';
import { useReversePosition } from '@/hooks/useReversePosition.js';
import deMapFun from '@gongLiaoJi/views/projectDetail/customize/deMap';
import {
  customCell,
  rowClassName,
  customHeaderCell,
} from '@/gaiSuanProject/views/projectDetail/customize/subItemProject/classAndStyleMethod';
import { useDecimalPoint } from '@gongLiaoJi/hooks/useDecimalPoint.js';

const { decimalFormat } = useDecimalPoint();

const store = projectDetailStore();
const emits = defineEmits(['closeDialog']);
const newConstructMajorType = ref('');
let dialogVisible = ref(false);
let activeKey = ref('');
const tabs = ref([
  {
    code: '',
    value: '费用汇总',
  },
  {
    code: 'rcj',
    value: '人材机汇总',
  },
]);

let initNavList = shallowRef([]);

const props = defineProps({
  deRowId: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: '',
  },
});

let xGrid = ref(null);
const gridOptions = reactive({
  border: true,
  keepSource: true,
  height: '100%',
  showOverflow: true,
  id: 'toolbar_demo_1',
  rowConfig: {
    isCurrent: true,
  },
  customConfig: {
    storage: true,
  },
  columns: [
    {
      title: '编码',
      field: 'deCode',
      dataIndex: 'deCode',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '名称',
      field: 'deName',
      dataIndex: 'deName',
      width: '40%',
      autoHeight: true,
      align: 'center',
    },
    {
      title: '单位',
      field: 'unit',
      dataIndex: 'unit',
      width: '80px',
      align: 'center',
    },
    {
      title: '工程量',
      dataIndex: 'quantity',
      field: 'quantity',
      width: '100px',
      align: 'center',
    },
    {
      title: '单价',
      dataIndex: 'price',
      field: 'price',
      width: '80px',
      align: 'center',
    },
    {
      title: '合价',
      dataIndex: 'totalNumber',
      field: 'totalNumber',
      width: '80px',
      align: 'center',
    },
  ],
  data: [],
});

const gridOptionsPreview = reactive({
  border: true,
  keepSource: true,
  showOverflow: true,
  height: '100%',
  id: 'toolbar_demo_2',
  rowConfig: {
    isCurrent: true,
  },
  customConfig: {
    storage: true,
  },
  virtualYConfig: {
    enabled: true,
    gt: 100,
  },
  columns: [
    { title: '序号', type: 'seq', width: 70, align: 'center' },
    { title: '费用代号', field: 'code', align: 'center' },
    { title: '名称', field: 'name', align: 'center' },
    { title: '计算基数', field: 'calculateFormula', align: 'center' },
    { title: '费率（%）', field: 'rate', align: 'center' },
    { title: '金额', field: 'price', align: 'center' },
    { title: '费用类别', field: 'category', align: 'center' },
    { title: '备注', field: 'remark', align: 'center' },
  ],
  data: [],
});

const selectState = reactive({
  selectedRowKeys: {
    yss: [],
    csxm: [],
  },
});
let currentInfo = ref();

const stableRef = ref();
const rowSelection = computed(() => {
  const checkboxDisabled = record => {
    if (useNav.value === 'yss') {
      return false;
    } else {
      if (
        useNav.value === 'csxm' &&
        csxmParams.defaultSelectKeys.includes(record.sequenceNbr)
      ) {
        return true;
      }
    }
  };
  return {
    checkStrictly: false,
    hideSelectAll: true,
    fixed: true,
    selectedRowKeys: selectState.selectedRowKeys[useNav.value],
    // getCheckboxProps: record => ({
    //   disabled:
    //     props.type == 'csxm'
    //       ? false
    //       : selectData.value.includes(record.sequenceNbr) ||
    //         ['0', '00'].includes(record.type)
    //       ? false
    //       : true,
    // }),
    // onChange: (selectedRowKeys,selectedRows) => {
    //   selectState.selectedRowKeys = selectedRowKeys
    // },
    getCheckboxProps: record => ({
      disabled: checkboxDisabled(record),
    }),
    onSelect: (record, selected, selectedRows, nativeEvent) => {
      currentInfo.value = record;
      // // 选中定额值自动添加其下级
      const keys = collectSequenceNbrs(record.children || []);
      if (selected) {
        const newKeys = Array.from(
          new Set([
            ...selectState.selectedRowKeys[useNav.value],
            record.sequenceNbr,
            ...keys,
          ])
        );
        selectState.selectedRowKeys[useNav.value] = newKeys;
      } else {
        const filterList = selectState.selectedRowKeys[useNav.value].filter(
          item => ![record.sequenceNbr, ...keys, record.parentId].includes(item)
        );
        const newKeys = Array.from(
          new Set([
            ...filterList,
            ...(useNav.value === 'csxm' ? csxmParams.defaultSelectKeys : []),
          ])
        );
        selectState.selectedRowKeys[useNav.value] = newKeys;
      }
    },
  };
});

function collectSequenceNbrs(tree) {
  let sequenceNbrs = new Set();
  function collect(node) {
    if (node.sequenceNbr !== undefined) {
      sequenceNbrs.add(node.sequenceNbr);
    }
    if (node.children) {
      node.children?.forEach(child => collect(child));
    }
  }

  tree?.forEach(node => collect(node));

  return sequenceNbrs;
}

const tabsChange = code => {
  newConstructMajorType.value = code;
  activeKey.value = code;
  if (code == 'rcj') {
    // 人材机列表
    gridOptionsPreview.columns = [
      { title: '序号', type: 'seq' },
      { title: '编码', field: 'materialCode', width: '80px' },
      { title: '类型', field: 'type', width: '80px' },
      { title: '名称', field: 'materialName', width: '200px' },
      { title: '规格型号', field: 'specification', width: '100px' },
      { title: '单位', field: 'unit', width: '60px' },
      { title: '数量', field: 'totalNumber', width: '80px' },
      {
        title: store.taxMade == 1 ? '不含税基期价' : '含税基期价',
        field: store.taxMade == 1 ? 'baseJournalPrice' : 'baseJournalTaxPrice',
        width: '80px',
      },
      {
        title: '不含税市场价',
        field: 'marketPrice',
        width: '80px',
      },
      {
        title: '含税市场价',
        field: 'marketTaxPrice',
        width: '80px',
      },
      { title: '税率（%）', field: 'taxRate', width: '80px' },
      { title: '含税市场价合计', field: 'totalTax', width: '80px' },
      { title: '不含税市场价合计', field: 'total', width: '80px' },
      { title: '价差合计', field: 'priceDifferencSum', width: '80px' },
      {
        title: '供应方式',
        field: 'ifDonorMaterial',
        width: '80px',
        slots: { default: 'ifDonorMaterial_default' },
      },
      { title: '产地', field: 'producer', width: '60px' },
      { title: '厂家', field: 'manufactor', width: '60px' },
      { title: '质量等级', field: 'qualityGrade', width: '60px' },
      { title: '品牌', field: 'brand', width: '60px' },
    ];
    // 1 一般计税  0 简易计税
    if (store.taxMade == '1') {
      gridOptionsPreview.columns = gridOptionsPreview.columns.filter(
        item => item.title !== '含税市场价合计'
      );
    } else if (store.taxMade == '0') {
      gridOptionsPreview.columns = gridOptionsPreview.columns.filter(
        item => item.title !== '不含税市场价合计'
      );
    }
    getRcjData();
    return;
  }

  gridOptionsPreview.columns = [
    { title: '序号', type: 'seq' },
    { title: '费用代号', field: 'code' },
    { title: '名称', field: 'name' },
    { title: '计算基数', field: 'calculateFormula' },
    { title: '基数说明', field: 'instructions' },
    { title: '费率（%）', field: 'rate' },
    { title: '金额', field: 'price' },
    { title: '费用类别', field: 'category' },
    { title: '备注', field: 'remark' },
  ];

  if (activeKey.value == '' && dzyStatus.value) {
    // 多专业 。费用汇总 列表
    gridOptionsPreview.columns = [
      { title: '序号', type: 'seq' },
      { title: '名称', field: 'name' },
      { title: '金额', field: 'price' },
      { title: '备注', field: 'remark' },
    ];
  }
  gridOptionsPreview.columns.forEach(item => {
    item.align = 'center';
    item.minWidth = '80px';
  });

  findPreview();
};

let originalData = shallowRef();
let isPreview = ref(false);
let showSubNav = ref(false);
let expandedRowKeys = ref([]);
let defaultExpandAllRows = shallowRef();
let selectData = ref([]);
const tempList = ref([]);

// 获取分类汇总信息
let cellectType = ref({});
const getRadioList = () => {
  let apiData = {
    type: 1,
    levelType: store.currentTreeInfo.type,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo.id,
  };
  api.getRcjCellectTypeData(apiData).then(res => {
    cellectType.value = {
      supplyType: res.supplyType.map(item => {
        const key = Object.keys(item)[0];
        const value = item[key];
        return { label: value, value: key };
      }),
    };
  });
};
getRadioList();

const navList = ref([
  {
    label: '预算书',
    type: 'yss',
  },
  {
    label: '措施项目',
    type: 'csxm',
  },
]);

const useNav = ref('yss');

const navChange = type => {
  useNav.value = type;
  getTreeList();
};

// 分别保存措施项目和预算书的勾选项
const saveCheckList = () => {
  let deLists = [];
  let csxmDeList = [];
  originalTableList.csxm.forEach(item => {
    item.isSelected = (selectState.selectedRowKeys.csxm || []).includes(
      item.sequenceNbr
    )
      ? 1
      : 0;
  });

  originalTableList.yss.forEach(item => {
    item.isSelected = (selectState.selectedRowKeys.yss || []).includes(
      item.sequenceNbr
    )
      ? 1
      : 0;
  });
};

let initListData = [];

let csxmParams = reactive({
  deMap: {},
  defaultSelectKeys: [],
  // deSelectSequenceNbr: [],
});
const getTreeList = async (deRowId = '') => {
  let formData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    unitId: store.currentTreeInfo?.id,
    deRowId,
  };

  const getListApiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
    sequenceNbr: store.asideMenuCurrentInfo?.sequenceNbr,
    posId: '',
    isShowAnnotations: false,
    token: null,
    pageSize: 300000,
    pageNum: 1,
  };

  const getListData = await csProject[
    useNav.value == 'yss' ? 'getDeTree4Unit' : 'itemPage'
  ](getListApiData);

  formData.deRowId =
    useNav.value == 'yss'
      ? getListData.result[0]?.deRowId
      : getListData.result.data[0]?.deRowId;

  let apiName =
    useNav.value == 'yss' ? 'getDeAllDepth' : 'getStepItemDeAllDepth';
  csProject[apiName](formData)
    .then(res => {
      if (res.result[0]) {
        if (res.result[0].type == '0') {
          res.result[0].deName = res.result[0].deName || '单位工程';
        }
      }

      // // 处理默认全打开
      defaultExpandAllRows.value = res.result.map(item => item.sequenceNbr);
      expandedRowKeys.value = xeUtils.clone(defaultExpandAllRows.value, true);

      originalData.value = res.result;

      let selectList = [];
      for (let i of res.result) {
        if (['0', '00', '01', '02'].includes(i.type)) {
          selectList.push(i?.sequenceNbr);
        }

        if (useNav.value === 'csxm' && i.type === '04') {
          csxmParams.deMap[i.sequenceNbr] = i;
          if (i.isCostDe === 1) {
            csxmParams.defaultSelectKeys = [
              ...csxmParams.defaultSelectKeys,
              i.sequenceNbr,
              i.parentId,
            ];

            let csxmselectedRowKeys = [
              ...csxmParams.defaultSelectKeys,
              ...selectState.selectedRowKeys.csxm,
            ];

            selectState.selectedRowKeys['csxm'] = [
              ...new Set(csxmselectedRowKeys),
            ];
          }
        }
      }
      selectData.value = selectList;

      initListData = JSON.parse(JSON.stringify(res.result));
      // handleDataList(res.result)
      gridOptions.data = xeUtils.toArrayTree(res.result, {
        key: 'sequenceNbr',
        parentKey: 'parentId',
      });
      // 处理整个树
      gridOptions.data.forEach(node => {
        removeEmptyChildren(node);
      });
      oldTableData.value = gridOptions.data;
      // console.log("🚀 ~ csProject.getDeAllDepth ~ = gridOptions.data:", gridOptions.data)
    })
    .finally(() => {
      dialogVisible.value = true;
    });
};

//进如页面，直接获取全部数据，因为列表有默认选中
let originalTableList = reactive({
  yss: [],
  csxm: [],
});
const initGetAllList = () => {
  let formData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    unitId: store.currentTreeInfo?.id,
  };

  const getListApiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
    sequenceNbr: store.asideMenuCurrentInfo?.sequenceNbr,
    posId: '',
    isShowAnnotations: false,
    token: null,
    pageSize: 300000,
    pageNum: 1,
  };

  // 预算书
  csProject.getDeTree4Unit(getListApiData).then(res => {
    formData.deRowId = res.result[0]?.deRowId;
    csProject.getDeAllDepth(formData).then(res => {
      originalTableList.yss = xeUtils.clone(res.result);
    });
  });

  // 措施项目
  csProject.itemPage(getListApiData).then(res => {
    formData.deRowId = res.result.data[0]?.deRowId;
    csProject.getStepItemDeAllDepth(formData).then(res => {
      originalTableList.csxm = xeUtils.clone(res.result);

      for (let i of originalTableList.csxm) {
        if (i.type === '04') {
          csxmParams.deMap[i.sequenceNbr] = i;
          if (i.isCostDe === 1) {
            csxmParams.defaultSelectKeys = [
              ...csxmParams.defaultSelectKeys,
              i.sequenceNbr,
              i.parentId,
            ];
            selectState.selectedRowKeys['csxm'] = JSON.parse(
              JSON.stringify(csxmParams.defaultSelectKeys)
            );
          }
        }
      }
    });
  });
};

// // 处理树结构
// const handleDataList = (tree) =>{
//   for(let i in tree){
//     if(tree[i].type=='05'){
//       tree.splice(i, 1)
//     }
//     if(tree[i].children>0){
//       handleDataList(tree[i].children)
//     }
//   }
// }
function removeEmptyChildren(node) {
  if (node.children && node.children.length === 0) {
    delete node.children; // 或者 node.children = undefined;
  } else if (node.children) {
    node.children.forEach(child => {
      removeEmptyChildren(child);
    });
  }
}

const oldTableData = shallowRef([]);
let deListsData = ref([]); //预算书
let csxmDeList = ref([]); //措施项目
let dzyStatus = ref(false);
let isYulan = ref(false);

let QfMajorType = ref(''); //单专业

const preview = async () => {
  clearFile();
  saveCheckList();
  isYulan.value = true;
  // 获取是否多专业
  const getIsSingleMajorFlag = await csProject.getIsSingleMajorFlag({
    unitId: store.currentTreeInfo?.id,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    isPartFlag: true,
  });
  let resConfig = null;
  tempList.value = [];
  QfMajorType.value = '';
  if (getIsSingleMajorFlag.result) {
    // 单专业
    let zyData = await api.getQfMajorTypeByUnit({
      unitId: store.currentTreeInfo?.id,
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId,
      isPartFlag: true,
    });
    QfMajorType.value = zyData.result;
    dzyStatus.value = false;
  } else {
    // 多专业设置
    dzyStatus.value = true;
    // 获取多专业配置
    resConfig = await api.getCostSummaryMajorByDeList({
      constructId: store.currentTreeGroupInfo?.constructId,
      unitId: store.currentTreeInfo?.id,
      singleId: store.currentTreeGroupInfo?.singleId,
      deLists: JSON.parse(JSON.stringify(originalTableList.yss)),
      csxmDeList: JSON.parse(JSON.stringify(originalTableList.csxm)),
    });
    (resConfig?.result || []).forEach(item => {
      const itemMap = Object.entries(item);
      let obj = {
        value: itemMap[0][1],
        code: itemMap[0][0],
      };
      tempList.value.push(obj);
    });
  }

  showSubNav.value = false;
  initNavList.value = [
    {
      code: QfMajorType.value,
      show: true,
      value: '费用汇总',
    },
    ...tempList.value,
    {
      code: 'rcj',
      show: true,
      value: '人材机汇总',
    },
  ];

  activeKey.value = initNavList.value[0].code;

  tabsChange(activeKey.value);
};

/**
 * 预览
 */
const findPreview = async () => {
  // 获取是否多专业
  let postData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    unitId: store.currentTreeInfo?.id,
    singleId: store.currentTreeGroupInfo?.singleId,
    constructMajorType: newConstructMajorType.value,
    deLists: JSON.parse(JSON.stringify(originalTableList.yss)),
    csxmDeList: JSON.parse(JSON.stringify(originalTableList.csxm)),
    isYulan: isYulan.value,
  };
  //
  // constructMajorType:store.currentTreeInfo.constructMajorType,
  // if (!dzyStatus.value && activeKey.value == '') {
  //   // 非多专业，并且是费用汇总
  //   postData.constructMajorType = store.currentTreeInfo?.qfMajorType;
  // }
  console.info(22222222222222, postData);

  csProject
    .getUnitPartCostSummaryList(postData)
    .then(res => {
      console.log('🚀 ~ csProject.getUnitPartCostSummaryList ~ res:', res);
      gridOptionsPreview.data = res.result?.costSummaryData || res?.result;
      gridOptionsPreview.data?.forEach(item => {
        item.price = decimalFormat(item.price, 'COST_SUMMARY_JE_PATH');
      });
      initNavList.value.forEach(item => {
        if (!item?.show) {
          let hasData = gridOptionsPreview.data.find(
            node => node.code == item.code
          );
          item.show = !!hasData;
        }
      });
      tabs.value = initNavList.value.filter(item => item.show);
      isPreview.value = true;
      showSubNav.value = true;
    })
    .finally(() => {
      isYulan.value = false;
    });
};

const decimalFormatValue = (value, row, filed) => {
  let path = '';
  let pointMap = {
    PTRCJZS: {
      baseJournalPrice: 'DETAIL_PTRCJZS_BASEJOURNALPRICE_PATH',
      baseJournalTaxPrice: 'DETAIL_PTRCJZS_BASEJOURNALTAXPRICE_PATH',
      marketPrice: 'DETAIL_PTRCJZS_MARKETPRICE_PATH',
      marketTaxPrice: 'DETAIL_PTRCJZS_MARKETTAXPRICE_PATH',
      totalNumberPrice: 'DETAIL_PTRCJZS_TOTAL_PATH',
      taxRate: 'DETAIL_RCJ_TAXRATE_PATH',
      total: 'DETAIL_PTRCJZS_TOTAL_PATH',
      totalTax: 'DETAIL_PTRCJZS_TOTAL_PATH',
    },
    BCRCJZS: {
      baseJournalPrice: 'DETAIL_BCRCJZS_BASEJOURNALPRICE_PATH',
      baseJournalTaxPrice: 'DETAIL_BCRCJZS_BASEJOURNALTAXPRICE_PATH',
      marketPrice: 'DETAIL_BCRCJZS_MARKETPRICE_PATH',
      marketTaxPrice: 'DETAIL_BCRCJZS_MARKETTAXPRICE_PATH',
      totalNumberPrice: 'DETAIL_PTRCJZS_TOTAL_PATH',
      taxRate: 'DETAIL_BCRCJ_TAXRATE_PATH',
      total: 'DETAIL_PTRCJZS_TOTAL_PATH',
      totalTax: 'DETAIL_PTRCJZS_TOTAL_PATH',
    },
    FYTZRCJ: {
      baseJournalPrice: 'DETAIL_FYTZRCJ_BASEJOURNALPRICE_PATH',
      baseJournalTaxPrice: 'DETAIL_FYTZRCJ_BASEJOURNALTAXPRICE_PATH',
      marketPrice: 'DETAIL_FYTZRCJ_MARKETPRICE_PATH',
      marketTaxPrice: 'DETAIL_FYTZRCJ_MARKETTAXPRICE_PATH',
      totalNumberPrice: 'DETAIL_PTRCJZS_TOTAL_PATH',
      taxRate: 'DETAIL_RCJ_TAXRATE_PATH',
      total: 'DETAIL_PTRCJZS_TOTAL_PATH',
      totalTax: 'DETAIL_PTRCJZS_TOTAL_PATH',
    },
  };

  let rowType = 'PTRCJZS';
  if (row.isFyrcj === 0) {
    rowType = 'FYTZRCJ';
  }
  if (
    row &&
    row?.materialCode &&
    ['BCJXF', 'BCCLF', 'BCSBF', 'BCZCF'].includes(
      row.materialCode?.split('#')[0]
    )
  ) {
    rowType = 'BCRCJZS';
  }

  path = pointMap[rowType][filed];

  if (!path) {
    // message.error('没有找到对应的路径');
    return value;
  }
  return decimalFormat(value, path);
};

const getRcjData = () => {
  let postData = {
    kind: '0',
    constructId: store.currentTreeGroupInfo?.constructId,
    levelType: store.currentTreeInfo.type,
    isShowAnnotations: false,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
    deLists: JSON.parse(JSON.stringify(originalTableList.yss)),
    csxmDeList: JSON.parse(JSON.stringify(originalTableList.csxm)),
  };
  csProject.getPartRcjCellectData(postData).then(res => {
    res.result.forEach(item => {
      autoChangePrice(
        item,
        store.taxMade == 1 ? 'marketPrice' : 'marketTaxPrice'
      );
      gridOptionsPreview.columns.forEach(column => {
        if (column.field) {
          const value = item[column.field];
          item[column.field] = decimalFormatValue(value, item, column.field);
        }
      });
    });
    gridOptionsPreview.data = res.result;
  });
};

// 自动模拟价格修改
const autoChangePrice = (row, filed) => {
  if (!row[filed]) {
    return;
  }
  if (filed == 'marketPrice') {
    // 不含税市场价
    row.total = (row.totalNumber * row[filed]).toFixed(2);
    row.priceDifferenc =
      (Number(row.marketPrice) - Number(row.baseJournalPrice)).toFixed(2) ?? 0;
    row.priceDifferencSum =
      (row.totalNumber * row.priceDifferenc).toFixed(2) ?? 0;
  } else {
    row.totalTax = (row.totalNumber * row[filed]).toFixed(2) ?? 0;
    row.priceDifferenc =
      (Number(row.marketTaxPrice) - Number(row.baseJournalTaxPrice)).toFixed(
        2
      ) ?? 0;
    row.priceDifferencSum =
      (row.totalNumber * row.priceDifferenc).toFixed(2) ?? 0;
  }
};

const back = () => {
  gridOptions.data = oldTableData.value;
  isPreview.value = false;
  clearFile();
};

//导出
const exportData = () => {
  console.log(activeKey.value);
  if (activeKey.value == 'rcj') {
    api
      .exportPartRcjCellectData({
        constructId: store.currentTreeGroupInfo?.constructId,
        unitId: store.currentTreeInfo?.id,
        rowList: [...toRaw(gridOptionsPreview.data)],
        isFyhz: tempList.value.length >= 2,
      })
      .then(res => {
        exportFollow(res);
      });
  } else if (activeKey.value == '' || activeKey.value) {
    api
      .exportUnitPartCostSummary({
        rowList: [...toRaw(gridOptionsPreview.data)],
        constructMajorType: activeKey.value,
        isFyhz: tempList.value.length >= 2,
      })
      .then(res => {
        exportFollow(res);
      });
  }
};

const exportFollow = res => {
  if (res.code == 200) {
    if (res.result && res.result?.code == 200) {
      message.success(res.result.message);
    }
  } else {
    message.error(res.result?.message);
  }
};

const cancel = () => {
  clearFile();
  dialogVisible.value = false;
  emits('closeDialog');
};

const resetHandle = () => {
  selectState.selectedRowKeys = {
    yss: [],
    csxm: [],
  };
  csxmParams.deMap = {};
};
const handleTree = type => {
  if (type) {
    expandedRowKeys.value = xeUtils.clone(defaultExpandAllRows.value, true);
  } else {
    let arr = [];

    if (initListData.length > 2) {
      handleTreeDe(initListData, arr);
    }
    expandedRowKeys.value = arr;
    // expandedRowKeys.value = [gridOptions.data[0]?.sequenceNbr]
  }
};
const handleTreeDe = (list, arr) => {
  let data = JSON.parse(JSON.stringify(list));
  for (let item of data) {
    if (['0'].includes(item.type)) {
      arr.push(item.sequenceNbr);
    }
  }
};

let businessType = ref('');
/**
 *
 * @param data 数据
 * @param type 进入的入口， yss
 */
const open = (data, type = 'yss') => {
  initGetAllList();
  businessType.value = type;
  useNav.value = type == 'yss' ? 'yss' : 'csxm';
  getTreeList(data);
};

const expandedRowsChange = rows => {
  expandedRowKeys.value = rows;
};

let isDzyHz = ref(false);
let dzyHzRef = ref();
// 多专业汇总关闭
const cancelData = () => {
  isDzyHz.value = false;
};
const openDzyHz = async () => {
  let zyData = await api.getQfMajorTypeByUnit({
    unitId: store.currentTreeInfo?.id,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    isPartFlag: true,
  });
  QfMajorType.value = zyData.result;
  isDzyHz.value = true;
};

// 多专业汇总确认
const sureData = () => {
  const qfMajorType = dzyHzRef.value.qfMajorType;
  const isSingleMajorFlag = dzyHzRef.value.isSingleMajorFlag;
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    qfMajorType: isSingleMajorFlag ? qfMajorType : '', //工程专业
    isSingleMajorFlag,
    isPartFlag: true,
  };

  if (isSingleMajorFlag && !qfMajorType) {
    message.error('请选择专业！');
    return;
  }

  api.setIsSingleMajorFlag(apiData).then(res => {
    console.info(res);
    if (res.status !== 200) {
      return message.error(res.message);
    }
    isDzyHz.value = false;
    message.success(res.message);
  });
};

const clearFile = () => {
  api.closePartCostSummary({
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo.id,
  });
};

defineExpose({ open, cancel });
</script>

<style lang="scss">
.gsPartialSummary {
  .vxe-modal--box {
    overflow: hidden !important;
  }
  .btns {
    width: 200px;
    bottom: 10px;
    display: flex;
    justify-content: space-around;
    margin: 0 auto;
    position: absolute;
    left: 29%;
  }
  .vxe-icon-minus,
  .vxe-icon-add {
    color: #87b2f2;
    font-size: 9px;
    position: relative;
    left: -8px;
    top: -1px;
    border: 1px solid #87b2f2;
  }
  .surely-table {
    font-size: 12px;
  }
  .surely-table-wrapper {
    width: 100%;
    overflow: hidden;
    font-size: 12px;
  }
  .vxe-modal--content {
    padding-bottom: 15px !important;
  }

  .quota-dialog-content {
    width: 100%;
    overflow: hidden;
    .quota-header {
      margin-bottom: 12px;
      display: flex;
      align-items: center;

      span {
        color: rgba(96, 96, 96, 1);
        margin-left: 30px;
        &:first-child {
          margin-left: 0;
        }
        b {
          font-weight: 400;
          color: rgba(0, 0, 0, 1);
        }
      }
    }

    .nav-bar-right {
      display: flex;
      align-items: center;
    }
    .nav-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30px;
      .nav-bar-left {
        display: flex;
        align-items: center;
        overflow-x: auto;
        width: 100%;
        padding-bottom: 10px;
        .nav-items {
          width: fit-content;
          flex-shrink: 0;
          padding: 5px 10px;
          text-align: center;
          font-weight: 400;
          font-size: 12px;
          font-weight: 400;
          color: #000000;
          cursor: pointer;
          border-top: 1px solid rgba(217, 217, 217, 1);
          border-bottom: 1px solid rgba(217, 217, 217, 1);
          border-left: 1px solid rgba(217, 217, 217, 1);
          &:last-child {
            border-right: 1px solid rgba(217, 217, 217, 1);
          }
          &.actives {
            color: #ffffff;
            background: rgba(40, 124, 250, 1);
            border-radius: 2px 0px 0px 2px;
            border-color: rgba(40, 124, 250, 1);
          }
        }
      }
    }
  }

  .quota-content {
    width: 100%;
    height: 50vh;
    overflow: hidden;
  }

  .quota-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;
    .tips {
      span {
        font-size: 12px;
        font-weight: 400;
        color: #2a2a2a;
      }
    }
  }
  .vxe-table--render-default .vxe-body--column .vxe-cell {
    white-space: normal !important;
    max-height: none !important;
  }
}
</style>
