/*
 * @Descripttion: 
 * @Author: sunchen
 * @Date: 2023-08-25 14:43:38
 * @LastEditors: sunchen
 * @LastEditTime: 2023-09-04 13:58:15
 */
import { reactive  } from "vue";
 
export const globalData = reactive({
  useMenu:'',// 表格头部选中的
  showSchedule:false,// 是否显示进度页面
  currentInfo:null,//工程项目点击
  treeParams:{},//获取报表左侧树
});

// 设置值
export const setLocalStorage = ( value) => {
  localStorage.setItem('reportForm', JSON.stringify(value));
}

// 删除缓存
export const removeLocalStorage = () => {
  localStorage.removeItem('reportForm');
}

// 获取值
export const getLocalStorage = () => {
  return JSON.parse(localStorage.getItem('reportForm'))
}