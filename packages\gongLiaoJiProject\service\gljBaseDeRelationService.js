const {Service} = require('../../../core');
const {BaseDeRelation2022} = require("../models/BaseDeRelation2022");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {Snowflake} = require("../utils/Snowflake");
const {NumberUtil} = require("../utils/NumberUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");

/**
 * 子目定额
 * @class
 */
class GljBaseDeRelationService extends Service{

    constructor(ctx) {
        super(ctx);
        this.baseDeRelation2022Dao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseDeRelation2022);
    }

    /**
     * 获取子目定额
     * @param deIdF
     * @returns {Promise<BaseDeRelation2022[]|Error>}
     */
    async getZmRelationByDeIdF(deIdF) {
        if (null == deIdF) {
            throw new Error("必传参数deIdF为空");
        }
        let result = await this.baseDeRelation2022Dao.find({
            where: {
                deIdF: deIdF
            }
        });
        result.forEach(item => item.isRelationed = false);
        return result;
    }

    /**
     * 获取子目定额
     * @param params
     * @returns {Promise<BaseDeRelation2022[]|Error>}
     */
    async getZmRelation(params) {
        if (null == params.libraryCode) {
            throw new Error("必传参数定额标准为空");
        }

        return await this.baseDeRelation2022Dao.find({
            where: {
                libraryCode: params.libraryCode,
                deCodeF: params.deCode,
                deNameF: params.deName,
            }
        });
    }

    /**
     * 获取子目定额
     * @param standardId
     * @returns {Promise<BaseDeRelation2022[]|Error>}
     */
    async zmDeList(standardId) {
        if (null == standardId) {
            throw new Error("必传参数standardId为空");
        }
        let result = {};
        let deRelationList = await this.getZmRelationByDeIdF(standardId);
        if (ObjectUtil.isEmpty(deRelationList)) {
            return result;
        }
        deRelationList.sort((a, b) =>
            a.deCodeZ.localeCompare(b.deCodeZ, undefined, {
                numeric: true,        // 按数字而非字符串排序
                sensitivity: 'base'   // 忽略大小写和重音
            })
        );
        result = await this.zmGroup(deRelationList);
        // 提取 quantity 值并去重
        let quantityFormulas = [...new Set(deRelationList.map(item => item.quantity))].map(q => ({ formula: q }));
        let quantitys = await this.cariableCoefficient(result.zmVariableRuleList, quantityFormulas);
        for (let zmDe of deRelationList) {
            zmDe.quantity = quantitys.find(item => zmDe.quantityExpression === item.formula)?.resultValue;
            zmDe.quantity = ObjectUtil.isEmpty(zmDe.quantity)?0:zmDe.quantity;
        }
        return result;
    }

    /**
     * 子目定额工程量计算
     * @param standardId
     * @returns {Promise<BaseDeRelation2022[]|Error>}
     */
    async zmDeListCalculate(constructId, standardId, zmVariableRuleList) {
        if (null == standardId) {
            throw new Error("必传参数standardId为空");
        }
        let result = {};
        let deRelationList = await this.getZmRelationByDeIdF(standardId);

        // 提取 quantity 值并去重
        let quantityFormulas = [...new Set(deRelationList.map(item => item.quantity))].map(q => ({ formula: q }));
        zmVariableRuleList = await this.service.gongLiaoJiProject.gljBaseDeRelationVariableCoefficientService.updateVariableCoefficient(zmVariableRuleList);
        zmVariableRuleList.forEach(item => {
            if (ObjectUtils.isNotEmpty(item.resultValue) || item.ifEditable === 1) {
                item.resultValue = NumberUtil.numberScale(item.resultValue, 6);
            }
        })
        let quantitys = await this.cariableCoefficient(zmVariableRuleList, quantityFormulas);
        for (let zmDe of deRelationList) {
            let quantity = quantitys.find(item => zmDe.quantityExpression === item.formula)?.resultValue
            zmDe.quantity = quantity;
        }
        deRelationList.sort((a, b) =>
            a.deCodeZ.localeCompare(b.deCodeZ, undefined, {
                numeric: true,        // 按数字而非字符串排序
                sensitivity: 'base'   // 忽略大小写和重音
            })
        );
        result = await this.zmGroup(deRelationList);
        result.zmVariableRuleList = zmVariableRuleList
        return result;
    }

    /**
     * 子目定额工程量计算
     * @param standardId
     * @returns {Promise<BaseDeRelation2022[]|Error>}
     */
    async zmCalculateQuantity(constructId, zmVariableRuleList, zmDe) {
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        // 提取 quantity 值并去重
        let quantityFormulas = [{ formula: zmDe.quantityExpression}];
        let quantitys = await this.cariableCoefficient(zmVariableRuleList, quantityFormulas);
        let quantity = quantitys.find(item => zmDe.quantityExpression === item.formula)?.resultValue
        zmDe.quantity = NumberUtil.numberScale(quantity, precision.EDIT.DE.quantity);
        return zmDe;
    }

    /**
     *
     * @returns {Promise<void>}
     */
    async zmGroup(deRelationList) {
        let result = {};
        if (ObjectUtil.isEmpty(deRelationList)) {
            return result
        }
        let findGroupId = 0;
        // 先对 libraryCode 分组
        let groupedByLibraryCode = deRelationList.reduce((acc, item) => {
            let { libraryCodeZ } = item;
            if (!acc[libraryCodeZ]) {
                acc[libraryCodeZ] = [];
            }
            acc[libraryCodeZ].push(item);
            return acc;
        }, {});
        // 在每个 libraryCodeZ 组内，再对 groupId 和 relationContent 组合分组
        let finalGrouped = {};
        for (let libraryCodeZ in groupedByLibraryCode) {
            let list = groupedByLibraryCode[libraryCodeZ];
            finalGrouped[libraryCodeZ] = list.reduce((acc, item) => {
                let { groupId, relationContent } = item;
                findGroupId = groupId;
                let key = `${groupId}-${relationContent}`;
                if (!acc[key]) {
                    acc[key] = {
                        sequenceNbr: Snowflake.nextId(),
                        groupId,
                        relationContent,
                        children: []
                    };
                }
                acc[key].children.push(item);
                return acc;
            }, {});
        }
        // 将结果转换为数组（可选）
        let zmDeList = []
        let zmPointList = []
        for (const libraryCode in finalGrouped) {
            const deLibrary =  await this.service.gongLiaoJiProject.gljBaseDeLibraryService.getByLibraryCode(libraryCode);
            let lGrouped = finalGrouped[libraryCode];
            let lGroupedResult = Object.values(lGrouped);
            let zmPointList2 = lGroupedResult.map(item => item.relationContent);
            let sh = {
                sequenceNbr: Snowflake.nextId(),
                relationContent: deLibrary.libraryName,
                children: lGroupedResult
            }
            zmDeList.push(sh)
            zmPointList.push(...zmPointList2);
        }
        result.zmDeList = zmDeList
        result.zmPointList = zmPointList
        //获取子目变量规则
        let zmVariableRuleList = await this.service.gongLiaoJiProject.gljBaseDeRelationVariableCoefficientService.getCoefficientByGroupId(findGroupId);
        result.libraryNameRelation = deRelationList[0].libraryNameRelation
        result.zmVariableRuleList = zmVariableRuleList
        return result;
    }

    async cariableCoefficient(data, quantitys = []) {
        // 创建一个对象来存储变量名和对应的值
        const variables = {};
        // 首先，将所有输入的值存储到 variables 对象中
        data.forEach(item => {
            if (item.variableCode && item.value !== undefined) {
                variables[item.variableCode] = item.resultValue;
            }
        });
        quantitys.forEach(item => {
            let formulaFunc = new Function(...Object.keys(variables), `return ${item.formula};`);
            item.resultValue = formulaFunc(...Object.values(variables));
        });
        return quantitys;
    }

}

GljBaseDeRelationService.toString = () => '[class GljBaseDeRelationService]';
module.exports = GljBaseDeRelationService;
