'use strict';

const { Service, Log } = require('../../core');
const { BaseArea } = require('../model/BaseArea');
const { BaseListDeStandard } = require('../model/BaseListDeStandard');
const { getRepository, In } = require('typeorm');
const { PricingFileFindUtils } = require('../utils/PricingFileFindUtils');
const { BaseFeeFileRelation } = require('../model/BaseFeeFileRelation');
const { UnitFeeFile } = require('../model/UnitFeeFile');
const { BaseFeeFile, BaseFeeFile2022 } = require('../model/BaseFeeFile');
const { ObjectUtils } = require('../utils/ObjectUtils');
const EngineeringLocationEnum = require('../enum/EngineeringLocationEnum');
const { NO_WAY } = require('../enum/AdjacentRoadsNumEnum');
const FloorSpaceEnum = require('../enum/FloorSpaceEnum');
const MunicipalEngineeringCostEnum = require('../enum/MunicipalEngineeringCostEnum');
const { UnitFeeDescription } = require('../model/UnitFeeDescription');
const { MANAGEMENT_FEE3 } = require('../enum/EngineeringTypeEnum');
const AdjacentRoadsNumEnum = require('../enum/AdjacentRoadsNumEnum');
const ConstantUtil = require('../enum/ConstantUtil');
const { Snowflake } = require('../utils/Snowflake');
const os = require('os');
const { ArrayUtil } = require('../utils/ArrayUtil');
const { TreeList } = require('../model/TreeList');
const { FeeCollectionVO } = require('../model/FeeCollectionVO');
const ProjectLevelConstant = require('../enum/ProjectLevelConstant');
const EngineeringTypeEnum = require('../enum/EngineeringTypeEnum');
const { BaseAnwenRate } = require('../model/BaseAnwenRate');
const { BasePolicyDocument } = require('../model/BasePolicyDocument');
const PolicyDocumentTypeEnum = require('../enum/PolicyDocumentTypeEnum');
const { ProjectOverview } = require('../model/ProjectOverview');
const { ConvertUtil } = require('../utils/ConvertUtils');
const gcjbxx = require('../jsonData/gcjbxx.json');
const { ProjectTaxCalculation } = require('../model/ProjectTaxCalculation');
const projectLevelConstant = require('../enum/ProjectLevelConstant');
const { ProjectTaxCalculationVO } = require('../model/ProjectTaxCalculationVO');
const TaxPayingRegionEnum = require('../enum/TaxPayingRegionEnum');
const { BaseTaxReformDocuments } = require('../model/BaseTaxReformDocuments');
const TaxCalculationMethodEnum = require('../enum/TaxCalculationMethodEnum');
const PrecastRateEnum = require('../enum/PrecastRateEnum');
const { ResponseData } = require('../utils/ResponseData');
const GsjRateKindEnum = require('../enum/GsjRateKindEnum');
const { DateUtils } = require('../utils/DateUtils');
const { BaseDe, BaseDe2022 } = require('../model/BaseDe');
const { groupBy } = require('lodash');
const SecondSpecialityEnum = require('../enum/SecondSpecialityEnum');
const TaxPayingRegion22Enum = require('../enum/TaxPayingRegion22Enum');
const { ParamUtils } = require('../../core/core/lib/utils/ParamUtils');
const { get2022BY2012 } = require('../model/Map2022And2012');
const { BaseFeeFileProject, BaseFeeFileProject2022 } = require('../model/BaseFeeFileProject');
var { UPCContext } = require('../unit_price_composition/core/UPCContext');
const { BaseManageRate, BaseManageRate2022 } = require('../model/BaseManageRate');
const CalculateBaseType = require('../enum/CalculateBaseType');
const CalculateBaseConfigEnum = require('../enum/CalculateBaseConfigEnum');
const CalculateBaseUnitInitEnum = require('../enum/CalculateBaseUnitInitEnum');
const {UPCCupmuteDe} = require("../unit_price_composition/compute/UPCCupmute");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const UnitPriceConstant = require("../enum/UnitPriceConstant");
const CalculationTool = require("../unit_price_composition/compute/CalculationTool");
const _ = require("lodash");
const DePropertyTypeConstant = require("../enum/DePropertyTypeConstant");
const ConsoleStrategy = require("../console_handle/ConsoleStrategy");
const {RcjCalculateHandler} = require("../rcj_handle/calculate/RcjCalculateHandler");
const {ObjectUtil} = require("../../common/ObjectUtil");
const {NumberUtil} = require("../utils/NumberUtil");

/**
 * 示例服务
 * @class
 */
class BaseFeeFileService extends Service {

  constructor(ctx) {
    super(ctx);
  }


  /**
   * 获取取费文件列表
   */
  getFeeCollectionTreeList(arg) {
    let feeCollectionList = this.getFeeCollectionList(arg);
    let feeFiles = ArrayUtil.distinctList(feeCollectionList, 'feeFileId');
    let treeList = new TreeList();
    treeList.itemList = this.setFeeFileTreeListData(feeFiles);
    return treeList;
  }

  async setMainFeeFile(arg) {
    let { constructId, singleId, unitId, sequenceNbr } = arg;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let mainFeeFileAfterChange = null;
    unit.feeFiles.filter(k => {
      k.defaultFeeFlag = 0;
      if (k.feeFileId == sequenceNbr) {
        k.defaultFeeFlag = 1;
        mainFeeFileAfterChange = k;
      }
    });

    //todo 需要触发全局计算业务
    if(!mainFeeFileAfterChange){
      return;
    }

    this._calculationByFeeFile(unit, mainFeeFileAfterChange);

    await this.service.management.sycnTrigger("unitDeChange");
    await this.service.management.trigger("itemChange");
    await this.service.autoCostMathService.autoCostMath({
      constructId: constructId,
      singleId: singleId,
      unitId: unitId,
      countCostCodeFlag: true,
    });
  }

  /**
   * 获取单位或者工程下面的取费文件列表
   * @param newArray
   * @param dataArray
   * @return {*}
   */
  getFeeCollectionList(arg) {
    let levelType = arg.levelType;
    let constructId = arg.constructId;
    let singleId = arg.singleId;
    let unitId = arg.unitId;
    let array = new Array();
    if (levelType == ConstantUtil.CONSTRUCT_LEVEL_TYPE) {
      //项目
      let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
      if (ObjectUtils.isNotEmpty(projectObj.singleProjects)) {
        for (let i = 0; i < projectObj.singleProjects.length; i++) {
          array = this.getUnitFeeFiles(projectObj.singleProjects[i], array);
        }
      } else if (ObjectUtils.isNotEmpty(projectObj.unitProjectArray)) {
        array = projectObj.unitProjectArray.reduce((result, obj) => [...result, ...obj.feeFiles], []);
      }
    } else if (levelType == ConstantUtil.SINGLE_LEVEL_TYPE){
      let unitList = PricingFileFindUtils.getUnitListBySingle(constructId,singleId);
      array = unitList.reduce((result, obj) => [...result, ...obj.feeFiles], []);

    }else {
      //单位
      let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
      array = unit.feeFiles;
    }
    array.sort((a, b) => {
      if (a.sortNo > b.sortNo) {
        return 1; // a 排在 b 后面
      } else if (a.sortNo < b.sortNo) {
        return -1; // a 排在 b 前面
      } else {
        // 如果 prop1 相等，则按照 prop2 排序
        if (new Date(a.recDate) > new Date(b.recDate)) {
          return 1; // a 排在 b 后面
        } else if (new Date(a.recDate) < new Date(b.recDate)) {
          return -1; // a 排在 b 前面
        } else {
          return 0; // a 和 b 排序相等
        }
      }
    });

    return array;
  }

  getUnitFeeFiles(singleProject, array) {

    if (!ObjectUtils.isEmpty(singleProject.unitProjects)) {  //达到最后一层包含单位的子单项
      const mergedArray = singleProject.unitProjects.filter(u => ObjectUtils.isNotEmpty(u.feeFiles)).reduce((result, obj) => [...result, ...obj.feeFiles], []);
      array = array.concat(mergedArray);
    } else {
      if (!ObjectUtils.isEmpty(singleProject.subSingleProjects)) {
        for (let i = 0; i < singleProject.subSingleProjects.length; i++) {
          array = this.getUnitFeeFiles(singleProject.subSingleProjects[i], array);
        }
      }
    }
    return array;
  }

  setFeeFileTreeListData(dataArray) {
    let newArray = new Array();
    for (const i in dataArray) {
      let feeFile = dataArray[i];
      let key = feeFile.feeFileId;
      let value = (parseInt(i) + 1) + ' ' + feeFile.feeCode + ' ' + feeFile.feeFileName;
      let obj = {};
      obj[key] = value;
      if (feeFile.defaultFeeFlag == 1) {
        obj.defaultFeeFlag = 1;
      }
      newArray.push(obj);
    }
    return newArray;
  }

  /**
   * 添加取费文件列表
   * @param arg
   * @return {Promise<void>}
   */
  async initFeeFile(unitProject) {
    // 1. 重置取费文件列表
    unitProject.feeFiles = new Array();
    let { baseFeeFileRelationService } = this.service;
    //获取取费文件关联关系 TODO 需要添加新的额映射表
    let unitIs2022 = PricingFileFindUtils.is22Unit(unitProject)

    let feeFileRelation = await baseFeeFileRelationService.getFeeFileProject(unitProject.constructMajorType, unitIs2022 ? BaseFeeFileProject2022 : BaseFeeFileProject);
    //循环取费关系列表数据
    for (const i in feeFileRelation) {
      let feeFileRelationDto = feeFileRelation[i];
      //获取取费文件
      let baseFeeFile = await this.getBaseFeeFile(feeFileRelationDto.qfCode, unitIs2022);

      await this.addFeeFile(baseFeeFile, unitProject, feeFileRelationDto);
    }
    if (unitProject.spId) {
      let constructId = unitProject.constructId;
      let levelType = ProjectLevelConstant.single;
      let singleId = unitProject.spId;
      let addFlag = true
      let unitList = PricingFileFindUtils.getUnitListBySingle(constructId,singleId);
      const flag = unitList.some(e => e.feeFiles === undefined);
      if (!flag){
        await this.feeCollectionData({constructId, levelType, singleId, addFlag});
      }
    }

    // 2. 设置主取费文件
    await this.dealWithMainFeeFile(unitProject);

    return unitProject;
  }

  /**
   * 设置单位工程的默认主取费文件标识
   * @param unitProject
   * @returns {Promise<void>}
   */
  async dealWithMainFeeFile(unitProject) {
    let constructDeStandard = PricingFileFindUtils.getConstructDeStandard(unitProject.constructId);
    let sql = '';
    if (ConstantUtil.DE_STANDARD_12 == constructDeStandard) {
      sql = 'select default_qf_code as code from base_speciality_de_fee_relation where unit_project_name = ? and library_code = ?';
    } else {
      sql = 'select default_qf_code as code from base_speciality_de_fee_relation_2022 where unit_project_name = ? and library_code = ?';
    }
    let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).get(unitProject.constructMajorType, unitProject.mainDeLibrary);

    if (sqlRes) {
      let mainFile = await this.service.baseFeeFileService.updateFBFXFeeFile(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr, sqlRes.code);
      mainFile.defaultFeeFlag = 1;
    } else {
      unitProject.feeFiles[0].defaultFeeFlag = 1;
    }
  }


  /**
   * 添加取费文件
   * @param baseFeeFile
   * @param unitProject
   * @param baseFeeFileRelation
   * @return {Promise<void>}
   */
  async addFeeFile(baseFeeFile, unitProject, feeFileRelationDto) {
    let unitFeeFile = new UnitFeeFile();
    unitFeeFile.constructId = unitProject.constructId;
    unitFeeFile.singleId = unitProject.spId;
    unitFeeFile.sequenceNbr = Snowflake.nextId();
    unitFeeFile.unitId = unitProject.sequenceNbr;
    unitFeeFile.feeFileId = baseFeeFile.sequenceNbr;
    unitFeeFile.feeFileCode = baseFeeFile.qfCode;
    //费用总览增加12,22单位标识
    unitFeeFile.deStandardReleaseYear = unitProject.deStandardReleaseYear;
    unitFeeFile.feeFileName = baseFeeFile.qfName;
    unitFeeFile.feeCode = feeFileRelationDto.code;//参与排序 只是排序
    // unitFeeFile.rateName = baseFeeFileRelation.rateName;//只是展示展示*/
    unitFeeFile.sortNo = parseInt(baseFeeFile.sortNo);
    unitFeeFile.anwenRateAdd = 0;
    //备份安文费增加费率备份
    unitFeeFile.anwenRateAddBackUp = unitFeeFile.anwenRateAdd;
    unitFeeFile.recDate = DateUtils.now('YYYY-MM-DD HH:mm:ss:SSS');
    let { baseManageRateService, baseAnwenRateService, baseGsjRateService } = this.service;

    //let baseManageRate = await baseManageRateService.queryByRateCode(baseFeeFileRelation.rateCode);
    let taxCalculationMethod = unitProject.projectTaxCalculation.taxCalculationMethod;

    //let unitIs2022 = PricingFileFindUtils.getConstructDeStandard(unitProject.constructId) == ConstantUtil.DE_STANDARD_22;
    let unitIs2022 = PricingFileFindUtils.is22Unit(unitProject);

    let gclb = null;
    let engineeringLocation = null;
    let adjacentRoadsNum = null;
    let floorSpace = null;
    let municipalEngineeringCost = null;
    let flag = false;

    // if (ObjectUtils.isNotEmpty(unitProject.feeFiles)){
    //   unitProject.feeFiles[0].unitFeeDescription.forEach(k =>{
    //     if (k.name == "工程类别"){
    //       gclb = k.context;
    //     }
    //     if (k.name == "工程所在地"){
    //       engineeringLocation = k.context;
    //     }
    //     if (k.name == "临路面数"){
    //       adjacentRoadsNum = k.context;
    //     }
    //   });
    //   floorSpace = FloorSpaceEnum.MORE_THAN_10000_SQUARE_METERS.code;
    //   municipalEngineeringCost = MunicipalEngineeringCostEnum.LESS_THAN_50_MILLION.code;
    // }else {
      // gclb = EngineeringTypeEnum.MANAGEMENT_FEE3.code
      // engineeringLocation = EngineeringLocationEnum.CITY_PROPER.code;
      // adjacentRoadsNum=NO_WAY.code;
      // floorSpace = FloorSpaceEnum.MORE_THAN_10000_SQUARE_METERS.code;
      // municipalEngineeringCost = MunicipalEngineeringCostEnum.LESS_THAN_50_MILLION.code;
      let singleProject = PricingFileFindUtils.getSingleProject( unitProject.constructId,unitProject.spId);
      let Project = PricingFileFindUtils.getProjectObjById(unitProject.constructId);
      if(singleProject && singleProject.unitFeeDescriptionList ){
        let unitFeeDescriptionList = ObjectUtil.cloneDeep(singleProject.unitFeeDescriptionList);
        unitFeeFile.unitFeeDescription = unitFeeDescriptionList.filter(e=>e.name !=='市政工程造价');
        gclb = unitFeeDescriptionList.find(res => res.name === '工程类别').context;
        engineeringLocation = unitFeeDescriptionList.find(res => res.name === '工程所在地').context;
        adjacentRoadsNum = unitFeeDescriptionList.find(res => res.name === '临路面数').context;
        floorSpace = unitFeeDescriptionList.find(res => res.name === '建筑面积').context;
        municipalEngineeringCost = unitFeeDescriptionList.find(res => res.name === '市政工程造价').context;
      }else if(Project && Project.unitFeeDescriptionList) {
        let unitFeeDescriptionList =  ObjectUtil.cloneDeep(Project.unitFeeDescriptionList);
        unitFeeFile.unitFeeDescription = unitFeeDescriptionList.filter(e=>e.name !=='市政工程造价');
        gclb = unitFeeDescriptionList.find(res => res.name === '工程类别').context;
        engineeringLocation = unitFeeDescriptionList.find(res => res.name === '工程所在地').context;
        adjacentRoadsNum = unitFeeDescriptionList.find(res => res.name === '临路面数').context;
        floorSpace = unitFeeDescriptionList.find(res => res.name === '建筑面积').context;
        municipalEngineeringCost = unitFeeDescriptionList.find(res => res.name === '市政工程造价').context;
    }else {
        flag = true;
        gclb = EngineeringTypeEnum.MANAGEMENT_FEE3.code
        engineeringLocation = EngineeringLocationEnum.CITY_PROPER.code;
        adjacentRoadsNum=NO_WAY.code;
        floorSpace = FloorSpaceEnum.MORE_THAN_10000_SQUARE_METERS.code;
        municipalEngineeringCost = MunicipalEngineeringCostEnum.LESS_THAN_50_MILLION.code;
      }

    // 管理费  利润
    let baseManageRate = await baseManageRateService.queryByQfCode(baseFeeFile.qfCode, unitIs2022, null, taxCalculationMethod);
    if (!ObjectUtils.isEmpty(baseManageRate)) {
      unitFeeFile.managementFee = parseFloat(baseManageRate.managementFee3);

      unitFeeFile.profit = parseFloat(baseManageRate.profit3);

      if (gclb === EngineeringTypeEnum.MANAGEMENT_FEE3.code) {
        unitFeeFile.managementFee = parseFloat(baseManageRate.managementFee3);
        unitFeeFile.profit = parseFloat(baseManageRate.profit3);
      } else if (gclb === EngineeringTypeEnum.MANAGEMENT_FEE2.code) {
        unitFeeFile.managementFee = parseFloat(baseManageRate.managementFee2);
        unitFeeFile.profit = parseFloat(baseManageRate.profit2);
      } else {
        unitFeeFile.managementFee = parseFloat(baseManageRate.managementFee1);
        unitFeeFile.profit = parseFloat(baseManageRate.profit1);
      }

      //备份 管理费
      unitFeeFile.managementFeeBackUp = unitFeeFile.managementFee;
      //备份 利润
      unitFeeFile.profitBackUp = unitFeeFile.profit;
    }
    let baseAnwenRate = await baseAnwenRateService.queryByLibraryCode(baseFeeFile.qfName
      , feeFileRelationDto.libraryCode
      , engineeringLocation
      , adjacentRoadsNum,
        floorSpace,
        municipalEngineeringCost, unitIs2022, taxCalculationMethod);
    if (!ObjectUtils.isNull(baseAnwenRate)) {
      unitFeeFile.anwenRateBase = parseFloat(baseAnwenRate.anwenRate);

      //备份安文费基本费率
      unitFeeFile.anwenRateBaseBackUp = unitFeeFile.anwenRateBase;
    }
    //规费
    //let baseGsjRateDTO = await baseGsjRateService.queryByCode(baseFeeFileRelation.projectType);
    let baseGsjRateDTO = await baseGsjRateService.queryByCode(feeFileRelationDto.projectType, unitIs2022);

    if (!ObjectUtils.isNull(baseGsjRateDTO)) {
      unitFeeFile.fees = parseFloat(baseGsjRateDTO.rate);
      //备份规费
      unitFeeFile.feesBackUp = unitFeeFile.fees;
    }
    unitProject.feeFiles.push(unitFeeFile);
    //针对市政工程的查询
    // if (flag) {
      let feeFilesBySHIZHENG = [];
      if (unitIs2022) {
        feeFilesBySHIZHENG = await this.service.baseFeeFileRelationService.getFeeFilesByLibraryCode('2022-SZGC-DEK', unitIs2022);
      } else {
        feeFilesBySHIZHENG = await this.service.baseFeeFileRelationService.getFeeFilesByLibraryCode('2012-SZGC-DEK', unitIs2022);
      }
      this.batchSaveUnitFeeDescription(unitProject, baseFeeFile.qfName, unitFeeFile, feeFilesBySHIZHENG,flag);
    // }
    return unitFeeFile;
  }


  batchSaveUnitFeeDescription(unitProjectDTO, feeFileName, dto, feeFilesBySHIZHENG,flag) {
    if (flag){
      dto.unitFeeDescription = new Array()
      let dto1 = new UnitFeeDescription();
      dto1.sequenceNbr = Snowflake.nextId();
      dto1.unitId = unitProjectDTO.sequenceNbr;
      dto1.singleId = unitProjectDTO.spId;
      dto1.unitFeeFileId = dto.sequenceNbr;
      dto1.constructId = unitProjectDTO.constructId;
      dto1.feeFileCode = dto.feeFileCode;
      dto1.sortNo = 1;
      dto1.name = '工程类别';
      dto1.context = MANAGEMENT_FEE3.code;
      dto.unitFeeDescription.push(dto1);

      let dto2 = new UnitFeeDescription();
      dto2.sequenceNbr = Snowflake.nextId();
      dto2.unitFeeFileId = dto.sequenceNbr;
      dto2.singleId = unitProjectDTO.spId;
      dto2.unitId = unitProjectDTO.sequenceNbr;
      dto2.constructId = unitProjectDTO.constructId;
      dto2.feeFileCode = dto.feeFileCode;
      dto2.sortNo = 2;
      dto2.name = '工程所在地';
      dto2.context = EngineeringLocationEnum.CITY_PROPER.code;
      dto.unitFeeDescription.push(dto2);

      let dto3 = new UnitFeeDescription();
      dto3.sequenceNbr = Snowflake.nextId();
      dto3.singleId = unitProjectDTO.spId;
      dto3.unitFeeFileId = dto.sequenceNbr;
      dto3.unitId = unitProjectDTO.sequenceNbr;
      dto3.constructId = unitProjectDTO.constructId;
      dto3.feeFileCode = dto.feeFileCode;
      dto3.sortNo = 3;
      dto3.name = '临路面数';
      dto3.context = AdjacentRoadsNumEnum.NO_WAY.code;
      dto.unitFeeDescription.push(dto3);

      let dto4 = new UnitFeeDescription();
      dto4.sequenceNbr = Snowflake.nextId();
      dto4.singleId = unitProjectDTO.spId;
      dto4.unitFeeFileId = dto.sequenceNbr;
      dto4.unitId = unitProjectDTO.sequenceNbr;
      dto4.constructId = unitProjectDTO.constructId;
      dto4.feeFileCode = dto.feeFileCode;
      dto4.sortNo = 4;
      dto4.name = '建筑面积';
      dto4.context = FloorSpaceEnum.MORE_THAN_10000_SQUARE_METERS.code;
      dto.unitFeeDescription.push(dto4);

    }



    //二级取费专业
    let spDto = new UnitFeeDescription();
    spDto.sequenceNbr = Snowflake.nextId();
    spDto.singleId = unitProjectDTO.spId;
    spDto.unitFeeFileId = dto.sequenceNbr;
    spDto.unitId = unitProjectDTO.sequenceNbr;
    spDto.constructId = unitProjectDTO.constructId;
    spDto.feeFileCode = dto.feeFileCode;
    spDto.sortNo = 5;
    spDto.name = '二级取费专业';
    let secondInstallationProjectName = unitProjectDTO.secondInstallationProjectName;
    if (!secondInstallationProjectName) {
      secondInstallationProjectName = '建筑智能化系统设备安装工程';
    }
    spDto.context = secondInstallationProjectName;
    dto.unitFeeDescription.push(spDto);


    if (ConstantUtil.PRECAST_RATE_FEE_NAME == feeFileName) {
      let dto5 = new UnitFeeDescription();
      dto5.sequenceNbr = Snowflake.nextId();
      dto5.singleId = unitProjectDTO.spId;
      dto5.unitFeeFileId = dto.sequenceNbr;
      dto5.unitId = unitProjectDTO.sequenceNbr;
      dto5.constructId = unitProjectDTO.constructId;
      dto5.feeFileCode = dto.feeFileCode;
      dto5.sortNo = 6;
      dto5.name = '预制率';
      dto5.context = PrecastRateEnum.PRECAST_RATE_15_30.code;
      dto.unitFeeDescription.push(dto5);
    }
    feeFilesBySHIZHENG = feeFilesBySHIZHENG.map(item => item.qfName);
    if (feeFilesBySHIZHENG.includes(feeFileName)) {
      let dto6 = new UnitFeeDescription();
      dto6.sequenceNbr = Snowflake.nextId();
      dto6.unitFeeFileId = dto.sequenceNbr;
      dto6.singleId = unitProjectDTO.spId;
      dto6.unitId = unitProjectDTO.sequenceNbr;
      dto6.constructId = unitProjectDTO.constructId;
      dto6.feeFileCode = dto.feeFileCode;
      dto6.sortNo = 7;
      dto6.name = '市政工程造价';
      dto6.context = MunicipalEngineeringCostEnum.LESS_THAN_50_MILLION.code;
      dto.unitFeeDescription.push(dto6);
    }
  }

  async setDefaultFeeDescription(feeCollectionVO,arg,obj) {


    let unitFeeDescriptionDTOList = new Array();

    let dto1 = new UnitFeeDescription();
    dto1.sequenceNbr = Snowflake.nextId();
    dto1.singleId = arg.singleId;
    dto1.constructId = arg.constructId;
    dto1.sortNo = 1;
    dto1.name = '工程类别';
    dto1.context = MANAGEMENT_FEE3.code;
    dto1.optionList = Object.keys(EngineeringTypeEnum).map((key) => {
      const code = EngineeringTypeEnum[key].code.toString();
      const desc = EngineeringTypeEnum[key].desc;
      return { [code]: desc };
    });
    unitFeeDescriptionDTOList.push(dto1);

    let dto2 = new UnitFeeDescription();
    dto2.sequenceNbr = Snowflake.nextId();
    dto2.singleId = arg.singleId;
    dto2.constructId = arg.constructId;
    dto2.sortNo = 2;
    dto2.name = '工程所在地';
    dto2.context = EngineeringLocationEnum.CITY_PROPER.code;
    dto2.optionList = Object.keys(EngineeringLocationEnum).map((key) => {
      const code = EngineeringLocationEnum[key].code.toString();
      const desc = EngineeringLocationEnum[key].desc;
      return { [code]: desc };
    });
    unitFeeDescriptionDTOList.push(dto2);

    let dto3 = new UnitFeeDescription();
    dto3.sequenceNbr = Snowflake.nextId();
    dto3.singleId = arg.singleId;
    dto3.constructId = arg.constructId;
    dto3.sortNo = 3;
    dto3.name = '临路面数';
    dto3.context = AdjacentRoadsNumEnum.NO_WAY.code;
    dto3.optionList = Object.keys(AdjacentRoadsNumEnum).map((key) => {
      const code = AdjacentRoadsNumEnum[key].code.toString();
      const desc = AdjacentRoadsNumEnum[key].desc;
      return { [code]: desc };
    });
    unitFeeDescriptionDTOList.push(dto3);

    let dto4 = new UnitFeeDescription();
    dto4.sequenceNbr = Snowflake.nextId();
    dto4.singleId = arg.singleId;
    dto4.constructId = arg.constructId;
    dto4.sortNo = 4;
    dto4.name = '建筑面积';
    dto4.context = FloorSpaceEnum.MORE_THAN_10000_SQUARE_METERS.code;
    dto4.optionList = Object.keys(FloorSpaceEnum).map((key) => {
      const code = FloorSpaceEnum[key].code.toString();
      const desc = FloorSpaceEnum[key].desc;
      return { [code]: desc };
    });
    unitFeeDescriptionDTOList.push(dto4);

    let dto6 = new UnitFeeDescription();
    dto6.sequenceNbr = Snowflake.nextId();
    dto6.singleId = arg.singleId;
    dto6.constructId = arg.constructId;
    dto6.sortNo = 5;
    dto6.name = '市政工程造价';
    dto6.context = MunicipalEngineeringCostEnum.LESS_THAN_50_MILLION.code;
    dto6.optionList = Object.keys(MunicipalEngineeringCostEnum).map((key) => {
      const code = MunicipalEngineeringCostEnum[key].code.toString();
      const desc = MunicipalEngineeringCostEnum[key].desc;
      return { [code]: desc };
    });
    unitFeeDescriptionDTOList.push(dto6);
    obj.unitFeeDescriptionList = ObjectUtil.cloneDeep(unitFeeDescriptionDTOList);
    feeCollectionVO.unitFeeDescriptionList = unitFeeDescriptionDTOList;
  }

  async setDefaultCostOverviews(feeCollectionVO,feeCollectionList,obj,args){
    let costOverviewList = new Array();
    let objC = ObjectUtil.cloneDeep(obj)
    for (const i in feeCollectionList) {
      let unitFeeFileDTO = feeCollectionList[i];
      if (objC.costOverview){
      let element = objC.costOverview.find(e=> e.feeFileCode === unitFeeFileDTO.feeFileCode && e.deStandardReleaseYear === unitFeeFileDTO.deStandardReleaseYear);
         if (element){
           costOverviewList.push(element);
           continue;
            }
      }
      let costOverviewDto = new UnitFeeFile();
          costOverviewDto.setCostOverview(Snowflake.nextId(),unitFeeFileDTO.feeCode,unitFeeFileDTO.feeFileId,unitFeeFileDTO.feeFileCode,unitFeeFileDTO.feeFileName,
          unitFeeFileDTO.sortNo,unitFeeFileDTO.managementFeeBackUp, unitFeeFileDTO.profitBackUp,unitFeeFileDTO.anwenRateBaseBackUp,unitFeeFileDTO.anwenRateAddBackUp,unitFeeFileDTO.managementFeeBackUp,
          unitFeeFileDTO.profitBackUp,unitFeeFileDTO.anwenRateBaseBackUp,unitFeeFileDTO.anwenRateAddBackUp);
          costOverviewDto.sortNum = parseInt(i) + 1;
          costOverviewDto.deStandardReleaseYear = unitFeeFileDTO.deStandardReleaseYear;
          if (unitFeeFileDTO.deStandardReleaseYear === "12"){
            costOverviewDto.fees = unitFeeFileDTO.fees;
            costOverviewDto.feesBackUp = unitFeeFileDTO.feesBackUp;
          }
          costOverviewDto.unitFeeDescription = objC.unitFeeDescriptionList;
          await this.feeDescriptionChangeResult(objC.unitFeeDescriptionList,costOverviewDto,args)
      if (costOverviewDto){
        costOverviewList.push(costOverviewDto);
      }
  }
    obj.costOverview = ObjectUtil.cloneDeep(costOverviewList);
    feeCollectionVO.costOverview = costOverviewList;
  }

  /**
   * 根据选择的单位或者工程项 和 取费文件id 获取费用总览和费率说明数据
   */
  async feeCollectionData(arg) {

    let levelType = arg.levelType;
    let constructId = arg.constructId;
    let feeFileId = arg.feeFileId;

    let vo = new FeeCollectionVO();

    // 查询出工程项目下的所有取费文件
    let feeCollectionList = await this.getFeeCollectionList(arg);
    //对于项目的取费文件进行去重
    let feeFiles = ArrayUtil.distinctList(feeCollectionList, 'feeFileId');
    let feeFile = feeFiles.find((item) => item.feeFileId == feeFileId);

    if (ProjectLevelConstant.construct === levelType) {
      let project = PricingFileFindUtils.getProjectObjById(constructId);
      if(project.unitFeeDescriptionList){
        vo.unitFeeDescriptionList =  ObjectUtil.cloneDeep(project.unitFeeDescriptionList);
      }else {
        await this.setDefaultFeeDescription(vo,arg,project);
      }
      if (ObjectUtils.isNotEmpty(feeFiles)) {
        if (project.costOverview  && project.costOverview.length === feeFiles.length){
          vo.costOverview =  ObjectUtil.cloneDeep(project.costOverview);
        }else {
          await this.setDefaultCostOverviews(vo,feeFiles,project,arg);
        }
      }
      await this.comparisonCostOverview(vo, arg, feeCollectionList);
    }else if( ProjectLevelConstant.single === levelType){
      let singleProject = await PricingFileFindUtils.getSingleProject(arg.constructId,arg.singleId);
      if(singleProject.unitFeeDescriptionList){
        vo.unitFeeDescriptionList =  ObjectUtil.cloneDeep(singleProject.unitFeeDescriptionList);
      }else {
        let project = PricingFileFindUtils.getProjectObjById(constructId);
        if (project.unitFeeDescriptionList){
          singleProject.unitFeeDescriptionList = ObjectUtil.cloneDeep(project.unitFeeDescriptionList);
          vo.unitFeeDescriptionList = ObjectUtil.cloneDeep(singleProject.unitFeeDescriptionList);
        }else {
          await this.setDefaultFeeDescription(vo,arg,singleProject);
        }
      }
      if (ObjectUtils.isNotEmpty(feeFiles)) {
        if (singleProject.costOverview && singleProject.costOverview.length === feeFiles.length){
          vo.costOverview = ObjectUtil.cloneDeep(singleProject.costOverview);
        }else {
          await this.setDefaultCostOverviews(vo,feeFiles,singleProject,arg);
          if (arg.addFlag){
            return ;
          }
        }
      }
      await this.comparisonCostOverview(vo, arg, feeCollectionList);
    }
    else {
      //单位
      // 费用总览列表数据
      await this.setCostOverview(vo, feeCollectionList);
      // 设置费率说明数据
      //await this.setUnitFeeDescriptionData(vo, feeFile,arg);
      await this.setUnitFeeDescriptionData(vo, feeFiles, arg);

      await this.comparisonCostOverview(vo, arg, feeCollectionList);
    }

    return vo;
  }

  async handleUnitAddFeeFile(constructId, singleId, unitId, deId, is2022) {
    let unitProjectDTO = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    if (ObjectUtils.isEmpty(unitProjectDTO)) {
      return null;
    }
    //主工程是否是2022
    //let unitIs2022 = PricingFileFindUtils.getConstructDeStandard(constructId) == ConstantUtil.DE_STANDARD_22;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let unitIs2022 = PricingFileFindUtils.is22Unit(unit);
    let selectIs2012 = !is2022;
    let baseDeDTO = await this.getBaseDe(deId, unitIs2022 ? BaseDe2022 : BaseDe);
    if (ObjectUtils.isEmpty(baseDeDTO)) {
      return null;
    }
    let qfCode = baseDeDTO.qfCode;
    //TODO 映射处理 找到对应2022的rateCode
    if (unitIs2022 && selectIs2012) {
      let rateData2022 = get2022BY2012(qfCode);
      if (ObjectUtils.isEmpty(rateData2022)) {
        return null;
      }
      qfCode = rateData2022.qfCode;
    }/*else {
            let rateCode = baseDeDTO.rateCode;
            // 取费文件关联关系
            let {baseFeeFileRelationService} = this.service;
             baseFeeFileRelationDTO = await baseFeeFileRelationService.queryFeeFileRelationByRateCodeGroupByQfCode(rateCode,selectIs2012);

            if (ObjectUtils.isEmpty(baseFeeFileRelationDTO)) {
                return null;
            }

        }*/
    let baseFeeFileRelationDTO = await this.service.baseFeeFileRelationService.getFeeFileProjectByQfCode(qfCode, unitIs2022);
    if (ObjectUtils.isEmpty(baseFeeFileRelationDTO)) {
      return null;
    }
    //获取当前单位下的取费文件数据
    let unitFeeFileDTO = unitProjectDTO.feeFiles.find((item) => item.feeFileCode === qfCode);
    // 如果这个单位已经有这个取费文件了  那么直接返回这个取费文件
    // 如果不存在这个取费文件  那么就给这个单位下新增一个取费文件
    if (!ObjectUtils.isEmpty(unitFeeFileDTO)) {
      return unitFeeFileDTO;
    }

    let baseFeeFileDTO = await this.getBaseFeeFile(qfCode, unitIs2022);
    if (ObjectUtils.isEmpty(baseFeeFileDTO)) {
      return null;
    }
    return await this.addFeeFile(baseFeeFileDTO, unitProjectDTO, baseFeeFileRelationDTO);
  }

  async getBaseDe(deId, type) {
    const baseDeDTO = await this.app.appDataSource.getRepository(type).findOne({
      where: {
        sequenceNbr: deId
      }
    });
    return baseDeDTO;
  }

  async getBaseFeeFile(qfCode, unitIs2022) {
    const baseFeeFileDTO = await this.app.appDataSource.getRepository(unitIs2022 ? BaseFeeFile2022 : BaseFeeFile).findOne({
      where: {
        qfCode
      }
    });
    return baseFeeFileDTO;
  }

  /**
   * 修改取费文件-分部分项用
   */
  async updateFBFXFeeFile(constructId, singleId, unitId, qfcode) {
    let unitProjectDTO = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    if (ObjectUtils.isEmpty(unitProjectDTO)) {
      return null;
    }
    let unitIs2022 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
    // 取费文件关联关系
    let { baseFeeFileRelationService } = this.service;
    let baseFeeFileRelationDTO = await baseFeeFileRelationService.getFeeFileProjectByQfCode(qfcode, unitIs2022);
    if (ObjectUtils.isEmpty(baseFeeFileRelationDTO)) {
      return null;
    }
    //获取当前单位下的取费文件数据
    let unitFeeFileDTO = unitProjectDTO.feeFiles.find((item) => item.feeFileCode === baseFeeFileRelationDTO.qfCode);
    // 如果这个单位已经有这个取费文件了  那么直接返回这个取费文件
    // 如果不存在这个取费文件  那么就给这个单位下新增一个取费文件
    if (!ObjectUtils.isEmpty(unitFeeFileDTO)) {
      return unitFeeFileDTO;
    }
    let baseFeeFileDTO = await this.getBaseFeeFile(baseFeeFileRelationDTO.qfCode, unitIs2022);
    if (ObjectUtils.isEmpty(baseFeeFileDTO)) {
      return null;
    }
    return await this.addFeeFile(baseFeeFileDTO, unitProjectDTO, baseFeeFileRelationDTO);
  }

  /**
   * 修改取费文件-导入xml用
   */
  async updateFeeFile(unitProjectDTO, qfcode) {
    // let unitIs2022 = PricingFileFindUtils.getConstructDeStandard(unitProjectDTO.constructId) == ConstantUtil.DE_STANDARD_22;
    let unitIs2022 = PricingFileFindUtils.is22Unit(unitProjectDTO);
    // 取费文件关联关系
    let { baseFeeFileRelationService } = this.service;
    let baseFeeFileRelationDTO = await baseFeeFileRelationService.getFeeFileProjectByQfCode(qfcode, unitIs2022);
    //let baseFeeFileRelationDTO = await baseFeeFileRelationService.getFeeFileRelationByQfCode(qfcode);
    if (ObjectUtils.isEmpty(baseFeeFileRelationDTO)) {
      return null;
    }
    //获取当前单位下的取费文件数据
    let unitFeeFileDTO = unitProjectDTO.feeFiles.find((item) => item.feeFileCode === baseFeeFileRelationDTO.qfCode);
    // 如果这个单位已经有这个取费文件了  那么直接返回这个取费文件
    // 如果不存在这个取费文件  那么就给这个单位下新增一个取费文件
    if (!ObjectUtils.isEmpty(unitFeeFileDTO)) {
      return unitFeeFileDTO;
    }
    let baseFeeFileDTO = await this.getBaseFeeFile(baseFeeFileRelationDTO.qfCode, unitIs2022);
    if (ObjectUtils.isEmpty(baseFeeFileDTO)) {
      return null;
    }
    return await this.addFeeFile(baseFeeFileDTO, unitProjectDTO, baseFeeFileRelationDTO);
  }


  /**
   * 获取工程项目 或者单位工程级别的政策文件数据
   * @param arg
   * @return {Promise<void>}
   */
  async policyDocument(arg) {
    let constructId = arg.constructId;
    //1查询工程项目 2查询单位工程
    let type = arg.type;

    let constructConfigDTO = PricingFileFindUtils.getProjectObjById(constructId);
    let vo = new FeeCollectionVO();
    let basePolicyDocumentMap = {};
    if (!ObjectUtils.isEmpty(constructConfigDTO)) {

      if (type === 1 || type === 2) {
        vo.rgfId = constructConfigDTO.rgfId;
      } else if (type === 3   ) {
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        vo.rgfId = unit.rgfId;
      }
      vo.gfId = constructConfigDTO.gfId;
      vo.awfId = constructConfigDTO.awfId;
      // 政策文件
      let baseAreaList = await this.app.appDataSource.getRepository(BaseArea).find({
        where: {
          pid: constructConfigDTO.ssProvince
        }
      });
      if (!ObjectUtils.isEmpty(baseAreaList)) {
        let areaIds = baseAreaList.map(obj => obj.sequenceNbr);
        // 根据省下面的市一级  查询对应的人工费
        let rgfDocumentList = await this.app.appDataSource.getRepository(BasePolicyDocument).find({
          where: {
            fileType: PolicyDocumentTypeEnum.RGF.code,
            areaId: In(areaIds)
          }
        });
        if (!ObjectUtils.isEmpty(rgfDocumentList)) {
          //按照时间倒序排序
          rgfDocumentList.sort(function(a, b) {
            return b.fileDate.localeCompare(a.fileDate);
          });

          for (const basePolicyDocumentDTO of rgfDocumentList) {
            //去掉政策文件的.pdf
            basePolicyDocumentDTO.name = basePolicyDocumentDTO.name.replace('.pdf', '');
            if (basePolicyDocumentDTO.sequenceNbr === constructConfigDTO.rgfId) {
              basePolicyDocumentDTO.selectFlag = 1;
              continue;
            }
          }
          //根据城市名称分组
          const map = rgfDocumentList.reduce((groups, item) => {
            const { cityName } = item;
            groups[cityName] = groups[cityName] || [];
            groups[cityName].push(item);
            return groups;
          }, {});
          basePolicyDocumentMap[PolicyDocumentTypeEnum.RGF.desc] = map;
        }
        // 再查询安防费率和规费
        let resMap = await this.getPolicyDocumentData();
        for (const k in resMap) {
          let typeEnum = Object.values(PolicyDocumentTypeEnum).find(item => item.code == k);
          if (!ObjectUtils.isEmpty(typeEnum)) {
            let v = resMap[k];
            if (!ObjectUtils.isEmpty(v)) {
              for (const basePolicyDocumentDTO of v) {
                if (basePolicyDocumentDTO.sequenceNbr === constructConfigDTO.gfId || basePolicyDocumentDTO.sequenceNbr === constructConfigDTO.awfId) {
                  basePolicyDocumentDTO.selectFlag = 1;
                }
              }
            }
            basePolicyDocumentMap[typeEnum.desc] = v;
          }
        }
      }
    }
    vo.basePolicyDocumentMap = basePolicyDocumentMap;
    return vo;
  }


  /**
   * 查询安防费率和规费
   */
  async getPolicyDocumentData() {
    let param = [PolicyDocumentTypeEnum.AFF.code, PolicyDocumentTypeEnum.GF.code];
    let basePolicyDocumentList = await this.app.appDataSource.getRepository(BasePolicyDocument).find({
      where: {
        fileType: In(param)
      }
    });
    let resMap = {};
    if (ObjectUtils.isEmpty(basePolicyDocumentList)) {
      return resMap;
    }
    for (const i of basePolicyDocumentList) {
      i.name = i.name.replace('.pdf', '');
      let basePolicyDocumentDTOList = resMap[i.fileType];
      if (ObjectUtils.isEmpty(basePolicyDocumentDTOList)) {
        resMap[i.fileType] = [i];
      } else {
        basePolicyDocumentDTOList.push(i);
      }
    }
    return resMap;
  }

  /**
   * 单次保存费用总览数据，保存单位工程级别的费用总览数据
   */
  saveCostOverview(arg) {
    // let constructId = arg.constructId;
    // let singleId = arg.singleId;
    // let unitId = arg.unitId;
    // 查询出工程项目下的所有取费文件
    if (!ObjectUtils.isEmpty(arg.managementFee)){
      arg.managementFee = NumberUtil.glfRateFormat(arg.managementFee);
    }
    if (!ObjectUtils.isEmpty(arg.profit)){
      arg.profit = NumberUtil.lrRateFormat(arg.profit);
    }
    if (!ObjectUtils.isEmpty(arg.anwenRateBase)){
      arg.anwenRateBase = NumberUtil.awfRateFormat(arg.anwenRateBase);
    }
    if (!ObjectUtils.isEmpty(arg.anwenRateAdd)){
      arg.anwenRateAdd = NumberUtil.awfRateFormat(arg.anwenRateAdd);
    }
    let feeCollectionList = this.getFeeCollectionList(arg);
    let feeFile = feeCollectionList.find(res => res.sequenceNbr === arg.sequenceNbr);
    ConvertUtil.deepCopyIgnore(arg, feeFile, ['unitId', 'constructId', 'feeFileId', 'feeFileName', 'feeFileCode', 'sortNo']);
    let constructId = arg.constructId;
    let singleId = arg.singleId;
    let unitId = arg.unitId;
    let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);

    this._calculationByFeeFile(unit, feeFile);

    // let szFeeFileName = null;
    // // 如果当前被修改的是主取费文件
    // if(feeFile.defaultFeeFlag == 1){
    //   szFeeFileName = "随主工程";
    // }
    // let calculationTool =new CalculationTool({constructId, singleId, unitId, allData:unit.measureProjectTables});
    // unit.measureProjectTables.filter(
    //      item=>item.kind=="04"&&( item.qfCode==feeFile.feeFileCode || item.qfName==szFeeFileName)
    //  ).forEach(item=>{
    //    calculationTool.calculationChian(item)
    //  });
    // let calculationToolCsxm =new CalculationTool({constructId, singleId, unitId, allData:unit.itemBillProjects});
    // unit.itemBillProjects.filter(
    //     item=>item.kind=="04" && ( item.qfCode==feeFile.feeFileCode || item.qfName==szFeeFileName)
    // ).forEach(item=>{
    //   calculationToolCsxm.calculationChian(item)
    // });

  }


  _calculationByFeeFile(unit, feeFile){
    let {constructId, spId:singleId, sequenceNbr:unitId} = unit;
    let szFeeFileName = null;
    // 如果当前被修改的是主取费文件
    if(feeFile.defaultFeeFlag == 1){
      szFeeFileName = "随主工程";
    }
    let calculationTool =new CalculationTool({constructId, singleId, unitId, allData:unit.measureProjectTables});
    unit.measureProjectTables.filter(
        item=>item.kind=="04"&&( item.qfCode==feeFile.feeFileCode || item.qfName==szFeeFileName)
    ).forEach(item=>{
      calculationTool.calculationChian(item)
    });
    let calculationToolCsxm =new CalculationTool({constructId, singleId, unitId, allData:unit.itemBillProjects});
    unit.itemBillProjects.filter(
        item=>item.kind=="04" && ( item.qfCode==feeFile.feeFileCode || item.qfName==szFeeFileName)
    ).forEach(item=>{
      calculationToolCsxm.calculationChian(item)
    });
  }


  /**
   * 恢复默认费率
   */
  async restoreDefaultFee(arg) {
    let { levelType, constructId, singleId, unitId, sequenceNbr, unitFeeDescriptionList } = arg;
    // 查询出工程项目下的所有取费文件
    let result = [];
    if (levelType == 1 ){
      let project = PricingFileFindUtils.getProjectObjById(constructId);
      if (ObjectUtils.isEmpty(project.costOverview) && ObjectUtils.isEmpty(arg.unitFeeDescriptionList)){
        return;
      }
      for (let i = 0; i < project.costOverview.length; i++ ){
        result.push(await this.feeDescriptionChangeResult(arg.unitFeeDescriptionList, project.costOverview[i], arg))
      }
      let vo = new FeeCollectionVO();
      vo.costOverview = result
      return vo;
    }
    if (levelType == 2) {
      let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);
      if  (ObjectUtils.isEmpty(singleProject.costOverview) || ObjectUtils.isEmpty(arg.unitFeeDescriptionList) ){
        return;
      }
      for (let i = 0; i < singleProject.costOverview.length; i++ ){
        result.push(await this.feeDescriptionChangeResult(arg.unitFeeDescriptionList, singleProject.costOverview[i], arg))
      }
      let vo = new FeeCollectionVO();
      vo.costOverview = result
      return vo;
      // return await this.feeDescriptionChangeResult(arg.unitFeeDescriptionList, singleProject.costOverview, arg);

    }
    let feeCollectionList = this.getFeeCollectionList(arg);
    for (const feeFile of feeCollectionList) {
      // 设置费用总览列表数据
      await this.feeDescriptionChangeResult(feeFile.unitFeeDescription, feeFile, arg);
    }

    let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
    let calculationTool =new CalculationTool({constructId, singleId, unitId, allData:unit.measureProjectTables});
    unit.measureProjectTables.filter(item=>item.kind=="04").forEach(item=>{
      calculationTool.calculationChian(item)
    });
    let calculationToolCsxm =new CalculationTool({constructId, singleId, unitId, allData:unit.itemBillProjects});
    unit.itemBillProjects.filter(item=>item.kind=="04").forEach(item=>{
      calculationToolCsxm.calculationChian(item)
    });
  }
  async restoreDefaultFeeBak(arg) {
    let { levelType, constructId, singleId, unitId, sequenceNbr, unitFeeDescriptionList } = arg;
    // 查询出工程项目下的所有取费文件
    let result = [];
    if (levelType == 1 ){
      let project = PricingFileFindUtils.getProjectObjById(constructId);
      for (let i = 0; i < project.costOverview.length; i++ ){
        result.push(await this.feeDescriptionChangeResult(arg.unitFeeDescriptionList, project.costOverview[i], arg))
      }
      let vo = new FeeCollectionVO();
      vo.costOverview = result
      return vo;
    }
    if (levelType == 2) {
      let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);
      for (let i = 0; i < singleProject.costOverview.length; i++ ){
        result.push(await this.feeDescriptionChangeResult(arg.unitFeeDescriptionList, singleProject.costOverview[i], arg))
      }
      let vo = new FeeCollectionVO();
      vo.costOverview = result
      return vo;
      // return await this.feeDescriptionChangeResult(arg.unitFeeDescriptionList, singleProject.costOverview, arg);

    }
    let feeCollectionList = this.getFeeCollectionList(arg);
    for (const feeFile of feeCollectionList) {
      // 设置费用总览列表数据
      await this.feeDescriptionChangeResult(feeFile.unitFeeDescription, feeFile, arg);
    }

    let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
    let calculationTool =new CalculationTool({constructId, singleId, unitId, allData:unit.measureProjectTables});
    unit.measureProjectTables.filter(item=>item.kind=="04").forEach(item=>{
      calculationTool.calculationChian(item)
    });
    let calculationToolCsxm =new CalculationTool({constructId, singleId, unitId, allData:unit.itemBillProjects});
    unit.itemBillProjects.filter(item=>item.kind=="04").forEach(item=>{
      calculationToolCsxm.calculationChian(item)
    });
  }


  async getCostOverview(arg) {
    //获取前端传递的费率说明
    let unitFeeDescription = arg.unitFeeDescriptionList;
    let feeFiles = arg.costOverview;

    // 查询出工程项目下的所有取费文件
    if (ObjectUtils.isEmpty(feeFiles)){
      let feeCollectionList = this.getFeeCollectionList(arg);
      //对于项目的取费文件进行去重
      feeFiles = ArrayUtil.distinctList(feeCollectionList, 'feeFileId');
    }

    let array = [];
    for (let item of feeFiles) {
      // 设置费用总览列表数据
      let deepCopy = ConvertUtil.deepCopy(item);
      arg.singleId = item.singleId;
      arg.unitId = item.unitId;
      let result = await this.feeDescriptionChangeResult(unitFeeDescription, deepCopy, arg);
      if (arg.costOverview){
      }
      array.push(result);

    }
    let vo = new FeeCollectionVO();
    vo.costOverview = array;
    return vo;
  }


  /**
   * 单次保存单位的费用说明数据，保存单位工程级别的费用说明数据
   */
  async saveFeeDescription(arg) {
    // 查询出工程项目下的所有取费文件
    let feeCollectionList = this.getFeeCollectionList(arg);

    let feeFile = feeCollectionList.find(res => res.feeFileId === arg.feeFileId);
    let unitFeeDescriptionList = feeFile.unitFeeDescription;
    let unitFeeDescription = unitFeeDescriptionList.find(res => res.name === arg.name);
    if (arg.name == '二级取费专业') {
      PricingFileFindUtils.getUnit(arg.constructId, arg.singleId, arg.unitId).secondInstallationProjectName = arg.context;
    }


    for (let i = 0; i < feeCollectionList.length; i++) {
      let feeFile = feeCollectionList[i];
      //feeFile.unitFeeDescription.find(i => i.name == arg.name)

      let f =feeFile.unitFeeDescription.find(i => i.name == arg.name);

      if(!ObjectUtils.isEmpty(f)){
        f.context = arg.context;
      }
      // 设置费用总览列表数据
      await this.feeDescriptionChangeResult(feeFile.unitFeeDescription, feeFile, arg);

    }

    //todo 重新计算项目


    let constructId = arg.constructId;
    let singleId = arg.singleId;
    let unitId = arg.unitId;
    this.service.unitPriceService.reCaculate(constructId, singleId, unitId, (c, s, u) => PricingFileFindUtils.getFbFx(c, s, u));
    this.service.unitPriceService.reCaculate(constructId, singleId, unitId, (c, s, u) => PricingFileFindUtils.getCSXM(c, s, u));

    // 触发安文费的重新计算
    await this.service.autoCostMathService.awfCost({ constructId, singleId, unitId });
    //重新计算所有金额  做定额和清单的汇总
    await this.service.unitPriceService.reCacaulateAll(constructId, singleId, unitId, PricingFileFindUtils.getCSXM(constructId, singleId, unitId));
    await this.service.unitPriceService.reCacaulateAll(constructId, singleId, unitId, PricingFileFindUtils.getFbFx(constructId, singleId, unitId));

    await this.service.autoCostMathService.autoCostMath({
      unitId: unitId,
      singleId: singleId,
      constructId: constructId
    });

    await this.service.unitCostCodePriceService.countCostCodePrice({
      constructId: constructId,
      singleId: singleId,
      unitId: unitId
    });
    return true;
  }


  /**
   * 保存选择的工程项目 或单位工程 的政策文件
   */
  async checkPolicyDocument(arg) {
    let constructId = arg.constructId;
    let rgfId = arg.rgfId;
    let awfId = arg.awfId;
    let gfId = arg.gfId;
    let type = arg.type;
    let projectObj = PricingFileFindUtils.getProjectObjById(constructId);

    //以系数计算的措施项目和机械台班中的人工单价参与调整 功能
    let rgfInMeasureAndRPriceInMechanicalAction = projectObj.rgfInMeasureAndRPriceInMechanicalAction;
    let rgfInMeasure  =null;
    if (rgfInMeasureAndRPriceInMechanicalAction == true && !ObjectUtils.isEmpty(rgfId)){
      rgfInMeasure = true;
    }else {
      rgfInMeasure = false;
    }
    /*if (rgfInMeasureAndRPriceInMechanicalAction === false && ObjectUtils.isEmpty(rgfId)){
      rgfInMeasure = false;
    }*/


    //保存 工程项目
    if (type === 1) {
      projectObj.rgfId = rgfId;
      let unitList = PricingFileFindUtils.getUnitList(constructId);

      let promise = null;
      //有可能为空
      if (!ObjectUtils.isEmpty(rgfId)) {
        promise = await this.service.basePolicyDocumentService.queryBySequenceNbr(rgfId);//cl
      }


      if (!ObjectUtils.isEmpty(unitList)) {
        for (let i = 0; i < unitList.length; i++) {
          unitList[i].rgfId = rgfId;
          if (!ObjectUtils.isEmpty(promise)) {
            let name = promise.cityName + promise.pricesource;
            this.service.rcjProcess.rcjRgfChange(unitList[i], promise.zhygLevel1, promise.zhygLevel2, promise.zhygLevel3, name,rgfInMeasure);
          } else {
            this.service.rcjProcess.rcjRgfChange(unitList[i], null,null,null,null,rgfInMeasure);
          }
        }
      }
      // 政策文件修改后  需要触发安装、超高、总价措施费用定额中的“措施中人工费调整”人材机的响应变更
      for (const unit of unitList) {
        // 处理工程项目级别的每个单位的措施中人工费调整变更
        await this.service.cszRgfAdjustProcess.handleCszRgfAdjustForPolicyDocument({
          constructId: unit.constructId,
          singleId: unit.spId,
          unitId: unit.sequenceNbr
        });
      }
      //保存单位工程
    } else if (type === 3) {
      let singleId = arg.singleId;
      let unitId = arg.unitId;
      let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
      unit.rgfId = rgfId;
      if (!ObjectUtils.isEmpty(rgfId)) {
        let promise = await this.service.basePolicyDocumentService.queryBySequenceNbr(rgfId);//cl
        let name = promise.cityName + promise.pricesource;
        this.service.rcjProcess.rcjRgfChange(unit, promise.zhygLevel1, promise.zhygLevel2, promise.zhygLevel3, name,rgfInMeasure);
      } else {
        this.service.rcjProcess.rcjRgfChange(unit, null,null,null,null,rgfInMeasure);
      }
      await this.service.cszRgfAdjustProcess.handleCszRgfAdjustForPolicyDocument({
        constructId: unit.constructId,
        singleId: unit.spId,
        unitId: unit.sequenceNbr
      });
    }

    projectObj.awfId = awfId;
    projectObj.gfId = gfId;
    return ResponseData.success();
  }


  /**
   * 统一应用，处理工程项目级别修改费率说明或者费率总览后的统一应用
   */
  async unifiedUse(arg) {
    //arg.levelType = 1;
    let feeFileId = arg.feeFileId;
    //费用总览
    let costOverview = arg.costOverview;
    let costOverviewO;
    let unitFeeDescriptionO;
    let singleFeeDescription;
    let feeCollectionList = await this.getFeeCollectionList(arg);
    if (ProjectLevelConstant.construct === arg.levelType){
      let project = await PricingFileFindUtils.getProjectObjById(arg.constructId);
      costOverviewO = project.costOverview;
      unitFeeDescriptionO =project.unitFeeDescriptionList;
      if(project.singleProjects[0].costOverview){
        let singleCostOverviewO = project.singleProjects.reduce((acc,e)=>acc.concat(e.costOverview),[]);
        feeCollectionList = feeCollectionList.concat(singleCostOverviewO);
      }
      if(project.singleProjects[0].unitFeeDescriptionList){
        singleFeeDescription = project.singleProjects.map(e=> e.unitFeeDescriptionList)
      }
    }else if (ProjectLevelConstant.single === arg.levelType){
      let singleProject = await PricingFileFindUtils.getSingleProject(arg.constructId,arg.singleId);
      costOverviewO = singleProject.costOverview
      unitFeeDescriptionO =singleProject.unitFeeDescriptionList;
    }
    //获取项目的所有取费文件

    //循环前端的费用总览数据
    for (const unitFeeFileDTO of costOverview) {
      if (costOverviewO){
        let costOverviewOne = costOverviewO.find(e => e.feeFileCode === unitFeeFileDTO.feeFileCode && e.deStandardReleaseYear === unitFeeFileDTO.deStandardReleaseYear);
        if (costOverviewOne){
          if (arg.isDecChange){
            await this.feeDescriptionChangeResult(arg.unitFeeDescription,costOverviewOne,arg);
          }
          costOverviewOne.managementFee =  NumberUtil.glfRateFormat(unitFeeFileDTO.managementFee);
          costOverviewOne.profit =  NumberUtil.lrRateFormat(unitFeeFileDTO.profit);
          if (unitFeeFileDTO.fees){
            costOverviewOne.fees =  NumberUtil.gfRateFormat(unitFeeFileDTO.fees);
          }
          costOverviewOne.anwenRateBase =  NumberUtil.awfRateFormat(unitFeeFileDTO.anwenRateBase);
          costOverviewOne.anwenRateAdd =  NumberUtil.awfRateFormat(unitFeeFileDTO.anwenRateAdd);
        }
      }
      //费用总览 统一应用
      let filter = feeCollectionList.filter(obj => obj.feeFileCode == unitFeeFileDTO.feeFileCode && obj.deStandardReleaseYear == unitFeeFileDTO.deStandardReleaseYear);
      for (const costOverview of filter) {
        if (arg.isDecChange){
          await this.feeDescriptionChangeResult(arg.unitFeeDescription,costOverview,arg);
        }
        if (!ObjectUtils.isEmpty(unitFeeFileDTO.managementFee)) {
          costOverview.managementFee = NumberUtil.glfRateFormat(unitFeeFileDTO.managementFee);
        }
        if (!ObjectUtils.isEmpty(unitFeeFileDTO.profit)) {
          costOverview.profit = NumberUtil.lrRateFormat(unitFeeFileDTO.profit);
        }
        if (!ObjectUtils.isEmpty(unitFeeFileDTO.fees)) {
          costOverview.fees = NumberUtil.gfRateFormat(unitFeeFileDTO.fees);
        }
        if (!ObjectUtils.isEmpty(unitFeeFileDTO.anwenRateBase)) {
          costOverview.anwenRateBase = NumberUtil.awfRateFormat(unitFeeFileDTO.anwenRateBase);
        }
        if (!ObjectUtils.isEmpty(unitFeeFileDTO.anwenRateAdd)) {
          costOverview.anwenRateAdd = NumberUtil.awfRateFormat(unitFeeFileDTO.anwenRateAdd);
        }
      }
    }


    //费用说明
    let unitFeeDescriptionList = arg.unitFeeDescription;
    //项目中所有的单位的该取费文件
    // let unitFeeFileDTO = feeCollectionList.filter(obj => obj.feeFileId === feeFileId);
    //循环前端的费率说明数据
    for (const descriptionDTO of unitFeeDescriptionList) {
      if (unitFeeDescriptionO){
        let unitFeeDescriptionOne = unitFeeDescriptionO.find(e => e.name === descriptionDTO.name);
        if (unitFeeDescriptionOne){
          unitFeeDescriptionOne.context = descriptionDTO.context;
        }
      }
      if (singleFeeDescription) {
        for (const item of singleFeeDescription) {
          let feeDescription = item.find(obj => obj.name === descriptionDTO.name);
          if (!ObjectUtils.isEmpty(feeDescription)) {
            feeDescription.context = descriptionDTO.context;
          }
        }
      }
      for (const item of feeCollectionList) {
        let feeDescription = item.unitFeeDescription.find(obj => obj.name === descriptionDTO.name);
        if (!ObjectUtils.isEmpty(feeDescription)) {
          feeDescription.context = descriptionDTO.context;
        }
      }
    }

    await this.saveTaxCalculation(arg);

    let units = [];
    if (ProjectLevelConstant.construct == arg.levelType) {
      units = PricingFileFindUtils.getUnitList(arg.constructId);
    }else if (ProjectLevelConstant.single == arg.levelType){
      units = PricingFileFindUtils.getUnitListBySingle(arg.constructId,arg.singleId);
    }
    for (let k of units){
      let {spId,sequenceNbr,measureProjectTables,itemBillProjects} = k;
      let calculationTool =new CalculationTool({constructId:arg.constructId, singleId:spId, unitId:sequenceNbr, allData:itemBillProjects});
      k.itemBillProjects.filter(item=>item.kind=="04").forEach(item=>{
        calculationTool.calculationChian(item)
      });
      let calculationToolCsxm =new CalculationTool({constructId:arg.constructId, singleId:spId, unitId:sequenceNbr, allData:measureProjectTables});
      k.measureProjectTables.filter(item=>item.kind=="04").forEach(item=>{
        calculationToolCsxm.calculationChian(item)
      });

      // zqf 费用定额 计算
      await this.service.autoCostMathService.autoCostMath({
        constructId: arg.constructId,
        singleId: spId,
        unitId: sequenceNbr
      });

      //汇总计算
      await this.service.unitCostCodePriceService.countCostCodePrice({
        constructId: arg.constructId,
        singleId: spId,
        unitId: sequenceNbr
      });

    }

  }


  /**
   * 设置费用总览数据
   * @param FeeCollectionVO
   */
  setCostOverview(vo, feeCollectionList) {

    let costOverviewList = new Array();

    for (const i in feeCollectionList) {
      let unitFeeFileDTO = feeCollectionList[i];

//            // 取费文件数据
//            UnitFeeFileDTO unitFeeFile = new UnitFeeFileDTO();
//            unitFeeFile.setFeeFileId(unitFeeFileDTO.getFeeFileId());
//            unitFeeFile.setFeeFileCode(unitFeeFileDTO.getFeeFileCode());
//            unitFeeFile.setFeeFileName(unitFeeFileDTO.getFeeFileName());
//            unitFeeFile.setFeeCode(unitFeeFileDTO.getFeeCode());
//            unitFeeFile.setSortNum(i + 1);
//            feeFileList.add(unitFeeFile);

      // 费用总览数据
      let costOverviewDto = new UnitFeeFile();
      costOverviewDto.sequenceNbr = unitFeeFileDTO.sequenceNbr;
      costOverviewDto.sortNum = parseInt(i) + 1;
      costOverviewDto.sortNo = unitFeeFileDTO.sortNo;
      costOverviewDto.feeFileId = unitFeeFileDTO.feeFileId;
      costOverviewDto.feeFileCode = unitFeeFileDTO.feeFileCode;
      costOverviewDto.feeFileName = unitFeeFileDTO.feeFileName;
      costOverviewDto.feeCode = unitFeeFileDTO.feeCode;
      costOverviewDto.deStandardReleaseYear = unitFeeFileDTO.deStandardReleaseYear;
      // 管理费
      costOverviewDto.managementFee = unitFeeFileDTO.managementFee;
      costOverviewDto.managementFeeBackUp = unitFeeFileDTO.managementFeeBackUp;

      if (unitFeeFileDTO.managementFee != unitFeeFileDTO.managementFeeBackUp) {
        costOverviewDto.managementFeeMarkFlagBlue = 1;
      }
      // 利润
      costOverviewDto.profit = unitFeeFileDTO.profit;
      costOverviewDto.profitBackUp = unitFeeFileDTO.profitBackUp;

      if (unitFeeFileDTO.profit != unitFeeFileDTO.profitBackUp) {
        costOverviewDto.profitMarkFlagBlue = 1;
      }

      // 规费
      costOverviewDto.fees = unitFeeFileDTO.fees;
      costOverviewDto.feesBackUp = unitFeeFileDTO.feesBackUp;

      if (unitFeeFileDTO.fees != unitFeeFileDTO.feesBackUp) {
        costOverviewDto.feesMarkFlagBlue = 1;
      }
      // 安文费基本费
      costOverviewDto.anwenRateBase = unitFeeFileDTO.anwenRateBase;
      costOverviewDto.anwenRateBaseBackUp = unitFeeFileDTO.anwenRateBaseBackUp;

      if (unitFeeFileDTO.anwenRateBase != unitFeeFileDTO.anwenRateBaseBackUp) {
        costOverviewDto.anwenRateBaseMarkFlagBlue = 1;
      }
      // 安文费增加费
      costOverviewDto.anwenRateAdd = unitFeeFileDTO.anwenRateAdd;
      costOverviewDto.anwenRateAddBackUp = unitFeeFileDTO.anwenRateAddBackUp;

      if (unitFeeFileDTO.anwenRateAdd != unitFeeFileDTO.anwenRateAddBackUp) {
        costOverviewDto.anwenRateAddMarkFlagBlue = 1;
      }
      // 附加税费
      costOverviewDto.additionalTax = unitFeeFileDTO.additionalTax;
      costOverviewList.push(costOverviewDto);
    }
    //        // 取费文件列表
    //        vo.setFeeFileList(feeFileList);
    // 费用总览列表数据
    vo.costOverview = costOverviewList;
  }


  /**
   * 获取工程项目或者单位的计税方式数据
   */
  async taxCalculation(arg) {
    let levelType = arg.levelType;
    let constructId = arg.constructId;
    let singleId = arg.singleId;
    let unitId = arg.unitId;
    let obj = null;
    let vo = new ProjectTaxCalculationVO();
    if (projectLevelConstant.construct === levelType || projectLevelConstant.single === levelType){
      let constructIs2022 = PricingFileFindUtils.is22De(constructId);
      if (constructIs2022){
        let unitList = PricingFileFindUtils.getUnitList(constructId);
        let obj22 = unitList.find(k =>k.deStandardReleaseYear== ConstantUtil.DE_STANDARD_22);
        //let obj12 = unitList.find(k =>k.deStandardReleaseYear== ConstantUtil.DE_STANDARD_12);
        if (ObjectUtils.isEmpty(obj22)){
          obj = PricingFileFindUtils.getProjectObjById(constructId);
          ConvertUtil.setDstBySrc(obj.projectTaxCalculation, vo);
          return vo;
        }else {
          obj = obj22;
        }
      }else {
        obj = PricingFileFindUtils.getProjectObjById(constructId);
      }
    } else {
      obj = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    }
    ConvertUtil.setDstBySrc(obj.projectTaxCalculation, vo);
    //let unitIs2022 = PricingFileFindUtils.getConstructDeStandard(constructId) == ConstantUtil.DE_STANDARD_22;
    let unitIs2022 = PricingFileFindUtils.is22Unit(obj);
    if (unitIs2022) {
      vo.taxPayingRegionOption = Object.keys(TaxPayingRegion22Enum).map((key) => {
        const value = TaxPayingRegion22Enum[key].value.toString();
        return { [value]: value };
      });
    } else {
      vo.taxPayingRegionOption = Object.keys(TaxPayingRegionEnum).map((key) => {
        const value = TaxPayingRegionEnum[key].value.toString();
        return { [value]: value };
      });
    }
    vo.taxCalculationMethodOption = Object.keys(TaxCalculationMethodEnum).map((key) => {
      const code = TaxCalculationMethodEnum[key].code.toString();
      const desc = TaxCalculationMethodEnum[key].desc;
      return { [code]: desc };
    });
    // 计税方式对应的税改文件下拉选项
    let baseTaxReformDocumentsList = await this.app.appDataSource.getRepository(BaseTaxReformDocuments).find({
      where: {
        //type: vo.taxCalculationMethod
        type: 1
      }
    });
    let array = new Array();
    for (const baseTaxReformDocumentsDTO of baseTaxReformDocumentsList) {
      const code = baseTaxReformDocumentsDTO.sequenceNbr;
      const desc = baseTaxReformDocumentsDTO.name;
      array.push({ [code]: desc });
    }
    vo.taxReformDocumentsOption = array;
    return vo;
  }


  /**
   * 入参为工程项目
   * @param obj
   * @returns {Promise<ProjectTaxCalculationVO>}
   */
  async taxCalculationParam(obj) {
    let vo = new ProjectTaxCalculationVO();
    ConvertUtil.setDstBySrc(obj.projectTaxCalculation, vo);

    vo.taxPayingRegionOption = Object.keys(TaxPayingRegionEnum).map((key) => {
      const value = TaxPayingRegionEnum[key].value.toString();
      return { [value]: value };
    });
    vo.taxCalculationMethodOption = Object.keys(TaxCalculationMethodEnum).map((key) => {
      const code = TaxCalculationMethodEnum[key].code.toString();
      const desc = TaxCalculationMethodEnum[key].desc;
      return { [code]: desc };
    });
    // 计税方式对应的税改文件下拉选项
    let baseTaxReformDocumentsList = await this.app.appDataSource.getRepository(BaseTaxReformDocuments).find({
      where: {
        //type: vo.taxCalculationMethod
        type: 1
      }
    });
    let array = new Array();
    for (const baseTaxReformDocumentsDTO of baseTaxReformDocumentsList) {
      const code = baseTaxReformDocumentsDTO.sequenceNbr;
      const desc = baseTaxReformDocumentsDTO.name;
      array.push({ [code]: desc });
    }
    vo.taxReformDocumentsOption = array;
    return vo;
  }

  async getDefaultTaxCalculation() {
    let vo = {};
    vo.taxCalculationMethodOption = Object.keys(TaxCalculationMethodEnum).map((key) => {
      const code = TaxCalculationMethodEnum[key].code.toString();
      const desc = TaxCalculationMethodEnum[key].desc;
      return { [code]: desc };
    });
    //排序

    vo.taxCalculationMethodOption.sort((a, b) => parseInt(Object.keys(b)[0]) - parseInt(Object.keys(a)[0]));

    // 计税方式对应的税改文件下拉选项
    let baseTaxReformDocumentsList = await this.app.appDataSource.getRepository(BaseTaxReformDocuments).find({
      where: {
        //type: vo.taxCalculationMethod
        type: 1
      }
    });
    let array = new Array();
    for (const baseTaxReformDocumentsDTO of baseTaxReformDocumentsList) {
      const code = baseTaxReformDocumentsDTO.sequenceNbr;
      const desc = baseTaxReformDocumentsDTO.name;
      array.push({ [code]: desc });
    }
    vo.taxReformDocumentsOption = array;

    return vo;
  }


  /**
   * 保存计税方式数据
   * @param arg
   */
  saveTaxCalculation(arg) {
    let levelType = arg.levelType;
    let constructId = arg.constructId;
    let singleId = arg.singleId;
    let unitId = arg.unitId;
    let projectTaxCalculation = new ProjectTaxCalculation();
    let { unitCostSummaryService } = this.service;
    ConvertUtil.setDstBySrc(arg, projectTaxCalculation);
    if (levelType === 1) {
      let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
      projectTaxCalculation.unitId = null;
      projectTaxCalculation.singleId = null;
      //修改项目的计税方式
      projectObj.projectTaxCalculation = projectTaxCalculation;
      //同步修改所有单位的计税方式
      let unitList = PricingFileFindUtils.getUnitList(constructId);
      if (!ObjectUtils.isEmpty(unitList)) {
        for (const unit of unitList) {
          if (projectObj.deStandardReleaseYear == ConstantUtil.DE_STANDARD_22 && !ObjectUtils.isEmpty(unit)
              && !PricingFileFindUtils.is22Unit(unit)){
            continue;
          }

          projectTaxCalculation.unitId = unit.unitId;
          unit.projectTaxCalculation = projectTaxCalculation;
          //同步修改费用汇总
          unitCostSummaryService.updateUnitCostSummaryRate(unit);
        }
      }
    }else if(levelType == 2){
      let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
      //同步修改所有单位的计税方式
      let unitList = PricingFileFindUtils.getUnitListBySingle(constructId,singleId);
      if (!ObjectUtils.isEmpty(unitList)) {
        for (const unit of unitList) {
          if (projectObj.deStandardReleaseYear == ConstantUtil.DE_STANDARD_22 && !ObjectUtils.isEmpty(unit)
              && !PricingFileFindUtils.is22Unit(unit)){
            continue;
          }
          projectTaxCalculation.unitId = unit.unitId;
          unit.projectTaxCalculation = projectTaxCalculation;
          //同步修改费用汇总
          unitCostSummaryService.updateUnitCostSummaryRate(unit);
        }
      }
    }else {
      let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
      unit.projectTaxCalculation = projectTaxCalculation;
      //同步修改费用汇总
      unitCostSummaryService.updateUnitCostSummaryRate(unit);
    }
  }

  /**
   * 费用总览数据标红判断
   * @param vo
   * @param arg
   * @param unitFeeFileDtoList
   */
  async comparisonCostOverview(vo, arg, unitFeeFileDtoList) {
    let costOverviewList = vo.costOverview;
    let flag = false;
    let parentCostOverview;
    if (ObjectUtils.isEmpty(costOverviewList)) {
      return;
    }
    if (ObjectUtils.isEmpty(unitFeeFileDtoList)) {
      return;
    }

    if (arg.levelType === ProjectLevelConstant.unit){
      let singleProject = await PricingFileFindUtils.getSingleProject(arg.constructId,arg.firstSingle);
      if (singleProject && singleProject.costOverview){
        flag = true;
        parentCostOverview = singleProject.costOverview;
      }
    }

    for (const costOverviewDTO of costOverviewList) {

      if (flag) {
        let costOverview = parentCostOverview.find(e => e.feeFileCode === costOverviewDTO.feeFileCode && e.deStandardReleaseYear === costOverviewDTO.deStandardReleaseYear);
        if (costOverview){
        if (costOverviewDTO.managementFee !== costOverview.managementFee && costOverviewDTO.deStandardReleaseYear === costOverview.deStandardReleaseYear) {
          costOverviewDTO.managementFeeMarkFlag = 1;
        }
        if (costOverviewDTO.fees !== costOverview.fees && costOverviewDTO.deStandardReleaseYear === costOverview.deStandardReleaseYear && costOverviewDTO.deStandardReleaseYear==="12") {
          costOverviewDTO.feesMarkFlag = 1;
        }
        if (costOverviewDTO.profit !== costOverview.profit && costOverviewDTO.deStandardReleaseYear === costOverview.deStandardReleaseYear) {
          costOverviewDTO.profitMarkFlag = 1;
        }
        if (costOverviewDTO.anwenRateBase !== costOverview.anwenRateBase && costOverviewDTO.deStandardReleaseYear === costOverview.deStandardReleaseYear) {
          costOverviewDTO.anwenRateBaseMarkFlag = 1;
        }
        if (costOverviewDTO.anwenRateAdd !== costOverview.anwenRateAdd && costOverviewDTO.deStandardReleaseYear === costOverview.deStandardReleaseYear) {
          costOverviewDTO.anwenRateAddMarkFlag = 1;
        }
        }
      }else {

        let codeGroupCostOverviewMap = {};

        for (const unitFeeFileDTO of unitFeeFileDtoList) {
          let unitFeeFileDTOS = codeGroupCostOverviewMap[unitFeeFileDTO.feeFileCode];
          if (ObjectUtils.isEmpty(unitFeeFileDTOS)) {
            codeGroupCostOverviewMap[unitFeeFileDTO.feeFileCode] = [unitFeeFileDTO];
          } else {
            unitFeeFileDTOS.push(unitFeeFileDTO);
          }
        }

        let unitFeeFileList = codeGroupCostOverviewMap[costOverviewDTO.feeFileCode];
        if (ObjectUtils.isEmpty(unitFeeFileList)) {
          continue;
        }
        for (const unitFeeFileDTO of unitFeeFileList) {
          if (costOverviewDTO.managementFee != unitFeeFileDTO.managementFee && costOverviewDTO.deStandardReleaseYear == unitFeeFileDTO.deStandardReleaseYear) {
            costOverviewDTO.managementFeeMarkFlag = 1;
          }
          if (costOverviewDTO.fees != unitFeeFileDTO.fees && costOverviewDTO.deStandardReleaseYear == unitFeeFileDTO.deStandardReleaseYear && unitFeeFileDTO.deStandardReleaseYear ==="12") {
            costOverviewDTO.feesMarkFlag = 1;
          }
          if (costOverviewDTO.profit != unitFeeFileDTO.profit && costOverviewDTO.deStandardReleaseYear == unitFeeFileDTO.deStandardReleaseYear) {
            costOverviewDTO.profitMarkFlag = 1;
          }
          if (costOverviewDTO.anwenRateBase != unitFeeFileDTO.anwenRateBase && costOverviewDTO.deStandardReleaseYear == unitFeeFileDTO.deStandardReleaseYear) {
            costOverviewDTO.anwenRateBaseMarkFlag = 1;
          }
          if (costOverviewDTO.anwenRateAdd != unitFeeFileDTO.anwenRateAdd && costOverviewDTO.deStandardReleaseYear == unitFeeFileDTO.deStandardReleaseYear) {
            costOverviewDTO.anwenRateAddMarkFlag = 1;
          }
          if (unitFeeFileDTO.anwenRateAddMarkFlag == 1
              && unitFeeFileDTO.profitMarkFlag == 1
              && unitFeeFileDTO.feesMarkFlag == 1
              && unitFeeFileDTO.anwenRateAddMarkFlag == 1
              && unitFeeFileDTO.anwenRateBaseMarkFlag == 1) {
            // 如果都是标红了  那就没必要继续这个循环了
            break;
          }
        }
      }
      if (costOverviewDTO.managementFee !== costOverviewDTO.managementFeeBackUp) {
        costOverviewDTO.managementFeeFlagBg = 1
      }
      if (costOverviewDTO.fees !== costOverviewDTO.feesBackUp && costOverviewDTO.deStandardReleaseYear ==="12") {
        costOverviewDTO.feesFlagBg = 1
      }
      if (costOverviewDTO.profit !== costOverviewDTO.profitBackUp) {
        costOverviewDTO.profitFlagBg = 1
      }
      if (costOverviewDTO.anwenRateBase !== costOverviewDTO.anwenRateBaseBackUp) {
        costOverviewDTO.anwenRateBaseFlagBg = 1
      }
      if (costOverviewDTO.anwenRateAdd !== costOverviewDTO.anwenRateAddBackUp) {
        costOverviewDTO.fanwenRateBaseFlagBg = 1
      }
    }
  }

  /**
   * 根据计税方式和纳税地区获取对应的税率
   * @param args
   */
  async getRateByMethodAndLocation(args) {
    let method = args.method;
    let region = args.region;
    const taxCalculationMethodEnum = Object.values(TaxCalculationMethodEnum).find(item => item.code == method);
    // TaxPayingRegionEnum

    let { baseGsjRateService } = this.service;
    let unitIs2022 = PricingFileFindUtils.is22De(args.constructId);
    let baseGsjRate = await baseGsjRateService.queryByKindAndMethodAndRegion(GsjRateKindEnum.SJ.code, taxCalculationMethodEnum.code, region, unitIs2022);
    let vo = new ProjectTaxCalculationVO();
    if (!ObjectUtils.isEmpty(baseGsjRate)) {
      if (unitIs2022) {
        vo.taxRate = baseGsjRate.rate;
      } else {
        if (TaxCalculationMethodEnum.GENERAL == taxCalculationMethodEnum) {
          vo.additionalTaxRate = baseGsjRate.rate;
          vo.outputTaxRate = 9;
        } else {
          vo.simpleRate = baseGsjRate.rate;
        }
      }
    }
    return vo;
  }

  /**
   * 设置费率说明数据
   * @param vo
   * @param feeFile
   */
  async setUnitFeeDescriptionData(vo, feeFiles, arg) {

    if (ObjectUtils.isEmpty(feeFiles)) {
      return;
    }
    const flattened = feeFiles.flatMap(item => item['unitFeeDescription']);


    let unitFeeDescriptionDTOList = ArrayUtil.distinctList(flattened, 'name');

    let unitFeeDescriptionList = new Array();
    for (const unitFeeDescriptionDTO of unitFeeDescriptionDTOList) {
      let deepCopy = ConvertUtil.deepCopy(unitFeeDescriptionDTO);
      await this.setUnitFeeDescriptionEnumOption(deepCopy, arg);
      unitFeeDescriptionList.push(deepCopy);
      if (deepCopy.name === '二级取费专业') {
        if(arg.constructId && arg.singleId && arg.unitId){
          PricingFileFindUtils.getUnit(arg.constructId, arg.singleId, arg.unitId).secondInstallationProjectName = deepCopy.context;
        }else {
          let constructId = ParamUtils.getPatram('commonParam').constructId ? ParamUtils.getPatram('commonParam').constructId : feeFiles[0].constructId;
          let singleId = ParamUtils.getPatram('commonParam').singleId ? ParamUtils.getPatram('commonParam').singleId : feeFiles[0].singleId;
          let unitId = ParamUtils.getPatram('commonParam').unitId ? ParamUtils.getPatram('commonParam').unitId : feeFiles[0].unitId;
          PricingFileFindUtils.getUnit(constructId, singleId, unitId).secondInstallationProjectName = deepCopy.context;
        }

      }
    }
    vo.unitFeeDescriptionList = unitFeeDescriptionList;

  }

  /**
   * 取费文件
   * @param args
   * @returns {Promise<void>}
   */
  async queryFeeFileData(args) {
    let {constructId, singleId, unitId, type, deId} = args;
    let is2022 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
    let list = UPCContext.getFeeFileData(is2022 ? "22" : "12");
    return list;
  }

  //单价构成模板
  async queryFeeFileTempData(args) {
    let {constructId, singleId, unitId, type} = args;
    let is2022 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
    if(ObjectUtils.isEmpty(is2022)){
      return  null;
    }
    let list = UPCContext.getUpcTemplateSelectList(is2022, constructId, unitId);
    return list.filter(item => item.qfCode != "SUIZHUGONGCHENG");
  }


  async setUnitFeeDescriptionEnumOption(descriptionDTO, args) {

    let context = descriptionDTO.context;

    const adjacentRoadsNumEnum = Object.values(AdjacentRoadsNumEnum).find(item => item.code == context);
    if (adjacentRoadsNumEnum != null) {
      descriptionDTO.optionList = Object.keys(AdjacentRoadsNumEnum).map((key) => {
        const code = AdjacentRoadsNumEnum[key].code.toString();
        const desc = AdjacentRoadsNumEnum[key].desc;
        return { [code]: desc };
      });
      return;
    }
    let engineeringLocationEnum = Object.values(EngineeringLocationEnum).find(item => item.code == context);
    if (engineeringLocationEnum != null) {
      descriptionDTO.optionList = Object.keys(EngineeringLocationEnum).map((key) => {
        const code = EngineeringLocationEnum[key].code.toString();
        const desc = EngineeringLocationEnum[key].desc;
        return { [code]: desc };
      });
      return;
    }

    let engineeringTypeEnum = Object.values(EngineeringTypeEnum).find(item => item.code == context);
    if (engineeringTypeEnum != null) {
      descriptionDTO.optionList = Object.keys(EngineeringTypeEnum).map((key) => {
        const code = EngineeringTypeEnum[key].code.toString();
        const desc = EngineeringTypeEnum[key].desc;
        return { [code]: desc };
      });

      return;
    }

    let floorSpaceEnum = Object.values(FloorSpaceEnum).find(item => item.code == context);
    if (floorSpaceEnum != null) {
      descriptionDTO.optionList = Object.keys(FloorSpaceEnum).map((key) => {
        const code = FloorSpaceEnum[key].code.toString();
        const desc = FloorSpaceEnum[key].desc;
        return { [code]: desc };
      });
      return;
    }

    let municipalEngineeringCostEnum = Object.values(MunicipalEngineeringCostEnum).find(item => item.code == context);
    if (municipalEngineeringCostEnum != null) {
      descriptionDTO.optionList = Object.keys(MunicipalEngineeringCostEnum).map((key) => {
        const code = MunicipalEngineeringCostEnum[key].code.toString();
        const desc = MunicipalEngineeringCostEnum[key].desc;
        return { [code]: desc };
      });
      return;
    }
    let precastRateEnum = Object.values(PrecastRateEnum).find(item => item.code == context);
    if (!ObjectUtils.isEmpty(precastRateEnum)) {
      descriptionDTO.optionList = Object.keys(PrecastRateEnum).map((key) => {
        const code = PrecastRateEnum[key].code;
        const desc = PrecastRateEnum[key].code;
        return { [code]: desc };
      });
      return;
    }
    if (args.levelType == ProjectLevelConstant.unit) {
      let unit = PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);
      let secondProjectTypeList = await this.service.unitProjectService.getSecondInstallationProjectName(unit.constructMajorType, unit.deStandardReleaseYear);
      let options = secondProjectTypeList.map((key) => {
        return { [key.cslbName]: key.cslbName };
      });
      descriptionDTO.optionList = options;
      return;
    }
  }

  //费率说明更改以后更新取费文件的费率
  async feeDescriptionChangeResult(unitFeeDescription, unitFeeFileDTO, arg) {
    let constructId = arg.constructId;
    let singleId = arg.singleId;
    let unitId = arg.unitId;
    let unit;
    let unitIs2022;
    let taxCalculationMethod;
    if (unitId){
      unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
      unitIs2022 = PricingFileFindUtils.is22Unit(unit);
      taxCalculationMethod = unit.projectTaxCalculation.taxCalculationMethod;
    }else {
      unitIs2022 = !(unitFeeFileDTO.deStandardReleaseYear && unitFeeFileDTO.deStandardReleaseYear === "12");
      let Project = PricingFileFindUtils.getProjectObjById(constructId);
      taxCalculationMethod = Project.projectTaxCalculation.taxCalculationMethod
    }

    // 费用总览数据
    let gclb = unitFeeDescription.find(res => res.name === '工程类别');
    let gcszd = unitFeeDescription.find(res => res.name === '工程所在地');
    let llms = unitFeeDescription.find(res => res.name === '临路面数');
    let jzmj = unitFeeDescription.find(res => res.name === '建筑面积');
    let yzl = unitFeeDescription.find(res => res.name === '预制率');
    let szgczj = unitFeeDescription.find(res => res.name === '市政工程造价');
    let { baseManageRateService, baseAnwenRateService, baseGsjRateService, baseFeeFileRelationService } = this.service;
    // 取费文件关联关系
    let baseFeeFileRelationDTO = await baseFeeFileRelationService.getFeeFileProjectByQfCode(unitFeeFileDTO.feeFileCode, unitIs2022);

    // 管理费  利润
    if (ObjectUtils.isEmpty(arg.name) || arg.name === "工程类别" ){
    let baseManageRate = await baseManageRateService.queryByQfCode(unitFeeFileDTO.feeFileCode, unitIs2022, ObjectUtils.isEmpty(yzl) ? null : yzl.context, taxCalculationMethod);
    if (ObjectUtils.isNotEmpty(baseManageRate)) {
      if (gclb.context === EngineeringTypeEnum.MANAGEMENT_FEE3.code) {
        unitFeeFileDTO.managementFee = NumberUtil.glfRateFormat(baseManageRate.managementFee3);
        unitFeeFileDTO.profit = NumberUtil.lrRateFormat(baseManageRate.profit3);
      } else if (gclb.context === EngineeringTypeEnum.MANAGEMENT_FEE2.code) {
        unitFeeFileDTO.managementFee = NumberUtil.glfRateFormat(baseManageRate.managementFee2);
        unitFeeFileDTO.profit = NumberUtil.lrRateFormat(baseManageRate.profit2);
      } else {
        unitFeeFileDTO.managementFee = NumberUtil.glfRateFormat(baseManageRate.managementFee1);
        unitFeeFileDTO.profit = NumberUtil.lrRateFormat(baseManageRate.profit1);
      }
      //备份利润 管理费 费率
      unitFeeFileDTO.managementFeeBackUp = unitFeeFileDTO.managementFee;
      unitFeeFileDTO.profitBackUp = unitFeeFileDTO.profit;
    }
    }
    //查询安文费
    if (ObjectUtils.isEmpty(arg.name)  || arg.name === "临路面数"|| arg.name === "建筑面积"|| arg.name === "工程所在地" || arg.name === "市政工程造价"){
    let baseAnwenRate = await baseAnwenRateService.queryByLibraryCode(unitFeeFileDTO.feeFileName
      , baseFeeFileRelationDTO.libraryCode
      , gcszd.context
      , llms.context,
      jzmj.context,
      ObjectUtils.isEmpty(szgczj) ? null : szgczj.context, unitIs2022, taxCalculationMethod);
    //安文费基本费
    unitFeeFileDTO.anwenRateBase = NumberUtil.awfRateFormat(baseAnwenRate.anwenRate);

    //备份安文费费率
    unitFeeFileDTO.anwenRateBaseBackUp = unitFeeFileDTO.anwenRateBase;

    //恢复安全 增加费
    unitFeeFileDTO.anwenRateAdd = unitFeeFileDTO.anwenRateAddBackUp;

    //恢复规费
    unitFeeFileDTO.fees = unitFeeFileDTO.feesBackUp;
    }

    return unitFeeFileDTO;
  }


  async queryCalculateBaseDropDownList(type,is22de) {
    if (CalculateBaseType.fee === type) {
      let deepCopy = ConvertUtil.deepCopy(CalculateBaseConfigEnum.fee);
      let fee = deepCopy;
      if (!ObjectUtils.isEmpty(is22de) && is22de ==true){
        for (let feeElement of fee) {
          if (feeElement.name.includes("定额")){
            feeElement.name = feeElement.name.replaceAll("定额","基期");
          }
        }
      }
      return fee;
    } else if (CalculateBaseType.csfy === type) {
      return CalculateBaseConfigEnum.csfy;
    }
  }

  async queryProjectUnitCalculateBaseList(constructId, singleId, unitId) {
    let calculateBaseList = [];
    if (ObjectUtils.isEmpty(unitId)) {
      let unitList = PricingFileFindUtils.getUnitList(constructId).filter(unit=>PricingFileFindUtils.is22Unit(unit));
      if (ObjectUtils.isNotEmpty(unitList)) {
        calculateBaseList = unitList[0].feeCalculateBaseList;
      }else {
        let unitList = PricingFileFindUtils.getUnitList(constructId).filter(unit=>unit.deStandardReleaseYear == ConstantUtil.DE_STANDARD_12);
        if (ObjectUtils.isNotEmpty(unitList)){
          calculateBaseList = unitList[0].feeCalculateBaseList;
        }
      }
    } else {
      let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
      if (ObjectUtils.isEmpty(unit.feeCalculateBaseList)) {
        unit.feeCalculateBaseList = CalculateBaseUnitInitEnum.fee;
      }
      calculateBaseList = unit.feeCalculateBaseList;
    }
    return ObjectUtils.isEmpty(calculateBaseList) ? [] : calculateBaseList;
  }


  async updateProjectUnitCalculateBaseList(constructId, singleId, unitId, feeCalculateBaseList) {
    if (ObjectUtils.isEmpty(feeCalculateBaseList)) {
      return;
    }

    if (ObjectUtils.isEmpty(unitId)) {
      let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
      projectObjById.feeCalculateBaseList = feeCalculateBaseList;
    } else {
      //todo 更新基数后，调用单价构成计算定额方法 constructId, singleId, unitId, type, code
      let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
      await this.updateBuildDE(constructId, singleId, unitId, unit, feeCalculateBaseList);
      //费用定额 计算
      await this.service.autoCostMathService.autoCostMath({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId,
        countCostCodeFlag: false
      });

      //汇总计算
      await this.service.unitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId
      });
    }




  }


  async updateBuildDE(constructId, singleId, unitId, unit, feeCalculateBaseList) {
    let glf = feeCalculateBaseList.find(o => o.type === CalculateBaseType.glf);
    let lr = feeCalculateBaseList.find(o => o.type === CalculateBaseType.lr);
    let gf = feeCalculateBaseList.find(o => o.type === CalculateBaseType.gf);
    unit.feeCalculateBaseList = feeCalculateBaseList;
    let is22De = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
    let systemTtemplate = UPCContext.systemTtemplateListMap.get(is22De ? "22" : "12");
    let pMap = UPCContext.getTemplateListbyPath(constructId, unitId);
    for (const [key, value] of systemTtemplate) {
      let vList = _.cloneDeep(value);
      vList.forEach(item => {
        if (item.typeCode == "UPC_GLF") {//管理费
          item.caculateBase = glf.code;
          item.desc = CalculateBaseConfigEnum.fee.find(o => o.code === item.caculateBase).name;
        }
        if (item.typeCode == "UPC_LR") {//利润
          item.caculateBase = lr.code;
          item.desc = CalculateBaseConfigEnum.fee.find(o => o.code === item.caculateBase).name;
        }
        if (item.typeCode == "UPC_GF") {
          item.caculateBase = gf.code;
          item.desc = CalculateBaseConfigEnum.fee.find(o => o.code === item.caculateBase).name;
        }
      })
      pMap.set(key, vList);
    }


    // 找分部分项下的定额
    let fbfxs = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
    let fbfxDe = fbfxs.filter(o => o.kind == BranchProjectLevelConstant.de);
    let qdset = new Set();
    for (let i = 0; i < fbfxDe.length; i++) {
      let deItem = fbfxDe[i];
      if (deItem.isCostDe === 1) {
        continue;
      }

      let uccore = new UPCCupmuteDe(deItem, constructId, singleId, unitId, fbfxs);
      uccore.prepare();
      let upcTemplateList = uccore.upcTemplateList;
      for (let item of upcTemplateList) {
        if (item.type === UnitPriceConstant.GLF_TYPE) {
          item.caculateBase = feeCalculateBaseList.find(o => o.type === CalculateBaseType.glf).code;
          item.desc = CalculateBaseConfigEnum.fee.find(o => o.code === item.caculateBase).name;
        } else if (item.type === UnitPriceConstant.LR_TYPE) {
          item.caculateBase = feeCalculateBaseList.find(o => o.type === CalculateBaseType.lr).code;
          item.desc = CalculateBaseConfigEnum.fee.find(o => o.code === item.caculateBase).name;
        } else if (item.type === UnitPriceConstant.GF_TYPE) {
          item.caculateBase = feeCalculateBaseList.find(o => o.type === CalculateBaseType.gf).code;
          item.desc = CalculateBaseConfigEnum.fee.find(o => o.code === item.caculateBase).name;
        }
      }
      uccore.cupmute();
      uccore.fillData();
      qdset.add(deItem.parentId);
    }
    let toos = new CalculationTool({constructId, singleId, unitId, allData: fbfxs});
    [...qdset].forEach(id => {
      toos.calculationChian({sequenceNbr: id});
    });
    qdset.clear();
    // 找措施项目下的定额
    let csxms = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
    let csxmsDe = csxms.filter(o => o.kind == BranchProjectLevelConstant.de);

    for (let i = 0; i < csxmsDe.length; i++) {
      let deItem = csxmsDe[i];
      if (deItem.isCostDe == DePropertyTypeConstant.AWF_DE) {
        continue;
      }
      let uccore = new UPCCupmuteDe(deItem, constructId, singleId, unitId, fbfxs);
      uccore.prepare();
      let upcTemplateList = uccore.upcTemplateList;
      for (let item of upcTemplateList) {
        if (item.type === UnitPriceConstant.GLF_TYPE) {
          item.caculateBase = feeCalculateBaseList.find(o => o.type === CalculateBaseType.glf).code;
          item.desc = CalculateBaseConfigEnum.fee.find(o => o.code === item.caculateBase).name;
        } else if (item.type === UnitPriceConstant.LR_TYPE) {
          item.caculateBase = feeCalculateBaseList.find(o => o.type === CalculateBaseType.lr).code;
          item.desc = CalculateBaseConfigEnum.fee.find(o => o.code === item.caculateBase).name;
        } else if (item.type === UnitPriceConstant.GF_TYPE) {
          item.caculateBase = feeCalculateBaseList.find(o => o.type === CalculateBaseType.gf).code;
          item.desc = CalculateBaseConfigEnum.fee.find(o => o.code === item.caculateBase).name;
        }
      }
      uccore.cupmute();
      uccore.fillData();
      qdset.add(deItem.parentId);
    }
    let tools = new CalculationTool({constructId, singleId, unitId, allData: csxms});
    [...qdset].forEach(id => {
      tools.calculationChian({sequenceNbr: id});
    });

  }


  async updateProjectUnitCalculateBaseApply(constructId, feeCalculateBaseList) {
    //统一应用到所有单位(取费计取基数)
    let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
    projectObjById.feeCalculateBaseList = feeCalculateBaseList;
    if (ObjectUtils.isEmpty(projectObjById.feeCalculateBaseList)) {
      return;
    }
    let listByConstructObj = PricingFileFindUtils.getUnitListByConstructObj(projectObjById);
    for (let unitObj of listByConstructObj) {
      // if (!unitObj.upName.includes('_')) {
        unitObj.feeCalculateBaseList = feeCalculateBaseList;
        //todo 更新基数后，调用计算方法 constructId, singleId, unitId, type, code
          await this.updateBuildDE(constructId, unitObj.spId, unitObj.sequenceNbr, unitObj, feeCalculateBaseList);
      //汇总计算
      await this.service.unitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        singleId: unitObj.spId,
        unitId: unitObj.sequenceNbr
      });
      // }
    }
  }

  //清空单价构成 重新计算
  async updateUpcTemplateDJGC({constructId, singleId, unitId, unit}) {
    //清除单价构成缓存
    //获取措施项目 列表 分部分项列表
    const {itemBillProjects, measureProjectTables} = unit;
    let deListFbfx = itemBillProjects.getNodesByKind(ConstantUtil.DE_KIND);
    if (deListFbfx) {
      deListFbfx = Array.from(deListFbfx.values()).map(item => item.sequenceNbr);
    }
    const calculationTool = new CalculationTool({constructId, singleId, unitId, allData: itemBillProjects});
    calculationTool.calculationDes(deListFbfx)

    let deListCsxm = measureProjectTables.getNodesByKind(ConstantUtil.DE_KIND);
    if (deListCsxm) {
      deListCsxm = Array.from(deListCsxm.values()).map(item => item.sequenceNbr);
    }
    const calculationTool1 = new CalculationTool({constructId, singleId, unitId, allData: measureProjectTables});
    calculationTool1.calculationDes(deListCsxm)
  }
  async updateFeeFileByTax(arg) {
    arg.levelType = 3;
    // let unitIs2022 = PricingFileFindUtils.getConstructDeStandard(constructId) == ConstantUtil.DE_STANDARD_22;
    // if (!unitIs2022){
    //   return;
    // }
    // 查询出工程项目下的所有取费文件
    let feeCollectionList = this.getFeeCollectionList(arg);
    for (let i = 0; i < feeCollectionList.length; i++) {
      let feeFile = feeCollectionList[i];
      // 设置费用总览列表数据
      await this.feeDescriptionChangeResult(feeFile.unitFeeDescription, feeFile, arg);
    }
  }

  async updateRcjByTax(arg) {


    let {constructId,singleId,unitId,unit} = arg;


    //是否是简易计税
    let isSimple = unit.projectTaxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code;

    //constructProjectRcjs  rcjDetailList

    if (PricingFileFindUtils.is22Unit(unit)){
      /*unit.constructProjectRcjs.forEach(k =>{
        k.marketPrice = isSimple?k.priceMarketTax:k.priceMarket;
        k.dePrice = isSimple?k.priceBaseJournalTax:k.priceBaseJournal;
      });

      unit.rcjDetailList.forEach(k =>{
        k.marketPrice = isSimple?k.priceMarketTax:k.priceMarket;
        k.dePrice = isSimple?k.priceBaseJournalTax:k.priceBaseJournal;
      });*/

      await this.changeTaxCalculationMethod(unit,isSimple);
    }

    const {itemBillProjects, measureProjectTables} = unit;
    /*const deListFbfx = itemBillProjects.getNodesByKind(ConstantUtil.DE_KIND);
    const deListCsxm = measureProjectTables.getNodesByKind(ConstantUtil.DE_KIND);*/
    const deListFbfx = itemBillProjects.filter(i=>i.kind === ConstantUtil.DE_KIND);
    const deListCsxm = measureProjectTables.filter(i=>i.kind === ConstantUtil.DE_KIND);
    let rcjCalculateHandler = new RcjCalculateHandler({constructId, singleId, unitId, projectObj:PricingFileFindUtils.getProjectObjById(constructId)});

    if (ObjectUtils.isNotEmpty(deListFbfx)){
      for (const deFbfx of deListFbfx.values()) {
        await rcjCalculateHandler.calculate(deFbfx);
      }
    }

    if (ObjectUtils.isNotEmpty(deListCsxm)){
      for (const deCsxm of deListCsxm.values()) {
        await rcjCalculateHandler.calculate(deCsxm);
      }
    }




  }

  async updateTaxCalculationMethod(args) {
    const { constructId, taxCalculationMethod, taxReformDocumentsId } = args;
    const constructObj = PricingFileFindUtils.getProjectObjById(constructId);
    const constructIs22De = PricingFileFindUtils.is22De(constructId);
    if (!constructIs22De && taxCalculationMethod == TaxCalculationMethodEnum['GENERAL'].code && ObjectUtils.isEmpty(taxReformDocumentsId)) {
      throw new Error('请选择税改文件');
    }
    const constructObjMethod = constructObj.projectTaxCalculation.taxCalculationMethod;
    let str = constructObjMethod == TaxCalculationMethodEnum['SIMPLE'].code ? '一般计税' : '简易计税';
    // 备份切换计税方式之前的工程项目文件
    // let fileSaveAs = await this.service.commonService.fileSaveAs({ constructId: constructId }, '（' + str + '）',false);
    // if (fileSaveAs.result === 0) {
    //   return;
    // }
    let newProject = await this.service.commonService.constructBackup({ constructId: constructId }, '（' + str + '）');
    if(newProject == null){
      return;
    }


      let newConstructId = newProject.sequenceNbr;

      // 更新当前工程项目的计税方式
      newProject.projectTaxCalculation.taxCalculationMethod = taxCalculationMethod;
      newProject.projectTaxCalculation.taxReformDocumentsId = null;
      // 工程项目的税率
      if (constructIs22De) {
        const additionalTaxRate = await this.service.projectTaxCalculationService.getAdditionalTaxRate22De(GsjRateKindEnum.SJ.code, taxCalculationMethod, TaxPayingRegionEnum.URBAN_AREA.value);
        if (ObjectUtils.isNotEmpty(additionalTaxRate)) {
          // 税率
          newProject.projectTaxCalculation.taxRate = additionalTaxRate.rate;
          newProject.projectTaxCalculation.taxRaadditionalTaxRatete = additionalTaxRate.rate;
        }
      } else {
        const additionalTaxRate = await this.service.projectTaxCalculationService.getAdditionalTaxRate(GsjRateKindEnum.SJ.code, taxCalculationMethod, TaxPayingRegionEnum.URBAN_AREA.value);
        if (ObjectUtils.isNotEmpty(additionalTaxRate)) {
          // 税率
          if (taxCalculationMethod == TaxCalculationMethodEnum['GENERAL'].code) {
            newProject.projectTaxCalculation.taxRate = additionalTaxRate.rate;
            newProject.projectTaxCalculation.additionalTaxRate = additionalTaxRate.rate;
          } else {
            newProject.projectTaxCalculation.simpleRate = additionalTaxRate.rate;
          }
        }
      }
      if (!constructIs22De && taxCalculationMethod == TaxCalculationMethodEnum['GENERAL'].code) {
        // 如果是12定额的一般计税  还需要税改文件
        newProject.projectTaxCalculation.taxReformDocumentsId = taxReformDocumentsId;
      }

      const singleList = PricingFileFindUtils.getSingleProjectList(newConstructId) || [];

      for (let single of singleList) {
        single.costOverview = [];
      }

      const unitList = PricingFileFindUtils.getUnitList(newConstructId) || [];

      for (const unit of unitList) {
        const is22Unit = PricingFileFindUtils.is22Unit(unit);
        // 修改单位工程的计税方式
        await this.updateUnitTaxCalculationMethod(unit, taxCalculationMethod, taxReformDocumentsId, is22Unit, newProject);
        // 取费表调整
        await this.updateFeeFileByTax({constructId: newConstructId, singleId: unit.spId, unitId: unit.sequenceNbr})

        // 市场价处理
        await this.updateRcjByTax({
          constructId: newConstructId,
          singleId: unit.spId,
          unitId: unit.sequenceNbr,
          unit: unit
        });

        // 单价构成文件调整
        await this.updateUpcTemplateDJGC({
          constructId: newConstructId,
          singleId: unit.spId,
          unitId: unit.sequenceNbr,
          unit
        });

        // 费用计取调整
        await this.service.autoCostMathService.autoCostMath({
          constructId: newConstructId,
          singleId: unit.spId,
          unitId: unit.sequenceNbr
        });
        // 费用汇总调整
        await this.service.unitCostSummaryService.getDefaultUnitCostSummary(unit);

        //汇总计算
        this.service.unitCostCodePriceService.countCostCodePrice({
          constructId: newConstructId,
          singleId: unit.spId,
          unitId: unit.sequenceNbr
        });
      }

      //切换计税方式需要根据计税方式顶级单项和工程项目默认的费率
    let params={
      levelType:1, constructId:newProject.sequenceNbr,unitFeeDescriptionList:newProject.unitFeeDescriptionList
    }
    await this.restoreDefaultFee( params )

    if(newProject.singleProjects){
      for (let i = 0; i < newProject.singleProjects.length; i++) {
        params.levelType = 2
        params.singleId = newProject.singleProjects[i].sequenceNbr
        await this.restoreDefaultFee( params )
      }

    }

    let consoleStrategy = new ConsoleStrategy({ path: newProject.path });
    await consoleStrategy.openFromMemory(newProject);
  }

  /**
   * 修改计税方式
   */
  async updateTaxCalculationMethodOld(args) {
    const { constructId, taxCalculationMethod, taxReformDocumentsId } = args;
    const constructObj = PricingFileFindUtils.getProjectObjById(constructId);
    const constructIs22De = PricingFileFindUtils.is22De(constructId);
    if (!constructIs22De && taxCalculationMethod == TaxCalculationMethodEnum['GENERAL'].code && ObjectUtils.isEmpty(taxReformDocumentsId)) {
      throw new Error('请选择税改文件');
    }
    const constructObjMethod = constructObj.projectTaxCalculation.taxCalculationMethod;
    let str = constructObjMethod == TaxCalculationMethodEnum['SIMPLE'].code ? '简易计税' : '一般计税';
    // 备份切换计税方式之前的工程项目文件
    // let fileSaveAs = await this.service.commonService.fileSaveAs({ constructId: constructId }, '（' + str + '）',false);
    // if (fileSaveAs.result === 0) {
    //   return;
    // }
    let backupProject = await this.service.commonService.constructBackup({ constructId: constructId }, '（' + str + '）');
    if(backupProject == null){
      return;
    }
    // 更新当前工程项目的计税方式
    constructObj.projectTaxCalculation.taxCalculationMethod = taxCalculationMethod;
    constructObj.projectTaxCalculation.taxReformDocumentsId = null;
    // 工程项目的税率
    if (constructIs22De) {
      const additionalTaxRate = await this.service.projectTaxCalculationService.getAdditionalTaxRate22De(GsjRateKindEnum.SJ.code, taxCalculationMethod, TaxPayingRegionEnum.URBAN_AREA.value);
      if (ObjectUtils.isNotEmpty(additionalTaxRate)) {
        // 税率
        constructObj.projectTaxCalculation.taxRate = additionalTaxRate.rate;
        constructObj.projectTaxCalculation.taxRaadditionalTaxRatete = additionalTaxRate.rate;
      }
    } else {
      const additionalTaxRate = await this.service.projectTaxCalculationService.getAdditionalTaxRate(GsjRateKindEnum.SJ.code, taxCalculationMethod, TaxPayingRegionEnum.URBAN_AREA.value);
      if (ObjectUtils.isNotEmpty(additionalTaxRate)) {
        // 税率
        if (taxCalculationMethod == TaxCalculationMethodEnum['GENERAL'].code) {
          constructObj.projectTaxCalculation.taxRate = additionalTaxRate.rate;
          constructObj.projectTaxCalculation.additionalTaxRate = additionalTaxRate.rate;
        }else {
          constructObj.projectTaxCalculation.simpleRate = additionalTaxRate.rate;
        }
      }
    }
    if (!constructIs22De && taxCalculationMethod == TaxCalculationMethodEnum['GENERAL'].code) {
      // 如果是12定额的一般计税  还需要税改文件
      constructObj.projectTaxCalculation.taxReformDocumentsId = taxReformDocumentsId;
    }

    const unitList = PricingFileFindUtils.getUnitList(constructId);
    if (ObjectUtils.isEmpty(unitList)) {
      return;
    }
    for (const unit of unitList) {
      const is22Unit = PricingFileFindUtils.is22Unit(unit);
      // 修改单位工程的计税方式
      await this.updateUnitTaxCalculationMethod(unit, taxCalculationMethod, taxReformDocumentsId, is22Unit, constructObj);
      // 取费表调整
      await this.updateFeeFileByTax({constructId:constructId,singleId:unit.spId,unitId:unit.sequenceNbr})

      // 市场价处理
      await this.updateRcjByTax({constructId:constructId,singleId:unit.spId,unitId:unit.sequenceNbr,unit:unit});

      // 单价构成文件调整
      await this.updateUpcTemplateDJGC({constructId: constructId, singleId: unit.spId, unitId: unit.sequenceNbr, unit});

      // 费用计取调整
      await this.service.autoCostMathService.autoCostMath({
        constructId: constructId,
        singleId: unit.spId,
        unitId: unit.sequenceNbr
      });
      // 费用汇总调整
      await this.service.unitCostSummaryService.getDefaultUnitCostSummary(unit);

      //汇总计算
      this.service.unitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        singleId: unit.spId,
        unitId: unit.sequenceNbr
      });
    }

  }

  async updateUnitTaxCalculationMethod(unit, taxCalculationMethod, taxReformDocumentsId, is22De, constructObj) {
    unit.projectTaxCalculation.taxCalculationMethod = taxCalculationMethod;
    if (!is22De && taxCalculationMethod == TaxCalculationMethodEnum['GENERAL'].code) {
      // 如果是12定额的一般计税  还需要税改文件
      unit.projectTaxCalculation.taxReformDocumentsId = taxReformDocumentsId;
    } else {
      unit.projectTaxCalculation.taxReformDocumentsId = null;
    }
    // 区市
    unit.projectTaxCalculation.taxPayingRegion = TaxPayingRegionEnum.URBAN_AREA.value;
    // 税率
    unit.projectTaxCalculation.taxRate = constructObj.projectTaxCalculation.taxRate;
    unit.projectTaxCalculation.additionalTaxRate = constructObj.projectTaxCalculation.additionalTaxRate;

    if (!is22De && taxCalculationMethod == TaxCalculationMethodEnum['GENERAL'].code) {
      const outputTaxRate = await this.service.projectTaxCalculationService.getOutputTaxRate(GsjRateKindEnum.SJ.code, taxCalculationMethod == TaxCalculationMethodEnum['GENERAL'].code ? '一般计税方法中销项税税率' : '简易计税方法市区');
      if (ObjectUtils.isNotEmpty(outputTaxRate)) {
        // 销项税费率
        unit.projectTaxCalculation.outputTaxRate = outputTaxRate.rate;
      }
    }


    if (!is22De && taxCalculationMethod == TaxCalculationMethodEnum['SIMPLE'].code){
      const additionalTaxRate = await this.service.projectTaxCalculationService.getAdditionalTaxRate(GsjRateKindEnum.SJ.code, taxCalculationMethod, TaxPayingRegionEnum.URBAN_AREA.value);
      unit.projectTaxCalculation.simpleRate = additionalTaxRate.rate;
    }
  }


  /**
   *
   * @param args
   * @returns {Promise<void>}
   */
  async get22ConstructUnit12de(args){

    const { constructId } = args;
    let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
    if (ObjectUtils.isEmpty(projectObjById) || projectObjById.deStandardReleaseYear == ConstantUtil.DE_STANDARD_12){
      return false;
    }

    let unitList = PricingFileFindUtils.getUnitList(constructId);
    if (ObjectUtils.isEmpty(unitList)){
      return false;
    }

    for (let unitListKey of unitList) {
      let deStandardReleaseYear = unitListKey.deStandardReleaseYear;

      if (deStandardReleaseYear == ConstantUtil.DE_STANDARD_12){
        return true;
      }
    }

    return false;
  }

  /**
   * 获取刊物说明
   * @returns {Promise<void>}
   */
  async getPeriodicalData(){
    let periodicalData = await this.app.appDataSource.getRepository(BasePolicyDocument).find({
      where: {
        fileType: 4
      }
    });

    return periodicalData;
  }


  /**
   * 获取政策文件
   * @returns {Promise<void>}
   */
  async getPolicyData(){
    let vo = new FeeCollectionVO();
    let basePolicyDocumentMap = {};
    // 政策文件
    let baseAreaList = await this.app.appDataSource.getRepository(BaseArea).find({
      where: {
        pid: 130000
      }
    });
    if (!ObjectUtils.isEmpty(baseAreaList)) {
      let areaIds = baseAreaList.map(obj => obj.sequenceNbr);
      // 根据省下面的市一级  查询对应的人工费
      let rgfDocumentList = await this.app.appDataSource.getRepository(BasePolicyDocument).find({
        where: {
          fileType: PolicyDocumentTypeEnum.RGF.code,
          areaId: In(areaIds)
        }
      });
      if (!ObjectUtils.isEmpty(rgfDocumentList)) {
        //按照时间倒序排序
        rgfDocumentList.sort(function(a, b) {
          return b.fileDate.localeCompare(a.fileDate);
        });

        for (const basePolicyDocumentDTO of rgfDocumentList) {
          //去掉政策文件的.pdf
          basePolicyDocumentDTO.name = basePolicyDocumentDTO.name.replace('.pdf', '');
        }
        //根据城市名称分组
        const map = rgfDocumentList.reduce((groups, item) => {
          const { cityName } = item;
          groups[cityName] = groups[cityName] || [];
          groups[cityName].push(item);
          return groups;
        }, {});
        basePolicyDocumentMap[PolicyDocumentTypeEnum.RGF.desc] = map;
      }
      // 再查询安防费率和规费
      let resMap = await this.getPolicyDocumentData();
      for (const k in resMap) {
        let typeEnum = Object.values(PolicyDocumentTypeEnum).find(item => item.code == k);
        if (!ObjectUtils.isEmpty(typeEnum)) {
          let v = resMap[k];
          basePolicyDocumentMap[typeEnum.desc] = v;
        }
      }
    }

    vo.basePolicyDocumentMap = basePolicyDocumentMap;
    return vo;
  }


  /**
   * 切换计税方式 人材机 对应价格处理
   * @param unit
   * @param isSimple
   * @returns {Promise<void>}
   */
  async changeTaxCalculationMethod(unit,isSimple){

    if (ObjectUtils.isEmpty(unit) || ObjectUtils.isEmpty(unit.constructProjectRcjs)){
      return null;
    }

    let map = new Map();

    let allIdMap = new Map();
    for (let k of unit.constructProjectRcjs) {
      allIdMap.set(k.sequenceNbr,k.standardId);
    }
    if (ObjectUtils.isEmpty(allIdMap.size)){
      return ;
    }

    const allSet = new Set(allIdMap.values());
    let allIds  = [...allSet];

    let rcjObjs = await this.service.baseRcj2022Service.getRcjListByRcjIdList(allIds);
    let rcjObjMap = new Map();
    rcjObjs.forEach((v) => rcjObjMap.set(v.sequenceNbr, v));


    for (let k of unit.constructProjectRcjs) {
      if (!ObjectUtils.isEmpty(k.standardId)){
        let v1 = rcjObjMap.get(k.standardId);
        if ((isSimple && k.dePrice ==k.priceMarket && k.taxRate == v1.taxRate) || (!isSimple &&  k.dePrice == k.priceMarketTax && k.taxRate == v1.taxRate)){
          if (k.kind != 4 && k.kind !=5) {
            map.set(k.sequenceNbr, k.standardId);
          }else {
            k.marketPrice = isSimple?k.priceMarketTax:k.priceMarket;
            k.dePrice = k.marketPrice;
          }
        }else {
          k.marketPrice = isSimple?k.priceMarketTax:k.priceMarket;
          k.dePrice = isSimple?k.priceBaseJournalTax:k.priceBaseJournal;
        }
      }else {
        k.marketPrice = isSimple?k.priceMarketTax:k.priceMarket;
        k.dePrice = isSimple?k.priceBaseJournalTax:k.priceBaseJournal;
      }
    }

    if (!ObjectUtils.isEmpty(unit.rcjDetailList)){
      for (let k of unit.rcjDetailList) {
        if (!ObjectUtils.isEmpty(k.standardId)){
          if ((isSimple && k.dePrice ==k.priceMarket && k.taxRate == v1.taxRate) || (!isSimple &&  k.dePrice == k.priceMarketTax && k.taxRate == v1.taxRate)){
            if (k.kind != 4 && k.kind !=5) {
              map.set(k.sequenceNbr, k.standardId);
            }else {
              k.marketPrice = isSimple?k.priceMarketTax:k.priceMarket;
              k.dePrice = k.marketPrice;
            }
          }else {
            k.marketPrice = isSimple?k.priceMarketTax:k.priceMarket;
            k.dePrice = isSimple?k.priceBaseJournalTax:k.priceBaseJournal;
          }
        }else {
          k.marketPrice = isSimple?k.priceMarketTax:k.priceMarket;
          k.dePrice = isSimple?k.priceBaseJournalTax:k.priceBaseJournal;
        }
      }
    }


    if (ObjectUtils.isEmpty(map.size)){
      return ;
    }

    /*const valueSet = new Set(map.values());
    let ids  = [...valueSet];*/

    for (let k of unit.constructProjectRcjs) {
      if (map.has(k.sequenceNbr)){
        let v1 = rcjObjMap.get(k.standardId);
        k.marketPrice = isSimple?v1.priceBaseJournalTax:v1.priceBaseJournal;
        if (isSimple){
          k.priceMarketTax = k.marketPrice;
          k.priceMarket = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(100,NumberUtil.divide(k.priceMarketTax,NumberUtil.add(100,k.taxRate))));

        }else {
          k.priceMarket = k.marketPrice;
          k.priceMarketTax = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(0.01,NumberUtil.multiply(k.priceMarket,NumberUtil.add(100,k.taxRate))));
        }
        k.dePrice = isSimple?v1.priceBaseJournalTax:v1.priceBaseJournal;
      }
    }

    if (!ObjectUtils.isEmpty(unit.rcjDetailList)){
      for (let k of unit.rcjDetailList) {
        if (map.has(k.sequenceNbr)){
          let v1 = rcjObjMap.get(k.standardId);
          k.marketPrice = isSimple?v1.priceBaseJournalTax:v1.priceBaseJournal;
          if (isSimple){
            k.priceMarketTax = k.marketPrice;
            k.priceMarket = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(100,NumberUtil.divide(k.priceMarketTax,NumberUtil.add(100,k.taxRate))));
          }else {
            k.priceMarket = k.marketPrice;
            k.priceMarketTax = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(0.01,NumberUtil.multiply(k.priceMarket,NumberUtil.add(100,k.taxRate))));
          }
          k.dePrice = isSimple?v1.priceBaseJournalTax:v1.priceBaseJournal;
        }
      }
    }

  }




}

BaseFeeFileService.toString = () => '[class BaseFeeFileService]';
module.exports = BaseFeeFileService;
