<!--
 * @Descripttion: 
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-22 09:30:22
-->
<template>
  <div>
    <common-modal
      @close="cancel()"
      className="dialog-comm tree-dialog"
      :showClose="!loading"
      :loading="loading"
      width="auto"
      v-model:modelValue="dialogVisible"
      :title="titleName"
    >
      <div class="tree-content-wrap">
        <div class="tree-list">
          <div class="dialog-content">
            <div class="title">当前标段结构</div>
            <div class="list" v-if="currentTreeData">
              <a-radio-group v-model:value="currentSelected.keys">
                <a-tree
                  :defaultExpandAll="true"
                  show-line
                  showIcon
                  :tree-data="currentTreeData"
                  :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
                  v-model:expandedKeys="expandedKeys"
                  @select="currentSelect"
                >
                  <template #switcherIcon="{ switcherCls }">
                    <down-outlined :class="switcherCls" />
                  </template>
                  <template
                    #title="{ levelType, id, name, whetherNew, num, originalFlag }"
                  >
                    <a-radio
                      class="mIcon"
                      v-if="
                        menuOperator.singleLevelList.includes(levelType) && !whetherNew
                      "
                      :disabled="singleDisable || originalFlag"
                      :value="id"
                    ></a-radio>
                    <icon-font
                      class="mIcon del-icon"
                      :class="`delicon${levelType}`"
                      type="icon-shanchu1"
                      v-show="whetherNew"
                      @click="delImportItem(id)"
                    />
                    <span class="check-labels">{{ name }}</span>
                  </template>
                </a-tree>
              </a-radio-group>
            </div>
          </div>

          <a-button type="primary" @click="move">
            <template #icon><left-outlined /></template>
            导入
          </a-button>

          <div class="dialog-content">
            <div class="title">
              <span>导入工程标段结构</span>
              <!-- <div class="checkhandle">
                <a-radio-group v-model:value="checkStatus" @change="changeStatus">
                  <a-radio value="all">全部</a-radio>
                  <a-radio value="part">取消全选</a-radio>
                </a-radio-group>
              </div> -->
            </div>
            <div class="list" v-if="importTreeData">
              <a-tree
                :defaultExpandAll="true"
                checkable
                show-line
                multiple
                :tree-data="importTreeData"
                @check="importSelect"
                :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
                :checkedKeys="importCheckedKeys"
                :selectedKeys="importCheckedKeys"
              >
                <template #switcherIcon="{ switcherCls, children }">
                  <down-outlined :class="switcherCls" />
                </template>
              </a-tree>
            </div>
          </div>
        </div>
        <!-- <div class="group-list">
        <a-radio-group v-model:value="dataStatus">
          <a-radio value="all">以当前项目费率为准</a-radio>
          <a-radio value="part">以导入项目标准率为准</a-radio>
        </a-radio-group>
      </div> -->
        <div class="footer-btn-list">
          <a-button @click="cancel()">取消</a-button>
          <a-button
            type="primary"
            @click="handleOk"
            :title="handleTip()"
            :loading="submitLoading"
            >确定</a-button
          >
        </div>
      </div>
    </common-modal>
    <common-modal
      @close="setCancel()"
      className="dialog-comm tree-dialog"
      :showClose="!loading"
      :loading="loading"
      width="400"
      v-model:modelValue="setDialogVisible"
      title="价差取费设置"
    >
      <div style="margin: 10px 0 10px">设置导入费率和政策性文件</div>
      <a-radio-group v-model:value="radioData">
        <a-radio value="a" style="margin: 20px 0">导入后费率按照当前项目设置</a-radio>
        <a-radio value="b">导入后费率按要导入的工程设置</a-radio>
      </a-radio-group>
      <div class="footer-btn-list">
        <a-button type="primary" @click="queryMove" :loading="submitLoading"
          >确定</a-button
        >
      </div>
    </common-modal>
  </div>
</template>
<script setup>
import { message } from "ant-design-vue";
import { ref, reactive, watch, nextTick, toRaw, defineExpose, watchEffect } from "vue";
import { useRoute } from "vue-router";
import csProject from "@/api/jiesuanApi";
import { getJsAsideTreeList } from "@/api/jiesuanApi";
import { DownOutlined, LeftOutlined } from "@ant-design/icons-vue";
import xeUtils from "xe-utils";
import { ConstructMenuOperator } from "@/components/editJsProjectStructure/ConstructMenuOperator";
const menuOperator = new ConstructMenuOperator();
import { projectDetailStore } from "@/store/projectDetail";
const store = projectDetailStore();

const emits = defineEmits(["closeDialog"]);
const route = useRoute();
const submitLoading = ref(false);
const dialogVisible = ref(false);
const setDialogVisible = ref(false);
const currentTreeData = ref(null);
const importTreeData = ref(null);
const importCheckedKeys = ref([]);
const checkStatus = ref(null); //全选，取消全选
const hasSingle = ref(false); //当前项目，是否有单项结构
const importSingleList = ref([]); // 导入项目，所有的单项结构
const loading = ref(false);
const expandedKeys = ref([]);
const levelTypeTwo = ref([]);
const radioData = ref("a");
const titleName = ref("导入文件");
const currentSelected = reactive({
  initData: [],
  keys: "",
  node: null,
});
const importSelected = reactive({
  historyKeys: [],
  handleKeys: [], //点击了全选
});
const dataStatus = ref(null);
const importYsf = ref(null);

const props = defineProps(["importJsOtherList", "otherData"]);

// 处理确定的提示语
const handleTip = () => {
  let msg = "";
  if (!importSelected.historyKeys.length) {
    msg = "请选择导入项目!";
  }

  // else if(!dataStatus.value){
  //   msg="请选择费率标准!"
  // }
  return msg;
};

const currentSelect = (selectedKeys, { selected, selectedNodes, node, event }) => {
  if (node.originalFlag) {
    currentSelected.keys = "";
    currentSelected.node = null;
  } else if (
    !node.whetherNew &&
    [1, 2].includes(node.levelType) &&
    !singleDisable.value
  ) {
    currentSelected.keys = selectedKeys[0];
    currentSelected.node = node.dataRef;
  }
};

const importSelect = (checkedKeys, { checked, checkedNodes, node, event }) => {
  checkStatus.value = null;
  // 处理子不关联父级，父级关联子级
  let ids = [];
  const { id, children, levelType } = node;
  ids.push(id);
  if (levelType == 2 && children && children.length) {
    children.forEach((child) => {
      ids.push(child.id);
    });
  }
  if (checked) {
    // 点击勾选
    importCheckedKeys.value = [...new Set([...toRaw(importCheckedKeys.value), ...ids])];
  } else {
    // 点击取消
    importCheckedKeys.value = toRaw(importCheckedKeys.value).filter((item) => {
      return !ids.includes(item);
    });
  }
};

watch(importCheckedKeys, (newValue, oldValue) => {
  currentListAutoHandle();
});

// 监听右侧选择了单项，左侧的所有单项禁用，并且默认挂载到工程项目下
const singleDisable = ref(false);
const currentListAutoHandle = (node = null) => {
  // singleDisable.value = false;
  // if (node && menuOperator.singleLevelList.includes(node.levelType)) {
  //   singleDisable.value = true;
  // } else if (importSingleList.value.length) {
  //   const list = importSelected.historyKeys.length
  //     ? importCheckedKeys.value.filter((i) => !importSelected.historyKeys.includes(i))
  //     : importCheckedKeys.value;
  //   singleDisable.value = list.some((i) => importSingleList.value.includes(i));
  // }
  // if (singleDisable.value) {
  //   currentSelected.keys = "";
  // }
};

// 修改导入项目选择
const changeStatus = async () => {
  importSelected.handleKeys = [];
  await flattenTree(importTreeData.value, true);
  importCheckedKeys.value =
    checkStatus.value === "all"
      ? [...importSelected.handleKeys, ...importSelected.historyKeys]
      : importSelected.historyKeys;
};

// 点击导入
const move = async () => {
  if (!currentTreeData.value.length) {
    message.error("当前项目错误！");
    return;
  }
  setDialogVisible.value = true;
};
// 确认导入
let newMoveList = ref([]);
const queryMove = async () => {
  setDialogVisible.value = false;
  // 查找要复制过去的元素
  const elNodes = importCheckedKeys.value
    .filter((i) => !importSelected.historyKeys.includes(i))
    .map((i) => toRaw(findElement(i, importTreeData.value)))
    .filter((i) => i.levelType === 3);
  console.log("elNodes", elNodes);
  const tree = xeUtils.toArrayTree(elNodes, { key: "id" });
  // 设置whetherNew
  recursionTree(tree, "addNew");
  // 向左侧插入数据前置校验
  const res = await appendAfterCheck(elNodes, tree);
  if (!res.status) {
    message.error(res.msg);
    return;
  }
  let target = menuOperator.getSingleNew(
    currentSelected.node,
    xeUtils.toTreeArray(currentTreeData.value)
  ); //获取选中目标最里层单项数据
  disposeCheckName(target.children, elNodes);
  newMoveList.value = await handleName(target.children, elNodes); //获取处理后的列表
  console.log(newMoveList.value);
  newMoveList.value.map((i) => (i.parentId = target.id));
  console.log("currentTreeData", newMoveList.value, toRaw(currentTreeData.value));
  //此处没有给左侧currentTreeData.value赋值
  currentTreeData.value = appendInTreeFun(
    target.id,
    currentTreeData.value,
    newMoveList.value
  );
  //  导入项目，禁用已经导入的
  disableItem();
  currentListAutoHandle();
};
const appendInTreeFun = (id, tree, obj) => {
  tree.forEach((ele) => {
    if (ele.id === id) {
      ele.children = [...obj];
      return tree;
    }
    // else if (ele.children) {
    appendInTreeFun(id, ele.children, obj);
    // }
  });
  return tree;
};
const disposeCheckName = (list1, list2) => {
  list1.map((i) => (i.checkName = i.name));
  list2.map((i) => (i.checkName = i.name));
};
// 处理名称是否有相同如果有则自增一
const handleName = async (initList, currentList) => {
  function getNameCount(namesMap, name) {
    return (namesMap.get(name) || 0) + 1;
  }
  function setName(item) {
    let NAME = "";
    let count = 0;
    let nameList = [];
    for (let [k, v] of namesMap) {
      nameList.push(k.trim());
    }
    for (let [index, name] of nameList.entries()) {
      let lastName = index > 0 ? `${item.name}_${index + 1}` : `${item.name}`;
      let currentName = index > 0 ? `${item.name}_${index}` : `${item.name}`;
      if (!nameList.includes(lastName.trim()) && nameList.includes(currentName.trim())) {
        NAME = lastName.trim();
        namesMap.set(lastName.trim(), 0);
        break;
      } else if (
        nameList.includes(lastName.trim()) &&
        !nameList.includes(currentName.trim())
      ) {
        NAME = currentName.trim();
        namesMap.set(currentName.trim(), 0);
        break;
      }
    }
    if (namesMap.has(item.checkName)) {
      NAME = NAME || `${item.name}_${nameList.length}`;
    } else {
      NAME = item.name;
    }
    return NAME;
  }
  // 初始化一个映射存储每个名称出现的次数
  let namesMap = new Map();
  initList.forEach((item) => {
    const count = getNameCount(namesMap, item.name);
    namesMap.set(item.name, count);
  });
  const renamedItems = currentList.reduce((acc, item, index) => {
    const count = getNameCount(namesMap, item.checkName);
    const newName = count > 1 ? `${item.checkName}_${count - 1}` : `${item.checkName}`;
    let newItem = { ...item, name: newName, num: count };
    if (namesMap.has(newName)) {
      const handleName = setName({ ...item });
      newItem = { ...item, name: handleName, num: count };
    }
    namesMap.set(item.checkName, count);
    acc.push(newItem);
    return acc;
  }, []);
  for (let i of renamedItems) {
    const count = getNameCount(namesMap, i.checkName);
    i.num = count;
  }
  return [...initList, ...renamedItems];
};

// 开始向左侧插入数据前置校验
const appendAfterCheck = (elNodes, tree) => {
  return new Promise((resolve, reject) => {
    if (!tree.length) {
      resolve({ status: false, msg: "请选择导入层级" });
      return;
    }

    // 查询导入项目选择的是否有单项
    const singleList = elNodes.filter((i) => {
      return menuOperator.singleLevelList.includes(i.levelType);
    });

    //导入的是否全是单位
    const isAllUnit = elNodes.every((i) => {
      return menuOperator.unitLevelList.includes(i.levelType);
    });
    console.log("currentSelected.keys", currentSelected.keys);
    // 获取当前项目选择了什么工程
    const currentNode = currentSelected.keys
      ? menuOperator.getSingleNew(
          currentSelected.node,
          xeUtils.toTreeArray(currentTreeData.value)
        )
      : currentSelected.initData[0];

    if (
      menuOperator.singleLevelList.includes(currentNode.levelType) &&
      singleList.length
    ) {
      // 选择了单项，但是选择了导入单项
      resolve({ status: false, msg: "请导入正确的单位层级结构" });
      return;
    }

    // if ([1].includes(currentNode.levelType) && isAllUnit && hasSingle.value) {
    //   // 有单项，但是未选择单项，不能导入纯单位
    //   resolve({ status: false, msg: "请选择当前项目单项" });
    //   return;
    // }

    // 导入的项目，若出现选中某一单项及其下单位，且选中其他单项下某单位工程未选择其对应的单项层级，此时点击【加入左侧】提示用户“请导入完整层级结构”
    if (singleList.length && singleList.length !== tree.length) {
      // 有单项，并且单项的数量和树结构的不相等
      resolve({ status: false, msg: "请导入完整层级结构" });
      return;
    }

    resolve({ status: true });
  });
};

/**
 * 处理当前项目的打开节点
 * @param {*} tree
 * @param {*} isAdd
 */
const handleExpandedKeys = (tree, isAdd = true) => {
  for (let i = 0; i < tree.length; i++) {
    if (
      menuOperator.singleLevelList.includes(tree[i].levelType) &&
      !expandedKeys.value.includes(tree[i].id)
    ) {
      expandedKeys.value.push(tree[i].id);
    }
  }
};

// 插入数据
const appendTree = (tree, target) => {
  let appendId = target.id || currentTreeData.value[0].id;
  handleExpandedKeys(tree);
  // 插入数据
  currentTreeData.value = appendNodeInTree(appendId, currentTreeData.value, toRaw(tree));
};

const delListId = ref([]); // 左侧点击删除的数据
const delImportItem = async (id) => {
  // 左侧的删除数据
  removeNodeInTree(currentTreeData.value, id);

  currentListAutoHandle();

  importSelected.historyKeys = toRaw(importSelected.historyKeys).filter((i) => {
    return !delListId.value.includes(i);
  });

  importCheckedKeys.value = toRaw(importSelected.historyKeys).filter((i) => {
    return !delListId.value.includes(i);
  });
  // 解除禁用操作
  recursionTree(importTreeData.value, "delDisable");
};

// 导入的数据，将左侧数据禁用
const disableItem = async () => {
  // 存储历史上一步操作的数据
  importSelected.historyKeys = xeUtils.clone(importCheckedKeys.value, true);

  recursionTree(importTreeData.value, "addDisable");
};

// 在树中查找匹配的元素
const findElement = (key, nodes) => {
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];
    if (node.id === key) {
      const list = xeUtils.clone(node, true);
      delete list.children;
      return list;
    }
    if (node.children) {
      const result = findElement(key, node.children);
      if (result) {
        return result;
      }
    }
  }
  return null;
};

// 扁平化树结构
const flattenTree = (tree, isLog = false, parent = null, result = []) => {
  for (const node of tree) {
    const { children, ...data } = node;
    result.push({ ...data });

    if (!node.disabled && isLog && !node.initDisable) {
      importSelected.handleKeys.push(node.id);
    }

    if (children && children.length > 0) {
      flattenTree(children, isLog, node.parentId, result);
    }
  }
  return result;
};

// 树结构插入数据
const appendNodeInTree = (id, tree, obj) => {
  tree.forEach((ele) => {
    if (ele.id === id) {
      obj.forEach((i) => (i.parentId = id));
      ele.children = ele.children ? [...ele.children, ...obj] : obj;
    } else if (ele.children) {
      appendNodeInTree(id, ele.children, obj);
    }
  });
  return tree;
};

// 树结构删除数据
const removeNodeInTree = (treeList, id) => {
  if (!treeList || !treeList.length) {
    return;
  }
  for (let i = 0; i < treeList.length; i++) {
    if (treeList[i].id === id) {
      let delId = [id];
      if (treeList[i].children && treeList[i].children.length) {
        treeList[i].children.map((i) => {
          delId.push(i.id);
        });
      }
      delListId.value = delId;

      treeList.splice(i, 1);
      break;
    }
    removeNodeInTree(treeList[i].children, id);
  }
};

// 循环，处理树
/*
**type addDisable  添加disable， 如果所有的子级勾选了，父级默认给勾选上
       addNew      添加new
*/
const recursionTree = (treeList, type) => {
  treeList.forEach((node) => {
    setValue(node, type);
    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => {
        setValue(child, type);
      });

      // addDisable  添加disable， 如果所有的子级勾选了，父级默认给勾选上
      if (
        ["addDisable", "delDisable"].includes(type) &&
        menuOperator.singleLevelList.includes(node.levelType)
      ) {
        let isAllUse = node.children.every((i) => {
          return importSelected.historyKeys.includes(i.id);
        });
        switch (type) {
          case "addDisable":
            if (isAllUse) {
              node.disabled = true;
            } else {
              node.disabled = false;
            }
            break;
          case "delDisable":
            node.disabled = isAllUse && !importSelected.historyKeys.includes(node.id);
            break;
          default:
            break;
        }
      }

      recursionTree(node.children, type);
    }
  });
};

//设置值
const setValue = (data, type) => {
  if (!data.initDisable) {
    // 过滤最原始就不能编辑的值
    switch (type) {
      case "addDisable":
        data.disableCheckbox = importCheckedKeys.value.includes(data.id);
        data.disabled = importCheckedKeys.value.includes(data.id);
        break;
      case "addNew":
        data.whetherNew = importCheckedKeys.value.includes(data.id);
        data.costType = radioData.value;
        break;
      case "delDisable":
        if (delListId.value.includes(data.id)) {
          data.disableCheckbox = false;
          data.disabled = false;
        }
        break;
      default:
        break;
    }
  }
};

const findValueInTree = (data) => {
  if (!data) return;
  // 遍历树节点
  for (let i = 0; i < data.length; i++) {
    const node = data[i];
    if ([1, 2].includes(node.levelType)) {
      expandedKeys.value.push(node.id);
    }
    if (node.children) {
      findValueInTree(node.children);
    }
  }
};

// 当前项目
const getTreeList = async () => {
  expandedKeys.value = [];
  await getJsAsideTreeList(route.query.constructSequenceNbr).then((res) => {
    if (res.code === 200) {
      const data = xeUtils.toArrayTree(
        Array.isArray(res.result) ? res.result : [res.result],
        {
          children: "children",
          id: "id",
          pid: "parentId",
          sort: "sort",
        }
      );
      currentTreeData.value = data;
      currentSelected.initData = data;
      handleTree(data);
      findValueInTree(data);
      // 获取导入项目
      getImportTreeList();
    }
  });
};
// 遍历获取所有单项数据
const handleTree = (arr) => {
  let values = [];
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].levelType !== 2) {
      handleTree(arr[i].children);
    } else {
      values.push(arr[i]);
    }
  }
  let levelTwo = levelTypeTwo.value;
  levelTypeTwo.value = levelTwo.concat(values);
};

// 导入项目列表
const getImportTreeList = async () => {
  const data = xeUtils.toArrayTree(
    Array.isArray(importYsf.value) ? importYsf.value : [importYsf.value],
    {
      children: "children",
      id: "id",
      pid: "parentId",
      sort: "sort",
    }
  );

  const list = await flattenTree(toRaw(currentTreeData.value));
  // 判断当前项目有没有单项
  hasSingle.value = list.some((i) => {
    return menuOperator.singleLevelList.includes(i.levelType);
  });
  handleImportTree(data, hasSingle.value);
  importTreeData.value = data;

  nextTick(() => {
    loading.value = false;
  });
};

// status true， 保存成功
const cancel = (status = false) => {
  dialogVisible.value = false;
  if (!status) {
    csProject.deleteImportProject(route.query.constructSequenceNbr);
  }
  emits("closeDialog", { status });
};
const setCancel = (status = false) => {
  setDialogVisible.value = false;
};

// 点击确定按钮
const handleOk = async () => {
  let treeData = currentTreeData.value;
  handleOkQuery(treeData);
};
const handleOkQuery = async (treeData) => {
  const status = await handleTip();
  if (status) {
    message.error(status);
    return;
  }
  if (submitLoading.value) return;
  // submitLoading.value = true;
  const postData = {
    constructId: route.query.constructSequenceNbr,
    importType: 1,
    projectStructureTree: JSON.parse(JSON.stringify(treeData[0])),
  };
  console.log(postData);
  csProject
    .importSingleJS(postData)
    .then((res) => {
      if (res.status === 200) {
        message.success("导入成功");
        setTimeout(() => {
          location.reload();
        }, 1000);
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};
// 初始化，处理导入项目
const handleImportTree = (tree, hasSingle, parentId = null) => {
  tree.forEach((node) => {
    // 空的单项禁用，工程项目禁用, hasSingle,当前项目没有单项的，导入项目单项也禁用
    const DisableStatus =
      [1].includes(node.levelType) ||
      (menuOperator.singleLevelList.includes(node.levelType) &&
        (!node.children || !node.children.length || !hasSingle));
    // 测试id不同
    // node.id = node.id + 2 //测试的
    node.whetherNew = false;
    node.disableCheckbox = DisableStatus;
    node.disabled = DisableStatus;
    if (DisableStatus) {
      // 设置最原始的值，默认就是不能编辑
      node.initDisable = true;
    }

    // 判断导入项目有没有单项
    if (menuOperator.singleLevelList.includes(node.levelType)) {
      importSingleList.value.push(node.id);
    }

    if (node.children && node.children.length > 0) {
      handleImportTree(node.children, hasSingle, node.id);
    }
  });
};

/**
 *
 * @param {*} ossPath 导入文件的路径
 */
const openDialog = async (data) => {
  if (data) {
    loading.value = true;
    importYsf.value = data;
    dialogVisible.value = true;
    await getTreeList();

    let clickNodeParent = menuOperator.getParentItem(
      store.currentTreeInfo,
      xeUtils.toTreeArray(currentTreeData.value)
    );
    switch (clickNodeParent.type) {
      case 1:
        titleName.value = "导入变更文件";
        break;
      case 2:
        titleName.value = "导入签证文件";
        break;
      case 3:
        titleName.value = "导入漏项文件";
        break;
      case 4:
        titleName.value = "导入索赔文件";
        break;
      case 5:
        titleName.value = "导入其他文件";
        break;
    }
    currentSelected.keys = store.currentTreeInfo.id;
    currentSelected.node = store.currentTreeInfo;
    //     titleName
    // importJsOtherList
  }
};

watchEffect(() => {
  if (props.importJsOtherList) {
    openDialog(JSON.parse(JSON.stringify(props.importJsOtherList)));
  }
});
</script>
<style lang="scss" scoped>
.del-icon {
  font-size: 17px;
}

.mIcon {
  margin-right: 10px;
}

.tree-content-wrap {
  width: 70vw;
  height: 100%;
  max-width: 800px;
  min-width: 700px;
  display: flex;
  flex-direction: column;
}

.tree-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dialog-content {
  background: rgba(250, 250, 250, 0.39);
  border: 1px solid #e1e1e1;
  border-radius: 2px;
  display: flex;
  flex-direction: column;
  height: 50vh;
  width: 43%;
  overflow: hidden;

  &:hover {
    overflow: auto;
  }

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 7px 13px;
    background-color: #eaeaea;

    span {
      font-size: 14px;
      font-weight: 600;
      color: #333333;
    }

    .checkhandle {
      display: flex;
      align-items: center;
    }
  }

  .list {
    flex: 1;
    padding: 16px 18px;
    background-color: #fafafa;
    overflow: hidden;

    &:hover {
      overflow: auto;
    }

    ::v-deep .ant-tree {
      background-color: #fafafa;

      .ant-tree-switcher-noop {
        opacity: 0;
      }

      .ant-tree-switcher {
        background-color: transparent;
      }
    }
  }
}

.group-list {
  margin: 12px 0 30px;
  padding: 9px 18px;
  background: rgba(250, 250, 250, 0.39);
  border: 1px solid #e1e1e1;
  border-radius: 2px;
}

.footer-btn-list {
  margin-top: 30px;
}
</style>
