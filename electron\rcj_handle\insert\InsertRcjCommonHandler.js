const EE = require('../../../core/ee');
const _ = require("lodash");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const {ConstructProjectRcj} = require("../../model/ConstructProjectRcj");
const {rcjModelConvertRules} = require("../rules/modelConvertRules")
const {UnitRcjCacheUtil} = require("../cache/UnitRcjCacheUtil");



class InsertRcjCommonHandler {
    constructor(ctx) {
        this.ctx = ctx;
        //this.allRcjList = [];
        this.parentRcj = [];
        this.result = null;
        this.childRcj = new Map();
        this.rcjResQtyMap = {};
        this.de = this.ctx.de;
    }

    async prepare(params) {
        throw new Error("需要子类去实现");
    }

    async process() {
        let parentRcjList = this.parentRcj;
        this.parentRcj = [];
        let{service}=EE.app;
        for (let i = 0; i < parentRcjList.length; i++) {
            let rcj = Object.assign(new ConstructProjectRcj(), parentRcjList[i]);
            await this._attributeAssignment(rcj);
            await this._getExistValue(rcj);
            //await this._otherRcj(rcj);
            await service.constructProjectRcjService.rcjResQtyCoefficient(rcj,this.ctx.unit,true);
            this.parentRcj.push(rcj);
        }

        const keys = Array.from(this.childRcj.keys());
        if (ObjectUtils.isNotEmpty(keys)) {
            for (const key of keys) {
                let rcjList = this.childRcj.get(key);
                for (let j = 0; j < rcjList.length; j++) {
                    let rcj = Object.assign(new ConstructProjectRcj(), rcjList[j]);
                    await this._attributeAssignment(rcj);
                    await this._getExistValue(rcj);
                    //await this._otherRcj(rcj);
                    rcjList[j] = rcj;
                }
                this.childRcj.set(key, rcjList);
            }
        }
        //拼接标准人材机数据
        if (ObjectUtils.isNotEmpty(this.standardDeFlag)){
            if(this.standardDeFlag)this.jointStandardRcj();
        }

    }

    /**
     * 拼接标准人材机数据
     */
    jointStandardRcj(){
        if (ObjectUtils.isEmpty(this.parentRcj)){
            this.de.jointStandardRcj =''
            return;
        }
        //如果是补充人材机的话 不需要拼接
        let supplementRcj = this.parentRcj.find(k =>k.isSupplement == 1);
        if (ObjectUtils.isNotEmpty(supplementRcj)){
            return;
        }
        this.de.jointStandardRcj = this.parentRcj.map(k =>k.materialCode).sort().join(",");
    }


    async insert(params) {
        await this.prepare(params);
        await this.process(params);
        this.ctx.unit.constructProjectRcjs.push(...this.parentRcj);
        // this.parentRcj.forEach(rcj => UnitRcjCacheUtil.add(this.ctx.unit, rcj, this.de.sequenceNbr));
        const keys = Array.from(this.childRcj.keys());
        if (ObjectUtils.isNotEmpty(keys)) {
            for (const key of keys) {
                let rcjList = this.childRcj.get(key);
                this.ctx.unit.rcjDetailList.push(...rcjList);
                //将标准的子集人材机数据入缓存
                rcjList.forEach(rcj => UnitRcjCacheUtil.add(this.ctx.unit, rcj, this.de.sequenceNbr));
            }
        }

        return this.result;
    }


    async _attributeAssignment(rcjModel) {
        this.rcj = rcjModel;
        this.rcj.formattedCoefficient = null;
        this.rcj.taxRateInit = null;
        for (const k of Object.keys(rcjModelConvertRules)) {
            let fn  =rcjModelConvertRules[k];
            if(fn){
                await fn(this);
            }
        }
        //处理补充人材机
        if (this.rcj.isSupplement ==1){
            this.rcj.dePrice = this.rcj.marketPrice;
        }
        // let {service} = EE.app;
        // //数据库数据转换为自定义model数据
        // let {sequenceNbr, rcjFlag} = this.de;
        // let {constructId, singleId, unitId} = this.ctx;
        // rcjModel.deId = sequenceNbr;
        // rcjModel.constructId = constructId;
        // rcjModel.singleId = singleId;
        // rcjModel.unitId = unitId;
        // rcjModel.type = service.baseRcjService.getRcjTypeEnumDescByCode(rcjModel.kind);
        // rcjModel.markSum = 1;
        // rcjModel.kindBackUp = rcjModel.kind;
        // rcjModel.rcjFlag = rcjFlag;
        // rcjModel.taxRemovalBackUp = rcjModel.taxRemoval;
        // if (ObjectUtils.isNotEmpty(rcjModel.levelMark)) {
        //     rcjModel.levelMark = rcjModel.levelMark.toString();
        // }
        // if (ObjectUtils.isEmpty(rcjModel.specification)) {
        //     rcjModel.specification = null;
        // }
        // //消耗量
        // rcjModel.resQty = this.rcjResQtyMap[rcjModel.sequenceNbr];
        // if (ObjectUtils.isEmpty(rcjModel.initResQty)) {
        //     rcjModel.initResQty = rcjModel.resQty;
        // }
        //
        // if (rcjModel.tempDeleteFlag) {
        //     rcjModel.tempDeleteBackupResQty = rcjModel.resQty;
        //     rcjModel.resQty = null;
        // }
        // //判断当前人材机数据是 22还是12
        // if (rcjModel.isSupplement != 1 && rcjModel.supplementDeRcjFlag != 1) {
        //     if (ObjectUtils.isNotEmpty(rcjModel.libraryCode)) {
        //         const is2022 = rcjModel.libraryCode.startsWith(ConstantUtil.YEAR_2022);
        //         if (is2022) {
        //             rcjModel.dePrice = this.ctx.isSimple ? rcjModel.priceBaseJournalTax : rcjModel.priceBaseJournal;
        //         }
        //     }
        // }
        // rcjModel.marketPrice = rcjModel.dePrice;
        // rcjModel.referenceRecord = "".concat(rcjModel.kind, rcjModel.materialName,
        //     rcjModel.specification, rcjModel.unit, rcjModel.dePrice);
    }

    columnJoin(rcj,keysRcj) {
        let tempArray = [];
        for (const key of keysRcj) {
            if (ObjectUtils.isEmpty(rcj[key])) {
                tempArray.push(null);
            } else {
                tempArray.push(rcj[key]);
            }
        }
        return tempArray.sort().join(",");
    }

    _getOriginalCode(code) {
        let str = code;
        if (str.includes("#")) {
            str = str.substring(0, str.lastIndexOf("#"));
        }
        return str;
    }

    async _getExistValue(rcj) {
        let column = ["ifDonorMaterial", "ifProvisionalEstimate",
            "markSum", "edit","supplementDeRcjFlag","dePrice"
            ,"marketPrice","sourcePrice","donorMaterialNumberManage","priceMarket","priceMarketTax","dispNo",
            "unitPostil","unitPostilState","singlePostil","singlePostilState","constructPostil","constructPostilState"];

        let keysRcj = [
            "materialName",
            "specification",
            "unit",
            "dePrice",
            "kind"
        ];

        //聚合所有人材机
        let tempAllRcjArray = ObjectUtils.isEmpty(this.ctx.unit.rcjCache)?[]:Object.values(this.ctx.unit.rcjCache);
        // let tempAllRcjArray = [];
        // tempAllRcjArray.push(...this.ctx.unit.constructProjectRcjs);
        // tempAllRcjArray.push(...this.ctx.unit.rcjDetailList);

        let rcjList = tempAllRcjArray.filter(k => this._getOriginalCode(k.materialCode) == this._getOriginalCode(rcj.materialCode)
            && this.columnJoin(rcj,keysRcj) === this.columnJoin(k,keysRcj) && k.sequenceNbr != rcj.sequenceNbr);

        if(ObjectUtils.isEmpty(rcjList)){
            return;
        }



        // let parentRcj = this.ctx.constructProjectRcjs;
        // let parentItem = parentRcj.find(i => i.materialCode === rcj.materialCode && i.sequenceNbr !== rcj.sequenceNbr);
        // if (!ObjectUtils.isEmpty(parentItem)) {
        //     column.forEach(k =>rcj[k] = parentItem[k]);
        // }
        //
        // let childRcj = this.ctx.rcjDetailList;
        // let childItem = childRcj.find(i => i.materialCode === rcj.materialCode && i.sequenceNbr !== rcj.sequenceNbr);


        let childItem = rcjList[0];
        if (!ObjectUtils.isEmpty(childItem)) {
            column.forEach(k =>rcj[k] = childItem[k]);
            if (rcj.ifDonorMaterial == 1){
                if (rcj.donorMaterialNumberManage != -1){
                    rcj.donorMaterialNumber = 0
                }
            }
        }


    }


    // async _otherRcj(rcj) {
    //     let {service} = EE.app;
    //     if (this.ctx.is2022) {
    //         return;
    //     }
    //     if (["10000001", "10000002", "10000003", "JXPB-005"].includes(rcj.materialCode)) {
    //         if (!ObjectUtils.isEmpty(this.ctx.unit.rgfId)) {
    //             let rgf = await service.basePolicyDocumentService.queryBySequenceNbr(this.ctx.unit.rgfId);
    //             rcj.marketPrice = rcj.dePrice;
    //             rcj.sourcePrice = null;
    //             if (!ObjectUtils.isEmpty(rgf)) {
    //                 rcj.sourcePrice = rgf.cityName + rgf.pricesource;
    //                 switch (rcj.materialCode) {
    //                     case "10000001":
    //                         rcj.marketPrice = rgf.zhygLevel1;
    //                         break;
    //                     case "10000002":
    //                         rcj.marketPrice = rgf.zhygLevel2;
    //                         break;
    //                     case "10000003":
    //                         rcj.marketPrice = rgf.zhygLevel3;
    //                         break;
    //                     case "JXPB-005":
    //                         rcj.marketPrice = rgf.zhygLevel2;
    //                         break;
    //                 }
    //             }
    //         }
    //     }
    // }
}

module.exports = {
    InsertRcjCommonHandler
}
