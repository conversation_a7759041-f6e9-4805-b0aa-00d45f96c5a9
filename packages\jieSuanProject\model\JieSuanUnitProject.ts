import {UnitProject} from "../../../electron/model/UnitProject";
import {RcjStageSetVo} from "./RcjStageSetVo";


export class JieSuanUnitProject extends UnitProject{


    public rcjStageSet : RcjStageSetVo; //人材机分期信息

    /**
     * 量差范围最大值
     */
    public quantityDifferenceRangeMax:number;
    /**
     * 量差范围最小值
     */
    public quantityDifferenceRangeMin:number;


    /**
     * 合同内标识
     */
    public originalFlag:boolean;

    /**
     * 指标专业
     */
    public majorIndex:string;

    /**
     * 指标查看范围  是否允许查看   true查看  false 不允许
     */

    public viewScopeFlag:boolean;

    /**
     * 指标查看范围 前端半选状态
     */
    public halfCheckFlag:boolean;

    /**
     * 单位工程归属id
     */
    public parentProjectId:string;
}


