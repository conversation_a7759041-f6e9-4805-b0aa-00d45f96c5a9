const {Controller} = require("../../core");
const {ResponseData} = require("../utils/ResponseData");
const {HttpUtils} = require("../utils/HttpUtils");
const internetAvailable = require("internet-available");
const BsRemoteUrl = require("../enum/BsRemoteUrl");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {dialog} = require("electron");
const _ = require("lodash");
const { app: electronApp, BrowserWindow } = require('electron');
const crypto = require("crypto");

class CommonController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 保存到文件
     * @param arg
     * @return {Promise<void>}
     */
    saveYsfFile(arg){
        return this.service.commonService.saveYsfFile(arg);

    }

    /**
     * 选择性导入ysf文件
     * @param arg
     * @returns {*}
     */
    importYsfFile(arg){
        return this.service.commonService.importYsfFile(arg);
    }

    /**
     * 保存导入后的ysf文件
     * @param arg
     * @returns {*}
     */
    saveImportProject(arg){
        this.service.commonService.saveImportProject(arg);
        return ResponseData.success();
    }

    /**
     * 删除导入后的ysf文件
     * @param arg
     * @returns {*}
     */
    deleteImportProject(arg){
         this.service.commonService.deleteImportProject(arg);
         return ResponseData.success();
    }


    /**
     * ysf文件选择性导出
     * @param arg
     * @return {*}
     */
    exportYsfFile(arg){
        return this.service.commonService.exportYsfFile(arg);

    }

    /**
     * 获取菜单栏数据
     * @param arg
     * @return {Promise<void>}
     */
    getMenuList(arg){
        return this.service.commonService.getMenuList(arg);

    }

    /**
     * 获取菜单栏数据
     * @param arg
     * @return {Promise<void>}
     */
    async getMenuData(arg){
        let menuData =await  this.service.commonService.getMenuData(arg);
        return ResponseData.success(menuData);
    }


    closeAllChildWindow(){
        this.service.commonService.closeAllChildWindow();
    }


    /**
     * 打开线上项目列表-BS
     * @param arg
     * @return {Promise<*>}
     */
    async openOnlineProject(arg){

        console.log(arg.agencyCode);
        console.log(arg.openId);

        let headers ={
            'AGENCY_CODE': 'HZJT',
            'PRODUCT_CODE': 'HZJT_YZJ_WEB'
        };
        let result =await HttpUtils.GET(BsRemoteUrl.openOnlineProjectList,headers,arg);
        return result;
    }

    /**
     * 判断是否有网络
     */
    async isOnline(){

        //let isOnline = false;
        let isOnline =await internetAvailable({
            timeout: 1000, // 超时时间（毫秒）
            retries: 3 ,// 重试次数
            host: '***************' //
        }).then(function(){
            return true;
        }).catch(function(){
            return false;
        });
        return ResponseData.success(isOnline);
    }



    /**
     * 菜单栏功能---另存为
     * @param arg
     * @return {*}
     */
    async fileSaveAs(arg) {
        return await this.service.commonService.fileSaveAs(arg, "");

    }

    /**
     * 设置--存储路径
     * @return {string|null}
     */
    async setSelectFolder (arg) {
        return await this.service.commonService.setSelectFolder(arg);
    }

    /**
     * 选择路径
     * @param arg
     * @return {string|null}
     */
    async selectPath(arg) {
        return ResponseData.success(await this.service.commonService.selectPath(arg));
    }


    /**
     * 查询--存储路径   工程项目便捷性设置查询
     * @return {string|null}
     */
    async selectFolder (args) {
        return await this.service.commonService.selectFolder(args);
    }


    /**
     * 判断项目是否有变更
     * @param arg
     * @return {Promise<string|null|*>}
     */
    async diffProject (arg) {
        return await this.service.commonService.diffProject(arg);
    }

    /**
     * 批量重刷
     * @param arg
     * @returns {Promise<void>}
     */
    async batchRefresh (arg) {
        let result = await this.service.commonService.batchRefresh(arg);
        return ResponseData.success(result);
    }


    async updaterData (arg) {
        this.service.dataUpdaterService.initNotificationTask();
    }



    async getWinIdBySequenceNbr(arg) {
        let {sequenceNbr} = arg
        let newVar = global.windowMap.get(sequenceNbr);
        let browserWindow = BrowserWindow.fromId(newVar);
        return ResponseData.success(browserWindow.webContents.id)

    }


    /**
     * 获取软件使用有效期
     */
    async getSoftwareIndate(args){
        let result = await this.service.commonService.getSoftwareIndate(args);
        return result;

    }

    /**
     * 获取软件使用有效期
     */
    async getSoftwareExpirationTime(){
        let result = await this.service.commonService.getSoftwareExpirationTime();
        return result;

    }



}

CommonController.toString = () => '[class CommonController]';
module.exports = CommonController;
