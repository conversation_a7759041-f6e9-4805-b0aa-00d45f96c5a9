<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-09-12 11:17:17
-->
<template>
  <!-- <fee-header class="head"></fee-header> -->
  <div class="table-content">
    <vxe-grid
      ref="qtxmTable"
      v-if="handlerColumns.length"
      v-bind="gridOptions"
      @edit-closed="editClosedEvent"
      @menu-click="contextMenuClickEvent"
      @cell-click="useCellClickEvent"
    >

      <!--名称-->
      <template #extraName_edit="{ row, rowIndex }">
        <vxe-input :clearable="false" :maxlength="2000"  v-model.trim="row.extraName"  type="text" @blur="clear()" ></vxe-input>
      </template>
      <!--计算基数-->
      <template #calculationBase_edit="{ row, rowIndex }">
        <vxeTableEditTable tableType="QTXM" @showTable="showTable" :filedValue="row.calculationBase" :placement="'bottom'"
            @update:filedValue=" newValue => { saveCustomInput(newValue, row, 'calculationBase'); } "/>
      </template>


      <template #jiesuanTotal_edit="{ row }">
        <span v-if="!row.isEdit">{{ row.amount }}</span>
        <vxe-input v-if="row.isEdit" :clearable="false" v-model.trim="row.amount"  type="text" :maxlength="10"
                   @blur=" row.amount = pureNumber(row.amount, 6); clear(); " ></vxe-input>
      </template>

      <!--计取安文费-->
      <template #isMarkSafe="{ row,rowIndex }">
      <!-- v-if=" row.extraName !== '材料暂估价' && row.extraName !== '设备暂估价' "-->
        <vxe-checkbox v-model="row.markSafa" :checked-value="1" :unchecked-value="0" name="awf" :disabled="originalFlag" @change="CheckChange(row,'markSafa')"
                      v-if=" row.type !== '材料暂估价' && row.type !== '设备暂估价' && row.type !== '专业工程暂估价' && rowIndex > 0 "></vxe-checkbox>
      </template>
      <!--jiesuanOriginal  === 1 为合同内-->
      <!--计入合价-->
      <template #putOntotalFlag="{ row,rowIndex }">
        <vxe-checkbox v-model="row.putOntotalFlag" name="putOntotalFlag"  v-if="rowIndex > 0" :disabled="Number(row.jiesuanOriginal) === 1" @change="CheckChange(row,'putOntotalFlag')"/>
      </template>
      <!--计取税金-->
      <template #isMarkSj="{ row, rowIndex }">
      <!--v-if=" row.extraName !== '材料暂估价' && row.extraName !== '设备暂估价'"-->
        <vxe-checkbox  v-model="row.markSj" :checked-value="1" :unchecked-value="0" name="sj" :disabled="originalFlag"
                       v-if=" row.type !== '材料暂估价' && row.type !== '设备暂估价' && row.type !== '专业工程暂估价' && rowIndex > 0"
                       @change="CheckChange(row,'markSj')"/>
      </template>

      <template #rate_edit="{ row }">
        <vxe-input :clearable="false" v-model.trim="row.rate" type="text" :maxlength="10"  @blur="(row.rate = pureNumber(row.rate, 2)), clear()" ></vxe-input>
      </template>
      <template #description_edit="{ row }">
        <vxe-input :clearable="false" :maxlength="2000" v-model.trim="row.description" type="text" @blur="clear()"></vxe-input>
      </template>
      <template #editType="{ row, index }">
        <vxe-select  v-model="row.type" transfer >
          <vxe-option
              v-for="(item, index) in typeList"
              :key="index"
              :value="item.value"
              :label="item.name"
          ></vxe-option>
        </vxe-select>
      </template>
    </vxe-grid>
  </div>
  <PageColumnSetting
    :columnOptions="handlerColumns"
    ref="columnSettingRef"
    title="页面显示列设置"
    @save="updateColumns"
    :getDefaultColumns="getDefaultColumns"
  />
</template>
<script setup>
// import FeeHeader from './FeeHeader.vue';
import {
  onMounted,
  onActivated,
  onUpdated,
  ref,
  watch,
  getCurrentInstance,
  nextTick,
  reactive
} from 'vue';
import { pureNumber } from '@/utils/index';

import { projectDetailStore } from '@/store/projectDetail';
import csProject from '@/api/csProject';
import jiesuanApi from '@/api/jiesuanApi';
import qtxmCommon from './qtxmCommon';
import { insetBus } from '@/hooks/insetBus';
import { message } from 'ant-design-vue';
import operateList from '@/views/projectDetail/customize/operate';
import { useCellClick } from '@/hooks/useCellClick';
import {useDecimalPoint} from "@/hooks/useDecimalPoint.js";
const { rateFormat } = useDecimalPoint();
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const projectStore = projectDetailStore();
let loading = ref(false);
let tableData = ref([]);
const activeKey = ref(1);
const originalFlag = ref(false);
let qtxmTable = ref();
let operateType = ref(0); // 操作类型
const typeList = reactive([
  {
    name: '普通费用',
    value: 'PTFY',
  },
  {
    name: '暂列金额',
    value: 'ZLJE',
  },
  {
    name: '暂估价',
    value: 'ZGJ',
  },
  {
    name: '材料暂估价',
    value: 'CLZGJ',
  },
  {
    name: '设备暂估价',
    value: 'SBZGJ',
  },
  {
    name: '专业工程暂估价',
    value: 'ZYGCZGJ',
  },
  {
    name: '总承包服务费',
    value: 'ZCBFWF',
  },
  {
    name: '计日工',
    value: 'JRG',
  },
  {
    name: '索赔与现场签证',
    value: 'SPYQZ',
  },
]);
const clear = () => {
  //清除编辑状态
  let $table = qtxmTable.value;
  $table.clearEdit();
};

import { useFormatTableColumns } from '@/hooks/useFormatTableColumns.js';
const {
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
  setHeaderCellClassName,
} = useFormatTableColumns({
  vxeGrid: qtxmTable,
  type: 'jieSuan',
  initCallback: () => {},
  initColumnsCallback: () => {
    initColumns({
      columns: createList(),
      pageName: 'qtxm',
    });
  },
});
const menuConfig = {
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 'insert',
          name: '插入',
        },
        {
          code: 'delete',
          name: '删除',
          className: 'redFont',
        },
        // {
        //   code: 'pageSetting',
        //   name: '页面显示列设置',
        //   visible: true,
        //   disabled: false,
        // },
      ],
    ],
  },
};
import { createListHooks } from './columns.js';
import {columnWidth} from "@/hooks/useSystemConfig.js";
import {orgTableColumns, tableColumns} from "@/jieSuan/views/projectDetail/customize/SummaryExpense/columns.js";
let { createList } = createListHooks('qtxmStatistics');
const defaultColumns = createList();

// const defaultColumns = [
//   {
//     field: 'dispNo',
//     width: 60,
//     title: '序号',
//     classType: 1,
//     initialize: true,
//   },
//   {
//     field: 'extraName',
//     width: 180,
//     title: '名称',
//     classType: 1,
//     initialize: true,
//   },
//   {
//     field: 'unit',
//     width: 100,
//     title: '单位',
//     classType: 1,
//     initialize: true,
//     originalFlag: false,
//   },
//   // {
//   //   field: 'jieSuanTotal',
//   //   width: 100,
//   //   title: '合同数量',
//   //   classType: 1,
//   //   initialize: true,
//   //   originalFlag: false,
//   // },
//   {
//     field: 'calculationBase',
//     width: 150,
//     title: '计算基数',
//     classType: 1,
//     initialize: true,
//   },
//   {
//     field: 'jieSuanAmount',
//     width: 100,
//     title: '合同金额',
//     classType: 1,
//     initialize: true,
//     originalFlag: false,
//   },
//   {
//     field: 'jieSuanJxTotal',
//     width: 180,
//     title: '合同进项合计',
//     classType: 1,
//     initialize: true,
//     originalFlag: false,
//   },
//   {
//     field: 'jieSuanCsTotal',
//     width: 180,
//     title: '合同除税合计',
//     classType: 1,
//     initialize: true,
//     originalFlag: false,
//   },
//   {
//     field: 'type',
//     width: 100,
//     title: '费用类别',
//     classType: 1,
//     initialize: true,
//     originalFlag: false,
//   },
//   {
//     field: 'isMarkSafe',
//     width: 100,
//     title: '计取安文费',
//     fixed: 'right',
//     slots: { default: 'isMarkSafe' },
//     classType: 1,
//     initialize: true,
//   },
//   {
//     field: 'putOntotalFlag',
//     width: 100,
//     title: '计入合价',
//     fixed: 'right',
//     slots: { default: 'putOntotalFlag' },
//     classType: 1,
//     initialize: true,
//   },
//   {
//     field: 'isMarkSj',
//     width: 100,
//     title: '计取税金',
//     fixed: 'right',
//     slots: { default: 'isMarkSj' },
//     classType: 1,
//     initialize: true,
//   },
//   // {
//   //   field: 'amount',
//   //   width: 180,
//   //   title: '结算数量',
//   //   editRender: { autofocus: '.vxe-input--inner' },
//   //   slots: { edit: 'jiesuanTotal_edit' },
//   //   classType: 1,
//   //   initialize: true,
//   // },
//   {
//     field: 'total',
//     width: 180,
//     title: '结算金额',
//     classType: 1,
//     initialize: true,
//   },
//   {
//     field: 'jxTotal',
//     width: 180,
//     title: '结算进项税额',
//     classType: 1,
//     initialize: true,
//   },
//   {
//     field: 'csTotal',
//     width: 180,
//     title: '结算除税合计',
//     classType: 1,
//     initialize: true,
//   }
// ];
let gridOptions = ref({
  align: 'center',
  keepSource: true,
  cellClassName: selectedClassName,
  loading: loading.value,
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isHover: true,
    isCurrent: true
  },
  data: [],
  height: 'auto',
  menuConfig: menuConfig,
  class: 'table-edit-common',
  editConfig: {
    trigger: 'click',
    mode: 'cell',
    beforeEditMethod({ rowIndex, row }) {
      if (rowIndex === 0 || ['CLZGJ', 'SBZGJ'].includes(row.type)) {
        //第一行不可编辑
        return false;
      }
      return true;
    },
  },
  columns: handlerColumns,
});

const contextMenuClickEvent = ({ menu, row }) => {
  console.log(menu, row);
  // if (menu.code === 'pageSetting') {
  //   showPageColumnSetting();
  // }
  qtxmTable.value.setCurrentRow(row);
  switch (menu.code) {
    case 'insert':
      // 插入
      operateType.value = 1;
      break;
    case 'delete':
      // 删除
      operateType.value = 2;
      break;
  }
  otherProjectLineDataColl();

};
// 页面列设置
let columnSettingRef = ref();
const showPageColumnSetting = () => {
  columnSettingRef.value.open();
};
// 定位方法
const posRow = sequenceNbr => {
  // 块料楼地面 bdCode: "011102003006"
  // let testId = '1725055299942461634'
  // currentInfo.value = { sequenceNbr };
  getOtherProjectList();
};
const limitNum = value => {
  if (typeof value !== 'string') return value;
  return value.replace(/[^(-?\d+)\.?(\d*)$]/g, '');
};
const editClosedEvent = ({ row, column }) => {
  const $table = qtxmTable.value;
  const field = column.field;
  let value = row[field];
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  if (field === 'amount' && row.amount && row.amount.length > 20) {
    row.amount = value.slice(0, 20);
  }
  if (field === 'unit' && row.unit && row.unit.length > 2000) {
    row.unit = value.slice(0, 2000);
  }
  if (field === 'amount' && row.amount && row.amount.length > 20) {
    row.amount = value.slice(0, 20);
  }
  if (field === 'calculationBase' && row.calCopy === row.calculationBase) {
    return;
  }
  if ((field === 'rate' && Number(value) > 1000) || Number(value) < 0) {
    message.warn(`费率可输入数值范围：0-1000`);
    $table.revertData(row, field);
    return;
  }
  if (field === 'rate' && row.rate != '') {
    row.rate = limitNum(row.rate); //数字
    row.rate = rateFormat(row.rate);
  }
  upDate(row, column.field);
};

const otherProjectLineDataColl = () => {
  let isCurrentRow = qtxmTable.value.getCurrentRecord();
  let apiData = {
    operateType: operateType.value,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeInfo?.parentId, //单项ID
    unitId: projectStore.currentTreeInfo?.id, //单位ID
    targetSequenceNbr: isCurrentRow?.sequenceNbr,
  };
  console.log('apiData', apiData);
  csProject.otherProjectLineDataColl(apiData).then(res => {
    console.log('接口结果', res);
    if (res.status === 200 && res.result) {
      getOtherProjectList();
    }
  });
};

const upDate = (row,field) => {
  let otherProject = {};
  otherProject[field] = row[field];
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeInfo?.parentId, //单项ID
    unitId: projectStore.currentTreeInfo?.id, //单位ID
    sequenceNbr: row.sequenceNbr,
    otherProject: otherProject,
  };
  console.log(apiData);
  csProject.updateOtherProject(apiData).then(res => {
    if (res.status === 200) {
      let costData = {
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeInfo?.parentId, //单项ID
        unitId: projectStore.currentTreeInfo?.id, //单位ID
      };
      // jiesuanApi.countCostCodePrice(costData);
      getOtherProjectList();
    }
  });
};
const getOtherProjectList = () => {
  originalFlag.value = projectStore.currentTreeInfo.originalFlag;
  loading.value = true;
  let insertData = operateList.value.find(item => item.name === 'insert');
  let deleteData = operateList.value.find(item => item.name === 'delete');
  insertData.disabled = true;
  deleteData.disabled = true;
  let apiData = qtxmCommon.requestParams();
  // let apiFun = csProject.getOtherProjectListJieSuan;
  csProject.getOtherProjectListJieSuan(apiData).then(res => {
    console.log(apiData, res);
    if (res.status === 200) {
      loading.value = false;
      res.result &&
      res.result.map(item => {
          item.isMarkSafe = item.markSafa === 1 ? true : false;
          item.isMarkSj = item.markSj === 1 ? true : false;

          item.calCopy = item.calculationBase;
          item.originalAmount = item.amount;
          if (
            item.type?.trim() === '材料暂估价' ||
            item.type?.trim() === '设备暂估价'
          ) {
            item.isEdit = false;
          } else {
            item.isEdit = true;
          }
        });
      tableData.value = res.result;
      gridOptions.value.data = res.result;

      console.log('********getOtherProjectList', gridOptions.value);
    }
  });
};

watch(
    () => projectStore.asideMenuCurrentInfo,
    () => {
      console.log('*****************其他项目');
      if (projectStore.asideMenuCurrentInfo?.sequenceNbr === 'qtxm00') {
        getOtherProjectList();
      }
    }
);

watch(
  ()=>projectStore.currentTreeInfo,
  ()=>{
    qtxmTable.value && qtxmTable.value.loadColumn(createList());
    setMenuConfig();
  }, {
  deep: true,
})

watch(
  () => projectStore.tabSelectName,
  async () => {
    // console.log('*****************其他项目');
    if (projectStore.tabSelectName === '其他项目') {
      let filterColumns = JSON.parse(JSON.stringify(createList()));
      await initColumns({
        columns: filterColumns.filter(item => {
          // if (!projectStore.currentTreeInfo.originalFlag) {
          //   return item.originalFlag !== false;
          // }
          return true;
        }),
        pageName: 'qtxm',
      });

      getOtherProjectList();
    }
  }
);
onMounted(async () => {
  let filterColumns = JSON.parse(JSON.stringify(createList()));
  await initColumns({
    columns: filterColumns.filter(item => {
      // if (!projectStore.currentTreeInfo.originalFlag) {
      //   return item.originalFlag !== false;
      // }
      return true;
    }),
    pageName: 'qtxm',
  });
  setMenuConfig();
  getOtherProjectList();
});

// 合同外展示右键插入和删除能力
const setMenuConfig = ()=>{
  if(projectStore.currentTreeInfo.originalFlag) {
    gridOptions.value.menuConfig.body.options[0].map(a => {
      a.visible = false;
    });
  }else{
    gridOptions.value.menuConfig.body.options[0].map(a => {
      a.visible = true;
    });
  }
}
const changeEdit = val => {
  // console.log('val', val);
};
// const checkChange = row => {
//   console.log('复选框CheckChange', row);
//   // row.markSafa = row.isMarkSafe ? 1 : 0;
//   // row.markSj = row.isMarkSj ? 1 : 0;
//   // row.markSafa = row.markSafa ? 1 : 0;
//   // row.markSj = row.markSj ? 1 : 0;
//   // row.putOntotalFlag = row.putOntotalFlag ? 1 : 0;//计价合算
//   if (column !== 'putOntotalFlag') {
//     row[column] = row[column] ? 1 : 0;
//   }
//   upDate(row);
// };
const CheckChange = (row,column) => {
  console.log('复选框CheckChange', row);
  if (column !== 'putOntotalFlag') {
    row[column] = row[column] ? 1 : 0;
  }
  upDate(row, column);
};
const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  // console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
};
onActivated(() => {
  insetBus(bus, projectStore.componentId, 'qtxmStatistics', async data => {
    if (data.name === 'insert') operateType.value = 1;
    otherProjectLineDataColl();
    // if (data.name === 'delete') operateType.value = 2;
    // otherProjectLineDataColl();
  });
});

defineExpose({
  getTableData: getOtherProjectList,
  posRow,
});
</script>
<style lang="scss" scoped>
// @import './otherProject.scss';
.table-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
