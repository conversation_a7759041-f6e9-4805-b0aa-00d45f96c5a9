<!--
 * @Descripttion: 导出。ysf文件
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: renmingming
 * @LastEditTime: 2024-07-18 16:45:24
-->

<template>
  <common-modal
    className="dialog-comm tree-dialog"
    width="auto"
    @close="cancel"
    v-model:modelValue="dialogVisible"
    title="合同外工程归属设置"
  >
    <div class="tree-content-wrap">
      <div class="dialog-content">
        <div class="list" v-if="treeData">
          <!--  @check="selectChildren" -->
          <a-directory-tree
            :defaultExpandAll="true"
            v-model:selectedKeys="checkedKeys"
            multiple
            @select="selectChildren"
            :fieldNames="{
              children: 'children',
              title: 'name',
              key: 'sequenceNbr',
              selectable: 'isSelectable',
            }"
            :tree-data="treeData"
          >
          </a-directory-tree>
        </div>
      </div>
      <div class="footer-btn-list" style="margin-top: 10px">
        <a-button @click="cancel">取消</a-button>
        <a-button
          type="primary"
          :disabled="!checkedKeys || !checkedKeys.length"
          @click="handleOk"
          :loading="submitLoading"
          >确定</a-button
        >
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import { message } from 'ant-design-vue';
import { ref, getCurrentInstance } from 'vue';
import { useRoute } from 'vue-router';
import jiesuanApi from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail.js';
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;

const emits = defineEmits(['closeDialog']);
const route = useRoute();
const treeData = ref(null);
const submitLoading = ref(false);
const dialogVisible = ref(false);
const checkedKeys = ref([]);
const parentProjectName = ref('');
const singleId = ref('');
const dataStatus = ref(null);
const projectStore = projectDetailStore();
const props = defineProps({
  belongData: {
    type: Object,
    default: () => ({}),
  },
});
const getTreeList = async () => {
  let list = null;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: props.belongData.parentId,
    unitId: props.belongData.id,
  };
  const res = await jiesuanApi.queryParentHierarchyFb(apiData);
  console.log('合同外工程归属树形结构数据', res);
  if (res.status === 200) {
    list = res.result;
    // checkedKeys.value=[list[0].children[0].sequenceNbr]
  } else {
    message.error(res.message);
    return;
  }
  handleTreeData(list);
};
// 遍历树结构
const handleTreeData = data => {
  data.forEach(item => {
    // 如果为单项工程则不允许选中
    if (item.levelType === 2) {
      item.selectable = false;
    } else if (item.levelType === 3) {
      if (item.isSelect) {
        checkedKeys.value.push(item.sequenceNbr);
      }
    }
    if (item.children && item.children.length > 0) {
      handleTreeData(item.children);
    }
  });
  treeData.value = data;
};
// 点击树节点
const selectChildren = (selectedKeys, e) => {
  parentProjectName.value = e.node.parentProjectName;
  singleId.value = getParentId(treeData.value, e.node.sequenceNbr);
};
// 递归获取父节点id
const getParentId = (list, id) => {
  for (let i in list) {
    if (list[i].id == id) {
      return [list[i]];
    }
    if (list[i].children) {
      let node = getParentId(list[i].children, id);
      if (node !== undefined) {
        return node.concat(list[i]);
      }
    }
  }
};
const cancel = () => {
  dialogVisible.value = false;
  checkedKeys.value = [];
  dataStatus.value = null;
  emits('closeDialog');
};
const handleOk = async () => {
  if (!checkedKeys.value || !checkedKeys.value.length) {
    message.error('请选择要导出的工程');
    return;
  }
  submitLoading.value = true;
  try {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: props.belongData.parentId,
      unitId: props.belongData.id,
      parentProjectId: checkedKeys.value[0],
      parentProjectName: parentProjectName.value,
    };
    const res = await jiesuanApi.updateParentProject(apiData);
    if (res.status == 200) {
      message.success(res.message);
      refreshTableList();
      // bus.emit('projectBelongChange');
      cancel();
    }
  } catch (error) {
    console.error(error);
  } finally {
    submitLoading.value = false;
  }
};
const refreshTableList = () => {
  // 处理偶现不刷界面的问题
  setTimeout(()=>{
    if (projectStore.componentId === 'subItemProject') {
      projectStore.subItemProjectAutoPosition?.queryBranchDataById();
    }
    if (projectStore.componentId === 'measuresItem') {
      projectStore.measuresItemProjectAutoPosition?.queryBranchDataById();
    }
  });
};
const open = () => {
  getTreeList();
  dialogVisible.value = true;
};
open();
</script>

<style lang="scss" scoped>
.tree-content-wrap {
  width: 60vw;
  max-width: 800px;
  height: 60vh;
  display: flex;
  flex-direction: column;
}

.dialog-content {
  background: rgba(250, 250, 250, 0.39);
  border: 1px solid #e1e1e1;
  display: flex;
  flex-direction: column;
  border-radius: 2px;
  flex: 1;
  overflow: hidden;

  &:hover {
    overflow: auto;
  }

  .title {
    padding: 7px 13px;
    background-color: #eaeaea;
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    display: flex;
    justify-content: space-between;
  }

  .list {
    flex: 1;
    padding: 16px 18px;
    background-color: #fafafa;
    overflow: hidden;

    &:hover {
      overflow: auto;
    }

    ::v-deep .ant-tree {
      background-color: #fafafa;

      .ant-tree-switcher-noop {
        opacity: 0;
      }

      .ant-tree-switcher {
        background-color: transparent;
      }
    }
  }
}
</style>
