<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-08-05 17:35:12
-->

<template>
  <div>
    <common-modal
      className="dialog-comm noMask"
      title="费用代码明细"
      width="900"
      height="600"
      v-model:modelValue="isFeiyong"
      @cancel="cancel"
      @close="isFeiyong = false"
      :mask="false"
      :show-zoom="true"
      :destroy-on-close="true"
    >
      <content-down></content-down>
    </common-modal>
    <common-modal
      className="dialog-comm noMask"
      title="安全生产、文明施工费明细"
      width="700"
      v-model:modelValue="isAWF"
      @cancel="cancel"
      @close="isAWF = false"
      :mask="false"
    >
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        :data="awfData"
        height="400"
      >
        <vxe-column
          field="sortNo"
          min-width="80"
          title="序号"
        > </vxe-column>
        <vxe-column
          field="costMajorName"
          min-width="100"
          title="取费专业"
        >
        </vxe-column>
        <vxe-column
          field="costFeeBase"
          min-width="100"
          title="取费基数（元）"
        >
        </vxe-column>
        <vxe-column
          field="basicRate"
          min-width="100"
          title="基本费率（%）"
        >
        </vxe-column>
        <vxe-column
          field="addRate"
          min-width="100"
          title="增加费率（%）"
        >
        </vxe-column>
        <vxe-column
          field="feeAmount"
          min-width="100"
          title="费用金额（元）"
        >
        </vxe-column>
        <template #empty>
          <span style="
              color: #898989;
              font-size: 14px;
              display: block;
              margin: 25px 0;
            ">
            <icon-font
              style="font-size: 22px"
              type="icon-zanwushuju"
            ></icon-font>
            暂无数据
          </span>
        </template>
      </vxe-table>
    </common-modal>
    <common-modal
      className="dialog-comm noMask"
      title="规费明细"
      width="700"
      v-model:modelValue="isGF"
      @cancel="cancel"
      @close="isGF = false"
      :mask="false"
    >
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        :data="gfData"
        height="400"
      >
        <vxe-column
          field="sortNo"
          min-width="80"
          title="序号"
        > </vxe-column>
        <vxe-column
          field="costMajorName"
          min-width="100"
          title="取费专业"
        >
        </vxe-column>
        <vxe-column
          field="costFeeBase"
          min-width="100"
          title="取费基数（元）"
        >
        </vxe-column>
        <vxe-column
          field="gfeeRate"
          min-width="100"
          title="规费费率（%）"
        >
        </vxe-column>
        <vxe-column
          field="feeAmount"
          min-width="100"
          title="费用金额（元）"
        >
        </vxe-column>

        <template #empty>
          <span style="
              color: #898989;
              font-size: 14px;
              display: block;
              margin: 25px 0;
            ">
            <icon-font
              style="font-size: 22px"
              type="icon-zanwushuju"
            ></icon-font>
            暂无数据
          </span>
        </template>
      </vxe-table>
    </common-modal>
    <common-modal
      className="dialog-comm noMask"
      title="价差安、文费明细"
      width="900"
      v-model:modelValue="isJcAWF"
      @cancel="cancel"
      @close="isJcAWF = false"
      :mask="false"
    >
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        :data="jcAwfData"
        height="400"
      >
        <vxe-column
          field="sortNo"
          min-width="80"
          title="序号"
        > </vxe-column>
        <vxe-column
          field="costMajorName"
          min-width="100"
          title="取费专业"
        >
        </vxe-column>
        <vxe-column
          field="costFeeBase"
          min-width="100"
          title="取费基数"
        >
        </vxe-column>
        <vxe-column
          field="basicRate"
          min-width="100"
          title="基本费率（%）"
        >
        </vxe-column>
        <vxe-column
          field="addRate"
          min-width="100"
          title="增加费率"
        >
        </vxe-column>
        <vxe-column
          field="priceDifferencFeeAmount"
          min-width="150"
          title="价差安全文明施工费"
        >
        </vxe-column>
        <template #empty>
          <span style="
              color: #898989;
              font-size: 14px;
              display: block;
              margin: 25px 0;
            ">
            <icon-font
              style="font-size: 22px"
              type="icon-zanwushuju"
            ></icon-font>
            暂无数据
          </span>
        </template>
      </vxe-table>
    </common-modal>
    <common-modal
      className="dialog-comm noMask"
      title="价差规费明细"
      width="900"
      v-model:modelValue="isJcGF"
      @cancel="cancel"
      @close="isJcGF = false"
      :mask="false"
    >
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        :data="jcGfData"
        height="400"
      >
        <vxe-column
          field="sortNo"
          min-width="80"
          title="序号"
        > </vxe-column>
        <vxe-column
          field="costMajorName"
          min-width="100"
          title="取费专业名称"
        >
        </vxe-column>
        <vxe-column
          field="costFeeBase"
          min-width="100"
          title="取费基数"
        >
        </vxe-column>
        <vxe-column
          field="gfeeRate"
          min-width="100"
          title="规费费率"
        >
        </vxe-column>
        <vxe-column
          field="priceDifferencFeeAmount"
          min-width="150"
          title="价差规费金额（元）"
        >
        </vxe-column>
        <template #empty>
          <span style="
              color: #898989;
              font-size: 14px;
              display: block;
              margin: 25px 0;
            ">
            <icon-font
              style="font-size: 22px"
              type="icon-zanwushuju"
            ></icon-font>
            暂无数据
          </span>
        </template>
      </vxe-table>
    </common-modal>
    <!-- <self-check v-model:checkVisible="checkVisible"></self-check> -->
    <utility-bills v-model:billVisible="billVisible"></utility-bills>
    <!-- 批量替换 -->
    <batch-replace-fee
      ref="replaceFeeRef"
      @refresh="setAggreScopeOk"
    ></batch-replace-fee>
  </div>
</template>
<script setup>
import { ref, onMounted, onActivated, getCurrentInstance } from 'vue';
import ContentDown from '@/views/projectDetail/customize/summaryExpense/ContentDown.vue';
import { insetBus } from '@/hooks/insetBus';
import feePro from '@/api/feePro';
import BatchReplaceFee from '@/views/projectDetail/customize/SummaryExpense//BatchReplaceFee.vue';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import jiesuanApi from '@/api/jiesuanApi';
// import SelfCheck from '@/views/projectDetail/customize/summaryExpense/selfCheck.vue';
import UtilityBills from '@/views/projectDetail/customize/SummaryExpense/utilityBills.vue';
const store = projectDetailStore();
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
let isFeiyong = ref(false);
let isAWF = ref(false);
let isGF = ref(false);
let isJcAWF = ref(false);
let isJcGF = ref(false);
const emit = defineEmits(['clickAdd']);
let awfData = ref([]);
let gfData = ref([]);
let jcAwfData = ref([]);
let jcGfData = ref([]);
let checkVisible = ref(false); // 项目自检弹框是否展示
let billVisible = ref(false); // 记取水电费弹框是否展示

// 获取安文费列表
const getAWFdate = () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeInfo?.parentId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  feePro.getSafeFee(apiData).then(res => {
    console.log('getAWFdate', res);
    if (res.status === 200) {
      res.result &&
        res.result.map((item, index) => {
          item.sortNo = index + 1;
          item.basicRate = removeExtraZerosAndDot(item?.basicRate);
        });
      awfData.value = res.result;
      console.log('getAWFdate-awfData.value', awfData.value);
    } else {
      awfData.value = [];
    }
  });
};
const removeExtraZerosAndDot = numStr => {
  return numStr.toString().replace(/(\.0*|0+)$/, '');
};
// 获取规费列表
const getGFdate = () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeInfo?.parentId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  feePro.getGfeeFee(apiData).then(res => {
    console.log('getGFdate', res);
    if (res.status === 200) {
      res.result && res.result.map((item, index) => (item.sortNo = index + 1));
      gfData.value = res.result;
    } else {
      gfData.value = [];
    }
  });
};
// 获取价差安文费列表
const getJcAWFdate = () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeInfo?.parentId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  jiesuanApi.getJcSafeFee(apiData).then(res => {
    console.log('价差安文费', res);
    if (res.status === 200) {
      res.result && res.result.map((item, index) => (item.sortNo = index + 1));
      jcAwfData.value = res.result;
    } else {
      jcAwfData.value = [];
    }
  });
};
// 获取价差规费列表
const getJcGFdate = () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeInfo?.parentId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  jiesuanApi.getJcGfeeFee(apiData).then(res => {
    console.log('价差规费', res);
    if (res.status === 200) {
      res.result && res.result.map((item, index) => (item.sortNo = index + 1));
      jcGfData.value = res.result;
    } else {
      jcGfData.value = [];
    }
  });
};
onMounted(() => {
  initBus();
});
onActivated(() => {
  initBus();
});
const initBus = () => {
  insetBus(bus, store.componentId, 'summaryExpense', async data => {
    if (data.name === 'insert') emit('clickAdd', true);
    if (data.name === 'charge-code') isFeiyong.value = true;
    if (data.name === 'anwen-fee') getAWFdate(), (isAWF.value = true);
    if (data.name === 'fees') getGFdate(), (isGF.value = true);
    if (data.name === 'jcAnwenFee') getJcAWFdate(), (isJcAWF.value = true);
    if (data.name === 'jcFees') getJcGFdate(), (isJcGF.value = true);
    if (data.name === 'selfCheck') getGFdate(), (checkVisible.value = true);
    if (data.name === 'utility-bills') getGFdate(), (billVisible.value = true);
    //保存模板
    if (data.name === 'save-on-mould') saveTempOperate();
    if (data.name === 'batch-replace-fee') {
      setReplaceFeeFun();
    }
  });
};
let replaceFeeRef = ref();
const setReplaceFeeFun = () => {
  replaceFeeRef.value.open(true);
};
const saveTempOperate = () => {
  //保存模板接口
  const { taxMode, deStandardReleaseYear } = store.constructConfigInfo;
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    taxMode,
    libraryCodeVersion: deStandardReleaseYear,
  };
  console.log('费用汇总保存模板呢', apiData);
  feePro.saveTemplateFYHZ(apiData).then(res => {
    console.log('保存模板返回呢', res, apiData);
    if (res.status === 200 && res.result) {
      res.result.indexOf('取消') !== -1
        ? message.info(res.result)
        : message.success(res.result);
    } else {
      message.success(res.message ? res.message : '保存模板失败');
    }
  });
};
</script>
<style lang="scss" scoped>
.head {
  span img {
    margin-right: -10px;
    margin-bottom: 3px;
  }
}
.dialog-comm {
  @extend .dialog-comm;
  z-index: 2222;
}
</style>
