<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:00:41
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-06-14 18:44:00
-->
<template>
  <div class="table-content">
    <vxe-table
      align="center"
      :loading="loading"
      :column-config="{ resizable: true }"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      :data="tableData"
      height="auto"
      ref="upTable"
      border="full"
      keep-source
      @edit-closed="editClosedEvent"
      @current-change="currentChangeEvent"
      :menu-config="menuConfig"
      @menu-click="contextMenuClickEvent"
      :cell-class-name="cellClassName"
      :header-cell-class-name="headerCellClassName"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
      }"
      class="table-edit-common"
      @cell-click="useCellClickEvent"
      @keydown="keyDownHandler"
    >
      <!-- <vxe-column field="index" min-width="60" title=""> </vxe-column> -->
      <!-- <vxe-column fixed="left" width="50" align="left">
        <template #default="{ row }">
          <div class="multiple-select">
            {{ row.index }}
          </div>
        </template>
      </vxe-column> -->
      <vxe-column
        field="dispNo"
        :width="columnWidth(60)"
        title="序号"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.dispNo"
            type="text"
            @blur="clear()"
            @keyup="row.dispNo = row.dispNo.replace(/[^\w.]/g, '')"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="code"
        :width="columnWidth(120)"
        title="费用代号"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.code"
            type="text"
            @blur="clear()"
            @keyup="validateAndFormatCode(row)"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="name"
        :min-width="columnWidth(220)"
        title="名称"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row }">
          <span>{{ row.name }}</span>
        </template>
        <template #edit="{ row }">
          <cell-textarea
            v-if="row.whetherTax !== 1"
            :clearable="false"
            v-model.trim="row.name"
            @blur="clear()"
            placeholder="请输入名称"
            :textHeight="row.height"
            @keyup="row.name = row.name.replace(/\-|\+|\*|\/|\.|\(|\)/g, '')"
          ></cell-textarea>
          <span v-else>{{ row.name }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="calculateFormula"
        :min-width="columnWidth(150)"
        title="计算基数"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row }">
          {{ row.calculateFormula }}
        </template>
        <template #edit="{ row }">
          <cell-textarea
            :clearable="false"
            v-model.trim="row.calculateFormula"
            placeholder="请输入计算基数"
            :textHeight="row.height"
            @blur="clear()"
            @keyup="
              row.calculateFormula = row.calculateFormula.replace(
                /[^0-9a-zA-Z|\_|\-|\*|\+|\/|\.|(|)]/g,
                '',
              )
            "
          ></cell-textarea>
          <icon-font
            type="icon-bianji"
            class="more-icon"
            @click="editCalc(row)"
          ></icon-font>
        </template>
      </vxe-column>
      <vxe-column
        field="instructions"
        :min-width="columnWidth(200)"
        title="基数说明"
      >
      </vxe-column>
      <vxe-column
        field="rate"
        :width="columnWidth(120)"
        title="费率（%）"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model="row.rate"
            @blur="clear()"
            @keyup="row.rate = limitNum(row.rate)"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="type"
        :width="columnWidth(150)"
        title="费率类别"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-select v-model="row.type" :transfer="true" :clearable="true">
            <vxe-option
              v-for="item of typeList"
              :key="item.code"
              :value="item"
              :label="item"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column field="price" :width="columnWidth(120)" title="金额">
      </vxe-column>
      <!-- <vxe-column field="type" width="180" title="费用类型"> </vxe-column> -->
      <vxe-column
        field="remark"
        title="备注"
        :min-width="columnWidth(100)"
        :edit-render="{ autofocus: '.vxe-textarea--inner' }"
      >
        <template #edit="{ row }">
          <cell-textarea
            :clearable="false"
            v-model.trim="row.remark"
            @blur="clear()"
            placeholder="请输入备注"
            :textHeight="row.height"
          ></cell-textarea>
        </template>
      </vxe-column>
      <vxe-column
        field="whetherPrint"
        title="打印"
        :width="columnWidth(80)"
        :cell-render="{}"
      >
        <template #default="{ row }">
          <vxe-checkbox
            v-model="row.whetherPrint"
            size="small"
            content=""
            :checked-value="1"
            :unchecked-value="0"
            @change="update(row, 'whetherPrint')"
          ></vxe-checkbox>
          <!-- <vxe-checkbox
            v-model="row.print"
            name="print"
            @change="CheckboxChange(row, 'print')"
          ></vxe-checkbox> -->
        </template>
      </vxe-column>
    </vxe-table>
  </div>
  <common-modal
    className="dialog-comm noMask"
    title="计算基数编辑"
    width="1200"
    height="450"
    v-model:modelValue="comModel"
    @cancel="cancel"
    @close="comModel = false"
    :mask="false"
  >
    <content-down
      :isTextArea="isTextArea"
      :textValue="textValue"
      ref="comArea"
    ></content-down>
    <span class="btns">
      <a-button @click="cancelData()">取消</a-button>
      <a-button type="primary" @click="sureData()">确定</a-button>
    </span>
  </common-modal>
</template>

<script setup>
import {
  ref,
  onMounted,
  watch,
  reactive,
  onActivated,
  getCurrentInstance,
} from 'vue';
import feePro from '@/api/feePro';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import { add } from 'xe-utils';
import { getUrl } from '@/utils/index';
import ContentDown from './ContentDown.vue';
import { columnWidth } from '@/hooks/useSystemConfig';
import { Modal } from 'ant-design-vue';
import infoMode from '../../../../plugins/infoMode';
import { useCellClick } from '@/hooks/useCellClick';
// import { pureNumber } from '@/utils/index';
import { useDecimalPoint } from '@/hooks/useDecimalPoint';
const { rateFormat } = useDecimalPoint();

const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const store = projectDetailStore();
const upTable = ref();
let tableData = ref([]);
let totalFee = ref([]);
let taxMode = ref(); //1-一般计税，2-简易计税
let isCurrent = ref(0);
let comModel = ref(false); //计算基数编写弹框
let isTextArea = ref(true);
let textValue = ref('');
let rowValue = ref('');
let deleteInfo = ref();
let loading = ref(false);
let oldValue = ref('');
const comArea = ref();
let selectData = ref(null); //批量选择的数据
const emits = defineEmits(['getMoveInfo']);
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
onMounted(() => {
  getTableData();
  //getTotalFeeCode();
  setUse();
});
const setUse = () => {
  window.addEventListener('keydown', keyDownOperate);
  bus.off('moveDeData');
  bus.on('moveDeData', ({ state, type }) => {
    moveDeData({ state, type });
  });
};
onActivated(() => {
  setUse();
});
const currentChangeEvent = ({ $rowIndex }) => {
  isCurrent.value = $rowIndex;
  let obj = {
    isCurrent: isCurrent.value,
    isLast: tableData.value.length - 1 === isCurrent.value,
  };
  emits('getMoveInfo', obj);
};

const keyDownOperate = event => {
  if (!upTable.value || store.tabSelectName !== '费用汇总') return;
  if (event.ctrlKey && event.code == 'KeyC') {
    copyItem(upTable.value.getCurrentRecord());
  }
  if (event.ctrlKey && event.code == 'KeyV') {
    getCurrentIndex(upTable.value.getCurrentRecord());
    pasteItem();
  }
  if (event.ctrlKey && event.code == 'KeyD') {
    deleteItem(upTable.value.getCurrentRecord());
  }
};
const props = defineProps({
  isCharu: {
    type: Boolean,
  },
});
watch(
  () => props.isCharu,
  () => {
    operate('insert', {});
  },
);

// 费用汇总上移下移
const moveDeData = ({ state, type }) => {
  if (store.tabSelectName !== '费用汇总') return;
  console.log(state, type);
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
    index: isCurrent.value,
    move: state === 1 ? 'up' : 'down',
  };
  console.log('apiData', apiData);
  if (type === 'move') {
    feePro.moveUpDown(apiData).then(res => {
      console.log('res移动', res);
      if (res.status === 200) {
        isCurrent.value =
          state === 1 ? isCurrent.value - 1 : isCurrent.value + 1;
        message.success('移动成功');
        getTableData();
      }
    });
  }
};

const keyDownHandler = event => {
  let code = event.$event.code;
  if (code == 'Delete') {
    deleteItem(event.$table.getCurrentRecord());
  }
};

const editCalc = row => {
  comModel.value = true;
  textValue.value = row;
  oldValue.value = row.calculateFormula;
};
const cancelData = () => {
  comArea.value.value = oldValue.value;
  textValue.value.calculateFormula = oldValue.value;
  comModel.value = false;
};

const validateAndFormatCode = row => {
  const code = row.code.trim();
  const codePattern = /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5]*$/;

  if (!codePattern.test(code)) {
    row.code = code.slice(0, -1); // 移除最后一个字符，因为它是非法字符
  }
};
const searchStrEach = (str, target) => {
  let sum = 0;
  for (let key of str) {
    if (key == target) {
      sum++;
    }
  }
  return sum;
};
const limitStr = value => {
  const FHlist = ['+', '-', '*', '/'];
  let num = 0;
  for (let key in FHlist) {
    num += searchStrEach(value, FHlist[key]);
  }
  let arr = value.split(/-|\+|\*|\/|\(|\)/);
  let effectiveValue = 0;
  arr.map(item => {
    if (item.trim() !== '') effectiveValue++;
  });
  //有效数字应该=符号的数量+1
  if (effectiveValue - 1 !== num) return false;
  return true;
};
const sureData = () => {
  //编辑计算基数弹框确认
  const value = comArea.value.value;
  console.log(
    comArea.value.value,
    'comArea.value.value',
    textValue.value.calculateFormula,
  );
  if (!value) {
    //不可以输入空
    message.warn(`输入不可为空`);
    return;
  }
  if (!limitStr(value)) {
    message.warn(`输入格式不正确,请重新输入`);
    return;
  }
  textValue.value.calculateFormula = value;
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    unitCostSummary: { ...textValue.value },
  };
  console.log('修改费用汇总数据', apiData);
  feePro.saveCostSummary(apiData).then(res => {
    console.log('修改费用返回结果', res);
    if (res.status === 500) {
      message.error(res.message);
      comArea.value.value = oldValue.value;
      textValue.value.calculateFormula = oldValue.value;
      return;
    } else if (res.status !== 500) {
      comModel.value = false;
      message.success('修改成功');
      tableData.value.map((item, index) =>
        textValue.value.sequenceNbr === item.sequenceNbr
          ? (isCurrent.value = index)
          : '',
      );
      getTableData();
    }
  });
};

//获取当前计税方式
const getTaxMethods = () => {
  let apiData = {
    levelType: store.currentTreeInfo.levelType,
    constructId: store.currentTreeGroupInfo?.constructId,
  };
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  feePro.getTaxCalculation(apiData).then(res => {
    if (res.status === 200) {
      taxMode.value = res.result && res.result.taxCalculationMethod;
      console.log('计税方式', taxMode.value);
    }
  });
};
const getTotalFeeCode = () => {
  getTaxMethods();
  const formdata = {
    type: '',
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  let apiName = 'costCodePrice'
  if(store.type === 'jieSuan') {
    apiName = 'costCodePriceJieSuan';
  }
  feePro[apiName](formdata).then(res => {
    if (res.status === 200) {
      res.result && res.result.map(item => totalFee.value.push(item.code));
    }
  });
};
const getTableData = () => {
  loading.value = true;

  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  totalFee.value = [];
  console.log('获取费用汇总数据', apiData);
  feePro.getUnitCostSummary(apiData).then(res => {
    console.log('获取费用汇总数据返回结果', res);
    if (res.status === 200) {
      loading.value = false;

      res.result &&
        res.result.map((item, index) => {
          totalFee.value.push(item.code);
          if (
            item.type === '附加税费' ||
            item.type === '销项税额' ||
            item.type === '税金'
          ) {
            item.whetherTax = 1;
          } else {
            item.whetherTax = 0;
          }
        });
      tableData.value = res.result ? res.result : [];
      tableData.value.map(item => (item._X_ROW_KEY = item.sequenceNbr));
      isCurrent.value
        ? upTable.value.setCurrentRow(tableData.value[isCurrent.value])
        : upTable.value.setCurrentRow(tableData.value[0]);
      let obj = {
        isCurrent: isCurrent.value,
        isLast: tableData.value.length - 1 === isCurrent.value,
      };
      console.log('obj', obj);
      emits('getMoveInfo', obj);
    }
  });

  getTotalFeeCode();
};
watch(
  () => [store.tabSelectName, store.currentTreeInfo],
  ([val, oldVal], [newY, oldy]) => {
    if (
      store.tabSelectName === '费用汇总' &&
      store.currentTreeInfo.levelType === 3
    ) {
      isCurrent.value = 0; //切换页面选中行默认选第一行
      getTableData();
    }
  },
);
const clear = () => {
  //清除编辑状态
  const $table = upTable.value;
  $table.clearEdit();
};
const limitNum = value => {
  if (typeof value !== 'string') return value;
  return value.replace(/[^(-?\d+)\.?(\d*)$]/g, '');
};
const editClosedEvent = ({ row, column }) => {
  console.log('费用汇总修改', row);
  const $table = upTable.value;
  const field = column.field;
  let value = row[field];
  const reg = /[^\d\.]/g;
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  if (!['remark', 'rate', 'type'].includes(field) && !row[field]) {
    //不可以输入空
    $table.revertData(row, field);
    message.warn(`输入不可为空`);
    return;
  }

  // 判断单元格值是否被修改
  if ((field === 'name' || field === 'remark') && value?.length > 50) {
    console.log('field', field, 'value', value);
    row[field] = value.slice(0, 50);
    message.warn(`输入字符应50个字符范围内`);
  }
  if (field === 'name' && value?.length === 0) {
    $table.revertData(row, field);
    message.warn(`输入名称不可为空!`);
    return;
  }
  if (field === 'calculateFormula' && value?.length === 0) {
    message.warn(`计算基数不可为空`);
    $table.revertData(row, field);
    return;
  }
  if (field === 'calculateFormula' && !limitStr(value)) {
    message.warn(`输入格式不正确,请重新输入`);
    $table.revertData(row, field);
    return;
  }
  if (field === 'rate' && value !== '' && reg.test(value)) {
    //不可以输入除数字和小数点之外的
    $table.revertData(row, field);
    return;
  }
  if (field === 'rate' && value === '') {
    row[field] = '';
  } else if ((field === 'rate' && Number(value) > 1000) || Number(value) < 0) {
    message.warn(`费率可输入数值范围：0-1000`);
    $table.revertData(row, field);
    return;
  }
  if (['rate'].includes(field) && row.rate !== '') {
    //金额输入限制
    row.rate = rateFormat(row.rate);
  }
  if (field === 'rate') {
    //22 税金，
    if (
      (Number(taxMode.value) === 1 &&
        (row.type === '附加税费' || row.type === '销项税额')) ||
      ((Number(taxMode.value) === 0 || store.deType === '22') &&
        row.type === '税金')
    ) {
      if ($table.isUpdateByRow(row, field)) {
        // rowValue.value = { ...row };
        infoMode.show({
          iconType: 'icon-querenshanchu',
          infoText: '是否确认修改税率？',
          descText: '修改税率将会同步关联取费表中计税设置的费率，是否确认修改?',
          isFunction: false,
          confirm: () => {
            update(row, field);
            infoMode.hide();
          },
          close: () => {
            $table.revertData(row, field);
            infoMode.hide();
          },
        });
        return;
      }
    }
  }
  // 判断单元格值是否被修改
  if ($table.isUpdateByRow(row, field)) {
    update(row, field);
  }
};
const update = (row, field) => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    unitCostSummary: { ...row },
  };
  console.log('费用汇总修改', apiData);
  feePro.saveCostSummary(apiData).then(res => {
    console.log('费用汇总修改结果', res);
    if (res.status !== 500) {
      message.success('修改成功!');
      tableData.value.map((item, index) => {
        if (item.sequenceNbr === row.sequenceNbr) {
          isCurrent.value = index;
        }
      });
      getTableData();
    } else if (res.status === 500) {
      message.error(res.message);
      const $table = upTable.value;
      $table.revertData(row, field);
    }
  });
};
const pasteIsDisabled = () => {
  if (store.summaryCopyInfo && store.summaryCopyInfo.asideTitle === 'fyhz') {
    menuConfig.body.options[0][2].disabled = false;
  } else {
    menuConfig.body.options[0][2].disabled = true;
  }
};
const menuConfig = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          name: '插入',
          code: 'add',
          disabled: false,
        },

        {
          code: 'copy',
          name: '复制',
        },
        {
          code: 'paste',
          name: '粘贴',
          disabled: true,
        },
        {
          code: 'delete',
          name: '删除',
          className: 'redFont',
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    if (!row) return;
    upTable.value.setCurrentRow(row);
    if (row && row.whetherTax === 1) {
      options[0][3].disabled = true;
      options[0][1].disabled = true;
      options[0][3].className = '';
    } else {
      options[0][3].disabled = false;
      options[0][1].disabled = false;
      options[0][3].className = 'redFont';
    }
    pasteIsDisabled();

    return true;
  },
});
const getCurrentIndex = item => {
  if (item) {
    tableData.value.map((n, index) => {
      if (n.sequenceNbr === item.sequenceNbr) {
        isCurrent.value = index;
      }
    });
  } else {
    isCurrent.value = 0;
  }
  let obj = {
    isCurrent: isCurrent.value,
    isLast: tableData.value.length - 1 === isCurrent.value,
  };
  console.log('obj---getCurrentIndex', obj);
  emits('getMoveInfo', obj);
};
const operate = (type, row) => {
  let operateType;
  switch (type) {
    case 'insert':
      // 插入
      operateType = 1;
      row ? getCurrentIndex(upTable.value.getCurrentRecord()) : '';
      break;
    case 'delete':
      // 删除
      operateType = 3;
      isCurrent.value = 0;
      break;
    case 'paste':
      // 粘贴
      operateType = 2;
      isCurrent.value += 1;
      break;
  }
  let isCurrentRow = upTable.value.getCurrentRecord();
  let targetSequenceNbr = isCurrentRow?.sequenceNbr;
  let lineNumber;
  tableData.value &&
    tableData.value.map((item, index) => {
      if (item.sequenceNbr === isCurrentRow.sequenceNbr) {
        lineNumber = index + 1;
      }
    });
  if (type === 'insert' || type === 'paste') {
    type === 'paste' ? (row.code = '') : '';
    let addData = {
      lineNumber: type === 'insert' ? lineNumber : lineNumber + 1,
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId, //单项ID
      unitId: store.currentTreeInfo?.id, //单位ID
      unitCostSummary: type === 'paste' ? { ...row } : {},
    };
    feePro.addCostSummary(addData).then(res => {
      if (res.status === 200) {
        type === 'insert'
          ? message.success('插入成功')
          : message.success('粘贴成功');
        console.log('********otherProjectOperates', res.result);
        getTableData();
      }
    });
  } else if (type === 'delete') {
    let deleteData = {
      sequenceNbr: row?.sequenceNbr,
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId, //单项ID
      unitId: store.currentTreeInfo?.id, //单位ID
    };
    feePro.deleteCostSummary(deleteData).then(res => {
      if (res.result && res.result.status === 500) {
        message.error(res.result.message);
        return;
      } else {
        message.success('删除成功');
        getTableData();
      }
    });
  }
};
const contextMenuClickEvent = ({ menu, row }) => {
  console.log('menu, row', menu, row);
  menu.code === 'delete' ? getCurrentIndex() : getCurrentIndex(row);
  switch (menu.code) {
    case 'copy':
      // 复制
      copyItem(row);
      break;
    case 'delete':
      // 删除
      deleteItem(row);
      break;
    case 'paste':
      // 粘贴
      pasteItem(row);
      break;
    case 'add':
      // 插入
      addItem(row);
      break;
  }
};
const pasteItem = () => {
  if (store.summaryCopyInfo && store.summaryCopyInfo.asideTitle === 'fyhz') {
    let paste = { ...store.summaryCopyInfo.copyInfo };
    operate('paste', paste);
  }
};
const addItem = item => {
  operate('insert', item);
};
const copyItem = item => {
  console.log('复制', item);
  if (item.whetherTax === 1) {
    message.warning('当前行不可复制');
    return;
  }
  store.SET_SUMMARY_COPYINFO({
    copyInfo: { ...item },
    asideTitle: 'fyhz',
  });
  message.success('复制成功');
  //需考虑跨单位工程之间可以复制粘贴
};
const deleteItem = item => {
  if (item.whetherTax === 1) {
    message.warning('当前行不可删除');
    return;
  }
  deleteInfo.value = { ...item };
  infoMode.show({
    iconType: 'icon-querenshanchu',
    isDelete: true,
    infoText: '是否确认删除？',
    descText: '是否删除当前数据行',
    confirm: () => {
      operate('delete', deleteInfo.value);
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};
const headerCellClassName = ({ column }) => {
  if (column.field === 'index') {
    return 'index-bg';
  }
  return null;
};
const cellClassName = ({ $columnIndex, row, column }) => {
  const selectName = selectedClassName({ $columnIndex, row, column });
  if (column.field === 'index') {
    return 'index-bg ' + selectName;
  }
  return selectName;
};
const tableCellClickEvent = ({ row, column, cell, event }) => {
  // currentInfo.value = row;
  // console.log(currentInfo.value, 'currentInfo.value-=-=-==-=--=');
  return true;
};
const typeList = [
  '规费',
  '税金',
  '分部分项工程量清单合计',
  '措施费用清单合计',
  '其他项目清单合计',
  '工程造价',
  '规费明细',
  '社会保障费',
  '安全文明施工费',
  '独立费',
  '单机措施项目费',
  '其他总价措施项目费',
  '安全文明施工费基本费',
  '安全文明施工费增加费',
  '分部分项人工费',
  '分部分项材料费',
  '分部分项机械费',
  '分部分项设备费',
  '分部分项主材费',
  '进项税额',
  '销项税额',
  '增值税应纳税额',
  '附加税费',
  '税前工程造价',
  '生产工具使用费',
  '繁华地段管理增加费',
  '冬季防寒费',
  '山地管护增加费',
  '增值税',
  '绿色施工安全防护措施费',
  '总价措施项目费',
  '甲供材料费',
  '空',
];
defineExpose({
  getTableData,
});
</script>
<style lang="scss" scoped>
.btns {
  margin-top: 15px;
  display: flex;
  justify-content: center;
  //position: absolute;
  //width: 200px;
  //bottom: 10px;
  //right: 40%;
  //display: flex;
  //justify-content: space-around;
  button + button {
    margin-left: 10px;
  }
}
.multiple-select {
  width: 50px;
  height: 30px;
  line-height: 30px;
  margin-left: -10px;
  text-indent: 10px;
  cursor: pointer;
}
// .content-project {
//   background: #ffffff;
//   height: 100%;
.table-content {
  // width: 100%;
  height: 100%;
  // overflow: hidden;
  background: #ffffff;
  user-select: none;
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  ::v-deep(.vxe-table .index-bg) {
    // background-color: rgba(243, 243, 243, 1);
    background-color: #fff;
  }
}
// }
</style>
