<template>
  <common-modal
    className="dialog-comm"
    v-model:modelValue="show"
    title="批量修改主材名称"
    width="750px"
    :mask="true"
    :lockView="false"
    @close="close"
  >
    <p style="width: 50%">
      <vxe-input
        style="width: 100%"
        @keydown="keydown"
        @clear="closeHandle"
        @search-click="suffixHandle"
        v-model.trim="inputValue"
        placeholder="请输入主材、设备名称或编码"
        type="search"
      ></vxe-input>
      <!-- <a-button @click="reset(1)">
        恢复名称规格型号
      </a-button>
      <a-button @click="reset(2)">
        恢复市场价
      </a-button> -->
    </p>
    <div>
      <vxe-table
        show-overflow
        ref="vxeTableView"
        :loading="viewLoading"
        border
        class="table-line"
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true, isCurrent: true }"
        :data="tableData"
        :min-height="200"
        :max-height="280"
        :scroll-y="{ enabled: true, gt: 0 }"
        @current-change="currentChangeRow"
        :tree-config="{
          transform: true,
          rowField: 'sequenceNbr',
          parentField: 'parentId',
          line: true,
          showIcon: true,
          expandAll: true,
          indent: 15,
          iconOpen: 'icon-caret-down',
          iconClose: 'icon-caret-right',
        }"
        :row-class-name="rowClassName"
      >
        <vxe-column field="bdCode" title="编码" :width="200" tree-node>
          <template #default="{ row }">
            <span v-if="row.kind === 5 || row.kind === 4">{{ row.materialCode }}</span>
            <span v-else>{{ row.bdCode }}</span>
          </template>
        </vxe-column>
        <vxe-column field="name" title="名称" show-overflow></vxe-column>
        <vxe-column field="specification" title="规格" show-overflow></vxe-column>
        <!-- <vxe-column
          field="marketPrice"
          title="市场价"
          show-overflow
        ></vxe-column>
        <vxe-column
          field="taxRemoval"
          title="税率"
          show-overflow
        ></vxe-column> -->
      </vxe-table>
    </div>

    <p style="height: 1px; width: 100%; margin: 8px 0; background: rgba(0, 0, 0, 0.06)"></p>
    <div style="display: flex; justify-content: space-between">
      <div style="width: 79%">
        <p style="display: flex; align-items: center; justify-content: space-between">
          <label>名称：</label>
          <vxe-input
            :readonly="currentRowData.kind !== 5 && currentRowData.kind !== 4"
            v-model.trim="updateName"
            @change="vxeInputChange('name')"
            placeholder=""
            style="width: 40%"
          ></vxe-input>
          <label style="margin-left: 10px">规格：</label>
          <vxe-input
            :readonly="currentRowData.kind !== 5 && currentRowData.kind !== 4"
            v-model.trim="updateSpecification"
            @change="vxeInputChange('specification')"
            placeholder=""
            style="width: 40%"
          ></vxe-input>
        </p>
        <vxe-table
          align="center"
          :loading="viewLoading"
          :column-config="{ resizable: true }"
          :checkbox-config="{ labelField: 'name', highlight: true, range: true }"
          :data="featureData"
          :height="128"
          :min-height="128"
          :scroll-y="{ enabled: true, gt: 0 }"
          @current-change="currentChangeRow"
        >
          <vxe-column field="featureName" :min-width="130" title="项目特征" show-overflow />
          <vxe-column field="featureValue" title="特征值" />
          <vxe-column field="checkFlag" title="选项">
            <template #default="{ row }">
              <a-checkbox v-model:checked="row.checkFlag" @change="vxeCheckbox()" />
            </template>
          </vxe-column>
        </vxe-table>
      </div>

      <div
        class="right-radio-checkbox"
        style="width: 21%; border: 1px solid #b9b9b9; margin-left: 8px; padding: 6px 0 6px 8px"
      >
        <div style="font-size: 14px; margin-bottom: 6px; color: rgba(0, 0, 0, 0.85)">修改规则</div>
        <a-radio-group v-model:value="nameAndSpecifications">
          <a-radio :value="1">更改材料名称</a-radio>
          <br />
          <a-radio :value="2">更改材料规格</a-radio>
        </a-radio-group>
        <a-checkbox-group v-model:value="subitemList" style="width: 100%">
          <a-checkbox value="A" @change="suborderNameChange">更改子目名称</a-checkbox>
          <br />
          <a-checkbox value="B" @change="takeAlistChange">取清单名称</a-checkbox>
        </a-checkbox-group>
      </div>
    </div>
    <div class="btn-list">
      <a-button
        style="margin-top: 4px; height: 29px; padding: 2px 10px; color: #1890ff"
        @click="batchProcessing"
        >批量应用特征值</a-button
      >
      <span class="btn-list-center">
        <a-button @click="close()">取消</a-button>
        <a-button type="primary" @click="sure()">确定</a-button>
      </span>
    </div>
  </common-modal>
</template>

<script setup>
import { computed, nextTick, reactive, ref, toRaw, watch, markRaw } from 'vue';
import csProject from '@/api/projectDetail';
import { message } from 'ant-design-vue';
// import XEUtils from 'xe-utils'
const XEUtils = require('xe-utils');
import { projectDetailStore } from '@/store/projectDetail.js';
import CommonModal from '@/components/global/commonModal/index.vue';
import infoMode from '@/plugins/infoMode.js';
const projectStore = projectDetailStore();
const props = defineProps(['visible']);
const emits = defineEmits(['update:visible', 'refreshTableList']);
const inputValue = ref();
const dialogVisible = ref(false);
const settingVisible = ref(false);
const vxeTableView = ref();
const vxeTableSetting = ref();
const settingLoading = ref(false);
const tableData = ref([]);
const featureData = ref([]);
const viewLoading = ref([]);

const nameAndSpecifications = ref(2);
const subitemList = ref([]);

const parameterList = ref({}); //缓存修改了的数据

const rowClassName = ({ row }) => {
  // 定义一个映射对象，将 row.kind 映射到相应的类名
  const classMapping = {
    0: 'row-unit',
    '01': 'row-sub',
    '02': 'row-sub',
    '03': 'row-qd',
    //主材设备设置字体颜色
    4: 'zcsb-color',
    5: 'zcsb-color',
  };
  return classMapping[row.kind] || 'normal-info';
};
const show = computed({
  get: () => props.visible,
  set: val => {
    emits('update:visible', val);
  },
});

const tableDataOld = ref([]);
const keydown = e => {
  console.log(e, 'keydown');
  if (e.$event.keyCode === 13) {
    suffixHandle();
  }
};
const closeHandle = e => {
  console.log(e, 'closeHandle');
  viewLoading.value = true;
  tableData.value = tableDataOld.value;
  vxeTableView.value.reloadData(tableData.value);
  viewLoading.value = false;
};
const suffixHandle = () => {
  viewLoading.value = true;
  if (inputValue.value) {
    let res = fuzzySearchFlatData(tableDataOld.value, inputValue.value);
    tableData.value = [...res];
  } else {
    tableData.value = [...tableDataOld.value];
  }
  vxeTableView.value.reloadData(tableData.value);
  viewLoading.value = false;
};

const close = () => {
  inputValue.value = '';
  tableData.value = [];
  nameAndSpecifications.value = 2;
  subitemList.value = [];
  featureData.value = [];
  updateName.value = '';
  updateSpecification.value = '';
  parameterList.value = [];
  emits('update:visible', false);
};

//取消
const cancel = (refresh = false) => {
  settingVisible.value = false;
};
//批量处理所有材料
const sure = async () => {
  let constructProjectRcjList = [];
  Object.values(parameterList.value).forEach(item => {
    if (item.kind === '04') {
      constructProjectRcjList.push({
        sequenceNbr: item.sequenceNbr,
        materialName: item.name,
        // materialCode: item.materialCode,
        specification: item.specification,
        type: item.type,
        libraryCode: item.libraryCode,
        column: 'name',
        value: item.name,
        rcjOrDe: 'de',
      });
    } else if (item.kind === 5 || item.kind === 4) {
      const obj = String(item.rcjFlag) === '1' ? { deId: item.deId } : {};
      constructProjectRcjList.push({
        sequenceNbr: item.sequenceNbr,
        materialName: item.name,
        // materialCode: item.materialCode,
        specification: item.specification,
        type: item.type,
        libraryCode: item.libraryCode,
        rcjOrDe: 'rcj',
        rcjLabelDe: item.rcjFlag ? item.rcjFlag : false,
        ...obj,
      });
    }
  });
  if (constructProjectRcjList.length === 0) {
    message.info('无修改项！');
    return null;
  }
  try {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pageType: projectStore.tabSelectName === '分部分项' ? 'fbfx' : 'csxm',
      constructProjectRcjList: constructProjectRcjList,
    };
    console.log(apiData);
    settingLoading.value = true;
    console.log('批量处理所有材料=》参数', apiData);
    let res = await csProject.zcRcjBatchUpdate(apiData);
    console.log('批量处理所有材料=》返回值', res);
    if (res.code === 200) {
      // await getTableList();
      message.success('修改成功！');
      emits('refreshTableList');
      close();
    }
  } catch (e) {
    console.error('批量处理所有材料=》错误', e);
  } finally {
    settingLoading.value = false;
  }
};
const currentRowData = ref({}); //最新值，可同步修改
const currentOldRow = ref({}); //缓存的最原始的值

const updateName = ref();
const updateSpecification = ref();

const listRow = ref({}); // 清单选中项
const listOldRow = ref({}); // 清单选中项

const suborderRow = ref({}); // 子目选中项
const suborderOldRow = ref({}); // 子目选中项

// 选中当前项
const currentChangeRow = ({ row }) => {
  currentOldRow.value = { ...row }; // 原始的值
  currentRowData.value = row;
  nameAndSpecifications.value = 2;
  subitemList.value = [];
  if (row.kind === 5 || row.kind === 4) {
    updateName.value = row.name;
    updateSpecification.value = row.specification;
    // 递归查找,满足条件的 子目更改项 和 清单
    const parentList = findParentsByCondition([tableData.value[0]], currentRowData.value, parent =>
      ['04', '03'].includes(parent.kind)
    );
    //清单数据缓存，复制一份地址值单独的数据
    listOldRow.value = {
      ...parentList.find(item => ['03'].includes(item.kind)),
    };
    //子目数据缓存，复制一份地址值单独的数据
    suborderOldRow.value = {
      ...parentList.find(item => ['04'].includes(item.kind)),
    };

    listRow.value = parentList.find(item => ['03'].includes(item.kind));
    suborderRow.value = parentList.find(item => ['04'].includes(item.kind));

    console.log(
      parentList.find(item => ['03'].includes(item.kind)),
      '111111111111'
    );
    featureData.value =
      row.qdFeatures.map(item => {
        item.checkFlag = false;
        return item;
      }) || [];
    // featureData.value = row.qdFeatures;
  } else {
    updateName.value = '';
    updateSpecification.value = '';
    featureData.value = [];
  }
  // featureData.value = row.qdFeatures||[];
  console.log('currentRowData', currentRowData.value);
};

// 查询列表
const getTableList = async () => {
  try {
    //批量修改主材
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pageType: projectStore.tabSelectName === '分部分项' ? 'fbfx' : 'csxm', // levelType = fbfx 分部分项 ,levelType =  csxm 措施项目
    };
    viewLoading.value = true;
    console.log('批量修改主材=》参数', apiData);
    let res = await csProject.zcRcjBatchSelect(apiData);
    console.log('批量修改查询=》返回值', res);
    if (res.code === 200 && res.result) {
      res.result.forEach(item => {
        if (item.kind === 5 || item.kind === 4) {
          item.qdFeatures.forEach(item => {
            item.checkFlag = false;
          });
        }
      });
      tableDataOld.value = [...res.result]; // XEUtils.clone([res.result[0]],true);

      tableData.value = res.result;
      vxeTableView.value.reloadData(tableData.value); //解决树不展开bug
    } else {
      tableDataOld.value = [];
      tableData.value = [];
      // vxeTableView.value.reloadData(tableData.value);//解决树不展开bug
    }
  } catch (e) {
    console.error('批量修改主材=>错误信息', e);
  } finally {
    viewLoading.value = false;
  }
};

// 名称、规格修改
const vxeInputChange = sign => {
  const obj = {
    name: () => {
      // 非主材 和设备的，更改
      if (currentRowData.value.kind !== 5 && currentRowData.value.kind !== 4) {
        return null;
      }

      // 检查是否包含 'A'
      const containsA = subitemList.value.includes('A');
      // 更新子目名称
      if (containsA) {
        suborderRow.value.name = updateName.value;
        setParameterList(suborderRow.value);
      } else {
        suborderRow.value.name = suborderOldRow.value.name;
        setParameterList(suborderRow.value, 'del');
      }

      // 更新当前行名称
      currentRowData.value.name = updateName.value;
      setParameterList(currentRowData.value);
    },
    specification: () => {
      // if(String(nameAndSpecifications.value) === '2'){
      //   currentRowData.value.specification = updateSpecification.value;
      // }
      if (currentRowData.value.kind !== 5 && currentRowData.value.kind !== 4) {
        return null;
      }
      currentRowData.value.specification = updateSpecification.value;
      setParameterList(currentRowData.value);
    },
  }[sign]();
};

// 缓存更改项
const setParameterList = (row, flag) => {
  if (flag === 'del' && parameterList.value.hasOwnProperty(row.sequenceNbr)) {
    delete parameterList.value[row.sequenceNbr];
    return null;
  }
  parameterList.value[row.sequenceNbr] = row;
};

// 表格选项复选功能
const vxeCheckbox = parameter => {
  /// 过滤出 checkFlag 为 true 的项，并提取 featureValue 拼接成字符串
  const str = featureData.value
    .filter(item => item.checkFlag)
    .map(item => item.featureValue)
    .join(' ')
    .trim();

  // 判断是更新名称还是规格
  const isNameUpdate = String(nameAndSpecifications.value) === '1';
  const isSpecificationUpdate = String(nameAndSpecifications.value) === '2';

  // 根据 str 是否为空，决定更新的值
  updateName.value = isNameUpdate ? str || currentOldRow.value.name : updateName.value;
  updateSpecification.value = isSpecificationUpdate
    ? str || currentOldRow.value.specification
    : updateSpecification.value;

  // 是否勾取清单名称
  if (subitemList.value.includes('B')) {
    const nameAndSpec = String(nameAndSpecifications.value);
    const checkList = featureData.value.filter(item => item.checkFlag);
    if (nameAndSpec === '1') {
      if (checkList.length) {
        updateName.value = `${listName.value} ${updateName.value ?? ''}`;
      } else {
        updateName.value = listName.value ?? '';
      }
      //updateName.value = listName.value ?? '';
    } else if (nameAndSpec === '2') {
      //updateSpecification.value = listName.value ?? '';
      if (checkList.length) {
        updateSpecification.value = `${listName.value} ${updateSpecification.value ?? ''}`;
      } else {
        updateSpecification.value = listName.value ?? '';
      }
    }
  }

  if (parameter) {
    vxeInputChange(parameter);
  } else {
    const mappedParam = {
      1: 'name',
      2: 'specification',
    }?.[nameAndSpecifications.value];
    vxeInputChange(mappedParam);
  }
};
const listName = ref('');

// 更改子目名称
const suborderNameChange = async e => {
  if (currentRowData.value.kind === 5 || currentRowData.value.kind === 4) {
    await nextTick();
    vxeCheckbox('name');
  }
};

// 取清单名称
const takeAlistChange = async e => {
  if (currentRowData.value.kind === 5 || currentRowData.value.kind === 4) {
    // 更新 listName 的值
    listName.value = e.target.checked ? (listRow.value.name ?? '') : '';
    // 等待 DOM 更新
    await nextTick();
    vxeCheckbox();
  }
};

// 批量处理所有材料
const batchProcessing = () => {
  infoMode.show({
    isSureModal: false,
    iconType: 'icon-querenshanchu',
    infoText: '是否按照当前修改规则，将页面中所有的主材及设备批量替换为特征值？',
    confirmText: '是',
    cancelText: '否',
    confirm: () => {
      update();
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
  const update = () => {
    console.log(tableData.value, featureData.value);
    const checkList = featureData.value.filter(item => item.checkFlag);
    treeEach([tableData.value[0]], (item, parent) => {
      if (item.kind === 5 || item.kind === 4) {
        console.log('🚀 ~ treeEach ~ item:', item);
        // if(checkList.length){
        //   if(Array.isArray(item.qdFeatures) && item.qdFeatures.length){
        //     item.qdFeatures.forEach(it=>it.checkFlag = true);
        //   }
        // }
        const str = item.qdFeatures
          .map(item => item.featureValue)
          .join(' ')
          .trim();
        // 第一种情况，不选更改子目名称  不选取清单名称
        if (!subitemList.value.includes('A') && !subitemList.value.includes('B')) {
          if (nameAndSpecifications.value === 1) {
            item.name = str;
          } else if (nameAndSpecifications.value === 2) {
            item.specification = str;
          }
          setParameterList(item);
        }
        // 第二种情况，选更改子目名称  不选取清单名称
        else if (subitemList.value.includes('A') && !subitemList.value.includes('B')) {
          if (nameAndSpecifications.value === 1) {
            parent.name = str;
            item.name = str;
          } else if (nameAndSpecifications.value === 2) {
            item.specification = str;
          }
          setParameterList(parent);
        }

        // 第三种情况，选更改子目名称  选取清单名称
        else if (subitemList.value.includes('A') && subitemList.value.includes('B')) {
          if (checkList.length) {
            if (nameAndSpecifications.value === 1) {
              item.name = `${parent.parent.name} ${str}`;
              parent.name = `${parent.parent.name} ${str}`;
            } else if (nameAndSpecifications.value === 2) {
              item.specification = `${parent.parent.name} ${str}`;
              // parent.specification = `${parent.parent.name} ${str}`;
            }
          } else {
            if (nameAndSpecifications.value === 1) {
              item.name = parent.parent.name;
              parent.name = parent.parent.name;
            } else if (nameAndSpecifications.value === 2) {
              item.specification = parent.parent.name;
              parent.name = item.name;
              // parent.specification = parent.parent.name;
            }
          }
          setParameterList(item);
          setParameterList(parent);
        }

        // 第四种情况，没选更改子目名称  选取清单名称
        else if (!subitemList.value.includes('A') && subitemList.value.includes('B')) {
          if (checkList.length) {
            if (nameAndSpecifications.value === 1) {
              item.name = `${parent.parent.name} ${str}`;
            } else if (nameAndSpecifications.value === 2) {
              item.specification = `${parent.parent.name} ${str}`;
            }
          } else {
            if (nameAndSpecifications.value === 1) {
              item.name = parent.parent.name; //
              // parent.name = parent.parent.name;
            } else if (nameAndSpecifications.value === 2) {
              item.specification = parent.parent.name; //
              // parent.specification = parent.parent.name;
            }
          }
          setParameterList(item);
        }
      }
    });
  };
};

watch(
  () => props.visible,
  async () => {
    if (props.visible) {
      inputValue.value = '';
      tableData.value = [];
      await getTableList();
      nameAndSpecifications.value = 2;
      subitemList.value = [];
      featureData.value = [];
      updateName.value = '';
      updateSpecification.value = '';
      parameterList.value = [];
    }
  }
);

/**
 * 查找所有父级并筛选满足条件的父级
 * @param {Array} array - 原数组
 * @param {Function} conditionFn - 定义条件函数
 * @param {Object} currentNode - 当前节点对象
 * @param {Array} parents - 子首次不传
 * 例如：const conditionFn = (parent) => parent.id === 1;
 */
function findParentsByCondition(array, currentNode, conditionFn, parents = []) {
  for (let item of array) {
    if (item.sequenceNbr === currentNode.sequenceNbr) {
      // 找到当前节点，筛选满足条件的父级
      return parents.filter(conditionFn);
    }
    if (item.children && item.children.length > 0) {
      // 递归查找子节点，并将当前节点加入父级列表
      const result = findParentsByCondition(item.children, currentNode, conditionFn, [
        ...parents,
        item,
      ]);
      if (result.length > 0) {
        return result; // 如果找到满足条件的父级，返回
      }
    }
  }
  return []; // 未找到
}
/**
 * 递归遍历树形结构
 * @param {Array} tree - 树形结构数据
 * @param {Function} callback - 回调函数，接收当前节点作为参数
 * @param {Object} options - 配置项
 * @param {string} options.childrenKey - 子节点的字段名，默认为 'children'
 * @param {boolean} options.isBreak - 是否支持提前终止遍历，默认为 false
 */
function treeEach(tree, callback, options = {}) {
  const { childrenKey = 'children', isBreak = false } = options;
  // 递归遍历函数
  function traverse(nodes, parent = null) {
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];

      // 执行回调函数
      const result = callback(node, parent);

      // 如果支持提前终止且回调返回 false，则终止遍历
      if (isBreak && result === false) {
        return false;
      }

      // 如果当前节点有子节点，递归遍历子节点
      if (node[childrenKey] && node[childrenKey].length > 0) {
        const shouldContinue = traverse(node[childrenKey], node);
        if (isBreak && shouldContinue === false) {
          return false;
        }
      }
    }
    return true;
  }

  // 开始遍历
  traverse(tree);
}
// 调用递归函数 rowField: 'sequenceNbr', parentField: 'parentId',
function fuzzySearch(nodes, keyword, keys = ['materialCode', 'name']) {
  const results = [];
  function search(nodes) {
    nodes.forEach(node => {
      if (node.kind === 4 || node.kind === 5) {
        // 检查多个字段是否包含关键字
        const isMatch = keys.some(key => node[key]?.toLowerCase().includes(keyword.toLowerCase()));
        if (isMatch) {
          results.push(node);
        }
      } else {
        results.push(node);
      }
    });
  }
  search(nodes);
  console.log(results);
  return results;
}
/**
 * 模糊查找二维数组并返回过滤后的二维数组
 * @param {Array} data 二维数组数据
 * @param {string} keyword 模糊查找关键字
 * @param {string} idKey 节点唯一标识字段（默认为 'id'）
 * @param {string} parentIdKey 父节点标识字段（默认为 'parentId'）
 * @param {Array} keys 查找字段（默认为 'materialCode','name'）
 * @returns {Array} 过滤后的二维数组
 */
function fuzzySearchFlatData(
  data,
  keyword,
  idKey = 'sequenceNbr',
  parentIdKey = 'parentId',
  keys = ['materialCode', 'name']
) {
  if (!keyword) return data; // 如果没有关键字，返回完整数据

  // 创建一个 Map，方便通过 id 快速查找节点
  const nodeMap = new Map(data.map(node => [node[idKey], node]));
  // 递归检查节点及其关联节点是否满足条件
  const checkNode = (nodeId, matchedIds) => {
    const node = nodeMap.get(nodeId);
    if (!node) return false; // 如果节点不存在，返回 false

    // 如果节点已经检查过，直接返回结果
    if (matchedIds.has(nodeId)) return true;

    // 检查当前节点是否满足条件
    // let keys = ['materialCode','name'];
    console.log(node);
    let isMatch = false;
    if (node.kind === 4 || node.kind === 5) {
      isMatch = keys.some(key => node[key]?.toLowerCase().includes(keyword.toLowerCase()));
    }
    //const isMatch = node[searchKey].toLowerCase().includes(keyword.toLowerCase());
    // 检查子节点是否满足条件
    let hasMatchedChild = false;
    for (const child of data) {
      if (child[parentIdKey] === nodeId) {
        if (checkNode(child[idKey], matchedIds)) {
          hasMatchedChild = true;
        }
      }
    }

    // 如果当前节点或子节点满足条件，则保留该节点
    if (isMatch || hasMatchedChild) {
      matchedIds.add(nodeId); // 标记为满足条件
      return true;
    }

    return false; // 不满足条件
  };

  // 收集所有满足条件的节点 ID
  const matchedIds = new Set();
  for (const node of data) {
    if (String(node[parentIdKey]) === '0' || node[parentIdKey] == null) {
      // 从根节点开始检查
      checkNode(node[idKey], matchedIds);
    }
  }
  // 过滤数据，只保留满足条件的节点
  return data.filter(node => matchedIds.has(node[idKey]));
}
const reset = type => {
  infoMode.show({
    isSureModal: false,
    iconType: 'icon-querenshanchu',
    infoText: `是否将所有主材、设备、定额的${type === 1 ? '名称、规格型号' : '市场价'}恢复？`,
    confirmText: '是',
    cancelText: '否',
    confirm: () => {
      resetFun(type);
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};
const resetFun = () => {
  if (type) {
    //恢复名称规
  }
};
</script>

<style lang="scss" scoped>
@use '@/views/projectDetail/customize/measuresItem/tableIcon.scss';

.right-radio-checkbox {
  .ant-radio-group,
  .ant-checkbox-group {
    //line-height:30px;
    .ant-radio-wrapper {
      margin-top: 10px;
    }
    .ant-checkbox-wrapper {
      margin-top: 10px;
    }
    .ant-radio-wrapper:first-child {
      margin-top: 4px;
    }
  }
}
.btnList,
.btn-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  &-center {
    margin-top: 12px;
    .ant-btn {
      height: 25px;
      padding: 0 10px;
      font-size: 12px;
      margin: 0 5px;
    }
  }
}
.table-content {
  width: 100%;
  margin: -15px auto;
  height: calc(100% - 30px);
}
.setting-content {
  height: 100%;
  width: 100%;
  .table-list {
    height: calc(100% - 40px);
    margin-bottom: 10px;
  }
}
::v-deep(.vxe-table .row-unit) {
  background: #e6dbeb !important;
}
::v-deep(.vxe-table .row-sub) {
  background: #efe9f2 !important;
}
::v-deep(.vxe-table .row-qd) {
  background: #dce6fa !important;
}
::v-deep(.vxe-body--row.row--current) {
  background: #a6c3fa;
}
.zcsb-color {
  color: #7d1dff !important;
}
::v-deep(.vxe-table) {
  .vxe-tree--line {
    /* 修改连接线的颜色 */
    border-left: 1px solid #87b2f2;
    border-bottom: 1px solid #87b2f2;
  }
}
</style>
