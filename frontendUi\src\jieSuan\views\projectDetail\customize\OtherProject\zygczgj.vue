<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-17 16:28:50
-->
<template>
  <div class="table-content">
    <child-page-table
      v-if="originalFlag"
      :pageType="'zygczgj'"
      :columnList="createList()"
    ></child-page-table>
    <child-page-table
      v-else
      :pageType="'zygczgj'"
      :columnList="createList()"
    ></child-page-table>
  </div>
</template>
<script setup>
import { ref, onActivated } from 'vue';
import ChildPageTable from './childPageTable.vue';
import { projectDetailStore } from '@/store/projectDetail';

import { createListHooks } from './columns.js';
const { createList } = createListHooks('zygczgj');



const projectStore = projectDetailStore();
const originalFlag = ref(false);
const columnListInner = [
  {
    field: 'dispNo',
    title: '序号',
    minWidth: 60,
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'dispNo_edit' },
  },
  {
    field: 'name',
    title: '工程名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'name_edit' },
  },
  {
    field: 'content',
    title: '工程内容',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'content_edit' },
  },
  {
    field: 'unit',
    title: '单位',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'unit_edit' },
  },
  {
    field: 'jieSuanTotal',
    title: '合同数量',
    minWidth: 80,
  },
  {
    field: 'jieSuanPrice',
    title: '合同单价',
    minWidth: 100,
  },
  {
    field: 'jieSuanAmount',
    minWidth: 100,
    title: '合同金额',
  },
  {
    field: 'jieSuanTaxRemoval',
    title: '合同除税系数(%)',
    minWidth: 180,
  },

  {
    field: 'jieSuanJxTotal',
    minWidth: 100,
    title: '合同进项合计',
  },
  {
    field: 'jieSuanCsPrice',
    minWidth: 100,
    title: '合同除税单价',
  },
  {
    field: 'jieSuanCsTotal',
    minWidth: 100,
    title: '合同除税合价',
  },
  {
    field: 'amount',
    minWidth: 100,
    title: '结算数量',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanTotal_edit' },
  },
  {
    field: 'price',
    minWidth: 100,
    title: '结算单价',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanPrice_edit' },
  },
  {
    field: 'total',
    minWidth: 100,
    title: '结算金额',
    // editRender: { autofocus: '.vxe-input--inner' },
    // type: 'text',
    // slots: { edit: 'jiesuanAmount_edit' },
  },
  {
    field: 'taxRemoval',
    minWidth: 130,
    title: '结算除税系数(%)',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanTaxRemoval_edit' },
  },
  {
    field: 'jxTotal',
    minWidth: 130,
    title: '结算进项合计',
  },
  {
    field: 'csPrice',
    minWidth: 130,
    title: '结算除税单价',
  },
  {
    field: 'csTotal',
    minWidth: 130,
    title: '结算除税合计',
  },
  {
    field: 'description',
    title: '备注',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'dec_edit' },
  },
];
const columnListOut = [
  {
    field: 'dispNo',
    title: '序号',
    minWidth: 60,
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'dispNo_edit' },
  },
  {
    field: 'name',
    title: '工程名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'name_edit' },
  },
  {
    field: 'content',
    title: '工程内容',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'content_edit' },
  },
  {
    field: 'unit',
    title: '单位',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'unit_edit' },
  },
  {
    field: 'amount',
    minWidth: 100,
    title: '结算数量',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanTotal_edit' },
  },
  {
    field: 'price',
    minWidth: 100,
    title: '结算单价',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanPrice_edit' },
  },
  {
    field: 'total',
    minWidth: 100,
    title: '结算金额',
    // editRender: { autofocus: '.vxe-input--inner' },
    // type: 'text',
    // slots: { edit: 'jiesuanAmount_edit' },
  },
  {
    field: 'taxRemoval',
    minWidth: 130,
    title: '结算除税系数(%)',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanTaxRemoval_edit' },
  },
  {
    field: 'jxTotal',
    minWidth: 130,
    title: '结算进项合计',
  },
  {
    field: 'csPrice',
    minWidth: 130,
    title: '结算除税单价',
  },
  {
    field: 'csTotal',
    minWidth: 130,
    title: '结算除税合计',
  },
];
onActivated(() => {
  originalFlag.value = projectStore.currentTreeInfo.originalFlag;
});
</script>
<style lang="scss" scoped>
@import './otherProject.scss';
</style>
