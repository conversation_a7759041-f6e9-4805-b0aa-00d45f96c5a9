const {JieSuanRcjStageUtils} = require("../../utils/JieSuanRcjStageUtils");
const {ObjectUtils} = require("../../../../electron/utils/ObjectUtils");
const {NumberUtil} = require("../../../../common/NumberUtil");
const {Organ, Gene} = require("@valuation/rules-engine");
let baseRules = {
    //全局数据
    CONTEXT:(ctx)=>{
        return {name:"CONTEXT",from:"runtime",column:(ctx)=>{
                return ctx;
            }};
    },

    //定额工程量
    DE_GCL:(ctx)=>{
        return {name:"DE_GCL",from:"runtime",column:(ctx)=>{
                let {isStage,dependRcjList,unit,arg,rcj,itemBillProjects,measureProjectTables} = ctx;
                //let {itemBillProjects,measureProjectTables} = unit;
                let  deIdList = dependRcjList.map(k => k.deId);
                let deList = itemBillProjects.filter(k =>k.kind =="04" &&deIdList.includes(k.sequenceNbr));
                deList.push(...measureProjectTables.filter(k =>deIdList.includes(k.sequenceNbr) && k.kind =="04"));
                //第i分期定额工程量=第i期清单工程量*定额含量
                //第i期A材料调差工程量 =∑ （第i分期定额工程量*材料A消耗量）
                let resultDeInfo = new Array();
                //不分期
                //if (!isStage || arg.kind == 0 || ObjectUtils.isEmpty(arg.adjustMethod)){
                if (!isStage){
                    const resultMap = deList.reduce((map, item) => {
                        map.set(item.sequenceNbr, item.quantity); // key 是 id，value 是 age
                        return map;
                    }, new Map());
                    resultDeInfo.push(resultMap);
                    return resultDeInfo;
                }
                //是否分次
                let  qdIdList = deList.map(k => k.parentId);
                let qdList = itemBillProjects.filter(k =>k.kind =="03" && qdIdList.includes(k.sequenceNbr));
                qdList.push(...measureProjectTables.filter(k =>qdIdList.includes(k.sequenceNbr) && k.kind =="03"));
                //获取当前调整
                let scopeList = JieSuanRcjStageUtils.rcjAdjustMethod(unit,rcj.kind,rcj.adjustMethod,arg.kind);
                if (ObjectUtils.isNotEmpty(scopeList)){
                    //获取到次对应的期数
                    let stagePeriods = JieSuanRcjStageUtils.periods(unit);
                    let periods = [];
                    if (ObjectUtils.isEmpty(arg.num)){
                        periods = JieSuanRcjStageUtils.generateNaturalNumbers([1,stagePeriods]);
                    }else if(ObjectUtils.isNotEmpty(arg.onlyStages)){
                        //工程量明细查看
                        periods = [arg.num];
                    }else {
                        periods = JieSuanRcjStageUtils.generateNaturalNumbers(scopeList[arg.num-1]);
                    }
                    periods.forEach(k =>{
                        //定额工程量=第i期清单工程量*含量  5000*0.01=50
                        let deInfo = new Map();
                        deList.forEach(a =>{
                            let qd = qdList.find(i =>i.sequenceNbr == a.parentId);
                            //清单第n期的工程量
                            let qdStageRatio = JieSuanRcjStageUtils.getQdStageRatio(qd,k-1);
                            //deInfo.set(a.sequenceNbr,NumberUtil.multiply(Number(qdStageQuantity),Number(a.deProportion)))
                            deInfo.set(a.sequenceNbr,Number(qdStageRatio))
                        });
                        resultDeInfo.push(deInfo);
                    });
                    return resultDeInfo;
                }

            }};
    },

    //基期价
    jieSuanBasePrice:(ctx)=>{
        return {name:"CONTEXT",from:"runtime",column:"jieSuanBasePrice"}
    },
    //合同价、合同单价
    jieSuanMarketPrice:(ctx)=>{
        return {name:"CONTEXT",from:"runtime",column:"jieSuanMarketPrice"}
    },
    //合同除税系数%
    taxRemoval:(ctx)=>{
        return {name:"CONTEXT",from:"runtime",column:"taxRemoval"}
    },

};
//调差工程量
let getjieSuanStageDifferenceQuantityRule=(seq)=>{
    return Organ.create({name:seq+"_jieSuanStageDifferenceQuantity",description:"调差工程量",gene:Gene.from(["DE_GCL","CONTEXT"],
            ({DE_GCL,CONTEXT})=>{
                let {isStage,dependRcjList,unit,method,num,constructId, singleId, unitId,rcj,arg,levelType,calculateSumFlag} = CONTEXT;

                //作用于单项或者工程项目级别不直接计算，而是从明细计算求和取值
                if (calculateSumFlag){
                    return rcj.totalNumber;
                }


                //不分期
                if (!isStage){
                    return dependRcjList.reduce((sum, item) => {
                        return sum + item.totalNumber;
                    }, 0);
                }
                //分期以及分次
                let scopeList = JieSuanRcjStageUtils.rcjAdjustMethod(unit,rcj.kind,rcj.adjustMethod,arg.kind);
                if (ObjectUtils.isNotEmpty(scopeList)){
                    //获取到次对应的期数
                    let sum = 0;
                    DE_GCL.forEach(k =>{
                        let reduce = dependRcjList.reduce((sum, item) => {
                            return sum + (item.totalNumber * NumberUtil.divide(k.get(item.deId),100));
                        }, 0);
                        sum = sum + reduce;
                    })
                    return sum;
                }
            })});
}

//基期价  通用
let getjieSuanBasePrice=(seq)=>{
    return Organ.create({name:seq+"_jieSuanBasePrice",description:"基期价",gene:Gene.from(["CONTEXT"],
            (cxt)=>{
        let {rcj} = cxt.CONTEXT
        return rcj.jieSuanBasePrice;
            })})
}

//结算单价 通用
let getmarketPrice=(seq)=>{
    return Organ.create({name:seq+"_marketPrice",description:"结算单价",gene:Gene.from(["CONTEXT"],
            (cxt)=>{
                let {isStage,unit,rcj,arg} = cxt.CONTEXT;
                let {num,adjustMethod,levelType} = arg;
                //let rcjJieSuanPrice = rcj.jieSuanRcjDifferenceTypeList.find(k =>k.rcjDifferenceType ==adjustMethod);
                if (!isStage){
                    return rcj.marketPrice;
                }
                let periods = 1;
                if (!ObjectUtils.isEmpty(arg.num)){
                    periods = arg.num;
                }
                return  rcj.jieSuanRcjDifferenceTypeList[periods-1].marketPrice;
            })})
}
//结算单价来源
let getjieSuanPriceSource=(seq)=>{
    return Organ.create({name:seq+"_jieSuanPriceSource",description:"结算单价来源",gene:Gene.from(["CONTEXT"],
            (cxt)=>{
                let {isStage,unit,rcj,arg} = cxt.CONTEXT;
                let {num,adjustMethod} = arg;
                //let rcjJieSuanPrice = rcj.jieSuanRcjDifferenceTypeList.find(k =>k.rcjDifferenceType ==adjustMethod);
                if (!isStage){
                    return rcj.jieSuanPriceSource;
                }
                //分期以及分次
                let scopeList = JieSuanRcjStageUtils.rcjAdjustMethod(unit,rcj.kind,rcj.adjustMethod);
                let periods = 1;
                if (!ObjectUtils.isEmpty(arg.num)){
                    periods = arg.num;
                }
                return  rcj.jieSuanRcjDifferenceTypeList[periods-1].jieSuanPriceSource;
            })})
}
//价差合计
let getJieSuanPriceDifferencSum=(seq)=>{
    return Organ.create({name:seq+"_jieSuanPriceDifferencSum",description:seq+"价差合计",gene:Gene.from(["CONTEXT",seq+"_jieSuanStageDifferenceQuantity",seq+"_jieSuanPriceDifferenc"],
            (ctx)=>{
                let {kind} = ctx.CONTEXT.arg;

                //价差合计=单位价差*调差工程量
                return NumberUtil.multiply(ctx[seq+"_jieSuanStageDifferenceQuantity"],ctx[seq+"_jieSuanPriceDifferenc"]);
            })});
}


//结算价差进项税额
let getSettlementPriceDifferencInputTax=(seq)=>{
    return Organ.create({name:seq+"_settlementPriceDifferencInputTax",description:seq+"结算价差进项税额",gene:Gene.from([seq+"_taxRemoval",seq+"_jieSuanStageDifferenceQuantity",seq+"_jieSuanPriceDifferenc","CONTEXT"],
            (ctx)=>{
                let {is2022} = ctx.CONTEXT;
                if (!is2022){
                    //结算价差进项税额=单位价差*结算除税系数%*调差工程量
                    return NumberUtil.multiply(ctx[seq+"_jieSuanStageDifferenceQuantity"],ctx[seq+"_jieSuanPriceDifferenc"],NumberUtil.divide(ctx[seq+"_taxRemoval"],100));
                }

            })});
}

//合同不含税市场价合价(一般计税)/合同不含税市场价合价(一般计税)
let getjieSuanPriceMarketTotal=(seq)=>{
    return Organ.create({name:seq+"_jieSuanPriceMarketTotal",description:seq+"合同不含税市场价合价(一般计税)/合同不含税市场价合价(一般计税) ",gene:Gene.from([seq+"_jieSuanTotalNumber","CONTEXT"],
            (ctx)=>{
                let {rcj} = ctx.CONTEXT
                //合同不含税市场价合价 = 合同数量 * 合同市场价
                return NumberUtil.multiply(ctx[seq+"_jieSuanTotalNumber"],rcj.jieSuanMarketPrice);

            })});
}



//合同数量
let getjieSuanTotalNumber=(seq)=>{
    return Organ.create({name:seq+"_jieSuanTotalNumber",description:seq+"合同数量",gene:Gene.from(["CONTEXT"],
            ({CONTEXT})=>{
                let {dependRcjList} = CONTEXT;
                return dependRcjList.reduce((sum, item) => {
                    return sum +item.jieSuanTotalNumber;
                }, 0);
            })});
}


//结算数量
let gettotalNumber=(seq)=>{
    return Organ.create({name:seq+"_totalNumber",description:seq+"结算数量",gene:Gene.from(["CONTEXT"],
            ({CONTEXT})=>{
                let {dependRcjList} = CONTEXT;
                return dependRcjList.reduce((sum, item) => {
                    return sum +item.totalNumber;
                }, 0);
            })});
}



//不含税基期价
let getpriceBaseJournal=(seq)=>{
    return Organ.create({name:seq+"_priceBaseJournal",description:"不含税基期价",gene:Gene.from(["CONTEXT"],
            (cxt)=>{
                let {isStage,dependRcjList,unit,method,constructId, singleId, unitId,rcj,arg} = cxt.CONTEXT;
                let {num,adjustMethod} = arg;
                if (!isStage){
                    return rcj.priceBaseJournal;
                }
                //分期以及分次
                let scopeList = JieSuanRcjStageUtils.rcjAdjustMethod(unit,rcj.kind,rcj.adjustMethod);
                if (ObjectUtils.isNotEmpty(scopeList)){
                    //获取到次对应的期数
                    let sum = 0;
                    //获取到次对应的期数
                    let stagePeriods = JieSuanRcjStageUtils.periods(unit);
                    let periods = [];
                    if (ObjectUtils.isEmpty(arg.num)){
                        periods = [1,stagePeriods];
                    }else {
                        periods = JieSuanRcjStageUtils.generateNaturalNumbers(scopeList[arg.num-1]);
                    }
                    periods.forEach(k =>{
                        sum = sum +  rcj.jieSuanRcjDifferenceTypeList[k-1].priceBaseJournal;
                    });
                    return sum;
                }
            })})
}

//含税基期价
let getpriceBaseJournalTax=(seq)=>{
    return Organ.create({name:seq+"_priceBaseJournalTax",description:"含税基期价",gene:Gene.from(["CONTEXT"],
            (cxt)=>{
                let {isStage,dependRcjList,unit,method,constructId, singleId, unitId,rcj,arg} = cxt.CONTEXT;
                let {num,adjustMethod} = arg;
                //let rcjJieSuanPrice = rcj.jieSuanRcjDifferenceTypeList.find(k =>k.rcjDifferenceType ==adjustMethod);
                if (!isStage){
                    return rcj.priceBaseJournalTax;
                }
                //分期以及分次
                let scopeList = JieSuanRcjStageUtils.rcjAdjustMethod(unit,rcj.kind,rcj.adjustMethod);
                if (ObjectUtils.isNotEmpty(scopeList)){
                    //获取到次对应的期数
                    let sum = 0;

                    //获取到次对应的期数
                    let stagePeriods = JieSuanRcjStageUtils.periods(unit);
                    let periods = [];
                    if (ObjectUtils.isEmpty(arg.num)){
                        periods = [1,stagePeriods];
                    }else {
                        periods = JieSuanRcjStageUtils.generateNaturalNumbers(scopeList[arg.num-1]);
                    }
                    periods.forEach(k =>{
                        sum = sum + rcj.jieSuanRcjDifferenceTypeList[k-1].priceBaseJournalTax;
                        //sum = sum + differencePrice.priceBaseJournalTax;
                    });
                    return sum;
                }
            })})
}

//第n期含税单价
let getpriceMarketTax=(seq)=>{
    return Organ.create({name:seq+"_priceMarketTax",description:"第n期含税单价",gene:Gene.from(["CONTEXT"],
            (cxt)=>{
                let {isStage,dependRcjList,unit,method,constructId, singleId, unitId,rcj,arg} = cxt.CONTEXT;
                let {num,adjustMethod} = arg;
                if (!isStage){
                    return rcj.priceMarketTax;
                }
                let periods = 1;
                if (!ObjectUtils.isEmpty(arg.num)){
                    periods = arg.num;
                }
                return  rcj.jieSuanRcjDifferenceTypeList[periods-1].priceMarketTax;
            })})
}

//第n期不含税单价
let getpriceMarket=(seq)=>{
    return Organ.create({name:seq+"_priceMarket",description:"第n期不含税单价",gene:Gene.from(["CONTEXT"],
            (cxt)=>{
                let {isStage,dependRcjList,unit,method,constructId, singleId, unitId,rcj,arg} = cxt.CONTEXT;
                if (!isStage){
                    return rcj.priceMarket;
                }
                let periods = 1;
                if (!ObjectUtils.isEmpty(arg.num)){
                    periods = arg.num;
                }
                return  rcj.jieSuanRcjDifferenceTypeList[periods-1].priceMarket;
            })})
}


//获取结算除税系数
let gettaxRemoval=(seq)=>{
    return Organ.create({name:seq+"_taxRemoval",description:"结算除税系数",gene:Gene.from(["CONTEXT"],
            (cxt)=>{
                let {isStage,dependRcjList,unit,method,constructId, singleId, unitId,rcj,arg} = cxt.CONTEXT;
                let {num,adjustMethod} = arg;
                //let rcjJieSuanPrice = rcj.jieSuanRcjDifferenceTypeList.find(k =>k.rcjDifferenceType ==adjustMethod);
                if (!isStage){
                    return rcj.taxRemoval;
                }
                let periods = 1;
                if (!ObjectUtils.isEmpty(arg.num)){
                    periods = arg.num;
                }
                return  rcj.jieSuanRcjDifferenceTypeList[periods-1].taxRemoval;
            })})
}


//基期价浮动率
let getBasePriceFloatRate=(seq)=>{
    return Organ.create({name:seq+"_basePriceFloatRate",description:seq+"基期价浮动率",gene:Gene.from(["jieSuanBasePrice","jieSuanMarketPrice"],
            (cxt)=>{
                //基期价浮动率=（基期价-合同/确认单价）/合同/确认单价*100%
                //基期价浮动率=（基期价-合同/确认单价）/合同/确认单价*100%
                return NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(cxt.jieSuanBasePrice,cxt.jieSuanMarketPrice),cxt.jieSuanMarketPrice),100)
            })});
}
//合同合价
let getjieSuanTotal=(seq)=>{
    return Organ.create({name:seq+"_jieSuanTotal",description:seq+"合同合价",gene:Gene.from(["CONTEXT"],
            ({CONTEXT})=>{
                //结算市场价合计=结算数量*【合同/确认单价】
                let {dependRcjList} = CONTEXT;
                return dependRcjList.reduce((sum, item) => {
                    return sum + (item.jieSuanTotal);
                }, 0);
            })});
}
//结算市场价合计
let gettotal=(seq)=>{
    return Organ.create({name:seq+"_total",description:seq+"结算市场价合计",gene:Gene.from(["CONTEXT"],
            ({CONTEXT})=>{
                //结算市场价合计=结算数量*【合同/确认单价】
                let {dependRcjList,arg} = CONTEXT;
                if (arg.levelType == 3 ){
                    return dependRcjList.reduce((sum, item) => {
                        return sum + item.totalNumber*item.marketPrice;
                    }, 0);
                }
                return dependRcjList.reduce((sum, item) => {
                    return Number(sum) + Number(item.total);
                }, 0);
            })});
}
//合同进项税额
let getjxTotal=(seq)=>{
    return Organ.create({name:seq+"_jxTotal",description:seq+"合同进项税额",gene:Gene.from(["CONTEXT"],
            ({CONTEXT})=>{
                //合同进项税额=合同/确认单价*合同除税系数%*合同数量
                let {dependRcjList} = CONTEXT;
                return dependRcjList.reduce((sum, item) => {
                    return sum + (item.jieSuanTotalNumber * item.jieSuanMarketPrice * NumberUtil.divide(item.jieSuanTaxRemoval,100));
                }, 0);
            })});
}
//结算进项税额
let getjieSuanJxTotal=(seq)=>{
    return Organ.create({name:seq+"_jieSuanJxTotal",description:seq+"结算进项税额",gene:Gene.from([seq+"_total","CONTEXT"],
            (ctx)=>{
                let {rcj,is2022,arg} = ctx.CONTEXT;
                //if (arg.levelType == 3 || arg.levelType == 1){
                    if (!is2022){
                        //结算进项税额=结算市场价合价*合同除税系数%
                        return NumberUtil.multiply(ctx[seq+"_total"],NumberUtil.divide(rcj.jieSuanTaxRemoval,100));
                    }
               // }

            })});
}




module.exports = {baseRules,getjieSuanStageDifferenceQuantityRule,getjieSuanBasePrice,getmarketPrice,getSettlementPriceDifferencInputTax,
    getjieSuanPriceMarketTotal,gettotalNumber,getjieSuanTotalNumber,getpriceBaseJournal,getpriceBaseJournalTax,
    getpriceMarketTax,getpriceMarket,getjieSuanPriceSource,gettaxRemoval,getBasePriceFloatRate,getjieSuanTotal,getJieSuanPriceDifferencSum,gettotal,getjxTotal,
    getjieSuanJxTotal
};
