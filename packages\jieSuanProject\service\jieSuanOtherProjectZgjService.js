const OtherProjectCalculationBaseConstant = require("../../../electron/enum/OtherProjectCalculationBaseConstant");
const {OtherProjectZygcZgj} = require("../../../electron/model/OtherProjectZygcZgj");
const {Service} = require("../../../electron/../core");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");

const { getConnection ,getRepository,getManager  } =require('typeorm');
class JieSuanOtherProjectZgjService extends Service{

    //默认数量
    static defaultAmount = 1;
    //默认单价
    static defaultPrice  = 0;

    //默认除税系数
    static defaultTaxRemoval = 3;
    //默认进项合计
    static defaultJxTotal = 0;
    //默认除税单价
    static defaultCsPrice  = 0 ;
    //默认除税合价
    static defaultCsTotal = 0;
    //默认单位
    static defaultUnit  = "项";

    //计算保留小数位
    static decimalPlaces = 2;

    constructor(ctx) {
        super(ctx);
    }

    //专业工程暂估价操作
    otherProjectZygcZgj(arg){

        //操作 类型  1:插入 2:粘贴 3删除 4 修改
        let operateType = arg.operateType;

        switch (operateType) {
            case 1:
                this.addOtherProjectZygcZgj(arg);
                break;
            case 2:
                this.pasteOtherProjectZygcZgj(arg);
                break;
            case 3:
                this.delectOtherProjectZygcZgj(arg);
                break;
            case 4:
                this.updateOtherProjectZygcZgj(arg);
                break;
        }

    }

    //导入初始化
    importInitOtherProjectZygcZgj(){
        let otherProjectZygcZgj = new OtherProjectZygcZgj();
        otherProjectZygcZgj.sequenceNbr = Snowflake.nextId();
        otherProjectZygcZgj.amount = Number(1);
        otherProjectZygcZgj.price = OtherProjectZgjService.defaultPrice;
        otherProjectZygcZgj.taxRemoval = OtherProjectZgjService.defaultTaxRemoval;
        otherProjectZygcZgj.jxTotal = OtherProjectZgjService.defaultJxTotal;
        otherProjectZygcZgj.csPrice = OtherProjectZgjService.defaultCsPrice;
        otherProjectZygcZgj.csTotal = OtherProjectZgjService.defaultCsTotal;
        otherProjectZygcZgj.unit = OtherProjectZgjService.defaultUnit;
        otherProjectZygcZgj.total = NumberUtil.multiplyToString(otherProjectZygcZgj.price,otherProjectZygcZgj.amount,OtherProjectZgjService.decimalPlaces);
        let otherProjectZygcZgjs = [];
        otherProjectZygcZgjs.push(otherProjectZygcZgj);
        return otherProjectZygcZgjs;

    }

    //插入
    addOtherProjectZygcZgj(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectZygcZgjs;

        let number;
        if (!ObjectUtils.isEmpty(targetSequenceNbr)) {
            number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);
        }else {
            number = null;
        }
        let otherProjectZygcZgj = new OtherProjectZygcZgj();
        otherProjectZygcZgj.sequenceNbr = Snowflake.nextId();
        otherProjectZygcZgj.amount = Number(1);
        otherProjectZygcZgj.price = OtherProjectZgjService.defaultPrice;
        otherProjectZygcZgj.taxRemoval = OtherProjectZgjService.defaultTaxRemoval;
        otherProjectZygcZgj.jxTotal = OtherProjectZgjService.defaultJxTotal;
        otherProjectZygcZgj.csPrice = OtherProjectZgjService.defaultCsPrice;
        otherProjectZygcZgj.csTotal = OtherProjectZgjService.defaultCsTotal;
        otherProjectZygcZgj.unit = OtherProjectZgjService.defaultUnit;
        otherProjectZygcZgj.total = NumberUtil.multiplyToString(otherProjectZygcZgj.price,otherProjectZygcZgj.amount,OtherProjectZgjService.decimalPlaces);



        if ( number !== null) {
            list.splice(number+1, 0, otherProjectZygcZgj);
        }else {
            if (ObjectUtils.isEmpty(list)){
                unit.otherProjectZygcZgjs = [];
                unit.otherProjectZygcZgjs.push(otherProjectZygcZgj);

            }else {
                list.push(otherProjectZygcZgj) ;
            }
        }

    }

    //粘贴
    pasteOtherProjectZygcZgj(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;
        let projectZygcZgj = arg.projectZygcZgj;
        let otherProjectZygcZgj = new OtherProjectZygcZgj();
        ConvertUtil.setDstBySrc(projectZygcZgj,otherProjectZygcZgj);
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectZygcZgjs;


        let number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);

        otherProjectZygcZgj.sequenceNbr = Snowflake.nextId();
        list.splice(number +1,0,otherProjectZygcZgj);

        // this.updateOtherProjectZygcZgjTotal(arg);
        this.service.otherProjectService.updateAllOtherProjects(arg);

    }



    //删除
    delectOtherProjectZygcZgj(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectZygcZgjs;
        let number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);
        list.splice(number,1);

        // this.updateOtherProjectZygcZgjTotal(arg);
        this.service.otherProjectService.updateAllOtherProjects(arg);
    }


    //编辑
    updateOtherProjectZygcZgj(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;

        let name = arg.projectZygcZgj.name;
        let price = arg.projectZygcZgj.price;
        let amount = arg.projectZygcZgj.amount;
        let taxRemoval = arg.projectZygcZgj.taxRemoval;
        let dispNo = arg.projectZygcZgj.dispNo;
        let description = arg.projectZygcZgj.description;
        let unitBj = arg.projectZygcZgj.unit;
        let content = arg.projectZygcZgj.content;


        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectZygcZgjs;
        let projectZygcZgjs = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
        if (!ObjectUtils.isEmpty(name)){
            projectZygcZgjs.name = name;
        }

        if (!ObjectUtils.isEmpty(price)){
            projectZygcZgjs.price = price;
        }

        if (!ObjectUtils.isEmpty(amount)){
            projectZygcZgjs.amount =amount;
        }

        if (!ObjectUtils.isEmpty(taxRemoval)){
            projectZygcZgjs.taxRemoval = taxRemoval;
        }

        if (!ObjectUtils.isEmpty(dispNo)){
            projectZygcZgjs.dispNo = dispNo;
        }

        if (!ObjectUtils.isEmpty(description)){
            projectZygcZgjs.description = description;
        }

        if (!ObjectUtils.isEmpty(unitBj)){
            projectZygcZgjs.unit = unitBj;
        }

        if (!ObjectUtils.isEmpty(content)){
            projectZygcZgjs.content = content;
        }
        //计算
        //计算专业工程暂估价 a.【金额】=【单价】*【数量】
        projectZygcZgjs.total = NumberUtil.removeExtraZerosAndDot(NumberUtil.multiplyToString(projectZygcZgjs.price,projectZygcZgjs.amount,OtherProjectZgjService.decimalPlaces));

        //除税系数默认=3%，【进项税合计】=暂列金额*除税系数%；【除税单价】=单价*（1-除税系数%）；【除税合价】=【暂列金额】*（1-除税系数%）
        let multiply = NumberUtil.multiply(projectZygcZgjs.taxRemoval,0.01);
        //进项税合计
        projectZygcZgjs.jxTotal = NumberUtil.multiplyToString(multiply,projectZygcZgjs.total,OtherProjectZgjService.decimalPlaces);
        let subtract = NumberUtil.subtract(1,multiply);
        //除税单价
        projectZygcZgjs.csPrice = NumberUtil.multiplyToString(projectZygcZgjs.price,subtract,OtherProjectZgjService.decimalPlaces);
        //除税合价
        projectZygcZgjs.csTotal = NumberUtil.multiplyToString(projectZygcZgjs.total,subtract,OtherProjectZgjService.decimalPlaces);

        // this.updateOtherProjectZygcZgjTotal(arg);
        this.service.otherProjectService.updateAllOtherProjects(arg);
    }


    //修改汇总表金额
    updateOtherProjectZygcZgjTotal(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectZygcZgjs;

        let total = 0;
        let jxTotal = 0;
        let csTotal = 0;

        if (!ObjectUtils.isEmpty(list)) {
            for (let otherProjectZygcZgj1 of list) {
                total = NumberUtil.add(total, otherProjectZygcZgj1.total);
                jxTotal = NumberUtil.add(jxTotal, otherProjectZygcZgj1.jxTotal);
                csTotal = NumberUtil.add(csTotal, otherProjectZygcZgj1.csTotal);
            }
        }

        let otherProjects = unit.otherProjects;
        if (!ObjectUtils.isEmpty(otherProjects)) {
            let t = otherProjects.find(j => j.calculationBase === OtherProjectCalculationBaseConstant.zygczgj);
            t.total = NumberUtil.multiplyToString(total, t.amount, OtherProjectZgjService.decimalPlaces);
            t.jxTotal = NumberUtil.multiplyToString(jxTotal, t.amount, OtherProjectZgjService.decimalPlaces);
            t.csTotal = NumberUtil.multiplyToString(csTotal, t.amount, OtherProjectZgjService.decimalPlaces);

            let t1 = otherProjects.find(i=>i.extraName === "暂估价");
            t1.total = t.total;
        }
    }
}
JieSuanOtherProjectZgjService.toString = () => '[class OtherProjectZgjService]';
module.exports = JieSuanOtherProjectZgjService;