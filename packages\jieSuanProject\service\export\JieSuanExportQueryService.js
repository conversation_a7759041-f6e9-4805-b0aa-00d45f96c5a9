const { Service } = require('../../../../core');
const { ResponseData } = require('../../../../electron/utils/ResponseData');
const JieSuanExportSheetNameEnum = require('../../enum/JieSuanExportSheetNameEnum');
const TaxCalculationMethodEnum = require('../../../../electron/enum/TaxCalculationMethodEnum');
const { PricingFileFindUtils } = require('../../../../electron/utils/PricingFileFindUtils');
const { ObjectUtils } = require('../../../../electron/utils/ObjectUtils');
const path = require('path');
const UtilsPs = require('../../../../core/ps');
const { JieSuanExcelUtil } = require('../../utils/JieSuanExcelUtil.js');
const { ZhaoBiaoUtil } = require('../../../../electron/utils/ZhaoBiaoUtil.js');
const { ExcelOperateUtil } = require('../../../../electron/utils/ExcelOperateUtil.js');
const { ConvertUtil } = require('../../../../electron/utils/ConvertUtils');
const { NumberUtil } = require('../../../../electron/utils/NumberUtil');
const BranchProjectLevelConstant = require('../../../../electron/enum/BranchProjectLevelConstant');
const JieSuanSingleTypeEnum = require('../../enum/JieSuanSingleTypeEnum');
const ProjectLevelConstant = require('../../../../electron/enum/ProjectLevelConstant');
const {
  app: electronApp,
  dialog, shell, BrowserView, Notification,
  powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const fs = require('fs');
const { ExcelUtil } = require('../../../../electron/utils/ExcelUtil');
const AdmZip = require('adm-zip');
const OtherProjectDayWorkRcjConstant = require('../../../../electron/enum/OtherProjectDayWorkRcjConstant');
const JieSuanRcjDifferenceEnum = require("../../enum/JieSuanRcjDifferenceEnum");
const projectLevelConstant = require("../../../../electron/enum/ProjectLevelConstant");
const JieSuanExcelEnum = require("../../enum/JieSuanExcelEnum");
const {DateTimeUtil} = require("../../../../common/DateTimeUtil");
const {da} = require("date-fns/locale");

class JieSuanExportQueryService extends Service {
  constructor(ctx) {
    super(ctx);
  }

  //展示报表查看的表名列表
  async jieSuanShowExportHeadLine(itemLevel, args) {
    let {constructId, singleId, unitId} = args;
    let result = [];

    //定义一个存放数据值和cell的对象
    function HeadLineList(desc, baoBiaoList) {
      this.desc = desc;//存放大标题
      this.baoBiaoList = baoBiaoList;
    }

    //如果是一般计税方式
    // itemLevel = "single";
    //存放大栏目下的表的表名
    let changYongBaoBiao = JieSuanExportSheetNameEnum.常用报表.filter(function (element) {
      if (element.projectLevel === itemLevel)
        return element;
    });
    let guiFanBaoBiao13 = JieSuanExportSheetNameEnum["13规范报表"].filter(function (element) {
      if (element.projectLevel === itemLevel)
        return element;
    });
    let zhibiao = JieSuanExportSheetNameEnum["指标"].filter(function (element) {
      if (element.projectLevel === itemLevel)
        return element;
    });
    args['levelType'] = 1;
    // 计税方式获取
    let taxCalculation = await this.service.baseFeeFileService.taxCalculation(args);
    let simple = false;
    //简易计税
    if (taxCalculation.taxCalculationMethod === TaxCalculationMethodEnum.SIMPLE.code) {
      simple = true;
    }
    let constructIs22 = PricingFileFindUtils.is22De(constructId)?"22":"12";
    //是合同内还是合同外   单项  单位有
    let htn = false;
    if (itemLevel === 'unit') {
      const unit = PricingFileFindUtils.getUnit(constructId, singleId,unitId);
      htn = unit && typeof unit.originalFlag === 'boolean' ? unit.originalFlag : false;
      let de =  PricingFileFindUtils.is22Unit(PricingFileFindUtils.getUnit(constructId,singleId,unitId))?'22':'12';
      if (de === '22') {
        changYongBaoBiao = changYongBaoBiao.filter(cy => ObjectUtils.isNotEmpty(cy.de) && cy.de.includes("22"));
        guiFanBaoBiao13 = guiFanBaoBiao13.filter(gf => ObjectUtils.isNotEmpty(gf.de) && gf.de.includes("22"));
      } else {
        changYongBaoBiao = changYongBaoBiao.filter(cy => ObjectUtils.isNotEmpty(cy.de) && cy.de.includes("12"))
        guiFanBaoBiao13 = guiFanBaoBiao13.filter(gf => ObjectUtils.isNotEmpty(gf.de) && gf.de.includes("12"));
      }
      //如果是合同内
      if (htn) {
        changYongBaoBiao = changYongBaoBiao.filter(cy => ObjectUtils.isNotEmpty(cy.htFlag) && cy.htFlag === 1);
        guiFanBaoBiao13 = guiFanBaoBiao13.filter(gf => ObjectUtils.isNotEmpty(gf.htFlag) && gf.htFlag === 1);
        if (simple) {
          guiFanBaoBiao13 = guiFanBaoBiao13.filter(gf => ObjectUtils.isEmpty(gf.jsfs));
        }

      } else {
        changYongBaoBiao = changYongBaoBiao.filter(cy => ObjectUtils.isNotEmpty(cy.htFlag) && cy.htFlag === 0);
        guiFanBaoBiao13 = guiFanBaoBiao13.filter(gf => ObjectUtils.isNotEmpty(gf.htFlag) && gf.htFlag === 0);
        if (simple) {
          guiFanBaoBiao13 = guiFanBaoBiao13.filter(gf => ObjectUtils.isEmpty(gf.jsfs));
        }
      }
    }

   else if (itemLevel === 'single') {
      const single = PricingFileFindUtils.getSingleProject(constructId,singleId);
      htn = single && typeof single.originalFlag === 'boolean' ? single.originalFlag : false;
      if (htn){
        //合同内 【单项】
        guiFanBaoBiao13=guiFanBaoBiao13.filter(cy => ObjectUtils.isNotEmpty(cy.htFlag) && cy.htFlag === 1 && cy.de.includes(constructIs22));
        changYongBaoBiao = changYongBaoBiao.filter(cy => ObjectUtils.isNotEmpty(cy.htFlag) && cy.htFlag === 1 && cy.de.includes(constructIs22));
      }else {
        guiFanBaoBiao13=guiFanBaoBiao13.filter(cy => ObjectUtils.isNotEmpty(cy.htFlag) && cy.htFlag === 0 && cy.de.includes(constructIs22));
      }
    }else if (itemLevel === 'project') {
      changYongBaoBiao = changYongBaoBiao.filter(cy => cy.de.includes(constructIs22));
      guiFanBaoBiao13 = guiFanBaoBiao13.filter(cy => cy.de.includes(constructIs22));
      zhibiao = zhibiao.filter(cy => cy.de.includes(constructIs22));
    }
    let headLineList1 = new HeadLineList('常用报表', changYongBaoBiao);
    let headLineList2 = new HeadLineList('13规范报表', guiFanBaoBiao13);
    let headLineList3 = new HeadLineList('指标', zhibiao);
    //是单项 同时是合同外  不要单项的常用报表
    if (itemLevel === 'single' && !htn) {
      //合同外 【单项】
    } else {
      result.push(headLineList1);
    }
    result.push(headLineList2);
    if (itemLevel == "project") {
      result.push(headLineList3);
    }
    return result;
  }

  async exportExcel(params,lanMuName,startPage,totalPage) {
    // let {constructName} = params.headLine;
    // let defaultStoragePath = ProjectDomain.getDefaultStoragePath(constructName);
    const dialogOptions = {
      title: '保存文件', defaultPath: params.headLine, filters: [{name: 'zip', extensions: ['zip']}]
    };
    let result = dialog.showSaveDialogSync(null, dialogOptions);
    //弹出弹框确定路径以后 走下面
    if (result && !result.canceled) {
      let filePath = result;
      console.log(filePath);
      await this.exportExcelZip(params, filePath,lanMuName,startPage,totalPage);
      return true;
      // this.service.shenHeYuSuanProject.systemService.openWindowForProject(constructName,sequenceNbr);
    } else {
      return false;
    }
  }


  async exportExcelZip(params, filePath,lanMuName,startPage,totalPage) {
    let construct = PricingFileFindUtils.getProjectObjById(params.id);
    // lanMuName = "常用报表";
    let taxCalculationMethodPath = "";
    let taxCalculationMethod = construct.projectTaxCalculation.taxCalculationMethod;
    if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
      taxCalculationMethodPath = "简易计税";
    }else {
      taxCalculationMethodPath = "一般计税";
    }
    let is22Str =  PricingFileFindUtils.is22De(params.id)?'22':'12';
    let project = await this.initWorkBook(ProjectLevelConstant.construct,is22Str,lanMuName,taxCalculationMethodPath,null);

    let fileDir = this.getProjectRootPath() + "\\excelTemplate\\jiesuan\\" + params.headLine;
    let workBookList = [];
    let args = {};
    args['constructId'] = params.id;
    args["lanMuName"] = lanMuName;
    await this.parseParams(params, project, null, null, fileDir, args,taxCalculationMethodPath,lanMuName,workBookList);


    for (let i = 0; i < workBookList.length; i++) {
      await this.createDirectory(workBookList[i].fileDir);
      if (ObjectUtils.isNotEmpty(workBookList[i].sheet)) {
        await workBookList[i].sheet.xlsx.writeFile(workBookList[i].filename);
      }
    }
    //对 对应的目录进行压缩 生成zip文件
    // 创建一个新的 Zip 实例
    const zip = new AdmZip();

    // 递归遍历目录及其子目录中的文件和子目录
    function traverseDirectory(dirPath, relativePath = '') {
      const files = fs.readdirSync(dirPath);

      files.forEach(file => {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);

        if (stats.isDirectory()) {
          const fileRelativeNext = path.join(relativePath, file);
          zip.addFile(fileRelativeNext + '/', Buffer.alloc(0), '', 493); // 添加空文件夹
          traverseDirectory(filePath, fileRelativeNext);
        } else {
          zip.addLocalFile(filePath, relativePath);
        }
      });
    }

    if (ObjectUtils.isNotEmpty(workBookList)) {
        traverseDirectory(fileDir, params.headLine);
        // 将 zip 文件保存到指定路径
        zip.writeZip(filePath);

        function deleteDirectory(dirPath) {
          if (fs.existsSync(dirPath)) {
            fs.readdirSync(dirPath).forEach(file => {
              const filePath = path.join(dirPath, file);

              if (fs.lstatSync(filePath).isDirectory()) {
                deleteDirectory(filePath); // 递归删除子目录
              } else {
                fs.unlinkSync(filePath); // 删除文件
              }
            });

            fs.rmdirSync(dirPath); // 删除空目录
            console.log('目录删除成功');
          } else {
            console.log('目录不存在');
          }
        }

        deleteDirectory(fileDir);
    }

  }

  // 创建目录
  async createDirectory(directoryPath) {
    if (!fs.existsSync(directoryPath)) {
      fs.mkdirSync(directoryPath, { recursive: true });
      console.log('目录已创建');
    } else {
      console.log('目录已存在');
    }
  }


  async parseParams(params, project, single, unit, fileDir, args,taxCalculationMethodPath,lanMuName,workBookList) {
    if (args == null) {
      args = {};
    }
    args.lanMuName = lanMuName;
    if (params.levelType == ProjectLevelConstant.construct) {
      args["constructId"] = params.id;
    }else if (params.levelType == ProjectLevelConstant.single) {
      args["singleId"] = params.id;
    }else {
      args["unitId"] = params.id;
    }
    for (let i = 0; i < params.childrenList.length; i++) {
      let param = params.childrenList[i];
      //如果为总工程层级
      if (param.projectLevel != null && param.projectLevel == "project") {

        args["levelType"] = params.levelType;
        if (param.selected) {
          await this.getWorkSheetWithData(project, "project", param.headLine, args);
        } else {
          project.removeWorksheet(param.headLine);
        }
      }
      if (param.projectLevel != null && param.projectLevel == "single") {
        args["levelType"] = params.levelType;
        if (param.selected) {
          await this.getWorkSheetWithData(single, "single", param.headLine, args);
        } else {
          single.removeWorksheet(param.headLine);
        }
      }
      if (param.projectLevel != null && param.projectLevel == "unit") {
        args["levelType"] = params.levelType;
        if (param.selected) {
          await this.getWorkSheetWithData(unit, "unit", param.headLine, args);
        } else {
          unit.removeWorksheet(param.headLine);
        }
      }
    }

    //针对不同的workbook 生成该一层级的excel文件
    let filename = fileDir + "\\" + params.headLine + ".xlsx";

    if (ObjectUtils.isNotEmpty(params.levelType) && params.levelType == ProjectLevelConstant.construct) {
      if (ObjectUtils.isNotEmpty(project)) {
        await this.resetOrderNo(params,project);
      }
      if (ObjectUtils.isNotEmpty(project) && project.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
        workBookList.push({sheet:project,fileDir:fileDir,filename:filename});
      }else if (params.selected){
        workBookList.push({sheet:null,fileDir:fileDir,filename:filename});
      }
    }
    if (ObjectUtils.isNotEmpty(params.levelType) && params.levelType == ProjectLevelConstant.single) {
      if (ObjectUtils.isNotEmpty(single)) {
        await this.resetOrderNo(params,single);
      }
      if (ObjectUtils.isNotEmpty(single) && single.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
        workBookList.push({sheet:single,fileDir:fileDir,filename:filename});
      }else if (params.selected){  //如果存在已勾选的空单项的情况
        workBookList.push({sheet:null,fileDir:fileDir,filename:filename});
      }
    }
    if (ObjectUtils.isNotEmpty(params.levelType) && params.levelType == ProjectLevelConstant.unit) {
      if (ObjectUtils.isNotEmpty(unit)) {
        await this.resetOrderNo(params,unit);
      }
      if (ObjectUtils.isNotEmpty(unit) && unit.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
        workBookList.push({sheet:unit,fileDir:fileDir,filename:filename});
      }else if (params.selected){
        workBookList.push({sheet:null,fileDir:fileDir,filename:filename});
      }
    }
    let filter = params.childrenList.filter(itemParam => itemParam.childrenList!=null);//含有子节点的节点
    if (filter != null) {
      let directory;
      for (let i = 0; i < filter.length; i++) {
        //同时对single  和 unit对象进行初始化
        if (filter[i].levelType ==ProjectLevelConstant.single) {
          let singleProject = PricingFileFindUtils.getSingleProject(args["constructId"],filter[i].id);
          let projectType = "";
          if (ObjectUtils.isNotEmpty(singleProject.type)) {
              projectType = this.findJieSuanSingleTypeEnumByCode(singleProject.type).value;
          }else {
              projectType = "合同内单项工程";
          }

          let is22Str =  PricingFileFindUtils.is22De(args["constructId"])?'22':'12';
          single = await this.initWorkBook(ProjectLevelConstant.single,is22Str,lanMuName,taxCalculationMethodPath,projectType);
        }
        if (filter[i].levelType == ProjectLevelConstant.unit) {
          let singleProject = PricingFileFindUtils.getSingleProject(args["constructId"],args["singleId"]);
          let projectType = "";
          if (ObjectUtils.isNotEmpty(singleProject.type)) {
              projectType = this.findJieSuanSingleTypeEnumByCode(singleProject.type).value;
          }else {
              projectType = "合同内单项工程";
          }
          let is22Str =  PricingFileFindUtils.is22UnitById(args["constructId"],args["singleId"],filter[i].id)?'22':'12';
          unit = await this.initWorkBook(ProjectLevelConstant.unit,is22Str,lanMuName,taxCalculationMethodPath,projectType);
        }
        directory = fileDir +"\\"+filter[i].headLine;
        await this.parseParams(filter[i],null,single,unit,directory,args,taxCalculationMethodPath,lanMuName,workBookList);
      }
    }
  }


  async resetOrderNo(params,workbook) {
    let selectedSheet = params.childrenList.filter(item => item.selected).map(item => item.headLine);
    await this.removeRedundantWorkSheets(selectedSheet,workbook);
    let orderNo = 0;
    for (let i = 0; i < params.childrenList.length; i++) {
      let element = params.childrenList[i];
      if (element.selected) {
        orderNo++;
        let filterWorkSheet = workbook._worksheets.filter(item => item.name==element.headLine);
        if (ObjectUtils.isNotEmpty(filterWorkSheet)) {
          filterWorkSheet[0].orderNo = orderNo;
        }
      }
    }
  }

  async removeRedundantWorkSheets(selectedSheet,workbook) {
    for (let i = 0; i < workbook._worksheets.length; i++) {
      if (ObjectUtils.isNotEmpty(workbook._worksheets[i]) && !selectedSheet.includes(workbook._worksheets[i].name)) {
        await workbook.removeWorksheet(workbook._worksheets[i].name);
        await this.removeRedundantWorkSheets(selectedSheet,workbook);
      }
    }

  }


  async initWorkBook(projectLevel,is22Str,lanMuName,taxCalculateMethod,fileType) {
    let loadDir = this.getProjectRootPath() + "\\excelTemplate\\jiesuan\\"+is22Str+"\\"+taxCalculateMethod+"\\"+lanMuName;
    let loadPath = "";
    if (projectLevel == ProjectLevelConstant.construct) {
      loadPath = loadDir + "\\工程项目.xlsx";
    } else if (projectLevel == ProjectLevelConstant.single) {
      loadPath = loadDir +"\\"+fileType+"\\单项工程.xlsx";
      if (lanMuName == "常用报表" && fileType!="合同内单项工程") { //常用报表的合同外没有单项工程的报表
        return null;
      }
    } else if (projectLevel == ProjectLevelConstant.unit) {
      loadPath = loadDir +"\\"+fileType+"\\单位工程"+"\\单位工程.xlsx";
    }
    if (ObjectUtils.isEmpty(loadPath)) return null;
    //加载workbook
    let workbook = await JieSuanExcelUtil.readToWorkBook(loadPath);
    return workbook;
  }



  async getWorkSheetWithData(workbook, projectType, sheetName, args) {
    let worksheet = workbook.getWorksheet(sheetName);
    args["workbook"] = workbook;
    let constructObj = {
      "constructId":args.constructId,
      "singleId":args.singleId,
      "unitId":args.unitId
    }
    args["constructObj"] = constructObj;
    try{
      worksheet = await this.switchWorkSheet(projectType, args["lanMuName"],worksheet, args);
      if (ObjectUtils.isNotEmpty(args["fileType"]) && args["fileType"]=="pdf") {
          await this.adjustPdf(worksheet);
      }
    } catch (e) {
        console.log("报表填充数据异常" + sheetName);
    }
    return worksheet;
  }

  async adjustPdf(workSheet) {
    function roundUpToFiveDecimalPlaces(num) {
      // 乘以 10 的五次方，将小数部分移到整数部分
      const multiplier = 1000;

      // 向上舍入
      const roundedNum = Math.floor(num * multiplier) / multiplier;

      return roundedNum;
    }
    let rowBreakBeforeIndex = 0;
    let traverse = false;
    let contentHeight = 0;
    if (ObjectUtils.isNotEmpty(workSheet.pageSetup) && ObjectUtils.isNotEmpty(workSheet.pageSetup.orientation) && workSheet.pageSetup.orientation == "portrait") {
      contentHeight = JieSuanExcelEnum.A4HeightPDF-(JieSuanExcelEnum.A4Top/(JieSuanExcelEnum.A4Height/JieSuanExcelEnum.A4HeightPDF))-(JieSuanExcelEnum.A4Bottom/(JieSuanExcelEnum.A4Height/JieSuanExcelEnum.A4HeightPDF));
    }else {  //如果为横版
      contentHeight = JieSuanExcelEnum.A4HeightHorizontalPDF-(JieSuanExcelEnum.A4TopHorizontal/(JieSuanExcelEnum.A4HeightHorizontal/JieSuanExcelEnum.A4HeightHorizontalPDF))-(JieSuanExcelEnum.A4BottomHorizontal/(JieSuanExcelEnum.A4HeightHorizontal/JieSuanExcelEnum.A4HeightHorizontalPDF));
    }

    for (let n = 0; n < workSheet.rowBreaks.length; n++) {
      traverse = true;
      let rowBreakIndex = workSheet.rowBreaks[n].id-1;
      let slice = workSheet._rows.slice(rowBreakBeforeIndex,rowBreakIndex+1);
      let rowTotalHeight = 0;
      for (let i = 0; i < slice.length; i++) {
        let rowCur = slice[i];
        rowTotalHeight+= rowCur.height;
      }
      rowBreakBeforeIndex = rowBreakIndex+1;
      let number = contentHeight-rowTotalHeight;
      let totalHeight = 0;
      for (let i = 0; i < slice.length-1; i++) {
        slice[i].height = await ExcelOperateUtil.rowHeightToFixed((slice[i].height/rowTotalHeight)*number+slice[i].height);
        totalHeight+= slice[i].height;
      }
      slice[slice.length-1].height = roundUpToFiveDecimalPlaces(contentHeight-totalHeight);
    }
    if (traverse) {
      let index = workSheet.rowBreaks[workSheet.rowBreaks.length-1].id-1;
      let splice = workSheet._rows.slice(index+1,workSheet._rows.length);
      let rowTotalHeight = 0;
      for (let i = 0; i < splice.length; i++) {
        let rowCur = splice[i];
        rowTotalHeight +=rowCur.height;
      }
      let number = contentHeight-rowTotalHeight;
      let totalHeight = 0;
      for (let i = 0; i < splice.length-1; i++) {
        let rowCur = splice[i];
        rowCur.height = await ExcelOperateUtil.rowHeightToFixed((rowCur.height/rowTotalHeight)*number+rowCur.height);
        totalHeight+= splice[i].height;
      }
      splice[splice.length-1].height = roundUpToFiveDecimalPlaces(contentHeight-totalHeight);
    }
    if (workSheet.rowBreaks.length == 0) {  //如果只有一页
      let sliceRows = workSheet._rows.slice(0,workSheet._rows.length);
      let rowTotalHeight = 0;
      for (let i = 0; i < sliceRows.length; i++) {
        let rowCur = sliceRows[i];
        rowTotalHeight +=rowCur.height;
      }
      let number = contentHeight-rowTotalHeight;
      let totalHeight = 0;
      for (let i = 0; i < sliceRows.length-1; i++) {
        let rowCur = sliceRows[i];
        rowCur.height = await ExcelOperateUtil.rowHeightToFixed((rowCur.height/rowTotalHeight)*number+rowCur.height);
        totalHeight+= sliceRows[i].height;
      }
      sliceRows[sliceRows.length-1].height = roundUpToFiveDecimalPlaces(contentHeight-totalHeight);
    }


    //针对列宽
    let columnWidthTotal = 0;
    for (let i = 0; i < workSheet._columns.length; i++) {
      let columnWidth = workSheet._columns[i].width;
      columnWidthTotal += columnWidth;
    }
    let differ = 0;
    if (ObjectUtils.isNotEmpty(workSheet.pageSetup) && ObjectUtils.isNotEmpty(workSheet.pageSetup.orientation) && workSheet.pageSetup.orientation == "portrait") {
      differ = (JieSuanExcelEnum.A4WidthPDF-JieSuanExcelEnum.A4Left/(JieSuanExcelEnum.A4Width/JieSuanExcelEnum.A4WidthPDF)-JieSuanExcelEnum.A4Right/(JieSuanExcelEnum.A4Width/JieSuanExcelEnum.A4WidthPDF));
    }else {
      differ = (JieSuanExcelEnum.A4WidthHorizontalPDF-JieSuanExcelEnum.A4LeftHorizontal/(JieSuanExcelEnum.A4WidthHorizontal/JieSuanExcelEnum.A4WidthHorizontalPDF)-JieSuanExcelEnum.A4RightHorizontal/(JieSuanExcelEnum.A4WidthHorizontal/JieSuanExcelEnum.A4WidthHorizontalPDF));
    }

    let columnWidthTotal2 = 0;
    for (let i = 0; i < workSheet._columns.length-1; i++) {
      workSheet._columns[i].width = Number(((workSheet._columns[i].width/columnWidthTotal)*differ).toFixed(0));
      columnWidthTotal2+= workSheet._columns[i].width;
    }
    workSheet._columns[workSheet._columns.length-1].width = await ExcelOperateUtil.colWidthToFixed(differ-columnWidthTotal2);
  }


  async jieSuanShowSheetStyle(itemLevel, lanMuName, sheetName, args) {
    let { constructId, singleId, unitId } = args;
    args.levelType = 1;
    let guiFanBiao = this.getProjectRootPath() + '\\excelTemplate\\jiesuan';
    let changyongBiao = this.getProjectRootPath() + '\\excelTemplate\\jiesuan';
    let zhibiao = this.getProjectRootPath() + '\\excelTemplate\\jiesuan';
    //判断定额，12或者22,添加相应路径
    let de;
    if (itemLevel === 'unit') {
      de =  PricingFileFindUtils.is22Unit(PricingFileFindUtils.getUnit(constructId,singleId,unitId))?'\\22':'\\12';
    }else {
      de =  PricingFileFindUtils.is22De(constructId)?'\\22':'\\12';
    }
    guiFanBiao = guiFanBiao + de;
    changyongBiao = changyongBiao + de;
    zhibiao = zhibiao + de;
    //计税方式   simple表示简易计税
    let simple = false;
    // 计税方式获取
    let taxCalculation = await this.service.baseFeeFileService.taxCalculation(args);
    //简易计税
    if (taxCalculation.taxCalculationMethod === TaxCalculationMethodEnum.SIMPLE.code) {
      simple = true;
      guiFanBiao = guiFanBiao + '\\简易计税';
      changyongBiao = changyongBiao + '\\简易计税';
      zhibiao = zhibiao + '\\简易计税';
    } else {
      guiFanBiao = guiFanBiao + '\\一般计税';
      changyongBiao = changyongBiao + '\\一般计税';
      zhibiao = zhibiao + '\\简易计税';
    }


    //合同内外
    let htn = false;
    //单项类型
    let singleType;
    //如果是工程项目  不需要考虑合同内外
    if (itemLevel !== 'project') {
      let singleProject = PricingFileFindUtils.getSingleProject(constructId, singleId);
      if (ObjectUtils.isNotEmpty(singleProject.originalFlag) && singleProject.originalFlag === true) {
        htn = true;
        singleType = '\\合同内单项工程';

      } else {
        //如果是合同外，并且是单项、单位  需要知道该单项的类型（签证索赔）
        let singleProject = PricingFileFindUtils.getSingleProject(constructId, singleId);
        singleType = '\\' + this.findJieSuanSingleTypeEnumByCode(singleProject.type).value;
      }
    }
    //组装Excel加载路径
    if (itemLevel === 'project') {
      guiFanBiao = guiFanBiao + '\\13规范报表\\工程项目.xlsx';
      changyongBiao = changyongBiao + '\\常用报表\\工程项目.xlsx';
      zhibiao = zhibiao + "\\指标\\指标.xlsx";
    } else if (itemLevel === 'single') {
      guiFanBiao = guiFanBiao + '\\13规范报表' + singleType + '\\单项工程.xlsx';
      changyongBiao = changyongBiao + '\\常用报表' + singleType + '\\单项工程.xlsx';
    } else if (itemLevel === 'unit') {
      guiFanBiao = guiFanBiao + '\\13规范报表' + singleType + '\\单位工程\\单位工程.xlsx';
      changyongBiao = changyongBiao + '\\常用报表' + singleType + '\\单位工程\\单位工程.xlsx';
    }
    let loadPath = '';
    if (lanMuName === '13规范报表') {
      //TODO 与产品沟通  合同内的 1-4表样式是一样的   但是在处理13规范报表的时候样式一直不正确， 临时使用合同内的1-4表
        loadPath = guiFanBiao;
    }
    if (lanMuName === '常用报表') {
      loadPath = changyongBiao;
    }
    if (lanMuName === "指标") {
      loadPath = zhibiao;
    }
    let workbook = await JieSuanExcelUtil.readToWorkBook(loadPath);
    args['workbook'] = workbook;
    //simple =true 简易计税
    args['jsfs'] = simple;
    //合同内
    args['htn'] = htn;
    try {
      await this.switchWorkSheet(itemLevel, lanMuName, workbook.getWorksheet(sheetName), args);
    } catch (e) {
      console.log(e.stack);
      console.log('报表填充数据异常');
    }
    let result;
    try {
      // if (sheetName == '表1-4 总价措施项目清单与计价表' && loadPath.includes('13规范报表')) {
      //   if (htn) {
      //     sheetName = sheetName + '14htn';
      //   } else {
      //     sheetName = sheetName + '14htw';
      //   }
      // }
      result = await JieSuanExcelUtil.findCellStyleList(workbook.getWorksheet(sheetName));
    } catch (e) {
      console.error(e.stack);
      console.log('报表填充数据异常');
    }
    return result;
  }

  async queryLanMuData(constructId,lanMuName) {
      let project = [];
      // lanMuName = "13规范报表";
      let construct = PricingFileFindUtils.getProjectObjById(constructId);
      let is22Str =  PricingFileFindUtils.is22De(constructId)?'22':'12';

      await this.traverseGetHeadLineAndLeaf(JieSuanExportSheetNameEnum[lanMuName], "project", is22Str,1,project);



      let result = {};
      let newProject = await ConvertUtil.deepCopy(project);
      result["headLine"] = construct.constructName;
      result["childrenList"] = newProject;
      result["id"] = construct.sequenceNbr;
      result["levelType"] = construct.levelType;
      if (lanMuName!="指标" && ObjectUtils.isNotEmpty(construct.singleProjects)) {
          for (let i = 0; i < construct.singleProjects.length; i++) {
            await this.traverseSingleForExport(construct.sequenceNbr,construct.singleProjects[i],newProject,lanMuName);
          }
      }

      //对result进行递归遍历  增加唯一序号
      let object = {};
      object['sequenceNbr'] = 1;
      await this.addOrderNum(result, object);

      return result;
  }

  async traverseSingleForExport(constructId,singleProject,result,lanMuName) {


      if (singleProject.type == ProjectLevelConstant.unit) {    //如果是工程项目下直接新建了单位工程
        // let unitObject = {};
        // unitObject["headLine"] = singleProject.name;
        // unitObject["childrenList"] = await this.deepCopy(unitReport);
        // unitObject["levelType"] = singleProject.type;
        // unitObject["id"] = singleProject.sequenceNbr;
        // result.push(unitObject);
        return;
      }
      let is22Str =  PricingFileFindUtils.is22De(constructId)?'22':'12';
      let htFlag = false;
      if (ObjectUtils.isNotEmpty(singleProject.originalFlag) && singleProject.originalFlag == true) {
          htFlag = true;
      }
      let singleReport = [];
      await this.traverseGetHeadLineAndLeaf(JieSuanExportSheetNameEnum[lanMuName], "single", is22Str,htFlag,singleReport);
      singleReport = await ConvertUtil.deepCopy(singleReport);
      let singleObject = {};
      singleObject["headLine"] = singleProject.projectName;
      singleObject["childrenList"] = singleReport;
      singleObject["levelType"] = singleProject.levelType;
      singleObject["id"] = singleProject.sequenceNbr;
      result.push(singleObject);

      if (!ObjectUtils.isEmpty(singleProject.unitProjects) && singleProject.unitProjects[0].levelType==ProjectLevelConstant.unit) {  //如果是含有单位的单项
        for (let i = 0; i < singleProject.unitProjects.length; i++) {
          let unit = singleProject.unitProjects[i];
          let is22Str = PricingFileFindUtils.is22Unit(PricingFileFindUtils.getUnit(constructId,singleProject.sequenceNbr,unit.sequenceNbr))?'22':'12';
          let htFlag = ObjectUtils.isNotEmpty(unit.originalFlag) && typeof unit.originalFlag === 'boolean' && unit.originalFlag ? 1 : 0;
          let unitReport = [];
          await this.traverseGetHeadLineAndLeaf(JieSuanExportSheetNameEnum[lanMuName], "unit", is22Str,htFlag,unitReport);

          let unitObject = {};
          unitObject["headLine"] = unit.upName;
          unitObject["childrenList"] = await ConvertUtil.deepCopy(unitReport);
          unitObject["levelType"] = unit.levelType;
          unitObject["id"] = unit.sequenceNbr;
          singleReport.push(unitObject);
        }
      }

      if (!ObjectUtils.isEmpty(singleProject.subSingleProjects) && singleProject.subSingleProjects[0].levelType==ProjectLevelConstant.single) {     //如果当前单项是子单项
        for (let i = 0; i < singleProject.subSingleProjects.length; i++) {
          await this.traverseSingleForExport(constructId,singleProject.subSingleProjects[i],singleReport,lanMuName);
        }
      }
  }

  //拿到list中 没有children的元素
  async traverseGetHeadLineAndLeaf(headLineList, levelType,is22Str,htFlag,list) {
    for (let i = 0; i < headLineList.length; i++) {
      let element = headLineList[i];
      if (ObjectUtils.isNotEmpty(is22Str) && element.de.includes(is22Str)
          && element.projectLevel == levelType && element.htFlag==htFlag) {
        element['selected'] = false;
        list.push(element);
      }
    }
    return list;
  }

  async addOrderNum(result, object) {
    if (result['sequenceNbr'] == null) {
      result['sequenceNbr'] = object.sequenceNbr++;
    }
    if (result.childrenList != null) {
      for (let i = 0; i < result.childrenList.length; i++) {
        await this.addOrderNum(result.childrenList[i], object);
      }
    }
  }

  async switchWorkSheet(projectType, lanMuName, worksheet, args) {
    let { constructId, unitId, singleId, workbook, jsfs, htn } = args;
    args.lanMuName = lanMuName;

    if (projectType == "project") {
        args['levelType'] = projectLevelConstant.construct;
    }else if (projectType == "single") {
        args['levelType'] = projectLevelConstant.single;
    }else if (projectType == "unit"){
        args['levelType'] = projectLevelConstant.unit;
    }
    //判断定额，12或者22,添加相应路径
    let de;
    if (projectType === 'unit') {
        de =  PricingFileFindUtils.is22Unit(PricingFileFindUtils.getUnit(constructId,singleId,unitId))?'\\22':'\\12';
    }else {
        de =  PricingFileFindUtils.is22De(constructId)?'\\22':'\\12';
    }
    args["deType"] = de;
    // 计税方式获取
    let taxCalculation = await this.service.baseFeeFileService.taxCalculation(args);
    let simple = false;
    //简易计税
    if (taxCalculation.taxCalculationMethod === TaxCalculationMethodEnum.SIMPLE.code) {
      simple = true;
    }
    args["simple"] = simple;
    let unit = {};
    if (constructId != null && singleId != null && unitId != null) {
      unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    }
    await JieSuanExcelUtil.copyTemplateSheet(worksheet);

    let headArgsBot = {};
    headArgsBot['headStartNum'] = 1;
    headArgsBot['headEndNum'] = 4;
    headArgsBot['titlePage'] = false;
    let rcjBaoBiao  = {};
    rcjBaoBiao['headStartNum'] = 1;
    rcjBaoBiao['headEndNum'] = 3;
    rcjBaoBiao['titlePage'] = false;
    if (lanMuName == '常用报表') {
      if (projectType == 'project') {
        switch (worksheet.name) {

          case '表1-1 建设项目费用汇总表'://横   单项汇总
            let array = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectsCostSummary(args);
            // for (let i = 0; i < 100; i++) {
            //   let object = {};
            //   object["projectName"] = i+"";
            //   array.push(object);
            // }

            await this.writeDataToUnitFbfx(array, worksheet,null,1);
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,headArgsBot,args);
            args["service"] = this.service;
            async function fillTotal(data,args) {
              let map = new Map();
              let totalJck = 0;
              let totalZlje = 0;
              let totalSafeFee = 0;
              let totalGfee = 0;
              let totalJsjc = 0;
              let totalZgj = 0;

              for (let i = 0; i < data.length; i++) {
                let bolResult = await args["service"].jieSuanProject.jieSuanExportQueryOtherService.judgeSingleProjectIsSub(data[i].sequenceNbr, constructId);
                if (bolResult) {
                    continue;
                }
                totalJck = NumberUtil.add(totalJck, data[i].jck);
                totalZlje = NumberUtil.add(totalZlje, data[i].qtxmzlje);
                totalSafeFee = NumberUtil.add(totalSafeFee, data[i].safeFee);
                totalGfee = NumberUtil.add(totalGfee, data[i].gfee);
                totalJsjc = NumberUtil.add(totalJsjc, data[i].jsjc);
                totalZgj = NumberUtil.add(totalZgj, data[i].qtxmzygczgj);
              }
              map.set(3, totalJck);
              map.set(4, totalZlje);
              map.set(5, totalZgj);
              map.set(6, totalSafeFee);
              map.set(7, totalGfee);
              map.set(9, totalJsjc);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,await fillTotal(array,args),"合计");
            break;

          case '表1-2 建设项目费用汇总表(沧州)':
            let array1_2 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectsCostSummary1_2(args);
            // for (let i = 0; i < 100; i++) {
            //   let object = {};
            //   object["projectName"] = i+"";
            //   array1_2.push(object);
            // }
            await this.writeDataToUnitFbfx(array1_2, worksheet,null,1);
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,headArgsBot,args);
            args["service"] = this.service;
            async function fill1_2Total(data,args) {
              let map = new Map();
              let totalJck = 0;
              let totalZlje = 0;
              let totalSafeFee = 0;
              let totalGfee = 0;
              let totalJsjc = 0;
              let totalZgj = 0;

              for (let i = 0; i < data.length; i++) {
                let bolResult = await args["service"].jieSuanProject.jieSuanExportQueryOtherService.judgeSingleProjectIsSub(data[i].sequenceNbr, constructId);
                if (bolResult) {
                  continue;
                }
                totalJck = NumberUtil.add(totalJck, data[i].jck);
                totalZlje = NumberUtil.add(totalZlje, data[i].qtxmzlje);
                totalSafeFee = NumberUtil.add(totalSafeFee, data[i].safeFee);
                totalGfee = NumberUtil.add(totalGfee, data[i].gfee);
                totalJsjc = NumberUtil.add(totalJsjc, data[i].jsjc);
                totalZgj = NumberUtil.add(totalZgj, data[i].qtxmzygczgj);
              }
              map.set(3, totalJck);
              map.set(4, totalZlje);
              map.set(5, totalZgj);
              map.set(6, totalSafeFee);
              map.set(7, totalGfee);
              map.set(9, totalJsjc);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,await fill1_2Total(array1_2,args),"合计")
            break;
          case '表1-3 工程项目竣工结算汇总表':
            let array1_3 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectsCostSummary1_3(args);
            // for (let i = 0; i < 100; i++) {
            //   let object = {};
            //   object["projectName"] = i+"";
            //   array1_3.push(object);
            // }
            //删掉最后一行无用行
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.writeDataToUnitFbfx(array1_3, worksheet,2,1);
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,rcjBaoBiao,args);
            args["service"] = this.service;
            async function fill1_3Total(data,args) {
              let map = new Map();
              let totalJieSuan = 0;
              let totalHeTong = 0;
              for (let i = 0; i < data.length; i++) {
                let bolResult = await args["service"].jieSuanProject.jieSuanExportQueryOtherService.judgeSingleProjectIsSub(data[i].sequenceNbr, constructId);
                if (bolResult) {
                  continue;
                }
                totalJieSuan = NumberUtil.add(totalJieSuan, data[i].jck);
                totalHeTong = NumberUtil.add(totalHeTong, data[i].total);
              }
              map.set(3, totalHeTong);
              map.set(4, totalJieSuan);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,await fill1_3Total(array1_3,args),"合计");
            break;
          case '表1-4 工程项目竣工结算汇总表(沧州)':
            let array1_4 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectsCostSummary1_4(args);
            //删掉最后一行无用行
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.writeDataToUnitFbfx(array1_4, worksheet,2,1);
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,rcjBaoBiao,args);
            args["service"] = this.service;
            async function fill1_4Total(data,args) {
              let map = new Map();
              let totalJieSuan = 0;
              let totalHeTong = 0;
              for (let i = 0; i < data.length; i++) {
                let bolResult = await args["service"].jieSuanProject.jieSuanExportQueryOtherService.judgeSingleProjectIsSub(data[i].sequenceNbr, constructId);
                if (bolResult) {
                  continue;
                }
                totalJieSuan = NumberUtil.add(totalJieSuan, data[i].jck);
                totalHeTong = NumberUtil.add(totalHeTong, data[i].total);
              }
              map.set(3, totalHeTong);
              map.set(4, totalJieSuan);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,await fill1_4Total(array1_4,args),"合计")
            break;
        }
      }
      if (projectType == 'single') {
        switch (worksheet.name) {
          case '表1-1 单项工程竣工结算汇总表':
            let array1_1 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectSingle1_1(args);
            //删掉最后一行无用行
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.writeDataToUnitFbfx(array1_1, worksheet,2,1,args);
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,rcjBaoBiao,args);
            function fill1_1Total(data) {
              let map = new Map();
              let totalJieSuan = 0;
              let totalHeTong = 0;
              for (let i = 0; i < data.length; i++) {
                  totalJieSuan = NumberUtil.add(totalJieSuan, data[i].jck);
                  totalHeTong = NumberUtil.add(totalHeTong, data[i].gczj_ys);
              }
              map.set(3,totalHeTong);
              map.set(5,totalJieSuan);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fill1_1Total(array1_1),"合计")
        }
      }
      if (projectType == 'unit') {
        switch (worksheet.name) {
          case '表1-1 单位工程竣工结算汇总表'://无合计
            let array1_1 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectUnit1_1(args);
            //删掉最后一行无用行
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.writeDataToUnitFbfx(array1_1, worksheet,2,1);
            args["reserveNumForDeleteBlankRows"] = 1;
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,rcjBaoBiao,args);
            break;
            //单位工程层级
          case '表1-2 分部分项合同清单工程量及结算工程量对比表':
            await this.getUnitProjectFbfxHtQdQuantityDbBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
            //单位工程层级
          case '表1-3 分部分项工程和单价措施项目清单与计价表':
            await this.get1_3UnitProjectFbfxDjcsQdJjBiao(constructId, singleId, unitId, worksheet);
            let cell = JieSuanExcelUtil.findValueCell(worksheet.originSheet,"本页小计");
            args["copyRowsList"] = [cell.cell.row,worksheet.originSheet._rows.length-1];
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 3;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot,args);
            break;
          case '表1-4 总价措施项目清单与计价表'://常用报表的合同内
            //删掉最后一行无用行
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.getUnitProjectZjcsHtQdJjBiao(constructId, singleId, unitId, worksheet,args);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-2,worksheet.originSheet._rows.length-1];
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 3;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
          case '表1-5 其他项目清单与计价汇总表':
            //删掉最后一行无用行
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.getUnitOtherProjectBiao(constructId, singleId, unitId, worksheet);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
          case '表1-6 材料暂估单价及调整表':
            //删掉最后一行无用行
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.getUnitOtherProjectClzgjBiao(constructId, singleId, unitId, worksheet,args);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot,args);
            break;
          case '表1-7 计日工表':
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.getUnitOtherProjectDayWorkBiao(constructId, singleId, unitId, worksheet);
            // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test.xlsx");
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
          case '表1-8 单位工程人材机汇总表':

            let cgrcj = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitCgRcjHzb(args);
            await this.writeDataToUnitFbfx(cgrcj, worksheet,2,1);
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            function fill1_8Total(data) {
              let map = new Map();
              let totalJieSuan = 0;
              let totalHeTong = 0;
              let totalDifference = 0;
              for (let i = 0; i < data.length; i++) {
                totalJieSuan = NumberUtil.add(totalJieSuan,data[i].total);
                totalHeTong = NumberUtil.add(totalHeTong,data[i].jieSuanPriceMarketTotal);
                totalDifference = NumberUtil.add(totalDifference,data[i].jieSuanPriceDifferencSum);
              }
              map.set(7,totalHeTong);
              map.set(9,totalJieSuan);
              map.set(11,totalDifference);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fill1_8Total(cgrcj),"合计")
            break;

          case '表1-9 工程议价材料表':
            let gcyjclb = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitCgGcclyjb(args);
            await this.writeDataToUnitFbfx(gcyjclb, worksheet,2,1);
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            function fill1_9Total(data) {
              let map = new Map();
              let totalDifference = 0;
              for (let i = 0; i < data.length; i++) {
                totalDifference = NumberUtil.add(totalDifference,data[i].jieSuanPriceDifferencSum);
              }
              map.set(12,totalDifference);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fill1_9Total(gcyjclb),"价差合计")

            break;
          case '表1-10 人材机调整明细表-1':
            let rcjtzmxb1 = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitRcjtzmx1(args);
            await this.writeDataToUnitFbfx(rcjtzmxb1, worksheet,2,1);
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            function fill1_10Total(data) {
              let map = new Map();
              let totalDifference = 0;//价差合计
              for (let i = 0; i < data.length; i++) {
                totalDifference = NumberUtil.add(totalDifference,data[i].jieSuanPriceDifferencSum);
              }
              map.set(8,totalDifference);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fill1_10Total(rcjtzmxb1),"合计")
            break;
          case '表1-11 人材机调整明细表-2':
            let rcjtzmxb2 = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitRcjtzmx2(args);
            await this.writeDataToUnitFbfx(rcjtzmxb2, worksheet,2,1);
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            function fill1_11Total(data) {
              let map = new Map();
              let totalDifference = 0;
              for (let i = 0; i < data.length; i++) {
                totalDifference = NumberUtil.add(totalDifference,data[i].jieSuanPriceDifferencSum);
              }
              map.set(12,totalDifference);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fill1_11Total(rcjtzmxb2),"合计")
            break;
          case '表1-12 主材汇总表':
            let zchzb = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitZchzb(args);
            await this.writeDataToUnitFbfx(zchzb, worksheet,2,1);
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            function fill1_12Total(data) {
              let map = new Map();
              let totalDifference = 0;
              let totalJieSuan = 0;
              for (let i = 0; i < data.length; i++) {
                totalJieSuan = NumberUtil.add(totalJieSuan,data[i].total);
                totalDifference = NumberUtil.add(totalDifference,data[i].jieSuanPriceDifferencSum);
              }
              map.set(10,totalJieSuan);
              map.set(12,totalDifference);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fill1_12Total(zchzb),"合计")
            break;
          case '表1-13 甲方供应材料表':
            let jfgyclb = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitJgclb(args);
            await this.writeDataToUnitFbfx(jfgyclb, worksheet,2,1);
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            function fill1_13Total(data) {
              let map = new Map();
              let totalJieSuan = 0;
              let totalBaoGuanFee = 0;
              let totalAfterKouChu = 0;
              for (let i = 0; i < data.length; i++) {
                let datum = data[i];
                let baoGuanFee = !ObjectUtils.isEmpty(datum.jieSuanAdminRate)?NumberUtil.numberScale2(NumberUtil.multiply(
                    NumberUtil.multiply(datum.total,datum.jieSuanAdminRate),0.01)):0;
                totalJieSuan = NumberUtil.add(totalJieSuan,datum.total);
                totalBaoGuanFee = NumberUtil.add(totalBaoGuanFee,baoGuanFee);
                totalAfterKouChu = NumberUtil.add(totalAfterKouChu,NumberUtil.subtract(datum.total,baoGuanFee));
              }
              map.set(9,totalJieSuan);
              map.set(11,totalBaoGuanFee);
              map.set(13,totalAfterKouChu);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fill1_13Total(jfgyclb),"合计")
            break;

          case '表1-14 甲供材料汇总表':
            let jgclhzb = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitJgclhzb(args);
            await this.writeDataToUnitFbfx(jgclhzb, worksheet,2,1);
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            function fill1_14Total(data) {
              let map = new Map();
              let totalJieSuan = 0;
              for (let i = 0; i < data.length; i++) {
                totalJieSuan = NumberUtil.add(totalJieSuan,data[i].total);
              }
              map.set(11,totalJieSuan);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fill1_14Total(jgclhzb),"合计")

            break;
            //合同外
          case '表1-1 单位工程人材机汇总表':
            let rcjhzbhtw = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitRcjhzbhtw(args);
            await this.writeDataToUnitFbfx(rcjhzbhtw, worksheet,2,1);
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            function fill1_1Total(data) {
              let map = new Map();
              let total = 0;
              for (let i = 0; i < data.length; i++) {
                total = NumberUtil.add(total,data[i].total);
              }
              map.set(8,total);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fill1_1Total(rcjhzbhtw),"合计")
            break;
          case '表1-2 单位工程人材机价差汇总表':
            let rcjjchzbhtw = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitRcjjchzbhtw(args);
            await this.writeDataToUnitFbfx(rcjjchzbhtw, worksheet,2,1);
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            function fillOut1_2Total(data) {
              let map = new Map();
              let totalDifference = 0;
              let totalJieSuan = 0;
              for (let i = 0; i < data.length; i++) {
                totalJieSuan = NumberUtil.add(totalJieSuan,data[i].total);
                totalDifference = NumberUtil.add(totalDifference,data[i].jieSuanPriceDifferencSum);
              }
              map.set(8,totalJieSuan);
              map.set(9,totalDifference);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fillOut1_2Total(rcjjchzbhtw),"合计")
            break;

          case '表1-3 人材机价差调整表':
            let rcjcjtzbhtw = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitRcjcjtzbhtw(args);
            await this.writeDataToUnitFbfx(rcjcjtzbhtw, worksheet,2,2);
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 2;
            args["copyRowsList"] = [worksheet.originSheet._rows.length];
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            function fillOut1_3Total(data) {
              let map = new Map();
              let totalDifference = 0;
              for (let i = 0; i < data.length; i++) {
                totalDifference = NumberUtil.add(totalDifference,data[i].jieSuanPriceDifferencSum);
              }
              map.set(12,totalDifference);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fillOut1_3Total(rcjcjtzbhtw),"合计")
            break;
          case '表1-4 主材汇总表':
            let zchzbHtw = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitZchzbHtw(args);
            await this.writeDataToUnitFbfx(zchzbHtw, worksheet,2,1);
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            function fillOut1_4Total(data) {
              let map = new Map();
              let totalDifference = 0;
              let totalJieSuan = 0;
              for (let i = 0; i < data.length; i++) {
                totalJieSuan = NumberUtil.add(totalJieSuan,data[i].total);
                totalDifference = NumberUtil.add(totalDifference,data[i].jieSuanPriceDifferencSum);
              }
              map.set(9,totalJieSuan);
              map.set(11,totalDifference);
              return map;
            }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fillOut1_4Total(zchzbHtw),"合计")
            break;
          default:
        }
      }
    }
    if (lanMuName == '13规范报表') {
      if (projectType == 'project') {
        switch (worksheet.name) {
          case "表1-1 总说明":
            // 当 expression 的值与 value3 匹配时执行的代码
            let param5 ={};
            //是招标还是投标 1招标 2投标
            param5.ifZbOrTb = 1;

            //工程项目ID
            param5.constructId = constructId;
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            let organization =await this.getOrganization(param5);
            if (organization[1].remark != null) {
              let remark = await ExcelUtil.removeTags(organization[1].remark);
              await ZhaoBiaoUtil.writeDataToCover3(remark,worksheet);
            }
            headArgsBot['titlePage'] = true;
            await JieSuanExcelUtil.dealWithPage(worksheet,workbook,headArgsBot,args);
            break;

          //工程项目层级
          // case '表1-1 竣工结算书封面':
          //   let obj = await this.service.exportQueryService.getconstructProjectJBXX({ constructId: constructId });
          //   await ZhaoBiaoUtil.writeDataToCover1(obj, worksheet);
          //   break;
          //工程项目层级
          case '表1-2 建设项目竣工结算汇总表':
            let array1_2gf = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectsCostSummary1_2gf(args,true);
            //删掉最后一行无用行
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.writeDataToUnitFbfx(array1_2gf, worksheet,3,1,args);
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,headArgsBot,args);
          function fill1_1Total(data) {
            let map = new Map();
            let totalJieSuan = 0;
            let totalAWF = 0;
            let totalGF = 0;
            for (let i = 0; i < data.length; i++) {
              totalJieSuan = NumberUtil.add(totalJieSuan, data[i].jieSuanPrice);
              totalAWF = NumberUtil.add(totalAWF, data[i].safeFee);
              totalGF = NumberUtil.add(totalGF, data[i].gfee);

            }
            map.set(3,totalJieSuan);
            map.set(5,totalAWF);
            map.set(6,totalGF);
            return map;
          }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fill1_1Total(array1_2gf),"合计")
            break;
          case '表1-3 建设项目竣工结算汇总表(沧州)':
            let array1_3gf = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectsCostSummary1_3gf(args);
            //删掉最后一行无用行
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.writeDataToUnitFbfx(array1_3gf, worksheet,3,1,args);
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,headArgsBot,args);
          function fill1_3Total_13(data) {
            let map = new Map();
            let totalJieSuan = 0;
            let totalAWF = 0;
            let totalGF = 0;
            for (let i = 0; i < data.length; i++) {
              totalJieSuan = NumberUtil.add(totalJieSuan, data[i].jieSuanPrice);
              totalAWF = NumberUtil.add(totalAWF, data[i].safeFee);
              totalGF = NumberUtil.add(totalGF, data[i].gfee);

            }
            map.set(3,totalJieSuan);
            map.set(5,totalAWF);
            map.set(6,totalGF);
            return map;
          }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fill1_3Total_13(array1_3gf),"合计")
            break;
          case '表1-1 竣工结算总价扉页':
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            let costAnalysisVO = await this.service.jieSuanProject.jieSuanUnitProjectService.getCostAnalysisData(args);
            let totalHeTong = 0;
            let totalJieSuan = 0;
            for (let i = 0; i < costAnalysisVO.costAnalysisConstructVOList.length; i++) {
              totalJieSuan = NumberUtil.add(totalJieSuan, costAnalysisVO.costAnalysisConstructVOList[i].jieSuanPrice);
              totalHeTong = NumberUtil.add(totalHeTong, costAnalysisVO.costAnalysisConstructVOList[i].contractPrice);
            }
            let map_bz = new Map();
            // NumberUtil.numToCny(project_fy.totalPrice,map_bz);
            let project_fy = PricingFileFindUtils.getProjectObjById(args.constructId);
            map_bz.set(3,project_fy.constructName);
            await JieSuanExcelUtil.fillContent(worksheet,map_bz,"工程项目");
            map_bz.clear();
            map_bz.set(2,totalHeTong);
            map_bz.set(6,NumberUtil.numToCny(totalHeTong));
            await JieSuanExcelUtil.fillContent(worksheet,map_bz,"签约合同价(小写)：");
            map_bz.clear();
            map_bz.set(2,totalJieSuan);
            map_bz.set(6,NumberUtil.numToCny(totalJieSuan));
            await JieSuanExcelUtil.fillContent(worksheet,map_bz,"竣工结算价(小写)：");

            map_bz.clear();
            map_bz.set(2,project_fy.constructProjectJBXX.find(e=>e.name === '招标人(发包人)' ).remark);
            map_bz.set(5,project_fy.constructProjectJBXX.find(e=>e.name === '投标人(承包人)' ).remark);
            map_bz.set(8,project_fy.constructProjectJBXX.find(e=>e.name === '工程造价咨询人' ).remark);
            await JieSuanExcelUtil.fillContent(worksheet,map_bz,"发  包  人：");

            map_bz.clear();
            map_bz.set(2,DateTimeUtil.nowWithFormat(DateTimeUtil.DATE_TIME_FORMAT3));
            if(project_fy.constructProjectJBXX.find(e=>e.name === '核对(复核)时间' )){
              let date_fh = DateTimeUtil.getDateTimeFromTimestamp(DateTimeUtil.getTimestampFromDateTime(project_fy.constructProjectJBXX.find(e=>e.name === '核对(复核)时间' ).remark,DateTimeUtil.DATE_TIME_FORMAT3),DateTimeUtil.DATE_TIME_FORMAT3)
              map_bz.set(6,date_fh);
            }
            await JieSuanExcelUtil.fillContent(worksheet,map_bz,"编制时间：");

            map_bz.clear();
            map_bz.set(6,project_fy.constructProjectJBXX.find(e=>e.name === '核对人(复核人)' ).remark);
            await JieSuanExcelUtil.fillContent(worksheet,map_bz,"核  对  人：");
            await this.writeDataToUnitFbfx([], worksheet,11,0,args);
            rcjBaoBiao['titlePage'] = true;
            rcjBaoBiao['headEndNum'] = 12
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,rcjBaoBiao,args);
            await this.setSheetStyleVerticalBottom(worksheet);
            break;

          case '表1-1 竣工结算书封面':
            // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test.xlsx");
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            let map = new Map();
            let project = PricingFileFindUtils.getProjectObjById(args.constructId);
            map.set(3,project.constructName);
            await JieSuanExcelUtil.fillContent(worksheet,map,"工程");
            map.clear();
            map.set(4,project.constructProjectJBXX.find(e=>e.name === '招标人(发包人)' ).remark);
            await JieSuanExcelUtil.fillContent(worksheet,map,"发包人：");
            map.set(4,project.constructProjectJBXX.find(e=>e.name === '投标人(承包人)' ).remark);
            await JieSuanExcelUtil.fillContent(worksheet,map,"承包人：");
            map.set(4,project.constructProjectJBXX.find(e=>e.name === '工程造价咨询人' ).remark);
            await JieSuanExcelUtil.fillContent(worksheet,map,"造价咨询人：");
            worksheet.getRow(9).getCell(4).value = DateTimeUtil.nowWithFormat(DateTimeUtil.DATE_TIME_FORMAT3);
            await this.setSheetStyleVerticalBottom(worksheet);
            break;
        }
      }
      if (projectType == 'single') {
        switch (worksheet.name) {
          case '表1-1 单项工程竣工结算汇总表':
            worksheet.lanMuName = '13规范报表'
            let array1_1 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectSingle1_1(args);
            //删掉最后一行无用行
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.writeDataToUnitFbfx(array1_1, worksheet,3,1,args);
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,headArgsBot,args);
          function fill1_1Total(data) {
            let map = new Map();
            let totalJieSuan = 0;
            let totalAWF = 0;
            let totalGF = 0;
            for (let i = 0; i < data.length; i++) {
              totalJieSuan = NumberUtil.add(totalJieSuan, data[i].gczj);
              totalAWF = NumberUtil.add(totalAWF,NumberUtil.numberScale2(NumberUtil.add(data[i].safeFee,data[i].jcaqwmsgfhj)));
              totalGF = NumberUtil.add(totalGF, NumberUtil.numberScale2(NumberUtil.add(data[i].gfee,data[i].jcgfhj)));

            }
            map.set(3,totalJieSuan);
            map.set(5,totalAWF);
            map.set(6,totalGF);
            return map;
          }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fill1_1Total(array1_1),"合计")
            break;
          case '表1-1 合同外单项工程竣工结算汇总表'://两张表一样  只是表名的区别
            let array_htw_1_1 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectSingle1_1(args);
            //删掉最后一行无用行
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.writeDataToUnitFbfx(array_htw_1_1, worksheet,3,1,args);
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,headArgsBot,args);
          function fill_htw_Total_1_1(data) {
            let map = new Map();
            let totalJieSuan = 0;
            let totalAWF = 0;
            let totalGF = 0;
            for (let i = 0; i < data.length; i++) {
              totalJieSuan = NumberUtil.add(totalJieSuan, data[i].gczj);
              totalAWF = NumberUtil.add(totalAWF, data[i].safeFee);
              totalGF = NumberUtil.add(totalGF, data[i].gfee);

            }
            map.set(3,totalJieSuan);
            map.set(5,totalAWF);
            map.set(6,totalGF);
            return map;
          }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fill_htw_Total_1_1(array_htw_1_1),"合计")
            break;
        }
      }
      if (projectType == 'unit') {
        let headArgsBot = {};
        headArgsBot['headStartNum'] = 1;
        headArgsBot['headEndNum'] = 4;
        headArgsBot['titlePage'] = false;

        let rcjBaoBiao  = {};
        rcjBaoBiao['headStartNum'] = 1;
        rcjBaoBiao['headEndNum'] = 3;
        rcjBaoBiao['titlePage'] = false;

        switch (worksheet.name) {
          //合同外
          case '表1-1 单位工程费用汇总表'://合同外
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            let array1_1 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingUnitCostSummarys1_1(args);
            await this.writeDataToUnitFbfx(array1_1, worksheet);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,rcjBaoBiao,args);
            break;
          case '表1-1 单位工程竣工结算汇总表（含价差）'://合同内
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            let array1_1_jc = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectUnit1_1(args);
            await this.writeDataToUnitFbfx(array1_1_jc, worksheet,null,null,args);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,rcjBaoBiao,args);
            break;
          case '表1-2 分部分项工程和单价措施项目清单与计价表':
            await this.getUnitProjectFbfxDjcsHtQdJjBiao(constructId, singleId, unitId, worksheet,args);
            let cell = JieSuanExcelUtil.findValueCell(worksheet.originSheet,"本页小计");
            args["copyRowsList"] = [cell.cell.row,worksheet.originSheet._rows.length-1];
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 3;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot,args);
            // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test.xlsx");
            break;
          case '表1-3 综合单价分析表':
            //不能直接复用
            await this.zongHeDanJiaAnalysis(constructId, singleId, unitId, worksheet,args);
            headArgsBot['headEndNum'] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot,args);
            await this.mergePointCellValues(worksheet);
            // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\testsplit.xlsx");
            await JieSuanExcelUtil.insertBlankRowForEachSheet(worksheet);
            break;
          case '表1-4 总价措施项目清单与计价表'://13规范 合同外 合同内都有
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.getUnitProjectZjcsHtQdJjBiao(constructId, singleId, unitId, worksheet,args);
            // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test.xlsx");
            args["copyRowsList"] = [worksheet.originSheet._rows.length-2,worksheet.originSheet._rows.length-1];
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 3;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
          case '表1-5 综合单价调整表':
            // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test.xlsx");
            await this.getUnitProjectZhdjTzBiao(constructId, singleId, unitId, worksheet);
            args["copyRowsList"] = [20,21,22];
            headArgsBot["headEndNum"] = 5;
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 3;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot,args);
            // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test1.xlsx");
            break;
          case '表1-5 其他项目清单与计价汇总表'://13规范 合同内
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.getUnitOtherProjectBiao(constructId, singleId, unitId, worksheet);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
          case '表1-6 材料(工程设备)暂估价及调整表'://合同内
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.getUnitOtherProjectClzgjBiao(constructId, singleId, unitId, worksheet);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot,args);
            break;
          case '表1-6 其他项目清单与计价汇总表'://13规范合同外
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.getUnitOtherProjectBiaoHtw(constructId, singleId, unitId, worksheet);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
          case '表1-7 专业工程结算价表'://合同内
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.getUnitOtherProjectZygczgjBiao(constructId, singleId, unitId, worksheet,args);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
          case '表1-7 暂列金额表':
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.getUnitOtherProjectProvisionalBiao(constructId, singleId, unitId, worksheet);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
          case '表1-8 计日工表':
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.getUnitOtherProjectDayWorkBiaoGf(constructId, singleId, unitId, worksheet);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot,args);
            break;
          case '表1-8 专业工程结算价表':
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.getUnitOtherProjectZygczgjBiaoHtw(constructId, singleId, unitId, worksheet);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
          case '表1-9 计日工表':
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.getUnitOtherProjectDayWorkBiaoGfHtw(constructId, singleId, unitId, worksheet);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot,args);
            break;
          case '表1-9 总承包服务费计价表':
            await this.getUnitOtherProjectServiceCostBiao(constructId, singleId, unitId, worksheet);
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
          case '表1-10 总承包服务费计价表'://合同外
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            await this.getUnitOtherProjectServiceCostBiaoHtw(constructId, singleId, unitId, worksheet);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
          case '表1-10 规费、税金项目结算表（不含价差）'://合同内
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            let array1_10 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectUnit1_10(args);
            await this.writeDataToUnitFbfx(array1_10, worksheet);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,null,args);
            break;
          case '表1-11 规费、税金项目结算表（含价差）'://合同内
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            let array1_11_jc = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectUnit1_11_jc(args);
            await this.writeDataToUnitFbfx(array1_11_jc, worksheet,3,1);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            //保留行使得数据只有一页时无需进行分页处理,同时用于空白行的插入
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,headArgsBot,args);
            break;

          case '表1-11 规费、税金项目清单与计价表'://合同外
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            let array1_11 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectUnit1_10(args);
            await this.writeDataToUnitFbfx(array1_11, worksheet,3,1);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            args["reserveNumForDeleteBlankRows"] = 2;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,headArgsBot,args);
          function fill_htw_total_array1_11(array1_11) {
            let map = new Map();
            let total = 0;
            for (let i = 0; i < array1_11.length; i++) {
              total = total + array1_11[i].price;
            }
            map.set(10,total);
            return map;
          }
            await JieSuanExcelUtil.fillTotalContent(worksheet,fill_htw_total_array1_11(array1_11),"合    计")
            break;

          case '表1-10 发包人提供材料和工程设备一览表'://合同内
            let clhgcsbb_1_10 = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitFbrtgclhgcsbb(args);
            await this.writeDataToUnitFbfx(clhgcsbb_1_10, worksheet,2);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
          case '表1-12 发包人提供材料和工程设备一览表'://合同内
            let clhgcsbb = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitFbrtgclhgcsbb(args);
            await this.writeDataToUnitFbfx(clhgcsbb, worksheet,2);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;

          case '表1-15 材料、机械、设备增值税计算表'://合同内
            let clJxSbzzsb = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitClJxSbzzsb(args);
            await this.writeDataToUnitFbfx(clJxSbzzsb, worksheet,2,1);
            args["copyRowsList"] = [worksheet.originSheet._rows.length];
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            let total_hs = 0;
            let total_cs = 0;
            let total_jx = 0;
            let total_xx = 0;
            for(let i=0;i<clJxSbzzsb.length;i++){
              total_hs = NumberUtil.add(total_hs,clJxSbzzsb[i].jieSuanTotal);
              total_cs = NumberUtil.add(total_cs,NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.numberScale2(NumberUtil.subtract(clJxSbzzsb[i].jieSuanPrice,NumberUtil.multiply(NumberUtil.multiply(clJxSbzzsb[i].jieSuanPrice,
                  clJxSbzzsb[i].taxRemoval),0.01))),clJxSbzzsb[i].jieSuanTotalNumber)));
              total_jx = NumberUtil.add(total_jx,clJxSbzzsb[i].jieSunJxTotal);
              total_xx = NumberUtil.add(total_xx,0);
            }
            let map_clJxSbzzsb = new Map();
            map_clJxSbzzsb.set(7,total_hs);
            map_clJxSbzzsb.set(10,total_cs)
            map_clJxSbzzsb.set(11,total_jx)
            map_clJxSbzzsb.set(13,total_xx)
            await JieSuanExcelUtil.fillTotalContent(worksheet,map_clJxSbzzsb,"合  计")
            break;
          case '表1-12 材料、机械、设备增值税计算表'://合同外
            let clJxSbzzsbHtw = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitClJxSbzzsbHtw(args);
            await this.writeDataToUnitFbfx(clJxSbzzsbHtw, worksheet,2,1);
            args["copyRowsList"] = [worksheet.originSheet._rows.length];
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            let total_hs_w = 0;
            let total_cs_w = 0;
            let total_jx_w = 0;
            let total_xx_w = 0;
            for(let i=0;i<clJxSbzzsbHtw.length;i++){
              total_hs_w = NumberUtil.add(total_hs_w,clJxSbzzsbHtw[i].jieSuanTotal);
              total_cs_w = NumberUtil.add(total_cs_w,NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.numberScale2(NumberUtil.subtract(clJxSbzzsbHtw[i].jieSuanPrice,NumberUtil.multiply(NumberUtil.multiply(clJxSbzzsbHtw[i].jieSuanPrice,
                  clJxSbzzsbHtw[i].taxRemoval),0.01))),clJxSbzzsbHtw[i].jieSuanTotalNumber)));
              total_jx_w = NumberUtil.add(total_jx_w,clJxSbzzsbHtw[i].jieSunJxTotal);
              total_xx_w = NumberUtil.add(total_xx_w,0);
            }
            let map_clJxSbzzsb_w = new Map();
            map_clJxSbzzsb_w.set(7,total_hs_w);
            map_clJxSbzzsb_w.set(10,total_cs_w)
            map_clJxSbzzsb_w.set(11,total_jx_w)
            map_clJxSbzzsb_w.set(13,total_xx_w)
            await JieSuanExcelUtil.fillTotalContent(worksheet,map_clJxSbzzsb_w,"合  计")

            break;
          case '表1-16 增值税进项税额计算汇总表'://合同内
            let array1_16 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectUnit1_16(args);
            await this.writeDataToUnitFbfx(array1_16, worksheet,2,1);
            args["copyRowsList"] = [worksheet.originSheet._rows.length];
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,rcjBaoBiao,args);
            let total = 0;
            for(let i=0;i<array1_16.length;i++){
              total = NumberUtil.add(total,array1_16[i].price);
            }
            let map = new Map();
            map.set(5,total)
            await JieSuanExcelUtil.fillTotalContent(worksheet,map,"合计")
            break;

          case '表1-13 增值税进项税额计算汇总表'://合同外
            let array1_13 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectUnit1_16(args);
            await this.writeDataToUnitFbfx(array1_13, worksheet,2,1);
            args["copyRowsList"] = [worksheet.originSheet._rows.length];
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook,null,args);
            let array1_13_total = 0;
            for(let i=0;i<array1_13.length;i++){
              array1_13_total = NumberUtil.add(array1_13_total,array1_13[i].price);
            }
            let array1_13_map = new Map();
            array1_13_map.set(5,array1_13_total)
            await JieSuanExcelUtil.fillTotalContent(worksheet,array1_13_map,"合计")

            // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx");

            break;

          case '表1-11 承包人提供主要材料和工程设备一览表'://22定额 合同内
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            let data_cbrcl_1_11 = await this.getUnitProjectRcjBiao(constructId, singleId, unitId, worksheet);
            await this.writeDataToUnitFbfx(data_cbrcl_1_11, worksheet,2,1);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
          case '表1-12 承包人提供主要材料和工程设备一览表'://22定额 合同内
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            let data_cbrcl_1_12 = await this.getUnitProjectRcjBiao(constructId, singleId, unitId, worksheet);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            args["reserveNumForDeleteBlankRows"] = 3;
            await this.writeDataToUnitFbfx(data_cbrcl_1_12, worksheet,2,3);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;

          case '表1-13 承包人提供主要材料和工程设备一览表'://合同内
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            let data_cbrcl_1_13 = await this.getUnitProjectRcjBiao(constructId, singleId, unitId, worksheet);
            await this.writeDataToUnitFbfx(data_cbrcl_1_13, worksheet,2,2);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            args["reserveNumForDeleteBlankRows"] = 1;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;
          case '表1-14 承包人提供主要材料和工程设备一览表'://合同内
            await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
            let data_cbrcl_1_14 = await this.getUnitProjectRcjBiao(constructId, singleId, unitId, worksheet);
            await this.writeDataToUnitFbfx(data_cbrcl_1_14, worksheet,2,3);
            args["copyRowsList"] = [worksheet.originSheet._rows.length-1];
            args["reserveNumForDeleteBlankRows"] = 3;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao,args);
            break;

          default:
        }
      }
    }
    if (lanMuName == "指标") {
      switch (worksheet.name) {
        case "表1-1工程特征分析表":
          await this.service.jieSuanProject.jieSuanExportQueryOtherService.getProjectFeatureAnalysis(args,worksheet);
          headArgsBot["headEndNum"] = 3;
          await JieSuanExcelUtil.dealWithPage(worksheet,workbook,headArgsBot,args);
          await this.mergeCellValues(worksheet);
          break;
        case '表1-2工程项目信息表':
          let construct = PricingFileFindUtils.getProjectObjById(constructId);
          let data = ConvertUtil.deepCopy(construct.constructProjectJBXX);
          data = data.filter(item => item.name!="基本信息");
          headArgsBot["headEndNum"] = 3;
          await this.writeDataToUnitFbfx(data, worksheet,2,0,args);
          await JieSuanExcelUtil.dealWithPage(worksheet,workbook,headArgsBot,args);
          break;
        case '表1-3主要工程量指标-按项目结构分析指标':
          headArgsBot["headEndNum"] = 3;
          await this.writeDataToUnitFbfx([], worksheet,2,0,args);
          await JieSuanExcelUtil.dealWithPage(worksheet,workbook,headArgsBot,args);
          break;
        case '表1-4主要工程量指标-按专业分析指标':
          headArgsBot["headEndNum"] = 3;
          await this.writeDataToUnitFbfx([], worksheet,2,0,args);
          await JieSuanExcelUtil.dealWithPage(worksheet,workbook,headArgsBot,args);
          break;
        case '表1-5主要工料分析指标-按项目结构分析指标':
          headArgsBot["headEndNum"] = 3;
          await this.writeDataToUnitFbfx([], worksheet,2,0,args);
          await JieSuanExcelUtil.dealWithPage(worksheet,workbook,headArgsBot,args);
          break;
        case '表1-6主要工料分析指标-按专业分析指标':
          headArgsBot["headEndNum"] = 3;
          await this.writeDataToUnitFbfx([], worksheet,2,0,args);
          await JieSuanExcelUtil.dealWithPage(worksheet,workbook,headArgsBot,args);
          break;
        case '表1-7主要经济指标分析表-按费用分析指标(单体)':
          headArgsBot["headEndNum"] = 3;
          await this.writeDataToUnitFbfx([], worksheet,2,0,args);
          await JieSuanExcelUtil.dealWithPage(worksheet,workbook,headArgsBot,args);
          break;
        case '表1-8主要经济指标分析表-按费用分析指标':
          headArgsBot["headEndNum"] = 3;
          await this.writeDataToUnitFbfx([], worksheet,2,0,args);
          await JieSuanExcelUtil.dealWithPage(worksheet,workbook,headArgsBot,args);
          break;
        case '表1-9主要经济指标分析表-按项目结构分析指标':
          headArgsBot["headEndNum"] = 3;
          await this.writeDataToUnitFbfx([], worksheet,2,0,args);
          await JieSuanExcelUtil.dealWithPage(worksheet,workbook,headArgsBot,args);
          break;
        case '表1-10主要经济指标分析表-按专业分析指标（单体）':
          headArgsBot["headEndNum"] = 3;
          await this.writeDataToUnitFbfx([], worksheet,2,0,args);
          await JieSuanExcelUtil.dealWithPage(worksheet,workbook,headArgsBot,args);
          break;
        case '表1-11主要经济指标分析表-按专业分析指标':
          headArgsBot["headEndNum"] = 3;
          await this.writeDataToUnitFbfx([], worksheet,2,0,args);
          await JieSuanExcelUtil.dealWithPage(worksheet,workbook,headArgsBot,args);
          break;
      }
    }


    //填充工程项目名称
    if (projectType == 'project' && !(worksheet.name.includes('封面') || worksheet.name.includes('扉页'))) {
      //填充 项目名称
      let project = PricingFileFindUtils.getProjectObjById(constructId);
      await JieSuanExcelUtil.fillSheetProjectName(worksheet, project.constructName, '工程名称：');
      //填充 工程项目总价表的工程名称
      await JieSuanExcelUtil.fillSheetProjectName(worksheet, project.constructName, '项目名称：');
    }
    if (projectType == 'single') {
      let singleProject = PricingFileFindUtils.getSingleProject(constructId, singleId);
      await JieSuanExcelUtil.fillSheetProjectName(worksheet, singleProject.projectName, '工程名称：');
    }
    if (projectType == 'unit' && !(worksheet.name.includes('封面') || worksheet.name.includes('扉页'))) {
      let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
      let single = PricingFileFindUtils.getSingleProject(constructId, singleId);
      let sheetProjectName = '';
      if (single != null) {
        sheetProjectName = single.projectName + unitProject.upName;
      } else {
        sheetProjectName = unitProject.upName;
      }
      await JieSuanExcelUtil.fillSheetProjectName(worksheet, sheetProjectName, '项目名称：');
      await JieSuanExcelUtil.fillSheetProjectName(worksheet, sheetProjectName, '工程名称：');
    }
    await JieSuanExcelUtil.adjustExcel(worksheet);
    await JieSuanExcelUtil.dealWithRowExcessivelyHigh(worksheet);
    return worksheet;
  }

  async mergeCellValues(workSheet) {

    let rowNumTopStart = 0;
    let rowNumTopBottom = 0;
    let headFlag = false;
    for (let i = 0; i < workSheet._rows.length-1; i++) {
      let rowElement = workSheet._rows[i];
      let rowElementNext = workSheet._rows[i+1];
      if (workSheet.rowDataTypelist[i + 1].field == "head"||workSheet.rowDataTypelist[i + 2].field == "head") {
        if (rowNumTopStart != 0) {
          workSheet.unMergeCells([rowNumTopStart, 1, rowNumTopBottom, 1]);
          workSheet.mergeCells([rowNumTopStart, 1, rowNumTopBottom, 1]);
          headFlag = true;
        }
        continue;
      }
      if (headFlag) {
        headFlag = false;
        rowNumTopStart = i+1;
        rowNumTopBottom = rowNumTopStart;
      }

      if (rowElement._cells.filter(item => ObjectUtils.isNotEmpty(item.value)).length == 1) {
        for (let j = 0; j < rowElement._cells.length; j++) {
          let alignmentObject = ConvertUtil.deepCopy(rowElement._cells[j].style);
          if (ObjectUtils.isNotEmpty(alignmentObject.alignment)) {
              alignmentObject.alignment.horizontal = "center";
              rowElement._cells[j].style = alignmentObject;
          }
        }
        //合并专业工程行
        workSheet.unMergeCells([i+1, 1, i+1, 4]);
        workSheet.mergeCells([i+1, 1, i+1, 4]);
        if (rowNumTopStart != 0) {
          workSheet.unMergeCells([rowNumTopStart, 1, rowNumTopBottom, 1]);
          workSheet.mergeCells([rowNumTopStart, 1, rowNumTopBottom, 1]);
          headFlag = true;
        }
        continue;
      }
      if (!(rowElement._cells.filter(item => ObjectUtils.isNotEmpty(item.value)).length > 1)) {
        continue;
      }
      if (rowNumTopStart == 0 && ObjectUtils.isNotEmpty(rowElement._cells[0].value) && rowElement._cells[0].value == rowElementNext._cells[0].value) {
        rowNumTopStart = i+1;
        rowNumTopBottom = rowNumTopStart+1;
      }else if (ObjectUtils.isNotEmpty(rowElement._cells[0].value) && rowElement._cells[0].value == rowElementNext._cells[0].value) {
        rowNumTopBottom++;
      }else {
        workSheet.unMergeCells([rowNumTopStart, 1, rowNumTopBottom, 1]);
        workSheet.mergeCells([rowNumTopStart, 1, rowNumTopBottom, 1]);
        rowNumTopStart = i+2;
        rowNumTopBottom = rowNumTopStart;
      }
    }
    // await workSheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test.xlsx");
  }


  async setSheetStyleVerticalBottom(workSheet) {
    for (let i = 3; i < workSheet._rows.length; i++) {
      let row = workSheet._rows[i];
      for (let j = 0; j < row._cells.length; j++) {
        if (ObjectUtils.isNotEmpty(row._cells[j].style) && ObjectUtils.isNotEmpty(row._cells[j].style.alignment)) {
          if (ObjectUtils.isNotEmpty(row._cells[j].value) && (row._cells[j].value == "（单位盖章）" ||
              row._cells[j].value == "(单位资质专用章)" || row._cells[j].value == ""
          )) {
            row._cells[j].style.alignment["vertical"] = "top";
          }else {
            row._cells[j].style.alignment["vertical"] = "bottom";
          }
        }
      }
    }
  }

  getProjectRootPath() {
    // let relativePath = __filename;
    // let index = relativePath.indexOf("pricing-cs");
    // let prefix = relativePath.substring(0,index);
    return UtilsPs.getExtraResourcesDir();
    // return prefix+"pricing-cs";
  }


  async getUnitProjectRcjBiao(constructId, singleId, unitId, worksheet) {
    let data = null;
    /*if (worksheet.name == '表1-12 主材汇总表') {
      let unitRcjQuery = this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId,
        kind: 0
      });
      data = unitRcjQuery.filter(k => k.kind == 7);
    }*/
    if (worksheet.name == '表1-13 承包人提供主要材料和工程设备一览表' || worksheet.name == '表1-11 承包人提供主要材料和工程设备一览表') {
      data = this.service.jieSuanProject.jieSuanRcjProcess.getJieSuanRcjBBzyclgc({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId,
        methodType: 1
      });
    }

    if (worksheet.name == '表1-14 承包人提供主要材料和工程设备一览表' || worksheet.name == '表1-12 承包人提供主要材料和工程设备一览表') {
      data = this.service.jieSuanProject.jieSuanRcjProcess.getJieSuanRcjBBzyclgc({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId,
        methodType: 4
      });

    }
    return data;
  }

  /**
   * 表1-2 分部分项合同清单工程量及结算工程量对比表
   * @param constructId
   * @param singleId
   * @param unitId
   * @returns {any[]}
   */
  async getUnitProjectFbfxHtQdQuantityDbBiao(constructId, singleId, unitId, worksheet) {

    //单位工程对象
    let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    //获取分部分项清单数据
    let allNodes = PricingFileFindUtils.getQdByfbfx(constructId, singleId, unitId);
    await this.writeDataToUnitFbfx(allNodes, worksheet,2,0);

  }

  async zongHeDanJiaAnalysis(constructId, singleId, unitId, worksheet,args) {
    // 人工单价取清单下人材机人工费单价数据
    // 未计价材料取清单下未计价的材料费

    //分部分项下清单的材料数据
    //获取分部分项清单数据
    let listTotal = [];
    let fbfxQd = PricingFileFindUtils.getQdByfbfx(constructId, singleId, unitId);
    let copyListFbfx = ConvertUtil.deepCopy(fbfxQd);
    listTotal.push(...copyListFbfx);
    let qdByZjcs = PricingFileFindUtils.getQdByZjcs(constructId,singleId, unitId);
    let copyListZjcs = ConvertUtil.deepCopy(qdByZjcs);
    listTotal.push(...copyListZjcs);
    let finalDataCollection = [];
    for (let i = 0; i < listTotal.length; i++) {
        let qdElement = listTotal[i];
        let list = await this.generateEmptyValueList(worksheet);
        await this.pointValueToList(1,2,list,"项目编码",{"templateNum":3,});
        await this.pointValueToList(3,4,list,qdElement.fxCode,{"templateNum":3,});
        await this.pointValueToList(5,6,list,"项目名称",{"templateNum":3,});
        await this.pointValueToList(7,9,list,qdElement.name,{"templateNum":3,});
        await this.pointValueToList(10,10,list,"计量单位",{"templateNum":3,});
        await this.pointValueToList(11,11,list,qdElement.unit,{"templateNum":3,});
        await this.pointValueToList(12,13,list,"工程量",{"templateNum":3,});
        await this.pointValueToList(14,14,list,qdElement.quantity,{"templateNum":3,});
        finalDataCollection.push(list);
        let list2 = await this.generateEmptyValueList(worksheet);
        await this.pointValueToList(1,14,list2,"清单综合单价组成明细",{"templateNum":4,});
        finalDataCollection.push(list2);
        let list3 = await this.generateEmptyValueList(worksheet);
        await this.pointValueToList(1,1,list3,"定额编号",{"templateNum":5,});
        await this.pointValueToList(2,2,list3,"定额项目名称",{"templateNum":5,});
        await this.pointValueToList(3,3,list3,"定额\n单位",{"templateNum":5,});
        await this.pointValueToList(4,4,list3,"数量",{"templateNum":5,});
        await this.pointValueToList(5,9,list3,"单价",{"templateNum":5,});
        await this.pointValueToList(10,14,list3,"合价",{"templateNum":5,});
        finalDataCollection.push(list3);
        let list4 = await this.generateEmptyValueList(worksheet);
        await this.pointValueToList(1,1,list4,"定额编号",{"templateNum":6,});
        await this.pointValueToList(2,2,list4,"定额项目名称",{"templateNum":6,});
        await this.pointValueToList(3,3,list4,"定额\n单位",{"templateNum":6,});
        await this.pointValueToList(4,4,list4,"数量",{"templateNum":6,});
        await this.pointValueToList(5,5,list4,"人工费",{"templateNum":6,});
        await this.pointValueToList(6,6,list4,"材料费",{"templateNum":6,});
        await this.pointValueToList(7,8,list4,"机械费",{"templateNum":6,});
        await this.pointValueToList(9,9,list4,"管理费\n和利润",{"templateNum":6,});
        await this.pointValueToList(10,10,list4,"人工费",{"templateNum":6,});
        await this.pointValueToList(11,11,list4,"材料费",{"templateNum":6,});
        await this.pointValueToList(12,13,list4,"机械费",{"templateNum":6,});
        await this.pointValueToList(14,14,list4,"管理费\n和利润",{"templateNum":6,});
        finalDataCollection.push(list4);
        //填充定额行
        let rgfTotal = 0;
        let clfTotal = 0;
        let jxfTotal = 0;
        let glflrTotal = 0;
        let des = PricingFileFindUtils.getDeByQdId(constructId,singleId,unitId,qdElement.sequenceNbr);
        for (let j = 0; j < des.length; j++) {
            let de = des[j];
            let result = this.service.unitPriceService.getPriceBuild(constructId, singleId, unitId, de.sequenceNbr);
            let rgfdj = result.filter(item=> item.type=="人工费")[0].displayUnitPrice;
            let rgfhj = result.filter(item=> item.type=="人工费")[0].displayAllPrice;

            let clfdj = result.filter(item=> item.type=="材料费")[0].displayUnitPrice;
            let clfhj = result.filter(item=> item.type=="材料费")[0].displayAllPrice;

            let jxfdj = result.filter(item=> item.type=="机械费")[0].displayUnitPrice;
            let jxfhj = result.filter(item=> item.type=="机械费")[0].displayAllPrice;

            let glfdj = result.filter(item=> item.type=="管理费")[0].displayUnitPrice;
            let glfhj = result.filter(item=> item.type=="管理费")[0].displayAllPrice;

            let lrdj = result.filter(item=> item.type=="利润")[0].displayUnitPrice;
            let lrhj = result.filter(item=> item.type=="利润")[0].displayAllPrice;

            rgfTotal = NumberUtil.add(rgfTotal,rgfhj);
            clfTotal = NumberUtil.add(clfTotal,clfhj);
            jxfTotal = NumberUtil.add(jxfTotal,jxfhj);
            glflrTotal = NumberUtil.addParams(glflrTotal,glfhj,lrhj);
            let deData = await this.generateEmptyValueList(worksheet);
            await this.pointValueToList(1,1,deData,de.bdCode,{"templateNum":7,});
            await this.pointValueToList(2,2,deData,de.name,{"templateNum":7,});
            await this.pointValueToList(3,3,deData,de.unit,{"templateNum":7,});
            await this.pointValueToList(4,4,deData,de.backQuantity,{"templateNum":7,});
            await this.pointValueToList(5,5,deData,rgfdj,{"templateNum":7,});
            await this.pointValueToList(6,6,deData,clfdj,{"templateNum":7,});
            await this.pointValueToList(7,8,deData,jxfdj,{"templateNum":7,});//机械费
            await this.pointValueToList(9,9,deData,NumberUtil.add(glfdj,lrdj),{"templateNum":7,});
            await this.pointValueToList(10,10,deData,rgfhj,{"templateNum":7,});
            await this.pointValueToList(11,11,deData,clfhj,{"templateNum":7,});
            await this.pointValueToList(12,13,deData,jxfhj,{"templateNum":7,});
            await this.pointValueToList(14,14,deData,NumberUtil.add(glfhj,lrhj),{"templateNum":7,});
            finalDataCollection.push(deData);
        }
        //填充小计行
        let xiaoJiData = await this.generateEmptyValueList(worksheet);
        await this.pointValueToList(1,2,xiaoJiData,"人工单价",{"templateNum":8,});
        await this.pointValueToList(3,9,xiaoJiData,"小计",{"templateNum":8,});
        await this.pointValueToList(10,10,xiaoJiData,rgfTotal,{"templateNum":8,});
        await this.pointValueToList(11,11,xiaoJiData,clfTotal,{"templateNum":8,});
        await this.pointValueToList(12,13,xiaoJiData,jxfTotal,{"templateNum":8,});
        await this.pointValueToList(14,14,xiaoJiData,glflrTotal,{"templateNum":8,});
        finalDataCollection.push(xiaoJiData);

        //填充未计价材料费行
        let list5 = await this.generateEmptyValueList(worksheet);
        await this.pointValueToList(3,9,list5,"未计价材料费",{"templateNum":9,});
        finalDataCollection.push(list5);
        let list6 = await this.generateEmptyValueList(worksheet);
        await this.pointValueToList(1,9,list6,"清单项目综合单价",{"templateNum":10,});
        finalDataCollection.push(list6);
        let list7 = await this.generateEmptyValueList(worksheet);
        await this.pointValueToList(1,1,list7,"材\n料\n费\n明\n细",{"templateNum":11,"dataType":"materials","field":"headLine"});
        await this.pointValueToList(2,6,list7,"主要材料名称、规格、型号",{"templateNum":11,"dataType":"materials","field":"headLine"});
        await this.pointValueToList(7,8,list7,"单位",{"templateNum":11,"dataType":"materials","field":"headLine"});
        await this.pointValueToList(9,9,list7,"数量",{"templateNum":11,"dataType":"materials","field":"headLine"});
        await this.pointValueToList(10,10,list7,"单价（元）",{"templateNum":11,"dataType":"materials","field":"headLine"});
        await this.pointValueToList(11,11,list7,"合价（元）",{"templateNum":11,"dataType":"materials","field":"headLine"});
        await this.pointValueToList(12,13,list7,"暂估单价\n（元）",{"templateNum":11,"dataType":"materials","field":"headLine"});
        await this.pointValueToList(14,14,list7,"暂估合价\n（元）",{"templateNum":11,"dataType":"materials","field":"headLine"});
        finalDataCollection.push(list7);
        let args = {
          id:qdElement.sequenceNbr,
          constructId:constructId,
          singleId:singleId,
          unitId:unitId,
          branchType:1,
        }
        let rcjMaterials = this.service.jieSuanProject.jieSuanRcjProcess.jieSuanQueryRcjDataByDeId(args);
        let rcjData = [];
        if (ObjectUtils.isNotEmpty(rcjMaterials) && ObjectUtils.isNotEmpty(rcjMaterials.result)) {
          rcjData = rcjMaterials.result.filter(item => item.type=="材料费"||item.type=="主材费");
        }
        let isContainOtherMaterials = false;
        let otherMaterialsTotal = 0;
        let otherMaterialsTotalZanGu = 0;
        let materialTotal = 0;
        let materialTotalZanGu = 0;
        for (let j = 0; j < rcjData.length; j++) {
          let element = rcjData[j];

          let isZanGuCailiao = false;
          if (element.kind == JieSuanRcjDifferenceEnum.ZANGUJIA.code) {
              isZanGuCailiao = true;
          }
          let listData = await this.generateEmptyValueList(worksheet);
          await this.pointValueToList(1,1,listData,"",{"templateNum":12,"dataType":"materials"});
          await this.pointValueToList(2,6,listData,ObjectUtils.isNotEmpty(element.specification)?element.materialName+"  "+element.specification:element.materialName
              ,{"templateNum":12,"dataType":"materials"}
          );
          await this.pointValueToList(7,8,listData,element.unit,{"templateNum":12,"dataType":"materials"});
          await this.pointValueToList(9,9,listData,element.jieSuanTotalNumber,{"templateNum":12,"dataType":"materials"});

          let price = null;
          let total = null;//合同合价
          if (args.deType == "22") {
              price = args.simple?listData.jieSuanPriceMarketTax:listData.jieSuanPriceMarket;
              total = args.simple?listData.jieSuanPriceMarketTotal:listData.jieSuanPriceMarketTotal;
          }else {
              price = listData.jieSuanMarketPrice;
              total = listData.jieSuanTotal;
          }
          if (element.materialName == "其他材料") {
            isContainOtherMaterials = true;
            otherMaterialsTotal = NumberUtil.add(otherMaterialsTotal,total);
            if (isZanGuCailiao) {
              otherMaterialsTotalZanGu = NumberUtil.add(otherMaterialsTotalZanGu,total);
            }
          }
          if (isZanGuCailiao) {
            materialTotal = NumberUtil.add(materialTotal,total);
          }else {
            materialTotalZanGu = NumberUtil.add(materialTotalZanGu,total);
          }
          await this.pointValueToList(10,10,listData,isZanGuCailiao?null:price,{"templateNum":12,"dataType":"materials"});
          await this.pointValueToList(11,11,listData,isZanGuCailiao?null:total,{"templateNum":12,"dataType":"materials"});
          await this.pointValueToList(12,13,listData,isZanGuCailiao?price:null,{"templateNum":12,"dataType":"materials"});
          await this.pointValueToList(14,14,listData,isZanGuCailiao?total:null,{"templateNum":12,"dataType":"materials"});
          finalDataCollection.push(listData);
        }
        //其他材料费
        if (isContainOtherMaterials) {
          let otherMaterial = await this.generateEmptyValueList(worksheet);
          await this.pointValueToList(2,9,otherMaterial,"其他材料费",{"templateNum":13,"dataType":"materials"});
          await this.pointValueToList(10,10,otherMaterial,"-",{"templateNum":13,"dataType":"materials"});
          await this.pointValueToList(11,11,otherMaterial,otherMaterialsTotal,{"templateNum":13,"dataType":"materials"});
          await this.pointValueToList(12,13,otherMaterial,"-",{"templateNum":13,"dataType":"materials"});
          await this.pointValueToList(14,14,otherMaterial,otherMaterialsTotalZanGu,{"templateNum":13,"dataType":"materials"});
          finalDataCollection.push(otherMaterial);
        }
        if (ObjectUtils.isNotEmpty(rcjData)) {
          //材料费小计
          let materialTotalData = await this.generateEmptyValueList(worksheet);
          await this.pointValueToList(1,1,materialTotalData,"",{"templateNum":14,"dataType":"materials","breakFlag":true});
          await this.pointValueToList(2,9,materialTotalData,"材料费小计",{"templateNum":14,"dataType":"materials","breakFlag":true});
          await this.pointValueToList(10,10,materialTotalData,"-",{"templateNum":14,"dataType":"materials","breakFlag":true});
          await this.pointValueToList(11,11,materialTotalData,materialTotal,{"templateNum":14,"dataType":"materials","breakFlag":true});
          await this.pointValueToList(12,13,materialTotalData,"-",{"templateNum":14,"dataType":"materials","breakFlag":true});
          await this.pointValueToList(14,14,materialTotalData,materialTotalZanGu,{"templateNum":14,"dataType":"materials","breakFlag":true});
          finalDataCollection.push(materialTotalData);
          for (let j = 0; j < list7.length; j++) {
            let element = list7[j];
            element.additionalProperties['height'] = 28.5;
          }
        }else{
          for (let j = 0; j < list7.length; j++) {
            let element = list7[j];
            element.additionalProperties['breakFlag'] = true;//分页标志
            element.additionalProperties['height'] = 66.7;
          }
        }
    }
    await this.writeDataToUnitFbfxForZongHeDanJiaAnalysis(finalDataCollection,worksheet,1,0,args);
    // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test.xlsx");
  }

  async mergePointCellValues(workSheet) {
    let startIndex = 0;
    let endIndex = workSheet._rows.length-1;
    let mergeNumList = [];
    if (ObjectUtils.isNotEmpty(workSheet.rowBreaks)) {

      for (let i = 0; i < workSheet.rowBreaks.length; i++) {
        endIndex = workSheet.rowBreaks[i].id-1;
        let rows = workSheet.rowDataTypelist.slice(startIndex+1,endIndex+2);
        for (let j = 0; j < rows.length; j++) {
          if (ObjectUtils.isNotEmpty(rows[j].dataType) && rows[j].dataType == "materials") {
            mergeNumList.push(startIndex+1+j);
          }
        }
        //合并指定列
        if (ObjectUtils.isNotEmpty(mergeNumList)) {
            workSheet.unMergeCells([mergeNumList[0], 1, mergeNumList[mergeNumList.length-1], 1]);
            workSheet.mergeCells([mergeNumList[0], 1, mergeNumList[mergeNumList.length-1], 1]);
            //给最后一个cell赋下边线
            workSheet._rows[mergeNumList[mergeNumList.length-1]-1]._cells[0].style.border['bottom'] = {
              style:"thin",
              color:{indexed:8},
            }
          //给最上一个cell赋上边线
          workSheet._rows[mergeNumList[0]-1]._cells[0].style.border['top'] = {
            style:"thin",
            color:{indexed:8},
          }
        }
        mergeNumList = [];
        startIndex = endIndex+2;
      }
      //对于最后一页的处理
      let rows = workSheet.rowDataTypelist.slice(endIndex+2,workSheet._rows.length+1);
      for (let j = 0; j < rows.length; j++) {
        if (ObjectUtils.isNotEmpty(rows[j].dataType) && rows[j].dataType == "materials") {
          mergeNumList.push(endIndex+2+j);
        }
      }
      //合并指定列
      if (ObjectUtils.isNotEmpty(mergeNumList)) {
        workSheet.unMergeCells([mergeNumList[0], 1, mergeNumList[mergeNumList.length-1], 1]);
        workSheet.mergeCells([mergeNumList[0], 1, mergeNumList[mergeNumList.length-1], 1]);
        //给最后一个cell赋下边线
        workSheet._rows[mergeNumList[mergeNumList.length-1]-1]._cells[0].style.border['bottom'] = {
          style:"thin",
          color:{indexed:8},
        }
        //给最上一个cell赋上边线
        workSheet._rows[mergeNumList[0]-1]._cells[0].style.border['top'] = {
          style:"thin",
          color:{indexed:8},
        }
      }
    }else {
      let rows = workSheet.rowDataTypelist.slice(startIndex+1,endIndex+2);
      for (let j = 0; j < rows.length; j++) {
        if (ObjectUtils.isNotEmpty(rows[j].dataType) && rows[j].dataType == "materials") {
          mergeNumList.push(startIndex+1+j);
        }
      }
      //合并指定列
      if (ObjectUtils.isNotEmpty(mergeNumList)) {
        workSheet.unMergeCells([mergeNumList[0], 1, mergeNumList[mergeNumList.length-1], 1]);
        workSheet.mergeCells([mergeNumList[0], 1, mergeNumList[mergeNumList.length-1], 1]);
        //给最后一个cell赋下边线
        workSheet._rows[mergeNumList[mergeNumList.length-1]-1]._cells[0].style.border['bottom'] = {
          style:"thin",
          color:{indexed:8},
        }
        //给最上一个cell赋上边线
        workSheet._rows[mergeNumList[0]-1]._cells[0].style.border['top'] = {
          style:"thin",
          color:{indexed:8},
        }
      }
    }

    //定位中间有分页的材料费明细 合并单元格  对值进行拆分
    // await workSheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test3333.xlsx");
    if (ObjectUtils.isNotEmpty(workSheet.rowBreaks)) {
      for (let i = 0; i < workSheet.rowBreaks.length; i++) {
        let pointIndex = workSheet.rowBreaks[i].id-1;//分页索引
        let indexListRequired = [];
        await this.pointSearch(pointIndex+1,null,null,workSheet.rowDataTypelist,indexListRequired);
        if (indexListRequired.length == 2) {
          let first = indexListRequired[0];
          let second = indexListRequired[1];
          let slice = workSheet._rows.slice(first,second+1);
          let totalHeight = 0;
          let beforeHeight = 0;
          let afterHeight = 0
          let beforeListRow = [];
          let afterListRow = [];
          for (let j = 0; j < slice.length; j++) {
            let element = slice[j];
            if (workSheet.rowDataTypelist[element.number].field =="head"){
              continue;
            }
            totalHeight +=element.height;
            if (element.number - 1 < pointIndex+1) {
              beforeHeight +=element.height;
              beforeListRow.push(element);
            }

            if (element.number - 1 > pointIndex+1) {
              afterHeight +=element.height;
              afterListRow.push(element);
            }
          }
          let ratio = beforeHeight/totalHeight;
          let beforeIndex = Math.ceil("材\n料\n费\n明\n细".length*ratio)
          let before = "材\n料\n费\n明\n细".substring(0,beforeIndex);
          let after = "材\n料\n费\n明\n细".substring(beforeIndex,"材\n料\n费\n明\n细".length);
          for (let j = 0; j < beforeListRow.length; j++) {
            beforeListRow[j]._cells[0].value = before;
          }
          for (let j = 0; j < afterListRow.length; j++) {
            afterListRow[j]._cells[0].value = after;
          }
        }
      }
    }
  }

  async pointSearch(pointIndex, before,after,list,indexList) {
    if (ObjectUtils.isNotEmpty(before)) {
      if (list[before+1].dataType == "materials") {
        await this.pointSearch(pointIndex,before-1,null,list,indexList);
      }else {
        if (pointIndex != before + 1) {  //在不等于原索引pointIndex的情况下
          indexList.push(before+1);
        }
      }
      return;
    }
    if (ObjectUtils.isNotEmpty(after)) {
      if (after==list.length) return;
      if (list[after+1].dataType == "materials") {
        await this.pointSearch(pointIndex,null,after+1,list,indexList);
      }else {
        if (pointIndex != after - 2) {
          indexList.push(after-1);
        }
      }
      return;
    }
    if (ObjectUtils.isEmpty(before) && ObjectUtils.isEmpty(after)) {
      await this.pointSearch(pointIndex,pointIndex-1,null,list,indexList);
      await this.pointSearch(pointIndex,null,pointIndex+2,list,indexList);
    }

  }

  async generateEmptyValueList(workSheet) {
    let list = [];
    for (let i = 0; i < workSheet._columns.length; i++) {
      let object = {};
      list.push(object);
    }
    return list;
  }

  async pointValueToList(leftNum, rightNum, list,value,args) {
    for (let i = leftNum-1; i <= rightNum-1; i++) {
        list[i].value = value;
        list[i].additionalProperties = args;
    }
  }

  //表1-2 分部分项工程和单价措施项目清单与计价表
  async getUnitProjectFbfxDjcsHtQdJjBiao(constructId, singleId, unitId, worksheet,args) {

    //单位工程对象
    let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

    //获取分部分项数据
    let itemBillProjects = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).filter(bt=>bt.kind==BranchProjectLevelConstant.top);
    let qdByfbfx = PricingFileFindUtils.getQdByfbfx(constructId, singleId, unitId);
    //获取措施项目数据
    let measure = PricingFileFindUtils.getCSXM(constructId, singleId, unitId).filter(bt=>bt.kind==BranchProjectLevelConstant.top);
    let qdByDjcs = PricingFileFindUtils.getQdByDjcs(constructId, singleId, unitId);
    PricingFileFindUtils.getd
    let allNodes = [];
    allNodes.push(...itemBillProjects);
    allNodes.push(...qdByfbfx);
    allNodes.push(...measure);
    if(qdByDjcs.length>0){
      allNodes.push(qdByDjcs[0].parent);
      qdByDjcs = qdByDjcs.filter(k=>ObjectUtils.isNotEmpty(k.name))
      allNodes.push(...qdByDjcs);
    }

    let copyList = ConvertUtil.deepCopy(allNodes);
    this.sortDispNo(copyList);

    // for (let i = 0; i < 100; i++) {
    //   let object = {};
    //   object["dispNo"] = i+"";
    //   object["bdCode"] = "la";
    //   object["bdName"] = "la";
    //   object["price"] = "la";
    //   object["rfee"] = "la";
    //   object["cfee"] = "la";
    //   allNodes.push(object);
    // }
    // await JieSuanExcelUtil.deleteRowsByContent("本页小计",worksheet);
    // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test1.xlsx");
    await this.writeDataToUnitFbfx(allNodes, worksheet, null,3,args);

    function total(data) {
      let total = 0;
      let map = new Map();
      for (let i = 0; i < data.length; i++) {
        let datum = data[i];
        if (datum.kind == BranchProjectLevelConstant.top) {
          total+=datum.total
        }
      }
      map.set(9,NumberUtil.numberScale2(total));
      return map;
    }
    // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test2.xlsx");
    await JieSuanExcelUtil.fillTotalContent(worksheet,total(allNodes),"本页小计");
    await JieSuanExcelUtil.fillTotalContent(worksheet,total(allNodes),"合计");
    //删掉最后一行无用行
    await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
  }


  //表1-3 分部分项工程和单价措施项目清单与计价表
  async get1_3UnitProjectFbfxDjcsQdJjBiao(constructId, singleId, unitId, worksheet) {

    //单位工程对象
    let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

    //获取分部分项数据
    let itemBillProjects = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).filter(bt=>bt.kind==BranchProjectLevelConstant.top);
    let qdByfbfx = PricingFileFindUtils.getQdByfbfx(constructId, singleId, unitId);
    //获取措施项目数据
    let measure = PricingFileFindUtils.getCSXM(constructId, singleId, unitId).filter(bt=>bt.kind==BranchProjectLevelConstant.fb && bt.name=="单价措施项目");
    let qdByDjcs = PricingFileFindUtils.getQdByDjcs(constructId, singleId, unitId);
    let allNodes = [];
    itemBillProjects = ConvertUtil.deepCopy(itemBillProjects);
    qdByfbfx = ConvertUtil.deepCopy(qdByfbfx);
    measure = ConvertUtil.deepCopy(measure);
    qdByDjcs = ConvertUtil.deepCopy(qdByDjcs);

    this.sortDispNo(qdByfbfx);
    this.sortDispNo(qdByDjcs);

    allNodes.push(...itemBillProjects);
    allNodes.push(...qdByfbfx);
    allNodes.push(...measure);
    allNodes.push(...qdByDjcs);
    // for (let i = 0; i < 100; i++) {
    //   let object = {};
    //   object["dispNo"] = i+"";
    //   object["bdCode"] = "la";
    //   object["bdName"] = "la";
    //   object["price"] = "la";
    //   object["rfee"] = "la";
    //   object["cfee"] = "la";
    //   allNodes.push(object);
    // }
    await this.writeDataToUnitFbfx(allNodes, worksheet, 3,4);

    function total(data) {
      let backTotal = 0;
      let total = 0;//结算价合计
      let differTotal = 0;
      let map = new Map();
      for (let i = 0; i < data.length; i++) {
        let datum = data[i];
        if (datum.kind == BranchProjectLevelConstant.qd) {
          backTotal+=datum.backTotal;
          total+=datum.total;
          differTotal += NumberUtil.subtract(datum.total, datum.backTotal);
        }
      }
      map.set(11,NumberUtil.numberScale2(backTotal));//合同价合计
      map.set(13,NumberUtil.numberScale2(total));//结算价合计
      map.set(14,NumberUtil.numberScale2(differTotal));//差额合计
      return map;
    }
    // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test2.xlsx");
    await JieSuanExcelUtil.fillTotalContent(worksheet,total(allNodes),"合计");
    //删掉最后一行无用行
    await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
  }


  //表1-4 总价措施项目清单与计价表
  async getUnitProjectZjcsHtQdJjBiaoHtn(constructId, singleId, unitId, worksheet) {

    //获取措施项目数据
    let qdByZjcs = PricingFileFindUtils.getQdByZjcs(constructId, singleId, unitId);
    let copyList = ConvertUtil.deepCopy(qdByZjcs);
    this.sortDispNo(copyList);
    await this.writeDataToUnitFbfx14Htn(copyList, worksheet, 2);

  }

  //表1-4 总价措施项目清单与计价表
  async getUnitProjectZjcsHtQdJjBiao(constructId, singleId, unitId, worksheet,args) {

    //获取措施项目数据
    let qdByZjcs = PricingFileFindUtils.getQdByZjcs(constructId, singleId, unitId);
    let copyList = ConvertUtil.deepCopy(qdByZjcs);
    this.sortDispNo(copyList);
    // for (let i = 0; i < 95; i++) {
    //   let object = {};
    //   object["dispNo"] = i+"";
    //   object["bdCode"] = "la";
    //   object["bdName"] = "la";
    //   object["price"] = "la";
    //   object["rfee"] = "la";
    //   object["cfee"] = "la";
    //   copyList.push(object);
    // }
    await this.writeDataToUnitFbfx(copyList, worksheet,2,3,args);

    function total(data,htn) {
      let backTotal = 0;
      let total = 0;//结算价合计
      let differTotal = 0;
      let map = new Map();
      for (let i = 0; i < data.length; i++) {
        let datum = data[i];
        if (datum.kind == BranchProjectLevelConstant.qd) {
          total = NumberUtil.add(total,datum.total);
          if  (htn) {
            backTotal =NumberUtil.add(backTotal,datum.backTotal);
            differTotal += NumberUtil.subtract(datum.total, datum.backTotal);
          }

        }
      }
      if  (htn) {
        map.set(7,NumberUtil.numberScale2(backTotal));//合同价合计
        map.set(8,NumberUtil.numberScale2(total));//结算价合计
        map.set(10,NumberUtil.numberScale2(differTotal));//差额合计
      }else {
        map.set(7,NumberUtil.numberScale2(total));//合同价合计
      }

      return map;
    }
    // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test2.xlsx");
    await JieSuanExcelUtil.fillTotalContent(worksheet,total(copyList,args.htn),"合    计");

  }

  //表1-4 总价措施项目清单与计价表  合同外
  async getUnitProjectZjcsHtQdJjBiaoHtw(constructId, singleId, unitId, worksheet) {

    //获取措施项目数据
    let qdByZjcs = PricingFileFindUtils.getQdByZjcs(constructId, singleId, unitId);
    let copyList = ConvertUtil.deepCopy(qdByZjcs);
    this.sortDispNo(copyList);
    await this.writeDataToUnitFbfx14Htw(copyList, worksheet);

  }


  //表1-5 其他项目清单与计价汇总表
  async getUnitOtherProjectBiao(constructId, singleId, unitId, worksheet) {
    let otherProject = PricingFileFindUtils.getOtherProject(constructId, singleId, unitId);
    otherProject = ConvertUtil.deepCopy(otherProject);
    // for (let i = 0; i < 100; i++) {
    //   let object = {};
    //   object["dispNo"] = i+"";
    //   object["bdCode"] = "la";
    //   object["extraName"] = "la";
    //   object["price"] = "la";
    //   object["rfee"] = "la";
    //   object["cfee"] = "la";
    //   otherProject.push(object);
    // }
    await this.writeDataToUnitFbfx(otherProject, worksheet,2,2);
    await this.writeDataToUnitOtherProject(otherProject, worksheet);
    function total(data) {
      let heTongTotal = 0;
      let jieSuanTotal = 0;//结算价合计
      let map = new Map();
      for (let i = 0; i < data.length; i++) {
        let datum = data[i];
        if (ObjectUtils.isNotEmpty(datum.putOntotalFlag) && datum.putOntotalFlag) {
          heTongTotal =NumberUtil.add(heTongTotal,datum.jieSuanTotal);//这里的jieSuanTotal 表示预算合同价
          jieSuanTotal = NumberUtil.add(jieSuanTotal,datum.total);
        }
      }
      map.set(3,NumberUtil.numberScale2(heTongTotal));//合同金额合计
      map.set(5,NumberUtil.numberScale2(jieSuanTotal));//结算金额合计
      return map;
    }
    // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test2.xlsx");
    await JieSuanExcelUtil.fillTotalContent(worksheet,total(otherProject),"合    计");
  }

  async writeDataToUnitOtherProject(data, worksheet) {
    let countRow = -1;
    let htnPriceTotal = 0;
    let priceTotal = 0;
    for (let i = 0; i < worksheet._rows.length; i++) {
      let rowNum = worksheet._rows[i];
      if (rowNum.number >= 4) {  //从第四行开始填充
        countRow++;//从索引零开始 填充
        if (countRow >= data.length) break;
        for (let j = 0; j < rowNum._cells.length; j++) {
          let cell = rowNum._cells[j];
          if (cell.col == 1) {
            cell.value = data[countRow].dispNo;
          }
          if (cell.col == 2) {
            cell.value = data[countRow].extraName;
          }
          if (cell.col == 4) {
            // 合同金额
            if (data[countRow].extraName.includes('材料暂估价') || data[countRow].extraName.includes('设备暂估价')) {
              cell.value = '/';
            } else {
              cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(data[countRow].jieSuanTotal),2);
              if (ObjectUtils.isNotEmpty(data[countRow].dispNo) && !data[countRow].dispNo.includes('.') && cell.value != null) {
                htnPriceTotal += Number.parseFloat(cell.value);
              }
            }
          }
          if (cell.col == 6) {
            if (data[countRow].extraName.includes('材料暂估价') || data[countRow].extraName.includes('设备暂估价')) {
              cell.value = '/';
            } else {
              cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(data[countRow].total),2);
              if (ObjectUtils.isNotEmpty(data[countRow].dispNo) && !data[countRow].dispNo.includes('.') && cell.value != null) {
                priceTotal += Number.parseFloat(cell.value);
              }
            }
          }
        }
      }
    }
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '合    计');
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[3].value = htnPriceTotal.toFixed(2);
    row._cells[5].value = priceTotal.toFixed(2);
  }

  //表1-6 其他项目清单与计价汇总表
  async getUnitOtherProjectBiaoHtw(constructId, singleId, unitId, worksheet) {
    let otherProject = PricingFileFindUtils.getOtherProject(constructId, singleId, unitId);
    otherProject = ConvertUtil.deepCopy(otherProject);
    await this.writeDataToUnitOtherProjectHtw(otherProject, worksheet);
  }

  async writeDataToUnitOtherProjectHtw(data, worksheet) {
    let countRow = -1;
    let priceTotal = 0;
    let ele_find = ["材料暂估价","设备暂估价","专业工程暂估价"]
    for (let i = 0; i < data.length; i++) {
        if  (!ele_find.includes(data[i].extraName)){
          priceTotal = NumberUtil.add(priceTotal,data[i].total);
        }
    }

    await this.writeDataToUnitFbfx(data, worksheet,2,2);
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '合    计');
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[3].value = priceTotal.toFixed(2);
  }

  //表1-6 材料暂估单价及调整表
  async getUnitOtherProjectClzgjBiao(constructId, singleId, unitId, worksheet,argsParam) {
    // 目前预算中没有这个表  暂时不处理
    let args = {};
    args.constructId = constructId;
    args.singleId = singleId;
    args.unitId = unitId;
    args.kind = JieSuanRcjDifferenceEnum.ZANGUJIA.code;
    let data = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
    data = ConvertUtil.deepCopy(data);
    for (let i = 0; i < data.length; i++) {
      data[i].dispNo = i+1;
    }
    await this.writeDataToUnitFbfx(data, worksheet,3,2,argsParam);
    async function fill1_6Total(data,args) {
      let map = new Map();
      let totalZanGu = 0;
      let totalConfirm = 0;
      let totalDiffer = 0;//差额合计
      for (let i = 0; i < data.length; i++) {
        let differ = 0;
        if (args.deType == "22") {
            differ = args.simple?NumberUtil.multiply(NumberUtil.subtract(data[i].priceMarketTax,data[i].jieSuanPriceMarketTax),
                data[i].totalNumber):NumberUtil.multiply(NumberUtil.subtract(data[i].priceMarket,data[i].jieSuanPriceMarket),data[i].totalNumber);
        }else {
            differ = NumberUtil.multiply(NumberUtil.subtract(data[i].marketPrice,data[i].jieSuanMarketPrice),data[i].totalNumber);
        }
        totalZanGu = NumberUtil.add(totalZanGu, data[i].jieSuanTotal);
        totalConfirm = NumberUtil.add(totalConfirm, data[i].total);
        totalDiffer = NumberUtil.add(totalDiffer, differ);
      }
      map.set(9, totalZanGu);
      map.set(10, totalConfirm);
      map.set(13, totalDiffer);
      return map;
    }
    await JieSuanExcelUtil.fillTotalContent(worksheet,await fill1_6Total(data,argsParam),"合计");

  }

  //表1-7 计日工表
  async getUnitOtherProjectDayWorkBiao(constructId, singleId, unitId, worksheet) {
    let otherProjectDayWork = PricingFileFindUtils.getOtherProjectDayWork(constructId, singleId, unitId);
    otherProjectDayWork = ConvertUtil.deepCopy(otherProjectDayWork);
    await this.writeDataToUnitOtherProjectDayWork(otherProjectDayWork, worksheet);
  }

  async writeDataToUnitOtherProjectDayWork(data, worksheet) {
    //数据行 dataType 2  标题行 1
    let dataList = [];
    let headLines = data.filter(item => item.dataType == 1);
    let total = 0;
    let jieSuanTotal = 0;
    for (let i = 0; i < headLines.length; i++) {
      dataList.push(headLines[i]);
      let children = data.filter(item => item.parentId == headLines[i].sequenceNbr);
      dataList.push(...children);
      let xiaoJi = {};
      xiaoJi.xiaojiFlag = 1;  // 小计行标识
      if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.rg) {
        xiaoJi.worksName = '人工小计';
      } else if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.cl) {
        xiaoJi.worksName = '材料小计';
      } else if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.jx) {
        xiaoJi.worksName = '机械小计';
      }
      xiaoJi.total = headLines[i].total;
      xiaoJi.jieSuanTotal = headLines[i].jieSuanTotal;
      if (xiaoJi.total != null) {
        total += Number.parseFloat(xiaoJi.total);
      }
      if (xiaoJi.jieSuanTotal != null) {
        jieSuanTotal += Number.parseFloat(xiaoJi.jieSuanTotal);
      }
      dataList.push(xiaoJi);
    }
    await this.writeDataToUnitFbfx(dataList, worksheet,2,2);
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '总    计');
    //确定合计行的行数
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[9].value = jieSuanTotal.toFixed(2);
    row._cells[10].value = total.toFixed(2);
  }

  // 表1-8 计日工表   13规范
  async getUnitOtherProjectDayWorkBiaoGf(constructId, singleId, unitId, worksheet) {
    let otherProjectDayWork = PricingFileFindUtils.getOtherProjectDayWork(constructId, singleId, unitId);
    otherProjectDayWork = ConvertUtil.deepCopy(otherProjectDayWork);
    await this.writeDataToUnitOtherProjectDayWorkGf(otherProjectDayWork, worksheet);
  }

  async writeDataToUnitOtherProjectDayWorkGf(data, worksheet) {
    //数据行 dataType 2  标题行 1
    let dataList = [];
    let headLines = data.filter(item => item.dataType == 1);
    let total = 0;
    let jieSuanTotal = 0;
    for (let i = 0; i < headLines.length; i++) {
      dataList.push(headLines[i]);
      let children = data.filter(item => item.parentId == headLines[i].sequenceNbr);
      dataList.push(...children);
      let xiaoJi = {};
      xiaoJi.xiaojiFlag = 1;  // 小计行标识
      if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.rg) {
        xiaoJi.worksName = '人工小计';
      } else if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.cl) {
        xiaoJi.worksName = '材料小计';
      } else if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.jx) {
        xiaoJi.worksName = '机械小计';
      }
      xiaoJi.total = headLines[i].total;
      xiaoJi.jieSuanTotal = headLines[i].jieSuanTotal;
      if (xiaoJi.total != null) {
        total += Number.parseFloat(xiaoJi.total);
      }
      if (xiaoJi.jieSuanTotal != null) {
        jieSuanTotal += Number.parseFloat(xiaoJi.jieSuanTotal);
      }
      dataList.push(xiaoJi);
    }
    await this.writeDataToUnitFbfx(dataList, worksheet,3,2);
    // let headCount = 3;//表示表头行索引的最大值
    // let countRow = 0;//索引  记录当前数据写入的游标
    // for (let i = 0; i < dataList.length; i++) {
    //   let rowObject;
    //   headCount++;//记录当前数据插入行的索引
    //   rowObject = worksheet._rows[headCount];//从第五行开始写入数据
    //   countRow = i;
    //   for (let j = 0; j < rowObject._cells.length; j++) {
    //     let cell = rowObject._cells[j];
    //     if (ObjectUtils.isNotEmpty(dataList[countRow].xiaojiFlag) && dataList[countRow].xiaojiFlag == 1) {
    //       if (cell.col == 1) {
    //         cell.value = dataList[countRow].worksName;//名称
    //       }
    //       worksheet.unMergeCells([rowObject.number, 1, rowObject.number, 8]);
    //       worksheet.mergeCells([rowObject.number, 1, rowObject.number, 8]);
    //     } else {
    //       if (cell.col == 1) {
    //         cell.value = dataList[countRow].dispNo;
    //       }
    //       if (cell.col == 2) {
    //         cell.value = dataList[countRow].worksName;//名称
    //       }
    //       if (cell.col == 3) {
    //         cell.value = dataList[countRow].unit;//计量单位
    //       }
    //       if (cell.col == 4) {
    //         cell.value = dataList[countRow].jieSuanTentativeQuantity;//暂定数量
    //       }
    //       if (cell.col == 5) {
    //         cell.value = dataList[countRow].tentativeQuantity;//结算数量
    //       }
    //       if (cell.col == 7) {
    //         cell.value = dataList[countRow].jieSuanPrice;//合同综合单价
    //       }
    //       if (cell.col == 8) {
    //         cell.value = dataList[countRow].price;//综合单价
    //       }
    //     }
    //     if (cell.col == 9) {
    //       cell.value = dataList[countRow].jieSuanTotal;//暂定合价
    //     }
    //     if (cell.col == 10) {
    //       cell.value = dataList[countRow].total;//结算合价
    //     }
    //   }
    // }
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '总    计');
    //确定合计行的行数
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[8].value = jieSuanTotal.toFixed(2);
    row._cells[9].value = total.toFixed(2);
  }

  // 表1-9 计日工表
  async getUnitOtherProjectDayWorkBiaoGfHtw(constructId, singleId, unitId, worksheet) {
    let otherProjectDayWork = PricingFileFindUtils.getOtherProjectDayWork(constructId, singleId, unitId);
    otherProjectDayWork = ConvertUtil.deepCopy(otherProjectDayWork);
    await this.writeDataToUnitOtherProjectDayWorkGfHtw(otherProjectDayWork, worksheet);
  }

  async writeDataToUnitOtherProjectDayWorkGfHtw(data, worksheet) {
    //数据行 dataType 2  标题行 1
    let dataList = [];
    let headLines = data.filter(item => item.dataType == 1);
    let total = 0;
    let jieSuanTotal = 0;
    for (let i = 0; i < headLines.length; i++) {
      dataList.push(headLines[i]);
      let children = data.filter(item => item.parentId == headLines[i].sequenceNbr);
      dataList.push(...children);
      let xiaoJi = {};
      xiaoJi.xiaojiFlag = 1;  // 小计行标识
      if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.rg) {
        xiaoJi.worksName = '人工小计';
      } else if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.cl) {
        xiaoJi.worksName = '材料小计';
      } else if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.jx) {
        xiaoJi.worksName = '机械小计';
      }
      xiaoJi.total = headLines[i].total;
      xiaoJi.jieSuanTotal = headLines[i].jieSuanTotal;
      if (xiaoJi.total != null) {
        total += Number.parseFloat(xiaoJi.total);
      }
      if (xiaoJi.jieSuanTotal != null) {
        jieSuanTotal += Number.parseFloat(xiaoJi.jieSuanTotal);
      }
      dataList.push(xiaoJi);
    }

    await this.writeDataToUnitFbfx(dataList, worksheet,3,2);
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '总    计');
    //确定合计行的行数
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[8].value = total.toFixed(2);
  }

  //表1-7 暂列金额表
  async getUnitOtherProjectProvisionalBiao(constructId, singleId, unitId, worksheet) {
    let otherProjectProvisional = PricingFileFindUtils.getOtherProjectProvisional(constructId, singleId, unitId);
    otherProjectProvisional = ConvertUtil.deepCopy(otherProjectProvisional);
    await this.writeDataToUnitOtherProjectProvisional(otherProjectProvisional, worksheet);
  }

  async writeDataToUnitOtherProjectProvisional(data, worksheet) {
    let total = 0;
    for (let i = 0; i < data.length; i++) {
         total =NumberUtil.add(total,data[i].price);
    }
    await this.writeDataToUnitFbfx(data, worksheet,2,2);
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '合    计');
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[5].value = total.toFixed(2);
  }

  // 表1-7 专业工程结算价表
  async getUnitOtherProjectZygczgjBiao(constructId, singleId, unitId, worksheet,args) {
    let unitOtherProjectZygczgj = PricingFileFindUtils.getOtherProjectZygcZgjList(constructId, singleId, unitId);
    unitOtherProjectZygczgj = ObjectUtils.cloneDeep(unitOtherProjectZygczgj);
    await this.writeDataToUnitFbfx(unitOtherProjectZygczgj, worksheet, 2,2,args);
    await this.writeDataToUnitOtherProjectZygczgj(unitOtherProjectZygczgj, worksheet);
  }

  async writeDataToUnitOtherProjectZygczgj(data, worksheet) {
    let total = 0;
    let htnTotal = 0;
    let differenceTotal = 0;
    let headCount = 3;//表示表头行索引的最大值
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];
      countRow = i;
      htnTotal = NumberUtil.add(htnTotal,  data[countRow].jieSuanTotal);
      total = NumberUtil.add(total,  data[countRow].total);
      differenceTotal = NumberUtil.add(NumberUtil.subtract(data[countRow].total, data[countRow].jieSuanTotal),  data[countRow].difference);
    }
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '合    计');
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[4].value = htnTotal.toFixed(2);
    row._cells[5].value = total.toFixed(2);
    row._cells[7].value = differenceTotal.toFixed(2);
  }

  // 表1-8 专业工程结算价表
  async getUnitOtherProjectZygczgjBiaoHtw(constructId, singleId, unitId, worksheet) {
    let unitOtherProjectZygczgj = PricingFileFindUtils.getOtherProjectZygcZgjList(constructId, singleId, unitId);
    unitOtherProjectZygczgj = ObjectUtils.cloneDeep(unitOtherProjectZygczgj);
    await this.writeDataToUnitOtherProjectZygczgjHtw(unitOtherProjectZygczgj, worksheet);
  }

  async writeDataToUnitOtherProjectZygczgjHtw(data, worksheet) {
    let total = 0;
    for (let i = 0; i < data.length; i++) {
        total += NumberUtil.add(total, data[i].total);
    }
    await this.writeDataToUnitFbfx(data, worksheet, 2,2);
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '合    计');
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[5].value = total.toFixed(2);
  }

  // 表1-9 总承包服务费计价表
  async getUnitOtherProjectServiceCostBiao(constructId, singleId, unitId, worksheet) {
    let otherProjectServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);
    otherProjectServiceCost = ObjectUtils.cloneDeep(otherProjectServiceCost);
    await this.writeDataToUnitOtherProjectServiceCost(otherProjectServiceCost, worksheet);
  }

  async writeDataToUnitOtherProjectServiceCost(data, worksheet) {
    let total = 0;
    let headCount = 2;//表示表头行索引的最大值
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];
      countRow = i;
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) {
          cell.value = data[countRow].dispNo;
        }
        if (cell.col == 2) {
          cell.value = data[countRow].fxName;
        }
        if (cell.col == 3) {
          cell.value = data[countRow].xmje; //项目价值(元)
        }
        if (cell.col == 4) {
          cell.value = data[countRow].serviceContent; //服务内容
        }
        if (cell.col == 6) {
          cell.value = data[countRow].xmje;//计算基础
        }
        if (cell.col == 8) {
          cell.value = data[countRow].rate;//费率
        }
        if (cell.col == 9) {
          cell.value = data[countRow].jieSuanFwje;//金额（元）
          if (cell.value != null && data[countRow].dataType == 2) {
            total += Number.parseFloat(cell.value);
          }
        }
      }
    }
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '合    计');
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[8].value = total.toFixed(2);
  }

  // 表1-10 总承包服务费计价表
  async getUnitOtherProjectServiceCostBiaoHtw(constructId, singleId, unitId, worksheet) {
    let otherProjectServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);
    otherProjectServiceCost = ObjectUtils.cloneDeep(otherProjectServiceCost);
    await this.writeDataToUnitOtherProjectServiceCostHtw(otherProjectServiceCost, worksheet);
  }

  async writeDataToUnitOtherProjectServiceCostHtw(data, worksheet) {
    let total = 0;
    for (let i = 0; i < data.length; i++) {
          if (data[i].dataType === 2) {
            total = NumberUtil.add(total, data[i].fwje)
          }
    }
    await this.writeDataToUnitFbfx(data, worksheet,2,2);
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '合    计');
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[7].value = total.toFixed(2);
  }

  //表1-5 综合单价调整表
  async getUnitProjectZhdjTzBiao(constructId, singleId, unitId, worksheet) {

    //获取分部分项清单数据
    let allNodes = PricingFileFindUtils.getQdByfbfx(constructId, singleId, unitId);
    await this.writeDataToUnitFbfx(allNodes, worksheet, 4,4);
    //删掉最后一行无用行
    await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
  }


  //序号重排
  sortDispNo(list) {
    if (ObjectUtils.isNotEmpty(list)) {
      let disp = 1;
      list.forEach(qd => {
        qd.dispNo = disp;
        disp++;
      });

    }

  }


  /**
   * 分部分项数据填充格式化
   * @param data
   * @param worksheet
   * @param arg
   * @returns {Promise<void>}
   */
  async writeDataToUnitFbfx(data, worksheet, num,distanceReserve,args) {
    let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
    if (ObjectUtils.isNotEmpty(distanceReserve)) {
      copyDistance = distanceReserve;
    }
    let headCount = 3;//表示表头行索引的最大值
    if (ObjectUtils.isNotEmpty(num)) {
      headCount = num;
    }

    worksheet.rowDataTypelist = [{},];//维护sheet页 每一行的行属性  索引为行号 值为 { field:"data"、"head"}
    for (let i = 0; i <= headCount; i++) {
      worksheet.rowDataTypelist.splice(i+1,0,{field:"head",templateNum:i+1});
    }
    for (let i = headCount+1; i < worksheet._rows.length; i++) {
      if (worksheet._rows.length - i <= copyDistance) {
        worksheet.rowDataTypelist.splice(i+1,0,{field:"reserve",templateNum:i+1});
      }else {
        worksheet.rowDataTypelist.splice(i+1,0,{field:"blank"});
      }
    }

    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      headCount++;//记录当前数据插入行的索引
      let rowObject = worksheet._rows[headCount];
      let rowObjectLast = worksheet._rows[headCount-1];
      let rowNext = worksheet._rows[headCount+copyDistance];

      if (rowNext == null) {
        //插入新行后最后一行的合并单元格丢失
        /****插入一条新行**************/
        let list = [];
        //复制当前数据插入行的格式到增加行
        for (let m = 0; m < rowObjectLast._cells.length; m++) {
          list.push('');
        }
        rowNext = worksheet.insertRow(headCount + 1, list, 'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
        worksheet.rowDataTypelist.splice(headCount+1,0,{field:"data"});
        await JieSuanExcelUtil.resetMerges(worksheet, headCount + 1);
        let mergeMaps = new Map(Object.entries(worksheet._merges));
        for (let m = 0; m < rowNext._cells.length; m++) {
          //获取模板行的合并单元格
          let mergeName = JieSuanExcelUtil.getMergeName(worksheet._merges, rowObjectLast._cells[m]);
          if (mergeName != null) {
            let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
            worksheet.unMergeCells([rowNext.number, left, rowNext.number, right]);
            worksheet.mergeCells([rowNext.number, left, rowNext.number, right]);
          }
          rowNext._cells[m].style = rowObjectLast._cells[m].style;
        }
        /**end**插入一条新行**************/
        // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test2.xlsx");
      }
      if (ObjectUtils.isEmpty(rowObject)) { //应对保留行为0的这种情况
          rowObject = rowNext;
      }
      countRow = i;
      worksheet.rowDataTypelist[headCount+1].field = "data";
      await this.insertSheetData(data, rowObject, worksheet, countRow,args);
    }
  }

  async writeDataToUnitFbfxForZongHeDanJiaAnalysis(data, worksheet, num,distanceReserve,args) {

    //清除表格内容
    while (worksheet._rows.length > (num + 1)) {
      await JieSuanExcelUtil.deleteRowsForPoint(worksheet._rows.length,worksheet);
    }
    let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
    if (ObjectUtils.isNotEmpty(distanceReserve)) {
      copyDistance = distanceReserve;
    }
    let headCount = 3;//表示表头行索引的最大值
    if (ObjectUtils.isNotEmpty(num)) {
      headCount = num;
    }

    worksheet.rowDataTypelist = [{},];//维护sheet页 每一行的行属性  索引为行号 值为 { field:"data"、"head"}
    for (let i = 0; i <= headCount; i++) {
      worksheet.rowDataTypelist.splice(i+1,0,{field:"head",templateNum:i+1});
    }
    for (let i = headCount+1; i < worksheet._rows.length; i++) {
      if (worksheet._rows.length - i <= copyDistance) {
        worksheet.rowDataTypelist.splice(i+1,0,{field:"reserve",templateNum:i+1});
      }else {
        worksheet.rowDataTypelist.splice(i+1,0,{field:"blank"});
      }
    }
    let mergeMapsTemplate = new Map(Object.entries(worksheet.originSheet._merges));
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      headCount++;//记录当前数据插入行的索引
      let rowObject = worksheet._rows[headCount];
      let rowObjectLast = worksheet._rows[headCount-1];
      let rowNext = worksheet._rows[headCount+copyDistance];

      if (rowNext == null) {
        //插入新行后最后一行的合并单元格丢失
        /****插入一条新行**************/
        let list = [];
        //复制当前数据插入行的格式到增加行
        for (let m = 0; m < rowObjectLast._cells.length; m++) {
          list.push('');
        }
        rowNext = worksheet.insertRow(headCount + 1, list, 'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
        worksheet.rowDataTypelist.splice(headCount+1,0,{field:"blank"});
        await JieSuanExcelUtil.resetMerges(worksheet, headCount + 1);
        let mergeMaps = new Map(Object.entries(worksheet._merges));
        for (let m = 0; m < rowNext._cells.length; m++) {
          //获取模板行的合并单元格
          let mergeName = JieSuanExcelUtil.getMergeName(worksheet._merges, rowObjectLast._cells[m]);
          if (mergeName != null) {
            let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
            // worksheet.unMergeCells([rowNext.number, left, rowNext.number, right]);
            // worksheet.mergeCells([rowNext.number, left, rowNext.number, right]);
          }
          rowNext._cells[m].style = rowObjectLast._cells[m].style;
        }
        /**end**插入一条新行**************/
        // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test2.xlsx");
      }
      if (ObjectUtils.isEmpty(rowObject)) {
          rowObject = rowNext;
      }
      countRow = i;
      worksheet.rowDataTypelist[headCount+1].field = "data";
      let additionalPropertiesObject = null;
      for (let j = 0; j < data[countRow].length; j++) {
        if (ObjectUtils.isNotEmpty(data[countRow][j].additionalProperties)) {
          additionalPropertiesObject = data[countRow][j].additionalProperties;
          break;
        }
      }
      if (ObjectUtils.isNotEmpty(additionalPropertiesObject) && additionalPropertiesObject['breakFlag']) {
          worksheet.rowDataTypelist[headCount+1].breakFlag = true;
      }
      if (ObjectUtils.isNotEmpty(additionalPropertiesObject) && ObjectUtils.isNotEmpty(additionalPropertiesObject["dataType"])) {
          worksheet.rowDataTypelist[headCount+1].dataType = additionalPropertiesObject["dataType"];
      }
      if (ObjectUtils.isNotEmpty(additionalPropertiesObject) && ObjectUtils.isNotEmpty(additionalPropertiesObject["field"])) {
          worksheet.rowDataTypelist[headCount+1].field = additionalPropertiesObject["field"];
      }
      if (ObjectUtils.isNotEmpty(additionalPropertiesObject) && ObjectUtils.isNotEmpty(additionalPropertiesObject["height"])) {
        rowObject.height = additionalPropertiesObject["height"];
      }
      let rowTemplate = worksheet.originSheet._rows[additionalPropertiesObject.templateNum-1];
      for (let m = 0; m < rowObject._cells.length; m++) {
        //获取模板行的合并单元格
        let mergeName = JieSuanExcelUtil.getMergeName(worksheet.originSheet._merges,rowTemplate._cells[m]);
        if (mergeName != null) {
          let { top, left, bottom, right } = mergeMapsTemplate.get(mergeName).model;
          if (rowTemplate.number == bottom) {
            worksheet.unMergeCells([rowObject.number-(bottom-top), left, rowObject.number, right]);
            worksheet.mergeCells([rowObject.number-(bottom-top), left, rowObject.number, right]);
          }
        }
        rowObject._cells[m].style = rowTemplate._cells[m].style;
      }
      /**end**插入一条新行**************/
      // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test2.xlsx");
      await this.insertSheetData(data, rowObject, worksheet, countRow,args);
      // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\exceltest\\test.xlsx");
      // console.log("")
    }
    }


  async writeDataToUnitFbfx14Htw(data, worksheet, num) {
    let headCount = 3;//表示表头行索引的最大值
    if (ObjectUtils.isNotEmpty(num)) {
      headCount = num;
    }
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];//从第五行开始写入数据
      let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
      // let rowNext = worksheet._rows[headCount+copyDistance];
      let rowNext = worksheet._rows[headCount];
      if (rowNext == null) {
        //插入新行后最后一行的合并单元格丢失
        /****插入一条新行**************/
        let list = [];
        //复制当前数据插入行的格式到增加行
        for (let m = 0; m < rowObject._cells.length; m++) {
          list.push('');
        }
        rowNext = worksheet.insertRow(headCount + 1, list, 'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
        await JieSuanExcelUtil.resetMerges(worksheet, headCount + 2);
        let mergeMaps = new Map(Object.entries(worksheet._merges));
        for (let m = 0; m < rowNext._cells.length; m++) {
          //获取模板行的合并单元格
          let mergeName = JieSuanExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
          if (mergeName != null) {
            let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
            worksheet.unMergeCells([rowNext.number, left, rowNext.number, right]);
            worksheet.mergeCells([rowNext.number, left, rowNext.number, right]);
          }
          rowNext._cells[m].style = rowObject._cells[m].style;
        }
        /**end**插入一条新行**************/
      }
      countRow = i;
      await this.insertSheetDataHtw(data, rowObject, worksheet, countRow);
    }
  }

  async writeDataToUnitFbfx14Htn(data, worksheet, num) {
    let headCount = 3;//表示表头行索引的最大值
    if (ObjectUtils.isNotEmpty(num)) {
      headCount = num;
    }
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];//从第五行开始写入数据
      let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
      // let rowNext = worksheet._rows[headCount+copyDistance];
      let rowNext = worksheet._rows[headCount];
      if (rowNext == null) {
        //插入新行后最后一行的合并单元格丢失
        /****插入一条新行**************/
        let list = [];
        //复制当前数据插入行的格式到增加行
        for (let m = 0; m < rowObject._cells.length; m++) {
          list.push('');
        }
        rowNext = worksheet.insertRow(headCount + 1, list, 'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
        await JieSuanExcelUtil.resetMerges(worksheet, headCount + 2);
        let mergeMaps = new Map(Object.entries(worksheet._merges));
        for (let m = 0; m < rowNext._cells.length; m++) {
          //获取模板行的合并单元格
          let mergeName = JieSuanExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
          if (mergeName != null) {
            let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
            worksheet.unMergeCells([rowNext.number, left, rowNext.number, right]);
            worksheet.mergeCells([rowNext.number, left, rowNext.number, right]);
          }
          rowNext._cells[m].style = rowObject._cells[m].style;
        }
        /**end**插入一条新行**************/
      }
      countRow = i;
      await this.insertSheetDataHtn(data, rowObject, worksheet, countRow);
    }
  }

  async writeDataToUnitFbfx13(data, worksheet, num) {
    let headCount = 3;//表示表头行索引的最大值
    if (ObjectUtils.isNotEmpty(num)) {
      headCount = num;
    }
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];//从第五行开始写入数据
      let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
      // let rowNext = worksheet._rows[headCount+copyDistance];
      let rowNext = worksheet._rows[headCount];
      if (rowNext == null) {
        //插入新行后最后一行的合并单元格丢失
        /****插入一条新行**************/
        let list = [];
        //复制当前数据插入行的格式到增加行
        for (let m = 0; m < rowObject._cells.length; m++) {
          list.push('');
        }
        rowNext = worksheet.insertRow(headCount + 1, list, 'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
        await JieSuanExcelUtil.resetMerges(worksheet, headCount + 2);
        let mergeMaps = new Map(Object.entries(worksheet._merges));
        for (let m = 0; m < rowNext._cells.length; m++) {
          //获取模板行的合并单元格
          let mergeName = JieSuanExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
          if (mergeName != null) {
            let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
            worksheet.unMergeCells([rowNext.number, left, rowNext.number, right]);
            worksheet.mergeCells([rowNext.number, left, rowNext.number, right]);
          }
          rowNext._cells[m].style = rowObject._cells[m].style;
        }
        /**end**插入一条新行**************/
      }
      countRow = i;
      await this.insertSheetData(data, rowObject, worksheet, countRow);
    }
  }


  // async writeData13计价表(data, worksheet,arg) {
  //     let headCount = 3;//表示表头行索引的最大值
  //     let countRow = 0;//索引  记录当前数据写入的游标
  //     for (let i = 0; i < data.length; i++) {
  //         let rowObject;
  //         headCount++;//记录当前数据插入行的索引
  //         rowObject = worksheet._rows[headCount];//从第五行开始写入数据
  //         let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
  //         // let rowNext = worksheet._rows[headCount+copyDistance];
  //         let rowNext = worksheet._rows[headCount];
  //         if (rowNext == null) {
  //             //插入新行后最后一行的合并单元格丢失
  //             /****插入一条新行**************/
  //             let list=[];
  //             //复制当前数据插入行的格式到增加行
  //             // for (let m = 0; m < rowObject._cells.length; m++) {
  //             //     list.push("");
  //             // }
  //             rowNext = worksheet.insertRow(headCount+1,list,'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
  //             await JieSuanExcelUtil.resetMerges(worksheet,headCount+2);
  //             let mergeMaps = new Map(Object.entries(worksheet._merges));
  //             for (let m = 0;m<rowNext._cells.length;m++){
  //                 //获取模板行的合并单元格
  //                 let mergeName = JieSuanExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
  //                 if (mergeName!=null){
  //                     let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
  //                     worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
  //                     worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
  //                 }
  //                 rowNext._cells[m].style = rowObject._cells[m].style;
  //             }
  //             /**end**插入一条新行**************/
  //         }
  //         countRow = i;
  //         this.insertSheetData(data,rowObject,worksheet);
  //     }
  // }

  async writeDataToUnitRcj(data, worksheet, num) {
    let headCount = 2;//表示表头行索引的最大值
    if (ObjectUtils.isNotEmpty(num)) {
      headCount = num;
    }
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];//从第五行开始写入数据
      let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
      // let rowNext = worksheet._rows[headCount+copyDistance];
      let rowNext = worksheet._rows[headCount];
      if (rowNext == null) {
        //插入新行后最后一行的合并单元格丢失
        /****插入一条新行**************/
        let list = [];
        //复制当前数据插入行的格式到增加行
        for (let m = 0; m < rowObject._cells.length; m++) {
          list.push('');
        }
        rowNext = worksheet.insertRow(headCount + 1, list, 'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
        await JieSuanExcelUtil.resetMerges(worksheet, headCount + 2);
        let mergeMaps = new Map(Object.entries(worksheet._merges));
        for (let m = 0; m < rowNext._cells.length; m++) {
          //获取模板行的合并单元格
          let mergeName = JieSuanExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
          if (mergeName != null) {
            let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
            worksheet.unMergeCells([rowNext.number, left, rowNext.number, right]);
            worksheet.mergeCells([rowNext.number, left, rowNext.number, right]);
          }
          rowNext._cells[m].style = rowObject._cells[m].style;
        }
        /**end**插入一条新行**************/
      }
      countRow = i;
      await this.insertSheetData(data, rowObject, worksheet, countRow);
    }
  }


  //插入数据
  async insertSheetData(data, rowObject, worksheet, countRow,args) {
    let simple = false;
    if (ObjectUtils.isNotEmpty(args)) {
        simple = args["simple"];
    }
    let deType = null;
    if (ObjectUtils.isNotEmpty(args)) {
        deType = args["deType"];
    }

    if (worksheet.name == '表1-1工程特征分析表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].name;
        if (cell.col == 3) cell.value = data[countRow].context;
      }
    }

    if (worksheet.name == '表1-2工程项目信息表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow+1;
        if (cell.col == 2) cell.value = data[countRow].name;
        if (cell.col == 3) cell.value = data[countRow].remark;
      }
    }

    if (worksheet.name == '表1-1 建设项目费用汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].projectName;
        if (cell.col == 3) cell.value = data[countRow].jck;
        if (cell.col == 4) {
          if (ObjectUtils.isNotEmpty(data[countRow].levelType)) {
            cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(NumberUtil.add(data[countRow].qtxmzlje, data[countRow].qtxmzygczgj)),2);//暂列金额 及专业工程暂估价
          }else {
            cell.value = null;
          }
        }
        if (cell.col == 5) {
          if (ObjectUtils.isNotEmpty(data[countRow].levelType)) {
            cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(data[countRow].totalMaterialsZgj),2);//材料暂估价
          }else {
            cell.value = null;
          }
        }
        if (cell.col == 6) {
          if (ObjectUtils.isNotEmpty(data[countRow].levelType)) {
            cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(NumberUtil.add(data[countRow].safeFee,data[countRow].jcaqwmsgfhj)),2);
          }else {
            cell.value = null;
          }
        }
        if (cell.col == 7) {
          if (ObjectUtils.isNotEmpty(data[countRow].levelType)) {
            cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(NumberUtil.add(data[countRow].gfee,data[countRow].jcgfhj)),2);
          }else {
            cell.value = null;
          }
        }
        if (cell.col == 9) {
          if (ObjectUtils.isNotEmpty(data[countRow].levelType)) {
            cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(data[countRow].jsjc),2);//人材机调整合计
          }else {
            cell.value = null;
          }
        }
      }
    }
    if (worksheet.name == '表1-2 建设项目费用汇总表(沧州)') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].projectName;
        if (cell.col == 3) cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(data[countRow].jck),2);
        if (cell.col == 4) {
          if (ObjectUtils.isNotEmpty(data[countRow].levelType)) {
              cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(NumberUtil.add(data[countRow].qtxmzlje, data[countRow].qtxmzygczgj)),2);//暂列金额 及专业工程暂估价
          }else {
              cell.value = null;
          }
        }
        if (cell.col == 5) {
          if (ObjectUtils.isNotEmpty(data[countRow].levelType)) {
              cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(data[countRow].totalMaterialsZgj),2);//材料暂估价
          }else {
              cell.value = null;
          }
        }
        if (cell.col == 6) {
            if (ObjectUtils.isNotEmpty(data[countRow].levelType)) {
                cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(NumberUtil.add(data[countRow].safeFee,data[countRow].jcaqwmsgfhj)),2);
            }else {
                cell.value = null;
            }
        }
        if (cell.col == 7) {
          if (ObjectUtils.isNotEmpty(data[countRow].levelType)) {
              cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(NumberUtil.add(data[countRow].gfee,data[countRow].jcgfhj)),2);
          }else {
              cell.value = null;
          }
        }
        if (cell.col == 9) {
            if (ObjectUtils.isNotEmpty(data[countRow].levelType)) {
                cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(data[countRow].jsjc),2);//人材机调整合计
            }else {
                cell.value = null;
            }
        }
      }
    }

    if (worksheet.name == "表1-3 综合单价分析表") {
        for (let j = 0; j < rowObject._cells.length; j++) {
          let cell = rowObject._cells[j];
          //非合并单元格 才进行赋值 否则会将主单元格的值覆盖
          if (!(ObjectUtils.isNotEmpty(cell._value) && ObjectUtils.isNotEmpty(cell._value._master))) {
            cell.value = data[countRow][j].value;
          }
        }
    }

    if (worksheet.name == '表1-3 工程项目竣工结算汇总表' || worksheet.name == '表1-4 工程项目竣工结算汇总表(沧州)') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].projectName;
        if (cell.col == 3) cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(data[countRow].total),2);
        if (cell.col == 4) cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(data[countRow].jck),2);

      }
    }
    if (worksheet.name == '表1-2 建设项目竣工结算汇总表' || worksheet.name == '表1-3 建设项目竣工结算汇总表(沧州)' ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].projectName;
        if (cell.col == 3) cell.value = data[countRow].jieSuanPrice;
        if (cell.col == 5) cell.value = NumberUtil.numberScale2(NumberUtil.add(data[countRow].safeFee,data[countRow].jcaqwmsgfhj))
        if (cell.col == 6) cell.value = NumberUtil.numberScale2(NumberUtil.add(data[countRow].gfee,data[countRow].jcgfhj))

      }
    }

    if (worksheet.name == '表1-1 单项工程竣工结算汇总表' ) {

      if('13规范报表' == worksheet.lanMuName  ){
        for (let j = 0; j < rowObject._cells.length; j++) {
          let cell = rowObject._cells[j];
          if (cell.col == 1) cell.value = countRow + 1;
          if (cell.col == 2) cell.value = data[countRow].upName;
          if (cell.col == 3) cell.value = data[countRow].gczj;
          if (cell.col == 5) cell.value = NumberUtil.numberScale2(NumberUtil.add(data[countRow].safeFee,data[countRow].jcaqwmsgfhj))
          if (cell.col == 6) cell.value = NumberUtil.numberScale2(NumberUtil.add(data[countRow].gfee,data[countRow].jcgfhj))

        }
      }else {
        for (let j = 0; j < rowObject._cells.length; j++) {
          let cell = rowObject._cells[j];
          if (cell.col == 1) cell.value = countRow + 1;
          if (cell.col == 2) cell.value = data[countRow].upName;
          if (cell.col == 4) cell.value = data[countRow].gczj;
          if (cell.col == 5) cell.value = data[countRow].jck

        }
      }

    }
    if (worksheet.name == '表1-1 合同外单项工程竣工结算汇总表' ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].upName;
        if (cell.col == 3) cell.value = data[countRow].gczj;
        if (cell.col == 5) cell.value = data[countRow].safeFee
        if (cell.col == 6) cell.value = data[countRow].gfee

      }
    }
    if (worksheet.name == '表1-1 单位工程竣工结算汇总表' ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].name;
        if (cell.col == 4) cell.value = data[countRow].jieSuanPrice;
        if (cell.col == 6) cell.value = data[countRow].price

      }
    }

    if (worksheet.name == '表1-1 单位工程竣工结算汇总表（含价差）'   ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].name;
        if (cell.col == 5) cell.value = data[countRow].price;

      }
    }
    if (worksheet.name == '表1-1 单位工程费用汇总表'   ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].name;
        if (cell.col == 5) cell.value = data[countRow].price;

      }
    }
    if (worksheet.name == '表1-6 材料暂估单价及调整表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].totalNumber;
        if (cell.col == 5) cell.value = data[countRow].totalNumber;
        //简易计税取含税价
        // 单价：暂估取合同市场价  确认取 结算单价
        // 合价：暂估取暂估数量*暂估单价      确认取 确认数量*确认单价
        // 差额：单价取确认单价-暂估单价   合价取确认差额*确认数量
        //暂估单价
        if (cell.col == 6) {
          if (deType == "22") {
            cell.value = simple?data[countRow].jieSuanPriceMarketTax:data[countRow].jieSuanPriceMarket;
          }else {
            cell.value = data[countRow].jieSuanMarketPrice;
          }
        }
        //暂估确认
        if (cell.col == 8) {
          if (deType == "22") {
              cell.value = simple?data[countRow].priceMarketTax:data[countRow].priceMarket;//priceMarket 结算不含税单价
          }else {
              cell.value = data[countRow].marketPrice;
          }
        }
        //暂估合价
        if (cell.col == 9) {
          // cell.value = simple?NumberUtil.multiply(data[countRow].totalNumber,data[countRow].jieSuanPriceMarketTax)
          //     :NumberUtil.multiply(data[countRow].totalNumber,data[countRow].jieSuanPriceMarket);
          cell.value = data[countRow].jieSuanTotal;//合同价合计
        }
        //合价确认
        if (cell.col == 10) {
          // cell.value = simple?NumberUtil.multiply(data[countRow].totalNumber,data[countRow].priceMarketTax)
          //     :NumberUtil.multiply(data[countRow].totalNumber,data[countRow].priceMarket);
          cell.value = data[countRow].total;
        }
        //差额单价
        if (cell.col == 11) {
          if (deType == "22") {
              cell.value = simple?NumberUtil.subtract(data[countRow].priceMarketTax,data[countRow].jieSuanPriceMarketTax):
                NumberUtil.subtract(data[countRow].priceMarket,data[countRow].jieSuanPriceMarket);
          }else {
              cell.value = NumberUtil.subtract(data[countRow].marketPrice,data[countRow].jieSuanMarketPrice);
          }
        }
        //差额合价
        if (cell.col == 13) {
          if (deType == "22") {
            cell.value = simple?NumberUtil.multiply(NumberUtil.subtract(data[countRow].priceMarketTax,data[countRow].jieSuanPriceMarketTax),data[countRow].totalNumber)
                :NumberUtil.multiply(NumberUtil.subtract(data[countRow].priceMarket,data[countRow].jieSuanPriceMarket),data[countRow].totalNumber);
          }else {
            cell.value = NumberUtil.multiply(NumberUtil.subtract(data[countRow].marketPrice,data[countRow].jieSuanMarketPrice),data[countRow].totalNumber);
          }
        }
        //备注
        if (cell.col == 14) cell.value = data[countRow].remark;

      }
    }

    if (worksheet.name == '表1-6 材料(工程设备)暂估价及调整表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow+1;
        // if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].totalNumber;
        if (cell.col == 5) cell.value = data[countRow].totalNumber;
        //简易计税取含税价
        // 单价：暂估取合同市场价  确认取 结算单价
        // 合价：暂估取暂估数量*暂估单价      确认取 确认数量*确认单价
        // 差额：单价取确认单价-暂估单价   合价取确认差额*确认数量
        //暂估单价
        if (cell.col == 6) {
          if (deType == "22") {
            cell.value = simple?data[countRow].jieSuanPriceMarketTax:data[countRow].jieSuanPriceMarket;
          }else {
            cell.value = data[countRow].jieSuanMarketPrice;
          }
        }
        //暂估确认
        if (cell.col == 8) {
          if (deType == "22") {
            cell.value = simple?data[countRow].priceMarketTax:data[countRow].priceMarket;//priceMarket 结算不含税单价
          }else {
            cell.value = data[countRow].marketPrice;
          }
        }
        //暂估合价
        if (cell.col == 9) {
          // cell.value = simple?NumberUtil.multiply(data[countRow].totalNumber,data[countRow].jieSuanPriceMarketTax)
          //     :NumberUtil.multiply(data[countRow].totalNumber,data[countRow].jieSuanPriceMarket);
          cell.value = data[countRow].jieSuanTotal;//合同价合计
        }
        //合价确认
        if (cell.col == 10) {
          // cell.value = simple?NumberUtil.multiply(data[countRow].totalNumber,data[countRow].priceMarketTax)
          //     :NumberUtil.multiply(data[countRow].totalNumber,data[countRow].priceMarket);
          cell.value = data[countRow].total;
        }
        //差额单价
        if (cell.col == 11) {
          if (deType == "22") {
            cell.value = simple?NumberUtil.subtract(data[countRow].priceMarketTax,data[countRow].jieSuanPriceMarketTax):
                NumberUtil.subtract(data[countRow].priceMarket,data[countRow].jieSuanPriceMarket);
          }else {
            cell.value = NumberUtil.subtract(data[countRow].marketPrice,data[countRow].jieSuanMarketPrice);
          }
        }
        //差额合价
        if (cell.col == 13) {
          if (deType == "22") {
            cell.value = simple?NumberUtil.multiply(NumberUtil.subtract(data[countRow].priceMarketTax,data[countRow].jieSuanPriceMarketTax),data[countRow].totalNumber)
                :NumberUtil.multiply(NumberUtil.subtract(data[countRow].priceMarket,data[countRow].jieSuanPriceMarket),data[countRow].totalNumber);
          }else {
            cell.value = NumberUtil.multiply(NumberUtil.subtract(data[countRow].marketPrice,data[countRow].jieSuanMarketPrice),data[countRow].totalNumber);
          }
        }
        //备注
        if (cell.col == 14) cell.value = data[countRow].remark;
      }
    }

    if (worksheet.name == '表1-6 其他项目清单与计价汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) {
          cell.value = data[countRow].dispNo;
        }
        if (cell.col == 2) {
          cell.value = data[countRow].extraName;
        }
        if (cell.col == 4) {
          // 合同金额
          cell.value = data[countRow].total;
        }
        if (cell.col == 5) {
          // 合同金额
          cell.value = data[countRow].description;
        }
      }
    }

    if (worksheet.name == '表1-7 暂列金额表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) {
          cell.value = data[countRow].dispNo;
        }
        if (cell.col == 2) {
          cell.value = data[countRow].name;
        }
        if (cell.col == 4) {
          cell.value = data[countRow].unit;
        }
        if (cell.col == 5) {
          cell.value = data[countRow].provisionalSum;//暂列金额
        }
        if (cell.col == 7) {
          cell.value = data[countRow].description;//备注
        }
      }
    }

    if (worksheet.name == '表1-7 专业工程结算价表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) {
          cell.value = data[countRow].dispNo;
        }
        if (cell.col == 2) {
          cell.value = data[countRow].name;
        }
        if (cell.col == 3) {
          cell.value = data[countRow].content;
        }
        if (cell.col == 5) {
          cell.value = data[countRow].jieSuanTotal;//暂估金额
        }
        if (cell.col == 6) {
          cell.value = data[countRow].total;//结算金额
        }
        if (cell.col == 8) {
          cell.value = NumberUtil.subtract(data[countRow].total, data[countRow].jieSuanTotal);//差额±(元）

        }
        if (cell.col == 9) {
          cell.value = data[countRow].description;//备注
        }
      }
    }

    if (worksheet.name == '表1-8 专业工程结算价表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) {
          cell.value = data[countRow].dispNo;
        }
        if (cell.col == 2) {
          cell.value = data[countRow].name; //工程名称
        }
        if (cell.col == 3) {
          cell.value = data[countRow].content;//工程内容
        }
        if (cell.col == 5) {
          cell.value = data[countRow].total;//结算金额(元）
        }
        if (cell.col == 7) {
          cell.value = data[countRow].description;//备注
        }
      }
    }

    if (worksheet.name == '表1-8 计日工表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (ObjectUtils.isNotEmpty(data[countRow].xiaojiFlag) && data[countRow].xiaojiFlag == 1) {
          if (cell.col == 1) {
            cell.value = data[countRow].worksName;//名称
          }
          worksheet.unMergeCells([rowObject.number, 1, rowObject.number, 8]);
          worksheet.mergeCells([rowObject.number, 1, rowObject.number, 8]);
        } else {
          if (cell.col == 1) {
            cell.value = data[countRow].dispNo;
          }
          if (cell.col == 2) {
            cell.value = data[countRow].worksName;//名称
          }
          if (cell.col == 3) {
            cell.value = data[countRow].unit;//计量单位
          }
          if (cell.col == 4) {
            cell.value = data[countRow].jieSuanTentativeQuantity;//暂定数量
          }
          if (cell.col == 5) {
            cell.value = data[countRow].tentativeQuantity;//结算数量
          }
          if (cell.col == 7) {
            cell.value = data[countRow].jieSuanPrice;//合同综合单价
          }
          if (cell.col == 8) {
            cell.value = data[countRow].price;//综合单价
          }
        }
        if (cell.col == 9) {
          cell.value = data[countRow].jieSuanTotal;//暂定合价
        }
        if (cell.col == 10) {
          cell.value = data[countRow].total;//结算合价
        }
      }
    }


    if (worksheet.name == '表1-9 计日工表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (ObjectUtils.isNotEmpty(data[countRow].xiaojiFlag) && data[countRow].xiaojiFlag == 1) {
          if (cell.col == 1) {
            cell.value = data[countRow].worksName;//名称
          }
          worksheet.unMergeCells([rowObject.number, 1, rowObject.number, 7]);
          worksheet.mergeCells([rowObject.number, 1, rowObject.number, 7]);
        } else {
          if (cell.col == 1) {
            cell.value = data[countRow].dispNo;
          }
          if (cell.col == 2) {
            cell.value = data[countRow].worksName;//名称
          }
          if (cell.col == 3) {
            cell.value = data[countRow].unit;//单位
          }
          if (cell.col == 5) {
            cell.value = data[countRow].tentativeQuantity;//结算数量
          }
          if (cell.col == 7) {
            cell.value = data[countRow].price;//综合单价
          }
        }
        if (cell.col == 9) {
          cell.value = data[countRow].total;//结算合价
        }
      }
    }

      if (worksheet.name == '表1-10 总承包服务费计价表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) {
          cell.value = data[countRow].dispNo;
        }
        if (cell.col == 2) {
          cell.value = data[countRow].fxName;
        }
        if (cell.col == 3) {
          cell.value = data[countRow].xmje; //项目价值(元)
        }
        if (cell.col == 4) {
          cell.value = data[countRow].serviceContent; //服务内容
        }
        if (cell.col == 5) {
          cell.value = data[countRow].xmje;//计算基础
        }
        if (cell.col == 7) {
          cell.value = data[countRow].rate;//费率
        }
        if (cell.col == 8) {
          cell.value = data[countRow].fwje;//金额（元）
        }
      }
    }


    if (worksheet.name == '表1-10 规费、税金项目结算表（不含价差）'|| worksheet.name == '表1-11 规费、税金项目结算表（含价差）' ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].name;
        if (cell.col == 3) cell.value = data[countRow].instructions;
        if (cell.col == 6) cell.value = data[countRow].calculateFormula;
        if (cell.col == 7) cell.value = data[countRow].rate;
        if (cell.col == 8) cell.value = data[countRow].price;

      }
    }

    if ( worksheet.name == '表1-11 规费、税金项目清单与计价表' ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 3) cell.value = data[countRow].name;
        if (cell.col == 7) cell.value = data[countRow].instructions;
        if (cell.col == 8) cell.value = data[countRow].rate
        if (cell.col == 10) cell.value = data[countRow].price;

      }
    }

    if (worksheet.name == '表1-16 增值税进项税额计算汇总表'||worksheet.name == '表1-13 增值税进项税额计算汇总表' ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 3) cell.value = data[countRow].name;
        if (cell.col == 5) cell.value = data[countRow].price;

      }
    }

    if (worksheet.name == '表1-11 承包人提供主要材料和工程设备一览表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;

        let temp = '';
        if (ObjectUtils.isNotEmpty(data[countRow].specification)) {
          temp = temp + '、' + data[countRow].specification;
        }
        if (cell.col == 2) cell.value = data[countRow].materialName + temp;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].jieSuanDifferenceQuantity;
        if (cell.col == 5) cell.value = data[countRow].jieSuanPriceLimit;
        if (cell.col == 6) cell.value = data[countRow].jieSuanBasePrice;
        if (cell.col == 8) cell.value = data[countRow].marketPrice;
        if (cell.col == 10) cell.value = data[countRow].jieSuanPrice;
        if (cell.col == 11) cell.value = data[countRow].description;

      }
    }
    if (worksheet.name == '表1-12 承包人提供主要材料和工程设备一览表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].specification;
        if (cell.col == 5) cell.value = data[countRow].unit;
        if (cell.col == 6) cell.value = data[countRow].totalNumber;
        if (cell.col == 8) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 9) cell.value = data[countRow].jieSuanMarketPrice;
        if (cell.col == 10) cell.value = data[countRow].jieSuanTotal;
        if (cell.col == 12) cell.value = data[countRow].jieSuanPriceDifferencSum;

      }
    }



    if (worksheet.name == '表1-13 承包人提供主要材料和工程设备一览表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;

        let temp = '';
        if (ObjectUtils.isNotEmpty(data[countRow].specification)) {
          temp = temp + '、' + data[countRow].specification;
        }
        if (cell.col == 2) cell.value = data[countRow].materialName + temp;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].jieSuanDifferenceQuantity;
        if (cell.col == 5) cell.value = data[countRow].jieSuanPriceLimit;
        if (cell.col == 6) cell.value = data[countRow].jieSuanBasePrice;
        if (cell.col == 8) cell.value = data[countRow].marketPrice;
        if (cell.col == 10) cell.value = data[countRow].jieSuanPrice;
        if (cell.col == 11) cell.value = data[countRow].description;

      }
    }
    if (worksheet.name == '表1-14 承包人提供主要材料和工程设备一览表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].specification;
        if (cell.col == 5) cell.value = data[countRow].unit;
        if (cell.col == 6) cell.value = data[countRow].totalNumber;
        if (cell.col == 8) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 9) cell.value = data[countRow].jieSuanMarketPrice;
        if (cell.col == 10) cell.value = data[countRow].jieSuanTotal;
        if (cell.col == 12) cell.value = data[countRow].jieSuanPriceDifferencSum;

      }
    }
    /*if (worksheet.name == '表1-12 主材汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].specification;
        if (cell.col == 5) cell.value = data[countRow].unit;
        if (cell.col == 6) cell.value = data[countRow].totalNumber;
        if (cell.col == 8) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 9) cell.value = data[countRow].jieSuanMarketPrice;
        if (cell.col == 10) cell.value = data[countRow].jieSuanTotal;
        if (cell.col == 12) cell.value = data[countRow].jieSuanPriceDifferencSum;

      }
    }*/

    if (worksheet.name == '表1-2 分部分项合同清单工程量及结算工程量对比表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].bdCode;
        if (cell.col == 3) cell.value = data[countRow].bdName;
        if (cell.col == 4) cell.value = data[countRow].unit;
        if (cell.col == 5) cell.value = data[countRow].backQuantity;
        if (cell.col == 7) cell.value = data[countRow].quantity;
        if (cell.col == 9) cell.value = data[countRow].quantityDifference;
        if (cell.col == 10) cell.value = data[countRow].quantityDifferenceProportion;

      }
    }


    if (worksheet.name == '表1-3 分部分项工程和单价措施项目清单与计价表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].bdCode;
        if (cell.col == 3) cell.value = data[countRow].name;
        if (cell.col == 4) cell.value = data[countRow].projectAttr;
        if (cell.col == 5) cell.value = data[countRow].unit;
        if (cell.col == 6) cell.value = data[countRow].backQuantity;
        if (cell.col == 7) cell.value = data[countRow].quantity;
        if (cell.col == 9) cell.value = data[countRow].quantityDifference;
        if (cell.col == 10) cell.value = data[countRow].price;
        if (cell.col == 11) cell.value = data[countRow].backTotal;
        if (cell.col == 13) cell.value = data[countRow].total;
        if (cell.col == 14) cell.value = NumberUtil.subtract(data[countRow].total, data[countRow].backTotal);

      }
    }

    if (worksheet.name == '表1-4 总价措施项目清单与计价表') {

      if ('13规范报表' === args.lanMuName) {
        for (let j = 0; j < rowObject._cells.length; j++) {
          let cell = rowObject._cells[j];
          if(args.htn){
            if (cell.col == 1) cell.value = data[countRow].dispNo;
            if (cell.col == 2) cell.value = data[countRow].bdCode;
            if (cell.col == 3) cell.value = data[countRow].name;
            if (cell.col == 4) cell.value = data[countRow].formulaRemark;
            if (cell.col == 6) cell.value = data[countRow].formulaRemark;
            if (cell.col == 7) cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(data[countRow].backTotal),2);
            if (cell.col == 9) cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(data[countRow].total),2);
            if (cell.col == 10) cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.subtract(data[countRow].total, data[countRow].backTotal),2);
            if (cell.col == 11) cell.value = data[countRow].description;
          }else {
            if (cell.col == 1) cell.value = data[countRow].dispNo;
            if (cell.col == 2) cell.value = data[countRow].bdCode;
            if (cell.col == 3) cell.value = data[countRow].name;
            if (cell.col == 4) cell.value = data[countRow].formulaRemark;
            if (cell.col == 6) cell.value = data[countRow].formulaRemark;
            if (cell.col == 7) cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(data[countRow].total),2);
            if (cell.col == 9) cell.value = data[countRow].description;
          }

        }
      } else {
        for (let j = 0; j < rowObject._cells.length; j++) {
          let cell = rowObject._cells[j];
          if (cell.col == 1) cell.value = data[countRow].dispNo;
          if (cell.col == 2) cell.value = data[countRow].bdCode;
          if (cell.col == 3) cell.value = data[countRow].name;
          if (cell.col == 4) cell.value = data[countRow].formulaRemark;
          if (cell.col == 7) cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(data[countRow].backTotal),2);
          if (cell.col == 8) cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.numberScale2(data[countRow].total),2);
          if (cell.col == 9) cell.value = JieSuanExcelUtil._roundAndPad(NumberUtil.subtract(data[countRow].total, data[countRow].backTotal),2);
          if (cell.col == 11) cell.value = data[countRow].description;

        }
      }
    }

    if (worksheet.name == '表1-4 总价措施项目清单与计价表13') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].bdCode;
        if (cell.col == 3) cell.value = data[countRow].name;
        if (cell.col == 4) cell.value = data[countRow].formulaRemark;
        if (cell.col == 7) cell.value = data[countRow].price;
        if (cell.col == 11) cell.value = data[countRow].description;

      }
    }

    if (worksheet.name == "表1-7 计日工表") {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (ObjectUtils.isNotEmpty(data[countRow].xiaojiFlag) && data[countRow].xiaojiFlag == 1) {
          if (cell.col == 1) {
            cell.value = data[countRow].worksName;//名称
          }
          worksheet.unMergeCells([rowObject.number, 1, rowObject.number, 8]);
          worksheet.mergeCells([rowObject.number, 1, rowObject.number, 8]);
        } else {
          if (cell.col == 1) {
            cell.value = data[countRow].dispNo;
          }
          if (cell.col == 2) {
            cell.value = data[countRow].worksName;//名称
          }
          if (cell.col == 3) {
            cell.value = data[countRow].unit;//计量单位
          }
          if (cell.col == 4) {
            cell.value = data[countRow].jieSuanTentativeQuantity;//暂定数量
          }
          if (cell.col == 5) {
            cell.value = data[countRow].tentativeQuantity;//结算数量
          }
          if (cell.col == 7) {
            cell.value = data[countRow].jieSuanPrice;//合同综合单价
          }
          if (cell.col == 8) {
            cell.value = data[countRow].price;//综合单价
          }
        }
        if (cell.col == 10) {
          cell.value = data[countRow].jieSuanTotal;//暂定合价
        }
        if (cell.col == 11) {
          cell.value = data[countRow].total;//结算合价
        }
      }
    }

    if (worksheet.name == '表1-2 分部分项工程和单价措施项目清单与计价表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if(args.htn){
          if (cell.col == 1) cell.value = data[countRow].dispNo;
          if (cell.col == 2) cell.value = data[countRow].bdCode;
          if (cell.col == 3) cell.value = data[countRow].name;
          if (cell.col == 4) cell.value = data[countRow].projectAttr;
          if (cell.col == 5) cell.value = data[countRow].unit;
          if (cell.col == 6) cell.value = data[countRow].backQuantity;
          // if (cell.col == 7) cell.value = data[countRow].quantity;
          // if (cell.col == 9) cell.value = data[countRow].quantityDifference;
          if (cell.col == 7) cell.value = data[countRow].price;
          if (cell.col == 9) cell.value = data[countRow].backTotal;
          // if (cell.col == 13) cell.value = data[countRow].total;
          // if (cell.col == 14) cell.value = NumberUtil.subtract(data[countRow].total, data[countRow].backTotal);
        }else {
          if (cell.col == 1) cell.value = data[countRow].dispNo;
          if (cell.col == 2) cell.value = data[countRow].bdCode;
          if (cell.col == 3) cell.value = data[countRow].name;
          if (cell.col == 4) cell.value = data[countRow].projectAttr;
          if (cell.col == 5) cell.value = data[countRow].unit;
          if (cell.col == 6) cell.value = data[countRow].quantity;
          // if (cell.col == 7) cell.value = data[countRow].quantity;
          // if (cell.col == 9) cell.value = data[countRow].quantityDifference;
          if (cell.col == 8) cell.value = data[countRow].price;
          if (cell.col == 9) cell.value = data[countRow].total;
          // if (cell.col == 13) cell.value = data[countRow].total;
          // if (cell.col == 14) cell.value = NumberUtil.subtract(data[countRow].total, data[countRow].backTotal);
        }
      }
    }


    if (worksheet.name == '表1-5 综合单价调整表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].bdCode;
        if (cell.col == 3) cell.value = data[countRow].bdName;
        if (cell.col == 4) cell.value = data[countRow].price;
        if (cell.col == 5) cell.value = data[countRow].rfee;
        if (cell.col == 6) cell.value = data[countRow].cfee;
        if (cell.col == 8) cell.value = data[countRow].jfee;
        if (cell.col == 9) cell.value = NumberUtil.add(data[countRow].managerFee, data[countRow].profitFee);

      }
    }

    if (worksheet.name == '表1-8 单位工程人材机汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) {
          if (deType == "22") {
            cell.value = simple?data[countRow].jieSuanPriceMarketTax:data[countRow].jieSuanPriceMarket;
          }else {
            cell.value = data[countRow].jieSuanMarketPrice;
          }
        }
        if (cell.col == 5) cell.value = data[countRow].jieSuanTotalNumber;//合同数量
        if (cell.col == 6) {    //合同合价
          if (deType == "22") {
              cell.value = simple?data[countRow].jieSuanPriceMarketTotal:data[countRow].jieSuanPriceMarketTotal;
          }else {
              cell.value = data[countRow].jieSuanTotal;
          }
        }

        if (cell.col == 8) cell.value = data[countRow].totalNumber;
        if (cell.col == 9) cell.value = data[countRow].total;//结算不含税市场价合价  和含税用的同一字段
        if (cell.col == 10) cell.value = data[countRow].jieSuanPriceDifferencSum;
        if (cell.col == 12) cell.value = data[countRow].remark;
      }
    }


    if (worksheet.name == '表1-9 工程议价材料表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].unit;

        if (cell.col == 5) cell.value = data[countRow].totalNumber;//结算数量

        //结算单价
        if (cell.col == 7) {
          if (deType == "22") {
              cell.value = simple?data[countRow].priceMarketTax:data[countRow].priceMarket;//priceMarket 结算不含税单价
          }else {
              cell.value = data[countRow].marketPrice;
          }
        }
        //合同单价
        if (cell.col == 8) {
          if (deType == "22") {
            cell.value = simple?data[countRow].jieSuanPriceMarketTax:data[countRow].jieSuanPriceMarket;
          }else {
            cell.value = data[countRow].jieSuanMarketPrice;
          }
        }

        if (cell.col == 9) cell.value = !ObjectUtils.isEmpty(data[countRow].riskAmplitudeRangeMin)&&!ObjectUtils.isEmpty(data[countRow].riskAmplitudeRangeMax)
            ?data[countRow].riskAmplitudeRangeMin + "~" + data[countRow].riskAmplitudeRangeMax:null;
        if (cell.col == 10) cell.value = data[countRow].jieSuanPriceDifferenc;
        if (cell.col == 12) cell.value = data[countRow].jieSuanPriceDifferencSum;
      }
    }


    if (worksheet.name == '表1-10 人材机调整明细表-1') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].specification;
        if (cell.col == 4) cell.value = data[countRow].unit;

        if (cell.col == 6) cell.value = data[countRow].jieSuanDifferenceQuantity;

        if (cell.col == 8) cell.value = data[countRow].jieSuanPriceDifferencSum;

      }
    }


    if (worksheet.name == '表1-11 人材机调整明细表-2') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].specification;
        if (cell.col == 4) cell.value = data[countRow].unit;
        if (cell.col == 5) {
          if (deType == "22") {
              cell.value = simple?data[countRow].jieSuanPriceMarketTax:data[countRow].jieSuanPriceMarket;
          }else {
              cell.value = data[countRow].jieSuanMarketPrice;
          }
        }
        if (cell.col == 6) cell.value = data[countRow].jieSuanBasePrice;//人材机基期价  12、22字段同
        if (cell.col == 8) {
          if (deType == "22") {
              cell.value = simple?data[countRow].priceMarketTax:data[countRow].priceMarket;//priceMarket 结算不含税单价
          }else {
              cell.value = data[countRow].marketPrice;
          }
        }
        if (cell.col == 9) cell.value = data[countRow].jieSuanPriceDifferenc;//单位价差
        if (cell.col == 10) cell.value = data[countRow].jieSuanDifferenceQuantity;
        if (cell.col == 12) cell.value = data[countRow].jieSuanPriceDifferencSum;
      }
    }

    if (worksheet.name == '表1-12 主材汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].specification;
        if (cell.col == 5) cell.value = data[countRow].unit;
        if (cell.col == 6) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 8) cell.value = data[countRow].totalNumber;
        if (cell.col == 9) {
          if (deType == "22") {
            cell.value = simple?data[countRow].jieSuanPriceMarketTax:data[countRow].jieSuanPriceMarket;
          }else {
            cell.value = data[countRow].jieSuanMarketPrice;
          }
        }
        if (cell.col == 10) cell.value = data[countRow].total;
        if (cell.col == 12) cell.value = data[countRow].jieSuanPriceDifferencSum;

      }

    }

    if (worksheet.name == '表1-13 甲方供应材料表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].unit;
        if (cell.col == 5) {
          if (deType == "22") {
            cell.value = simple?data[countRow].jieSuanPriceMarketTax:data[countRow].jieSuanPriceMarket;
          }else {
            cell.value = data[countRow].jieSuanMarketPrice;
          }
        }
        if (cell.col == 6) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 8) cell.value = data[countRow].totalNumber;
        if (cell.col == 9) cell.value = data[countRow].total;
        if (cell.col == 10) cell.value = data[countRow].jieSuanAdminRate;
        if (cell.col == 12) cell.value =!ObjectUtils.isEmpty(data[countRow].jieSuanAdminRate)?NumberUtil.numberScale2(NumberUtil.multiply(
            NumberUtil.multiply(data[countRow].total,data[countRow].jieSuanAdminRate),0.01)):null ;
        if (cell.col == 13) cell.value = !ObjectUtils.isEmpty(data[countRow].jieSuanAdminRate)?NumberUtil.numberScale2(NumberUtil.subtract(data[countRow].total,NumberUtil.
        numberScale2(NumberUtil.multiply(
            NumberUtil.multiply(data[countRow].total,data[countRow].jieSuanAdminRate),0.01)))):null;

      }
    }


    if (worksheet.name == '表1-14 甲供材料汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].specification;
        if (cell.col == 5) cell.value = data[countRow].unit;
        if (cell.col == 6) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 8) {
          if (deType == "22") {
            cell.value = simple?data[countRow].jieSuanPriceMarketTax:data[countRow].jieSuanPriceMarket;
          }else {
            cell.value = data[countRow].jieSuanMarketPrice;
          }
        }
        if (cell.col == 9) cell.value = data[countRow].donorMaterialNumber;
        if (cell.col == 10) cell.value = data[countRow].total;
        if (cell.col == 12) cell.value = data[countRow].remark;

      }
    }


    if (worksheet.name == '表1-12 发包人提供材料和工程设备一览表' || worksheet.name == '表1-10 发包人提供材料和工程设备一览表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = !ObjectUtils.isEmpty(data[countRow].specification) ?data[countRow].materialName +
            data[countRow].specification:data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].donorMaterialNumber;
        if (cell.col == 5) cell.value = data[countRow].marketPrice;
        if (cell.col == 7) cell.value = null;
        if (cell.col == 8) cell.value = data[countRow].deliveryLocation;
        if (cell.col == 9) cell.value = data[countRow].remark;

      }
    }

    if (worksheet.name == '表1-15 材料、机械、设备增值税计算表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = !ObjectUtils.isEmpty(data[countRow].specification) ?data[countRow].materialName +
            data[countRow].specification:data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 5) cell.value = data[countRow].taxRemoval;
        if (cell.col == 6) cell.value = data[countRow].jieSuanPrice;
        if (cell.col == 7) cell.value = data[countRow].jieSuanTotal;

        if (cell.col == 9) cell.value = NumberUtil.numberScale2(NumberUtil.subtract(data[countRow].jieSuanPrice,NumberUtil.multiply(NumberUtil.multiply(data[countRow].jieSuanPrice,
            data[countRow].taxRemoval),0.01)));
        if (cell.col == 10) cell.value = NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.numberScale2(NumberUtil.subtract(data[countRow].jieSuanPrice,NumberUtil.multiply(NumberUtil.multiply(data[countRow].jieSuanPrice,
            data[countRow].taxRemoval),0.01))),data[countRow].jieSuanTotalNumber));
        if (cell.col == 11) cell.value = data[countRow].jieSunJxTotal;
        if (cell.col == 13) cell.value = null;

      }
    }

    if (worksheet.name == '表1-12 材料、机械、设备增值税计算表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = !ObjectUtils.isEmpty(data[countRow].specification) ?data[countRow].materialName +
            data[countRow].specification:data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 5) cell.value = data[countRow].taxRemoval;
        if (cell.col == 6) cell.value = data[countRow].marketPrice;
        if (cell.col == 7) cell.value = data[countRow].jieSuanPriceMarketTotal;

        if (cell.col == 9) cell.value = NumberUtil.numberScale2(NumberUtil.subtract(data[countRow].marketPrice,NumberUtil.multiply(NumberUtil.multiply(data[countRow].marketPrice,
            data[countRow].taxRemoval),0.01)));
        if (cell.col == 10) cell.value = NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.numberScale2(NumberUtil.subtract(data[countRow].jieSuanPriceMarketTotal,NumberUtil.multiply(NumberUtil.multiply(data[countRow].jieSuanPriceMarketTotal,
            data[countRow].taxRemoval),0.01))),data[countRow].jieSuanPriceMarketTotal));
        if (cell.col == 11) cell.value = data[countRow].jieSuanJxTotal;
        if (cell.col == 13) cell.value = NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.numberScale2(NumberUtil.subtract(data[countRow].jieSuanPriceMarketTotal,NumberUtil.multiply(NumberUtil.multiply(data[countRow].jieSuanPriceMarketTotal,
            data[countRow].taxRemoval),0.01))),data[countRow].jieSuanPriceMarketTotal)*0.09);

      }
    }


    if (worksheet.name == '表1-1 单位工程人材机汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = !ObjectUtils.isEmpty(data[countRow].specification) ?data[countRow].materialName +
            "  "+data[countRow].specification:data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].totalNumber;

        if (cell.col == 6) {   //合同/确认单价
          if (deType == "22") {
            cell.value = simple?data[countRow].priceMarketTax:data[countRow].priceMarket;
          }else {
            cell.value = data[countRow].marketPrice;
          }
        }
        if (cell.col == 8) cell.value = data[countRow].total;
      }
    }


    if (worksheet.name == '表1-2 单位工程人材机价差汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = !ObjectUtils.isEmpty(data[countRow].specification) ?data[countRow].materialName +
            data[countRow].specification:data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;

        if (cell.col == 4) {
          if (deType == "22") {
            cell.value = simple?data[countRow].priceMarketTax:data[countRow].priceMarket;
          }else {
            cell.value = data[countRow].marketPrice;
          }
        }

        if (cell.col == 6) cell.value = data[countRow].totalNumber;
        if (cell.col == 8) cell.value = data[countRow].total;
        if (cell.col == 9) cell.value = data[countRow].jieSuanPriceDifferencSum;
        if (cell.col == 10) cell.value = data[countRow].remark;
      }
    }


    if (worksheet.name == '表1-3 人材机价差调整表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = !ObjectUtils.isEmpty(data[countRow].specification) ?data[countRow].materialName +
            "  "+data[countRow].specification:data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].totalNumber;
        if (cell.col == 5) {  //投标单价   基准单价是基期价，投标单价是市场价
            if (deType == "22") {
              cell.value = simple?data[countRow].priceMarketTax:data[countRow].priceMarket;
            }else {
              cell.value = data[countRow].marketPrice;
            }
        }


        if (cell.col == 6) cell.value = data[countRow].jieSuanBasePrice;//基准单价
        if (cell.col == 8) {   //结算单价
          if (deType == "22") {
              cell.value = simple?data[countRow].jieSuanPriceMarketTax:data[countRow].jieSuanPriceMarket;
          }else {
              cell.value = data[countRow].jieSuanPrice;//12结算单价
          }
        }
        if (cell.col == 9) cell.value = !ObjectUtils.isEmpty(data[countRow].riskAmplitudeRangeMin)&&!ObjectUtils.isEmpty(data[countRow].riskAmplitudeRangeMax)
            ?data[countRow].riskAmplitudeRangeMin + "~" + data[countRow].riskAmplitudeRangeMax:null;
        if (cell.col == 10) cell.value = data[countRow].jieSuanPriceDifferenc;
        if (cell.col == 12) cell.value = data[countRow].jieSuanStagePriceDifferencSum;
        if (cell.col == 13) cell.value = data[countRow].remark;
      }
    }

    if (worksheet.name == '表1-4 主材汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].specification;
        if (cell.col == 5) cell.value = data[countRow].unit;


        if (cell.col == 6) cell.value = data[countRow].totalNumber;
        if (cell.col == 8) {  //合同/确认单价
          if (deType == "22") {
            cell.value = simple?data[countRow].priceMarketTax:data[countRow].priceMarket;
          }else {
            cell.value = data[countRow].marketPrice;
          }
        }
        if (cell.col == 9) {
          cell.value = data[countRow].total;
        }
        if (cell.col == 11) cell.value = data[countRow].jieSuanPriceDifferencSum;

      }
    }

  }



  async insertSheetDataHtw(data, rowObject, worksheet, countRow) {
    if (worksheet.name == '表1-4 总价措施项目清单与计价表14htw') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].bdCode;
        if (cell.col == 3) cell.value = data[countRow].name;
        if (cell.col == 4) cell.value = data[countRow].formulaRemark;
        if (cell.col == 7) cell.value = data[countRow].total;
        if (cell.col == 9) cell.value = data[countRow].description;

      }
    }
  }


  async insertSheetDataHtn(data, rowObject, worksheet, countRow) {

    if (worksheet.name == '表1-4 总价措施项目清单与计价表14htn') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].bdCode;
        if (cell.col == 3) cell.value = data[countRow].name;
        if (cell.col == 4) cell.value = data[countRow].formulaRemark;
        if (cell.col == 7) cell.value = data[countRow].price;
        if (cell.col == 11) cell.value = data[countRow].description;

      }
    }
  }


  findJieSuanSingleTypeEnumByCode(code) {
    let value;
    for (let singleTypeEnum in JieSuanSingleTypeEnum) {
      if (JieSuanSingleTypeEnum[singleTypeEnum].code == code) {
        value = JieSuanSingleTypeEnum[singleTypeEnum];
        return value;
      }
    }
  }
  async getOrganization(param){

    //获取工程项目对象
    let projectObjById = PricingFileFindUtils.getProjectObjById(param.constructId);
    //获取基本信息
    let constructProjectJBXX = projectObjById.constructProjectJBXX;
    if (constructProjectJBXX == null) {
      return ;
    }
    let t = constructProjectJBXX.find(i=>i.name === "工程名称");
    let array = new Array();
    let project = {};
    project.name = t.name;
    project.remark = t.remark;
    array.push(project) ;


    let organizationInstructions = PricingFileFindUtils.getOrganizationInstructions(1,param.constructId,null,null);
    let project1 = {};
    project1.name = "总说明";
    project1.remark = organizationInstructions.context;
    array.push(project1) ;

    return array;

  }

}


JieSuanExportQueryService.toString = () => '[class JieSuanExportQueryService]';
module.exports = JieSuanExportQueryService;
