const {Organ, Gene} = require("@valuation/rules-engine");
const {JieSuanRcjStageUtils} = require("../../utils/JieSuanRcjStageUtils");
const {PricingFileFindUtils} = require("../../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../../electron/utils/ObjectUtils");
const {NumberUtil} = require("../../../../common/NumberUtil");
const JieSuanRcjDifferenceEnum = require("../../enum/JieSuanRcjDifferenceEnum");
const {baseRules, getjieSuanStageDifferenceQuantityRule, getjieSuanMarketPriceTotal, getjieSuanPriceMarketTotal,
	getjieSuanTotalNumber, getjieSuanTotal, gettotalNumber, gettotal, getjieSuanJxTotal, getjxTotal
} = require("./baseRules");
const EE = require("../../../../core/ee");


//价差合计
let getJieSuanPriceDifferencSum=(seq)=>{
	return Organ.create({name:seq+"_jieSuanPriceDifferencSum",description:seq+"价差合计",gene:Gene.from(["CONTEXT",seq+"_DZQZA",seq+"_jieSuanValuetWeightB",seq+"_jieSuanBasePriceF0",seq+"_jieSuanCurrentPriceFt"],
			(ctx)=>{
			if (ObjectUtils.isEmpty(ctx[seq+"_jieSuanCurrentPriceFt"]) || ObjectUtils.isEmpty(ctx[seq+"_jieSuanBasePriceF0"])){
				return ;
			}
			let {isStage,dependRcjList,unit,method,constructId, singleId, unitId,rcj,arg} = ctx.CONTEXT;

			let { service } = EE.app;
			//P0  费用汇总工程造价下结算金额
			let args = {
				constructId:constructId,
				singleId:singleId,
				unitId:unitId,
				levelType:arg.levelType
			}
			let  P0 = service.jieSuanProject.jieSuanUnitCostSummaryService.getJzByCostSummary(args);

			//Ft.f0 手动输入  每一期都有
			//P0*(A + (B*(Ft/f0))-1)
			let Ft_f0 = NumberUtil.divide(ctx[seq+"_jieSuanCurrentPriceFt"],ctx[seq+"_jieSuanBasePriceF0"]);
			let B_Ft_f0 = NumberUtil.multiply(ctx[seq+"_jieSuanValuetWeightB"],Ft_f0);
			let A_B_Ft_f0_1 = NumberUtil.subtract(NumberUtil.add(ctx[seq+"_DZQZA"],B_Ft_f0),1);
			return NumberUtil.multiply(P0.price,A_B_Ft_f0_1);
			})});
}


//材料合同合价
let getjieSuanMarketPriceSum=(seq)=>{
	return Organ.create({name:seq+"_jieSuanMarketPriceSum",description:"材料合同合价",gene:Gene.from(["CONTEXT"],
			({CONTEXT})=>{
				let {dependRcjList} = CONTEXT;
				return dependRcjList.reduce((sum, item) => {
					return sum +item.jieSuanTotal;
				}, 0);
		})})
};
//变值权重B
let getjieSuanValuetWeightB=(seq)=>{
	return Organ.create({name:seq+"_jieSuanValuetWeightB",description:"变值权重B",gene:Gene.from(["CONTEXT",seq+"_jieSuanPriceMarketTotal",seq+"_jieSuanStageDifferenceQuantity"],
			(cxt)=>{
				let {isStage,dependRcjList,unit,method,constructId, singleId, unitId,rcj,arg} = cxt.CONTEXT;
				let {num,adjustMethod} = arg;
				let { service } = EE.app;

				//B=a材料合同合价/工程造价下合同金额
				let args = {
					constructId:constructId,
					singleId:singleId,
					unitId:unitId,
					levelType:arg.levelType
				}
				let  price = service.jieSuanProject.jieSuanUnitCostSummaryService.getJzByCostSummary(args);
				let B = NumberUtil.divide(cxt[seq+"_jieSuanPriceMarketTotal"],price.price);//结算金额取:price
				if (!isStage){
					return B;
				}
				//a材料结算工程量
				let sum = dependRcjList.reduce((sum, item) => {
					return sum +item.totalNumber;
				}, 0);
				//Bi=（a材料第i期调差工程量/a材料结算工程量）/B
				let differenceQuantity = NumberUtil.divide(cxt[seq+"_jieSuanStageDifferenceQuantity"],sum);
				let Bi = NumberUtil.divide(differenceQuantity,B);
				return Bi;
			})})
}
//定值权重A
let getDZQZA=(seq)=>{
	return Organ.create({name:seq+"_DZQZA",description:"定值权重A",gene:Gene.from([seq+"_jieSuanValuetWeightB"],
			(cxt)=>{
				return NumberUtil.numberScale(1-cxt[seq+"_jieSuanValuetWeightB"],5)
			})})
}

//基本价格指数F0
let getjieSuanBasePriceF0=(seq)=>{
	return Organ.create({name:seq+"_jieSuanBasePriceF0",description:"基本价格指数F0",gene:Gene.from(["CONTEXT"],
			(cxt)=>{
				let {isStage,unit,arg,rcj} = cxt.CONTEXT;
				let {num,adjustMethod} = arg;

				//不分期
				if (!isStage){
					return  rcj.jieSuanBasePriceF0;
				}
				//是否分次
				let scopeList = JieSuanRcjStageUtils.rcjAdjustMethod(unit,rcj.kind,rcj.adjustMethod);
				if (ObjectUtils.isNotEmpty(scopeList)){
					//获取到次对应的期数
					if (ObjectUtils.isNotEmpty(arg.num)){
						//let periods = JieSuanRcjStageUtils.generateNaturalNumbers(scopeList[arg.num-1]);
						return rcj.jieSuanRcjDifferenceTypeList[arg.num-1].jieSuanBasePriceF0
					}

				}
			})})
}
//现行价格指数Ft
let getjieSuanCurrentPriceFt=(seq)=>{
	return Organ.create({name:seq+"_jieSuanCurrentPriceFt",description:"现行价格指数Ft",gene:Gene.from(["CONTEXT"],
			(cxt)=>{
				let {isStage,unit,arg,rcj} = cxt.CONTEXT;
				let {num,adjustMethod} = arg;
				//let rcjJieSuanPrice = rcj.jieSuanRcjDifferenceTypeList.find(k =>k.rcjDifferenceType ==adjustMethod);
				//不分期
				if (!isStage){
					return rcj.jieSuanCurrentPriceFt;
				}
				//是否分次
				let scopeList = JieSuanRcjStageUtils.rcjAdjustMethod(unit,rcj.kind,rcj.adjustMethod);
				if (ObjectUtils.isNotEmpty(scopeList)){
					//获取到次对应的期数
					if (ObjectUtils.isNotEmpty(arg.num)){
						//let periods = JieSuanRcjStageUtils.generateNaturalNumbers(scopeList[arg.num-1]);
						return rcj.jieSuanRcjDifferenceTypeList[arg.num-1].jieSuanCurrentPriceFt
					}
				}
			})})
}




let  calculateRules = {
	jieSuanPriceDifferencSum:getJieSuanPriceDifferencSum,
	jieSuanStageDifferenceQuantity:getjieSuanStageDifferenceQuantityRule,
	jieSuanMarketPriceSum:getjieSuanMarketPriceSum,
	jieSuanValuetWeightB:getjieSuanValuetWeightB,
	DZQZA:getDZQZA,
	jieSuanBasePriceF0:getjieSuanBasePriceF0,
	jieSuanCurrentPriceFt:getjieSuanCurrentPriceFt,
	jieSuanTotalNumber:getjieSuanTotalNumber,
	jieSuanPriceMarketTotal:getjieSuanPriceMarketTotal,
	jieSuanMarketPriceTotal:getjieSuanPriceMarketTotal,
	jieSuanTotal:getjieSuanTotal,
	jieSuanTotalNumber:getjieSuanTotalNumber,
	totalNumber:gettotalNumber,
	total:gettotal,
	jieSuanJxTotal:getjieSuanJxTotal,
	jxTotal:getjxTotal,


};

//字段
let method4RulesField_22 = {
	"jieSuanValuetWeightB":4,// 变值权重B
	"jieSuanStageDifferenceQuantity":4,//调差工程量
	"jieSuanPriceDifferencSum":2,	// 价差合计
	"jieSuanMarketPriceSum":2,	// 材料合同合价
	"jieSuanBasePriceF0":4,	// 基本价格指数F0
	"jieSuanCurrentPriceFt":4,	// 现行价格指数Ft
	"jieSuanMarketPriceTotal":2,	// 合同市场价合价
	"jieSuanPriceMarketTotal":2,	// 合同不含税市场价合价(一般计税)/合同不含税市场价合价(一般计税)
	"jieSuanTotalNumber":4,	// 合同不含税市场价合价(一般计税)/合同不含税市场价合价(一般计税)
	"jieSuanTotal":2,	// 合同市场价合计
	"totalNumber":4,//结算数量
	"total":2,//结算市场价合价
	"jxTotal":2,//合同进项税额
	"jieSuanJxTotal":2,//结算进项税额
	"jxTotal":2,//合同进项税额

}


/**
 * 根据类型获取到具体的列表数据
 */
let getMethod4Rule=(ctx)=>{

	let {kind} = ctx.arg;

	let {is2022} = ctx;
	let rulesField = method4RulesField_22;
	// if (is2022){
	// 	rulesField = method4RulesField_22;
	// }
	ctx.calculateField = rulesField;
	ctx.rules = {...baseRules};
	ctx.calculateRules = calculateRules;

}


module.exports = {getMethod4Rule};
