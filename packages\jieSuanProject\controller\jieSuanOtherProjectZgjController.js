// const {ResponseData} = require("../../../electron/utils/ResponseData");
// const {Controller} = require("../../../core");
// const JieSuanOtherProjectZgjService = require("../service/jieSuanOtherProjectZgjService");
// class JieSuanOtherProjectZgjController extends Controller{
//
//
//     /**
//      * 构造函数
//      * @param ctx
//      */
//     constructor(ctx) {
//         super(ctx);
//         this.jieSuanOtherProjectZgjService = new JieSuanOtherProjectZgjService(ctx);
//     }
//
//     /**
//      * 专业工程暂估价 操作
//      * @param arg 清单册code
//      * @returns {Promise<*>}
//      */
//     async otherProjectZygcZgj(arg) {
//
//        await this.jieSuanOtherProjectZgjService.otherProjectZygcZgj(arg);
//
//         if (arg.operateType !==1){
//
//             await this.service.management.sycnTrigger("unitDeChange");
//             await this.service.management.trigger("itemChange");
//         }
//         return ResponseData.success(null);
//     }
//
// }
//
// JieSuanOtherProjectZgjController.toString = () => '[class JieSuanOtherProjectZgjController]';
// module.exports = JieSuanOtherProjectZgjController;