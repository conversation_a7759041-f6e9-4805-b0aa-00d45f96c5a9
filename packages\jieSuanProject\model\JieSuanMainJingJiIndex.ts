/**
 * 主要经济指标
 */
export class JieSuanMainJingJiIndex {

    /**
     *
     */
    public id : string;
    /**
     * 序号
     */
    public disp : number;
    /**
     *  科目名称
     */
    public name : number;//调整法类型
    /**
     * 造价
     */
    public price : number;
    /**
     * 单方造价
     */
    public unitPrice : number;

    /**
     * 占造价比（%）
     */
    public proportionPrice : number;

    /**
     * 计算口径
     */
    public indexCountCaliber :number;


    constructor(id: string, disp: number, name: number, price: number, unitPrice: number, proportionPrice: number, indexCountCaliber: number) {
        this.id = id;
        this.disp = disp;
        this.name = name;
        this.price = price;
        this.unitPrice = unitPrice;
        this.proportionPrice = proportionPrice;
        this.indexCountCaliber = indexCountCaliber;
    }
}