<!--
 * @Descripttion: 
 * @Author: 
 * @Date: 2024-07-05 15:09:05
 * @LastEditors: wangru
 * @LastEditTime: 2025-06-12 18:25:18
-->
<template>
  <div class="operate-container hover-scrollbar-thumb">
    <div
      class="operate"
      :style="operateStyle"
    >
      <div class="operate-scroll">
        <template
          v-for="(item, index) in filterOperateList"
          :key="index"
        >
          <div
            class="operate-item"
            :class="{ disabled: item.disabled }"
            v-if="!item.isPolymerizeShow"
            @click="!item.disabled && !item.type ? setEmit(item) : ''"
          >
            <a-tooltip
              placement="bottom"
              v-model:visible="item.decVisible"
              @visibleChange="val => infoVisibleChange(val, item)"
            >
              <template #title>
                <span style="font-size: 12px; text-decoration: underline">{{
                item.label
              }}</span>
                <p
                  v-if="item.infoDec"
                  style="font-size: 10px"
                >
                  {{ item.infoDec }}
                </p>
              </template>

              <!-- <template v-if="['select', 'selectRadio'].includes(item.type)">
              <a-dropdown @visibleChange="setEmit(item)" trigger="click">
                <div
                  class="select-radio"
                  v-if="['selectRadio'].includes(item.type)"
                >
                  <div class="select-head">
                    <icon-font
                      :type="item.iconType"
                      class="iconType"
                      :style="item.iconStyle ?? {}"
                    />
                    <div class="label" :style="item.labelStyle ?? {}">
                      {{ item.label }}
                    </div>
                    <icon-font
                      type="icon-xiala"
                      style="color: rgba(51, 51, 51, 0.39)"
                    />
                  </div>
                  <div class="sub-name">
                    {{
                      item.options.find(opt => opt.kind === item.value)?.name ||
                      ''
                    }}
                  </div>
                </div>
                <div v-else>
                  <OperateItemTitle :item="item"></OperateItemTitle>
                </div>
                <template #overlay>
                  <a-menu>
                    <a-menu-item
                      v-for="selectitem in item.options"
                      :key="selectitem.kind"
                      :disabled="!selectitem.isValid"
                      @click="setSelectEmit(selectitem, item)"
                    >
                      <a-checkbox
                        v-if="['selectCheck'].includes(item.type)"
                        :checked="selectitem.kind == checkedIndex"
                      ></a-checkbox>
                      <a-radio
                        v-if="['selectRadio'].includes(item.type)"
                        :checked="selectitem.kind == item.value"
                      ></a-radio>
                      <span
                        v-if="['select-color'].includes(item.name)"
                        class="color-border"
                        :class="`${selectitem.kind}`"
                      ></span>
                      {{ selectitem.name }}
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template> -->
              <template v-if="['selectCheck'].includes(item.type)">
                <a-dropdown
                  @visibleChange="setEmit(item)"
                  trigger="click"
                  v-model:visible="visible"
                >
                  <div>
                    <template v-if="'filter-list' == item.name">
                      <icon-font
                        :type="
                        filteringStates ? 'icon-guolv-xiaoxi' : item.iconType
                      "
                        class="iconType"
                        :style="item.iconStyle ?? {}"
                      />
                    </template>
                    <template v-else>
                      <icon-font
                        :type="item.iconType"
                        class="iconType"
                        :style="item.iconStyle ?? {}"
                      />
                    </template>
                    <div
                      v-if="systemConfig.functionalArea.isExpand"
                      class="label"
                      :style="item.labelStyle ?? {}"
                    >
                      {{ item.label }}
                    </div>
                  </div>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item
                        v-for="selectitem in item.options"
                        :key="selectitem.kind"
                        :disabled="!selectitem.isValid"
                        @click="setSelectEmit(selectitem, item)"
                      >
                        <a-checkbox
                          v-if="['selectCheck'].includes(item.type)"
                          :checked="selectitem.kind == checkedIndex"
                        ></a-checkbox>
                        <span
                          v-if="['select-color'].includes(item.name)"
                          class="color-border"
                          :class="`${selectitem.kind}`"
                        ></span>
                        {{ selectitem.name }}
                      </a-menu-item>
                      <a-menu-item
                        class="filter-color"
                        v-if="['selectCheck'].includes(item.type)"
                      >
                        <span class="title">按颜色过滤</span>
                        <div style="display: flex; flex-direction: column">
                          <a-checkbox
                            v-if="['selectCheck'].includes(item.type)"
                            :indeterminate="state.indeterminate"
                            v-model:checked="state.checkAll"
                            @change="onCheckAllChange"
                          >全部</a-checkbox>
                          <a-checkbox-group v-model:value="checkColorList">
                            <div
                              v-for="(color, index) in colorList"
                              :key="index"
                            >
                              <a-checkbox
                                v-model:checked="state.checkAll"
                                :value="color"
                              >
                                <span
                                  class="color-border"
                                  :class="`${color}`"
                                ></span>
                              </a-checkbox>
                            </div>
                          </a-checkbox-group>
                        </div>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </template>
              <template v-else-if="item.type === 'menuList'">
                <a-dropdown
                  trigger="click"
                  v-model:visible="item.dropdownVisible"
                >
                  <div class="menu-list-btn">
                    <div class="menu-list-head">
                      <OperateItemTitle :item="item">
                        <template #label>
                          {{ item.label }}
                          <icon-font
                            type="icon-xiala"
                            style="color: rgba(51, 51, 51, 0.39)"
                          />
                        </template>
                      </OperateItemTitle>
                    </div>
                  </div>
                  <template #overlay>
                    <div class="menu-list-content">
                      <div
                        :class="{ disabled: subItem.disabled, 'menu-list': true }"
                        @click="!subItem.disabled ? setEmit(subItem, item) : ''"
                        v-for="subItem in item.menuInfoList || []"
                        :key="subItem.name"
                      >
                        <OperateItem
                          :item="subItem"
                          @setSelectEmit="
                          itemInfo =>
                            setSelectEmit(itemInfo.item, itemInfo.data, item)
                        "
                        ></OperateItem>
                        <!-- <OperateItemTitle :item="item"></OperateItemTitle> -->
                      </div>
                    </div>
                  </template>
                </a-dropdown>
              </template>
              <template v-else>
                <OperateItem
                  :item="item"
                  @setSelectEmit="
                  itemInfo => setSelectEmit(itemInfo.item, itemInfo.data)
                "
                  @setEmit="setEmit"
                ></OperateItem>
              </template>
            </a-tooltip>
          </div>
        </template>
      </div>
      <!-- <div class="contract-btn" @click="contractChange()">
      <up-outlined v-if="systemConfig.functionalArea.isExpand" />
      <down-outlined v-else />
    </div> -->
    </div>
    <div
      class="contract-btn"
      @click="contractChange()"
    >
      <up-outlined v-if="systemConfig.functionalArea.isExpand" />
      <down-outlined v-else />
    </div>
  </div>
</template>
<script setup>
import {
  getCurrentInstance,
  watch,
  ref,
  computed,
  reactive,
  onMounted,
} from 'vue';
import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import operateList, { updateOperateByName } from './operate';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import { systemConfigStore } from '@/store/systemConfig';
import shApi from '@/api/shApi';
import csProject from '@/api/csProject';
import apiObj from '@/api/projectDetail.js';
import jieSuanApi from '@/api/jiesuanApi';
import { useRoute } from 'vue-router';
import OperateItem from './OperateItem.vue';
import OperateItemTitle from './OperateItemTitle.vue';
import XEUtils from 'xe-utils';
const systemConfig = systemConfigStore();
const projectStore = projectDetailStore();
const props = defineProps(['projectComponentType']);
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const noShowList = ['fees','jcFees']; //22定额标准  组价方案匹配按钮,规费明细 记取水、电费不展示
const noShowList12 = ['taxation']; //12定额标准  计税方式
const emit = defineEmits(['showLoadMould']);
const route = useRoute();
const value = ref([]);
const checked = ref(true); // 已选中的选项
let checkColorList = ref([]); // 颜色筛选项
let colorList = ref([]); // 已有的颜色列表
let visible = ref(false); // 过滤下拉菜单判断是否关闭

const operateStyle = computed(() => {
  return {
    height: systemConfig.functionalArea.height,
    lineHeight: systemConfig.functionalArea.height,
  };
});
const contractChange = (isExpand = !systemConfig.functionalArea.isExpand) => {
  systemConfig.setFunctionalArea({
    isExpand,
    height: isExpand ? '60px' : '30px',
  });
};
const filterOperateList = computed(() => {
  // 将原来模板v-ifv-show条件改为计算属性
  let list = [];
  for (let item of operateList.value) {
    if (
      item.levelType.includes(projectStore.currentTreeInfo?.levelType) &&
      item.components.includes(projectStore.componentId) &&
      !(
        noShowList.includes(item.name) &&
        projectStore.deStandardReleaseYear === '22'
      ) &&
      item.windows.includes(projectStore.standardGroupOpenInfo?.type) &&
      !item.hidden &&
      isShowRender(item)
    ) {
      list.push(item);
    }
  }
  return list;
});

/**
 * 根据menuList 生成对应的操作按钮列表
 * @param item
 */
const getMenuList = item => {
  const list = [];
  for (let menu of item.menuList) {
    if (typeof menu === 'string') {
      const operate = filterOperateList.value.find(opt => opt.name === menu);
      if (operate) list.push(operate);
    } else {
      list.push(menu);
    }
  }
  return list;
};
const checkedIndex = computed(() => {
  return projectStore.currentTreeInfo?.screenCondition;
});
// 处理全选的逻辑
const state = reactive({
  indeterminate: true,
  checkAll: false,
});

const onCheckAllChange = e => {
  checkColorList.value = e.target.checked ? colorList.value : [];
  state.checkAll = e.target.checked;
  state.indeterminate = false;
};
watch(checkColorList, val => {
  state.indeterminate = !!val.length && val.length < colorList.value.length;
  state.checkAll = val.length === colorList.value.length;
});

// 处理界面是否显示过滤标识
const filteringStates = computed(() => {
  const { currentTreeInfo } = projectStore;
  if (
    currentTreeInfo?.screenCondition != null &&
    currentTreeInfo?.screenCondition != undefined &&
    currentTreeInfo?.screenCondition != 0
  ) {
    return true;
  }
  if (currentTreeInfo?.checkColorList) {
    if (projectStore.componentId === 'subItemProject') {
      const { fbfx = [] } = currentTreeInfo.checkColorList;
      return fbfx.filter(element => element != null).length > 0;
    }
    if (projectStore.componentId === 'measuresItem') {
      const { csxm = [] } = currentTreeInfo.checkColorList;
      return csxm.filter(element => element != null).length > 0;
    }
  }
  return false;
});

watch(
  () => visible.value,
  val => {
    if (val) {
      getInitColorList();
    }
  }
);
const infoVisibleChange = (val, item) => {
  if (val) {
    item.decVisible = true;
  } else {
    item.decVisible = false;
  }
};

const isShowRender = item => {
  const type = props.projectComponentType || projectStore.type;
  const show = !item.showProjectType || item.showProjectType?.includes(type);
  //内容侧边菜单列表限制

  if (item.name === 'insert') {
    item.asideKey =
      projectStore.tabSelectName === '人材机汇总' ? [8, 10] : null; //人材机汇总-暂估材料表+承包人增加插入按钮
  }
  const asideKeyLimit =
    !item.asideKey ||
    (item.asideKey?.length > 0 &&
      item.asideKey.includes(+projectStore.asideMenuCurrentInfo?.key));
  if (type === 'ys') {
    if (
      projectStore.updateSS &&
      [
        'unify-humanMachineSummary',
        'load-on-mould',
        'selfCheck',
        'component-matching',
        'reuse-group-price',
        'qd-group-price',
        'standard-group-price',
        'dataReplacement',
        'save-on-mould',
        'load-on-mould',
        'lock-subItem',
        'market-price',
      ].includes(item.name)
    ) {
      // 修改送审，隐藏人材机汇总里面的统一应用,调整市场价系数,  载入模板 保存模板
      // 费用汇总：载入模板、项目自检
      //分部分项+措施项目：组价方案匹配 复用组价  清单快速组价 标准组价  清单锁定
      return false;
    }
    // 预算没值或者有值存在ys显示+内容左侧asideMenuCurrentInfo限制
    return show && asideKeyLimit;
  }
  if (type === 'yssh') {
    // 结算显示没值或者有值存在yssh显示
    return show && asideKeyLimit;
  }
  if (type === 'jieSuan') {
    if (item.label === '固定安文费') {
      return AWF.value;
    }
    if (item.label === '批量设置结算除税系数') {
      if(projectStore.currentTreeInfo.deStandardReleaseYear=='12'){
        return true
      }else{
        return false
      }
    }
    if(item.label === '调整市场价系数'){
      return !projectStore.currentTreeInfo?.originalFlag && ['2','3'].includes(String(projectStore.currentTreeInfo?.levelType));
    }
    //合同外：人材机调整右键：载入Excel市场价（迭代四4.1.5）
    if(item.label === '导入excel市场价'){
      return !projectStore.currentTreeInfo?.originalFlag && ['2','3'].includes(String(projectStore.currentTreeInfo?.levelType));
    }
    return show && jieSuanOperateShowRender(item.label) && asideKeyLimit;
  }
  return true;
};
let AWF = ref(true);
const jieSuanOperateShowRender = name => {
  if (!projectStore.currentTreeInfo?.originalFlag) {
    const excludeNameObj = {
      subItemProject: ['工程量量差设置', '人材机分期调整','统一调价'],
      measuresItem: ['人材机分期调整', '结算方式','统一调价'],
    };
    const obj = excludeNameObj[projectStore.componentId];
    return obj ? !obj.includes(name) : true;
  }
  // 对应模块合同外显示操作
  const nameObj = {
    subItemProject: [
      '插入',
      '补充',
      '删除',
      '人材机分期调整',
      '工程量量差设置',
      '展开到',
      '工程量批量乘以系数',
      '颜色','过滤'
    ],
    measuresItem: ['人材机分期调整', '结算方式', '展开到','颜色','过滤'],
    qtxmStatistics: [],
    summaryExpense: [
      // '插入',  // 合同内】 费用汇总，左上角的插入按钮，需要屏蔽掉
      '费用代码明细',
      '安全生产、文明施工费明细',
      '规费明细',
      '价差规费明细',
      '价差安、文费明细',
      '计取水、电费',
    ]
  };
  const obj = nameObj[projectStore.componentId];
  return obj ? obj.includes(name) : true;
};

watch(
  () => projectStore.componentId,
  val => {
    if (val) {
      operateList.value.sort((a, b) => {
        const aSetIndex = a.setIndex?.[val] || 0;
        const bSetIndex = b.setIndex?.[val] || 0;
        return aSetIndex - bSetIndex;
      });
      // projectStore.currentTreeInfo?.colorList?.forEach(item => {
      //   console.log('item', item)
      //   // colorList.value.push({
      //   //   value: item,
      //   // });
      // });

      getInitColorList();
      menuPolymerizeHandler();
    }
  }
);

const getInitColorList = () => {
  const { fbfx = [], csxm = [] } =
    projectStore.currentTreeInfo?.colorList || {};
  if (projectStore.componentId === 'subItemProject') {
    colorList.value = ['none', ...fbfx];
  }
  if (projectStore.componentId === 'measuresItem') {
    colorList.value = ['none', ...csxm];
  }
  const { fbfx: cfbfx = [], csxm: ccsxm = [] } =
    projectStore.currentTreeInfo?.checkColorList || {};
  if (projectStore.componentId === 'subItemProject') {
    checkColorList.value = cfbfx;
  }
  if (projectStore.componentId === 'measuresItem') {
    checkColorList.value = ccsxm;
  }
  // if (cfbfx.length || ccsxm.length) {
  projectStore.SET_CHECK_COLOR_LIST(checkColorList.value);
  // }
};

watch(
  () => projectStore.currentTreeInfo,
  val => {
    if (val) {
      getInitColorList();
      updateOperateByName('filter-list', item => {
        item.value = projectStore.currentTreeInfo?.screenCondition;
      });
    }
  },
  { deep: true }
);
onMounted(async () => {
  if (projectStore.type === 'jieSuan') {
    AWF.value = await jieSuanApi.selectSecurityFee({
      constructId: route.query.constructSequenceNbr,
    });
  }

  // 窗口缩放监听，窗口缩放时菜单聚合显示处理
  window.addEventListener(
    'resize',
    XEUtils.debounce(() => {
      menuPolymerizeHandler();
    }, 500)
  );
});

/**
 * 菜单聚合显示处理
 */
const menuPolymerizeHandler = () => {
  const clientWidth = document.body.clientWidth;
  const menuListOperate = operateList.value.filter(
    item => item.type === 'menuList'
  );
  const isMenuPolymerize = name => {
    const map = {
      'cost-calculation': 1700,
      'convenient-pricing': 1400,
    };
    return clientWidth < (map[name] || 1400); // 菜单聚合条件
  };
  for (let operate of menuListOperate) {
    operate.hidden = !isMenuPolymerize(operate.name);
    operate.menuInfoList = getMenuList(operate);
  }
  for (let operate of menuListOperate) {
    operate.menuInfoList.forEach(item => {
      const info = operateList.value.find(sub => sub.name === item.name);
      if (info) info.isPolymerizeShow = isMenuPolymerize(operate.name); // 聚合显示，菜单点开聚合显示，外部不显示
    });
  }
};

const setEmit = (item, parentItem = null) => {
  item.decVisible = false;
  // 父级菜单为聚合显示菜单时，其子菜单不为下拉时，关闭父菜单下拉
  if (
    parentItem &&
    parentItem.type === 'menuList' &&
    !['select', 'selectRadio'].includes(item.type)
  ) {
    parentItem.dropdownVisible = false;
  }
  if (item.name === 'load-on-mould') {
    // 显示加载模板，后续多个业务都要用
    if (
      projectStore.type === 'jieSuan' &&
      projectStore.currentTreeInfo.originalFlag &&
      ['其他项目', '费用汇总'].includes(projectStore.tabSelectName)
    ) {
      message.info('合同内暂不支持该功能');
      return;
    } else {
      emit('showLoadMould');
    }
  }
  // else if (item.name === 'save-on-mould') {
  //   // 显示保存模板，后续多个业务都要用
  //   message.info('功能建设中...');

  //   // emit('showLoadMould');
  // }
  else if (item.name === 'return-proEdit') {
    //返回项目编辑
    console.log('projectStore', projectStore);
    let postData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
    };
    csProject.standardMergeBack(postData).then(res => {
      console.log('返回项目编辑', res, postData);
      if (res.status === 200) {
        let { selectProjectId } = projectStore.standardGroupOpenInfo;
        setTab(selectProjectId);
        projectStore.SET_CURRENT_TREE_INFO({
          ...projectStore.standardGroupOpenInfo.oldTreeInfo.currentTreeInfo,
        });
        projectStore.SET_CURRENT_TREE_GROUP_INFO({
          ...projectStore.standardGroupOpenInfo.oldTreeInfo
            .currentTreeGroupInfo,
        });
        projectStore.SET_STANDARD_GROUP_OPEN_INFO({
          isOpen: false,
          info: null,
          selectProjectId: selectProjectId,
          type: 'parentPage', //子窗口
          modalTip: null,
          treeGroup: {},
          oldTreeInfo: {
            currentTreeInfo: null,
            currentTreeGroupInfo: null,
          },
        });
      }
    });
    // let { selectProjectId } = projectStore.standardGroupOpenInfo;
    // setTab(selectProjectId);
    // projectStore.SET_STANDARD_GROUP_OPEN_INFO({
    //   isOpen: false,
    //   info: null,
    //   selectProjectId: selectProjectId,
    //   type: 'parentPage', //子窗口
    // });
  } else if (item.name === 'filter-list') {
    console.log('2322222222', visible.value);
    if (!visible.value) {
      updateUnitColorColl();
    }
  } else {
    bus.emit(item.name, item);
  }
};
const setTab = id => {
  //设置切换主窗体tab栏定位到分部分项
  let list = [...projectStore.proCheckTab];
  list.map(i => {
    if (i.id === id) {
      i.clickTab = '分部分项';
    }
  });
  projectStore.SET_PRO_CHECK_TAB(list); //此处初始化tab的点击记忆
};
const setSelectEmit = (item, data, parentItem = null) => {
  // 父级菜单为聚合显示菜单时，其子菜单不为下拉时，关闭父菜单下拉
  if (
    parentItem &&
    parentItem.type === 'menuList' &&
    !['select', 'selectRadio'].includes(item.type)
  ) {
    parentItem.dropdownVisible = false;
  }
  if (['selectRadio', 'selectCheck']?.includes(data.type)) {
    data.value = item.kind;
  }
  if (data.type === 'selectCheck' && data.name === 'filter-list') {
    console.log('🚀 ~ setSelectEmit ~ item:', item.kind);
    visible.value = false;
    // bus.emit(data.name + item.kind, data);
    // let screenCondition = item.kind;
    if (projectStore.currentTreeInfo?.screenCondition === item.kind) {
      data.value = 0;
    }
    let postData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      screenCondition: data.value,
    };
    csProject.updateUnitScreenCondition(postData).then(res => {
      bus.emit(data.name + item.kind, data);
      if (projectStore.updateSS) {
        setUnitInfo();
        refreshList();
      } else {
        projectStore.isRefreshProjectTree = true;
      }
    });
    return;
  }
  visible.value = false;
  bus.emit(data.name + item.kind, data);
};

const refreshList = () => {
  projectStore.$state.mainContentComponentRefresh = false;
  setTimeout(() => {
    projectStore.$state.mainContentComponentRefresh = true;
  }, 10);
};
const setUnitInfo = () => {
  // 修改送审，过滤之后screenCondition左侧树未同步，改接口拿值重新复值
  if (!projectStore.updateSS) return; // 不是修改送审
  shApi
    .getUnitProject({
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
    })
    .then(res => {
      if (!res) return;
      console.log(res, 'screenCondition左侧树未同步');
      const { screenCondition } = res;
      projectStore.$state.currentTreeInfo.screenCondition = screenCondition;
    });
};
const updateUnitColorColl = () => {
  if (checkColorList.value.filter(x => x == null).length > 0) {
    checkColorList.value = [];
  }
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    checkColorList: JSON.parse(JSON.stringify(checkColorList.value)),
    type:
      projectStore.componentId === 'subItemProject'
        ? 'fbfx'
        : projectStore.componentId === 'measuresItem'
        ? 'csxm'
        : '',
  };
  console.log('过滤参数', apiData);
  apiObj.updateUnitColorColl(apiData).then(res => {
    console.log('颜色过滤', res);
    if (res.status === 200 && res.result) {
      projectStore.isRefreshProjectTree = true;
      projectStore.SET_CHECK_COLOR_LIST(checkColorList.value);
    }
  });
};
</script>
<style lang="scss" scoped>
.menu-list-content {
  padding: 5px 10px;
  display: flex;
  background-color: #fff;
  .menu-list {
    margin: 0 4px;
    text-align: center;
    cursor: pointer;
    &.disabled {
      opacity: 0.5;
    }
  }
}
.menu-list-btn {
  display: flex;
  align-items: center;
}
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.select-radio {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  .select-head {
    display: flex;
    align-items: center;
    font-size: 14px;
  }
  .sub-name {
    margin-top: 4px;
    width: 100%;
    font-size: 12px;
    text-align: center;
  }
}
.operate {
  // flex-direction: row;
  box-sizing: border-box;
  background: #f3f6f9;
  width: calc(100% - 30px);
  //overflow-x: hidden;
  overflow-y: hidden;
  height: var(--project-detail-functional-area-height);
  line-height: var(--project-detail-functional-area-height);
  user-select: none;
  //&:hover {
  //  overflow-x: auto;
  //}
  overflow-x: auto;
  &-scroll {
    display: flex;
    min-width: fit-content; /* 设置最小宽度为子元素的总宽度 */
    padding: 0 5px;
  }
  &-item {
    display: flex;
    flex-direction: column;
    justify-content: center; /* 垂直居中 */
    //align-items: center; /* 水平居中 */
    min-width: 50px;
    padding: 0 4px;
    text-align: center;
    height: var(--project-detail-functional-area-height);
    cursor: pointer;
    div {
      height: auto;
      line-height: initial;
      text-align: center;
    }
    .iconType {
      font-size: 26px;
    }
    .icon {
      width: 28px;
      img {
        width: 100%;
      }
    }
    .label {
      font-size: 12px;
      margin-top: 2px;
    }
  }
}
.operate-container {
  position: relative;
  border-bottom: 1px solid #d6d6d6;
}
.contract-btn {
  position: absolute;
  top: 50%;
  right: 0;
  width: 30px;
  height: 100%;
  display: flex;
  align-items: center;
  transform: translateY(-50%);
  justify-content: center;
  cursor: pointer;
}
.color-border {
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 1px solid #eeeeee;
  border-radius: 3px;
  margin-right: 5px;
}
.none {
  background: none;
}
.red {
  background: #ef7c77 !important;
}
.green {
  background: #e3fada !important;
}
.orange {
  background: #e59665 !important;
}
.yellow {
  background: #fdfdac !important;
}
.blue {
  background: #8fa9fa !important;
}
.purple {
  background: #cfaadd !important;
}
.lightBlue {
  background: #a5d7f0 !important;
}
.deepYellow {
  background: #fbdf89 !important;
}
.filter-color {
  text-align: center;
  .color-list {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .color-border {
    margin-left: 10px;
    width: 16px;
    height: 16px;
  }
  .color-all {
    margin-left: 10px;
  }
  .title {
    display: block;
  }
  .ant-checkbox-group {
    ::v-deep(.ant-checkbox-group-item) {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
