const EE = require('../../../core/ee');
const { ObjectUtils } = require('../../utils/ObjectUtils');
const ConstantUtil = require('../../enum/ConstantUtil');
const { Column } = require('typeorm');
const { NumberUtil } = require('../../utils/NumberUtil');
const RcjTypeEnum = require('../../enum/RcjTypeEnum');
const { PricingFileFindUtils } = require('../../utils/PricingFileFindUtils');


let rcjModelConvertRules = {
  deId: ({ de, rcj }) => {
    let { sequenceNbr } = de;
    rcj.deId = sequenceNbr;
  },
  constructId: ({ ctx, rcj }) => {
    let { constructId } = ctx;
    rcj.constructId = constructId;
  },
  singleId: ({ ctx, rcj }) => {
    let { singleId } = ctx;
    rcj.singleId = singleId;
  },
  unitId: ({ ctx, rcj }) => {
    let { unitId } = ctx;
    rcj.unitId = unitId;
  },
  markSum: ({ rcj }) => {
    rcj.markSum = 1;
  },
  materialCode: ({ rcj }) => {
    rcj.initMaterialCode = rcj.materialCode;
  },
  kindBackUp: ({ rcj }) => {
    rcj.kindBackUp = rcj.kind;
  },
  rcjFlag: ({ rcj, de }) => {
    let { rcjFlag } = de;
    rcj.rcjFlag = rcjFlag;
  },
  type: ({ rcj }) => {
    let { service } = EE.app;
    rcj.type = service.baseRcjService.getRcjTypeEnumDescByCode(rcj.kind);
  },
  levelMark: ({ rcj, childRcj }) => {
    if (ObjectUtils.isNotEmpty(rcj.levelMark)) {
      rcj.levelMark = rcj.levelMark.toString();
    } else {
      // 如果没有子级  那么值为0：无需下沉   此处先默认给0
      rcj.levelMark = '0';
      // 再判断是否有子级
      if (ObjectUtils.isNotEmpty(childRcj)) {
        // 如果rcj是材料费，则levelMark= 1：下沉配比
        // 如果rcj是机械费，则levelMark= 2：下沉机械
        if ([RcjTypeEnum.TYPE6.code, RcjTypeEnum.TYPE7.code, RcjTypeEnum.TYPE8.code, RcjTypeEnum.TYPE9.code, RcjTypeEnum.TYPE10.code].includes(rcj.kind)) {
          rcj.levelMark = '1';
        } else if (rcj.kind == RcjTypeEnum.Jixie.code) {
          rcj.levelMark = '2';
        }
      }
    }
  },
  specification: ({ rcj }) => {
    if (ObjectUtils.isEmpty(rcj.specification)) {
      rcj.specification = null;
    }
  },
  resQty: ({ rcj, rcjResQtyMap }) => {
    rcj.resQty = rcjResQtyMap[rcj.sequenceNbr];
    //小数点处理
    rcj.resQty = NumberUtil.rcjDetailAmountFormat(rcj.resQty);
  },

  taxRemovalBackUp: ({ rcj }) => {
    rcj.taxRemovalBackUp = rcj.taxRemoval;
  },
  initResQty: ({ rcj }) => {
    if (ObjectUtils.isEmpty(rcj.initResQty)) {
      rcj.initResQty = rcj.resQty;
    }
  },

  //消耗量系数
  formattedCoefficient: ({ rcj, rcjResQtyMap }) => {
    if (rcj.initResQty ==0 ){
      rcj.formattedCoefficient = rcj.resQty + "*1" ;
    }else {
      rcj.formattedCoefficient = rcj.resQty;
    }
  },
  ifDonorMaterial: ({ rcj }) => {
    rcj.ifDonorMaterial = 0;
  },
  isLock: ({ rcj,de }) => {

    // if (de.isCostDe != 0){
    //   rcj.isLock = true;
    //   return;
    // }
    //此处需要查询全局设置是否锁定标识
    let { service } = EE.app;
    let globalConfig = service.globalConfigurationService.getGlobalConfig();
    rcj.isLock = globalConfig.budget.input.lockRcjResQty;
  },
  tempDeleteBackupResQty: ({ rcj }) => {
    if (rcj.tempDeleteFlag) {
      rcj.tempDeleteBackupResQty = rcj.resQty;
      rcj.resQty = null;
    }
  },
  dePrice: ({ rcj, ctx }) => {
    let { isSimple } = ctx;
    if (!ObjectUtils.isEmpty(rcj.dePrice)) {
      return;
    }
    //判断当前人材机数据是 22还是12
    // const is2022 = rcj.libraryCode.startsWith(ConstantUtil.YEAR_2022);
    if (ctx.is2022) {
      rcj.dePrice = isSimple ? rcj.priceBaseJournalTax : rcj.priceBaseJournal;
    }
  },
  marketPrice: ({ rcj, ctx }) => {
      // if (ctx.taxCalculationMethod == '1') {
      //   // 一般计税
      //   rcj.marketPrice = rcj.priceMarket;
      // } else {
      //   // 简易计税
      //   rcj.marketPrice = rcj.priceMarketTax;
      // }

    if (('JXTBRGFTZ' != rcj.materialCode && 'hbrcj' != rcj.extend1 && rcj.supplementDeRcj != 1)) {
      rcj.marketPrice = rcj.dePrice;
    }
  },
  // '不含税基期价',
  priceBaseJournal: ({ rcj, ctx }) => {

  },
  // '含税基期价',
  priceBaseJournalTax: ({ rcj }) => {

  },
  //'不含税市场价',
  priceMarket: ({ rcj, ctx }) => {
    //判断当前人材机数据是 22还是12
    const is2022 = PricingFileFindUtils.is22UnitById(rcj.constructId, rcj.singleId, rcj.unitId);
    if (is2022) {
      if (ObjectUtils.isNotEmpty(rcj.rcjId) && rcj.isSupplement == 1) {
        // 说明是子级的补充人材机  直接使用参数传来的值
        // rcj.priceMarket = rcj.priceMarket;
      } else {
        if (ObjectUtils.isNotEmpty(rcj.dePrice)) {
          if (rcj.supplementDeRcj != 1 && rcj.supplementDeRcj != 2) {
            // supplementDeRcj = 1  表示是补充定额时输入的人工费、机械费、材料费、主材费、设备费 带出来的那条人材机  这条人材机不需要把dePrice赋值给priceMarket
            // supplementDeRcj = 2  表示是补充定额时在弹窗中从人材机所有里面添加的标准人材机，这种人材机的价格不能使用人材机标准数据，需要使用页面输入的

            // 如果是补充人材机  那么也要把页面上传来的dePrice根据计税方式赋值给【不含税市场价】或者【含税市场价】
            // 一般计税的时候  赋值给不含税市场价
            if (ctx.taxCalculationMethod == '1' && 'hbrcj' != rcj.extend1) {
              rcj.priceMarket = rcj.dePrice;

            }
          }
          //临时处理2025.4月17
          // if(ObjectUtils.isNotEmpty(rcj.taxRate)){
          //   rcj.taxRate = 1;
          // }
          if (ObjectUtils.isNotEmpty(rcj.taxRate) && ObjectUtils.isNotEmpty(rcj.priceMarket)) {
            rcj.priceMarketTax = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(rcj.priceMarket, 1 + NumberUtil.divide100(rcj.taxRate)));

          }
        } else {
          rcj.priceMarket = ctx.is2022 ? rcj.priceBaseJournal : rcj.dePrice;

        }
      }
      if (ctx.taxCalculationMethod == '1') {
        // 一般计税
        rcj.marketPrice = rcj.priceMarket;
      } else {
        // 简易计税
        rcj.marketPrice = rcj.priceMarketTax;
      }
    }
  },
  // '含税市场价',
  priceMarketTax: ({ rcj, ctx }) => {
    //判断当前人材机数据是 22还是12
    const is2022 = PricingFileFindUtils.is22UnitById(rcj.constructId, rcj.singleId, rcj.unitId);
    if (is2022) {
      if (ObjectUtils.isNotEmpty(rcj.rcjId) && rcj.isSupplement == 1) {
        // 说明是子级的补充人材机  直接使用参数传来的值
        // rcj.priceMarketTax = rcj.priceMarketTax;
      } else {
        if (ObjectUtils.isNotEmpty(rcj.dePrice)) {
          if (rcj.supplementDeRcj != 1 && rcj.supplementDeRcj != 2) {
            // supplementDeRcj = 1  表示是补充定额时输入的人工费、机械费、材料费、主材费、设备费 带出来的那条人材机  这条人材机不需要把dePrice赋值给priceMarket
            // supplementDeRcj = 2  表示是补充定额时在弹窗中从人材机所有里面添加的标准人材机，这种人材机的价格不能使用人材机标准数据，需要使用页面输入的

            // 如果是补充人材机  那么也要把页面上传来的dePrice根据计税方式赋值给【不含税市场价】或者【含税市场价】
            // 简易计税的时候  赋值给含税市场价
            if (ctx.taxCalculationMethod != '1' && 'hbrcj' != rcj.extend1) {
              rcj.priceMarketTax = rcj.dePrice;
            }
          }
          if (ObjectUtils.isNotEmpty(rcj.taxRate) && ObjectUtils.isNotEmpty(rcj.priceMarketTax)) {
            rcj.priceMarket = NumberUtil.costPriceAmountFormat(NumberUtil.divide(rcj.priceMarketTax, 1 + NumberUtil.divide100(rcj.taxRate)));

          }else {
            rcj.priceMarketTax = ctx.is2022 ? rcj.priceBaseJournalTax : rcj.dePrice;

          }
        } else {
          rcj.priceMarketTax = ctx.is2022 ? rcj.priceBaseJournalTax : rcj.dePrice;

        }
      }
      if (ctx.taxCalculationMethod == '1') {
        // 一般计税
        rcj.marketPrice = rcj.priceMarket;
      } else {
        // 简易计税
        rcj.marketPrice = rcj.priceMarketTax;
      }
    }
  },
  // '税率',
  taxRate: ({ rcj, ctx }) => {
    //判断当前人材机数据是 22还是12
    // const is2022 = rcj.libraryCode.startsWith(ConstantUtil.YEAR_2022);
    rcj.taxRate = ctx.is2022 ? rcj.taxRate : 0;
  },
  // '税率初始值',
  taxRateInit: ({ rcj, ctx }) => {
    if (ObjectUtils.isEmpty(rcj.taxRateInit)) {
      rcj.taxRateInit = rcj.taxRate;
    }
  },


  // 人材机五要素
  referenceRecord: ({ rcj }) => {
    rcj.referenceRecord = ''.concat(rcj.kind, rcj.materialName,
      rcj.specification, rcj.unit, rcj.dePrice);
  },
  sourcePrice: async ({ rcj, ctx }) => {
    let { service } = EE.app;
    let { is2022, unit } = ctx;
    if (!is2022) {
      if (['10000001', '10000002', '10000003', 'JXPB-005', 'R00001'].includes(rcj.materialCode)) {


        let parentRcj = unit.constructProjectRcjs;
        let parentItem = parentRcj.find(i => i.materialCode === rcj.materialCode && i.sequenceNbr !== rcj.sequenceNbr);
        if (!ObjectUtils.isEmpty(parentItem)) {
          rcj.marketPrice = parentItem.marketPrice;
          return;
        }

        let childRcj = unit.rcjDetailList;
        let childItem = childRcj.find(i => i.materialCode === rcj.materialCode && i.sequenceNbr !== rcj.sequenceNbr);
        if (!ObjectUtils.isEmpty(childItem)) {
          rcj.marketPrice = childItem.marketPrice;
          return;
        }

        if (ObjectUtils.isNotEmpty(unit.rgfId)) {
          let rgf = await service.basePolicyDocumentService.queryBySequenceNbr(unit.rgfId);
          rcj.sourcePrice = null;
          if (ObjectUtils.isNotEmpty(rgf)) {
            let { cityName, pricesource, zhygLevel1, zhygLevel2, zhygLevel3 } = rgf;
            rcj.sourcePrice = cityName + pricesource;
            switch (rcj.materialCode) {
              case '10000001'://
                rcj.marketPrice = zhygLevel1;
                break;
              case '10000002':
                rcj.marketPrice = zhygLevel2;
                break;
              case '10000003':
                rcj.marketPrice = zhygLevel3;
                break;
              case 'JXPB-005':
                rcj.marketPrice = zhygLevel2;
                break;
              case 'R00001':
                rcj.marketPrice = zhygLevel2;
                break;
            }
          }
        }
      }
    }
  }

};

module.exports = { rcjModelConvertRules };
