/**
 *
 */
class JieSuanExcelEnum {

    /**Start**竖版相关参数********************/
    static A4Height = 835;//单位 磅;   839.0551   835可以适配wps的pdf预览
    static A4Width = 107; //单位 0.1英寸   82.28    106    110大了
    static A4Top = 0.3*72;  //单位 磅         0.3*72 对应的pdf的上边距为 0.3*2.54 = 0.762cm
    static A4Bottom = 0.6*72;
    static A4Left = 0.75*10;   //单位 0.1英寸  0.3*10 对应的pdf左边距为 0.6cm
    static A4Right = 0.75*10;
    static A4Header = 0.59375*72;  //单位 磅
    static A4Footer = 0;
    static xiaoJi = 30;//本页小计的行高高度
    static defaultRowHeight = 40;//默认每行行高
    /**End**竖版相关参数********************/
    /**Start**横版相关参数********************/
    static A4HeightHorizontal  = 588;  //计算高度时  单位统一为磅   横版适配wps的pdf预览
    static A4WidthHorizontal  = 152;  //单位 0.1英寸   152边距为0时合适
    static A4TopHorizontal = 0.19975*72; //单位 磅
    static A4BottomHorizontal = 0.19975*72;
    static A4LeftHorizontal = 0.59375*10;   //单位 0.1英寸
    static A4RightHorizontal = 0.59375*10;

    /**End**横版相关参数********************/

    static fontName = "宋体";//正文默认字体类别
    static fontSize = 10;//正文默认字体大小

    static screenPPI = 80;//设定屏幕分辨率  ppi  该参数在实际报表生成过程中没有意义
    /**Start***********适用于导出的PDF竖版相关参数************************/
    static A4HeightPDF = 869;  //=869.198  单位磅    817.69为信纸的高度
    static A4WidthPDF =  (108.4/21.59)*21;//=110.07
    /**End***********PDF************************/
    /**Start***********适用于导出的PDF横版相关参数************************/
    static A4HeightHorizontalPDF = (817.69/27.94)*21;
    static A4WidthHorizontalPDF =  (108.4/21.59)*29.7;
    /**End***********PDF************************/
    static templateVersion = "3";//模板更新的版本号
}

module.exports = JieSuanExcelEnum;
