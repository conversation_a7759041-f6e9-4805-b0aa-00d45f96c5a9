// const {ResponseData} = require("../../../electron/utils/ResponseData");
// const {Controller} = require("../../../core");
// const JieSuanOtherProjectServiceCostService = require("../service/jieSuanOtherProjectServiceCostService");
// class JieSuanOtherProjectServiceCostController extends Controller{
//
//
//     /**
//      * 构造函数
//      * @param ctx
//      */
//     constructor(ctx) {
//         super(ctx);
//         this.jiesuanOtherProjectServiceCostService = new JieSuanOtherProjectServiceCostService(ctx);
//     }
//
//     getOtherProjectServiceCost(args){
//         const res = this.jiesuanOtherProjectServiceCostService.getOtherProjectServiceCost(args);
//         return ResponseData.success(res);
//     }
//
//     /**
//      * 获取默认总承包服务费
//      */
//     getDefaultOtherProjectServiceCost(){
//         const res = this.jiesuanOtherProjectServiceCostService.getDefaultOtherProjectServiceCost();
//         return ResponseData.success(res);
//     }
//
//     async otherProjectServiceCost(args){
//        await this.jiesuanOtherProjectServiceCostService.otherProjectServiceCost(args);
//
//         if (args.operateType !==1){
//
//             await this.service.management.sycnTrigger("unitDeChange");
//             await this.service.management.trigger("itemChange");
//         }
//
//         return ResponseData.success(null);
//     }
//
// }
//
// JieSuanOtherProjectServiceCostController.toString = () => '[class JieSuanOtherProjectServiceCostController]';
// module.exports = JieSuanOtherProjectServiceCostController;