
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const ConstructionMeasureTypeConstant = require("../enum/ConstructionMeasureTypeConstant");
const DePropertyTypeConstant = require("../enum/DePropertyTypeConstant");
const CalculateBaseType = require("../enum/CalculateBaseType");
const {arrayToTree} = require("../main_editor/tree");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {Gfee} = require("../model/Gfee");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {NumberUtil} = require("../utils/NumberUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {Service} = require('../../core');
class GfeeService extends Service{
    constructor(ctx) {
        super(ctx);
        this.decimalPointConfig = this.service.globalConfigurationService.getDecimalPointConfig()
    }

    async getGfeeFee(args){
        await this.countGfees(args);
        let unit = PricingFileFindUtils.getUnit(args.constructId,args.singleId, args.unitId);
        return unit.gfees;
    }
    /**
     * 计算规费明细
     * @param args
     */
    // async countGfees(args){
    //
    //     let unit = PricingFileFindUtils.getUnit(args.constructId,args.singleId, args.unitId);
    //     //分部分项
    //     let itemBillProjects = unit.itemBillProjects;
    //     //措施项目
    //     let measureProjectTables = unit.measureProjectTables;
    //     // if (Array.isArray(measureProjectTables)) {
    //     //     measureProjectTables =arrayToTree(measureProjectTables)
    //     // }
    //     // if (Array.isArray(itemBillProjects)) {
    //     //     itemBillProjects =arrayToTree(itemBillProjects)
    //     // }
    //     //分部分项定额集合
    //     let itemBiliDeList = itemBillProjects.filter(item=>item.kind ===BranchProjectLevelConstant.de);
    //     //单价措施标题主键
    //     let djcsBtSequenceNbr = measureProjectTables.filter(item=>!ObjectUtils.isEmpty(item.constructionMeasureType) && item.constructionMeasureType === ConstructionMeasureTypeConstant.DJCS).map(item =>item.sequenceNbr);
    //
    //     //单价措施定额
    //     let djDeMeasureProjectTables = PricingFileFindUtils.getUnitDatas(measureProjectTables,BranchProjectLevelConstant.de,djcsBtSequenceNbr);
    //
    //     //总价措施标题主键
    //     let zjcsBtSequenceNbr = measureProjectTables.filter(item=>!ObjectUtils.isEmpty(item.constructionMeasureType) && item.constructionMeasureType === ConstructionMeasureTypeConstant.ZJCS).map(item =>item.sequenceNbr);
    //
    //     //总价措施定额
    //     let zjDeMeasureProjectTables = PricingFileFindUtils.getUnitDatas(measureProjectTables,BranchProjectLevelConstant.de,zjcsBtSequenceNbr);
    //
    //     //获取所有的定额
    //     let deList = [...itemBiliDeList,...djDeMeasureProjectTables,...zjDeMeasureProjectTables]
    //
    //     let copyDeList = ConvertUtil.deepCopy(deList);
    //     // let caculateBase = unit.feeCalculateBaseList.find(o => o.type === CalculateBaseType.gf).code;
    //
    //     // let gfCalculateBase =  unit.feeCalculateBaseList.find(item => item.type ==='gf').code
    //     // 根据人材机以及下级计算定额价格
    //     let gfeeBaseTempList = await this.countDePrice(copyDeList,args.constructId,args.singleId, args.unitId,null);
    //
    //
    //
    //
    //
    //     //根据取费专业分组
    //     let groupData = ArrayUtil.group(gfeeBaseTempList,'costMajorName');
    //     //取费文件
    //     let feeFiles = unit.feeFiles;
    //     //规费明细
    //     let gfees = new Array();
    //     if(!ObjectUtils.isEmpty(gfeeBaseTempList)){
    //         if(ObjectUtils.isNotEmpty(feeFiles)){
    //             for (let i = 0; i < feeFiles.length; i++) {
    //                 let unitFeeFile = feeFiles[i];
    //                 //根据定额册子名称获取基数数据
    //                 let resArray = groupData.get(unitFeeFile.feeFileName);
    //                 if(!ObjectUtils.isEmpty(resArray)){
    //                     let gfee = new Gfee();
    //                     let rjCont =resArray.reduce((accumulator, item) => {
    //                         return accumulator + item.base;
    //                     }, 0);
    //                     gfee.unitId = unit.sequenceNbr;
    //                     gfee.costMajorName = unitFeeFile.feeFileName;
    //                     gfee.costFeeBase = Number(rjCont.toFixed(2));
    //                     gfee.gfeeRate = unitFeeFile.fees;
    //                     gfee.feeAmount = Number(NumberUtil.multiplyParams(rjCont,0.01,unitFeeFile.fees).toFixed(2));
    //                     gfees.push(gfee);
    //                 }
    //             }
    //         }
    //     }
    //     unit.gfees = gfees;
    // }


    async countGfees(args){
        let unit = PricingFileFindUtils.getUnit(args.constructId,args.singleId, args.unitId);
        //单价构成
        let feeBuild =unit.feeBuild;
        //分部分项定额集合
        let itemBiliDeList = unit.itemBillProjects.filter(item=>item.kind ===BranchProjectLevelConstant.de);
        //措施项目定额集合
        let measureProjectTablesDeList = unit.measureProjectTables.filter(item=>item.kind ===BranchProjectLevelConstant.de);

        let fbfxArray = await this.countDePrice(itemBiliDeList, feeBuild);
        let csxmArray = await this.countDePrice(measureProjectTablesDeList, feeBuild);


        let gfeeBaseTempList = [...fbfxArray,...csxmArray]
        //根据取费专业分组
        let groupData = ArrayUtil.group(gfeeBaseTempList,'costMajorName');
        //取费文件
        let feeFiles = unit.feeFiles;
        //规费明细
        let gfees = new Array();
        if(!ObjectUtils.isEmpty(gfeeBaseTempList)){
            if(ObjectUtils.isNotEmpty(feeFiles)){
                for (let i = 0; i < feeFiles.length; i++) {
                    let unitFeeFile = feeFiles[i];
                    //根据定额册子名称获取基数数据
                    let resArray = groupData.get(unitFeeFile.feeFileName);
                    if(!ObjectUtils.isEmpty(resArray)){
                        let gfee = new Gfee();
                        let rjCont =resArray.reduce((accumulator, item) => {
                            return accumulator + item.deGfBaseValue;
                        }, 0);
                        gfee.unitId = unit.sequenceNbr;
                        gfee.costMajorName = unitFeeFile.feeFileName;
                        gfee.costFeeBase = Number(rjCont.toFixed(this.decimalPointConfig.costPrice));
                        gfee.gfeeRate = unitFeeFile.fees;
                        gfee.feeAmount = Number(NumberUtil.multiplyParams(rjCont,0.01,unitFeeFile.fees).toFixed(this.decimalPointConfig.costPrice));
                        gfee.feeCode = unitFeeFile.feeCode;//生成惠招标要用
                        gfees.push(gfee);
                    }
                }
            }
        }
        unit.gfees = gfees;
    }

    countDePrice(deList, feeBuild) {
        if (ObjectUtils.isNotEmpty(deList)) {
            let res = [];
            for (let i = 0; i < deList.length; i++) {
                if(ObjectUtils.isNotEmpty(feeBuild[deList[i].sequenceNbr])){
                    //定额单价构成
                    let deFeeBuild = feeBuild[deList[i].sequenceNbr].filter(item => item.type == '规费')
                    let deGfBaseValue = 0;
                    if (0 !== deList[i].quantity) {//定额工程量为0不计算基数
                        //定额规费的基数
                        if (ObjectUtils.isNotEmpty(deFeeBuild)) {
                            deGfBaseValue = deFeeBuild.reduce((accumulator, djgc) => {
                                return NumberUtil.add(accumulator, NumberUtil.multiply(djgc.caculateBaseValue, deList[i].quantity));
                            }, 0)
                        }
                    }

                    let gfeeBaseTempList = {
                        'costMajorName': deList[i].costMajorName,
                        'deGfBaseValue': deGfBaseValue
                    }
                    res.push(gfeeBaseTempList)
                }
            }
            return res
        }
        return [];
    }

    async convertRcjMap(rcjList) {



        if(ObjectUtils.isEmpty(rcjList)){
            return {}
        }
        return  ArrayUtil.group(rcjList,'deId');


    }

    /**
     * 根据人材机以及下级计算(R J)定额价格
     *  （人+机）*消耗量*定额工程量* 计算基数【费用定额】
     * @returns {Promise<void>}
     */
    // async countDePrice(deList, constructId, singleId, unitId,gfCalculateBase){
    //
    //     const is22De = PricingFileFindUtils.is22De(constructId);
    //
    //     let allRcjList = PricingFileFindUtils.getRcjList(constructId, singleId, unitId);
    //
    //     let rcjMap = ArrayUtil.group(allRcjList,'deId');
    //
    //     if(!ObjectUtils.isEmpty(deList)){
    //         for (let i = 0; i < deList.length; i++) {
    //             let de = deList[i];
    //
    //             let rcjList = rcjMap.get(de.sequenceNbr)
    //
    //             if(!ObjectUtils.isEmpty(rcjList)){
    //                 //定额人工费定额价
    //                 let rgfDePrice=0;
    //                 //定额机械费定额价
    //                 let jxfDePrice=0;
    //
    //                 for (let j = 0; j < rcjList.length; j++) {
    //                     let rcj = rcjList[j];
    //
    //                     //rcj消耗量
    //                     let resQty =  rcj.resQty;
    //                     //定额工程量
    //                     let quantity = de.quantity;
    //                     //计算基数 默认是1 如果是费用定额
    //                     let formula = 1;
    //                     let rBase = 1;
    //                     let jBase = 1;
    //
    //                     if (DePropertyTypeConstant.AZ_DE == de.isCostDe){
    //                         //安装定额
    //                         rBase = de.baseNum ? de.baseNum[1] : 0;
    //                         jBase = de.baseNum ? de.baseNum[3] : 0;
    //                         if(rcj.kind === 1 ){
    //                             //人
    //                             formula = formula *rBase;
    //                         }else if(rcj.kind === 3){
    //                             //机械
    //                             formula = formula *jBase;
    //                         }
    //
    //                     }else {
    //                         //除了安装的其他费用定额
    //                         if (DePropertyTypeConstant.NON_COST_DE !== de.isCostDe && DePropertyTypeConstant.DS_CY_DE !== de.isCostDe && DePropertyTypeConstant.DX_CY_DE !== de.isCostDe){
    //                             formula = de.formula
    //                         }
    //                         // 这里对这个值重新覆盖超高的计算基数 其他的不确定 保留原来的值
    //                         // 此处原来是判断除了安装外的其他费用定额  垂运的费用定额计算基数都是1  所以按上面的默认值处理  不需要单独判断
    //                         // 对于超高的费用定额：12的超高费用定额有计算基数(取值字段是：baseNum)  应该算进去  22的超高的计算基数默认就是1 乘1没有影响  所以此处不做12或者22的判断
    //                         if (DePropertyTypeConstant.CG_DE == de.isCostDe) {
    //                             formula = de.baseNum;
    //                             if (!is22De) {
    //                                 // 12的超高定额的baseNum不是一个数字  是个对象  里面的def字段存的计算基数
    //                                 formula = de.baseNum.def;
    //                             }
    //                         }
    //                     }
    //
    //
    //                     //单位影响系数
    //                     let unitXs = 1;
    //                     if (rcj.unit === '%') {
    //                         //如果单位是 '%'
    //                         unitXs = 0.01
    //                     }
    //
    //                     if(rcj.kind === 1 ){
    //                         //人工
    //
    //                         let rcjDetails = this.service.constructProjectRcjService.getRcjDetailListByDeId(rcj.sequenceNbr, constructId, singleId, unitId);
    //                         if(!ObjectUtils.isEmpty(rcjDetails)){
    //
    //                             //(∑子级定额价*子级消耗量*父级消耗量*定额工程量)*计算基数
    //                             //计算基数在配比材料累加完 【子级定额价*子级消耗量*父级消耗量*定额工程量】之后在乘
    //                             let temp =0  //子级定额价*子级消耗量*父级消耗量*定额工程量
    //                             for (let k = 0; k < rcjDetails.length; k++) {
    //                                 let rcjDetail = rcjDetails[k];
    //                                 let rgRcjDePrice = NumberUtil.numberScale2(NumberUtil.multiplyParams(rcjDetail.dePrice,rcjDetail.resQty,resQty,quantity,unitXs));
    //                                 temp += rgRcjDePrice;
    //
    //                             }
    //                             let detailsCount = NumberUtil.numberScale2(NumberUtil.multiply(temp,formula));
    //                             rgfDePrice += detailsCount;
    //
    //                         }else {
    //
    //                             let rgRcjDePrice = NumberUtil.numberScale2(NumberUtil.multiplyParams(rcj.dePrice,resQty,quantity,formula,unitXs));
    //                             rgfDePrice += rgRcjDePrice;
    //                         }
    //
    //                     }else if(rcj.kind === 3){
    //                         //机械
    //                         let rcjDetails = this.service.constructProjectRcjService.getRcjDetailListByDeId(rcj.sequenceNbr, constructId, singleId, unitId);
    //                         if(!ObjectUtils.isEmpty(rcjDetails)){
    //
    //                             //(∑子级定额价*子级消耗量*父级消耗量*定额工程量)*计算基数
    //                             //计算基数在配比材料累加完 【子级定额价*子级消耗量*父级消耗量*定额工程量】之后在乘
    //                             let temp =0  //子级定额价*子级消耗量*父级消耗量*定额工程量
    //                             for (let k = 0; k < rcjDetails.length; k++) {
    //                                 let rcjDetail = rcjDetails[k];
    //                                 let jxRcjDePrice = NumberUtil.numberScale2(NumberUtil.multiplyParams(rcjDetail.dePrice,rcjDetail.resQty,resQty,quantity,unitXs));
    //                                 temp += jxRcjDePrice;
    //
    //                             }
    //                             let detailsCount = NumberUtil.numberScale2(NumberUtil.multiply(temp,formula));
    //                             jxfDePrice += detailsCount;
    //
    //
    //
    //                         }else {
    //                             let jxRcjDePrice = NumberUtil.numberScale2(NumberUtil.multiplyParams(rcj.dePrice,resQty,quantity,formula,unitXs));
    //                             jxfDePrice += jxRcjDePrice;
    //                         }
    //
    //                     }
    //
    //
    //
    //                 }
    //                 //临时存放定额对应的人工和机械费的 定额价
    //                 de.rgfDePrice = rgfDePrice;
    //                 de.jxfDePrice = jxfDePrice;
    //             }
    //         }
    //     }
    //
    //     //临时存放当前项目的规费的基数
    //     let gfeeBaseTempList = deList.map(item =>{
    //         return { base:  NumberUtil.add(item.rgfDePrice,item.jxfDePrice)  ,costMajorName:item.costMajorName}
    //     });
    //
    //     return gfeeBaseTempList;
    // }


}
GfeeService.toString = () => '[class GfeeService]';
module.exports = GfeeService;
