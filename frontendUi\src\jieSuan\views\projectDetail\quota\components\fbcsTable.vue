<template>
  <div class="table-content zbClsssBox">
    <s-table
      size="small"
      ref="stableRef"
      class="s-table"
      :columns="columns"
      bordered
      :delay="200"
      :rangeSelection="true"
      :scroll="{ y: stableHeight }"
      :row-selection="rowSelection"
      :custom-cell="customCell"
      @mousedown="mousedownHandle"
      :custom-header-cell="customHeaderCell"
      :rowClassName="(row, index) => rowClassName(row, index, tableData,projectStore.currentTreeInfo?.originalFlag)"
      :animateRows="false"
      :pagination="false"
      :data-source="tableData"
    >
      <!-- 自定义头部 -->
      <template #headerCell="{ title, column }">
        <span class="custom-header" style="font-weight: bold">
          <i class="vxe-icon-edit" v-show="column.editable"></i>&nbsp;{{
            title
          }}
        </span>
      </template>
      <!--自定义内容 -->
      <template #bodyCell="{
        text,
        record: row,
        index,
        column,
        key,
        openEditor,
        closeEditor,
      }">
        <div class="cell-line-break-el" v-if="column.field === 'bdCode'">
          <i @click.stop="changeStatus(row)" v-if="row.displaySign === 1 && tableData.length > 1"
            class="vxe-icon-caret-down"></i>
          <i @click.stop="changeStatus(row)" v-if="row.displaySign === 2" class="vxe-icon-caret-right"></i>
          <span class="code">
            <a-tooltip>
              <template #title>{{ row.bdCode }}
                {{ row.redArray?.length > 0 ? row.redArray.join(',') : ''
                }}{{
                  row.blackArray?.length > 0
                    ? row.blackArray.join(',')
                    : ''
                }}</template>
              {{ row.bdCode }}
              {{ row.redArray?.length > 0 ? row.redArray.join(',') : '' }}
            </a-tooltip> 
          </span>
          <span class="code-black" v-if="row.blackArray?.length > 0">{{
            row.blackArray.join(',')
          }}</span>
        </div>
      </template>
      <!--自定义编辑 -->
      <template
        #cellEditor="{
          column,
          modelValue,
          save,
          closeEditor,
          record: row,
          editorRef,
          getPopupContainer,
          recordIndexs,
        }"
      >
        <template v-if="column.field=='indicatorName'">
          <a-cascader
            v-model:value="row['indicatorVal']"
            :options="activeKey<3?fbfxList:gclList"
            :displayRender="displayRender"
            :allowClear="false"
            @change="
              () => {
                handleChange(row,column.field,'indicatorVal');
                closeEditor();
              }
            "
             @blur="
                () => {
                  closeEditor();
                }
              "
          />
        </template>
        <template v-if="column.field=='quantityIndicatorName'">
          <a-cascader
            v-model:value="row['quantityVal']"
            :options="gclList"
            :displayRender="displayRender"
            :allowClear="false"
            @change="
              () => {
                handleChange(row,column.field,'quantityVal');
                closeEditor();
              }
            "
             @blur="
                () => {
                  closeEditor();
                }
              "
          />
        </template>
      </template>
    </s-table>
  </div>
</template>

<script setup>
import { onMounted,reactive, ref, watch,onActivated } from 'vue';
import jiesuanApi from '@/api/jiesuanApi';
import api from '@/api/projectDetail.js';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
import { Cascader } from 'ant-design-vue';
import xeUtils from 'xe-utils';
import {
  customCell,
  rowClassName,
  customHeaderCell,
} from '../economyQuota/classAndStyleMethod';
const props = defineProps(['currentInfo']);
const emits = defineEmits(['update:visible', 'updateData']);
const projectStore = projectDetailStore();
const tableRef = ref();
const tableData = ref([]);
const upRow=ref({})
const stableHeight = ref(400);
let page = ref(1);
let limit = ref(300000);
const stableRef=ref()
const fbfxList=ref([])
const gclList=ref([])
const columns=ref([])
const activeKey=ref(1)
const isShowNotSet=ref(false)
onMounted(() => {
  getFbfxZbList()
  getGclZbList()
})
onActivated(() => {
})
const getDataList = (row,active=1,showNotSet=false) => {
  let tableEl = document.querySelector('.rightBox');
  stableHeight.value = tableEl.clientHeight - 130;
  tableData.value=[]
  activeKey.value=active
  isShowNotSet.value=showNotSet
  let oldColumns = [
  {
    title: '序号',
    field: 'dispNo',
    dataIndex: 'dispNo',
    width: 50,
    align: 'center',
    headerAlign: 'center',
    fixed: 'left',
  },
  {
    title: '项目编码',
    field: 'bdCode',
    dataIndex: 'bdCode',
    autoHeight:true,
    width: 150,
    headerAlign: 'center',
    fixed: 'left',
  },
  {
    title: '项目名称',
    field: 'name',
    dataIndex: 'name',
    autoHeight:true,
    width: 150,
    headerAlign: 'center',
    fixed: 'left',
  },
  {
    title: '项目特征',
    field: 'projectAttr',
    dataIndex: 'projectAttr',
    autoHeight:true,
    width: 180,
    headerAlign: 'center',
  },
  {
    title: '单位',
    field: 'unit',
    dataIndex: 'unit',
    width: 65,
    align: 'center',
    headerAlign: 'center',
  },
  {
    title: '结算工程量',
    field: 'quantity',
    dataIndex: 'quantity',
    width: 120,
    headerAlign: 'center',
  },
  {
    title: '结算单价',
    field: 'price',
    dataIndex: 'price',
    width: 100,
    headerAlign: 'center',
  },
  {
    title: '结算合价',
    field: 'total',
    dataIndex: 'total',
    width: 100,
    headerAlign: 'center',
  },
  {
    title: '分部分项指标',
    field: 'indicatorName',
    dataIndex: 'indicatorName',
    editRender: { autofocus: '.vxe-select' },
    ellipsis: true,
    visible: true,
    slot: true,
    editable: ({ record: rows }) => {
      if(rows.displaySign === 0&&(row.parentProjectId||row.originalFlag)){
        return 'cellEditorSlot';
      }else{
        return false
      }
    },
    width: 120,
    headerAlign: 'center',
  },
  {
    title: '措施项目指标',
    field: 'indicatorName',
    dataIndex: 'indicatorName',
    editRender: { autofocus: '.vxe-select' },
    ellipsis: true,
    visible: true,
    slot: true,
    editable: ({ record: rows }) => {
      if(rows.displaySign === 0&&(row.parentProjectId||row.originalFlag)){
        return 'cellEditorSlot';
      }else{
        return false
      }
    },
    width: 120,
    headerAlign: 'center',
  },
  {
    title: '工程量指标',
    field: 'quantityIndicatorName',
    dataIndex: 'quantityIndicatorName',
    editRender: { autofocus: '.vxe-select' },
    ellipsis: true,
    visible: true,
    slot: true,
    editable: ({ record: rows }) => {
      if(rows.displaySign === 0&&(row.parentProjectId||row.originalFlag)){
        return 'cellEditorSlot';
      }else{
        return false
      }
    },
    width: 120,
    headerAlign: 'center',
  },
  {
    title: '工程量指标单位',
    field: 'quantityIndicatorUnit',
    dataIndex: 'quantityIndicatorUnit',
    width: 130,
    headerAlign: 'center',
  },
  {
    title: '转换系数',
    field: 'conversionCoefficient',
    dataIndex: 'conversionCoefficient',
    width: 100,
    headerAlign: 'center',
  },
  ]
  let gclColumns = [
  {
    title: '序号',
    field: 'dispNo',
    dataIndex: 'dispNo',
    width: 50,
    align: 'center',
    headerAlign: 'center',
    fixed: 'left',
  },
  {
    title: '材料编码',
    field: 'materialCode',
    dataIndex: 'materialCode',
    autoHeight:true,
    width: 150,
    headerAlign: 'center',
    fixed: 'left',
  },
  {
    title: '名称',
    field: 'materialName',
    dataIndex: 'materialName',
    autoHeight:true,
    width: 150,
    headerAlign: 'center',
    fixed: 'left',
  },
  {
    title: '单位',
    field: 'unit',
    dataIndex: 'unit',
    width: 65,
    align: 'center',
    headerAlign: 'center',
  },
  {
    title: '结算数量',
    field: 'totalNumber',
    dataIndex: 'totalNumber',
    width: 120,
    headerAlign: 'center',
  },
  {
    title: '合同/确认单价',
    field: 'marketPrice',
    dataIndex: 'marketPrice',
    width: 100,
    headerAlign: 'center',
  },
  {
    title: '结算合价',
    field: 'total',
    dataIndex: 'total',
    width: 100,
    headerAlign: 'center',
  },
  {
    title: '工料指标',
    field: 'indicatorName',
    dataIndex: 'indicatorName',
    editRender: { autofocus: '.vxe-select' },
    ellipsis: true,
    visible: true,
    slot: true,
    height:50,
    editable: ({ record: row }) => {
      if((row.parentProjectId||row.originalFlag)&&!(row.markSum === 1 && (row.levelMark === 1 || row.levelMark === 2))&&row.isFyrcj!=0 ){
        return 'cellEditorSlot';
      }else{
        return false
      }
    },
    width: 100,
    headerAlign: 'center',
  },
  {
    title: '工料指标单位',
    field: 'quantityIndicatorUnit',
    dataIndex: 'quantityIndicatorUnit',
    width: 130,
    headerAlign: 'center',
  },
  {
    title: '转换系数',
    field: 'conversionCoefficient',
    dataIndex: 'conversionCoefficient',
    width: 100,
    headerAlign: 'center',
  },
  ]
  if(active==3){
    columns.value=gclColumns
  }else{
    if(active==1){
      oldColumns.splice(9,1)
    }else if(active==2){
      oldColumns.splice(8,1)
    }
    columns.value=oldColumns
  }
  upRow.value=row
  let apiData={
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: row.parentId,
    unitId: row.id,
    matchType:active,
    showFlag:showNotSet
  }
  jiesuanApi.queryUnitIndexMatchColl(apiData).then((res) => {
    let resultData = res.result
    resultData.forEach((item)=>{
      // 分部分项指标、措施项目木指标下拉默认值
      if(item.indicatorName){
        item['indicatorVal']=[item.indicatorNameOfMajor,item.indicatorName]
      }else{
        item['indicatorVal']=[item.indicatorNameOfMajor,'无']
      }
      // 工程量指标下拉默认值
      if(item.quantityIndicatorName){
        item['quantityVal']=[item.quantitIndicatorNameOfMajor,item.quantityIndicatorName]
      }else{
        item['quantityVal']=[item.quantitIndicatorNameOfMajor,'无']
      }
    })
    let datas = xeUtils.toArrayTree(resultData, {
      key: 'sequenceNbr',
      parentKey: 'parentId',
    });
    resultData = xeUtils.toTreeArray(addLevelToTree(datas)).map(i => {
      i.key = i.sequenceNbr;
      delete i.children;
      return i;
    });
    console.info('匹配指标右侧列表树',resultData)
    tableData.value=resultData
  });
}
const addLevelToTree = (data, parentLevel = 0, parent = null) => {
  return data.map(node => {
    node.customParent = parent; // 自定义字段父级数据
    const { children, ...other } = node;
    return {
      ...node,
      customLevel: parentLevel + 1,
      children:
        (node.children || []).length > 0
          ? addLevelToTree(node.children, parentLevel + 1, other)
          : [],
    };
  });
};
const changeStatus = row => {
  let index = tableData.value.findIndex(x => x.sequenceNbr === row.sequenceNbr);
  page.value = Math.ceil((index + 1) / limit.value);
  if (row.displaySign === 1) {
    row.displaySign = 2;
    closeTree(row);
  } else if (row.displaySign === 2) {
    row.displaySign = 1;
    openTree(row);
  }
  // updateFbData(row, 'seq');
};
// 展开列表
const openTree = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: upRow.value.parentId,
    unitId: upRow.value.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  let apiFun=api.openTree
  if(activeKey.value==2){
    apiFun=api.itemOpen
  }
  apiFun(apiData).then(res => {
    if (res.status === 200 && res.result) {
      getDataList(upRow.value,activeKey.value,isShowNotSet.value)
    }
  });
};
// 关闭列表
const closeTree = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: upRow.value.parentId,
    unitId: upRow.value.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  let apiFun=api.closeTree
  if(activeKey.value==2){
    apiFun=api.itemClose
  }
  apiFun(apiData).then(res => {
    if (res.status === 200 && res.result) {
      getDataList(upRow.value,activeKey.value,isShowNotSet.value)
    }
  });
};
// 获取工程量指标下拉列表
const getGclZbList = () =>{
  let apiFun=jiesuanApi.querGclIndicatorListColl// 工程量指标
  if(activeKey.value==3){
    apiFun=jiesuanApi.querGongLiaoIndicatorListColl// 工料指标
  }
  apiFun().then((res) => {
    let result = Object.entries(res.result).map(([key, value]) => ({
      label:key,
      value:key,
      children: value.map(item => ({
        label:item['指标名称'],
        value:item['指标名称'],
      }))
    }));
    gclList.value=result
  });
}
// 获取分部分项指标下拉列表
const getFbfxZbList = () =>{
  let apiFun=jiesuanApi.querFbfxIndicatorListColl// 分部分项指标
  if(activeKey.value==2){
    apiFun=jiesuanApi.querCsxmIndicatorListColl// 措施项目指标
  }
  apiFun().then((res) => {
    let result = Object.entries(res.result).map(([key, value]) => ({
      label:key,
      value:key,
      children: value.map(item => ({
        label:item[activeKey.value==1?'分部分项指标':'措施项目指标列表'],
        value:item[activeKey.value==1?'分部分项指标':'措施项目指标列表'],
      }))
    }));
    console.info('指标下拉列表返回结果',result)
    fbfxList.value=result
  });
}
// 切换下拉修改
const handleChange=(row,field,type)=> {
  row[field]=row[type][1]
  let apiData={
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: upRow.value.parentId,
    unitWorkId: upRow.value.id,
    pointLineId:row.sequenceNbr,
    value:row[type][1],
    major:row[type][0],
    column:field,
    params:null,
  }
  let apiFun=jiesuanApi.updateJieSuanFbfxDataColl
  if(activeKey.value==2){
    apiFun=jiesuanApi.updateJieSuanCsxmDataColl
  }else if(activeKey.value==3){
    apiData = {
      levelType: 3,
      constructId:projectStore.currentTreeGroupInfo?.constructId,
      sequenceNbr: row.sequenceNbr,
      pointLine:JSON.parse(JSON.stringify(row)),
      constructProjectRcj: {
        indicatorName:row[type][1]
      },
      adjustMethod: null,
      num:null,
      singleId: upRow.value.parentId,
      unitId:upRow.value.id,
      major:row[type][0]
    };
    apiFun=jiesuanApi.changeRcjNewJieSuan
  }
  console.info('匹配指标右侧指标修改参数',apiData)
  apiFun(apiData).then((res) => {
    if(res.code!==200){
      return message.error(res.message)
    }
    getDataList(upRow.value,activeKey.value,isShowNotSet.value)
  });
}
// 表格上任意地方点击时捕获，目前控制 移除cut 时的样式
const mousedownHandle = e => {
  stableRef.value.closeEditor();
};
const displayRender=(labels)=> {
  let val=labels.labels
  return val[val.length - 1];
}
defineExpose({
  getDataList,
});
</script>
<style lang="scss" src="@/assets/css/subItemProject.scss" scoped></style>
<style lang="scss" scoped>
@import '@/views/projectDetail/customize/subItemProject/s-table.scss';
.zbClsssBox{
  ::v-deep .surely-table-cell-content{
    min-height:35px !important;
  }
  
  ::v-deep(.surely-table-body .no-edit) {
    background: #f3f2f3;
    color: #a7a7a7;
  }
}

</style>
