'use strict';



const {ConstructProject} = require("../model/ConstructProject");
const {Service} = require("../../core");
const {Snowflake} = require("../utils/Snowflake");
const fs = require('fs')
const xml2js = require('xml2js');
const ConstructBiddingTypeConstant = require("../enum/ConstructBiddingTypeConstant");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const BranchProjectDisplayConstant = require("../enum/BranchProjectDisplayConstant");
const OtherProjectCalculationBaseConstant = require("../enum/OtherProjectCalculationBaseConstant");
const PolicyDocumentTypeEnum = require("../enum/PolicyDocumentTypeEnum");
const {arrayToTree} = require("../main_editor/tree");
const {BasePolicyDocument} = require("../model/BasePolicyDocument");
const {NumberUtil} = require("../utils/NumberUtil");
const {ResponseData} = require("../utils/ResponseData");
const {MeasureProjectTable} = require("../model/MeasureProjectTable");
const {OtherProjectZygcZgj} = require("../model/OtherProjectZygcZgj");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const {ItemBillProject} = require("../model/ItemBillProject");
const {OtherProjectDayWork} = require("../model/OtherProjectDayWork");
const {OtherProjectServiceCost} = require("../model/OtherProjectServiceCost");
const {OtherProjectZgj} = require("../model/OtherProjectZgj");
const {OtherProjectProvisional} = require("../model/OtherProjectProvisional");
const {OtherProject} = require("../model/OtherProject");
const {UnitProject} = require("../model/UnitProject");
const {SingleProject} = require("../model/SingleProject");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {ReadXmlUtil} = require("../utils/ReadXmlUtil");
const {getUnitFormatEnum} = require("../main_editor/rules/format");
const ConstantUtil = require("../enum/ConstantUtil");

class AnalyzingXMLServiceHZB extends Service{
    constructor(ctx) {
        super(ctx);

        this.qbExtraTableArray = new Array();

        this.qdMap = new Map();
        this.dispNo = 1;

    }


    async  analysis(constructProject,data){
        let 文件类别 = data.工程造价文件.$.文件类别;

        let 工程项目 = data.工程造价文件.工程数据[0].工程项目[0].$;
        let 单项工程 = data.工程造价文件.工程数据[0].工程项目[0].单项工程;
        //单项工程
        let singleProjects = new Array();

        if("招标文件"===文件类别){
            if(ObjectUtils.isEmpty(constructProject.biddingType)){
                constructProject.biddingType =ConstructBiddingTypeConstant.zbProject
            }
        }else {
            constructProject.biddingType =ConstructBiddingTypeConstant.tbProject
            return ResponseData.fail('文件类型有误，请重新选择');
        }

        let jsType ; //1 一般 0 简易

        if(工程项目.计税方式 === '增值税方式_一般计税'){
            jsType = 1;
        }else {
            jsType = 0;
        }
        //计税方式
        await this.service.projectTaxCalculationService.importXMLInitProjectTaxCalculation(constructProject,jsType);


        if(ObjectUtils.isEmpty(constructProject.sequenceNbr)){
            constructProject.sequenceNbr = Snowflake.nextId();
        }
        // constructProject.constructName = 工程项目.项目名称;
        constructProject.constructCode = 工程项目.项目编码;
        constructProject.projectOverview = 工程项目.报价说明;
        constructProject.total = 工程项目.金额;
        constructProject.gfee = 工程项目.其中规费;
        constructProject.safeFee = 工程项目.其中安全生产文明施工费;
        constructProject.sbfsj = 工程项目.其中设备费及相关费用;
        constructProject.fddbr = 工程项目.发包人法定代表人;
        constructProject.constructionUnit = 工程项目.招投标单位名称;
        constructProject.gfId = '14';
        constructProject.awfId = '44';

        //人工费id
        let rgfPolicyDocumentList = await this.app.appDataSource.getRepository(BasePolicyDocument).find({
            where: {
                areaId:130100,
                fileType: PolicyDocumentTypeEnum.RGF.code,
            }
        });
        if (!ObjectUtils.isEmpty(rgfPolicyDocumentList)) {

            //时间倒叙排列
            rgfPolicyDocumentList.sort(function(a, b) {
                return b.fileDate.localeCompare(a.fileDate);
            });
            constructProject.rgfId=rgfPolicyDocumentList[0].sequenceNbr;
        }

        let map = this.convertConstructProjectJBXX(constructProject, data);
        // 编制说明 ---项目层级
        this.service.constructProjectService.initProjectOrUnitBZSM(1, constructProject);
        constructProject.organizationInstructions.context = 工程项目.编制说明
        //解析单项工程
        await this.convertSingleProject(单项工程,constructProject,map);
        //放入内存
        PricingFileWriteUtils.writeToMemory(constructProject);
        return constructProject.sequenceNbr;
    }


    convertConstructProjectJBXX(constructProject, data) {
        //工程基本信息
        this.service.constructProjectService.initProjectOrUnitData(constructProject, 1);
        let constructProjectJBXX = constructProject.constructProjectJBXX;
        let 工程属性子目 =[] ;
        if(data.工程造价文件.工程数据[0].工程项目[0].工程属性){
            工程属性子目 = data.工程造价文件.工程数据[0].工程项目[0].工程属性[0].工程属性子目;
        }else {
            工程属性子目 = data.工程造价文件.工程数据[0].工程项目[0].单项工程[0].工程属性[0].工程属性子目
        }

        let 招投标信息 = data.工程造价文件.工程数据[0].招投标信息[0].$;
        let map = new Map();
        for (let i = 0; i < 工程属性子目.length; i++) {
            let $ = 工程属性子目[i].$;
            map.set($.名称, $.属性值)
        }
        for (let i = 0; i < constructProjectJBXX.length; i++) {
            switch (constructProjectJBXX[i].name) {
                case '工程名称':
                    constructProjectJBXX[i].remark = constructProject.constructName;
                    break;
                case '招标人(发包人)':
                    constructProjectJBXX[i].remark = 招投标信息.发包人;
                    break;
                case '招标人(发包人)法人或其授权人':
                    constructProjectJBXX[i].remark = 招投标信息.发包人法定代表人;
                    break;
                case '编制人':
                    if(constructProjectJBXX[i].groupCode == 2){
                        constructProjectJBXX[i].remark = 招投标信息.编制人;
                    }
                    break;
                case '编制时间':
                    if(constructProjectJBXX[i].groupCode == 2) {
                        constructProjectJBXX[i].remark = 招投标信息.编制日期;
                    }
                    break;
                case '核对人(复核人)':
                    constructProjectJBXX[i].remark = 招投标信息.核对人;
                    break;
                case '工程造价咨询人':
                    constructProjectJBXX[i].remark = 招投标信息.工程造价咨询人;
                    break;
                case '工程造价咨询人法人或其授权人':
                    constructProjectJBXX[i].remark = map.get('工程造价法人代表');
                    break;
                case '核对(复核)时间':
                    constructProjectJBXX[i].remark = map.get('复核时间');
                    break;
                default:
                    constructProjectJBXX[i].remark = map.get(constructProjectJBXX[i].name)
                    break;
            }

        }
        constructProject.constructProjectJBXX = constructProjectJBXX;
        return map;
    }

    /**
     * 解析单项工程
     * @param 单项工程
     * @param constructProject
     */
    async convertSingleProject(单项工程, constructProject,map) {
        if(!ObjectUtils.isObject(单项工程)){
            let singleProjects = new Array();
            for (let i = 0; i < 单项工程.length; i++) {
                let singleProject = new SingleProject();
                let model = 单项工程[i];
                let $ = model.$;
                singleProject.sequenceNbr = Snowflake.nextId();
                singleProject.constructId = constructProject.sequenceNbr;
                singleProject.projectCode = $.编码;
                singleProject.projectName = $.名称;
                singleProject.total = $.金额;
                singleProject.safeFee = $.其中安全生产文明施工费;
                singleProject.gfee = $.其中规费;
                singleProject.sbf = $.其中设备及相关费;
                //判断单项下是否还有单项
                if(model.单位工程 === undefined){
                    //还有单项, 递归去解析
                    await this.recursionSingleProject(model.单项工程, singleProject, map, constructProject);
                }else{
                    //解析单位工程
                    await this.convertUnitProject(model.单位工程,singleProject,map,constructProject.rgfId);
                }
                singleProjects.push(singleProject);
            }
            constructProject.singleProjects = singleProjects
        }
    }

    /**
     * 递归处理子单项
     */
    async recursionSingleProject(xmlSingleProjects, oldSingleProjects, map, constructProject) {
        let newSingleProjects = new Array();
        for (let i = 0; i < xmlSingleProjects.length; i++) {
            let singleProject = new SingleProject();
            let model = xmlSingleProjects[i];
            let $ = model.$;
            singleProject.sequenceNbr = Snowflake.nextId();
            singleProject.constructId = constructProject.sequenceNbr;
            singleProject.projectCode = $.编码;
            singleProject.projectName = $.名称;
            singleProject.total = $.金额;
            singleProject.safeFee = $.其中安全生产文明施工费;
            singleProject.gfee = $.其中规费;
            singleProject.sbf = $.其中设备及相关费;
            //判断单项下是否还有单项
            if(model.单位工程 === undefined){
                await this.recursionSingleProject(model.单项工程, singleProject, map, constructProject);
            }else{
                //解析单位工程
                await this.convertUnitProject(model.单位工程, singleProject, map, constructProject.rgfId);
            }
            newSingleProjects.push(singleProject);
        }
        oldSingleProjects.subSingleProjects = newSingleProjects;

    }

    /**
     * 解析单位工程
     * @param 单位工程
     * @param singleProject
     */
    async convertUnitProject(单位工程, singleProject, map, rgfId) {
        if(!ObjectUtils.isObject(单位工程)){

            let unitProjects = new Array();
            for (let i = 0; i < 单位工程.length; i++) {
                let model = 单位工程[i].$;
                let unitProject = new UnitProject();
                unitProject.sequenceNbr = Snowflake.nextId();
                unitProject.upCode = model.编码;
                unitProject.upName = model.名称;
                unitProject.uptotal = model.金额;
                unitProject.csxhj = model.其中措施费;
                unitProject.djcsxhj = model.其中单价措施费;
                unitProject.zjcsxhj = model.其中总价措施费;
                unitProject.safeFee = model.其中安全文明费;
                unitProject.gfee = model.其中规费;
                unitProject.sbf = model.其中设备费及相关费用;
                unitProject.spId = singleProject.sequenceNbr;
                unitProject.constructId = singleProject.constructId;
                unitProject.rgfId = rgfId;
                // 添加工程基本信息 ---单位层级
                this.service.constructProjectService.initProjectOrUnitData(unitProject, 3,map);
                // 编制说明 ---单位层级
                this.service.constructProjectService.initProjectOrUnitBZSM(3, unitProject);
                //单位工程费用汇总（包含单位工程部分数据）
                await this.convertUnitProjectSummary(单位工程[i].单位工程费汇总表,unitProject);
                //分部分项
                this.dispNo = 1;
                await this.convertItemBill(单位工程[i].分部分项工程,unitProject);
                this.dispNo = 1;
                //单价措施
                await this.convertMeasureTableDJ(单位工程[i].措施项目,unitProject);
                //总价措施
                await this.convertMeasureTableZJ(单位工程[i].措施项目,unitProject);

                this.qbExtraTableArray = new Array();
                //暂列金额
                await this.convertProvisional(单位工程[i].其他项目[0].暂列金额,unitProject);
                //暂估价
                await this.convertZgjSums(单位工程[i].其他项目[0].暂估价,unitProject);
                //总承包服务费
                await this.convertServiceCosts(单位工程[i].其他项目[0].总承包服务费,unitProject);
                //计日工
                await this.convertDayWorks(单位工程[i].其他项目[0].计日工, unitProject);
                //暂估价
                await this.convertZgj(单位工程[i].其他项目[0].暂估价, unitProject);
                //承包人
                await this.convertCbr(单位工程[i].人材机汇总表[0].人材机子目,unitProject);

                if(ObjectUtils.isNotEmpty(单位工程[i].增值税进项税额计算汇总表[0].进项税额费用子目)){
                    unitProject.deStandardReleaseYear = ConstantUtil.DE_STANDARD_12
                }

                //其他项目 签证与索赔计价表 初始化
                let otherProjectQzSpJjbList = await this.service.otherProjectService.getInitOtherProjectQzSpJjb(unitProject);
                unitProject.otherProjectQzAndSuoPeis = otherProjectQzSpJjbList;

                //保存其他项目
                unitProject.otherProjects= ObjectUtils.isNotEmpty(this.qbExtraTableArray)?this.qbExtraTableArray:unitProject.otherProjects;;
                unitProject.constructProjectRcjs = [];
                unitProject.rcjDetailList = [];
                unitProjects.push(unitProject);
            }
            singleProject.unitProjects = unitProjects;
        }

    }

    /**
     * 费用汇总
     * @param 单位工程费汇总表
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertUnitProjectSummary(单位工程费汇总表, unitProject) {
        if(!ObjectUtils.isEmpty(单位工程费汇总表)){
            let 单位工程费用子目 = 单位工程费汇总表[0].单位工程费用子目;

            for (let i = 0; i < 单位工程费用子目.length; i++) {
                let model = 单位工程费用子目[i].$;
                switch(model.序号){
                    //分部分项工程量清单计价合计
                    case "1": {
                        unitProject.fbfxhj  = model.金额;
                        unitProject.fbfxrgf = model.其中人工费;
                        unitProject.fbfxclf = model.其中材料费;
                        unitProject.fbfxjxf = model.其中机械费;
                        break;
                    }

                    //措施项目清单计价合计
                    case "2": {
                        unitProject.csxhj  = model.金额;
                        unitProject.csxrgf = model.其中人工费;
                        unitProject.csxclf = model.其中材料费;
                        unitProject.csxjxf = model.其中机械费;
                        break;
                    }

                    //单价措施项目工程量清单计价合计
                    case "2.1": {
                        unitProject.djcsxhj = model.金额;
                        unitProject.djcsxrgf= model.其中人工费;
                        unitProject.djcsxclf= model.其中材料费;
                        unitProject.djcsxjxf= model.其中机械费;
                        break;
                    }

                    //其他总价措施项目清单计价合计
                    case "2.2": {
                        unitProject.zjcsxhj = model.金额;
                        unitProject.zjcsxrgf= model.其中人工费;
                        unitProject.zjcsxclf= model.其中材料费;
                        unitProject.zjcsxjxf= model.其中机械费;
                        break;
                    }

                    //其他项目清单计价合计
                    case "3": {
                        unitProject.qtxmhj = model.金额;
                        unitProject.qtxmrgf= model.其中人工费;
                        unitProject.qtxmclf= model.其中材料费;
                        unitProject.qtxmjxf= model.其中机械费;
                        break;
                    }

                    //规费
                    case "4": {
                        unitProject.gfee= model.金额;
                        break;
                    }

                    //安全生产、文明施工费
                    case "5": {
                        unitProject.safeFee= model.金额;
                        break;
                    }

                    //税前工程造价
                    case "6": {
                        unitProject.sqgczj= model.金额;
                        break;
                    }

                    //进项税额
                    case "6.1": {
                        unitProject.jxse= model.金额;
                        break;
                    }

                    //销项税额
                    case "7": {
                        unitProject.xxse= model.金额;
                        break;
                    }

                    //增值税应纳税额
                    case "8": {
                        unitProject.zzsynse= model.金额;
                        break;
                    }

                    //附加税费
                    case "9": {
                        unitProject.fjse= model.金额;
                        break;
                    }

                    //税金
                    case "10": {
                        unitProject.sj= model.金额;
                        break;
                    }
                }



            }
        }


        
    }

    /**
     * 暂列金额
     * @param 暂列金额
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertProvisional(暂列金额, unitProject) {

        if(ObjectUtils.isEmpty(暂列金额)){
            //调用插入暂列金额默认值
            //调用插入暂列金额默认值
            unitProject.otherProjectProvisionals = await this.service.otherProjectProvisionalService.importInitProjectProvisional();
            return
        }

        if(!ObjectUtils.isEmpty(暂列金额)){
            let model = 暂列金额[0].$;
            //其他项目赋值
            let otherProject = new OtherProject();
            otherProject.sequenceNbr = Snowflake.nextId();
            otherProject.constructId = unitProject.constructId;
            otherProject.spId = unitProject.spId;
            otherProject.unitId = unitProject.sequenceNbr;
            otherProject.sortNo = 1;
            otherProject.extraName = '暂列金额';
            otherProject.type = OtherProjectCalculationBaseConstant.zljr;
            otherProject.markSj = 1;
            otherProject.markSafa = 1;
            otherProject.amount = 1;
            otherProject.putOntotalFlag = true;
            let qtJxTotal = 0;
            let qtCsTotal = 0;

            if(!ObjectUtils.isEmpty(model)){
                otherProject.dispNo = model.序号;
                otherProject.total = NumberUtil.costPriceAmountFormat(model.金额);
                otherProject.unit = model.计量单位;
                otherProject.description = model.备注;

            }else {
                otherProject.dispNo = '1';
                // return;
            }
            //为空给一条默认数据
            let otherProjectProvisionalArray = new Array();
            let otherProjectProvisional = new OtherProjectProvisional();
            otherProjectProvisional.sequenceNbr = Snowflake.nextId();
            //单位
            otherProjectProvisional.unit = '项';
            //数量
            otherProjectProvisional.number = 1;
            otherProjectProvisional.dispNo = 1;
            //单价
            otherProjectProvisional.price = 0;
            //暂定金额
            otherProjectProvisional.provisionalSum = 0;
            otherProjectProvisionalArray.push(otherProjectProvisional);
            unitProject.otherProjectProvisionals = otherProjectProvisionalArray;

            if(!ObjectUtils.isEmpty(暂列金额[0].暂列金额子目)){
                let 暂列金额子目 = 暂列金额[0].暂列金额子目;
                let otherProjectProvisionalArray = new Array();
                for (let i = 0; i < 暂列金额子目.length; i++) {
                    let $ = 暂列金额子目[i].$;
                    let otherProjectProvisional = new OtherProjectProvisional();
                    otherProjectProvisional.sequenceNbr = Snowflake.nextId();
                    otherProjectProvisional.name = $.项目名称;
                    otherProjectProvisional.unit = $.计量单位;
                    otherProjectProvisional.provisionalSum = NumberUtil.costPriceAmountFormat(ObjectUtils.isEmpty($.暂定金额)?0:Number($.暂定金额)) ;
                    otherProjectProvisional.sortNo = i+1;
                    otherProjectProvisional.dispNo = $.序号;
                    otherProjectProvisional.description = $.备注;
                    otherProjectProvisional.constructId = unitProject.constructId;
                    otherProjectProvisional.spId = unitProject.spId;
                    otherProjectProvisional.unitId = unitProject.sequenceNbr;

                    otherProjectProvisional.amount = 1 ;
                    otherProjectProvisional.price = otherProjectProvisional.provisionalSum;//单价 没有单价所以直接默认赋值暂定金额
                    otherProjectProvisional.taxRemoval = 3 ; //除税系数(%)
                    // 进项合计 暂定金额*除税系数
                    otherProjectProvisional.jxTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectProvisional.provisionalSum,otherProjectProvisional.taxRemoval/100)) ;
                    otherProjectProvisional.csPrice = NumberUtil.subtract(otherProjectProvisional.provisionalSum,otherProjectProvisional.jxTotal);
                    otherProjectProvisional.csTotal = otherProjectProvisional.csPrice;

                    qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectProvisional.jxTotal);
                    qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectProvisional.csTotal);

                    otherProjectProvisionalArray.push(otherProjectProvisional);
                }
                unitProject.otherProjectProvisionals = otherProjectProvisionalArray;
                
            }

            otherProject.jxTotal = qtJxTotal;
            otherProject.csTotal = qtCsTotal;
            this.qbExtraTableArray.push(otherProject);
        }

    }

    /**
     * 暂估价
     * @param 暂估价
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertZgjSums(暂估价, unitProject) {
        if(!ObjectUtils.isEmpty(暂估价)){

            let otherProjectZgjArray =new Array();
            let model = 暂估价[0].$;
            //其他项目赋值
            let otherProject = new OtherProject();
            otherProject.sequenceNbr = Snowflake.nextId();
            otherProject.extraName = '暂估价';
            otherProject.sortNo = 2;
            otherProject.constructId = unitProject.constructId;
            otherProject.spId = unitProject.spId;
            otherProject.unitId = unitProject.sequenceNbr;
            otherProject.markSj = 1;
            otherProject.markSafa = 1;
            otherProject.amount = 1;
            otherProject.putOntotalFlag = true;
            if(!ObjectUtils.isEmpty(model)){
                otherProject.dispNo = model.序号;
                otherProject.total = NumberUtil.costPriceAmountFormat(model.金额);
                otherProject.unit = model.计量单位;
                otherProject.description = model.备注;
            }else {
                otherProject.dispNo = '2';
            }

            let qtJxTotal = 0;
            let qtCsTotal = 0;
            this.qbExtraTableArray.push(otherProject);
            let sortNo= 0;
            let 材料暂估价 = 暂估价[0].材料暂估价;
            if(!ObjectUtils.isEmpty(材料暂估价)){
                model = 材料暂估价[0].$;
                let otherProjectZgj = new OtherProjectZgj();
                //其他项目
                otherProject = new OtherProject();
                otherProject.sequenceNbr = Snowflake.nextId();
                otherProject.extraName = '材料暂估价';
                otherProject.sortNo = 3;
                otherProject.constructId = unitProject.constructId;
                otherProject.spId = unitProject.spId;
                otherProject.unitId = unitProject.sequenceNbr;
                otherProject.amount = 1;
                otherProject.markSj = 0;
                otherProject.markSafa = 0;
                if(!ObjectUtils.isEmpty(model)){
                    otherProject.dispNo = model.序号;
                    otherProject.total = NumberUtil.costPriceAmountFormat(model.金额);
                    otherProject.unit = model.计量单位;
                    otherProject.description = model.备注;
                }else {
                    otherProject.dispNo = '2.1';
                }
                qtJxTotal = 0;
                qtCsTotal = 0;

                let 暂估材料子目 = 材料暂估价[0].暂估材料子目;
                if(!ObjectUtils.isEmpty(暂估材料子目)){
                    for (let i = 0; i < 暂估材料子目.length; i++) {
                        model = 暂估材料子目[i].$;
                        otherProjectZgj = new OtherProjectZgj();
                        otherProjectZgj.sequenceNbr = Snowflake.nextId();
                        otherProjectZgj.price   = NumberUtil.costPriceAmountFormat(model.市场价);
                        otherProjectZgj.name   = model.名称;
                        otherProjectZgj.attr  =model.规格型号;
                        otherProjectZgj.unit  =model.计量单位;
                        otherProjectZgj.taxRemoval  =model.除税系数;
                        otherProjectZgj.jxTotal  =NumberUtil.costPriceAmountFormat(model.进项税额合计);
                        otherProjectZgj.csPrice  =NumberUtil.costPriceAmountFormat(model.除税市场价);
                        otherProjectZgj.csTotal  =NumberUtil.costPriceAmountFormat(model.除税合价);
                        // otherProjectZgj.parentId = id;
                        otherProjectZgj.dispNo   = ''+(i+1);
                        otherProjectZgj.sortNo   = sortNo++;
                        otherProjectZgj.description = model.备注;
                        otherProjectZgj.constructId = unitProject.constructId;
                        otherProjectZgj.spId = unitProject.spId;
                        otherProjectZgj.unitId = unitProject.sequenceNbr;
                        otherProjectZgjArray.push(otherProjectZgj);
                    }
                }
                this.qbExtraTableArray.push(otherProject);
            }
            unitProject.otherProjectClZgjs = otherProjectZgjArray;
            otherProjectZgjArray = new Array();
            let 设备暂估价 = 暂估价[0].设备暂估价;
            if(!ObjectUtils.isEmpty(设备暂估价)){
                model = 设备暂估价[0].$;
                let otherProjectZgj = new OtherProjectZgj();
                //其他项目
                otherProject = new OtherProject();
                otherProject.sequenceNbr = Snowflake.nextId();
                otherProject.extraName = '设备暂估价';
                otherProject.sortNo = 4;
                otherProject.constructId = unitProject.constructId;
                otherProject.spId = unitProject.spId;
                otherProject.unitId = unitProject.sequenceNbr;
                otherProject.amount = 1;
                otherProject.markSj = 0;
                otherProject.markSafa = 0;
                if(!ObjectUtils.isEmpty(model)){
                    otherProject.dispNo = model.序号;
                    otherProject.total = NumberUtil.costPriceAmountFormat(model.金额);
                    otherProject.unit = model.计量单位;
                    otherProject.description = model.备注;
                }else {
                    otherProject.dispNo = '2.2';
                }


                let 暂估设备子目 = 设备暂估价[0].暂估设备子目;
                if(!ObjectUtils.isEmpty(暂估设备子目)){
                    for (let i = 0; i < 暂估设备子目.length; i++) {
                        model = 暂估设备子目[i].$;
                        otherProjectZgj = new OtherProjectZgj();
                        otherProjectZgj.sequenceNbr = Snowflake.nextId();
                        otherProjectZgj.price   = NumberUtil.costPriceAmountFormat(model.金额);
                        otherProjectZgj.name   = model.名称;
                        otherProjectZgj.attr  =model.规格型号;
                        otherProjectZgj.unit  =model.计量单位;
                        otherProjectZgj.taxRemoval  =model.除税系数;
                        otherProjectZgj.jxTotal  =NumberUtil.costPriceAmountFormat(model.进项税额合计);
                        otherProjectZgj.csPrice  =NumberUtil.costPriceAmountFormat(model.除税市场价);
                        otherProjectZgj.csTotal  =NumberUtil.costPriceAmountFormat(model.除税合价);
                        // otherProjectZgj.parentId = id;
                        otherProjectZgj.dispNo   = ''+(i+1);
                        otherProjectZgj.sortNo   = sortNo++;
                        otherProjectZgj.description = model.备注;
                        otherProjectZgj.constructId = unitProject.constructId;
                        otherProjectZgj.spId = unitProject.spId;
                        otherProjectZgj.unitId = unitProject.sequenceNbr;
                        otherProjectZgjArray.push(otherProjectZgj);
                    }
                }
                this.qbExtraTableArray.push(otherProject);
            }
            unitProject.otherProjectSbZgjs = otherProjectZgjArray;
            otherProjectZgjArray = new Array();
            let 专业工程暂估价 = 暂估价[0].专业工程暂估价;
            qtJxTotal = 0;
            qtCsTotal = 0;
            if(!ObjectUtils.isEmpty(专业工程暂估价)){
                model = 专业工程暂估价[0].$;
                let otherProjectZgj = new OtherProjectZygcZgj();
                //其他项目
                otherProject = new OtherProject();
                otherProject.sequenceNbr = Snowflake.nextId();
                otherProject.extraName = '专业工程暂估价';
                otherProject.sortNo = 5;
                otherProject.constructId = unitProject.constructId;
                otherProject.spId = unitProject.spId;
                otherProject.unitId = unitProject.sequenceNbr;
                otherProject.type = OtherProjectCalculationBaseConstant.zygczgj;
                otherProject.markSj = 1;
                // otherProject.markSafa = 1;
                otherProject.amount = 1;
                if(!ObjectUtils.isEmpty(model)){
                    otherProject.dispNo = model.序号;
                    otherProject.total = NumberUtil.costPriceAmountFormat(model.金额);
                    otherProject.unit = model.计量单位;
                    otherProject.description = model.备注;
                }else {
                    otherProject.dispNo = '2.3';
                }
                // this.qbExtraTableArray.push(otherProject);
                let 暂估工程子目 = 专业工程暂估价[0].暂估工程子目;
                if(!ObjectUtils.isEmpty(暂估工程子目)){
                    for (let i = 0; i < 暂估工程子目.length; i++) {
                        model = 暂估工程子目[i].$;
                        otherProjectZgj = new OtherProjectZygcZgj();
                        otherProjectZgj.sequenceNbr = Snowflake.nextId();
                        otherProjectZgj.total   = NumberUtil.costPriceAmountFormat(model.金额);
                        otherProjectZgj.name   = model.工程名称;
                        otherProjectZgj.content   = model.工程内容;
                        otherProjectZgj.unit  =model.单位;
                        // otherProjectZgj.parentId = id;
                        otherProjectZgj.dispNo   = ''+(i+1);
                        otherProjectZgj.sortNo   = sortNo++;
                        otherProjectZgj.description = model.备注;
                        otherProjectZgj.constructId = unitProject.constructId;
                        otherProjectZgj.spId = unitProject.spId;
                        otherProjectZgj.unitId = unitProject.sequenceNbr;

                        otherProjectZgj.amount = 1 ;
                        otherProjectZgj.price = otherProjectZgj.total;//单价 没有单价所以直接默认赋值暂定金额
                        otherProjectZgj.taxRemoval = 3 ; //除税系数(%)
                        // 进项合计 暂定金额*除税系数
                        otherProjectZgj.jxTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectZgj.total,otherProjectZgj.taxRemoval/100)) ;
                        otherProjectZgj.csPrice = NumberUtil.subtract(otherProjectZgj.total,otherProjectZgj.jxTotal);
                        otherProjectZgj.csTotal = otherProjectZgj.csPrice;

                        qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectZgj.jxTotal);
                        qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectZgj.csTotal);
                        otherProjectZgjArray.push(otherProjectZgj);

                    }
                }
                if(ObjectUtils.isEmpty(otherProjectZgjArray)){
                    //专业工程暂估价没有子母, 给一条默认
                    otherProjectZgj = new OtherProjectZygcZgj();
                    otherProjectZgj.sequenceNbr = Snowflake.nextId();
                    otherProjectZgj.dispNo   = 1;
                    otherProjectZgj.unit  = '项';
                    otherProjectZgj.amount = '1' ;
                    otherProjectZgj.price = '0';//单价
                    otherProjectZgj.total   = '0';
                    otherProjectZgjArray.push(otherProjectZgj);
                }
                unitProject.otherProjectZygcZgjs = otherProjectZgjArray;
                otherProject.jxTotal = qtJxTotal;
                otherProject.csTotal = qtCsTotal;
                this.qbExtraTableArray.push(otherProject);

            }


            this.qbExtraTableArray[1].jxTotal =this.qbExtraTableArray[4].jxTotal;
            this.qbExtraTableArray[1].csTotal =this.qbExtraTableArray[4].csTotal;
        }
    }


    /**
     * 总承包服务费
     * @param 总承包服务费
     * @param unitProject
     * @returns {Promise<void>}
     */
    async  convertServiceCosts(总承包服务费, unitProject) {


        let model = 总承包服务费[0].$;

        let otherProject = new OtherProject();
        otherProject.sequenceNbr = Snowflake.nextId();
        otherProject.extraName = '总承包服务费';
        otherProject.sortNo = 6;

        otherProject.constructId = unitProject.constructId;
        otherProject.spId = unitProject.spId;
        otherProject.unitId = unitProject.sequenceNbr;
        otherProject.type = OtherProjectCalculationBaseConstant.zcbfwf;
        otherProject.markSj = 1;
        otherProject.markSafa = 1;
        otherProject.amount = 1;
        otherProject.putOntotalFlag = true;
        if(!ObjectUtils.isEmpty(model)){
            otherProject.description = model.备注;
            otherProject.unit = model.计量单位;
            otherProject.dispNo = model.序号;
            otherProject.total = NumberUtil.costPriceAmountFormat(model.金额);
        }else {
            otherProject.dispNo = '3';
        }



        if(ObjectUtils.isEmpty(总承包服务费)){
            //调用初始化总承包服务接口
            this.qbExtraTableArray.push(otherProject);
            unitProject.otherProjectServiceCosts = await this.service.otherProjectService.getInitOtherProjectZcbfwfList()
            return;
        }
        let 总承包服务费子目 = 总承包服务费[0].总承包服务费子目;

        if(ObjectUtils.isEmpty(总承包服务费子目)){
            //调用初始化总承包服务接口
            this.qbExtraTableArray.push(otherProject);
            unitProject.otherProjectServiceCosts = await this.service.otherProjectService.getInitOtherProjectZcbfwfList()
            return;
        }
        // 根据类型分组

        let zcbfwf = new Array();
        for (let i = 0; i < 总承包服务费子目.length; i++) {
            zcbfwf.push(总承包服务费子目[i].$);
        }
        let map = ArrayUtil.group(zcbfwf,'类型');

        let zygcList = map.get('招标人另行发包专业工程');
        let clList = map.get('招标人供应材料');
        let sbList = map.get('招标人供应设备');
        unitProject.otherProjectServiceCosts = new Array();
        await this.setServiceCosts(unitProject, zygcList, '1', '招标人另行发包专业工程');
        await this.setServiceCosts(unitProject, clList, '2', '招标人供应材料');
        await this.setServiceCosts(unitProject, sbList, '3', '招标人供应设备');

        let ts = unitProject.otherProjectServiceCosts.filter(item =>item.dispNo.indexOf('.')!== -1);
        otherProject.total = Number(ts.reduce((accumulator, item) => {
            return NumberUtil.add(accumulator,item.fwje)  ;
        }, 0).toFixed(2))
        this.qbExtraTableArray.push(otherProject);
    }

    /**
     * 总承包服务费
     * @param unitProject
     * @param list
     * @param dispNo
     * @param name
     * @returns {Promise<void>}
     */
    async  setServiceCosts(unitProject, list, dispNo, name) {
        let serviceCostsArray = new Array();
        let parentsUuid = Snowflake.nextId();
        let serviceCostsParents = new OtherProjectServiceCost();
        serviceCostsParents.sequenceNbr = parentsUuid;
        serviceCostsParents.dispNo = dispNo;
        serviceCostsParents.fxName = name;
        serviceCostsParents.sortNo = 0;
        serviceCostsParents.constructId = unitProject.constructId;
        serviceCostsParents.spId = unitProject.spId;
        serviceCostsParents.unitId = unitProject.sequenceNbr;
        serviceCostsParents.dataType = 1;

        serviceCostsArray.push(serviceCostsParents);

        if (!ObjectUtils.isEmpty(list)) {
            for (let i = 0; i < list.length; i++) {
                let model = list[i];
                let uuid = Snowflake.nextId();
                let otherProjectServiceCost = new OtherProjectServiceCost();
                otherProjectServiceCost.sequenceNbr  = uuid;
                otherProjectServiceCost.xmje  = NumberUtil.costPriceAmountFormat(model.计算基础);
                //otherProjectServiceCost.dispNo  = dispNo+'.'+(i+1);
                otherProjectServiceCost.dispNo  = model.序号;
                otherProjectServiceCost.fxName  = model.项目名称;
                otherProjectServiceCost.fwje  = NumberUtil.costPriceAmountFormat(model.金额);
                //otherProjectServiceCost.fwje = NumberUtil.numberScale2(NumberUtil.multiply(otherProjectServiceCost.xmje,NumberUtil.divide(otherProjectServiceCost.rate,100)));
                otherProjectServiceCost.rate  = model.费率;
                otherProjectServiceCost.serviceContent  = model.服务内容;
                otherProjectServiceCost.parentId  = parentsUuid;
                otherProjectServiceCost.constructId = unitProject.constructId;
                otherProjectServiceCost.spId = unitProject.spId;
                otherProjectServiceCost.unitId = unitProject.sequenceNbr;
                otherProjectServiceCost.sortNo  = i + 1;
                otherProjectServiceCost.amount  = 1;
                otherProjectServiceCost.dataType = 2;


                serviceCostsArray.push(otherProjectServiceCost);
            }
        }else {
            let myNumber = 0;
            let formattedNumber = NumberUtil.costPriceAmountFormat(myNumber);
            let otherProjectServiceCost = new OtherProjectServiceCost();
            otherProjectServiceCost.sequenceNbr = Snowflake.nextId();
            otherProjectServiceCost.dispNo=dispNo+'.1';
            otherProjectServiceCost.xmje = formattedNumber;
            otherProjectServiceCost.rate= formattedNumber;
            otherProjectServiceCost.fwje = formattedNumber;
            otherProjectServiceCost.dataType =2 ;
            otherProjectServiceCost.parentId = parentsUuid;
            serviceCostsArray.push(otherProjectServiceCost);
        }
        unitProject.otherProjectServiceCosts.push(...serviceCostsArray);

    }

    /**
     * 计日工
     * @param 计日工
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertDayWorks(计日工, unitProject) {
        if (ObjectUtils.isEmpty(计日工)) {
            return;
        }

        const model = 计日工[0].$;

        const otherProject = new OtherProject();
        otherProject.sequenceNbr = Snowflake.nextId();
        otherProject.extraName = '计日工';
        otherProject.sortNo = 7;
        otherProject.constructId = unitProject.constructId;
        otherProject.spId = unitProject.spId;
        otherProject.unitId = unitProject.sequenceNbr;
        otherProject.type = OtherProjectCalculationBaseConstant.jrg;
        otherProject.markSj = 1;
        otherProject.markSafa = 1;
        otherProject.amount = 1;
        otherProject.putOntotalFlag = true;
        if(!ObjectUtils.isEmpty(model)){
            otherProject.unit = model.计量单位;
            otherProject.dispNo = model.序号;
            otherProject.total = NumberUtil.costPriceAmountFormat(model.金额);
            otherProject.description = model.备注;
        }else {
            otherProject.dispNo = '4';
        }

        let qtJxTotal = 0;
        let qtCsTotal = 0;


        const dayWorksArray = [];
        let sort = 0;

        const types = ['人工', '材料', '机械'];

        for (let type of types) {
            const arr = 计日工[0][type]?.[0];
            if (!arr) continue;

            let otherProjectDayWork = new OtherProjectDayWork();
            let id = Snowflake.nextId();

            otherProjectDayWork.sequenceNbr = id;
            otherProjectDayWork.worksName = type;
            otherProjectDayWork.total = NumberUtil.costPriceAmountFormat(arr.$.金额);
            otherProjectDayWork.dispNo = types.indexOf(type) + 1;
            otherProjectDayWork.sortNo = sort++;
            otherProjectDayWork.constructId = unitProject.constructId;
            otherProjectDayWork.spId = unitProject.spId;
            otherProjectDayWork.unitId = unitProject.sequenceNbr;
            otherProjectDayWork.dataType = 1;

            dayWorksArray.push(otherProjectDayWork);

            const 子目 = arr[`${type}子目`];
            if (!子目) continue;

            for (let i = 0; i < 子目.length; i++) {
                const element = 子目[i].$;

                otherProjectDayWork = new OtherProjectDayWork();
                otherProjectDayWork.sequenceNbr = Snowflake.nextId();
                otherProjectDayWork.worksName = element.名称;
                otherProjectDayWork.specification = element.规格型号;
                otherProjectDayWork.unit = element.计量单位;
                otherProjectDayWork.tentativeQuantity = NumberUtil.qtxmAmountFormat(element.数量);

                otherProjectDayWork.price = NumberUtil.costPriceAmountFormat(element.市场价);
                otherProjectDayWork.total = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.tentativeQuantity, otherProjectDayWork.price));
                otherProjectDayWork.taxRemoval = 0;
                if(type === '材料'){
                    otherProjectDayWork.taxRemoval =  11.28;
                }
                if(type === '机械'){
                    otherProjectDayWork.taxRemoval =  8.66;
                }
                otherProjectDayWork.jxTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.total,otherProjectDayWork.taxRemoval/100));
                otherProjectDayWork.csPrice = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.price,(100-otherProjectDayWork.taxRemoval)/100)) ;
                otherProjectDayWork.csTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.total,(100-otherProjectDayWork.taxRemoval)/100)) ;
                //otherProjectDayWork.dispNo = `${types.indexOf(type) + 1}.${i + 1}`;
                otherProjectDayWork.dispNo = element.编码;
                otherProjectDayWork.sortNo = sort++;
                otherProjectDayWork.parentId = id;
                otherProjectDayWork.constructId = unitProject.constructId;
                otherProjectDayWork.spId = unitProject.spId;
                otherProjectDayWork.unitId = unitProject.sequenceNbr;
                otherProjectDayWork.dataType = 2;

                qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectDayWork.jxTotal);
                qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectDayWork.csTotal);

                dayWorksArray.push(otherProjectDayWork);
            }
        }

        otherProject.jxTotal = qtJxTotal;
        otherProject.csTotal = qtCsTotal;
        this.qbExtraTableArray.push(otherProject);
        unitProject.otherProjectDayWorks = dayWorksArray;
    }


    async convertZgj(暂估价, unitProject){


        if (ObjectUtils.isEmpty(暂估价) || ObjectUtils.isEmpty(unitProject)){
            return ;
        }
        let zgjRcjList = [];
        let model1;
        let model2;
        let model;
        if (ObjectUtils.isNotEmpty(暂估价[0].材料暂估价)){
            model1 = 暂估价[0].材料暂估价[0].暂估材料子目;
            model = model1;
        }
        if (ObjectUtils.isNotEmpty(暂估价[0].设备暂估价)){
            model2 = 暂估价[0].设备暂估价[0].暂估材料子目;
            if (model){
                model = model1.concat(model2);
            }else {
                model = model2;
            }
        }

        if (ObjectUtils.isEmpty(model)){
            return;
        }

        for (let i = 0; i<model.length; i++){
            if (ObjectUtils.isEmpty(model[i])){
                continue;
            }
            if (!ObjectUtils.isEmpty(model[i].$.名称) && model[i].$.名称.includes("名称")){
                continue;
            }

            let zgjRcj = {};
            //主键
            //zgjRcj.sequenceNbr = Snowflake.nextId();

            //编码
            zgjRcj.materialCode = model[i].$.编码;
            //名称
            zgjRcj.materialName = model[i].$.名称;
            //类型
            zgjRcj.kind = model[i].$.类型;
            if (ObjectUtils.isEmpty(model[i].$.类型)){
                zgjRcj.kind = -1;
            }else {
                if (model[i].$.类型.includes("材")){
                    zgjRcj.kind = 2;
                }else if (model[i].$.类型.includes("设")){
                    zgjRcj.kind = 4;
                }
            }

            //规格型号
            zgjRcj.specification = model[i].$.规格型号;
            //单位
            zgjRcj.unit = model[i].$.计量单位;
            //来源
            zgjRcj.source = "文件导入";
            //产地
            zgjRcj.producer =model[i].$.产地;
            //厂家
            zgjRcj.manufactor = model[i].$.厂家;
            //备注
            zgjRcj.remark = model[i].$.备注;
            //关联材料编码
            zgjRcj.relevancyMaterialCodeList = null;
            //锁定
            zgjRcj.lock = 0;
            //市场价
            zgjRcj.marketPrice = NumberUtil.costPriceAmountFormat(model[i].$.市场价);
            if (ObjectUtils.isEmpty(zgjRcj.marketPrice)){
                zgjRcj.marketPrice = 0;
            }
            zgjRcj.totalNumber = model[i].$.数量;
            //zgjRcjList.push(zgjRcj);

            await this.service.rcjProcess.addZgjList(unitProject,zgjRcj,"文件导入");
        }
        //unitProject.zgjRcjList = zgjRcjList;

    }


    async convertCbr(cbr,unitProject){
        if (ObjectUtils.isEmpty(cbr) || ObjectUtils.isEmpty(unitProject)){
            return ;
        }

        for (let zyclsbMx of cbr) {
            if (ObjectUtils.isEmpty(zyclsbMx)){
                continue;
            }
            let cbrRcj = {};
            //编码
            cbrRcj.materialCode = zyclsbMx.$.编码;
            //名称
            cbrRcj.materialName = zyclsbMx.$.名称;
            //规格型号
            cbrRcj.specification = zyclsbMx.$.规格型号;
            //单位
            cbrRcj.unit = zyclsbMx.$.计量单位;
            //来源
            cbrRcj.source = "文件导入";
            //合计数量
            cbrRcj.totalNumber = zyclsbMx.$.数量;
            //单价
            cbrRcj.marketPrice = zyclsbMx.$.市场价;
            //备注
            cbrRcj.remark = zyclsbMx.$.备注;
            //基础单价
            cbrRcj.benchmarkUnitPrice = zyclsbMx.$.预算价;
            //产地
            cbrRcj.producer = zyclsbMx.$.产地;
            //厂家
            cbrRcj.manufactor = zyclsbMx.$.厂家;

            await this.service.rcjProcess.addCbrList(unitProject,cbrRcj,"文件导入");
        }

    }




    // async convertItemBill(分部分项工程, unitProject) {
    //     let itemBillProjectArray = new Array();
    //     //构建 单独的单位工程一行数据
    //     let itemBillProject = new ItemBillProject();
    //
    //     let topId = Snowflake.nextId();
    //     itemBillProject.sequenceNbr = topId;
    //     itemBillProject.name = '单位工程';
    //     itemBillProject.kind = BranchProjectLevelConstant.top;
    //     itemBillProject.constructId = unitProject.constructId;
    //     itemBillProject.spId = unitProject.spId;
    //     itemBillProject.unitId = unitProject.sequenceNbr;
    //     itemBillProjectArray.push(itemBillProject)
    //
    //     let 分部子目 = 分部分项工程[0].分部子目;
    //
    //     if(ObjectUtils.isEmpty(分部子目)){
    //         unitProject.itemBillProjects = itemBillProjectArray;
    //         return;
    //     }
    //
    //     let sortNo =0;
    //     for (let i = 0; i < 分部子目.length; i++) {
    //         let 分部子目Element = 分部子目[i];
    //         let $ = 分部子目Element.$;
    //         //添加分布
    //         itemBillProject = new ItemBillProject();
    //         let fbId = Snowflake.nextId();
    //         itemBillProject.sequenceNbr = fbId;
    //         itemBillProject.name =$.名称;
    //         itemBillProject.bdCode =$.编码;
    //         itemBillProject.total =$.金额;
    //         itemBillProject.kind = BranchProjectLevelConstant.fb;
    //         itemBillProject.parentId = topId;
    //         itemBillProject.constructId = unitProject.constructId;
    //         itemBillProject.spId = unitProject.spId;
    //         itemBillProject.unitId = unitProject.sequenceNbr;
    //         itemBillProjectArray.push(itemBillProject);
    //         //此处判断下级是分布还是清单 二
    //         if(ObjectUtils.isEmpty(分部子目Element.分部分项工程量清单)){
    //             //子分部
    //             let 子分部 = 分部子目Element.分部子目;
    //             for (let j = 0; j < 子分部.length; j++) {
    //                 let 子分部Element = 子分部[j];
    //                 $ = 子分部Element.$;
    //                 let zfbId = Snowflake.nextId();
    //                 itemBillProject.sequenceNbr = zfbId;
    //                 itemBillProject.name =$.名称;
    //                 itemBillProject.bdCode =$.编码;
    //                 itemBillProject.total =$.金额;
    //                 itemBillProject.kind = BranchProjectLevelConstant.zfb;
    //                 itemBillProject.parentId = fbId;
    //                 itemBillProject.constructId = unitProject.constructId;
    //                 itemBillProject.spId = unitProject.spId;
    //                 itemBillProject.unitId = unitProject.sequenceNbr;
    //                 itemBillProjectArray.push(itemBillProject);
    //
    //                 //此处判断下级是分布还是清单 三
    //                 if(ObjectUtils.isEmpty(子分部Element.分部分项工程量清单)){
    //                     //子分部
    //                     let 子子分部 = 子分部Element.分部子目;
    //                     for (let k = 0; k < 子子分部.length; k++) {
    //                         let 子子分部Element = 子子分部[k];
    //                         $ = 子子分部Element.$;
    //                         let zzfbId = Snowflake.nextId();
    //                         itemBillProject.sequenceNbr = zzfbId;
    //                         itemBillProject.name =$.名称;
    //                         itemBillProject.bdCode =$.编码;
    //                         itemBillProject.total =$.金额;
    //                         itemBillProject.kind = BranchProjectLevelConstant.zfb;
    //                         itemBillProject.parentId = zfbId;
    //                         itemBillProject.constructId = unitProject.constructId;
    //                         itemBillProject.spId = unitProject.spId;
    //                         itemBillProject.unitId = unitProject.sequenceNbr;
    //                         itemBillProjectArray.push(itemBillProject);
    //                         //此处判断下级是分布还是清单 四
    //                         if(ObjectUtils.isEmpty(子子分部Element.分部分项工程量清单)){
    //                             //子分部
    //                             let 子子子分部 = 子子分部Element.分部子目;
    //                             for (let q = 0; q < 子子分部.length; q++) {
    //                                 let 子子子分部Element = 子子子分部[q];
    //                                 $ = 子子子分部Element.$;
    //                                 let zzzfbId = Snowflake.nextId();
    //                                 itemBillProject.sequenceNbr = zzzfbId;
    //                                 itemBillProject.name =$.名称;
    //                                 itemBillProject.bdCode =$.编码;
    //                                 itemBillProject.total =$.金额;
    //                                 itemBillProject.kind = BranchProjectLevelConstant.zfb;
    //                                 itemBillProject.parentId = zzfbId;
    //                                 itemBillProject.constructId = unitProject.constructId;
    //                                 itemBillProject.spId = unitProject.spId;
    //                                 itemBillProject.unitId = unitProject.sequenceNbr;
    //                                 itemBillProjectArray.push(itemBillProject);
    //
    //                                 //清单
    //                                 let 分部分项工程量清单 = 子子分部Element.分部分项工程量清单;
    //                                 for (let w = 0; w < 分部分项工程量清单.length; w++) {
    //                                     $ = 分部分项工程量清单[w].$;
    //                                     itemBillProject = new ItemBillProject();
    //                                     itemBillProject.sequenceNbr = Snowflake.nextId();
    //                                     itemBillProject.name =$.项目名称;
    //                                     itemBillProject.bdCode =$.项目编码;
    //                                     itemBillProject.unit =$.计量单位;
    //                                     itemBillProject.quantity =$.工程量;
    //                                     itemBillProject.rfee =$.人工费单价;
    //                                     itemBillProject.cfee =$.材料费单价;
    //                                     itemBillProject.jfee =$.机械费单价;
    //                                     itemBillProject.managerFee =$.管理费单价;
    //                                     itemBillProject.profitFee =$.利润单价;
    //                                     itemBillProject.price =$.综合单价;
    //                                     itemBillProject.total =$.综合合价;
    //                                     itemBillProject.projectAttr =$.项目特征;
    //                                     itemBillProject.kind = BranchProjectLevelConstant.qd;
    //                                     itemBillProject.sortNo =sortNo++;
    //                                     itemBillProject.parentId = zzzfbId;
    //                                     itemBillProject.constructId = unitProject.constructId;
    //                                     itemBillProject.spId = unitProject.spId;
    //                                     itemBillProject.unitId = unitProject.sequenceNbr;
    //                                     itemBillProjectArray.push(itemBillProject);
    //
    //                                 }
    //                             }
    //                         }else {
    //                             //清单
    //                             let 分部分项工程量清单 = 子子分部Element.分部分项工程量清单;
    //                             for (let e = 0; e < 分部分项工程量清单.length;e++) {
    //                                 $ = 分部分项工程量清单[e].$;
    //                                 itemBillProject = new ItemBillProject();
    //                                 itemBillProject.sequenceNbr = Snowflake.nextId();
    //                                 itemBillProject.name =$.项目名称;
    //                                 itemBillProject.bdCode =$.项目编码;
    //                                 itemBillProject.unit =$.计量单位;
    //                                 itemBillProject.quantity =$.工程量;
    //                                 itemBillProject.rfee =$.人工费单价;
    //                                 itemBillProject.cfee =$.材料费单价;
    //                                 itemBillProject.jfee =$.机械费单价;
    //                                 itemBillProject.managerFee =$.管理费单价;
    //                                 itemBillProject.profitFee =$.利润单价;
    //                                 itemBillProject.price =$.综合单价;
    //                                 itemBillProject.total =$.综合合价;
    //                                 itemBillProject.projectAttr =$.项目特征;
    //                                 itemBillProject.kind = BranchProjectLevelConstant.qd;
    //                                 itemBillProject.sortNo =sortNo++;
    //                                 itemBillProject.parentId = zzfbId;
    //                                 itemBillProject.constructId = unitProject.constructId;
    //                                 itemBillProject.spId = unitProject.spId;
    //                                 itemBillProject.unitId = unitProject.sequenceNbr;
    //                                 itemBillProjectArray.push(itemBillProject);
    //                             }
    //                         }
    //                     }
    //                 }else {
    //                     //清单
    //                     let 分部分项工程量清单 = 子分部Element.分部分项工程量清单;
    //                     for (let r = 0; r < 分部分项工程量清单.length; r++) {
    //                         $ = 分部分项工程量清单[r].$;
    //                         itemBillProject = new ItemBillProject();
    //                         itemBillProject.sequenceNbr = Snowflake.nextId();
    //                         itemBillProject.name =$.项目名称;
    //                         itemBillProject.bdCode =$.项目编码;
    //                         itemBillProject.unit =$.计量单位;
    //                         itemBillProject.quantity =$.工程量;
    //                         itemBillProject.rfee =$.人工费单价;
    //                         itemBillProject.cfee =$.材料费单价;
    //                         itemBillProject.jfee =$.机械费单价;
    //                         itemBillProject.managerFee =$.管理费单价;
    //                         itemBillProject.profitFee =$.利润单价;
    //                         itemBillProject.price =$.综合单价;
    //                         itemBillProject.total =$.综合合价;
    //                         itemBillProject.projectAttr =$.项目特征;
    //                         itemBillProject.kind = BranchProjectLevelConstant.qd;
    //                         itemBillProject.sortNo =sortNo++;
    //                         itemBillProject.parentId = zfbId;
    //                         itemBillProject.constructId = unitProject.constructId;
    //                         itemBillProject.spId = unitProject.spId;
    //                         itemBillProject.unitId = unitProject.sequenceNbr;
    //                         itemBillProjectArray.push(itemBillProject);
    //
    //
    //                     }
    //                 }
    //
    //             }
    //         }else {
    //             //清单
    //             let 分部分项工程量清单 = 分部子目Element.分部分项工程量清单;
    //             for (let t = 0; t < 分部分项工程量清单.length; t++) {
    //                  $ = 分部分项工程量清单[t].$;
    //                 itemBillProject = new ItemBillProject();
    //                 itemBillProject.sequenceNbr = Snowflake.nextId();
    //                 itemBillProject.name =$.项目名称;
    //                 itemBillProject.bdCode =$.项目编码;
    //                 itemBillProject.unit =$.计量单位;
    //                 itemBillProject.quantity =$.工程量;
    //                 itemBillProject.rfee =$.人工费单价;
    //                 itemBillProject.cfee =$.材料费单价;
    //                 itemBillProject.jfee =$.机械费单价;
    //                 itemBillProject.managerFee =$.管理费单价;
    //                 itemBillProject.profitFee =$.利润单价;
    //                 itemBillProject.price =$.综合单价;
    //                 itemBillProject.total =$.综合合价;
    //                 itemBillProject.projectAttr =$.项目特征;
    //                 itemBillProject.kind = BranchProjectLevelConstant.qd;
    //                 itemBillProject.sortNo =sortNo++;
    //                 itemBillProject.parentId = fbId;
    //                 itemBillProject.constructId = unitProject.constructId;
    //                 itemBillProject.spId = unitProject.spId;
    //                 itemBillProject.unitId = unitProject.sequenceNbr;
    //                 itemBillProjectArray.push(itemBillProject);
    //             }
    //         }
    //     }
    //
    //     unitProject.itemBillProjects = itemBillProjectArray;
    // }


    /**
     * 分部分项
     * @param 分部分项工程
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertItemBill(分部分项工程, unitProject) {
        let itemBillProjectArray = [];
        let itemBillProject = new ItemBillProject();
        let topId = Snowflake.nextId();
        itemBillProject.sequenceNbr = topId;
        itemBillProject.name = '单位工程';
        itemBillProject.kind = BranchProjectLevelConstant.top;
        itemBillProject.constructId = unitProject.constructId;
        itemBillProject.spId = unitProject.spId;
        itemBillProject.unitId = unitProject.sequenceNbr;
        itemBillProject.displaySign = BranchProjectDisplayConstant.open;
        itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
        itemBillProjectArray.push(itemBillProject);
        let 分部子目 = 分部分项工程[0].分部子目;
        if (ObjectUtils.isEmpty(分部子目)) {
            let 分部分项工程量清单 = 分部分项工程[0].分部分项工程量清单;
            if(!ObjectUtils.isEmpty(分部分项工程量清单)){
                await this.convertItemBillQd(分部分项工程量清单, topId, unitProject, itemBillProjectArray);
            }
            unitProject.itemBillProjects = arrayToTree(itemBillProjectArray);
            return;
        }

        let kind = BranchProjectLevelConstant.fb;
        // 递归处理子项目
        const createSubProjects =async  (subProjects, parentId,kind) => {
            for (let i = 0; i < subProjects.length; i++) {
                let subProjectElement = subProjects[i];
                let $ = subProjectElement.$;
                itemBillProject = new ItemBillProject();
                let id = Snowflake.nextId();
                itemBillProject.sequenceNbr = id;
                itemBillProject.name = $.名称;
                itemBillProject.bdCode = $.编码;
                itemBillProject.total = Number($.金额);
                itemBillProject.kind = kind;
                itemBillProject.parentId = parentId;
                itemBillProject.constructId = unitProject.constructId;
                itemBillProject.spId = unitProject.spId;
                itemBillProject.unitId = unitProject.sequenceNbr;
                itemBillProject.displaySign = BranchProjectDisplayConstant.open;
                itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
                itemBillProjectArray.push(itemBillProject);

                if (ObjectUtils.isEmpty(subProjectElement.分部分项工程量清单)) {
                    await  createSubProjects(subProjectElement.分部子目, id,BranchProjectLevelConstant.zfb);
                } else {
                    let 分部分项工程量清单 = subProjectElement.分部分项工程量清单;
                    await this.convertItemBillQd(分部分项工程量清单, id, unitProject, itemBillProjectArray);

                }
            }
        };

        await createSubProjects(分部子目, topId,kind);
        unitProject.itemBillProjects = arrayToTree(itemBillProjectArray);
    }


    async convertItemBillQd(分部分项工程量清单, id, unitProject, itemBillProjectArray) {
        for (let t = 0; t < 分部分项工程量清单.length; t++) {
            let $ = 分部分项工程量清单[t].$;
            let itemBillProject = new ItemBillProject();
            itemBillProject.sequenceNbr = Snowflake.nextId();
            itemBillProject.name = $.项目名称;
            itemBillProject.name = $.项目名称;
            itemBillProject.bdCode = $.项目编码;
            itemBillProject.fxCode = $.项目编码;
            itemBillProject.unit = $.计量单位;
            // itemBillProject.quantity = $.工程量;
            itemBillProject.quantity = NumberUtil.numberScale($.工程量,getUnitFormatEnum(itemBillProject.unit,unitProject.constructId).value) + "";
            //清单工程量=清单工程量表达式/单位符号前数值
            let 单位num = $.计量单位.replace(/[^0-9].*/ig, '') !== '' ? $.计量单位.replace(/[^0-9].*/ig, '') : 1;
            itemBillProject.quantityExpression = NumberUtil.multiplyToStringExEnd0(单位num, $.工程量, ConstantUtil.QD_DE_DECIMAL_POINT);
            itemBillProject.quantityExpressionNbr = NumberUtil.multiplyToStringExEnd0(单位num, $.工程量, ConstantUtil.QD_DE_DECIMAL_POINT);
            // itemBillProject.rfee = $.人工费单价;
            // itemBillProject.cfee = $.材料费单价;
            // itemBillProject.jfee = $.机械费单价;
            // itemBillProject.managerFee = $.管理费单价;
            // itemBillProject.profitFee = $.利润单价;
            // itemBillProject.price = $.综合单价;
            // itemBillProject.total = $.综合合价;
            itemBillProject.projectAttr = $.项目特征;
            itemBillProject.kind = BranchProjectLevelConstant.qd;
            itemBillProject.dispNo = (this.dispNo++) + '';
            itemBillProject.parentId = id;
            itemBillProject.constructId = unitProject.constructId;
            itemBillProject.spId = unitProject.spId;
            itemBillProject.unitId = unitProject.sequenceNbr;
            itemBillProject.displaySign = BranchProjectDisplayConstant.noSign;
            itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
            if (ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode))) {
                //如果map中没有去查数据库
                let res = await this.service.baseListService.queryQdByCode(itemBillProject.fxCode);
                if (!ObjectUtils.isEmpty(res)) {
                    this.qdMap.set(itemBillProject.fxCode, res)
                }
            }
            itemBillProject.standardId = !ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode)) ? this.qdMap.get(itemBillProject.fxCode).sequenceNbr : '';
            itemBillProject.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode)) ? 0 : 1;

            itemBillProjectArray.push(itemBillProject);
        }
    }

    /**
     * 单价措施项目（目前只处理单价措施清单）
     * @param 措施项目
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertMeasureTableDJ(措施项目, unitProject) {
        if(ObjectUtils.isEmpty(措施项目)){
            return ;
        }
        let 措施标题 = 措施项目[0].措施标题;
        if(!ObjectUtils.isEmpty(措施标题)){



            if(ObjectUtils.isNotEmpty(措施标题.filter(aa => aa.$.名称=='单价措施项目'))){
                for (let i = 0; i < 措施标题.length; i++) {
                    let 措施标题Element = 措施标题[i];

                    let $ = 措施标题Element.$;
                    if($.名称==='单价措施项目'){
                        await this.creatDjcs(措施标题Element, unitProject, $);

                    }
                }
            }else {
                //特殊处理单价措施没有 单机措施项目标题的情况
                措施标题 = 措施标题.filter(aa => aa.$.名称!=='总价措施项目')
                if(ObjectUtils.isNotEmpty( 措施标题)){
                    let $ ={
                        '序号':'1',
                        '编码':'1',
                        '名称':'单价措施项目',
                        '金额':'0',
                    }
                    let djcsBt ={
                        '$':$,
                        '措施标题':措施标题
                    }

                    await this.creatDjcs(djcsBt, unitProject, $);
                }
            }


        }
    }

    async creatDjcs(措施标题Element, unitProject, $) {
        let djMeasureProjectTableArray = new Array();

        let 单价措施标题 = 措施标题Element.措施标题;
        let dispNo = 1;

        if (ObjectUtils.isEmpty(单价措施标题) && !ObjectUtils.isEmpty(措施标题Element.措施子目清单)) {
            await this.djcsQd(措施标题Element.措施子目清单, dispNo, null, unitProject, djMeasureProjectTableArray);
        } else {
            //遍历标题
            for (let j = 0; j < 单价措施标题.length; j++) {
                $ = 单价措施标题[j].$;
                //存放标题
                let measureProjectTableBt = new MeasureProjectTable();
                let btId = Snowflake.nextId();
                measureProjectTableBt.sequenceNbr = btId;
                measureProjectTableBt.fxCode = $.编码;
                measureProjectTableBt.name = $.名称;
                measureProjectTableBt.total = Number($.金额);
                measureProjectTableBt.constructId = unitProject.constructId;
                measureProjectTableBt.spId = unitProject.spId;
                measureProjectTableBt.unitId = unitProject.sequenceNbr;
                measureProjectTableBt.kind = BranchProjectLevelConstant.zfb;
                measureProjectTableBt.displaySign = BranchProjectDisplayConstant.open;
                measureProjectTableBt.displayStatu = BranchProjectDisplayConstant.displayMax;
                measureProjectTableBt.adjustmentCoefficient = 1;
                djMeasureProjectTableArray.push(measureProjectTableBt)
                //存放标题下的清单
                let 措施子目清单 = 单价措施标题[j].措施子目清单;
                if (!ObjectUtils.isEmpty(措施子目清单)) {
                    await this.djcsQd(措施子目清单, dispNo, btId, unitProject, djMeasureProjectTableArray);
                }
            }
        }
        unitProject.djMeasureProjectTableArray = djMeasureProjectTableArray;
    }

    async djcsQd(措施子目清单, dispNo, parentId, unitProject, djMeasureProjectTableArray) {
        for (let k = 0; k < 措施子目清单.length; k++) {
            let $ = 措施子目清单[k].$;
            let measureProjectTable = new MeasureProjectTable();
            measureProjectTable.sequenceNbr = Snowflake.nextId();
            measureProjectTable.dispNo = (dispNo++) + '';
            measureProjectTable.name = $.名称;
            measureProjectTable.name = $.名称;
            measureProjectTable.bdCode = $.编码;
            measureProjectTable.fxCode = $.编码;
            measureProjectTable.total = Number($.金额);
            measureProjectTable.projectAttr = $.项目特征;
            measureProjectTable.unit = $.计量单位;
            // measureProjectTable.quantity = $.工程量;
            measureProjectTable.quantity = NumberUtil.numberScale($.工程量,getUnitFormatEnum(measureProjectTable.unit,unitProject.constructId).value);
            let 单位num = $.计量单位.replace(/[^0-9].*/ig, '') !== '' ? $.计量单位.replace(/[^0-9].*/ig, '') : 1;
            measureProjectTable.quantityExpression = NumberUtil.multiplyToStringExEnd0(单位num, $.工程量, ConstantUtil.QD_DE_DECIMAL_POINT);
            measureProjectTable.quantityExpressionNbr = NumberUtil.multiplyToStringExEnd0(单位num, $.工程量, ConstantUtil.QD_DE_DECIMAL_POINT);
            // measureProjectTable.rfee = $.人工费单价;
            // measureProjectTable.cfee = $.材料费单价;
            // measureProjectTable.jfee = $.机械费单价;
            // measureProjectTable.managerFee = $.管理费单价;
            // measureProjectTable.profitFee = $.利润单价;
            // measureProjectTable.price = $.综合单价;
            // measureProjectTable.total = $.综合合价;
            measureProjectTable.kind = BranchProjectLevelConstant.qd;
            //单价措施类型
            measureProjectTable.parentId = parentId;
            measureProjectTable.constructId = unitProject.constructId;
            measureProjectTable.spId = unitProject.spId;
            measureProjectTable.unitId = unitProject.sequenceNbr;
            measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
            measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
            measureProjectTable.adjustmentCoefficient = 1;

            if (ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))) {
                //如果map中没有去查数据库
                let res = await this.service.baseListService.queryQdByCode(measureProjectTable.fxCode);
                if (!ObjectUtils.isEmpty(res)) {
                    this.qdMap.set(measureProjectTable.fxCode, res)
                }
            }

            if(!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                if (!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode)){
                    measureProjectTable.zjcsClassCode =  Number(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode);
                }
            }
            measureProjectTable.standardId = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? this.qdMap.get(measureProjectTable.fxCode).sequenceNbr : '';
            measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;
            djMeasureProjectTableArray.push(measureProjectTable);
        }

    }

    /**
     *
     * @param 总价措施措施项目
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertMeasureTableZJ(措施项目, unitProject) {
        if(ObjectUtils.isEmpty(措施项目)){
            return ;
        }
        let 措施标题 = 措施项目[0].措施标题;
        if(!ObjectUtils.isEmpty(措施标题)){
            for (let i = 0; i < 措施标题.length; i++) {
                let 措施标题Element = 措施标题[i];
                let $ = 措施标题Element.$;
                if($.名称==='总价措施项目'){
                    let 总价措施标题 = 措施标题Element.措施标题;
                    if(!ObjectUtils.isEmpty(总价措施标题)){
                        for (let j = 0; j < 总价措施标题.length; j++) {
                            let 总价措施标题Element = 总价措施标题[j];
                            $ = 总价措施标题Element.$;
                            if($.名称==='安全生产、文明施工费'){
                                let awfMeasureProjectTableArray = new Array();
                                let 措施子目清单 = 总价措施标题Element.措施子目清单;
                                if(!ObjectUtils.isEmpty(措施子目清单)){
                                    for (let k = 0; k < 措施子目清单.length; k++) {
                                        $ = 措施子目清单[k].$ ;
                                        let measureProjectTable = new MeasureProjectTable();
                                        measureProjectTable.sequenceNbr =Snowflake.nextId();
                                        measureProjectTable.dispNo = (k+1)+'';
                                        measureProjectTable.name = $.名称;
                                        measureProjectTable.name = $.名称;
                                        measureProjectTable.bdCode = $.编码;
                                        measureProjectTable.fxCode = $.编码;
                                        measureProjectTable.projectAttr = $.项目特征;
                                        measureProjectTable.unit = '项';
                                        // measureProjectTable.quantity = $.工程量;
                                        measureProjectTable.quantity = NumberUtil.numberScale($.工程量,getUnitFormatEnum(measureProjectTable.unit, unitProject.constructId).value);
                                        let 单位num = $.计量单位.replace(/[^0-9].*/ig,'')!==''?$.计量单位.replace(/[^0-9].*/ig,''): 1;
                                        measureProjectTable.quantityExpression =NumberUtil.multiplyToStringExEnd0(单位num, $.工程量,ConstantUtil.QD_DE_DECIMAL_POINT);
                                        measureProjectTable.quantityExpressionNbr = NumberUtil.multiplyToStringExEnd0(单位num, $.工程量,ConstantUtil.QD_DE_DECIMAL_POINT);
                                        // measureProjectTable.rfee = $.人工费单价;
                                        // measureProjectTable.cfee = $.材料费单价;
                                        // measureProjectTable.jfee = $.机械费单价;
                                        // measureProjectTable.managerFee = $.管理费单价;
                                        // measureProjectTable.profitFee = $.利润单价;
                                        // measureProjectTable.price = $.综合单价;
                                        // measureProjectTable.total = $.综合合价;
                                        measureProjectTable.kind = BranchProjectLevelConstant.qd;
                                        measureProjectTable.constructId = unitProject.constructId;
                                        measureProjectTable.spId = unitProject.spId;
                                        measureProjectTable.unitId = unitProject.sequenceNbr;
                                        measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
                                        measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                                        measureProjectTable.adjustmentCoefficient = 1;
                                        if(ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                            //如果map中没有去查数据库
                                            let res =await this.service.baseListService.queryQdByCode(measureProjectTable.fxCode);
                                            if(!ObjectUtils.isEmpty(res)){
                                                this.qdMap.set(measureProjectTable.fxCode,res)
                                            }
                                        }
                                        if(!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                            if (!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode)){
                                                measureProjectTable.zjcsClassCode =  Number(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode);
                                            }
                                        }
                                        measureProjectTable.standardId = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))? this.qdMap.get(measureProjectTable.fxCode).sequenceNbr:'';
                                        measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;
                                        awfMeasureProjectTableArray.push(measureProjectTable)
                                    }
                                    unitProject.awfMeasureProjectTableArray = awfMeasureProjectTableArray;
                                }
                            }else if($.名称=== '其他总价措施项目'){
                                let zjMeasureProjectTableArray = new Array();
                                let 措施子目清单 = 总价措施标题Element.措施子目清单;
                                if(!ObjectUtils.isEmpty(措施子目清单)){
                                    for (let k = 0; k < 措施子目清单.length; k++) {
                                        $ = 措施子目清单[k].$ ;
                                        let measureProjectTable = new MeasureProjectTable();
                                        measureProjectTable.sequenceNbr =Snowflake.nextId();
                                        measureProjectTable.dispNo = (k+1)+'';
                                        measureProjectTable.name = $.名称;
                                        measureProjectTable.name = $.名称;
                                        measureProjectTable.bdCode = $.编码;
                                        measureProjectTable.fxCode = $.编码;
                                        measureProjectTable.projectAttr = $.项目特征;
                                        measureProjectTable.unit = $.计量单位;
                                        // measureProjectTable.quantity = $.工程量;
                                        measureProjectTable.quantity = NumberUtil.numberScale($.工程量,getUnitFormatEnum(measureProjectTable.unit, unitProject.constructId).value);
                                        let 单位num = $.计量单位.replace(/[^0-9].*/ig,'')!==''?$.计量单位.replace(/[^0-9].*/ig,''): 1;
                                        measureProjectTable.quantityExpression =NumberUtil.multiplyToStringExEnd0(单位num, $.工程量,ConstantUtil.QD_DE_DECIMAL_POINT);
                                        measureProjectTable.quantityExpressionNbr = NumberUtil.multiplyToStringExEnd0(单位num, $.工程量,ConstantUtil.QD_DE_DECIMAL_POINT);
                                        // measureProjectTable.rfee = $.人工费单价;
                                        // measureProjectTable.cfee = $.材料费单价;
                                        // measureProjectTable.jfee = $.机械费单价;
                                        // measureProjectTable.managerFee = $.管理费单价;
                                        // measureProjectTable.profitFee = $.利润单价;
                                        // measureProjectTable.price = $.综合单价;
                                        // measureProjectTable.total = $.综合合价;
                                        measureProjectTable.kind = BranchProjectLevelConstant.qd;
                                        measureProjectTable.constructId = unitProject.constructId;
                                        measureProjectTable.spId = unitProject.spId;
                                        measureProjectTable.unitId = unitProject.sequenceNbr;
                                        measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
                                        measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                                        measureProjectTable.adjustmentCoefficient = 1;
                                        if(ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                            //如果map中没有去查数据库
                                            let res = await this.service.baseListService.queryQdByCode(measureProjectTable.fxCode);
                                            if(!ObjectUtils.isEmpty(res)){
                                                this.qdMap.set(measureProjectTable.fxCode,res)
                                            }
                                        }
                                        if(!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                            if (!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode)){
                                                measureProjectTable.zjcsClassCode =  Number(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode);
                                            }
                                        }
                                        measureProjectTable.standardId = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))? this.qdMap.get(measureProjectTable.fxCode).sequenceNbr:'';
                                        measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;
                                        zjMeasureProjectTableArray.push(measureProjectTable);
                                    }
                                    unitProject.zjMeasureProjectTableArray = zjMeasureProjectTableArray;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

}


AnalyzingXMLServiceHZB.toString = () => '[class AnalyzingXMLServiceHZB]';
module.exports = AnalyzingXMLServiceHZB;