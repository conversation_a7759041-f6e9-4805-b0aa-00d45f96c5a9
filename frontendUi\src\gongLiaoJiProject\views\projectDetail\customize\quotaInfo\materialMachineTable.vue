<!--
 * @Descripttion: 人材机明细
 * @Author: liuxia
 * @Date: 2023-05-29 11:39:52XS
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-06-17 11:24:10
-->
<template>
  <div class="material-machine-content">
    <!-- {{currentInfo}} -->
    <!-- &&
        (props.currentInfo?.bdCode || props.currentInfo?.fxCode) &&
        (props.currentInfo?.kind !== '07' || props.currentInfo.isCostDe === 4) &&
        props.currentInfo?.levelMark !== 0 &&
        !(projectStore.deType === '22' && props.currentInfo.rcjFlag === 1) -->
    <div
      class="head"
      v-if="
        props.currentInfo &&
        deMapFun.isDe(props.currentInfo?.kind) &&
        !['00', '01', '02', '05', '06', '09', '07', '-1'].includes(
          props.currentInfo?.kind
        ) &&
        isEditDe &&
        parentRcjIsShow
      "
    >
      <a-button type="text" @click="addRcjData"
        ><icon-font type="icon-biaodan-charu"></icon-font>插入</a-button
      >
      <a-button type="text" @click="bcRcjData"
        ><icon-font type="icon-biaodan-charu"></icon-font>补充</a-button
      >
      <a-button type="text" @click="deleteType"
        ><icon-font type="icon-biaodan-shanchu"></icon-font>删除</a-button
      >
    </div>
    <div class="content" :class="autoHeight">
      <vxe-table
        ref="vexTable"
        keep-source
        :data="vexTableData"
        :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
        :tree-config="{
          children: 'pbs',
          expandAll: true,
        }"
        height="auto"
        @current-change="currentChangeEvent"
        @edit-closed="editClosedEvent"
        @cell-dblclick="cellDBLClickEvent"
        @edit-actived="editActivated"
        @cell-click="
          cellData => {
            useCellClickEvent(cellData, tableCellClick, ['materialCode']);
          }
        "
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          beforeEditMethod: customCellBeforeEditMethod,
        }"
        :scroll-x="{ enabled: true, gt: 20 }"
        :scroll-y="{ enabled: true, gt: 20 }"
        class="table-scrollbar table-edit-common"
        :cell-class-name="cellClassName"
        :row-class-name="rowClassName"
        :menu-config="contextmenuList"
        @menu-click="onContextMenuClick"
        @keydown="handleKeyDownEvent"
      >
        <vxe-column :min-width="columnWidth(35)" field="sortNo"> </vxe-column>
        <!-- <vxe-column type="seq" width="60" title="序号"> </vxe-column> -->
        <vxe-column
          tree-node
          title="编码"
          field="materialCode"
          :width="columnWidth(130)"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row }">
            <div>{{ row.materialCode }}</div>
          </template>
          <template #edit="{ row }">
            <vxeTableEditSelect
              :filedValue="row.materialCode"
              v-if="
                row.isTempRemove !== 1 &&
                deMapFun.isDe(props.currentInfo?.kind) &&
                (props.currentInfo?.kind !== '07' ||
                  props.currentInfo.isCostDe === 4) &&
                !deMapFun.isTz(row.materialCode) &&
                !deMapFun.isJxxs(row.materialCode)
              "
              :list="bmCodeHcList"
              :isNotLimit="true"
              placeholder="材料编码"
              @update:filedValue="
                newValue => {
                  row.materialCode = newValue.split('：')[0];
                }
              "
              @showTable="RefreshMemoryCode(currentInfo)"
              @usePosition="
                v => {
                  spByCode = v;
                }
              "
            ></vxeTableEditSelect>
            <!-- <vxe-input
              v-model="row.materialCode"
              v-if="
                row.isTempRemove !== 1 &&
                deMapFun.isDe(props.currentInfo?.kind) &&
                (props.currentInfo?.kind !== '07' ||
                  props.currentInfo.isCostDe === 4) &&
                !deMapFun.isJxxs(row.materialCode)
              "
              placeholder="请输入材料编码"
            ></vxe-input> -->
            <span v-else>{{ row.materialCode }}</span>
          </template>
        </vxe-column>
        <vxe-column title="类别" field="type" :min-width="columnWidth(80)" :edit-render="{}">
          <template #default="{ row }">
            <span>{{ row.type }}</span>
          </template>
          <template #edit="{ row }">
            <vxe-select
              v-model="row.type"
              transfer
              v-if="
                !isChangeAva(row) &&
                row.isTempRemove !== 1 &&
                row.isFyrcj == 1 &&
                deMapFun.isDe(props.currentInfo?.kind) &&
                (props.currentInfo?.kind !== '07' ||
                  props.currentInfo.isCostDe === 4) &&
                (row.kind == 2 || row.kind == 5 || row.kind == 4) &&
                currentInfo?.parentLevel2 != '配比材料' &&
                !deMapFun.isJxxs(row.materialCode) &&
                !deMapFun.isTz(row.materialCode) &&
                !row.rcjPbsId &&
                (Object.hasOwnProperty.call(row, 'pbs') ||
                  !deMapFun.isQtclf(row.materialCode))
              "
            >
              <vxe-option
                v-for="item in typeList"
                :key="item.value"
                :value="item.name"
                :label="item.name"
              ></vxe-option>
            </vxe-select>
            <template v-else>{{ row.type }}</template>
          </template>
        </vxe-column>
        <vxe-column
          title="名称"
          field="materialName"
          :min-width="columnWidth(100)"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row }">
            <span>{{ row.materialName }}</span>
          </template>

          <template #edit="{ row }">
            <vxe-input
              :maxlength="50"
              v-model="row.materialName"
              v-if="
                row.isTempRemove !== 1 &&
                row.isFyrcj == 1 &&
                deMapFun.isDe(props.currentInfo?.kind) &&
                (props.currentInfo?.kind !== '07' ||
                  props.currentInfo.isCostDe === 4) &&
                !deMapFun.isJxxs(row.materialCode) &&
                !deMapFun.isTz(row.materialCode) &&
                !deMapFun.isQtclf(row.materialCode)
              "
            />
            <span v-else>{{ row.materialName }}</span>
          </template>
        </vxe-column>
        <vxe-column
          title="规格型号"
          field="specification"
          :min-width="columnWidth(100)"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row }">
            <span>{{ row.specification }}</span>
          </template>

          <template #edit="{ row }">
            <vxe-input
              :maxlength="50"
              v-model="row.specification"
              v-if="
                row.isTempRemove !== 1 &&
                deMapFun.isDe(props.currentInfo?.kind) &&
                (props.currentInfo?.kind !== '07' ||
                  props.currentInfo.isCostDe === 4) &&
                !deMapFun.isTz(row.materialCode) &&
                !deMapFun.isJxxs(row.materialCode)
              "
            />
            <span v-else>{{ row.specification }}</span>
          </template>
        </vxe-column>
        <vxe-column title="单位" field="unit" :min-width="columnWidth(70)" :edit-render="{}">
          <template #default="{ row }">
            <span>{{ row.unit }}</span>
          </template>
          <template #edit="{ row }">
            <vxe-select
              v-model="row.unit"
              transfer
              v-if="
                row.isTempRemove !== 1 &&
                row.isFyrcj == 1 &&
                deMapFun.isDe(props.currentInfo?.kind) &&
                (props.currentInfo?.kind !== '07' ||
                  props.currentInfo.isCostDe === 4) &&
                !deMapFun.isJxxs(row.materialCode) &&
                !deMapFun.isTz(row.materialCode) &&
                !deMapFun.isQtclf(row.materialCode)
              "
            >
              <vxe-option
                v-for="item in unitList"
                :key="item"
                :value="item"
                :label="item"
              ></vxe-option>
            </vxe-select>
            <span v-else>{{ row.unit }}</span>
          </template>
        </vxe-column>
        <vxe-column
          title="消耗量"
          field="resQty"
          :min-width="columnWidth(70)"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
          v-if="cxHideColumn"
        >
          <template #default="{ row }">
            <span
              v-if="!['00', '01', '02'].includes(props.currentInfo?.kind)"
              >{{
                decimalFormat(row.qts || row.resQty, 'DETAIL_RCJ_RESQTY_PATH')
              }}</span
            >
          </template>

          <template #edit="{ row }">
            <!-- &&
                (props.currentInfo?.kind != '08' ||
                  ['主材费', '设备费'].includes(row.type)) -->
            <vxe-input
              v-model="row.resQty"
              v-if="
                !row.isNumLock &&
                row.isTempRemove !== 1 &&
                deMapFun.isDe(props.currentInfo?.kind) &&
                (props.currentInfo?.kind !== '07' ||
                  props.currentInfo.isCostDe === 4) &&
                !deMapFun.isCgf(props.currentInfo.deCode)
              "
              @keyup="
                row.resQty = (row.resQty.match(/-?\d{0,8}(\.\d*)?/) || [''])[0]
              "
            />
            <span v-else-if="!deMapFun.isDe(props.currentInfo?.kind)"></span>
            <span v-else-if="props.currentInfo?.kind != '00'">{{
              row.resQty
            }}</span>
          </template>
        </vxe-column>
        <vxe-column
          title="数量"
          field="totalNumber"
          :min-width="columnWidth(70)"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row }">
            <span>{{
              decimalFormat(row.totalNumber, 'DETAIL_RCJ_TOTALNUMBER_PATH')
            }}</span>
          </template>

          <template #edit="{ row }">
            <vxe-input
              v-model="row.totalNumber"
              v-if="
                row.isFyrcj == 1 &&
                row.isTempRemove !== 1 &&
                deMapFun.isDe(props.currentInfo?.kind) &&
                (props.currentInfo?.kind !== '07' ||
                  props.currentInfo.isCostDe === 4) &&
                isPartEdit &&
                props.currentInfo?.quantity != 0 &&
                row.pTotalNumber != 0 &&
                !deMapFun.isJxxs(row.materialCode) &&
                !deMapFun.isQtclf(row.materialCode)
              "
              @keyup="
                row.totalNumber = (row.totalNumber.match(/\d{0,8}(\.\d*)?/) || [
                  '',
                ])[0]
              "
            />
            <span v-else>{{
              decimalFormat(row.totalNumber, 'DETAIL_RCJ_TOTALNUMBER_PATH')
            }}</span>
          </template>
        </vxe-column>
        <vxe-column
          v-if="projectStore.taxMade == 1"
          title="不含税基期价"
          field="baseJournalPrice"
          :min-width="columnWidth(90)"
        >
          <template #default="{ row }">
            <span>{{
              isChangeAva(row)
                ? '-'
                : decimalFormatValue(
                    row.baseJournalPrice,
                    row,
                    'baseJournalPrice'
                  )
            }}</span>
          </template>
        </vxe-column>
        <vxe-column
          v-if="projectStore.taxMade == 0"
          title="含税基期价"
          field="baseJournalTaxPrice"
          :min-width="columnWidth(90)"
        >
          <template #default="{ row }">
            <span>{{
              isChangeAva(row)
                ? '-'
                : decimalFormatValue(
                    row.baseJournalTaxPrice,
                    row,
                    'baseJournalTaxPrice'
                  )
            }}</span>
          </template>
        </vxe-column>

        <vxe-column
          :title="'不含税市场价'"
          field="marketPrice"
          :min-width="columnWidth(100)"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row, column }">
            <span>{{
              isChangeAva(row)
                ? '-'
                : decimalFormatValue(row.marketPrice, row, 'marketPrice')
            }}</span>
          </template>

          <template #edit="{ row }">
            <vxe-input
              v-model="row.marketPrice"
              v-if="
                row.ifProvisionalEstimate != 1 &&
                row.isFyrcj == 1 &&
                codeCheckEdit(row) &&
                row.isTempRemove !== 1 &&
                deMapFun.isDe(props.currentInfo?.kind) &&
                (props.currentInfo?.kind !== '07' ||
                  props.currentInfo.isCostDe === 4) &&
                row.ifLockStandardPrice !== 1 &&
                (row.levelMark == 0 ||
                  !row.pbs ||
                  (row.levelMark !== 0 && row.pbs.length === 0)) &&
                isPartEdit &&
                !deMapFun.isJxxs(row.materialCode) &&
                !deMapFun.isQtclf(row.materialCode) &&
                !deMapFun.isTz(row.materialCode) &&
                !isChangeAva(row)
              "
              @blur="row.marketPrice = pureNumber0(row.marketPrice)"
            />
            <span v-else>{{
              isChangeAva(row)
                ? '-'
                : decimalFormatValue(row.marketPrice, row, 'marketPrice')
            }}</span>
          </template>
        </vxe-column>
        <vxe-column
          :title="'含税市场价'"
          field="marketTaxPrice"
          :min-width="columnWidth(100)"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row, column }">
            <span>{{
              isChangeAva(row)
                ? '-'
                : decimalFormatValue(row.marketTaxPrice, row, 'marketTaxPrice')
            }}</span>
          </template>

          <template #edit="{ row }">
            <vxe-input
              v-model="row.marketTaxPrice"
              v-if="
                row.ifProvisionalEstimate != 1 &&
                row.isFyrcj == 1 &&
                codeCheckEdit(row) &&
                row.isTempRemove !== 1 &&
                deMapFun.isDe(props.currentInfo?.kind) &&
                (props.currentInfo?.kind !== '07' ||
                  props.currentInfo.isCostDe === 4) &&
                row.ifLockStandardPrice !== 1 &&
                (row.levelMark == 0 ||
                  !row.pbs ||
                  (row.levelMark !== 0 && row.pbs.length === 0)) &&
                isPartEdit &&
                !deMapFun.isJxxs(row.materialCode) &&
                !deMapFun.isQtclf(row.materialCode) &&
                !deMapFun.isTz(row.materialCode) &&
                !isChangeAva(row)
              "
              @blur="row.marketTaxPrice = pureNumber0(row.marketTaxPrice)"
            />
            <span v-else>{{
              isChangeAva(row)
                ? '-'
                : decimalFormatValue(row.marketTaxPrice, row, 'marketTaxPrice')
            }}</span>
          </template>
        </vxe-column>

        <vxe-column
          title="税率（%）"
          field="taxRate"
          :min-width="columnWidth(90)"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row, column }">
            <span>{{
              isChangeAva(row)
                ? '-'
                : decimalFormatValue(row.taxRate, row, 'taxRate')
            }}</span>
          </template>
          <template #edit="{ row }">
            <vxe-input
              v-model="row.taxRate"
              v-if="
                (['08'].includes(props.currentInfo?.kind) &&
                  ['BCJXF', 'BCCLF', 'BCSBF', 'BCZCF'].includes(
                    row.materialCode?.split('#')[0]
                  ) &&
                  !row.ifProvisionalEstimate &&
                  isTempRemove !== 1 &&
                  row.ifLockStandardPrice !== 1) ||
                ((row.taxRate || row.taxRate == 0) &&
                  !row.ifProvisionalEstimate &&
                  row.isFyrcj == 1 &&
                  row.isTempRemove !== 1 &&
                  isNotCostDe &&
                  row.ifLockStandardPrice !== 1 &&
                  (row.levelMark == 0 ||
                    !row.rcjDetailsDTOs ||
                    (row.levelMark != 0 && row.rcjDetailsDTOs.length == 0)) &&
                  isPartEdit &&
                  !isChangeAva(row) &&
                  row.supplementDeRcjFlag !== 1 &&
                  !deMapFun.isTz(row.materialCode) &&
                  row.kind !== 1)
              "
              @blur="
                () => {
                  if (row.taxRate >= 0) {
                    row.taxRate = pureNumber0(row.taxRate);
                  } else {
                    restore('taxRate');
                  }
                }
              "
            />
            <span v-else>{{ isChangeAva(row) ? '-' : row.taxRate }}</span>
          </template>
        </vxe-column>

        <vxe-column title="合价" field="totalNumberPrice" :min-width="columnWidth(70)">
          <template #default="{ row }">
            <span>{{
              isChangeAva(row)
                ? '-'
                : decimalFormatValue(
                    row.totalNumberPrice,
                    row,
                    'totalNumberPrice'
                  )
            }}</span>
          </template>
        </vxe-column>

        <vxe-column title="是否暂估" field="ifProvisionalEstimate" :min-width="columnWidth(90)">
          <template #default="{ row }">
            <vxe-checkbox
              v-if="
                isNotCostDe &&
                row.isTempRemove !== 1 &&
                (!row.rcjDetailsDTOs || +row.levelMark === 0) &&
                !isChangeAva(row) &&
                !['人工费', '机械费'].includes(row.type)
              "
              v-model="row.ifProvisionalEstimate"
              :checked-value="1"
              :unchecked-value="0"
              :disabled="true"
              @change="updateStatus(row)"
            >
            </vxe-checkbox>
            <vxe-checkbox
              v-else-if="
                !['人工费', '机械费'].includes(row.type) && !isChangeAva(row)
              "
              :disabled="true"
              v-model="row.ifProvisionalEstimate"
              :checked-value="1"
              :unchecked-value="0"
              @change="updateStatus(row)"
            >
            </vxe-checkbox>
          </template>
        </vxe-column>

        <vxe-column
          title="锁定数量"
          v-if="cxHideColumn"
          field="isNumLock"
          :min-width="columnWidth(70)"
        >
          <template #default="{ row }">
            <vxe-checkbox
              @change="editClosedEvent({ row, column: { field: 'isNumLock' } })"
              v-if="
                row.isTempRemove !== 1 &&
                row.isFyrcj == 1 &&
                deMapFun.isDe(props.currentInfo?.kind) &&
                (props.currentInfo?.kind !== '07' ||
                  props.currentInfo.isCostDe === 4) &&
                row.pbs &&
                !deMapFun.isJxxs(row.materialCode) &&
                !deMapFun.isQtclf(row.materialCode)
              "
              v-model="row.isNumLock"
            ></vxe-checkbox>
          </template>
        </vxe-column>
        <vxe-column
          title="原始含量"
          field="originalQty"
          :min-width="columnWidth(70)"
          v-if="cxHideColumn"
        >
          <template #default="{ row }">
            {{ decimalFormat(row.originalQty, 'DETAIL_RCJ_RESQTY_PATH') }}
          </template>
        </vxe-column>
        <vxe-column
          title="价格来源"
          field="sourcePrice"
          :min-width="columnWidth(100)"
          v-if="cxHideColumn"
        ></vxe-column>
      </vxe-table>
      <!-- {{props.currentInfo}} -->
      <!-- <de-description-info
        :currentInfo="props.currentInfo"
        v-if="props.currentInfo?.kind === '04'"
      ></de-description-info>
      <description-info
        :currentInfo="props.currentInfo"
        v-if="props.currentInfo?.kind === '03'"
        :type="props.type"
      ></description-info> -->
    </div>
  </div>
  <bcRcj
    v-model:visible="rcjVisible"
    :code="bdCode"
    :currentInfo="cloneCurrentInfo"
    :ysscurrentInfo="props.currentInfo"
    :unitList="projectStore.unitListString"
    @bcCancel="rcjCancel"
    @rcjSaveData="rcjSaveData"
  ></bcRcj>

  <inquiryPopup
    v-if="priceVisible"
    :info="currentInfo"
    :subCurrentInfo="props.currentInfo"
    @closeDialog="closeInquiryPopup"
  ></inquiryPopup>
  <common-modal
    className="dialog-comm"
    :title="'同步名称至子目'"
    :width="500"
    :height="200"
    v-model:modelValue="synchronizationVisible"
    :mask="true"
    :lockView="true"
  >
    <synchronization
      v-if="synchronizationVisible"
      :currentInfo="currentInfo"
      :subCurrentInfo="subCurrentInfo"
      @close="close"
      @updateData="updateQuantityData(tipRow, 'accumulateFlag')"
      @add="addData"
    ></synchronization>
  </common-modal>
  <common-modal
    title="单位换算系数"
    width="554px"
    v-model:modelValue="unitVisible"
    className="dialog-comm"
  >
    <div class="dialog-content">
      <div class="init-text">替换的资源和当前资源单位不同，是否继续替换？</div>
      <div class="init-text">
        单位换算系数：<span>1{{ currentMaterialInfoTihuan?.unit }}&nbsp;=</span
        ><a-input
          v-model:value="conversionCoefficient"
          @keyup="
            conversionCoefficient = (conversionCoefficient.match(
              /\d{0,8}(\.\d{0,3}|100)?/
            ) || [''])[0]
          "
        /><span>{{ currentInfo?.unit }}</span>
      </div>
    </div>
    <div class="footer-btn-list">
      <a-button @click="emits('updateData', 1), (unitVisible = false)"
        >取消</a-button
      >
      <a-button
        type="primary"
        @click="
          updateBjqRcjReplaceData(
            currentMaterialInfoTihuan.materialCode,
            conversionCoefficient
          ),
            (unitVisible = false)
        "
        >确定</a-button
      >
    </div>
  </common-modal>
</template>

<script setup>
import {
  defineAsyncComponent,
  onActivated,
  onMounted,
  nextTick,
  ref,
  watch,
  reactive,
  computed,
  watchEffect,
  getCurrentInstance,
} from 'vue';
import api from '@/gongLiaoJiProject/api/projectDetail';
import DescriptionInfo from '../descriptionInfo/index.vue';
import DeDescriptionInfo from '../deDescriptionInfo/index.vue';
import { projectDetailStore } from '@/store/projectDetail';
import infoMode from '@/plugins/infoMode';
import { message } from 'ant-design-vue';
import { useCellClick } from '@gongLiaoJi/hooks/useCellClick';
import gSdetailApi from '@/gongLiaoJiProject/api/projectDetail.js';
import deMapFun from '../deMap';
import { map } from 'xe-utils';
import { pureNumber0 } from '@/utils/index';
import synchronization from './synchronization.vue';
import { useDecimalPoint } from '@gongLiaoJi/hooks/useDecimalPoint.js';
const { decimalFormat } = useDecimalPoint();
import { columnWidth } from '@gongLiaoJi/hooks/useSystemConfig';

//  小数点处理 taxMade == 0 含税 baseJournalTaxPrice   marketTaxPrice
//                       不含税基期价 baseJournalPrice  marketPrice
const decimalFormatValue = (value, row, filed) => {
  let path = '';
  let pointMap = {
    PTRCJZS: {
      baseJournalPrice: 'DETAIL_PTRCJZS_BASEJOURNALPRICE_PATH',
      baseJournalTaxPrice: 'DETAIL_PTRCJZS_BASEJOURNALTAXPRICE_PATH',
      marketPrice: 'DETAIL_PTRCJZS_MARKETPRICE_PATH',
      marketTaxPrice: 'DETAIL_PTRCJZS_MARKETTAXPRICE_PATH',
      totalNumberPrice: 'DETAIL_PTRCJZS_TOTAL_PATH',
      taxRate: 'DETAIL_RCJ_TAXRATE_PATH',
    },
    BCRCJZS: {
      baseJournalPrice: 'DETAIL_BCRCJZS_BASEJOURNALPRICE_PATH',
      baseJournalTaxPrice: 'DETAIL_BCRCJZS_BASEJOURNALTAXPRICE_PATH',
      marketPrice: 'DETAIL_BCRCJZS_MARKETPRICE_PATH',
      marketTaxPrice: 'DETAIL_BCRCJZS_MARKETTAXPRICE_PATH',
      totalNumberPrice: 'DETAIL_PTRCJZS_TOTAL_PATH',
      taxRate: 'DETAIL_BCRCJ_TAXRATE_PATH',
    },
    FYTZRCJ: {
      baseJournalPrice: 'DETAIL_FYTZRCJ_BASEJOURNALPRICE_PATH',
      baseJournalTaxPrice: 'DETAIL_FYTZRCJ_BASEJOURNALTAXPRICE_PATH',
      marketPrice: 'DETAIL_FYTZRCJ_MARKETPRICE_PATH',
      marketTaxPrice: 'DETAIL_FYTZRCJ_MARKETTAXPRICE_PATH',
      totalNumberPrice: 'DETAIL_PTRCJZS_TOTAL_PATH',
      taxRate: 'DETAIL_RCJ_TAXRATE_PATH',
    },
  };

  let rowType = 'PTRCJZS';
  if (row.isFyrcj === 0) {
    rowType = 'FYTZRCJ';
  }
  if (
    row &&
    row?.materialCode &&
    ['BCJXF', 'BCCLF', 'BCSBF', 'BCZCF'].includes(
      row.materialCode?.split('#')[0]
    )
  ) {
    rowType = 'BCRCJZS';
  }

  path = pointMap[rowType][filed];

  if (!path) {
    message.error('没有找到对应的路径');
    return;
  }
  return decimalFormat(value, path);
};
const autoHeight = computed(() => {
  if( props.currentInfo && deMapFun.isDe(props.currentInfo?.kind) &&  !['00', '01', '02', '05', '06', '09', '07', '-1'].includes( props.currentInfo?.kind  )
      && isEditDe.value && parentRcjIsShow.value){
    return 'glj-h40-detailed-area';
  }else{
    return 'glj-h100-detailed-area';
  }
})

const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();

const customCellBeforeEditMethod = () => {
  if (!isEditDe.value) {
    return;
  }
  return cellBeforeEditMethod();
};

let vexTableData = ref([]); //表格数据
const props = defineProps([
  'tableData',
  'currentInfo',
  'type',
  'currentMaterialInfo',
  'RefreshList',
]);
const synchronizationVisible = ref(false);
const inquiryPopup = defineAsyncComponent(() =>
  import('./inquiryPopup/index.vue')
);

// 消耗量，耗量”“锁定数量”“原始含量”和“价格来源，措施项目非定额列不能查看
const cxHideColumn = computed(() => {
  return (
    props.type == '1' ||
    (props.type == '2' && deMapFun.isDe(props.currentInfo?.kind))
  );
});

const priceVisible = ref(false); //智能询价弹窗
const subCurrentInfo = ref({});
const bmCodeHcList = ref([]);
let unitVisible = ref(false);
let conversionCoefficient = ref('');
let currentMaterialInfoTihuan = ref({});
const emits = defineEmits([
  'cellDBLClickEvent',
  'updateData',
  'update:currentMaterialInfo',
]);
const contextmenuList = reactive({
  className: 'materialMachineTable-menus',
  body: {
    options: [
      [
        {
          code: 1,
          name: '增加材料',
          visible: true,
          disabled: false,
        },
        {
          code: 2,
          name: '删除明细',
          visible: true,
          disabled: false,
        },
        {
          code: 4,
          name: '同步主材/设备名称至子目',
          visible: true,
          disabled: false,
        },
        {
          code: 'tempDelete',
          name: '临时删除',
          visible: true,
          disabled: false,
        },
        {
          code: 'cancelTempDelete',
          name: '取消临时删除',
          visible: true,
          disabled: false,
        },
        {
          code: 3,
          name: '智能询价',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod: ({ options, row }) => {
    let tar = contextmenuList.body.options[0].find(a => a.name === '智能询价');
    let tempDelete = contextmenuList.body.options[0].find(
      a => a.name === '临时删除'
    );
    let cancelTempDelete = contextmenuList.body.options[0].find(
      a => a.name === '取消临时删除'
    );
    if (
      ((row?.levelMark == 1 || row?.levelMark == 2) && row?.markSum == 1) ||
      row.unit === '元' ||
      row.isFyrcj === 0 ||
      row.ifLockStandardPrice == 1 ||
      row.supplementRcjFlag == 1
    ) {
      console.log('智能询价行', row);
      tar.disabled = true;
      //单位为元、单位为%、补充人材机、已锁定市场价、已勾选是否汇总/二次解析的父级材料 都不可以智能询价  这些材料都不可以智能询价
    } else {
      tar.disabled = false;
    }
    // 如果定额为临时删除
    if (props.currentInfo.isTempRemove === 1 || !row.pbs) {
      tempDelete.visible = false;
      cancelTempDelete.visible = false;
    } else if (row.isTempRemove == 1) {
      tempDelete.visible = false;
      cancelTempDelete.visible = true;
    } else {
      tempDelete.visible = true;
      cancelTempDelete.visible = false;
    }
    // if (row?.libraryCode?.startsWith('2022')) {
    //   contextmenuList.body.options[0][2].visible = false;
    // }

    if (['主材费', '设备费'].includes(row.type)) {
      contextmenuList.body.options[0][2].visible = true;
    } else {
      contextmenuList.body.options[0][2].visible = false;
    }

    // 普通专业的安装定额、措施定额、泵送定额的人材机均不可以编辑
    for (let i of contextmenuList.body.options[0]) {
      if (i.visible) {
        i.visible = isEditDe.value;
      }
    }

    return (
      deMapFun.isDe(props.currentInfo?.kind) &&
      (props.currentInfo?.kind !== '07' || props.currentInfo.isCostDe === 4)
    );
  },
});

// 基期价、市场价为“-
const isChangeAva = row => {
  return Number(row.isDataTaxRate) == 0;
};

const typeList = ref([
  {
    name: '材料费',
    value: 2,
  },
  {
    name: '主材费',
    value: 5,
  },
  {
    name: '设备',
    value: 4,
  },
]);

const vexTable = ref();
const currentInfo = ref();
let cloneCurrentInfo = ref(); // 当时的currentInfo，防止取消编辑时点击了其他行，数据修改了
let currentIndex = ref(0);
const projectStore = projectDetailStore();
let rcjVisible = ref(false);
let bdCode = ref('');
let unitList = ref([]);

const bcRcj = defineAsyncComponent(() =>
  import('../subItemProject/components/bcRcj.vue')
);

onMounted(() => {
  currentInfo.value = null;
  queryUnit();
});

onActivated(() => {
  currentIndex.value = 0;
  currentInfo.value = null;
});
/**
 * 关闭了智能询价
 * @param {*} v  true 点击了确定按钮  false 点击了取消按钮
 */
const closeInquiryPopup = v => {
  priceVisible.value = false;
  if (v) {
    props.RefreshList();
  }
};

watch(
  () => props.currentInfo?.kind,
  () => {
    nextTick(() => {
      if (vexTable.value) {
        const $table = vexTable.value;
        $table.showColumn('resQty');
        $table.showColumn('originalQty');
        $table.showColumn('sourcePrice');
      }
    });
  },
  { deep: true }
);

watch(
  () => props.currentMaterialInfo,
  () => {
    vexTable.value.setCurrentRow(props.currentMaterialInfo);
    vexTable.value.scrollToRow(props.currentMaterialInfo);
  }
);
watch(
  () => props.tableData,
  val => {
    vexTableData.value = props.tableData.map(a => {
      return {
        ...a,
        pbs: a.markSum
          ? a.pbs.map(b => {
              return { ...b, pTotalNumber: a.totalNumber };
            })
          : [],
      };
    });
    // vexTable.value.reloadData(vexTableData.value)
    // vexTableData.value = val
    console.log('人材机明细数据', vexTableData.value);
  }
);
const parentRcjIsShow = computed(() => {
  return props.currentInfo.markSum != 0;
});
const rcjCancel = () => {
  const $table = vexTable.value;
  $table.revertData(currentInfo.value, 'materialCode');
  rcjVisible.value = false;
};

/**
 * 还原值
 */
const restore = filed => {
  const $table = vexTable.value;
  $table.revertData(currentInfo.value, filed);
};

const queryUnit = () => {
  api.queryUnit().then(res => {
    if (res.status === 200 && res.result) {
      unitList.value = res.result;
    }
  });
};
// ['QTCLFBFB','34000001-2','J00004','J00031','J00031','C11384','C00007','C000200'] 不能编辑
const isPartEdit = computed(() => {
  return ![
    'QTCLFBFB',
    '34000001-2',
    'J00004',
    'J00031',
    'J00031',
    'C11384',
    'C00007',
    'C000200',
  ].includes(currentInfo.value?.materialCode);
});

// 调整人材机在工料法中，分着一般计税和简易计税，编码是RGFTZ、CLFTZ、JXFTZ,SBFTZ,ZCFTZ的人材机的不含税基期价、含税基期价、不含税市场价、含税市场价字段不可以修改
const codeCheckEdit = row => {
  return !['RGFTZ', 'CLFTZ', 'JXFTZ', 'SBFTZ', 'ZCFTZ'].includes(
    row.materialCode
  );
};

const cellDBLClickEvent = ({ row, column }) => {
  if (
    !(
      deMapFun.isDe(props.currentInfo?.kind) &&
      (props.currentInfo?.kind !== '07' || props.currentInfo.isCostDe === 4)
    ) ||
    deMapFun.isJxxs(row.materialCode) ||
    deMapFun.isTz(row.materialCode)
  ) {
    return false;
  }
  const $table = vexTable.value;
  let parentData = $table.getParentRow(row);
  row.parentId = parentData?.sequenceNbr;
  if (
    column.field === 'materialCode' &&
    deMapFun.isDe(props.currentInfo?.kind) &&
    isEditDe.value
  ) {
    emits('cellDBLClickEvent', row);
  }
};

// 可以编辑的定额吓得人材机普通专业的装饰超高、垂直运输定额下人材机可自由编辑
const isEditDe = computed(() => {
  return deMapFun.isEditRcj(props.currentInfo);
});

const columnSel = ref(0);
const rowSel = ref(0);
const tableCellClick = event => {
  if (
    event.$columnIndex !== 1 ||
    (event.$columnIndex === columnSel.value && event.rowid === rowSel.value)
  ) {
    return true;
  }

  if (!isEditDe.value) {
    return;
  }

  bmCodeHcList.value = [];
  columnSel.value = event.$columnIndex;
  rowSel.value = event.rowid;
  // 人材机明细编码下拉列表
  RefreshMemoryCode(event.row);
  return true;
};

// 刷新下拉列表
const RefreshMemoryCode = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    idList: [row?.sequenceNbr],
    materialCode: row?.originalMaterialCode,
  };
  // 人材机明细编码下拉列表
  gSdetailApi.getMemoryCode(apiData).then(res => {
    if (res.code == 200) {
      let datas = res.result;
      let arr = [];
      datas.forEach(item => {
        if (!item.specification) {
          item.specification = '';
        }
        arr.push(
          `${item.materialCode}：${item.materialName}：${item.specification}：${item.unit}`
        );
      });
      bmCodeHcList.value = arr.join(',');
    }
  });
};

/**
 *
 * @param param0 taxMade == 0 含税 baseJournalTaxPrice   marketTaxPrice
 *                          不含税基期价 baseJournalPrice  marketPrice
 */
const cellClassName = ({ row, column, $columnIndex }) => {
  const selectName = selectedClassName({ $columnIndex, row, column });
  if (column.field === 'marketPrice' && projectStore.taxMade == 1) {
    if (row.marketPrice > row.baseJournalPrice) {
      return 'price-add-color ' + selectName;
    }
    if (row.marketPrice < row.baseJournalPrice) {
      return 'price-reduce-color ' + selectName;
    }
  }

  if (column.field === 'marketTaxPrice' && projectStore.taxMade == 0) {
    // 1-一般计税  2-简易计税
    if (row.marketTaxPrice > row.baseJournalTaxPrice) {
      return 'price-add-color ' + selectName;
    }
    if (row.marketTaxPrice < row.baseJournalTaxPrice) {
      return 'price-reduce-color ' + selectName;
    }
  }
  // 税率和库里税率不同，标红
  if (column.field === 'taxRate') {
    if (row.taxRate !== row.taxRateInit && row.taxRateInit !== undefined) {
      return 'diff-tax-rate ' + selectName;
    }
  }

  if (
    deMapFun.isDe(props.currentInfo?.kind) &&
    column.field === 'resQty' &&
    decimalFormat(row.resQty, 'DETAIL_RCJ_RESQTY_PATH') !==
      decimalFormat(row.originalQty, 'DETAIL_RCJ_RESQTY_PATH')
  ) {
    return 'resQty-color ' + selectName;
  } else if ([4, 5].includes(row.kind)) {
    return 'mainMaterial-color ' + selectName;
  }
  return selectName;
};

const rowClassName = ({ row }) => {
  if (row.isTempRemove === 1) {
    return 'temp-delete';
  }
  if (
    !deMapFun.isDe(props.currentInfo?.kind) ||
    props.currentInfo?.kind === '07'
  ) {
    return 'disable-bg';
  }
  return null;
};

const onContextMenuClick = ({ menu, row }) => {
  if (!row) {
    message.warning('请先选择要材料');
    return;
  }
  currentInfo.value = row;
  const value = menu.code;
  switch (value) {
    case 1:
      const $table = vexTable.value;
      let parentData = $table.getParentRow(row);
      row.parentId = parentData?.sequenceNbr;
      emits('cellDBLClickEvent', row);
      break;
    case 2:
      deleteType();
      break;
    case 'tempDelete':
      updateDelTempStatusColl(row);
      break;
    case 'cancelTempDelete':
      updateCancelDelTempStatusColl(row);
      break;
    case 3:
      priceVisible.value = true;
      break;
    case 4:
      console.log('props.subCurrentInfo;', props.currentInfo);
      subCurrentInfo.value = props.currentInfo;
      synchronizationVisible.value = true;
      break;
    default:
      break;
  }
};
const handleKeyDownEvent = event => {
  let sTable = event.$table;
  let code = event.$event.code;
  let row = sTable.getCurrentRecord();
  let isEditing = sTable.isEditByRow(row)
  // 删除
  if(code == 'Delete'&&!isEditing){
    deleteType()
  }
};
// 临时删除
const updateDelTempStatusColl = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    idList: [row.sequenceNbr],
    deRowId: props.currentInfo.deRowId,
  };
  console.log('临时删除参数', apiData);
  gSdetailApi.tempRemoveDeRow(apiData).then(res => {
    console.log('res临时删除', res);
    if (res.status === 200) {
      message.success(res.message);
      emits('updateData', 1);
    }
  });
};
// 取消临时删除
const updateCancelDelTempStatusColl = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    idList: [row.sequenceNbr],
    deRowId: props.currentInfo.deRowId,
  };
  console.log('取消临时删除参数', apiData);
  gSdetailApi.cancelTempRemoveDeRow(apiData).then(res => {
    console.log('res取消临时删除', res);
    if (res.status === 200) {
      message.success(res.message);
      emits('updateData', 1);
    }
  });
};
const addRcjData = () => {
  const $table = vexTable.value;
  let parentData = $table.getParentRow(currentInfo.value);
  if (currentInfo.value) {
    currentInfo.value.parentId = parentData?.sequenceNbr;
  }
  emits('cellDBLClickEvent', currentInfo.value);
};

// 补充人材机
const bcRcjData = () => {
  bdCode.value = '';
  if (!currentInfo.value && vexTableData.value.length > 0) {
    currentInfo.value = vexTableData.value[0];
  }
  rcjVisible.value = true;
};

// 删除人材机
const deleteType = () => {
  if (!currentInfo.value) {
    message.warning('请先选择要删除的材料');
    return;
  }
  infoMode.show({
    isDelete: true,
    iconType: 'icon-querenshanchu',
    infoText: '是否确认删除?',
    descText: '是否删除当前已选中数据',
    confirm: () => {
      deleteRcj();
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};
const close = type => {
  synchronizationVisible.value = false;
  if (type) emits('updateData', 1);
};
const deleteRcj = () => {
  // if (!currentInfo.value.pbs) {
  //   delDetail();
  // } else {
  //   delRcjData();
  // }
  delRcjData();
};

// 删除人材机数据
const delRcjData = row => {
  let apiData = {
    rcjDetailId: row ? row.sequenceNbr : currentInfo.value.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    deId: row ? row.parentId : props.currentInfo?.sequenceNbr,
  };
  console.log('delRcjData', apiData, row);
  api.delRcjData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('删除成功');
      currentInfo.value = null;
      emits('updateData', 1);
    }
  });
};

// 删除单个配比材料数据
const delDetail = () => {
  let apiData = {
    sequenceNbr: currentInfo.value.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  api.delDetail(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('删除成功');
      emits('updateData', 1);
    }
  });
};

const updateStatus = row => {
  updateConstructRcj(row, 'ifProvisionalEstimate');
};

const currentChangeEvent = ({ row, rowIndex }) => {
  console.log('🚀 ~ currentChangeEvent ~ row:', row);
  const $table = vexTable.value;
  let parentData = $table.getParentRow(row);
  currentIndex.value = rowIndex;
  setTimeout(() => {
    row.parentId = parentData?.sequenceNbr;
    row.isPbs = Object.hasOwnProperty.call(row, 'pbs');
    currentInfo.value = row;
    emits('update:currentMaterialInfo', row);
    bus.emit('currentChangeRcjEvent', row);
  }, 500);
};

const editActivated = ({ row, column }) => {
  // row[column.field] = row[column.field].toFixed(1);
  console.log('🚀 ~ editActivated ~ row:', row[column.field], column);
};

// 单元格退出编辑事件
const editClosedEvent = ({ row, column }) => {
  const $table = vexTable.value;
  let field = column.field;
  if (['specification', 'unit', 'materialName'].includes(field)) {
    // 处理编码下拉未及时更新数据
    columnSel.value = 0;
  }

  if ($table.isUpdateByRow(row, field)) {
    if (!row.materialCode) {
      currentInfo.value.materialCode = currentInfo.value.originalMaterialCode;
      return;
    }
    if (field === 'materialCode') {
      const regex = /[[@:;]/;
      if (regex.test(row.materialCode)) {
        message.error('编码中含有非法字符，不能带有@或:等符号，请重新输入!');
        $table.revertData(row, 'materialCode');
        return;
      }
    }
    updateConstructRcj(row, field);
    isRcjCodeMainQuotaLibrary(field, row.materialCode, row);
  }
};
const updateMarketPriceAndTotalNumber = field => {
  let apiRoute = {
    marketPrice: 'updateMarketPrice',
    totalNumber: 'updateTotalNumber',
  };
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    deRowId: props.currentInfo?.sequenceNbr,
    resourceId: JSON.parse(JSON.stringify(currentInfo.value)).sequenceNbr,
    [field]: JSON.parse(JSON.stringify(currentInfo.value))[field],
  };
  api[apiRoute[field]](apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('修改成功');
      queryBranchDataById();
    }
  });
};
const updateConstructRcj = (row, field) => {
  if (field === 'materialCode') return;
  let value;
  if (field === 'type') {
    value = row[field] === '材料费' ? 2 : row[field] === '主材费' ? 5 : 4;
  }
  if (['marketPrice', 'totalNumber', 'resQty'].includes(field)) {
    row[field] = +row[field];
  }

  if (['taxRate'].includes(field) && !row[field]) {
    row[field] = 0;
  }

  console.log('updateConstructRcj', field, row[field]);
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    deId: props.currentInfo?.sequenceNbr,
    rcjDetailId: row.sequenceNbr,
    constructRcj: {
      [field === 'type' ? 'kind' : field]:
        field === 'type' ? value : row[field],
    },
  };
  console.log('修改人材机明细数据参数', apiData, api.updateConstructRcj);
  api.updateConstructRcj(apiData).then(res => {
    if (res.status === 200) {
      message.success('修改成功');
      emits('updateData', 1);
    }
  });
};

// 判断输入的材料编码是否与主定额库编码相同
const isRcjCodeMainQuotaLibrary = (field, code, row) => {
  console.log('判断输入的材料编码是否与主定额库编码相同', field, code);
  if (field !== 'materialCode') return;
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    code: code,
  };
  api.isRcjCodeMainQuotaLibrary(apiData).then(res => {
    console.log('判断输入的材料编码是否与主定额库编码相同', res);
    if (res.status === 200) {
      if (res.result) {
        // 输入的编码为主定额库编码
        if ((!row.pbs && res.result.levelMark == 0) || row.pbs) {
          // if (currentInfo.value.unit !== res.result.unit) {
          //   currentMaterialInfoTihuan.value = res.result;
          //   unitVisible.value = true;
          //   return;
          // } else {
          updateBjqRcjReplaceData(code, '', row);
          // }
        } else {
          infoMode.show({
            isSureModal: true,
            iconType: 'icon-querenshanchu',
            infoText: '配合比材料下不允许增加配合比材料',
            confirm: () => {
              infoMode.hide();
              row.materialCode = row.originalMaterialCode;
            },
          });
        }
      } else {
        isStandardRcj(code, row);
      }
    }
  });
};

// 判断输入的材料编码是否标准人材机数据
const isStandardRcj = (code, row) => {
  let currentRow = currentInfo.value;
  if (row) {
    currentRow = row;
  }
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    code: code,
    deId: props.currentInfo.sequenceNbr,
    deRowId: props.currentInfo.deRowId,
    rcjDetailId: currentRow.sequenceNbr,
    rcjId: currentRow.parentId,
  };
  console.log('🚀 ~ isStandardRcj ~ apiData:', apiData);
  api.replaceRcjByCodeData(apiData).then(res => {
    console.log('================人材机是否是标准数据', res);
    if (res.status === 200) {
      if (res.result) {
        rcjVisible.value = false;
        emits('updateData', 1);
      } else if (res.result === 'unqualified') {
        infoMode.show({
          isSureModal: true,
          iconType: 'icon-querenshanchu',
          infoText: '配合比材料下不允许增加配合比材料',
          confirm: () => {
            infoMode.hide();
            row.materialCode = row.originalMaterialCode;
          },
        });
      } else {
        infoMode.show({
          iconType: 'icon-querenshanchu',
          infoText: '主定额库不存在该材料编码，是否补充人材机？',
          confirm: () => {
            rcjVisible.value = true;
            bdCode.value = code;
            infoMode.hide();
            cloneCurrentInfo.value = row;
          },
          close: () => {
            console.log('row---------');
            infoMode.hide();
            row.materialCode = row.originalMaterialCode;
          },
        });
      }
    }
  });
};

let spByCode = ref(0); //接口需要增加 type 字段    替换数据： 0   （number类型）      从内存替换：1  （number类型）
// 预算书 措施项目 替换编辑区的人材机数据
const updateBjqRcjReplaceData = (code, conversionCoefficient, row = null) => {
  let currentRow = currentInfo.value;
  if (row) {
    currentRow = row;
  }

  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentRow)),
    deId: props.currentInfo.sequenceNbr,
    code: code,
    region: 1,
    rcjDetailId: currentRow.sequenceNbr,
    rcjId: currentRow.parentId,
    factor: Number(conversionCoefficient) || '',
    type: spByCode.value,
  };

  console.log('明细区替换人材机', apiData);
  api.updateBjqRcjReplaceData(apiData).then(res => {
    console.log(res);
    currentMaterialInfoTihuan.value = {};
    if (res.status === 200 && res.result) {
      message.success('人材机替换成功');
      rcjVisible.value = false;
      emits('updateData', 1);
    } else {
      currentInfo.value.materialCode = currentInfo.value.originalMaterialCode;
      message.error(
        '插入子目必须与其他子目类型相同且不能具有层级结构，请重新插入！'
      );
    }
  });
};

// 预算书 措施项目 补充界面替换人材机数据
const spRcjByPage = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    detail: JSON.parse(JSON.stringify(inputData)),
    deId: props.currentInfo.sequenceNbr,
    deRowId: props.currentInfo.deRowId,
    rcjDetailId: cloneCurrentInfo.value.sequenceNbr,
    rcjId: cloneCurrentInfo.value.parentId,
    replaceFlag: 1,
  };
  console.log('新增参数', apiData, cloneCurrentInfo.value);
  api.addBjqBcRcjData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('人材机替换成功');
      rcjVisible.value = false;
      const $table = vexTable.value;
      $table.clearEdit();
      columnSel.value = 0;
      emits('updateData', 1);
    }
  });
};

// 预算书 措施项目 添加编辑区的人材机数据
const addMxqBcRcjData = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    detail: JSON.parse(JSON.stringify(inputData)),
    deId: props.currentInfo.sequenceNbr,
    deRowId: props.currentInfo.deRowId,
  };
  if (props.currentInfo.kind === '06') {
    apiData.rcjId = props.tableData[0].parentId;
  } else if (currentInfo.value) {
    if (!currentInfo.value.isPbs) {
      apiData.rcjId = currentInfo.value.parentId;
    }
  }

  console.log('新增参数', apiData);
  api.addBjqBcRcjData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('人材机新建成功');
      rcjVisible.value = false;
      emits('updateData', 1);
    }
  });
};

const rcjSaveData = inputData => {
  let postData = {
    ...inputData,
  };

  if (bdCode.value) {
    spRcjByPage(postData);
  } else {
    addMxqBcRcjData(postData);
  }
};

const currentMaterialInfo = sequenceNbr => {
  if (props.tableData?.length > 0) {
    const $table = vexTable.value;
    projectStore.isAutoPosition = false;
    nextTick(() => {
      setTimeout(() => {
        currentInfo.value = $table.getRowById(sequenceNbr);
        $table.setCurrentRow(currentInfo.value);
        $table.scrollToRow(currentInfo.value);
        console.log('$table.getCurrentRow()', $table.getRowById(sequenceNbr));
      }, 1200);
    });
  } else {
    setTimeout(() => {
      currentMaterialInfo(sequenceNbr);
    }, 1000);
  }
};
const setSynchronization = (row, subRow) => {
  currentInfo.value = row;
  subCurrentInfo.value = subRow;
  synchronizationVisible.value = true;
};

/**
 * 是否费用定额
 * 12的垂运，22的装饰超高、垂运不属于费用定额
 * isCostDe是否是费用定额 0不是  1 安文费 2 总价措施 3 超高 4 垂运 5 安装费'
 */
const isNotCostDe = computed(() => {
  const { kind, isCostDe } = props.currentInfo || {};
  return (
    deMapFun.isDe(props.currentInfo?.kind) &&
    (!isCostDe ||
      isCostDe === 4 ||
      (projectStore.deStandardReleaseYear === '22' &&
        props.currentInfo.isCostDe === 3))
  );
});

watchEffect(() => {
  console.log('🚀 ~ isNotCostDe ~ props.currentInfo:', props.currentInfo);

  if (props.tableData?.length) {
    vexTableData.value = props.tableData.map(a => {
      return {
        ...a,
        pbs: a.markSum
          ? a.pbs.map(b => {
              return { ...b, pTotalNumber: a.totalNumber };
            })
          : [],
      };
    });
    currentInfo.value = vexTableData.value[currentIndex.value];
    // vexTable.value.reloadData(vexTableData.value)
    // vexTable.value.reloadData(vexTableData.value)
  }
});

defineExpose({
  currentMaterialInfo,
  setSynchronization,
  delRcjData,
});
</script>

<style lang="scss" scoped>
.material-machine-content {
  width: 100%;
  //display: flex;
  //flex-direction: column;
  //justify-content: space-between;
  height: 100%;
  .content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    //height: calc(100% - 40px);
  }
  :deep(.vxe-table) {
    width: 100%;
  }
  ::v-deep(
      .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column
    ) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
  ::v-deep(.vxe-table--render-default.is--tree-line .vxe-header--column) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
  ::v-deep(.vxe-table .resQty-color) {
    color: #287cfa;
  }
  ::v-deep(.vxe-table .mainMaterial-color) {
    color: #7d1dff;
  }
  ::v-deep(.vxe-table .price-add-color) {
    color: #de3f3f;
  }
  ::v-deep(.vxe-table .price-reduce-color) {
    color: #67c23a;
  }
  ::v-deep(.vxe-table .diff-tax-rate) {
    color: #de3f3f;
  }
  ::v-deep(.vxe-table .disable-bg) {
    background: #f3f2f3;
    color: #a7a7a7;
  }
  ::v-deep(.vxe-table .temp-delete) {
    background: #f3f2f3;
    color: #a7a7a7;
    text-decoration: line-through;
  }
  ::v-deep(.vxe-pulldown--wrapper) {
    width: 600px;
  }
}
.delete-dialog {
  background: #67c23a;
  :deep(.ant-modal-content) {
    background: red;
    :deep(.ant-modal-header) {
      padding: 0;
    }
    :deep(.ant-modal-close-x) {
      color: #ffffff;
    }
    :deep(.ant-modal-footer) {
      text-align: center;
    }
  }
}
.dialog-content {
  padding: 46px;
  text-align: center;
}
.init-text {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333333;
  .icon {
    margin-right: 5px;
  }
  span {
    color: #1e90ff;
    margin: 0 2px;
  }
  .ant-input {
    width: 105px;
    margin-left: 5px;
  }
}
.init-text:nth-last-of-type(1) {
  margin-top: 25px;
}
.footer-btn-list {
  padding-bottom: 20px;
}
</style>
<style lang="scss">
body .materialMachineTable-menus .vxe-context-menu--link {
  width: auto !important;
}
</style>
