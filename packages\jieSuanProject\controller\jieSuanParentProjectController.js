const {Controller} = require("../../../core");


/**
 * 合同外工程归属，复用合同清单接口服务
 * @class
 */
class JieSuanParentProjectController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 查询合同内所有工程
     * @return
     */
    async queryHierarchyFb(args) {
        // constructId:项目id
        // singleId:单项id
        // unitId:当前单位工程id
        let {constructId,singleId, unitId} = args;
        const result = await this.service.jieSuanProject.jieSuanParentProjectService.queryParentProject(constructId,singleId, unitId);
        return result;
    }

    /*
    * 修改合同外单项工程的工程归属
    * */
    async updateParentProject(args){
        // constructId:项目id
        // singleId:单项id
        // unitId:当前单位工程id
        // parentProjectId:归属工程id
        // parentProjectName:归属工程显示值
        let {constructId,singleId,unitId,parentProjectId,parentProjectName} = args;
        const result = await this.service.jieSuanProject.jieSuanParentProjectService.updateParentProject(constructId,singleId,unitId,parentProjectId,parentProjectName);
        return result;
    }

    // 合同内清单筛选查询接口
    async queryInventory(args){
        /*constructId:项目id
        * singleId:单项id
        * unitId:当前单位工程id
        * name：名称关键词
        * difference：是否量差搜索；true为是.false为否
        * small：最小值
        * big：最大值*/
        let {constructId, singleId, unitId,name,difference,small,big} = args;
        const result = await this.service.jieSuanProject.jieSuanParentProjectService.queryInventory(constructId, singleId, unitId,name,difference,small,big);
        return result;
    }

    // 合同外复用合同清单接口
    async setInventory(args){
        // constructId:项目id
        // singleId:所选复用的单项id
        // unitId:所选复用的单位id
        // reuseSingleId：需要复用的单项id
        // reuseUnitId：需要复用的单位id
        // rule复用规则：1只复制清单，2清单和组件全部复制
        // quantityRule工程量复用规则：1量差幅度以外的工程量，2工程量全部复制，3工程量为0
        // istrue:quantityRule为1时，判断是否扣减合同外工程量
        // fbfxData：分部分项复用清单
        // csxmData：措施项目服用清单
        // small：最小值
        // big：最大值
        // relevanceName：关联合同显示字段：单项工程名称/单位工程名称
        let {constructId, singleId, unitId, reuseSingleId, reuseUnitId, rule, quantityRule, fbfxData, csxmData,istrue,small,big,relevanceName} = args;
        const result = await this.service.jieSuanProject.jieSuanParentProjectService.setInventory(constructId, singleId, unitId, reuseSingleId, reuseUnitId, rule, quantityRule, fbfxData, csxmData,istrue,small,big,relevanceName);
        return result;
    }

    // 新增修改关联合同清单接口
    async updateRelevanceInventory(args){
        // constructId:项目id
        // reuseSingleId:合同外单项id
        // reuseUnitId:合同外单位id
        // type：类型,fbfx分部分项，csxm措施项目
        // reuseSequenceNbr：合同外清单id
        // relevanceName：关联合同显示字段：单项工程名称/单位工程名称/清单编码
        // relevanceId：合同内清单id：单项工程id/单位工程id/清单id
        let {constructId, reuseSingleId, reuseUnitId, type, reuseSequenceNbr,relevanceName,relevanceId} = args;
        const result = await this.service.jieSuanProject.jieSuanParentProjectService.updateRelevanceInventory(constructId,reuseSingleId, reuseUnitId, type, reuseSequenceNbr,relevanceName,relevanceId);
        return result;
    }

    // 关联清单查看接口
    async queryRelevanceInventory(args){
        // constructId:项目id
        // relevanceId：合同内清单id：单项工程id/单位工程id/清单id
        // type：类型,fbfx分部分项，csxm措施项目
        let {constructId,relevanceId,type} = args;
        const result = await this.service.jieSuanProject.jieSuanParentProjectService.queryRelevanceInventory(constructId,relevanceId,type);
        return result;
    }


}

JieSuanParentProjectController.toString = () => '[class JieSuanParentProjectController]';
module.exports = JieSuanParentProjectController;
