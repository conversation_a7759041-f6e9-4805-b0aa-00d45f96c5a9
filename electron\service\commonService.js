const {Service, Storage} = require("../../core");
const MenuBarEnum = require("../enum/MenuBarEnum");
const {ResponseData} = require("../utils/ResponseData");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme,BrowserWindow
} = require('electron');


const {Electron} = require("../../core/electron");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {writeFile} = require("fs");
const {Snowflake} = require("../utils/Snowflake");
const os = require("os");
const path = require("path");
const _ = require("lodash");
const {DateUtils} = require("../utils/DateUtils");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const fs = require("fs");
const ConstantUtil = require("../enum/ConstantUtil");
const {arrayToTree} = require("../main_editor/tree");
const {ConstructOperationUtil} = require("../utils/ConstructOperationUtil");
const {toJsonYsfString} = require("../main_editor/util");
const {CryptoUtils} = require("../utils/CrypUtils");
const {DateTimeUtil} = require("../../common/DateTimeUtil");
const {HttpUtils} = require("../utils/HttpUtils");
const BsRemoteUrl = require("../enum/BsRemoteUrl");
const Log = require("../../core/log");
const HttpClient = require("../../core/httpclient");
const Fixs = require("../fixs");
const {ProjectFileUtils} = require("../../common/ProjectFileUtils");
let macCollector = require('getmac');

class CommonService extends Service {


    constructor(ctx) {
        super(ctx);
        let {
            constructProjectService,
            baseFeeFileService,
            projectOverviewService,
            itemBillProjectService,
            otherProjectService,
            rcjProcess
        } = this.service;
        this.map = new Map();
        this.map.set(MenuBarEnum.PROJECT_OVERVIEW, {
            obj: projectOverviewService,
            fun: projectOverviewService.getProjectOverviewCollectionData
        });//項目概況-项目
        this.map.set(MenuBarEnum.UNIT_PROJECT_OVERVIEW, {
            obj: projectOverviewService,
            fun: projectOverviewService.getProjectOverviewCollectionData
        });//項目概況-单位
        this.map.set(MenuBarEnum.FEE_COLLECTION_FORM, {
            obj: baseFeeFileService,
            fun: baseFeeFileService.getFeeCollectionTreeList
        });//取费文件列表-工程项目
        this.map.set(MenuBarEnum.SINGLE_FEE_COLLECTION_FORM, {
            obj: baseFeeFileService,
            fun: baseFeeFileService.getFeeCollectionTreeList
        });//取费文件列表-单项
        this.map.set(MenuBarEnum.UNIT_FEE_COLLECTION_FORM, {
            obj: baseFeeFileService,
            fun: baseFeeFileService.getFeeCollectionTreeList
        });//取费文件列表-单位
        this.map.set(MenuBarEnum.UNIT_ITEM_BILL, {
            obj: itemBillProjectService,
            fun: itemBillProjectService.queryUnitBranchTree
        });//分部分項-左側目錄書
        this.map.set(MenuBarEnum.UNIT_OTHER_PROJECT, {obj: otherProjectService, fun: otherProjectService.menuData});//分部分項-左側目錄書

        this.map.set(MenuBarEnum.UNIT_RCJ_SUMMARY, {obj: rcjProcess, fun: rcjProcess.getUnitRcjType});//人材机汇总左侧树
        this.map.set(MenuBarEnum.RCJ_SUMMARY, {obj: rcjProcess, fun: rcjProcess.getUnitRcjType});//人材机汇总左侧树
        this.map.set(MenuBarEnum.SINGLE_RCJ_SUMMARY, {obj: rcjProcess, fun: rcjProcess.getUnitRcjType});//人材机汇总左侧树
        //this.map.set(MenuBarEnum.RCJ_UNIT_CLASSIFICATION,{obj:rcjProcess,fun:rcjProcess.getRcjUnitClassification});//右键新建人材机分类表
    }


    async importYsfFile(args) {
        //let defaultStoragePath = PricingFileFindUtils.getDefaultStoragePath(null);
        let defaultStoragePath = await this.service.commonService.getSetStoragePath(null);
        let {extensions, dialogOptions} = await this.service.constructProjectService.defaultDialog(PricingFileFindUtils.getProjectObjById(args.constructId).biddingType, defaultStoragePath);
        const options = {
            properties: ['openFile'],
            //defaultPath: defaultStoragePath.toString(), // 默认保存路径
            defaultPath: defaultStoragePath, // 默认保存路径
            filters: [
                {name: '云算房', extensions:  [ConstantUtil.YUSUAN_FILE_SUFFIX_Z,ConstantUtil.YUSUAN_FILE_SUFFIX_D,ConstantUtil.YUSUAN_FILE_SUFFIX]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            console.log("未选中任何文件");
            return;
        }
        //获取选中的路径
        let filePath = result[0];
        if (!await this.service.constructProjectService.checkFileExistence(filePath)) {
            console.log("路径有误");
            return;
        }
        //导入后的
        let importProjectObj = await PricingFileFindUtils.getProjectObjByPath(filePath);
        await  new Fixs(importProjectObj,importProjectObj.version).fix();
        let importRes = await this.service.constructProjectService.generateLevelTreeNode(importProjectObj);

        //导入后的项目临时存放在当前项目中
        let projectObjById = await PricingFileFindUtils.getProjectObjById(args.constructId);

        if(projectObjById.path.endsWith(ConstantUtil.YUSUAN_FILE_SUFFIX_JS)){
            let v = await  new Fixs(projectObjById,importProjectObj.version).compareVersions();
            if ( v==1) {
                console.log("结算合同外导入预算项目 ysf文件版本低于1.0.38");
                return ResponseData.fail("预算文件版本过低，请重新上传");
            }
        }


        if (importProjectObj.deStandardReleaseYear !== projectObjById.deStandardReleaseYear) {
            return ResponseData.fail('定额标准不一致，无法导入');
        }

        if ((importProjectObj.projectTaxCalculation.taxCalculationMethod + '') !== (projectObjById.projectTaxCalculation.taxCalculationMethod + '')) {
            return ResponseData.fail('计税方式不一致，无法导入');
        }

        projectObjById.importProjectObj = importProjectObj;
        await this.service.projectOverviewService.updateYsfFile(projectObjById);

        return importRes;
    }

    async _editImportUnit(baseConstructId, singleId, unitParam, baseConstructFlatMap, importConstructFlatMap, parent, ifParentIsConstruct) {

        let lastUnit = null;
        if (unitParam.whetherNew === true) {
            lastUnit = importConstructFlatMap.get(unitParam.id);
            lastUnit.sequenceNbr = Snowflake.nextId();
            ObjectUtils.updatePropertyValue(lastUnit, 'unitId', lastUnit.sequenceNbr);
            ObjectUtils.updatePropertyValue(lastUnit, 'spId', singleId);
            ObjectUtils.updatePropertyValue(lastUnit, 'singleId', singleId);
            ObjectUtils.updatePropertyValue(lastUnit, 'constructId', baseConstructId);

            lastUnit.constructId = baseConstructId;
            lastUnit.spId = singleId;

            let itemBillProjects = lastUnit.itemBillProjects;
            let itemBillProjectsQd = itemBillProjects.filter(item => item.kind === ConstantUtil.QD_KIND);
            if (ObjectUtils.isNotEmpty(itemBillProjectsQd)) {
                for (let k = 0; k < itemBillProjectsQd.length; k++) {
                    itemBillProjectsQd[k].isLocked = 1;
                }
            }
            let measureProjectTables = lastUnit.measureProjectTables;
            let measureProjectTablesQd = measureProjectTables.filter(item => item.kind === "03");
            if (ObjectUtils.isNotEmpty(measureProjectTablesQd)) {
                for (let k = 0; k < measureProjectTablesQd.length; k++) {
                    measureProjectTablesQd[k].isLocked = 1;
                }
            }
            //将导入的ysf中 itemBillProjects measureProjectTables Array转为树

            lastUnit.itemBillProjects = arrayToTree(lastUnit.itemBillProjects)
            lastUnit.measureProjectTables = arrayToTree(lastUnit.measureProjectTables)
        } else {
            lastUnit = baseConstructFlatMap.get(unitParam.id);
        }

        ConstructOperationUtil.updateUnitName(lastUnit, unitParam.name);

        // 处理安装工程【房修需求】历史版本兼容问题
        await this.service.azCostMathService.clearAzUnitCacheByFwxsVersion(lastUnit);

        // 将处理后的unit加入父级中
        if (ifParentIsConstruct) {
            parent.unitProjectArray.push(lastUnit);
        } else {
            parent.unitProjects.push(lastUnit);
        }
    }

    async _editImportSingle(baseConstructId, singleParam, baseConstructFlatMap, importConstructFlatMap, parent, ifParentIsConstruct) {
        let lastSingle = null;
        if (singleParam.whetherNew === true) {
            lastSingle = importConstructFlatMap.get(singleParam.id);
            //导入工程与被导入工程有可能是同一个工程，需要重新生成id
            lastSingle.sequenceNbr = Snowflake.nextId();
            lastSingle.constructId = baseConstructId;
        } else {
            lastSingle = baseConstructFlatMap.get(singleParam.id);
        }

        lastSingle.projectName = singleParam.name;
        lastSingle.unitProjects = new Array();
        lastSingle.subSingleProjects = new Array();

        if (ifParentIsConstruct) {
            parent.singleProjects.push(lastSingle);
        } else {
            parent.subSingleProjects.push(lastSingle);
        }

        if (ObjectUtils.isNotEmpty(singleParam.children)) {
            for (const item of singleParam.children) {
                if (item.levelType == ConstantUtil.SINGLE_LEVEL_TYPE) {
                    await this._editImportSingle(baseConstructId, item, baseConstructFlatMap, importConstructFlatMap, lastSingle, false);
                } else if (item.levelType == ConstantUtil.UNIT_LEVEL_TYPE) {
                    await this._editImportUnit(baseConstructId, lastSingle.sequenceNbr, item, baseConstructFlatMap, importConstructFlatMap, lastSingle, false);
                }
            }
        }
    }

    async saveImportProject(args) {
        let baseConstructObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
        let importConstructObj = baseConstructObj.importProjectObj;
        //获取页面选择的结果树
        let newConstructStructureTree = args.projectStructureTree;
        if (ObjectUtils.isEmpty(newConstructStructureTree.children)) {
            delete baseConstructObj.importProjectObj;
            return;
        }

        let baseConstructFlatMap = ConstructOperationUtil.flatConstructTreeToMapById(args.constructId);
        let importConstructFlatMap = ConstructOperationUtil.flatConstructTreeToMapByObj(importConstructObj);

        // 1. 工程项目处理
        baseConstructObj.unitProjectArray = [];
        baseConstructObj.singleProjects = [];

        // 2. 工程项目下单项/单位工程处理
        for (const item of newConstructStructureTree.children) {
            if (item.levelType == ConstantUtil.SINGLE_LEVEL_TYPE) {
                await this._editImportSingle(args.constructId, item, baseConstructFlatMap, importConstructFlatMap, baseConstructObj, true);
            } else if (item.levelType == ConstantUtil.UNIT_LEVEL_TYPE) {
                await this._editImportUnit(args.constructId, null, item, baseConstructFlatMap, importConstructFlatMap, baseConstructObj, true);
            }
        }

        delete baseConstructObj.importProjectObj;
        baseConstructObj.globalConfigXX = null;
        await this.service.projectConfigProcess.refreshConstruct(baseConstructObj);
        await this.service.projectOverviewService.refreshJBXX(baseConstructObj);
        await this.service.projectOverviewService.updateYsfFile(baseConstructObj);
    }

    async saveImportProjectOld(args) {
        let projectObjById = await PricingFileFindUtils.getProjectObjById(args.constructId);

        //获取导入后的项目
        let importProjectObj = projectObjById.importProjectObj;


        //获取导入后所有单位
        let unitList = await this.getImportProjectUnitList(importProjectObj);

        //获取页面选择的结果树
        let projectStructureTree = args.projectStructureTree;

        if (!ObjectUtils.isEmpty(projectStructureTree.children)) {
            for (let i = 0; i < projectStructureTree.children.length; i++) {
                let singleProjectChild = projectStructureTree.children[i];

                if (singleProjectChild.levelType === 2) {
                    //单项
                    //原项目对应的单项
                    let singleProject = PricingFileFindUtils.getOneFromSingleProjects(projectObjById.singleProjects, singleProjectChild.id);
                    //先判断是否有新增的单项
                    if (singleProjectChild.whetherNew === true) {
                        let newSingleProject = PricingFileFindUtils.getOneFromSingleProjects(importProjectObj.singleProjects, singleProjectChild.id);

                        let copySingleProject = ConvertUtil.deepCopy(newSingleProject);
                        //重新刷新所有的ID
                        let spId = Snowflake.nextId();
                        copySingleProject.sequenceNbr = spId;
                        ObjectUtils.updatePropertyValue(copySingleProject, 'spId', spId);

                        if (!ObjectUtils.isEmpty(copySingleProject.unitProjects)) {
                            for (let j = 0; j < copySingleProject.unitProjects.length; j++) {
                                let copyUnit = ConvertUtil.deepCopy(copySingleProject.unitProjects[j]);
                                //重新刷新所有的单位ID
                                let unitId = Snowflake.nextId();
                                copyUnit.sequenceNbr = unitId;
                                ObjectUtils.updatePropertyValue(copyUnit, 'unitId', unitId);

                                let itemBillProjects = copyUnit.itemBillProjects;
                                let itemBillProjectsQd = itemBillProjects.filter(item => item.kind === "03");
                                if (!ObjectUtils.isEmpty(itemBillProjectsQd)) {
                                    for (let k = 0; k < itemBillProjectsQd.length; k++) {
                                        itemBillProjectsQd[k].isLocked = 1;
                                    }
                                }
                                let measureProjectTables = copyUnit.measureProjectTables;
                                let measureProjectTablesQd = measureProjectTables.filter(item => item.kind === "03");
                                if (!ObjectUtils.isEmpty(measureProjectTablesQd)) {
                                    for (let k = 0; k < measureProjectTablesQd.length; k++) {
                                        measureProjectTablesQd[k].isLocked = 1;
                                    }
                                }
                                copySingleProject.unitProjects[j] = copyUnit;
                            }
                        }

                        projectObjById.singleProjects.push(copySingleProject);
                    } else {
                        if (!ObjectUtils.isEmpty(singleProjectChild.children)) {
                            for (let j = 0; j < singleProjectChild.children.length; j++) {
                                let unitProjectChild = singleProjectChild.children[j];
                                if (unitProjectChild.whetherNew === true) {
                                    let newUnit = unitList.find(item => item.sequenceNbr === unitProjectChild.id);
                                    newUnit.spId = singleProject.sequenceNbr;
                                    newUnit.constructId = projectObjById.sequenceNbr;
                                    if (ObjectUtils.isEmpty(singleProject.unitProjects)) {
                                        singleProject.unitProjects = new Array();
                                    }
                                    let copyUnit = ConvertUtil.deepCopy(newUnit);
                                    //重新刷新所有的单位ID
                                    let unitId = Snowflake.nextId();
                                    copyUnit.sequenceNbr = unitId;
                                    ObjectUtils.updatePropertyValue(copyUnit, 'unitId', unitId);

                                    let itemBillProjects = copyUnit.itemBillProjects;
                                    let itemBillProjectsQd = itemBillProjects.filter(item => item.kind === "03");
                                    if (!ObjectUtils.isEmpty(itemBillProjectsQd)) {
                                        for (let k = 0; k < itemBillProjectsQd.length; k++) {
                                            itemBillProjectsQd[k].isLocked = 1;
                                        }
                                    }
                                    let measureProjectTables = copyUnit.measureProjectTables;
                                    let measureProjectTablesQd = measureProjectTables.filter(item => item.kind === "03");
                                    if (!ObjectUtils.isEmpty(measureProjectTablesQd)) {
                                        for (let k = 0; k < measureProjectTablesQd.length; k++) {
                                            measureProjectTablesQd[k].isLocked = 1;
                                        }
                                    }
                                    singleProject.unitProjects.push(copyUnit)
                                }
                            }
                        }
                    }
                } else if (singleProjectChild.levelType === 3) {
                    if (singleProjectChild.whetherNew === true) {
                        let newUnit = unitList.find(item => item.sequenceNbr === singleProjectChild.id);
                        newUnit.constructId = projectObjById.sequenceNbr;

                        let copyUnit = ConvertUtil.deepCopy(newUnit);
                        //重新刷新所有的单位ID
                        let unitId = Snowflake.nextId();
                        copyUnit.sequenceNbr = unitId;
                        ObjectUtils.updatePropertyValue(copyUnit, 'unitId', unitId);

                        let itemBillProjects = copyUnit.itemBillProjects;
                        let itemBillProjectsQd = itemBillProjects.filter(item => item.kind === "03");
                        if (!ObjectUtils.isEmpty(itemBillProjectsQd)) {
                            for (let k = 0; k < itemBillProjectsQd.length; k++) {
                                itemBillProjectsQd[k].isLocked = 1;
                            }
                        }
                        let measureProjectTables = copyUnit.measureProjectTables;
                        let measureProjectTablesQd = measureProjectTables.filter(item => item.kind === "03");
                        if (!ObjectUtils.isEmpty(measureProjectTablesQd)) {
                            for (let k = 0; k < measureProjectTablesQd.length; k++) {
                                measureProjectTablesQd[k].isLocked = 1;
                            }
                        }

                        projectObjById.unitProjectArray.push(copyUnit);
                    }
                }
            }
        }
        delete projectObjById.importProjectObj;

        //计算工作台底部价格汇总计算
        // PricingFileWriteUtils.countConstructProject(projectObjById.sequenceNbr)

        await this.service.projectOverviewService.updateYsfFile(projectObjById);
    }

    async deleteImportProject(args) {
        let projectObjById = await PricingFileFindUtils.getProjectObjById(args.constructId);
        delete projectObjById.importProjectObj
        await this.service.projectOverviewService.updateYsfFile(projectObjById);
    }

    async getImportProjectUnitList(proJectData) {
        let array = new Array();
        if (2 == proJectData.biddingType) {
            //单位工程
            let unitProject = proJectData.unitProject;
            array.push(unitProject);
            return array;
        } else {
            //单项
            let singleProjectList = proJectData.singleProjects;
            if (ObjectUtils.isEmpty(singleProjectList)) {
                if (!ObjectUtils.isEmpty(proJectData.unitProjectArray)) {
                    let unitProject = proJectData.unitProjectArray;
                    array = array.concat(unitProject);
                    return array;
                }
            } else {
                for (const single of singleProjectList) {
                    if (!ObjectUtils.isEmpty(single.unitProjects)) {
                        array = array.concat(single.unitProjects);
                    }
                }
                return array;
            }
        }

        return array;
    }

    /**
     * 返回传入的数据
     * @param projectObj
     * @returns {*}
     */
    importYsfHandler(projectObj) {
        //复制一份原始数据
        let copyObj = ConvertUtil.deepCopy(projectObj);

        let res = {};
        res.sequenceNbr = projectObj.sequenceNbr;
        res.constructName = projectObj.constructName;
        //无单项，只有单位的情况
        if (!ObjectUtils.isEmpty(copyObj.unitProjectArray)) {
            let unitProjects = new Array();
            let unitProjectArray = copyObj.unitProjectArray;
            for (let i = 0; i < unitProjectArray.length; i++) {
                let unitProject = {};
                let unitProjectArrayElement = unitProjectArray[i];
                unitProject.sequenceNbr = unitProjectArrayElement.sequenceNbr;
                unitProject.upName = unitProjectArrayElement.upName;
                unitProjects.push(unitProject);
            }
            res.unitProjects = unitProjects;
        }

        //既有单项又有单位的情况
        if (!ObjectUtils.isEmpty(copyObj.singleProjects)) {


            let singleProjects = new Array();
            let singleProjectsArray = copyObj.singleProjects;
            for (let i = 0; i < singleProjectsArray.length; i++) {
                let singleProject = {};

                let singleProjectElement = singleProjectsArray[i];
                singleProject.sequenceNbr = singleProjectElement.sequenceNbr;
                singleProject.projectName = singleProjectElement.projectName;

                if (!ObjectUtils.isEmpty(singleProjectElement.unitProjects)) {
                    let unitProjectArray = singleProjectElement.unitProjects;
                    let unitProjects = new Array();
                    for (let j = 0; j < unitProjectArray.length; j++) {
                        let unitProjectElement = unitProjectArray[j];
                        let unitProject = {};
                        unitProject.sequenceNbr = unitProjectElement.sequenceNbr;
                        unitProject.upName = unitProjectElement.upName;
                        unitProjects.push(unitProject);
                    }
                    singleProject.unitProjects = unitProjects;
                }
                singleProjects.push(singleProject);

            }
            res.singleProjects = singleProjects
        }
        return res;
    }

    async constructBackup(args, fileNameSuffix) {
        let {constructId} = args;

        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);

        let defaultStoragePath = await this.getSetStoragePath(projectObjById.constructName + fileNameSuffix);
        let {extensions, dialogOptions} = await this.service.constructProjectService.defaultDialog(projectObjById.biddingType, defaultStoragePath,'另存为');

        let filePath = dialog.showSaveDialogSync(null, dialogOptions);
        if (filePath) {
            if (!filePath.toUpperCase().endsWith(extensions[0])) {
                filePath += '.'+extensions[0];
            }
            //查询选择的路径是否已经有被打开的文件
            let result = await this.service.constructProjectFileService.getOneProDataByPath(filePath);
            if (!ObjectUtils.isEmpty(result)) {
                let projectObj = PricingFileFindUtils.getProjectObjById(result.sequenceNbr);
                if (!ObjectUtils.isEmpty(projectObj)) {
                    return ResponseData.success(2);
                }
            }
            //复制一份原始数据
            let copyObj = ConvertUtil.deepCopy(projectObjById);
            copyObj.path = filePath;
            //重新刷新所有的项目ID
            let constructId = Snowflake.nextId();
            copyObj.sequenceNbr = constructId;
            ObjectUtils.updatePropertyValue(copyObj, 'constructId', constructId);

            //PricingFileWriteUtils.creatYsfFile(copyObj);
            PricingFileWriteUtils.writeToMemory(copyObj);
            await this.service.ysfHandlerService.creatYsfFile(copyObj);

            return copyObj;
        }
        return null;
    }

    /**
     * 另存为
     */
    async fileSaveAs(args, fileNameSuffix,clearMemory =true) {
        let {constructId} = args;

        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);

        let defaultStoragePath = await this.getSetStoragePath(projectObjById.constructName + fileNameSuffix);
        let {extensions, dialogOptions} = await this.service.constructProjectService.defaultDialog(projectObjById.biddingType, defaultStoragePath,'另存为');
        let filePath = dialog.showSaveDialogSync(null, dialogOptions);
        if (filePath) {
            if (!filePath.toUpperCase().endsWith(extensions[0])) {
                filePath += '.'+extensions[0];
            }
            //查询选择的路径是否已经有被打开的文件
            let result = await this.service.constructProjectFileService.getOneProDataByPath(filePath);
            if (!ObjectUtils.isEmpty(result)) {
                let projectObj = PricingFileFindUtils.getProjectObjById(result.sequenceNbr);
                if (!ObjectUtils.isEmpty(projectObj)) {
                    return ResponseData.success(2);
                }
            }
            //复制一份原始数据
            let copyObj = ConvertUtil.deepCopy(projectObjById);
            copyObj.path = filePath;
            //重新刷新所有的项目ID
            let constructId = Snowflake.nextId();
            copyObj.sequenceNbr = constructId;
            ObjectUtils.updatePropertyValue(copyObj, 'constructId', constructId);

            //PricingFileWriteUtils.creatYsfFile(copyObj);
            PricingFileWriteUtils.writeToMemory(copyObj);
            await this.service.ysfHandlerService.creatYsfFile(copyObj);
            if (clearMemory) {
                global.constructProject[projectObjById.sequenceNbr] = null;

            }
            let newVar = global.windowMap.get(projectObjById.sequenceNbr);
            global.windowMap.set(copyObj.sequenceNbr,newVar);
            global.windowMap.delete(projectObjById.sequenceNbr);
            //用户的打开历史记录列表数据处理
            ProjectFileUtils.writeUserHistoryListFile(copyObj);
            return ResponseData.success(copyObj.sequenceNbr);
        }
        return ResponseData.success(0);
    }


    /**
     * 备份
     */
    async backups(args, fileNameSuffix,clearMemory =true) {
        let {constructId} = args;

        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);

        let defaultStoragePath = await this.getSetStoragePath(projectObjById.constructName + fileNameSuffix);
        let {extensions, dialogOptions} = await this.service.constructProjectService.defaultDialog(projectObjById.biddingType, defaultStoragePath,'另存为');
        let filePath = dialog.showSaveDialogSync(null, dialogOptions);
        if (filePath) {
            if (!filePath.toUpperCase().endsWith(extensions[0])) {
                filePath += '.'+extensions[0];
            }
            //查询选择的路径是否已经有被打开的文件
            let result = await this.service.constructProjectFileService.getOneProDataByPath(filePath);
            if (!ObjectUtils.isEmpty(result)) {
                let projectObj = PricingFileFindUtils.getProjectObjById(result.sequenceNbr);
                if (!ObjectUtils.isEmpty(projectObj)) {
                    return ResponseData.success(2);
                }
            }
            //复制一份原始数据
            let copyObj = ConvertUtil.deepCopy(projectObjById);
            copyObj.path = filePath;
            //重新刷新所有的项目ID
            let constructId = Snowflake.nextId();
            copyObj.sequenceNbr = constructId;
            ObjectUtils.updatePropertyValue(copyObj, 'constructId', constructId);

            //PricingFileWriteUtils.creatYsfFile(copyObj);
            PricingFileWriteUtils.writeToMemory(copyObj);
            await this.service.ysfHandlerService.creatYsfFile(copyObj);
            if (clearMemory) {
                global.constructProject[projectObjById.sequenceNbr] = null;
                let newVar = global.windowMap.get(projectObjById.sequenceNbr);
                global.windowMap.set(copyObj.sequenceNbr,newVar);
                global.windowMap.delete(projectObjById.sequenceNbr);
            }

            //用户的打开历史记录列表数据处理
            ProjectFileUtils.writeUserHistoryListFile(copyObj);
            return ResponseData.success(copyObj.sequenceNbr);
        }
        return ResponseData.success(0);
    }


    /**
     * ysf选择性导出
     * @param args
     * @return {Promise<ResponseData>}
     */
    async exportYsfFile(args) {
        let {name, id,biddingType} = args;
        //let defaultStoragePath = PricingFileFindUtils.getDefaultStoragePath(name);
        let defaultStoragePath = await this.service.commonService.getDownloadPath(name);

        let {extensions, dialogOptions} =await this.service.constructProjectService.defaultDialog(biddingType,defaultStoragePath)



        let filePath = dialog.showSaveDialogSync(null, dialogOptions);
        if (filePath && !filePath.canceled) {
            if (!filePath.toUpperCase().endsWith(extensions[0])) {
                filePath += '.'+extensions[0];
            }
            //查询选择的路径是否已经有被打开的文件
            let result = await this.service.constructProjectFileService.getOneProDataByPath(filePath);

            if (!ObjectUtils.isEmpty(result)) {
                let projectObj = PricingFileFindUtils.getProjectObjById(result.sequenceNbr);
                if (!ObjectUtils.isEmpty(projectObj)) {
                    return ResponseData.success(2);
                }
            }
            //导出的ysf
            let copyObj = await this.exportYsfHandler(args);
            copyObj.path = filePath;
            //重新刷新所有的项目ID
            let constructId = Snowflake.nextId();
            copyObj.sequenceNbr = constructId;
            await ObjectUtils.updatePropertyValue(copyObj, 'constructId', constructId);
            //PricingFileWriteUtils.creatYsfFile(copyObj);
            PricingFileWriteUtils.writeToMemory(copyObj);
            await this.service.ysfHandlerService.creatYsfFile(copyObj);
            global.constructProject[copyObj.sequenceNbr] = null;
            return ResponseData.success(1);
        }
        return ResponseData.success(0);
    }

    async exportYsfHandler(args) {
        //获取到内存中的原始项目
        let projectObj = await PricingFileFindUtils.getProjectObjById(args.id);
        //复制一份原始数据
        let copyObj = ConvertUtil.deepCopy(projectObj);
        let arr = [];
        this.getUnitList(arr, args);
        //无单项，只有单位的情况
        if (!ObjectUtils.isEmpty(copyObj.unitProjectArray)) {
            let unitIdList = arr.map(k => k.id);
            let unitProjectArray = copyObj.unitProjectArray;
            for (let i = unitProjectArray.length - 1; i >= 0; i--) {
                let item = unitProjectArray[i];
                if (!unitIdList.includes(item.sequenceNbr)) {
                    unitProjectArray.splice(i, 1);
                }
            }
        }

        await this.getSelected(args);

        await this.service.constructProjectService.reassign(args, ConstructOperationUtil.flatConstructTreeToMapByObj(copyObj), copyObj);
        /* //既有单项又有单位的情况
         if (!ObjectUtils.isEmpty(copyObj.singleProjects)){
             //前端单项tree
             let singleTree = args.children;
             //找到筛选的单项(过滤没选的)
             copyObj.singleProjects =  copyObj.singleProjects.filter(obj =>singleTree.some(item=>item.id === obj.sequenceNbr && item.selected ));
             singleTree = singleTree.filter(obj =>obj.selected);
             for (let i = 0; i < copyObj.singleProjects.length; i++) {
                 //内存单项
                 let singleProject = copyObj.singleProjects[i];
                 if(){

                 }
                 this.aaa(singleProject.subSingleProjects,singleTree[i].children)
             }

         }*/


        return copyObj;
    }


    getSelected(args) {
        let children = args.children;
        if (!ObjectUtils.isEmpty(children)) {
            for (let i = children.length - 1; i >= 0; i--) {
                if (!children[i].selected) {
                    children.splice(i, 1);
                } else {
                    let delFlag = this.getSelected(children[i]);
                }

            }
        }


    }


    aaa(subSingleProjects, children, unitProjects) {
        if (!ObjectUtils.isEmpty(subSingleProjects)) {
            for (let i = 0; i < subSingleProjects.length; i++) {
                let subSingleProjectSon = subSingleProjects[i].subSingleProjects;
                if (!ObjectUtils.isEmpty(subSingleProjectSon)) {
                    this.aaa(subSingleProjects, children[i].children, subSingleProjects[i].unitProjects);
                } else {
                    subSingleProjects[i].unitProjects = unitProjects.filter(obj => children.some(item => item.id === obj.sequenceNbr && item.selected));
                }
            }
        } else {
            unitProjects = unitProjects.filter(obj => children.some(item => item.id === obj.sequenceNbr && item.selected));

        }
    }

    getUnitList(arr, args) {
        let children = args.children;
        if (ObjectUtils.isEmpty(children)) {
            return;
        }
        for (const item of children) {
            if (item.levelType === 3 && item.selected) {
                arr.push(item);
            } else {
                this.getUnitList(arr, item);
            }
        }
    }


    /**
     * 保存内存数据到文件
     * @param args
     * @return {ResponseData}
     */
    async saveYsfFile(args) {
        let constructId = args.constructId;
        let projectObj = PricingFileFindUtils.getProjectObjById(constructId);

        let isReadOnly = await this.service.constructProjectService.isFileReadOnly(projectObj.path)

        if (isReadOnly){
            return ResponseData.success(false);
        }

        await this.service.ysfHandlerService.updateYsfFile(projectObj);
        return ResponseData.success(true);

    }

    /**
     * 获取菜单栏数据
     * @param args
     * @return {ResponseData}
     */
    getMenuList(args) {
        let type = args.type;
        let levelType = args.levelType;
        let values = Object.values(MenuBarEnum);
        let result = values.filter(item => item.type == type && item.levelType == levelType);
        return ResponseData.success(result);
    }

    /**
     * 获取菜单栏数据
     * @param args
     * @return {Promise<*|ResponseData>}
     */
    async getMenuData(args) {
        let type = args.type;
        let levelType = args.levelType;
        let code = args.code;
        let menuBarEnum = this.getMenuBarEnum(type, levelType, code);
        if (ObjectUtils.is_Undefined(menuBarEnum) || ObjectUtils.isNull(menuBarEnum)) {
            return ResponseData.fail("参数错误");
        }
        let objDefinition = this.map.get(menuBarEnum);
        if (ObjectUtils.isEmpty(objDefinition)) {
            return ResponseData.fail("缺少处理菜单方法");
        }
        let result = await objDefinition.fun.call(objDefinition.obj, args);
        /*if (args.code === "3" &&
            result.itemList && result.itemList.length>0 &&
            args.constructId && args.constructId !== "" &&
            args.singleId && args.singleId !== "" &&
            args.unitId && args.unitId !== "" ) {

            let baseFileId = PricingFileFindUtils.getMainFeeFile(args.constructId, args.singleId, args.unitId).feeFileId;
            for (let i = 0 ; i<result.itemList.length;++i) {
                if(result.itemList[i][baseFileId]) {
                    result.itemList[i].defaultFeeFlag = 1;
                }
            }
        }

        if (args.code === "3" &&
            (!args.singleId || args.singleId === "") &&
            result.itemList && result.itemList.length>0) {
            let unitFeeFiles;
            if (!args.unitId || args.unitId === "") {
                // 找全部的
                unitFeeFiles = [];
                let units = PricingFileFindUtils.getUnitList(args.constructId);
                for (let i = 0 ; i < units.length ; ++ i) {
                    unitFeeFiles = unitFeeFiles.concat(units[i].feeFiles);
                }
            } else {
                 // 找特定的 unit
                let unitp = PricingFileFindUtils.getUnitList(args.constructId).filter(f=>f.sequenceNbr === args.unitId);
                if (unitp.length > 0) {
                    unitFeeFiles =  unitp[0].feeFiles;
                } else {
                    unitFeeFiles =  PricingFileFindUtils.getUnitList(args.constructId)[0].feeFiles;
                }
            }

            let defFeeFile = unitFeeFiles.filter(f=>f.defaultFeeFlag && f.defaultFeeFlag === 1)[0];
            let baseFileId = defFeeFile.feeFileId;
            for (let i = 0 ; i<result.itemList.length;++i) {
                if(result.itemList[i][baseFileId]) {
                    result.itemList[i].defaultFeeFlag = 1;
                }
            }
        }*/

        //console.log(result);
        return result;
    }


    /**
     * 获取枚举值
     * @param type
     * @param levelType
     * @param code
     * @return {object | string | bigint | number | boolean | symbol}
     */
    getMenuBarEnum(type, levelType, code) {
        for (let menuBarEnumKey in MenuBarEnum) {
            let Bartype = MenuBarEnum[menuBarEnumKey].type;
            let BarlevelType = MenuBarEnum[menuBarEnumKey].levelType;
            let Barcode = MenuBarEnum[menuBarEnumKey].code;
            if (Bartype == type && BarlevelType == levelType && Barcode == code) {
                return MenuBarEnum[menuBarEnumKey];
            }
        }
    }

    /**
     * 关闭所有的子窗口
     * @return {ResponseData}
     */
    closeAllChildWindow() {
        const addonWindow = this.app.addon.window;
        let windowIdMap = addonWindow.windowIdMap;
        let winIdList = Object.keys(windowIdMap);
        winIdList.forEach(k => {
            this.saveYsfFile({constructId: windowIdMap[Number(k)]});
            this.service.selfTestService.remove(windowIdMap[Number(k)])
            let win = BrowserWindow.fromId(Number(k));
            win.close();
        });


    }


    /**
     * 获取默认存储路径
     */
    async getSetStoragePath(fileName) {
        const baseDataDir = `${os.homedir()}\\.xilidata\\userHistory.json`;
        //读取数据
        const data = fs.readFileSync(baseDataDir, 'utf8');
        const userHistoryData = JSON.parse(data);
        let result = {content: userHistoryData.DEF_SAVE_PATH}
        //此处需要判断是否登录 正常登录  离线登录  未登录
        if (ObjectUtils.isEmpty(global.idInformation)) {
            //未登录
            if (ObjectUtils.isEmpty(fileName)) {
                return `${result.content}`;
            } else {
                return `${result.content}\\${fileName}` + ConstantUtil.YUSUAN_FILE_DOT_SUFFIX;
            }
        } else {
            //获取用户信息
            let sequenceNbr = global.idInformation.sequenceNbr;
            let identity = global.idInformation.identity;
            if (ObjectUtils.isEmpty(fileName)) {
                return `${result.content}\\${sequenceNbr}\\${identity}`;
            } else {
                return `${result.content}\\${sequenceNbr}\\${identity}\\${fileName}` + ConstantUtil.YUSUAN_FILE_DOT_SUFFIX;
            }
        }


    }


    /**
     * 获取下载路径
     */
    async getDownloadPath(fileName) {
        const baseDataDir = `${os.homedir()}\\.xilidata\\userHistory.json`;
        //读取数据
        const data = fs.readFileSync(baseDataDir, 'utf8');
        const userHistoryData = JSON.parse(data);
        return `${userHistoryData.FILE_DOWNLOAD_PATH}\\${fileName}` + ConstantUtil.YUSUAN_FILE_DOT_SUFFIX;
        // //此处需要判断是否登录 正常登录  离线登录  未登录
        // if(ObjectUtils.isEmpty(global.idInformation)){
        //     //未登录
        //     if (ObjectUtils.isEmpty(fileName)){
        //         return `${result.content}`;
        //     }else {
        //         return `${result.content}\\${fileName}` + ConstantUtil.YUSUAN_FILE_DOT_SUFFIX;
        //     }
        // }else {
        //     //获取用户信息
        //     let sequenceNbr  = global.idInformation.sequenceNbr;
        //     let identity  = global.idInformation.identity;
        //     if (ObjectUtils.isEmpty(fileName)){
        //         return `${result.content}\\${sequenceNbr}\\${identity}`;
        //     }else {
        //         return `${result.content}\\${sequenceNbr}\\${identity}\\${fileName}` + ConstantUtil.YUSUAN_FILE_DOT_SUFFIX;
        //     }
        // }


    }


    /**
     * 设置存储路径
     * @param arg
     * @return {string|null}
     */
    async setSelectFolder(arg) {
        let {paramsTag} = arg;

        const baseDataDir = `${os.homedir()}\\.xilidata\\userHistory.json`;
        //读取数据
        const data = fs.readFileSync(baseDataDir, 'utf8');
        const userHistoryData = JSON.parse(data);


        let defaultStoragePath = await this.service.commonService.getSetStoragePath(null);
        const filePaths = dialog.showOpenDialogSync({
            properties: ['openDirectory', 'createDirectory'],
            defaultPath: userHistoryData[paramsTag]
        });

        if (_.isEmpty(filePaths)) {
            return ResponseData.success(0);
        }
        userHistoryData[paramsTag] = filePaths[0];
        let obj = ObjectUtils.toJsonString(userHistoryData);
        fs.writeFileSync(baseDataDir, obj);
        return ResponseData.success(1);
    }

    /**
     * 选择路径
     * @param arg
     * @return {string|null}
     */
    async selectPath(arg) {
        let {paramsTag, defaultPath} = arg;

        // const baseDataDir = `${os.homedir()}\\.xilidata\\userHistory.json`;
        // //读取数据
        // const data = fs.readFileSync(baseDataDir, 'utf8');
        // const userHistoryData = JSON.parse(data);


        // let defaultStoragePath = await this.service.commonService.getSetStoragePath(null);
        const filePaths = dialog.showOpenDialogSync({
            properties: ['openDirectory', 'createDirectory'],
            defaultPath: defaultPath
        });
        if (_.isEmpty(filePaths)) {
            return null;
        }
        //
        // userHistoryData[paramsTag] = filePaths[0];
        // let obj = ObjectUtils.toJsonString(userHistoryData);
        // fs.writeFileSync(baseDataDir, obj);

        return filePaths[0];
    }

    /**
     * 查询存储路径
     * @param arg
     * @return {string|null}
     */
    async selectFolder(args) {
        let {constructId} = args;
        let project = PricingFileFindUtils.getProjectObjById(constructId);
        const baseDataDir = `${os.homedir()}\\.xilidata\\userHistory.json`;
        //读取数据
        const data = fs.readFileSync(baseDataDir, 'utf8');
        const userHistoryData = JSON.parse(data);
        let array = [];
        array.push({paramsTag: "DEF_SAVE_PATH", content: userHistoryData.DEF_SAVE_PATH});
        array.push({paramsTag: "FILE_DOWNLOAD_PATH", content: userHistoryData.FILE_DOWNLOAD_PATH});
        array.push({paramsTag: "PROJECTATTR", content: project.projectAttrRelateMergeScheme});
        array.push({
            paramsTag: "RGFINMEASUREANDRPRICEINMECHANICALACTION",
            content: project.rgfInMeasureAndRPriceInMechanicalAction
        });
        array.push({paramsTag: "deGlTcFlag", content: project.deGlTcFlag});
        array.push({paramsTag:"mainRcjShowFlag",content:project.mainRcjShowFlag});
        array.push({paramsTag:"standardConversionShowFlag",content:project.standardConversionShowFlag});
        return ResponseData.success(array);
    }

    async diffProject(arg) {

        let constructId = arg.constructId;
        let cachePro = PricingFileFindUtils.getProjectObjById(constructId);
        let cacheProStr = toJsonYsfString(cachePro);
        let cacheDara = CryptoUtils.objectHash(cacheProStr, cachePro.sequenceNbr, false);
        let fileData = global.editProMap.get(cachePro.sequenceNbr);
        let result = false;
        if (cacheDara == fileData) {
            result = true;
        }
        // let localFilePro = await PricingFileFindUtils.getProjectObjByPath(cachePro.path);
        // let localFileProStr = JSON.stringify(localFilePro)
        // let result = ObjectUtils.deepEqual(cacheProStr,localFileProStr);
        return ResponseData.success(!result);
    }

    /**
     * 编码重刷
     * @param args
     * @returns {Promise<boolean>}
     */
    async batchRefresh(args) {
        let {constructId, singleId, unitId,type} = args;
        if(ObjectUtils.isEmpty(type)){
            type=2;
        }
        let standardMap = new Map();
        if(type==1){
              //获取所有单位
            let unitList = PricingFileFindUtils.getUnitList(constructId);

            for(const unit of unitList){
                await this.unitBatchRefresh(unit,standardMap);
            }
        }else {
            //分布分项              措施项目
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            await this.unitBatchRefresh(unit,standardMap);
        }
        //清除缓存
        standardMap.clear();
        return true;
    }

    async  unitBatchRefresh(unit,standardMap){

        //获取新结构数据
        let qdFbfx = [];
        let qd = unit.itemBillProjects.dll.head.next;
        if (qd) qdFbfx.push(qd);
        while (qd && qd.next) {
            qd = qd.next;
            qdFbfx.push(qd);
        }


        //获取所有标准的分部分项清单项目
        let filterItemBillProjects = _.filter(qdFbfx, (item) => item.kind == BranchProjectLevelConstant.qd && !_.isEmpty(item.standardId));
        if (!_.isEmpty(filterItemBillProjects)) {
            let groupItemBillProjects = _.groupBy(filterItemBillProjects, (item) => item.standardId);
            for (const groupItemBillProjectsKey in groupItemBillProjects) {
                let item = groupItemBillProjects[groupItemBillProjectsKey];
                //截取前9位
                let standardcode = _.truncate(item[0].bdCode, {length: 9, separator: "", omission: ""});
                //设置初始化编码  从1开始
                if(ObjectUtils.isEmpty(standardMap.get(groupItemBillProjectsKey))){
                    standardMap.set(groupItemBillProjectsKey, {standardcode, number: 1});
                }
            }
            for (let i = 0; i < qdFbfx.length; i++) {
                let item = qdFbfx[i];
                if (item.kind == BranchProjectLevelConstant.qd && !_.isEmpty(item.standardId)) {
                    let value = standardMap.get(item.standardId);
                    qdFbfx[i].bdCode = value.standardcode + _.padStart(value.number + "", 3, "0");
                    //使用后重置编码 编码+1
                    standardMap.set(item.standardId, {"standardcode": value.standardcode, "number": value.number + 1})
                }
            }
        }

        let qdCsxm = [];
        let cqd = unit.measureProjectTables.dll.head.next;
        if (cqd) qdFbfx.push(cqd);
        while (cqd && cqd.next) {
            cqd = cqd.next;
            qdCsxm.push(cqd);
        }
        //获取所有的标准的措施项目清单
        let filterMeasureProjectTables = _.filter(qdCsxm, (item) => item.kind == BranchProjectLevelConstant.qd && !_.isEmpty(item.standardId));
        if (!_.isEmpty(filterMeasureProjectTables)) {
            let groupMeasureProjectTables = _.groupBy(filterMeasureProjectTables, (item) => item.standardId);
            for (const groupMeasureProjectsKey in groupMeasureProjectTables) {
                //如果已经存在项目的 code  则不需要处理
                if (standardMap.has(groupMeasureProjectsKey)) continue;
                let item = groupMeasureProjectTables[groupMeasureProjectsKey];
                //截取前9位
                let standardcode = _.truncate(item[0].fxCode, {length: 9, separator: "", omission: ""});
                if(ObjectUtils.isEmpty(standardMap.get(groupMeasureProjectsKey))){
                    standardMap.set(groupMeasureProjectsKey, {standardcode, number: 1});
                }
                // //设置初始化编码  从1开始
                // standardMap.set(groupMeasureProjectsKey, {standardcode, number: 1})
            }
            for (let i = 0; i < qdCsxm.length; i++) {
                let item = qdCsxm[i];
                if (item.kind == BranchProjectLevelConstant.qd && !_.isEmpty(item.standardId)) {
                    let value = standardMap.get(item.standardId);
                    qdCsxm[i].fxCode = value.standardcode + _.padStart(value.number + "", 3, "0");
                    // 2025/4/21日   发现修改编码时，误判断编码有重复
                    // 原因是SupplementController.qdCodeExistInUnit中优先判断了bdCode，没有bdCode才去判断fxCode
                    // 但是发现bdCode和fxCode不一样，所以怀疑是修改编码时只改了fxCode
                    // 没有修改bdCode，此处改了fxCode之后再改下bdCode
                    qdCsxm[i].bdCode = qdCsxm[i].fxCode;
                    //使用后重置编码 编码+1
                    standardMap.set(item.standardId, {"standardcode": value.standardcode, "number": value.number + 1})
                }
            }
        }
    }

    /**
     * 获取软件使用有效期
     */
    async getSoftwareIndate(args) {
        //截止时间
        let startTime = "2024-10-31 23:59:59"
        let networkTime = await DateTimeUtil.getNetworkTime();

        let result = {};
        //有效期截止时间
        result.endTime = startTime;
        //相差几天
        result.differenceDay = 0;
        //是否正常使用
        result.isUse = true;
        //离线登录
        if (ObjectUtils.isEmpty(args.telNumber)) {
            if (DateTimeUtil.isAfter(networkTime, result.endTime, DateTimeUtil.DATE_TIME_FORMAT)) {
                result.isUse = false;
            }
            result.endTime = DateTimeUtil.getDateTimeFromTimestamp(result.endTime, DateTimeUtil.DATE_TIME_FORMAT3);
            return result;
        }
        //获取加了几天免费使用期限
        let data = await HttpUtils.GET(BsRemoteUrl.softwareIndate + args.telNumber, {'PRODUCT_CODE': 'AGENCY_ADMIN'}, null);
        let days = 0;
        if (ObjectUtils.isNotEmpty(data.result)) {
            days = data.result.days;
        }

        if (DateTimeUtil.isBefore(networkTime, result.endTime, DateTimeUtil.DATE_TIME_FORMAT)) {
            result.differenceDay = days;
        } else {
            //获取最终的结束时间
            let endTime = DateTimeUtil.addDaysToDate(startTime, days, DateTimeUtil.DATE_TIME_FORMAT);
            result.endTime = endTime;
            if (DateTimeUtil.isBefore(networkTime, endTime, DateTimeUtil.DATE_TIME_FORMAT)) {
                //需要加上总部接口免费时间
                let num = DateTimeUtil.getDaysDifference(networkTime, endTime, DateTimeUtil.DATE_TIME_FORMAT1);
                result.differenceDay = DateTimeUtil.getDaysBetweenDates(networkTime, endTime);
            } else {
                result.isUse = false;
            }
        }
        result.endTime = DateTimeUtil.getDateTimeFromTimestamp(result.endTime, DateTimeUtil.DATE_TIME_FORMAT3);
        return result;
    }

    /**
     * 获取软件使用有效期
     */
    async getSoftwareExpirationTime() {
        // let httpClient = new HttpClient(this.config);
        // //截止时间
        // let expirationTime = "2025-03-31 23:59:59";
        // const url = "https://hsjt-ysf-prod.oss-cn-zhangjiakou.aliyuncs.com/pricing/updater/expirationTime.json";
        // let result = await httpClient.request(url, {
        //     method: 'GET',
        //     data: {},
        //     headers: {},
        // });
        // if (result.res.status == 200) {
        //     let data = JSON.parse(result.res.data.toString());
        //     expirationTime = data.expirationTime;
        // } else {
        //     Log.info('error', result.res.toString());
        // }
        // let networkTime = await DateTimeUtil.getNetworkTime();
        // if (DateTimeUtil.isBefore(expirationTime, networkTime, DateTimeUtil.DATE_TIME_FORMAT)) {
        //     return false;
        // }
        return  true;
    }

    getMacAddress() {
        let mac = macCollector.default()
        console.log('---------mac: ' + mac);
        return mac;
    }
}
CommonService.toString = () => '[class CommonService]';
module.exports = CommonService;
