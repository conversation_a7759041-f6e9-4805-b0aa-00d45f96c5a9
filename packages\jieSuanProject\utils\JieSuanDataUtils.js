const os = require("os");
const fs = require("fs");
const {JieSuanFileUtils} = require("./JieSuanFileUtils");
const {throws} = require("assert");

const JieSuanCommonUtils = require("./JieSuanCommonUtils");
const {ObjectUtil} = require("../../../common/ObjectUtil");
// const {NullPointerException} = require("../../PreliminaryEstimate/core/tools/ProjectFileReader");
const JieSuanConstantUtil = require("../enum/JieSuanConstantUtil");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");


/**
 * 项目数据操作
 */
class JieSuanDataUtils extends JieSuanCommonUtils{


    rcjCollectSort(list,sort){
        if(ObjectUtils.isEmpty(sort)){
            return  list ;
        }
        let  ll = [];
        /*let before=list.filter(item=>{
            if(!((item.levelMark === ResourceConstants.LEVEL_MARK_PB_CL || item.levelMark === ResourceConstants.LEVEL_MARK_PB_JX) && item.markSum === RcjCommonConstants.MARKSUM_JX)){
                return   item;
            }
        });
        let after=list.filter(item=>{
            if((item.levelMark === ResourceConstants.LEVEL_MARK_PB_CL || item.levelMark === ResourceConstants.LEVEL_MARK_PB_JX) &&item.markSum === RcjCommonConstants.MARKSUM_JX){
                return   item;
            }
        });

        before.sort((a, b) => {
            return  this.sortDetail(a,b,sort);
        });
        after.sort((a, b) => {
            return  this.sortDetail(a,b,sort);
        });*/


        return  list.sort((a, b) => {
            return  this.sortDetail(a,b, {"field":sort.field, "direction":sort.order});
        });
    }
    sortDetail(a, b, { field, direction }) {
        const getValue = obj => (obj[field] || '').toString();

        let valueA = getValue(a);
        let valueB = getValue(b);

        // 判断是否为空值
        const isEmptyA = valueA === '';
        const isEmptyB = valueB === '';

        // 判断是否为数字
        const isNumericA = !isNaN(valueA) && !isEmptyA;
        const isNumericB = !isNaN(valueB) && !isEmptyB;

        // 判断是否为字母（只考虑英文字母）
        const isAlphabeticA = /^[a-zA-Z]+$/.test(valueA.replace(/[^a-zA-Z]/g, '')) && !isEmptyA && !isNumericA;
        const isAlphabeticB = /^[a-zA-Z]+$/.test(valueB.replace(/[^a-zA-Z]/g, '')) && !isEmptyB && !isNumericB;

        // 判断是否为汉字
        const isChineseA = /[\u4e00-\u9fff]/.test(valueA) && !isEmptyA && !isNumericA && !isAlphabeticA;
        const isChineseB = /[\u4e00-\u9fff]/.test(valueB) && !isEmptyB && !isNumericB && !isAlphabeticB;

        // 特殊字符（非数字、非字母、非汉字）
        const isSpecialA = !isNumericA && !isAlphabeticA && !isChineseA && !isEmptyA;
        const isSpecialB = !isNumericB && !isAlphabeticB && !isChineseB && !isEmptyB;

        // 排序优先级（空值 > 特殊字符 > 汉字 > 字母 > 数字）
        const orderA = isEmptyA ? 4 : isSpecialA ? 3 : isChineseA ? 2 : isAlphabeticA ? 1 : 0;
        const orderB = isEmptyB ? 4 : isSpecialB ? 3 : isChineseB ? 2 : isAlphabeticB ? 1 : 0;

        if (orderA !== orderB) {
            return direction === 'asc' ? orderA - orderB : orderB - orderA;
        }

        // 同优先级内比较
        if (isNumericA && isNumericB) {
            return direction === 'asc' ? valueA - valueB : valueB - valueA;
        } else if (isAlphabeticA && isAlphabeticB) {
            return direction === 'asc' ? valueA.localeCompare(valueB, undefined, { sensitivity: 'base', ignorePunctuation: true }) : valueB.localeCompare(valueA, undefined, { sensitivity: 'base', ignorePunctuation: true });
        } else if (isChineseA && isChineseB) {
            return direction === 'asc' ? valueA.localeCompare(valueB, undefined, { sensitivity: 'base' }) : valueB.localeCompare(valueA, undefined, { sensitivity: 'base' });
        } else if (isSpecialA && isSpecialB) {
            // 特殊字符按Unicode码点排序
            return direction === 'asc' ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);
        } else {
            // 处理混合类型或空值情况（理论上不应该到达这里，因为orderA和orderB已经处理过）
            return 0; // 或者可以根据实际情况返回其他值以决定排序顺序
        }
    }






    /**
     * 获取用户设置的默认存储路径
     * @param fileName
     * @return {string}
     */
     getSetStoragePath(fileName) {
        // if (!this.checkFileExistence(JieSuanConstantUtil.USERHISTORY_PATH))throw new NullPointerException("文件路径不能为空");
        if (!this.checkFileExistence(JieSuanConstantUtil.USERHISTORY_PATH)){
            return ;
        };
        //读取数据
        const userHistoryData = this.userHistoryData();
        if (ObjectUtil.isEmpty(fileName)){
            return `${userHistoryData.DEF_SAVE_PATH}\\${this.userId()}\\${this.userIdentity()}`;
        }else {
            return `${userHistoryData.DEF_SAVE_PATH}\\${this.userId()}\\${this.userIdentity()}\\${fileName}`.concat("."+JieSuanConstantUtil.JIESUAN_FILE_SUFFIX);
        }
    }


    /**
     * 根据项目ID获取项目数据并且返回项目数据对象
     * 从内存里面取
     * @param projectId 项目ID
     */
    getProjectDataById(projectId) {
        if (ObjectUtil.isEmpty(projectId) || ObjectUtil.isEmpty(global.jieSuanProject) || ObjectUtil.isEmpty(global.jieSuanProject[projectId])) {
            throw new throws("文件路径不能为空");
        }
        return global.jieSuanProject[id].proJectData;
    }


    /**
     * 获取项目下指定的单项
     * @param constructId
     * @param singleId
     * @return {*|null}
     */
    getSingleProject(constructId,singleId) {
        let proJectData = this.getProjectDataById(constructId);
        if (ObjectUtil.isEmpty(proJectData.singleProjects)){
            return null;
        }
        //单项
        let singleProject = proJectData.singleProjects.find((item ) => item.sequenceNbr === singleId);
        return singleProject;

    }


    /**
     * 获取单位工程
     * @param fileName
     * @return {UnitProject}
     */
    getUnit(constructId,singleId, unitId) {
        let proJectData = this.getProjectDataById(constructId);
        if (2 == proJectData.biddingType){
            //单位工程
            let unitProject = proJectData.unitProject;
            return unitProject;
        }else {
            //单项
            let singleProject = this.getSingleProject(constructId,singleId);
            if (ObjectUtil.isEmpty(singleProject)){
                if (!ObjectUtil.isEmpty(proJectData.unitProjectArray)){
                    let unitProject = proJectData.unitProjectArray.find((item ) => item.sequenceNbr === unitId);
                    return unitProject;
                }
            }else {
                let unitProject = singleProject.unitProjects.find((item ) => item.sequenceNbr === unitId);
                return unitProject;
            }
        }

    }

}

module.exports = {
    JieSuanDataUtils: new JieSuanDataUtils()
};

