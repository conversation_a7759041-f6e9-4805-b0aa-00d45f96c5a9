const os = require('os');
class ConstantUtil {

    static YUSUAN_FILE_SUFFIX = "YSF"; //投标项目
    static YUSUAN_FILE_SUFFIX_Z = "YSFZ"; //招标项目
    static YUSUAN_FILE_SUFFIX_D = "YSFD"; //单位工程
    static YUSUAN_FILE_SUFFIX_G = "YSFG"; //工料机
    static YUSUAN_FILE_SUFFIX_GS = "YGS"; //概算
    static YUSUAN_FILE_SUFFIX_JS = "YJS"; //结算
    static YUSUAN_FILE_SUFFIX_SH = "YSH"; //审核

    static YUSUAN_FILE_SUFFIX_LIST = ["YSFZ","YSFD","YSFG","YSF","YJS","YSH","YGS"];


    static YUSUAN_ROUTE = '#/projectDetail/customize?constructSequenceNbr=';
    static JIESUAN_ROUTE ='#/projectDetail/customize?type=jieSuan&constructSequenceNbr=';
    static YUSUANSHENHE_ROUTE ='#/projectDetail/customize?type=yssh&constructSequenceNbr=';
    static GS_ROUTE ='#/gsProjectDetail/customize&constructSequenceNbr=';
    static GLJ_ROUTE ='#/gljProjectDetail/customize?constructSequenceNbr=';


    //用户历史数据路径
    static USERHISTORY_PATH =`${os.homedir()}\\.xilidata\\userHistory.json`;
    static UPC_FILE_SUFFIX = "djgc";
    static FYHZ_FILE_SUFFIX = 'FYMB';
    static GAISUAN_FILE_SUFFIX = "YGS";
    static YUSUAN_FILE_DOT_SUFFIX = "." + ConstantUtil.YUSUAN_FILE_SUFFIX;

    static MUNICIPAL_ENGINEERING_COST_LIST = ["排水工程", "给水工程", "隧道工程", "桥涵工程", "道路工程", "路灯工程", "燃气与集中供热", "市政土石方工程、大型机械一次安拆及场外运输、拆除工程"];

    static PRECAST_RATE_FEE_NAME = "装配式混凝土结构工程";

    static HAS_SEC_LEVEL_PROJECT_NAME = "安装工程";

    static XML_FACTORY = ["惠招标", "优招标", "E招冀成", "招采进宝", "云采供", "冀招标", "招标通"];

    static DE_STANDARD_12 = "12";

    static DE_STANDARD_22 = "22";
    static DE_STANDARD_23 = '23';

    static TITLE_WITH_MARJOR_PROJECT = "随主工程";

    static STR_ANZHUANG_PROJECT = "安装工程";

    static YEAR_2022 = '2022';

    static BASE_FILE_LEVEL_TYPE = 0;
    static CONSTRUCT_LEVEL_TYPE = 1;
    static SINGLE_LEVEL_TYPE = 2;
    static UNIT_LEVEL_TYPE = 3;
    static SHEET_LEVEL_TYPE = 5;

    /** 清单kind */
    static QD_KIND = "03";
    /** 定额kind */
    static DE_KIND = "04";


    static UNIT_RENGONG  = "工日";

    static EXP_CG_RGHJ = "CGGRHJ";

    static GCLMXHJ = "GCLMXHJ";

    static LABEL_RGF_ADJUST= "措施中人工费调整";
    static CODE_RGF_ADJUST= "JXTBRGFTZ";
    static SPECIAL_RCJ= ['QTCLFBFB', '34000001-2', 'J00004', 'J00031', 'J00031', 'C11384', 'C00007', 'C000200','C11408',"C11388","J00006","J00008"];

    /** 12定额标准下综合用工编码 */
    static ZHYG_LEVEL_STR_1 = "10000001";
    static ZHYG_LEVEL_STR_2 = "10000002";
    static ZHYG_LEVEL_STR_3 = "10000003";

    /**
     * 22定额中设备费及其税金计算 费率
     * @type {number}
     */
    static SHEBEI_RATE =1.09;

    /**
     * 22定额中简易计税下设备费及其税金计算 费率
     * @type {number}
     */
    static SHEBEI_RATE_SIMPLE =1.03;

    /**
     * 12简易计税费率
     * @type {number}
     */
    static SHEBEI_RATE_SIMPLE_12 =1.0338;



    /**
    * 自定义列存储文件名
    * */
    static COLUMNNAME = "column.json";

    static BAIFENHAO = "%";


    //排序
    static QD_SORT  = "sort";
    //保存排序
    static QD_SORT_SAVE  = "sortSave";
    //还原排序
    static QD_SORT_RESET  = "sortReset";

    // 人材机 其他机械 总价措施记取使用
    static OTHER_JX = ['J00004', 'J00031', 'J00006', 'J00008'];

    static QD_DE_DECIMAL_POINT = 10;



}
module.exports = ConstantUtil
