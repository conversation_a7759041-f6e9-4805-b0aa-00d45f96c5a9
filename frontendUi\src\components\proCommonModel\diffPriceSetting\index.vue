<template>
  <div class="diff-price-setting">
    <common-modal
      className="dialog-comm resizeClass"
      title="价差取费设置"
      width="400"
      height="300"
      v-model:modelValue="props.visible"
      :mask="false"
      @close="cancel()"
    >
      <div class="range-content">
        <p class="single-item">
          <span>人工：</span>
          <vxe-select v-model="inputData.renGong" transfer>
            <vxe-option
              v-for="item in selectList"
              :key="item.code"
              :value="item.code"
              :label="item.name"
            ></vxe-option>
          </vxe-select>
        </p>
        <p class="single-item">
          <span>材料：</span>
          <vxe-select v-model="inputData.caiLiao" transfer>
            <vxe-option
              v-for="item in selectList"
              :key="item.code"
              :value="item.code"
              :label="item.name"
            ></vxe-option>
          </vxe-select>
        </p>
        <p class="single-item">
          <span>机械：</span>
          <vxe-select v-model="inputData.jiXie" transfer>
            <vxe-option
              v-for="item in selectList"
              :key="item.code"
              :value="item.code"
              :label="item.name"
            ></vxe-option>
          </vxe-select>
        </p>
        <p>说明：设置完成后,将在费用汇总中记取相应费用。</p>
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="sureHandle">确定</a-button>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import { reactive, watch, ref } from "vue";
import api from "@/api/jiesuanApi";
import { projectDetailStore } from "@/store/projectDetail.js";
import { message } from "ant-design-vue";
import operateList from "@/views/projectDetail/customize/operate";
import jieSuanDetail from "@/api/jiesuanApi";

const props = defineProps(["visible"]);
const emits = defineEmits(["update:visible", "updateData"]);
const inputData = reactive({
  renGong: 3,
  caiLiao: 3,
  jiXie: 3,
});
let unifyData = operateList.value.find(
  (item) => item.name === "unify-humanMachineSummary"
);
const projectStore = projectDetailStore();
const selectList = ref([])
const selectAllList = ref([
  {
    name: "记取税金",
    code: 1,
  },
  {
    name: "记取规费、税金",
    code: 2,
  },
  {
    name: "记取安文费、税金",
    code: 3,
  },
]);
const cancel = () => {
  emits("update:visible", false);
};

watch(
  () => props.visible,
  (val) => {
    console.log("价差取费设置", val);
    if (val) {
      if (projectStore?.currentTreeInfo?.deStandardReleaseYear === "22") {
        let newSelectList = JSON.parse(JSON.stringify(selectAllList.value));
        newSelectList = newSelectList.filter((item) => {
          return item.code !== 2;
        });
        selectList.value = newSelectList;
      }else{
        selectList.value = JSON.parse(JSON.stringify(selectAllList.value))
      }
      getInitList()
    }
  }
);
// 获取初始值
const getInitList = () => {
  let apiData = {
    levelType: projectStore.currentTreeInfo.levelType,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  if (projectStore.currentTreeInfo.levelType === 2) {
    apiData['singleId']=projectStore.currentTreeGroupInfo?.singleId
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  jieSuanDetail.getFeeSet(apiData).then((res) => {
    if (res.result) {
      inputData.renGong=res.result[1]
      inputData.caiLiao=res.result[2]
      inputData.jiXie=res.result[3]
    }
  })
}

// 工程项目级别价差取费设置
const sureHandle = () => {
  let obj = {
    1: inputData.renGong,
    2: inputData.caiLiao,
    3: inputData.jiXie,
  };
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    map: JSON.parse(JSON.stringify(obj)),
    kind: Number(projectStore.asideMenuCurrentInfo?.key),
    levelType:projectStore.currentTreeInfo.levelType
  };
  if(projectStore.currentTreeInfo.levelType==2){
    apiData['singleId']=projectStore.currentTreeGroupInfo?.singleId
  }
  if(projectStore.currentTreeInfo.levelType==3){
    apiData['singleId']=projectStore.currentTreeInfo?.parentId
    apiData['unitId']=projectStore.currentTreeInfo?.id
  }
  console.log("apiData", apiData);
  api.constructPriceDifferenceDeeSettingController(apiData).then((res) => {
    if (res.status === 200 && res.result) {
      message.success("价差取费设置成功");
      cancel();
      emits("updateData");
      if(projectStore.currentTreeInfo.levelType==3){
        return
      }
      projectStore.SET_HUMAN_UPDATA_DATA({
        isEdit: true,
        name: "unify-humanMachineSummary",
        updataData: [],
      });
      unifyData.disabled = false;
    }
  });
};
</script>

<style lang="scss" scoped>
.range-content {
  margin-bottom: 15px;
}
</style>
