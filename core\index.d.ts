import { Database } from "better-sqlite3";
import { BatchOperationalConversionRuleItemDto } from "../electron/controller/conversionDeController";
import { DataSource } from "typeorm";
import { StandardConvertMod } from "../electron/enum/ConversionSourceEnum";
import { ConstructProject } from "../electron/model/ConstructProject";
import { ItemBillProject } from "../electron/model/ItemBillProject";
import { MeasureProjectTable } from "../electron/model/MeasureProjectTable";
import {BaseRcj} from "../electron/model/BaseRcj";
import {BaseRcj2022} from "../electron/model/BaseRcj2022";

import { GsBatchOperationalConversionRuleItemDto } from "../packages/PreliminaryEstimate/controller/conversionDeController";
import { GsStandardConvertMod } from "../packages/PreliminaryEstimate/enums/ConversionSourceEnum";
import { GsItemBillProject } from "../packages/PreliminaryEstimate/models/GsItemBillProject";
import { GsMeasureProjectTable } from "../packages/PreliminaryEstimate/models/GsMeasureProjectTable";



export type CommonDto = {
	standardDeId: string;
	fbFxDeId: string;
	constructId: string;
	singleId: string;
	unitId: string;
};

declare class App {
	betterSqlite3DataSource: Database;
	appDataSource: DataSource;
	gsAppDataSource: any;
	gsSqlite3DataSource: any;
	gljAppDataSource: any;
	gljSqlite3DataSource: any;
}

declare class ConversionDeService {
	resetLineSuffix(line: ItemBillProject | MeasureProjectTable): void;
	conversionRule(
		constructId: string,
		singleId: string,
		unitId: string,
		fbFxDeId: string,
		v: BatchOperationalConversionRuleItemDto[]
	): any;
	upDateDefault(
		constructId: string,
		singleId: string,
		unitId: string,
		deId: string,
		updateDef: any
	): any;
	switchConversionMod(dto: {
		deId: string;
		standardConvertMod: StandardConvertMod;
	}): any;
	switchConversionMainMatMod(dto: { deId: string }): any;
	getDefDonversion(dto: CommonDto & { deId: string }): any;
}

declare class ItemBillProjectOptionService {
	addDeTempDel(constructId: any, singleId: any, unitId: any, arg3: any): any;
	removeLine(
		constructId: string,
		singleId: string,
		unitWorkId: string,
		pointLine: {
			sequenceNbr: string;
			parentId: string;
			kind: string;
		},
		isBlock: boolean
	): boolean;
	fillDataFromIndexPage(
		constructId: string,
		singleId: string,
		unitWorkId: string,
		pointLine: any,
		createType: string,
		indexId: string,
		unit: string,
		rootLineId: string,
		rcjFlag: number,
		bzhs: number,
		type?: string,
		libraryCode?: string,
		isInvokeCountCostCodePrice?: string
	): Promise<{ data: any; index: any }>;
}

declare class StepItemCostService {
	removeLine(
		constructId: string,
		singleId: string,
		unitWorkId: string,
		pointLine: {
			sequenceNbr: string;
			parentId: string;
			kind: string;
		},
		isBlock: boolean
	): boolean;
	fillDataFromIndexPage(
		constructId: string,
		singleId: string,
		unitWorkId: string,
		pointLine: any,
		createType: string,
		indexId: string,
		unit: string,
		rcjFlag: number,
		bzhs: number,
		type?: string,
		isInvokeCountCostCodePrice?: string,
		libraryCode?: string,
	): Promise<{ data: any; index: any }>;
}

declare class Management {
	sycnTrigger(name: string): Promise<void>;
	trigger(name: string): Promise<void>;
}

declare class ConversionInfoService {
	deleteConversionInfo(
		constructId: any,
		singleId: any,
		unitId: any,
		fbFxDeId: any,
		ruleId: any
	): any;
	addConversionInfo(
		constructId: any,
		singleId: any,
		unitId: any,
		fbFxDeId: any,
		baseRuleDetails: {
			sequenceNbr: any;
			kind: number;
			selected: any;
			math: any;
			rcjId: string;
			type: string;
			defaultValue: any;
		}
	): any;
	getDeConversionInfo(
		constructId: string,
		singleId: string,
		unitId: string,
		fbFxDeId: string
	): any;
}

declare class ConversionDeProcess {
	cleanRules(
		standardDeId: string,
		fbFxDeId: string,
		constructId: string,
		singleId: string,
		unitId: string
	): Promise<any>;
	conversionRuleList(dto: CommonDto): Promise<any>;
	conversionRuleListAll(dto: CommonDto): Promise<any>;
	getGroupNames(libraryCode: string): any;
	getGroupDetail(groupName: string, libraryCode: string): any;
}

declare class BaseBranchProjectOptionService {
	findLineOnlyById(deId: string): { line: any; belong: "fbfx" | "csxm" };
	[K: string]: (...args: any[]) => any;
}

declare class RcjProcess {
	addRcjData(
		deId: any,
		baseRcj: any,
		constructId: any,
		singleId: any,
		unitId: any
	): any;
	listBaseRcjArray(libraryCode: any, needInsertRcjCodes: string[]): any;
	specialRcjCode: any;
	addRcjLineOnOptionMenu(
		constructId: string,
		singleId: string,
		unitId: string,
		baseIndex: any,
		deId: any,
		is2022?: boolean
	):
		| { newRcj: any; pb: any; rcj: any }
		| PromiseLike<{ newRcj: any; pb: any; rcj: any }>;
	reCaculateRcjPrice(
		find: any,
		rcjs: any,
		constructId: any,
		singleId: any,
		unitId: any
	): any;
	getRcjListByDeId(
		sequenceNbr: any,
		constructId: any,
		singleId: any,
		unitId: any
	): any;
	[K: string]: (...args: any[]) => any;
}

declare class UnitPriceService {
	caculataDEUnitPrice(
		constructId: any,
		singleId: any,
		unitId: any,
		sequenceNbr: any,
		arg4: boolean,
		allData: any[]
	): any;
	caculateDeByRcj(
		consturctId: string,
		singleId: string,
		unitId: string,
		line: any,
		type?: string
	): any;
}
declare class BaseRcjService {
    getRcjListByRcjIdList(rcjIdList:any[]): Promise<BaseRcj[]>;
}

declare class BaseRcjService2022 {
    getRcjListByRcjIdList(rcjIdList:any[]): Promise<BaseRcj2022[]>;
}

declare class AutoCostMathService {
    autoCostMath(args:any):Promise<any>;
}

declare class UnitCostCodePriceService {
    countCostCodePrice(args:any):Promise<any>;
}

declare class GsProjectCommonService {
	findDeByDeId(constructId: string, unitId: string, deId: string): any;
	getUnit(constructId: string, unitId: string): any;
	getAllRcjByUnitId(constructId: string, unitId: string): any;
	getConstructProjectRcjs(constructId: string, unitId: string): any;
	findLineOnlyById(constructId: string, unitId: string, deId: string): any;
	findDeByUnit(constructId: string, unitId: string): any;
}

declare class GsRuleDetailFullService {
	getStandardConvertList(constructId: any, unitId: any, fbFxDeId: any): any;
	getStandardConvertListAll(constructId: any, unitId: any): any;
}

declare class GsConversionDeService {
	resetLineSuffix(line: GsItemBillProject | GsMeasureProjectTable): void;
	conversionRule(
		constructId: string,
		singleId: string,
		unitId: string,
		fbFxDeId: string,
		v: GsBatchOperationalConversionRuleItemDto[]
	): any;
	upDateDefault(
		constructId: string,
		singleId: string,
		unitId: string,
		deId: string,
		updateDef: any
	): any;
	switchConversionMod(dto: {
		deId: string;
		standardConvertMod: GsStandardConvertMod;
	}): any;
	switchConversionMainMatMod(dto: { deId: string }): any;
	getDefDonversion(dto: CommonDto & { deId: string }): any;
}

declare class GsConversionInfoService {
	deleteConversionInfo(
		constructId: any,
		singleId: any,
		unitId: any,
		fbFxDeId: any,
		ruleId: any
	): any;
	addConversionInfo(
		constructId: any,
		singleId: any,
		unitId: any,
		fbFxDeId: any,
		baseRuleDetails: {
			sequenceNbr: any;
			kind: number;
			selected: any;
			math: any;
			rcjId: string;
			type: string;
			defaultValue: any;
		}
	): any;
	getDeConversionInfo(
		constructId: string,
		singleId: string,
		unitId: string,
		fbFxDeId: string
	): any;
	updateDeConversionInfo(
		constructId: string,
		singleId: string,
		unitId: string,
		fbFxDeId: string,
		selectId: string,
		operateAction: string,
	): any;
}


declare class GsConversionDeProcess {
	cleanRules(
		standardDeId: string,
		fbFxDeId: string,
		constructId: string,
		singleId: string,
		unitId: string
	): Promise<any>;
	conversionRuleList(dto: CommonDto): Promise<any>;
	conversionRuleListAll(dto: CommonDto): Promise<any>;
	getGroupNames(libraryCode: string): any;
	getGroupDetail(groupName: string, libraryCode: string): any;
}

declare class GsAZservice {
    calculateAZFeeLianDongAndAloneBzhs(
        constructId: any,
        singleId: any,
        unitId: any,
        deRowId: any,
        type: any
    ): any;
}

declare class GsDeService {
	calculateZSFee(
		constructId: any,
		unitId: any,
		isCaclu: any
	): any;
}

declare class GljProjectCommonService {
	findDeByDeId(constructId: string, unitId: string, deId: string): any;
	getUnit(constructId: string, unitId: string): any;
	getAllRcjByUnitId(constructId: string, unitId: string): any;
	getConstructProjectRcjs(constructId: string, unitId: string): any;
	findLineOnlyById(constructId: string, unitId: string, deId: string): any;
	findDeByUnit(constructId: string, unitId: string): any;
}

declare class GljRuleDetailFullService {
	getStandardConvertList(constructId: any, unitId: any, fbFxDeId: any): any;
	getStandardConvertListAll(constructId: any, unitId: any): any;
}

declare class GljConversionDeService {
	resetLineSuffix(line: GsItemBillProject | GsMeasureProjectTable): void;
	conversionRule(
		constructId: string,
		singleId: string,
		unitId: string,
		fbFxDeId: string,
		v: GsBatchOperationalConversionRuleItemDto[]
	): any;
	upDateDefault(
		constructId: string,
		singleId: string,
		unitId: string,
		deId: string,
		updateDef: any
	): any;
	switchConversionMod(dto: {
		deId: string;
		standardConvertMod: GsStandardConvertMod;
	}): any;
	switchConversionMainMatMod(dto: { deId: string }): any;
	getDefDonversion(dto: CommonDto & { deId: string }): any;
}

declare class GljConversionInfoService {
	deleteConversionInfo(
		constructId: any,
		singleId: any,
		unitId: any,
		fbFxDeId: any,
		ruleId: any
	): any;
	addConversionInfo(
		constructId: any,
		singleId: any,
		unitId: any,
		fbFxDeId: any,
		baseRuleDetails: {
			sequenceNbr: any;
			kind: number;
			selected: any;
			math: any;
			rcjId: string;
			type: string;
			defaultValue: any;
		}
	): any;
	getDeConversionInfo(
		constructId: string,
		singleId: string,
		unitId: string,
		fbFxDeId: string
	): any;
	updateDeConversionInfo(
		constructId: string,
		singleId: string,
		unitId: string,
		fbFxDeId: string,
		selectId: string,
		operateAction: string,
	): any;
}


declare class GljConversionDeProcess {
	cleanRules(
		standardDeId: string,
		fbFxDeId: string,
		constructId: string,
		singleId: string,
		unitId: string
	): Promise<any>;
	conversionRuleList(dto: CommonDto): Promise<any>;
	conversionRuleListAll(dto: CommonDto): Promise<any>;
	getGroupNames(libraryCode: string): any;
	getGroupDetail(groupName: string, libraryCode: string): any;
}


declare class GljAZservice {
	calculateAZFeeLianDongAndAloneBzhs(
		constructId: any,
		singleId: any,
		unitId: any,
		deRowId: any,
		type: any
	): any;
}

declare class GljDeService {
	calculateZSFee(
		constructId: any,
		unitId: any,
		isCaclu: any
	): any;
}

type NestedService = {
	jieSuanOtherProjectService: any;
	otherProjectService: any;
	gongLiaoJiProject: any;
	PreliminaryEstimate: any;
	exportDesignService: any;
	pdfService: any;
	constructProjectRcjService: any;
	stepItemCostService: StepItemCostService;
	itemBillProjectOptionService: ItemBillProjectOptionService;
	management: Management;
	baseDeRuleRelationService: any;
	baseRuleDetailsService: any;
	baseRuleFileDetailsService: any;
	conversionRuleOperationRecordService: any;
	conversionInfoService: ConversionInfoService;
	conversionDeProcess: ConversionDeProcess;
	conversionDeService: ConversionDeService;
	baseBranchProjectOptionService: BaseBranchProjectOptionService;
	rcjProcess: RcjProcess;
	unitPriceService: UnitPriceService;
	baseRcjService: BaseRcjService;
	baseRcj2022Service: BaseRcjService2022;
	autoCostMathService: AutoCostMathService;
	unitCostCodePriceService:UnitCostCodePriceService;
	gsConversionInfoService: GsConversionInfoService;
	gsConversionDeProcess: GsConversionDeProcess;
	gsConversionDeService: GsConversionDeService;
	gsProjectCommonService: GsProjectCommonService;
	gsAZservice: GsAZservice;
	gsDeService: GsDeService;

	gljConversionInfoService: GljConversionInfoService;
	gljConversionDeProcess: GljConversionDeProcess;
	gljConversionDeService: GljConversionDeService;
	gljProjectCommonService: GljProjectCommonService;
	gljAZservice: GljAZservice;
	gljDeService: GljDeService;
};





export class Service {
	constructor(ctx: unknown);
	protected readonly service: NestedService;
	protected readonly app: App;
}
export class Controller {
	constructor(ctx: unknown);
	protected readonly service: NestedService;
}

export type Rule = {
	relationDeId?: any;
	libraryCode?: string;
	sequenceNbr: string;
	type: string;
	kind: number;
	math: string;
	relation: string;
	defaultValue: number;
	selectedRule: string;
	fbFxDeId: string;
	clpb?: any;
	nowChange?: boolean;
};
declare global {
	namespace globalThis {
		var constructProject: { [T: string]: ConstructProject };
	}
}
