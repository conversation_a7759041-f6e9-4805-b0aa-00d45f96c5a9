'use strict';

const FxthCgFeeMatchHandle = require("../../../../../electron/service/costCalculation/fxtjCostMatch/fxthCgFeeMatchHandle");
const DePropertyTypeConstant = require("../../../../../electron/enum/DePropertyTypeConstant");
const {JieSuanCostMatchUtil} = require("../jieSuanCostMatchUtil");
const {PricingFileFindUtils} = require("../../../../../electron/utils/PricingFileFindUtils");
const EE = require("../../../../../core/ee");
const { BaseDe2022, BaseDe } = require('../../../../../electron/model/BaseDe');
const {BaseDeFwxsCgRelation2022} = require("../../../../../electron/model/BaseDeFwxsCgRelation2022");
const {BaseDeFwxsCgRelation} = require("../../../../../electron/model/BaseDeFwxsCgRelation");

/**
 * 房修土建 超高费 记取
 */
class JieSuanFxthCgFeeMatchHandle extends FxthCgFeeMatchHandle {

  constructor() {
    super();
  }

  async customizeConfirmCostDe(unit, baseDeArr, args, qdNode) {
    const is22Unit = PricingFileFindUtils.is22Unit(unit);
    // 超高费需要根据檐高来确定费用定额，根据檐高对基数定额进行分组
    const baseDeGroupByFloors = baseDeArr.reduce((result, current) => {
      if (!result[current.cgEavesHighFloors]) {
        result[current.cgEavesHighFloors] = [];
      }
      result[current.cgEavesHighFloors].push(current);
      return result;
    }, {});
    let result = {
      'costDeArr': [],
      'costDeBaseDe': {}
    };
    let costDeArr = [];
    let costDeBaseDe = {};
    for (const baseDeId of Object.keys(baseDeGroupByFloors)) {
      // 本次要添加的费用定额对应的基数定额
      const baseDeArr = baseDeGroupByFloors[baseDeId];
      const baseDeFwxsCgRelation = await EE.app.appDataSource.manager.getRepository(is22Unit ? BaseDeFwxsCgRelation2022 : BaseDeFwxsCgRelation).findOne({
        where: { sequenceNbr: baseDeId }
      });
      const baseDe = await EE.app.appDataSource.manager.getRepository(is22Unit ? BaseDe2022 : BaseDe).findOne({
        where: {
          libraryCode: baseDeFwxsCgRelation.libraryCode,
          deCode: baseDeFwxsCgRelation.deCode,
          deName: baseDeFwxsCgRelation.deName
        }
      });
      let costDe = JieSuanCostMatchUtil.getFxtjCostDeByBaseDe(baseDe, DePropertyTypeConstant.FXTJ_CG);
      const newCostDe = await JieSuanCostMatchUtil.confirmQdAddCostDe(unit, qdNode, costDe, args.constructionMeasureType);
      costDeArr.push(newCostDe);
      costDeBaseDe[newCostDe.sequenceNbr] = baseDeArr;
    }
    result.costDeArr = costDeArr;
    result.costDeBaseDe = costDeBaseDe;
    return result;
  }


}

JieSuanFxthCgFeeMatchHandle.toString = () => '[class JieSuanFxthCgFeeMatchHandle]';
module.exports = JieSuanFxthCgFeeMatchHandle;
