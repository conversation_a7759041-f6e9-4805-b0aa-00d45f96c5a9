'use strict';

const FxtjCostMatchContext = require("../../../../../electron/service/costCalculation/fxtjCostMatch/FxtjCostMatchContext");
const FxtjCostConstants = require("../../../../../electron/service/costCalculation/fxtjCostMatch/FxtjCostConstants");
const JieSuanFxthCgFeeMatchHandle = require("./jieSuanFxthCgFeeMatchHandle");
const JieSuanFxthCzysFeeMatchHandle = require("./jieSuanFxthCzysFeeMatchHandle");
const JieSuanFxthZxxjxFeeMatchHandle = require("./jieSuanFxthZxxjxFeeMatchHandle");
const JieSuanFxthGcsdFeeMatchHandle = require("./jieSuanFxthGcsdFeeMatchHandle");

/**
 * 房修土建费用记取上下文
 */
class JieSuanFxtjCostMatchContext extends FxtjCostMatchContext{

  constructor(feeType) {
    super(feeType);
    this.costMatchStrategy = this.getHandler(feeType);
  }

  getHandler(feeType) {
    let handle;
    switch (feeType) {
      case FxtjCostConstants.CG:
        handle = new JieSuanFxthCgFeeMatchHandle();
        break;
      case FxtjCostConstants.CZYS:
        handle = new JieSuanFxthCzysFeeMatchHandle();
        break;
      case FxtjCostConstants.ZXXJX:
        handle = new JieSuanFxthZxxjxFeeMatchHandle();
        break;
      case FxtjCostConstants.GCSDF:
        handle = new JieSuanFxthGcsdFeeMatchHandle();
        break;
    }
    return handle;
  }


  async fxtjCostMatch(args) {
    return await this.costMatchStrategy.fxtjCostMatch(args);
  }

}

JieSuanFxtjCostMatchContext.toString = () => '[class JieSuanFxtjCostMatchContext]';
module.exports = JieSuanFxtjCostMatchContext;
