
const {ResponseData} = require("../../../electron/utils/ResponseData");
const SecurityFeeProjectController = require("../../../electron/controller/securityFeeProjectController");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const JieSuanSecurityFeeProjectService = require("../service/jieSuanSecurityFeeProjectService");

/**
 * 动态列字段展示接口
 * @class
 */
class JieSuanSecurityFeeProjectController extends SecurityFeeProjectController {


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
        this.jieSuanSecurityFeeProjectService =
            ObjectUtils.isNotEmpty(this.service.jieSuanSecurityFeeProjectService)?this.service.jieSuanSecurityFeeProjectService :new JieSuanSecurityFeeProjectService(ctx);
    }

    async queryAllProjectSecurity(args){
        // constructId:项目id

        let arr = await this.jieSuanSecurityFeeProjectService.queryAllProjectSecurity(args.constructId);
        return  ResponseData.success(arr)
    }

    async updateAllProjectSecurity(args){
       //  constructId:项目id
       //  securityFee:固定安文费值
       //  unitList:固定安文费清单
       return await this.jieSuanSecurityFeeProjectService.updateAllProjectSecurity(args.constructId,args.securityFee,args.unitList);
    }


}
JieSuanSecurityFeeProjectController.toString = () => '[class JieSuanSecurityFeeProjectController]';
module.exports = JieSuanSecurityFeeProjectController;