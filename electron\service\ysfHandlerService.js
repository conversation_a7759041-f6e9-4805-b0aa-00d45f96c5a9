const {Service} = require("../../core");
const {ConstructProject} = require("../model/ConstructProject");
const {Snowflake} = require("../utils/Snowflake");
const {ObjectUtils} = require("../utils/ObjectUtils");
const JSZip = require("jszip");
const fs = require("fs");
const {writeFile} = require("fs");
const ConstructBiddingTypeConstant = require("../enum/ConstructBiddingTypeConstant");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const {UPCContext} = require("../unit_price_composition/core/UPCContext");
const {toJsonYsfString} = require("../main_editor/util");
const crypto = require("crypto");
const ConstantUtil = require("../enum/ConstantUtil");
const {ReadXmlUtil} = require("../utils/ReadXmlUtil");
const {CryptoUtils} = require("../utils/CrypUtils");
const { PricingFileFindUtils } = require('../utils/PricingFileFindUtils');
const path = require('path');

class YsfHandlerService extends Service{

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 创建YSF文件
     * @param obj
     * @return {Promise<*>}
     */
    async creatYsfFile(obj) {
        if (!obj instanceof ConstructProject) {
            throw new Error("参数有误");
        }
        obj.fileCode = Snowflake.nextId();
        let data = toJsonYsfString(obj);
        // 创建一个新的压缩包实例
        const zip = new JSZip();
        // 添加JSON数据到压缩包中
        data = CryptoUtils.encryptAESData(data);
        zip.file('file.json', data);
        // 添加XML数据到压缩包中
        let type = 1;
        if (obj.biddingType === ConstructBiddingTypeConstant.tbProject || obj.biddingType === ConstructBiddingTypeConstant.unitProject){
            type = 2;
        }
        let stringPromise =await this.service.jsonToXmlService.generateXmlString(obj,type);
        stringPromise = CryptoUtils.encryptAESData(stringPromise);
        zip.file('file.xml', stringPromise);
        //考虑到历史文件 可能为ysf 需要重新生成对应后缀的文件
        await this.updateSuffix(obj);
        // 生成压缩包
        //await zip.generateAsync({ type: 'nodebuffer', compression: 'DEFLATE',compressionOptions: { level: 9 }}).then(function (content) {
        await zip.generateAsync({ type: 'nodebuffer'}).then(function (content) {
            // 将压缩包数据写入磁盘并将后缀名改为ysf
            fs.writeFileSync(obj.path, content);
        }).catch(function (error) {
            console.error('创建压缩包时发生错误:', error);
        });
        return obj;
    }

    async updateYsfFile(obj) {
        if (!obj instanceof ConstructProject) {
            throw new Error("参数有误");
        }
        obj.fileCode = Snowflake.nextId();
        obj.UPCContext = UPCContext.getAllData();
        //此处暂存是因为修改后缀后找不到之前的文件了
        let beforePath = obj.path
        //考虑到历史文件 可能为ysf 需要重新生成对应后缀的文件
        await this.updateSuffix(obj);
        // 创建一个新的压缩包实例
        const zip = new JSZip();
        // 添加JSON数据到压缩包中
        let jsonData = CryptoUtils.encryptAESData(toJsonYsfString(obj));
        zip.file('file.json', jsonData);
        // 添加XML数据到压缩包中
        let type = 1;
        if (obj.biddingType === ConstructBiddingTypeConstant.tbProject || obj.biddingType === ConstructBiddingTypeConstant.unitProject){
            type = 2;
        }
        let stringPromise =await this.service.jsonToXmlService.generateXmlString(obj,type);
        stringPromise = CryptoUtils.encryptAESData(stringPromise);
        zip.file('file.xml', stringPromise);
        const zipFileContent = fs.readFileSync(beforePath);
        // 读取压缩包
        const zipObject = await new JSZip().loadAsync(zipFileContent);
        //考虑到之前存储过 更新招标书存储过
        if (zipObject.file("zb.xml")) {
            const zbXmlContent = await zipObject.file("zb.xml").async("string");
            zip.file('zb.xml', zbXmlContent);
        }
        if (zipObject.file(obj.constructName+".html")) {
            const htmlContent = await zipObject.file(obj.constructName+".html").async("string");
            zip.file(obj.constructName+".html", htmlContent);
        }

        //处理依据文件数据
        let  tempExtractPath = await this.handleAccordingFileToZip(zip,zipObject,beforePath);
        // 生成压缩包
        await zip.generateAsync({ type: 'nodebuffer' }).then(function (content) {
            // 将压缩包数据写入磁盘并将后缀名改为ysf
            fs.writeFileSync(obj.path, content);
        }).catch(function (error) {
            console.error('创建压缩包时发生错误:', error);
        });

        // //删除临时文件
        // if(ObjectUtils.isNotEmpty(tempExtractPath)){
        //     try {
        //         fs.rmSync(tempExtractPath, { recursive: true, force: true });
        //         console.log('目录及其内容已成功删除');
        //     } catch (err) {
        //         console.error('删除目录时出错:', err);
        //     }
        // }


        // 在最后一步更新文件hash值  放在前面可能会引起hash之后的文件被修改
        let data = toJsonYsfString(PricingFileFindUtils.getProjectObjById(obj.sequenceNbr));// ObjectUtils.toJsonString(obj);
        CryptoUtils.objectHash(data, obj.sequenceNbr, true);
        return obj;
    }

    /**
     * 处理保存yjs文件时的依据文件数据
     * @param zip
     * @param zipObject
     * @returns {Promise<void>}
     */
    async handleAccordingFileToZip(zip,zipObject,objPath){
        //获取文件后缀名
        let pathSuffix =objPath.match(/[^.]+$/)[0];
        if (pathSuffix != "YJS"){
            return ;
        }
        let tempExtractPath=null;
        //获取所有的依据文件
        let files = Object.keys(zipObject.files);
        if(ObjectUtils.isNotEmpty(files)){
            let ts = files.filter(f=>f.startsWith("accordingFile"));
            if(ObjectUtils.isEmpty(ts)){ return }
            //创建临时文件
            // tempExtractPath = path.join(__dirname, 'temp_file');
            // // 创建临时目录并提取文件
            // if (!fs.existsSync(tempExtractPath)) {
            //     fs.mkdirSync(tempExtractPath);
            // }
            for(const af of ts){
                let extractedFile = zipObject.file(af);
                //创建文件
                // const extractedFilePath = path.join(tempExtractPath, path.basename(af));
                // fs.writeFileSync(extractedFilePath, await extractedFile.async('nodebuffer'));
                // const fileContent = fs.readFileSync(extractedFilePath);
                // zip.file(af, fileContent);
                zip.files[af]=extractedFile;
            }
        }

        return  tempExtractPath;
    }


    async updateSuffix(obj) {
        const suffixToBiddingType = {YSFZ: 0, YSF: 1, YSFD: 2, YJS: 3, YSH: 5, YGS: 4, YSFG: 7,};
        const biddingTypeToSuffix = Object.entries(suffixToBiddingType).reduce((acc, [suffix, type]) => {
            acc[type] = suffix;
            return acc;
        }, {});
        // 获取文件后缀
        let pathSuffix = obj.path.match(/[^.]+$/)[0];
        if (obj.biddingType !== suffixToBiddingType[pathSuffix]) {
            //如果不一致 根据上边的后缀改成对应的后缀
            // 如果不匹配，获取正确的后缀
            const correctSuffix = biddingTypeToSuffix[obj.biddingType];
            obj.path = obj.path.replace(/\.[^.]+$/, `.${correctSuffix}`);
        }
    }
}
YsfHandlerService.toString = () => '[class YsfHandlerService]';
module.exports = YsfHandlerService;
