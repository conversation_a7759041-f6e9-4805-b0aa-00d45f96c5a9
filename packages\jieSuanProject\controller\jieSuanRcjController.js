const {Controller} = require("../../../core");
const {ResponseData} = require("../../../common/ResponseData");


/**
 * 结算人材机处理接口
 * @class
 */
class JieSuanRcjController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    // /**
    //  * 合同内22定额非原始数据的含税、不含税市场价是否可以手动编辑
    //  *      如果当前单位的合同内原始人材机中包含五要素相同的人材机数据时不可编辑
    //  * @return   true  可以修改   false不能修改
    //  */
    // editableHtnNotOriginalPriceColl(args){
    //     let {constructId, singleId, unitId, rcjList} = args;
    //     this.service.jieSuanProject.jieSuanRcjProcess.editableHtnNotOriginalPrice(args);
    //     return ResponseData.success(rcjList);
    // }



    /**
     * 分部分项/措施项目 人材机明细列表
     * @param id 分部分项/措施项目id
     * @param branchType 1分部分项、2措施项目
     * @param constructId
     * @param singleId
     * @param unitId
     * @return {ConstructProjectRcj[]}
     */
    jieSuanQueryRcjDataByDeId(args) {
        let constructProjectRcjs = this.service.jieSuanProject.jieSuanRcjProcess.jieSuanQueryRcjDataByDeId(args);
        return constructProjectRcjs;

    }

    async getjieSuanConstructIdTree(args) {
        const result = await this.service.jieSuanProject.jieSuanRcjProcess.getjieSuanConstructIdTree(args);
        return ResponseData.success(result);

    }

    /**
     * 市场价调整系数修改
     */
    async adjustmentCoefficient(args){
        const result = await this.service.jieSuanProject.jieSuanRcjProcess.adjustmentCoefficient(args);
        return ResponseData.success(result);
    }




    /**
     * 来源分析
     */
    async rcjFrom(args){
        const result = await this.service.jieSuanProject.jieSuanRcjProcess.rcjFrom(args);
        return ResponseData.success(result);
    }


    /**
     * 单项人材机导入
     * @param args
     * @return {Promise<ResponseData>}
     */
    async useSingleExcelRcjPrice(args){

        const result = await this.service.jieSuanProject.jieSuanRcjProcess.useSingleExcelRcjPrice(args);
        return ResponseData.success(result);

    }

    /**
     * 单位工程 人材机汇总 导入 Excel 修改人材机汇总 市场价
     * @param args
     * @returns {Promise<void>}
     */
    async useUnitExcelRcjPrice(args){
        //const result = await this.service.rcjProcess.useUnitExcelRcjPrice(args);
        const result = await this.service.jieSuanProject.jieSuanRcjProcess.useUnitExcelRcjPrice(args);

        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");

        return ResponseData.success(result);

    }


}

JieSuanRcjController.toString = () => '[class JieSuanRcjController]';
module.exports = JieSuanRcjController;
