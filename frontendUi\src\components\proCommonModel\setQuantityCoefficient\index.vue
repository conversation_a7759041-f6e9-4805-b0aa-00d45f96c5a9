<!--
 * @Descripttion: 工程量乘系数
 * @Author: renmingming
 * @Date: 2024-03-06 18:56:51
 * @LastEditors: renmingming
 * @LastEditTime: 2024-07-18 11:28:23
-->
<template>
  <commonModal
    className="dialog-comm"
    :width="500"
    v-model:modelValue="show"
    title="设置系数"
  >
    <a-form
      :model="formState"
      name="basic"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item
        label="工程量乘系数"
        name="coefficientValue"
        :rules="[
          { required: true, trigger: 'blur', message: '请输入工程量乘系数' },
        ]"
      >
        <a-input-number
          @blur="coefficientValueBlur"
          v-model:value="formState.coefficientValue"
        />
      </a-form-item>
      <!-- <a-checkbox v-model:checked="checkClear" @change="onCheckClearChange">
        清除系数调整
      </a-checkbox> -->
      <div class="list-btn">
        <a-button @click="onHandleClose" style="margin-right: 25px"
          >取消</a-button
        >
        <a-button type="primary" @click="onHandleConfirm">确认</a-button>
      </div>
    </a-form>
  </commonModal>
</template>

<script setup>
import { ref, toRaw, watch, computed, inject } from 'vue';
import jsApi from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
const projectStore = projectDetailStore();
const emit = defineEmits(['successCallback', 'update:visible']);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const coefficientValueBlur = () => {
  if (Number(formState.value.coefficientValue) === 0) {
    formState.value.coefficientValue = 1;
  }
};
const show = computed({
  get: () => {
    if (props.visible) {
      if (projectStore.subCurrentInfo?.stageType === 2) {
        message.error('按分期工程量输入无法使用该功能');
        emit('update:visible', false);
        return false;
      }
      if (!selectQDData().length) {
        message.error('请选择未锁定综合单价的清单数据！');
        emit('update:visible', false);
        return false;
      }
    }
    return props.visible;
  },
  set: val => {
    if (!val) {
    }
    emit('update:visible', val);
  },
});

// 选中的未锁定的清单数据
const selectQDData = () => {
  let selectData = projectStore.subItemProjectAutoPosition?.selectData;
  if (!selectData && projectStore.subCurrentInfo) {
    console.log(
      'selectData无值:',
      selectData,
      'subCurrentInfo有值：',
      projectStore.subCurrentInfo
    );
    selectData =  [projectStore.subCurrentInfo] ;
  }
  // console.log(selectData, 'selectData');
  return (
    selectData?.filter(
      item =>
        item.kind === '03' &&
        (!item.lockPriceFlag || item.originalFlag) &&
        (item.stageType !== 2 || !item.stageType)
    ) || []
  );
};

let formState = ref({
  coefficientValue: 1,
});
let checkClear = ref(false);
const onCheckClearChange = () => {};
const onHandleClose = () => {
  show.value = false;
  formState.value.coefficientValue = 1;
};
const onHandleConfirm = () => {
  const qdList = JSON.parse(JSON.stringify(selectQDData()));
  if (!qdList.length) return message.error('无符合条件的清单数据');
  const param = {
    coefficientValue: formState.value.coefficientValue,
    qdList,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  console.log(param);
  jsApi.batchCoefficientAdjustment(param).then(res => {
    console.log(res);
    onHandleClose();
    message.success('系数调整成功');
    projectStore.subItemProjectAutoPosition?.queryBranchDataById();
  });
};
</script>
<style lang="scss" scoped>
.list-btn {
  margin-top: 20px;
  text-align: center;
}
</style>
