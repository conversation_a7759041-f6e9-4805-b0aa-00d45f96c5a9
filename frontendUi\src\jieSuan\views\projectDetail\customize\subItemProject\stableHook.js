import api from '@/api/projectDetail.js';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import {useEventListener} from "@vueuse/core";

const projectStore = projectDetailStore();
export const stableHook = ({ currentInfo }, type, callBack ) => {

  /**
   * 是否全部选中主材设备
   * @returns
   */
  const isAllSelectedZCSB = (rows) => {
    if (!rows) {
      rows = Array.isArray(currentInfo.value)? currentInfo.value :[currentInfo.value] ;// getSelectedRows();
    }
    if (!rows.length) return false;
    return rows.every(item => [94, 95].includes(item.kind));
  };

  return {
    isAllSelectedZCSB
  }
};
