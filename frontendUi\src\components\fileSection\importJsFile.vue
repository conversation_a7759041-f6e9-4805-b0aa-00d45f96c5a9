<!--
 * @Descripttion: 
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-25 17:43:51
-->
<template>
  <common-modal @close="cancel()" className="dialog-comm tree-dialog" :showClose="!loading" :loading="loading"
    width="auto" v-model:modelValue="dialogVisible" title="导入项目">
    <div class="tree-content-wrap">
      <div class="tree-list">
        <div class="dialog-content">
          <div class="title">当前项目</div>
          <div class="list" v-if="currentTreeData">
            <a-radio-group v-model:value="currentSelected.keys">
              <a-tree :defaultExpandAll="true" show-line selectable :tree-data="currentTreeData"
                :fieldNames="{ children: 'children', title: 'name', key: 'id' }" @select="currentSelect">
                <template #switcherIcon="{ switcherCls, children }">
                  <down-outlined :class="switcherCls" />
                </template>
                <template #title="{ levelType, id, name, whetherNew, whetherReplace, parentId }">
                  <icon-font class="mIcon del-icon" :class="`delicon${levelType}`" type="icon-shanchu1"
                    v-show="whetherNew || whetherReplace" @click="onClickDelete(id, whetherNew, whetherReplace)" />
                  <span class="check-labels">{{ name }}</span>
                </template>
              </a-tree>
            </a-radio-group>
          </div>
        </div>
        <div class="conButtonBox">
          <a-button type="primary" @click="move('add')" :disabled="disableAddBtn">添加</a-button>
          <div style="width:10px;height:15px;"></div>
          <a-button type="primary" @click="move('replace')" :disabled="disableReplaceBtn">替换</a-button>
          <!-- <div style="width:10px;height:15px;"></div>
          <a-button type="primary" @click="move('match')" :disabled="isMath">匹配合并</a-button> -->
          <!-- <a-button type="primary" @click="move">
            <template #icon><left-outlined /></template>
            导入
          </a-button> -->
          <!-- <a-button type="primary" @click="match" style="margin-top: 20px">
            匹配合并
          </a-button> -->
        </div>

        <div class="dialog-content">
          <div class="title">
            <span>导入项目</span>
            <div class="checkhandle">
              <a-radio-group v-model:value="checkStatus" @change="changeStatus">
                <a-radio value="all">全部</a-radio>
                <a-radio value="part">取消全选</a-radio>
              </a-radio-group>
            </div>
          </div>
          <div class="list" v-if="importTreeData">
            <a-tree :defaultExpandAll="true" checkable show-line multiple :tree-data="importTreeDataShow"
              @check="importSelect" :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
              :checkedKeys="importCheckedKeys">
              <template #switcherIcon="{ switcherCls, children }">
                <down-outlined :class="switcherCls" />
              </template>
            </a-tree>
          </div>
        </div>
      </div>
      <!-- <div class="group-list">
        <a-radio-group v-model:value="dataStatus">
          <a-radio value="all">以当前项目费率为准</a-radio>
          <a-radio value="part">以导入项目标准率为准</a-radio>
        </a-radio-group>
      </div> -->
      <!-- <div style="position: absolute; bottom: 30px">
        <a-radio-group
          v-model:value="radioAorT"
          :disabled="currentSelected.node ? currentSelected.node.originalFlag : true"
        >
          <a-radio value="1">合同外追加导入</a-radio>
          <a-radio value="2">合同外替换导入</a-radio>
        </a-radio-group>
      </div> -->
      <div class="footer-btn-list">
        <a-button @click="cancel()">取消</a-button>
        <a-button type="primary" @click="handleOk" :title="handleTip()" :loading="submitLoading">确定</a-button>
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { message } from "ant-design-vue";
import { ref, reactive, watch, nextTick, toRaw, computed, watchEffect } from "vue";
import { useRoute } from "vue-router";
import csProject from "@/api/jiesuanApi";
import { getJsAsideTreeList } from "@/api/jiesuanApi";
import { ConstructMenuOperator } from "@/components/editJsProjectStructure/ConstructMenuOperator";
const menuOperator = new ConstructMenuOperator();
import { DownOutlined, LeftOutlined } from "@ant-design/icons-vue";
import xeUtils from "xe-utils";
const emits = defineEmits(["closeDialog"]);
const route = useRoute();
const submitLoading = ref(false);
const dialogVisible = ref(false);
const currentTreeData = ref(null);
const copyTreeData = ref(null);
const importTreeData = ref(null);
const importCheckedKeys = ref([]);
const checkStatus = ref(null); //全选，取消全选
const hasSingle = ref(false); //当前项目，是否有单项结构
const importSingleList = ref([]); // 导入项目，所有的单项结构
const loading = ref(false);
const currentSelectKeys = ref([]); //当前选中项目
const expandedKeys = ref([]); //当前项目选中
const radioAorT = ref("");
const historyKeys = ref([]); //记录已经操作过的选中的数据
const currentSelected = reactive({
  initData: [],
  keys: "",
  node: null,
  parentObj: [], //选中项单位的父级列表
});
const importSelected = reactive({
  historyKeys: [],
  handleKeys: [], //点击了全选
});
const dataStatus = ref(null);
const importYsf = ref(null);
const disableAddBtn = computed(() => {
  return !currentSelected?.node || importCheckedKeys.value.length === 0;
});
const isMath=ref(true)
const disableReplaceBtn = computed(() => {
  const newSelectData = getNewLevel3Nodes(
    importTreeData.value?importTreeData.value:[],
    importCheckedKeys.value,
    historyKeys.value
  );
  console.log(newSelectData);
  return !(
    currentSelected?.node &&
    currentSelected?.node?.levelType === 3 &&
    !["add", "replace", "merge"].includes(currentSelected?.node?.type) &&
    newSelectData.length === 1
  );
});
const props = defineProps(["importJsList"]);
watch(
  () => historyKeys.value,
  () => {
    importTreeData.value = markDisabled(importTreeData.value, historyKeys.value);
  }
);
function markDisabled(nodes, disableIds) {
  return nodes.map((node) => {
    // 先处理子节点
    const children = Array.isArray(node.children)
      ? markDisabled(node.children, disableIds)
      : [];

    // 判断自己是否在禁用列表中
    const selfDisabled = disableIds.includes(node.id);
    // 如果有子节点，且所有子节点都被禁用，则父节点也应禁用
    const allChildrenDisabled =
      children.length > 0 && children.every((child) => child.disabled === true);

    // 最终的 disabled 状态
    const disabled = selfDisabled || allChildrenDisabled;

    return {
      ...node,
      children,
      disabled,
    };
  });
}
// 处理确定的提示语
const handleTip = () => {
  let msg = "";
  if (!historyKeys.value.length) {
    msg = "请选择导入项目!";
  }

  // else if(!dataStatus.value){
  //   msg="请选择费率标准!"
  // }
  return msg;
};

const currentSelect = (selectedKeys, { selected, selectedNodes, node, event }) => {
  // expandedKeys.value----当前选中项直级父级
  let target = currentTreeTile.find((i) => i.id === selectedKeys[0]);
  if (target?.levelType === 3) {
    let parent = menuOperator.getSingleNew(target, currentTreeTile);
    expandedKeys.value = target.parentId;
  } else {
    expandedKeys.value = selectedKeys[0];
  }
  let nodeData = node.dataRef;
  currentSelected.keys = selectedKeys[0]; //当前选中的key
  currentSelected.node = nodeData; //当前选中的node
  let parentList = getParentId(currentTreeData.value, currentSelected.keys);
  currentSelected.parentObj =
    parentList && parentList.filter((i) => i.levelType !== 1 && i.levelType !== 3);
  console.log(
    "currentSelected",
    currentSelected,
    "expandedKeys.value",
    expandedKeys.value
  );
  //   // 如果选择的为合同内项目则下方切换设置为空
  if (nodeData.originalFlag) {
    radioAorT.value = "";
  } else {
    radioAorT.value = "1";
  }
};

const importSelect = (checkedKeys, { checked, checkedNodes, node, event }) => {
  let importArr=[]
  for(let item of checkedNodes){
    if(!item.disabled){
      importArr.push(item.id)
    }
  }
  importCheckedKeys.value = importArr;
};
let unitImportList = ref([]);
const getImportUnitList = () => {
  unitImportList.value = importTreeTile.filter(
    (i) => i.levelType === 3 && importCheckedKeys.value.includes(i.id)
  );
};

// 修改导入项目选择
const changeStatus = async () => {
  importSelected.handleKeys = [];
  await flattenTree(importTreeData.value, true);
  importCheckedKeys.value =
    checkStatus.value === "all"
      ? [...importSelected.handleKeys, ...importSelected.historyKeys]
      : importSelected.historyKeys;
};

//平铺结构的导入树及导出树
let currentTreeTile = reactive([]); //当前平铺树
let importTreeTile = reactive([]); //导入平铺树
const setOriginalFlag = (parent) => {
  if (parent.levelType !== 1 && parent.children.length > 0) {
    parent.children.map((a) => {
      a.originalFlag = parent.originalFlag;
      setOriginalFlag(a);
    });
  } else if (parent.levelType === 1 && parent.children.length > 0) {
    parent.children.map((a) => {
      setOriginalFlag(a);
    });
  }
};
const tileTreeList = () => {
  setOriginalFlag(currentTreeData.value[0]);
  setOriginalFlag(importTreeData.value[0]);
  currentTreeTile = xeUtils.toTreeArray(currentTreeData.value);
  importTreeTile = xeUtils.toTreeArray(importTreeData.value);
};


// 点击添加
let impostMoveNodes = ref([]);
const move = async (type) => {
  if (!currentTreeData.value.length) {
    message.error("当前项目错误！");
    return;
  }
  if(type=='match'){
    onClickMerge()
    return
  }
  // 查找要复制过去的元素
  const elNodes = importCheckedKeys.value
    .filter((i) => !importSelected.historyKeys.includes(i))
    .map((i) => toRaw(findElement(i, importTreeData.value)));
  const unitelNodes = elNodes.filter((i) => i.levelType === 3);
  impostMoveNodes.value = [...elNodes];
  const tree = xeUtils.toArrayTree(unitelNodes, { key: "id" });
  // 向左侧插入数据前置校验
  const res = await appendAfterCheck(tree, unitelNodes);
  if (!res.status) {
    message.error(res.msg);
    return;
  }
  // const target = currentTreeTile.find((i) => i.id === expandedKeys.value);//获取左侧树选中节点
  if(type=='add'){
    // 设置whetherNew
    recursionTree(tree, "addNew");
    onClickAdd()
  }else if(type=='replace'){
    onClickReplace()
  }
  //插入数据
  // appendTree(toRaw(unitelNodes),type);
  //  导入项目，禁用已经导入的
  // disableItem();
};

// 开始向左侧插入数据前置校验
const appendAfterCheck = (tree, importNodes) => {
  return new Promise(async (resolve, reject) => {
    // if (!tree.length) {
    //   resolve({ status: false, msg: "请选择导入层级" });
    //   return;
    // }
    const target = currentTreeTile.find((i) => i.id === currentSelected.keys);
    if (!currentSelected.keys||!target) {
      resolve({ status: false, msg: "请选择当前项目" });
      return;
    }
    if (importCheckedKeys.value.length === 0) {
      resolve({ status: false, msg: "请选择要导入项目" });
      return;
    }
    // 如果左侧点击的是工程项目级别则不需要进行合同内外判断
    if(target.levelType==1){
      resolve({ status: true });
      return true;
    }
    const importTar = [...importNodes];
    if (importTar.filter((i) => i.originalFlag === target.originalFlag).length===0) {
      resolve({ status: false, msg: "合同内项目与合同外项目不可交叉导入" });
      return;
    }
    if (importTar.filter((i) => i.type === target.type).length===0) {
      resolve({ status: false, msg: "单项工程标签不一致，导入失败！" });
      return;
    }
    // let leveLimit = true; //是否符合规则
    // // 左侧选择的如果是合同内  则右侧单位工程也得选中的是合同内
    // if (target.originalFlag) {
    //   importTar.map((i) => {
    //     // 如果选中的右侧数据包含合同外的数据则不符合规则
    //     if(!i.originalFlag){
    //       leveLimit=false
    //     }
    //   });
    // }
    // if (!leveLimit) {
    //   resolve({ status: false, msg: "请选择正确的层级结构进行导入" });
    //   return;
    // }
    resolve({ status: true });
  });
};
// 删除左侧数据
const onClickDelete = (targetId,whetherNew,whetherReplace) => {
  // 1. 定位目标节点
  const targetNode = findNodeById(currentTreeData.value, targetId);
  if (targetNode) {
    // 2. 收集目标节点及其子孙节点的 ID
    const idsToDelete = collectSubtreeIds(targetNode);
    // 收集祖先节点 ID
    const ancestorIds = collectImportAncestorIds(importTreeData.value, targetId);
    // 3. 从 historyKeys 中剔除相关 ID
    historyKeys.value = historyKeys.value.filter(
      (key) => !new Set([...idsToDelete, ...ancestorIds]).has(key)
    );

    // 3. actionType = 'add'：向上删除父节点逻辑
    // 如果是新增节点
    if (whetherNew) {
      // 初始删除后的树
      let updatedTree = removeNodesByIdSet(currentTreeData.value, idsToDelete);
      // 从父节点开始
      let parent = findParentInCurrent(currentTreeData.value, targetId);
      while (parent) {
        // 在 updatedTree 中查找父节点
        const parentInUpdated = findNodeById(updatedTree, parent.id);
        if (!parentInUpdated) break;
        // 若父节点无子节点且 type==='add'
        if (
          (!parentInUpdated.children || parentInUpdated.children.length === 0) &&
          parent.whetherNew
        ) {
          // 将父节点加入删除集合
          idsToDelete.add(parent.id);
          // 从 updatedTree 中删除该父节点
          updatedTree = removeNodesByIdSet(updatedTree, new Set([parent.id]));
          // 向上继续
          parent = findParentInCurrent(currentTreeData.value, parent.id);
        } else {
          break;
        }
      }
    }
    let newTree;
    // 如果是替换节点
    if (whetherReplace) {
      // 替换节点
      newTree = replaceOrRemoveNode(currentTreeData.value, idsToDelete);
      currentTreeData.value = newTree;
      importTreeData.value = updateImportTreeData(importTreeData.value, targetId);
    } else {
      // 4. 从当前树中删除目标节点及其子树
      newTree = removeNodesByIdSet(currentTreeData.value, idsToDelete);
      currentTreeData.value = newTree;
    }
  }
};
/**
 * 收集指定节点及其子孙节点的所有 ID。
 * @param {Object} rootNode - 起始节点对象
 * @returns {Set<string|number>} 返回所有需删除节点的 ID 集合
 */
function collectSubtreeIds(rootNode) {
  const idSet = new Set();
  function dfs(node) {
    idSet.add(node.id);
    if (node.children) {
      node.children.forEach((child) => dfs(child));
    }
  }
  dfs(rootNode);
  return idSet;
}
function removeNodesByIdSet(nodes, idSet) {
  return nodes
    .filter((node) => !idSet.has(node.id))
    .map((node) => {
      if (node.children) {
        return {
          ...node,
          children: removeNodesByIdSet(node.children, idSet),
        };
      }
      return node;
    });
}
/**
 * 基于 importTreeData 查找指定子节点的父节点。
 */
function findParentInImport(nodes, childId) {
  for (const node of nodes) {
    if (node.children && node.children.some((child) => child.id === childId)) {
      return node;
    }
    if (node.children) {
      const found = findParentInImport(node.children, childId);
      if (found) return found;
    }
  }
  return null;
}
/**
 * 基于 importTreeData，收集指定节点所有祖先节点的 ID。
 */
 function collectImportAncestorIds(importTreeData, targetId) {
  const idSet = new Set();
  let currentId = targetId;
  while (true) {
    const parent = findParentInImport(importTreeData, currentId);
    if (!parent) break;
    idSet.add(parent.id);
    currentId = parent.id;
  }
  return idSet;
}
// 在 currentTreeData 中查找子节点的父节点
function findParentInCurrent(nodes, childId) {
  for (const node of nodes) {
    if (node.children && node.children.some((c) => c.id === childId)) return node;
    if (node.children) {
      const found = findParentInCurrent(node.children, childId);
      if (found) return found;
    }
  }
  return null;
}
/**
 * 在树中查找指定 ID 的节点对象。
 * @param {Array<Object>} nodes - 当前节点列表（可能含有嵌套 children）
 * @param {string|number} targetId - 要查找的节点 ID
 * @returns {Object|null} 找到则返回节点对象，否则返回 null
 */
function findNodeById(nodes, targetId) {
  for (const node of nodes) {
    if (node.id === targetId) {
      return node;
    }
    if (node.children) {
      const found = findNodeById(node.children, targetId);
      if (found) return found;
    }
  }
  return null;
}
//#region 添加
const onClickAdd = () => {
  console.log("点击添加", currentSelected, importCheckedKeys.value);
  if (currentSelected.node.levelType === 1) {
    //当前标段选中了工程项目
    //保留当前结构,添加到工程项目级别最后面
    currentTreeData.value = mergeSelectedTreeProject(
      currentTreeData.value,
      importTreeData.value,
      importCheckedKeys.value,
      currentSelected.node.id,
      historyKeys.value
    );
    onActionHistoryKeys(currentSelected.node,importCheckedKeys.value,importTreeData.value);
  } else if (currentSelected.node.levelType === 2) {
    //当前标段选中了单项
    currentTreeData.value = mergeSelectedTreeSingle(
      currentTreeData.value,
      importTreeData.value,
      importCheckedKeys.value,
      currentSelected.node.id,
      historyKeys.value
    );
    onActionHistoryKeys(currentSelected.node,importCheckedKeys.value,importTreeData.value);
  } else {
    //当前标段选中了单位
    currentTreeData.value = mergeSelectedTreeUnit(
      currentTreeData.value,
      importTreeData.value,
      importCheckedKeys.value,
      currentSelected.node.id,
      historyKeys.value
    );
    onActionHistoryKeys(currentSelected.node,importCheckedKeys.value,importTreeData.value);
  }
};
function mergeSelectedTreeProject(
  currentTreeData,
  importTreeData,
  importCheckedKeys,
  currentSelectedNodeId,
  historyKeys = []
) {
  // 1. 过滤 importTreeData，生成待插入节点（保留层级结构），忽略 historyKeys
  function filterTree(nodes) {
    return nodes.reduce((res, node) => {
      if (historyKeys.includes(node.id)) {
        // 如果节点已在 history 中，忽略其自身和子孙
        return res;
      }
      const children = Array.isArray(node.children) ? filterTree(node.children) : [];
      if (node.levelType === 1) {
        if (children.length) res.push(...children);
      } else if (importCheckedKeys.includes(node.id) || children.length) {
        res.push({ ...node, children });
      }
      return res;
    }, []);
  }

  // 2. 转义正则
  function escapeRegExp(str) {
    return str.replace(/[.*+?^${}()|[\\]\\]/g, "\\$&");
  }

  // 3. 唯一名称生成：优先填补最小可用后缀空位
  function generateUniqueName(base, existingNames) {
    const used = new Set();
    existingNames.forEach((n) => {
      if (n === base) used.add(0);
      else if (n.startsWith(base + "_")) {
        const num = parseInt(n.slice(base.length + 1), 10);
        if (!isNaN(num)) used.add(num);
      }
    });
    let i = 1;
    while (used.has(i)) i++;
    return `${base}_${i}`;
  }

  // 4. 标记节点及其子孙为新增
  function markAdded(node) {
    node.whetherNew = true;
    node.whetherReplace = false;
    if (Array.isArray(node.children)) {
      node.children.forEach((child) => markAdded(child));
    }
  }

  const selected = filterTree(importTreeData);
  const newTree = JSON.parse(JSON.stringify(currentTreeData));
  // 5. 合并节点工具：将 source 合并到 dest （同 id 合并children，否则添加并标记）
  function mergeNode(dest, source) {
    const destNames = dest.children.map((c) => c.name);
    source.children.forEach((srcChild) => {
      if (historyKeys.includes(srcChild.id)) {
        return; // 忽略已处理的
      }
      const exist = dest.children.find((c) => c.id === srcChild.id);
      if (exist) {
        mergeNode(exist, srcChild);
      } else {
        const clone = JSON.parse(JSON.stringify(srcChild));
        markAdded(clone);
        if (destNames.includes(clone.name)) {
          clone.name = generateUniqueName(clone.name, destNames);
        }
        destNames.push(clone.name);
        dest.children.push(clone);
      }
    });
  }

  // 6. 查找目标并插入/合并
  function findInto(nodes) {
    for (const node of nodes) {
      if (node.id === currentSelectedNodeId) {
        if (!Array.isArray(node.children)) node.children = [];
        const existingNames = node.children.map((c) => c.name);
        selected.forEach((src) => {
          if (historyKeys.includes(src.id)) return;
          const exist = node.children.find((c) => c.id === src.id);
          if (exist) {
            mergeNode(exist, src);
          } else {
            const clone = JSON.parse(JSON.stringify(src));
            markAdded(clone);
            if (existingNames.includes(clone.name))
              clone.name = generateUniqueName(clone.name, existingNames);
            existingNames.push(clone.name);
            // 如果右侧选中的是合同内则追加到合同内最后面,否则追加到最后
            if(src.originalFlag){
              let insertIndex=0
              node.children.forEach((item2,index2) => {
                // 如果循环到合同外则将索引值留下,后面将右侧单项存入到此索引位置
                if(!item2.originalFlag&&!insertIndex){
                  insertIndex=index2
                }
              })
              node.children.splice(insertIndex, 0, clone);
            }else{
              node.children.push(clone);
            }
          }
        });
        return true;
      }
      if (Array.isArray(node.children) && node.children.length) {
        if (findInto(node.children)) return true;
      }
    }
    return false;
  }

  const inserted = findInto(newTree);
  if (!inserted) {
    // 根层级合并逻辑
    const existingNames = newTree.map((n) => n.name);
    selected.forEach((src) => {
      if (historyKeys.includes(src.id)) return;
      const exist = newTree.find((n) => n.id === src.id);
      if (exist) {
        mergeNode(exist, src);
      } else {
        const clone = JSON.parse(JSON.stringify(src));
        markAdded(clone);
        if (existingNames.includes(clone.name))
          clone.name = generateUniqueName(clone.name, existingNames);
        existingNames.push(clone.name);
        newTree.push(clone);
      }
    });
  }
  return newTree;
}
function mergeSelectedTreeSingle(
  currentTreeData,
  importTreeData,
  importCheckedKeys,
  currentSelectedId,
  historyKeys = []
) {
  // 深拷贝 currentTreeData
  const newTreeData = JSON.parse(JSON.stringify(currentTreeData));

  // 构建 id->节点 映射 用于快速查找
  const idMap = {};
  (function buildMap(nodes) {
    for (const node of nodes) {
      idMap[node.id] = node;
      if (Array.isArray(node.children)) buildMap(node.children);
    }
  })(newTreeData);

  // 定位目标节点，并找到最终插入位置
  let target = idMap[currentSelectedId];
  if (!target) return newTreeData;
  const getLvl2 = (node) => (node.children || []).filter((c) => c.levelType === 2);
  let lvl2 = getLvl2(target);
  while (lvl2.length > 1) {
    target = lvl2[lvl2.length - 1];
    lvl2 = getLvl2(target);
  }
  if (lvl2.length === 1) target = lvl2[0];

  // 构建 importTree 映射
  const importMap = {};
  (function buildImportMap(nodes) {
    for (const node of nodes) {
      importMap[node.id] = node;
      if (Array.isArray(node.children)) buildImportMap(node.children);
    }
  })(importTreeData);

  // 构建 historySet，包含 historyKeys 及其所有后代
  const historySet = new Set();
  function collectDescendants(node) {
    historySet.add(node.id);
    if (Array.isArray(node.children)) {
      for (const child of node.children) collectDescendants(child);
    }
  }
  for (const hKey of historyKeys) {
    const hNode = importMap[hKey];
    if (hNode) collectDescendants(hNode);
  }

  // 筛选待导入节点：选中且 levelType=3 且不在 historySet
  const toImport = importCheckedKeys
    .map((key) => importMap[key])
    .filter((n) => n && n.levelType === 3 && !historySet.has(n.id)&&target.originalFlag===n.originalFlag&&(n?.type??-1)===(target?.type??-1));

  // 初始化目标 children 及现有 id/name 集合
  target.children = target.children || [];
  const idSet = new Set(target.children.map((c) => c.id));
  const nameSet = new Set(target.children.map((c) => c.name).filter((n) => n != null));

  // 辅助：生成唯一值
  function genUnique(base, set) {
    if (!set.has(base)) return base;
    let idx = 1,
      val;
    do {
      val = `${base}_${idx++}`;
    } while (set.has(val));
    return val;
  }

  // 辅助：递归标记 type
  function markAdded(node) {
    node.whetherNew = true;
    node.whetherReplace = false;
    if (Array.isArray(node.children)) {
      for (const child of node.children) markAdded(child);
    }
  }

  // 深拷贝并插入，同时更新集合并标记所有新增节点
  for (const node of toImport) {
    const clone = JSON.parse(JSON.stringify(node));
    clone.children = clone.children || [];
    markAdded(clone);

    // 唯一化 id
    clone.id = genUnique(String(clone.id), idSet);
    idSet.add(clone.id);

    // 唯一化 name
    if (clone.name != null) {
      clone.name = genUnique(String(clone.name), nameSet);
      nameSet.add(clone.name);
    }

    // 更新 parentId 为目标节点 id
    clone.parentId = target.id;

    target.children.push(clone);
  }

  return newTreeData;
}
function mergeSelectedTreeUnit(
  currentTreeData,
  importTreeData,
  importCheckedKeys,
  currentSelected,
  historyKeys = []
) {
  // 深拷贝 currentTreeData
  const newTreeData = JSON.parse(JSON.stringify(currentTreeData));

  // 构建 id->节点 映射 用于快速查找
  const idMap = {};
  (function buildMap(nodes) {
    for (const node of nodes) {
      idMap[node.id] = node;
      if (Array.isArray(node.children)) buildMap(node.children);
    }
  })(newTreeData);

  // 定位目标节点，并找到最终插入位置
  let target = idMap[currentSelected];
  if (!target) return newTreeData;

  // 构建 importTreeData 的 id -> 节点 映射
  const importMap = new Map();
  function buildImportMap(node) {
    importMap.set(node.id, node);
    if (Array.isArray(node.children)) {
      node.children.forEach(buildImportMap);
    }
  }
  importTreeData.forEach(buildImportMap);

  // 构建排除集合：historyKeys 及其所有子节点
  const excludedIds = new Set();
  function collectExcluded(node) {
    excludedIds.add(node.id);
    if (Array.isArray(node.children)) {
      node.children.forEach(collectExcluded);
    }
  }
  historyKeys.forEach((id) => {
    const node = importMap.get(id);
    if (node) collectExcluded(node);
  });

  // 筛选出 levelType=3 且在 importCheckedKeys 中，且不在 excludedIds 中的节点，并深拷贝清空 children，添加 type: 'add'
  const toImport = importCheckedKeys
    .map((id) => importMap.get(id))
    .filter((node) => node && node.levelType === 3 && !excludedIds.has(node.id)&&target.originalFlag===node.originalFlag&&(node?.type??-1)===(target?.type??-1))
    .map((node) => ({ ...node, children: [], whetherNew: true, whetherReplace:false }));
  if (!toImport.length) return currentTreeData;

  // 在 currentTreeData 中找到 currentSelected 节点及其父节点和索引
  let parent = null;
  let siblings = currentTreeData;
  let selectedIndex = -1;

  function dfs(nodes, parentNode) {
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      if (node.id === currentSelected) {
        parent = parentNode;
        siblings = nodes;
        selectedIndex = i;
        return true;
      }
      if (Array.isArray(node.children) && node.children.length) {
        if (dfs(node.children, node)) return true;
      }
    }
    return false;
  }
  dfs(currentTreeData, null);

  if (selectedIndex === -1) {
    throw new Error(`Cannot find node with id ${currentSelected} in currentTreeData`);
  }

  // 插入到与 selected 同级的位置：在 selected 之后
  const parentId = parent ? parent.id : null;

  // 获取现有同级节点的名字集合
  const existingNames = new Set(siblings.map((node) => node.name));

  function getUniqueName(baseName) {
    if (!existingNames.has(baseName)) {
      existingNames.add(baseName);
      return baseName;
    }
    let index = 1;
    while (existingNames.has(`${baseName}_${index}`)) {
      index++;
    }
    const newName = `${baseName}_${index}`;
    existingNames.add(newName);
    return newName;
  }

  toImport.forEach((node) => {
    node.parentId = parentId;
    node.name = getUniqueName(node.name);
  });

  siblings.splice(selectedIndex + 1, 0, ...toImport);

  return currentTreeData;
}
const onActionHistoryKeys = (currentSelectedNode,importCheckedKeys,importTreeData) => {
  let newHistoryKeys = JSON.parse(JSON.stringify(historyKeys.value));
  
  if(currentSelectedNode.levelType==1){
    //如果是最顶级就不用判断哪些是否真的进行了添加操作
    importCheckedKeys.forEach((item) => {
      if (historyKeys.value.findIndex((ele) => ele === item) === -1) {
        newHistoryKeys.push(item);
      }
    });
  }
  else{
    // 深拷贝 currentTreeData
    const newTreeData = JSON.parse(JSON.stringify(importTreeData));

    // 构建 id->节点 映射 用于快速查找
    const idMap = {};
    (function buildMap(nodes) {
      for (const node of nodes) {
        idMap[node.id] = node;
        if (Array.isArray(node.children)) buildMap(node.children);
      }
    })(newTreeData);
   
    importCheckedKeys.forEach((item) => {
      // 定位目标节点
      let target = idMap[item];
      if (target &&
        historyKeys.value.findIndex((ele) => ele === item) === -1 &&
        target.originalFlag===currentSelectedNode.originalFlag && //合同内合同外一致才会新增
        (currentSelectedNode?.type??-1)===(target?.type??-1)  //标签一致才会新增
      ) {
        newHistoryKeys.push(item);
      }
      
    });
  }
  historyKeys.value = newHistoryKeys;
};
const importTreeDataShow = computed(() => {
  const filterTree = (nodes, parentVisible = true) => {
    if (!nodes) return [];

    return nodes
      .map((node) => {
        const isVisible = parentVisible && node.show !== false; // 父可见且自己没有show: false
        if (!isVisible) {
          return null; // 当前节点不可见，整支不展示
        }
        return {
          ...node,
          children: filterTree(node.children, isVisible),
        };
      })
      .filter((node) => node !== null);
  };
  return filterTree(importTreeData.value);
});
// 替换
const onClickReplace = () => {
  const newSelectData = getNewLevel3Nodes(
    importTreeData.value,
    importCheckedKeys.value,
    historyKeys.value
  );
  if (newSelectData.length > 0) {
    const { currentTreeData: newCurrent, importTreeData: newImport } = mergeSelectedNode(
      JSON.parse(JSON.stringify(currentTreeData.value)),
      JSON.parse(JSON.stringify(importTreeData.value)),
      currentSelected.node.id,
      newSelectData[0]
    );
    currentTreeData.value = newCurrent;
    importTreeData.value = newImport;
  }
  onActionHistoryKeys();
};
function getNewLevel3Nodes(treeData, checkedKeys, historyKeys) {
  // 将历史选中和新选中转换为 Set 方便查找
  const historySet = new Set(historyKeys);
  const checkedSet = new Set(checkedKeys);

  // 扁平化所有节点，以 id 为键，便于 O(1) 查找
 const nodeMap = {};
  (function buildMap(nodes) {
    for (const node of nodes) {
      nodeMap[node.id] = node;
      if (node.children && node.children.length) {
        buildMap(node.children);
      }
    }
  })(treeData);

  const result = new Set();

  // 递归收集子节点中 levelType = 3 且不在历史选中中的节点
  function collectLevel3(node) {
    if (node.levelType === 3) {
      // 仅当该节点不在历史选中中时，才加入结果
      if (!historySet.has(node.id) && node.show !== false) {
        result.add(node.id);
      }
    } else if (node.children && node.children.length) {
      for (const child of node.children) {
        collectLevel3(child);
      }
    }
  }

  // 遍历当前已选中，但历史中不存在的 key
  for (const key of checkedSet) {
    if (!historySet.has(key)) {
      const node = nodeMap[key];
      if (node) {
        // 如果找到了节点：收集其子孙中所有符合条件的 levelType=3 节点
        collectLevel3(node);
      } else {
        // 节点数据不存在（父级未展开加载子节点）：默认当作 levelType=3 处理，直接添加
        result.add(key);
      }
    }
  }

  // 返回去重后的数组
  return Array.from(result);
}
function mergeSelectedNode(
  currentTreeData,
  importTreeData,
  currentSelectedId,
  importSelectedId
) {
  /**
   * 在树中查找指定 ID 的节点，并返回节点引用、所在父数组及索引
   * @param {Array<Object>} tree - 要查找的树数组
   * @param {string|number} targetId - 目标节点 ID
   * @param {Array<Object>} parentArr - 父级数组（内部调用时使用）
   * @returns {{ node: Object, parentArr: Array<Object>, index: number }|null}
   */
  function findNode(tree, targetId, parentArr = null) {
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.id === targetId) {
        return { node, parentArr: parentArr || tree, index: i };
      }
      if (node.children) {
        const found = findNode(node.children, targetId, node.children);
        if (found) return found;
      }
    }
    return null;
  }

  const currFound = findNode(currentTreeData, currentSelectedId);
  const importFound = findNode(importTreeData, importSelectedId);

  if (!currFound)
    throw new Error(`未找到 currentTreeData 中 ID 为 ${currentSelectedId} 的节点`);
  if (!importFound)
    throw new Error(`未找到 importTreeData 中 ID 为 ${importSelectedId} 的节点`);

  const { node: currNode } = currFound;
  const { node: impNode, parentArr: impArr, index: impIndex } = importFound;
  // 保存原节点数据以便还原
  // 浅拷贝原节点所有属性，包括 children
  currNode.replace = {
    ...currNode,
    children: currNode?.children ? [...currNode?.children] : undefined,
  };
  const { name: _impName, ...impRest } = impNode;
  Object.assign(currNode, impRest);
  // 标记为替换类型
  currNode.whetherNew = false;
  currNode.whetherReplace = true;

  // 在 importTreeData 中标记该节点不显示
  impNode.show = false;

  return { currentTreeData, importTreeData };
}
function updateImportTreeData(nodes, targetId) {
  return nodes.map((node) => {
    if (node.id === targetId) {
      return removeShowField(node);
    }
    if (node.children) {
      return { ...node, children: updateImportTreeData(node.children, targetId) };
    }
    return node;
  });
}
/**
 * 移除节点的 show 字段（递归处理子节点）。
 */
function removeShowField(node) {
  const newNode = { ...node };
  delete newNode.show;
  if (newNode.children) {
    newNode.children = newNode.children.map((child) => removeShowField(child));
  }
  return newNode;
}
/**
 * 替换节点，如果存在 replace 字段则用 replace 替换，否则正常删除。
 */
 function replaceOrRemoveNode(nodes, idSet) {
  console.log(JSON.parse(JSON.stringify(idSet)));
  return nodes
    .map((node) => {
      if (idSet.has(node.id)) {
        if (node.replace) {
          if (currentSelected.node.id === node.id) {
            currentSelected.node = node.replace;
          }
          return {
            ...node.replace,
          };
        }
        return null;
      }
      if (node.children) {
        return {
          ...node,
          children: replaceOrRemoveNode(node.children, idSet).filter(Boolean),
        };
      }
      return node;
    })
    .filter(Boolean);
}
// 匹配合并
const onClickMerge = () => {
  const { newTree, addedIds, replaceIds } = mergeMatch(
    currentTreeData.value,
    importTreeData.value,
    importCheckedKeys.value
  );
  if(addedIds.length==0&&replaceIds.length==0){
    return false;
  }
  currentTreeData.value = newTree;
  let newHistoryKeys = JSON.parse(JSON.stringify(historyKeys.value));
  addedIds.forEach((item) => {
    if (newHistoryKeys.findIndex((ele) => ele === item) === -1) {
      newHistoryKeys.push(item);
    }
  });
  replaceIds.forEach((item) => {
    if (newHistoryKeys.findIndex((ele) => ele === item) === -1) {
      newHistoryKeys.push(item);
    }
  });
  historyKeys.value = newHistoryKeys;
  // let leftSelData={
  //   ...currentTreeData.value[0],
  //   dataRef:{
  //     ...currentTreeData.value[0]
  //   }
  // }
  // currentSelect([leftSelData.id],{node:leftSelData})
};
//#region 匹配合并
/*
 * 将 importTreeData 中选中的 levelType=3 节点合并并追加到 currentTreeData 中。
 * @param {Array<Object>} currentTreeData - 当前的树形数据数组。
 * @param {Array<Object>} importTreeData - 导入的树形数据数组。
 * @param {Array<string|number>} importCheckedKeys - importTreeData 中选中节点的 ID 数组。
 * @returns {Array<Object>} 返回合并后的新树数据数组。
 */
 function mergeMatch(currentTreeData, importTreeData, importCheckedKeys) {
  // 深拷贝 currentTreeData，避免直接修改原数据
  const newTree = JSON.parse(JSON.stringify(currentTreeData));
  const checkedSet = new Set(importCheckedKeys);
  const selectedLeaves = [];
  const addedIds = [];
  const replaceIds = [];

  // 递归遍历 importTreeData，根据选中状态收集 levelType=3 且 type 合规的节点
  function traverseImport(node, parentSelected) {
    const isSelected = parentSelected || checkedSet.has(node.id);
    if (
      node.levelType === 3 &&
      isSelected &&
      node.type !== "add" &&
      node.type !== "replace"
    ) {
      selectedLeaves.push(node);
    }
    if (Array.isArray(node.children)) {
      node.children.forEach((child) => traverseImport(child, isSelected));
    }
  }
  importTreeData.forEach((root) => traverseImport(root, false));

  // 对每个选中的 leaf，在 newTree 中精准匹配对应路径并追加
  selectedLeaves.forEach((leaf) => {
    // 找到 leaf 在 importTreeData 中的完整路径
    const path = [];
    let found = false;
    function dfs(node, acc) {
      if (found) return;
      const newAcc = acc.concat(node);
      if (node.id === leaf.id) {
        path.push(...newAcc);
        found = true;
        return;
      }
      if (Array.isArray(node.children)) {
        node.children.forEach((child) => dfs(child, newAcc));
      }
    }
    importTreeData.forEach((root) => dfs(root, []));
    // 筛选出除 levelType=1 之外的父级节点
    const parentsList = path.filter((n) => n.levelType !== 1 && n.id !== leaf.id);
    if (parentsList.length === 0) return;

    // 在 newTree 中查找匹配整个 parentsList 路径的节点列表
    const candidateParents = [];
    function matchNode(node, idx) {
      const target = parentsList[idx];
      if (node.levelType === target.levelType && node.name === target.name) {
        if (idx === parentsList.length - 1) {
          candidateParents.push(node);
        } else if (Array.isArray(node.children)) {
          node.children.forEach((child) => matchNode(child, idx + 1));
        }
      }
    }
    newTree.forEach((root) => {
      if (Array.isArray(root.children)) {
        root.children.forEach((child) => matchNode(child, 0));
      }
    });
    let leftSelData={
      ...currentTreeData[0],
      dataRef:{
        ...currentTreeData[0]
      }
    }
    // currentSelect([leftSelData.id],{node:leftSelData})
    console.info(232423423423,candidateParents)
    // 如果没有匹配到父级则带着单项直接走添加逻辑
    if(candidateParents.length==0){
      onClickAdd()
    }
    // 对每个匹配到的父节点，检查并追加 leaf
    candidateParents.forEach((parent) => {
      const exists =
        Array.isArray(parent.children) &&
        parent.children.some(
          (child) => child.levelType === leaf.levelType && child.name === leaf.name
        );
      if (!exists) {
        const newLeaf = {
          ...leaf,
          parentId: parent.id,
          whetherNew: true,
          whetherReplace: false,
          children: [],
        };
        // 新增节点
        parent.children = parent.children || [];
        parent.children.push(newLeaf);
        addedIds.push(newLeaf.id);
      }else{
        const newLeaf = {
          ...leaf,
          parentId: parent.id,
          whetherNew: false,
          whetherReplace: true,
          children: [],
        };
        //替换节点
        let index=parent.children.findIndex((a)=>a.name===newLeaf.name)
        let oldObj=JSON.parse(JSON.stringify(parent.children[index]))
        newLeaf.replace = {
          ...oldObj,
          children: [],
        };
        parent.children[index]=newLeaf
        replaceIds.push(newLeaf.id)
      }
    });
  });

  return { newTree, addedIds, replaceIds };
}





const compareSameLevel = (impList, importItem) => {
  //比较两个列表层级是否相同
  let leveLimit = true; //导入与当前选中层级相同
  let targetChildList = menuOperator.getChildList(currentSelected.node, []);
  //单项层级是否相同
  if (
    !targetChildList.find((a) => a.levelType === 3) &&
    targetChildList.find((a) => a.name === impList[0].name)
  ) {
    importItem = impList[0];
  }
  let curSameList = targetChildList.filter(
    (cur) =>
      cur.name === importItem.name &&
      cur.originalFlag === importItem.originalFlag &&
      cur.levelType === importItem.levelType
  );
  if (curSameList.length === 0) {
    return false;
  } else {
    leveLimit = curSameList.some((cur) => {
      let curParentObj = getParentId(currentTreeData.value, cur.id);
      curParentObj = curParentObj.filter((d) => [2].includes(d.levelType));
      if (curParentObj.length === impList.length) {
        console.log("impList", impList);
        let flag = curParentObj.every(
          (a, idx) =>
            a.name === impList[idx].name && a.levelType === impList[idx].levelType
        );
        if (flag) return true;
      }
      return false;
    });
    return leveLimit;
  }
};
const compareLevel = (curList, impList) => {
  //比较两个列表层级是否相同
  if (impList.length !== curList.length) {
    return false;
  } else {
    impList.map((item, idx) => {
      if (curList[idx].name !== item.name) {
        return false;
      }
    });
  }
  return true;
};
/**
 * 处理当前项目的打开节点
 * @param {*} tree
 * @param {*} isAdd
 */
const handleExpandedKeys = (tree, isAdd = true) => {
  for (let i = 0; i < tree.length; i++) {
    if ([2].includes(tree[i].levelType) && !expandedKeys.value.includes(tree[i].id)) {
      expandedKeys.value.push(tree[i].id);
    }
  }
};

// 添加\数据
const appendTree = (tree,type) => {
  const target = currentTreeTile.find((i) => i.id === expandedKeys.value);
  // 插入数据
  currentTreeData.value = appendNodeInTree(target, currentTreeData.value, tree,type);
};

const delListId = ref([]); // 左侧点击删除的数据
const delParentIdList = ref([]);
const delImportItem = async (id, name, parentId, levelType) => {
  // 左侧的删除数据
  let parent = null;
  if (levelType === 3) {
    //删除单位的话连带父级一起删除
    parent = currentTreeTile.find(
      (a) => a.id === parentId && (a.whetherNew || a.whetherReplace)
    );
  }
  // debugger;
  if (parent) {
    await removeNodeInTree(
      currentTreeData.value,
      parent.id,
      parent.name,
      parent.parentId,
      parent.levelType
    );
  } else {
    await removeNodeInTree(currentTreeData.value, id, name, parentId, levelType);
  }
  console.log(delListId.value);
  // handleleftTree();
  console.log(
    "importSelected.historyKeys",
    importSelected.historyKeys,
    importCheckedKeys.value,
    delListId.value
  );
  let patentIdList = [];
  delListId.value.map((i) => {
    let list = getParentId(importTreeData.value, i);
    console.log(i, importTreeTile);
    list &&
      list.map((a) => {
        if (!patentIdList.includes(a.id)) {
          patentIdList.push(a.id);
        }
      });
  });
  delParentIdList.value = patentIdList;
  console.log("patentIdList", patentIdList);
  importSelected.historyKeys = toRaw(importSelected.historyKeys).filter((i) => {
    return !patentIdList.includes(i);
  });

  importCheckedKeys.value = toRaw(importSelected.historyKeys).filter((i) => {
    return !patentIdList.includes(i);
  });
  currentTreeTile = xeUtils.toTreeArray(currentTreeData.value);
  // 解除禁用操作
  recursionTree(importTreeData.value, "delDisable");
};

// 导入的数据，将左侧数据禁用
const disableItem = async () => {
  // 存储历史上一步操作的数据
  importSelected.historyKeys = xeUtils.clone(importCheckedKeys.value, true);

  recursionTree(importTreeData.value, "addDisable");
};

// 在树中查找匹配的元素
const findElement = (key, nodes) => {
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];
    if (node.id === key) {
      const list = xeUtils.clone(node, true);
      delete list.children;
      return list;
    }
    if (node.children) {
      const result = findElement(key, node.children);
      if (result) {
        return result;
      }
    }
  }
  return null;
};

// 扁平化树结构
const flattenTree = (tree, isLog = false, parent = null, result = []) => {
  for (const node of tree) {
    const { children, ...data } = node;
    result.push({ ...data });
    if (!node.disabled && isLog && !node.initDisable) {
      importSelected.handleKeys.push(node.id);
    }
    if (children && children.length > 0) {
      flattenTree(children, isLog, node.parentId, result);
    }
  }
  return result;
};
// 树结构替换子数据
const replaceChildrenInTreeFun = (treeData, parentId, newChildren) => {
  for (let i = 0; i < treeData.length; i++) {
    const node = treeData[i];
    if (node.id === parentId && node.levelType !== 3) {
      node.children = newChildren;
      return;
    }
    if (Array.isArray(node.children)) {
      replaceChildrenInTreeFun(node.children, parentId, newChildren);
    }
  }
};
// 遍历树,将单项id增加唯一值,子级parentId增加唯一值
function treeParentIdHandle(node, num) {
  node.id += num;
  if (node.children && node.children.length > 0) {
    node.children.forEach(child => {
      child.parentId += num;
    });
  }
}
function processNode(node,unitObj) {
  node.whetherNew = true;
  node.whetherReplace = false;
  if (node.children && node.children.length > 0 && node.children[0].levelType!=3) {
    node.children.forEach(child => {
      processNode(child,unitObj);
    });
  }else{
    node.children.push(unitObj)
  }
}
// 树结构插入数据
const appendInTreeFun = (tar, tree, obj) => {
  tree.forEach((ele) => {
    // 如果当前树id与选中节点id一致
    if (ele.id === tar.id) {
      // 如果此节点有子项
      if (ele.children) {
        obj.checkName = obj.name;
        //判断名称是否重复  重复+-1
        const newObj = handleName(ele.children, obj);
        // 如果左侧树选择的是工程项目
        if(tar.levelType==1){
          // 如果右侧选中的是合同内则追加到合同内最后面,否则追加到最后
          if(obj.originalFlag){
            let insertIndex=0
            ele.children.forEach((item,index) => {
              // 如果循环到合同外则将索引值留下,后面将右侧单项存入到此索引位置
              if(!item.originalFlag&&!insertIndex){
                insertIndex=index
              }
            })
            treeParentIdHandle(newObj,insertIndex)
            ele.children.splice(insertIndex, 0, newObj);
          }else{
            treeParentIdHandle(newObj,ele.children.length)
            ele.children = [...ele.children, newObj];
          }
        }
        // debugger;
        // ele.children = [...ele.children, newObj];
      } else {
        ele.children = [obj];
      }
    } else if (ele.children) {
      appendInTreeFun(tar, ele.children, obj);
    }
  });
  return tree;
};
const getTHNewChild = (parent, obj, target) => {
  //替换操作获取新的子级
  let children = [...parent.children];
  let newChildren = [];
  children.map((a) => {
    if (a.id === target.id) {
      a = { ...a, ...obj };
    }
    newChildren.push(a);
  });
  return newChildren;
};
const getNewTHObj = (obj, oldObj) => {
  let newObj = { ...oldObj, ...obj }; //获取新的对象
  //替换
  newObj.whetherReplace = true;
  newObj.whetherNew = false;
  newObj.oldId = oldObj.id;
  return newObj;
};
const getNewZJObj = (obj, parent) => {
  let newObj = { ...obj }; //获取新的对象
  //追加
  newObj.whetherReplace = false;
  newObj.whetherNew = true;
  newObj.parentId = parent.id;
  return newObj;
};
const appendFun = (targetParent, obj, queryParent, target, tree) => {
  //追加操作中的一种条件
  // targetParent--左侧树选中最外层父级单项  obj--导入项  queryParent--导入项直接父级单项
  let targetList = menuOperator.getChildList(targetParent, []);
  const currentHasSingle = targetList.find(
    (a) => (a.levelType == 2 && a.id !== targetParent.id) || a.levelType == 3
  ); //查找有无子单项或单位工程
  const currentHasNameSingle = targetList.find(
    (a) => a.levelType == 2 && a.name === queryParent.name && a.id !== targetParent.id
  ); //查找有无同名子单项
  obj.whetherReplace = false;
  obj.whetherNew = true;
  if (currentHasNameSingle) {
    //有同名单项追加至同名单项下
    let tar = menuOperator.getChildSingle(currentHasNameSingle);
    let newObj = { ...obj };
    newObj.parentId = tar.id;
    appendInTreeFun(tar, tree, newObj);
  } else if (currentHasSingle && !currentHasNameSingle) {
    //无同名子单项追加至第一个最里面
    let currentUnitParent;
    if (currentHasSingle.levelType === 3) {
      currentUnitParent = targetList.find((a) => a.id === currentHasSingle.parentId);
      let newObj = { ...obj };
      newObj.parentId = currentUnitParent.id;
      appendInTreeFun(currentUnitParent, tree, newObj);
    } else {
      let tar = menuOperator.getChildSingle(currentHasSingle); //最里层单项
      let newObj = { ...obj };
      newObj.parentId = tar.id;
      appendInTreeFun(tar, tree, newObj);
    }
  } else if (!currentHasSingle) {
    //当前目标下无子单项--连带导入目标的单项父级一起追加过来
    //替换的话只追加单位
    let newObj = { ...obj };
    newObj.whetherNew = true;
    newObj.whetherReplace = false;
    if (radioAorT.value === "2") {
      newObj.parentId = target.id;
      appendInTreeFun(target, tree, newObj);
    } else {
      let newChildrenItem = { ...queryParent };
      newChildrenItem.children = [obj];
      newChildrenItem.whetherNew = true;
      newChildrenItem.whetherReplace = false;
      newChildrenItem.parentId = target.id;
      appendInTreeFun(target, tree, newChildrenItem);
    }
  }
};
// 处理左侧点击工程项目级别导入逻辑
const handleProjectExprot = async (tree,unitelNodes,target) => {
  let exportArr=[]
  unitelNodes.forEach((obj)=>{
    let list = getParentId(importTreeData.value, obj.id); //第一项当前选中项，最后一项为最外层子级单项
    let parentObj = JSON.parse(JSON.stringify(list.filter((i) => i.levelType !== 1))); //过滤工程项目级别
    // 循环单项工程,将每一级的单项工程子级都置空
    for(let i in parentObj){
      parentObj[i].children=[]
    }
    let treeParent=xeUtils.toArrayTree(parentObj, { key: "id" })
    let isHavaSingle=exportArr.filter((a)=>a.id==treeParent[0].id)
    // 如果已经存在此单项则将单位工程增加到这个单项下面否则增加此单项
    if(isHavaSingle.length!==0){

    }else{
      exportArr.push(treeParent[0])
      appendInTreeFun(target, tree, treeParent[0]);
    }
  })
  // return tree
}
// 树结构插入数据
const appendNodeInTree = (target, tree, obj, type) => {
  let id = target.id;
  // let targetChildList = menuOperator.getChildList(target, []); //当前选中项的所有子级
  let targetParent = currentSelected.parentObj[currentSelected.parentObj.length - 1]; //左侧树选中最外层单项父级
  tree.forEach(async (ele) => {
    for (let i in obj) {
      // 是否匹配到单项工程名称相同的数据
      let noEqual = true;
      let currentIsSame = false;
      let currentSingleIsSame = false;
      // 循环左侧树结构
      // 获取从当前点击的数据一直获取到最顶级树结构
      let list = getParentId(importTreeData.value, obj[i].id); //第一项当前选中项，最后一项为最外层子级单项
      let parentObj = list.filter((i) => i.levelType !== 1 && i.levelType !== 3); //过滤工程项目级别
      
      // 单项工程级别对象
      let queryParent = JSON.parse(JSON.stringify(parentObj[parentObj.length-1]));
      processNode(queryParent,obj[i])
      // 如果是添加
      if(type=='add'){
        // 如果左侧点击的是工程项目级别,则右侧合同内/外直接带上单项增加到左侧合同内/外最下面
        if(target.levelType==1){
          let newChildrenItem = { ...queryParent };
          newChildrenItem.parentId = target.id;
          appendInTreeFun(target, tree, newChildrenItem);
        }
      }



      // 如果为合同内单项或者合同外选择替换模式则进行替换操作
      // if (obj[i].originalFlag || radioAorT.value === "2") {
      //   if (
      //     (!queryParent.originalFlag && target.type === queryParent.type) ||
      //     queryParent.originalFlag
      //   ) {
      //     // 匹配两方单项工程名称是否相同--合同内单项名称相同  合同外标签+名称相同（此外还需要考虑层级）
      //     // currentIsSame = compareLevel(currentSelected.parentObj, parentObj);
      //     currentIsSame = compareSameLevel(parentObj, obj[i]);
      //     //当前选中项与目标项单项工程标签与名称及层级是否一致
      //     if (!currentIsSame) {
      //       currentSingleIsSame =
      //         targetParent.name === parentObj[parentObj.length - 1].name &&
      //         targetParent.type === parentObj[parentObj.length - 1].type; //当前选中项最外层单项与目标项最外层单项工程标签与名称是否一致
      //     }
      //     if (currentIsSame || currentSingleIsSame || target.type === queryParent.type)
      //       noEqual = false;
      //   }
      //   if (currentIsSame) {
      //     //当前选中项单项与导入项单项标签名称同层级相同
      //     //合同外有同名单位替换  无的话追加
      //     console.log("targetChildList", targetChildList, obj[i]);
      //     const flag = targetChildList.find(
      //       (a) =>
      //         a.name === obj[i].name &&
      //         a.levelType === obj[i].levelType &&
      //         !a.whetherNew &&
      //         !a.whetherReplace
      //     );
      //     if (flag) {
      //       //找到的话进行替换
      //       let newObj = getNewTHObj(obj[i], flag);
      //       let flagParent = currentTreeTile.find((a) => a.id === flag.parentId);
      //       let newChildren = getTHNewChild(flagParent, newObj, flag);
      //       newChildren.map((a) => (a.parentId = target.id));
      //       replaceChildrenInTreeFun(tree, flagParent.id, newChildren);
      //       console.log(tree, target.id, newChildren);
      //     } else {
      //       //找不到的话进行追加--只追加单位
      //       let newObj = getNewZJObj(obj[i], target);
      //       appendInTreeFun(target, tree, newObj);
      //     }
      //   } else if (!currentIsSame && currentSingleIsSame) {
      //     //替换需要保证单位名称一致，增加至最子级单项中优先追加至同名子单项中，否则就是最里面子单项
      //     let targetList = menuOperator.getChildList(targetParent, []);
      //     const hasSameNameUnit = targetList.find(
      //       (a) =>
      //         a.name === obj[i].name &&
      //         a.levelType === obj[i].levelType &&
      //         !a.whetherReplace &&
      //         !a.whetherNew
      //     ); //判断是否有同名单位
      //     if (hasSameNameUnit) {
      //       //有同名单位执行替换操作
      //       let parentTar = currentTreeTile.find(
      //         (a) => a.id === hasSameNameUnit.parentId
      //       );
      //       let newObj = getNewTHObj(obj[i], hasSameNameUnit);
      //       let newChildren = getTHNewChild(parentTar, newObj, hasSameNameUnit);
      //       newChildren.map((a) => (a.parentId = target.id));
      //       replaceChildrenInTreeFun(tree, parentTar.id, newChildren);
      //     } else {
      //       //无同名单项最子级单项执行追加操作
      //       //当前文件下是否有子级单位  有的话只追加单位工程无得话连带导入文件的单项一起追加
      //       appendFun(targetParent, obj[i], queryParent, target, tree);
      //     }
      //   } else if (target.type === queryParent.type) {
      //     //只有标签相同的话  不管如何均执行追加操作---同上面相同
      //     //无同名子单项执行追加单位
      //     //当前文件下是否有子级单位  有的话只追加单位工程无得话连带导入文件的单项一起追加
      //     appendFun(targetParent, obj[i], queryParent, target, tree);
      //   }
      //   if (noEqual) {
      //     const brother = impostMoveNodes.value.find(
      //       (a) => a.parentId === obj[i].parentId && a.id !== obj[i].id
      //     ); //有同父级单位已经被移动过去
      //     const hasMove = brother
      //       ? currentTreeTile.find((a) => a.id === brother.id)
      //       : null;
      //     if (hasMove) {
      //       // 移动新增到brother同级
      //       const brotherParent = currentTreeTile.find((a) => a.id === brother.parentId);
      //       let newObj = getNewZJObj(obj[i], brotherParent);
      //       appendInTreeFun(brotherParent, tree, newObj);
      //     } else {
      //       let tar = currentTreeTile[0];
      //       let newChildrenItem = { ...queryParent };
      //       let newObj = obj[i];
      //       newObj.whetherNew = true;
      //       newObj.whetherReplace = false;
      //       newChildrenItem.children = [newObj];
      //       newChildrenItem.whetherNew = true;
      //       newChildrenItem.whetherReplace = false;
      //       newChildrenItem.parentId = tar.id;
      //       appendInTreeFun(tar, tree, newChildrenItem);
      //     }
      //   }
      //   // 追加操作
      // } else {
      //   //合同外追加
      //   if (
      //     (!queryParent.originalFlag && target.type === queryParent.type) ||
      //     queryParent.originalFlag
      //   ) {
      //     // 匹配两方单项工程名称是否相同--合同内单项名称相同  合同外标签+名称相同（此外还需要考虑层级）
      //     // currentIsSame = compareLevel(currentSelected.parentObj, parentObj);
      //     currentIsSame = compareSameLevel(parentObj, obj[i]);
      //     //当前选中项与目标项单项工程标签与名称及层级是否一致
      //     if (!currentIsSame) {
      //       console.log("targetParent", targetParent, parentObj[parentObj.length - 1]);
      //       currentSingleIsSame =
      //         targetParent.name === parentObj[parentObj.length - 1].name &&
      //         targetParent.type === parentObj[parentObj.length - 1].type; //当前选中项最外层单项与目标项最外层单项工程标签与名称是否一致
      //     }
      //     if (currentIsSame || currentSingleIsSame || target.type === queryParent.type)
      //       noEqual = false;
      //   }
      //   if (currentIsSame) {
      //     //当前选中项单项与导入项单项标签名称同层级相同
      //     //合同外有同名单位替换  无的话追加
      //     //只追加单位
      //     let newObj = { ...obj[i] };
      //     newObj.whetherNew = true;
      //     newObj.whetherReplace = true;
      //     newObj.parentId = target.id;
      //     appendInTreeFun(target, tree, newObj);
      //   } else if (!currentIsSame && currentSingleIsSame) {
      //     //无同名单项最子级单项执行追加操作
      //     //当前文件下是否有子级单位  有的话只追加单位工程无得话连带导入文件的单项一起追加
      //     appendFun(targetParent, obj[i], queryParent, target, tree);
      //   } else if (target.type === queryParent.type) {
      //     //只有标签相同的话  不管如何均执行追加操作---同上面相同
      //     //无同名子单项执行追加单位
      //     //当前文件下是否有子级单位  有的话只追加单位工程无得话连带导入文件的单项一起追加
      //     appendFun(targetParent, obj[i], queryParent, target, tree);
      //   }
      //   if (noEqual) {
      //     const brother = impostMoveNodes.value.find(
      //       (a) => a.parentId === obj[i].parentId && a.id !== obj[i].id
      //     ); //有同父级单位已经被移动过去
      //     const hasMove = brother
      //       ? currentTreeTile.find((a) => a.id === brother.id)
      //       : null;
      //     if (hasMove) {
      //       // 移动新增到brother同级
      //       const brotherParent = currentTreeTile.find((a) => a.id === brother.parentId);
      //       let newObj = getNewZJObj(obj[i], brotherParent);
      //       appendInTreeFun(brotherParent, tree, newObj);
      //     } else {
      //       let tar = currentTreeTile[0];
      //       let newChildrenItem = { ...queryParent };
      //       let newObj = obj[i];
      //       newObj.whetherNew = true;
      //       newObj.whetherReplace = false;
      //       newChildrenItem.children = [newObj];
      //       newChildrenItem.whetherNew = true;
      //       newChildrenItem.whetherReplace = false;
      //       newChildrenItem.parentId = tar.id;
      //       appendInTreeFun(tar, tree, newChildrenItem);
      //     }
      //   }
      // }
      currentTreeTile = xeUtils.toTreeArray(tree);
    }
  });
  return tree;
};

// 处理单位工程名称是否有相同如果有则自增一
const handleName = (initList, current) => {
  //initList---同级列表数据   current--增加的名称数据
  function getNameCount(namesMap, name) {
    return (namesMap.get(name) || 0) + 1;
  }

  function setName(item) {
    let NAME = "";
    let count = 0;
    let nameList = [];
    for (let [k, v] of namesMap) {
      nameList.push(k.trim());
    }

    for (let [index, name] of nameList.entries()) {
      let lastName = index > 0 ? `${item.name}_${index + 1}` : `${item.name}`;
      let currentName = index > 0 ? `${item.name}_${index}` : `${item.name}`;
      if (!nameList.includes(lastName.trim()) && nameList.includes(currentName.trim())) {
        NAME = lastName.trim();
        namesMap.set(lastName.trim(), 0);
        break;
      } else if (
        nameList.includes(lastName.trim()) &&
        !nameList.includes(currentName.trim())
      ) {
        NAME = currentName.trim();
        namesMap.set(currentName.trim(), 0);
        break;
      }
    }

    if (namesMap.has(item.checkName)) {
      NAME = NAME || `${item.name}_${nameList.length}`;
    } else {
      NAME = item.name;
    }

    return NAME;
  }

  // 初始化一个映射存储每个名称出现的次数
  let namesMap = new Map();

  initList.forEach((item) => {
    const count = getNameCount(namesMap, item.name);
    namesMap.set(item.name, count);
  });
  const currentList = [current];
  const renamedItems = currentList.reduce((acc, item, index) => {
    const count = getNameCount(namesMap, item.checkName);

    const newName = count > 1 ? `${item.checkName}_${count - 1}` : `${item.checkName}`;

    let newItem = { ...item, name: newName, num: count };
    if (namesMap.has(newName)) {
      const handleName = setName({ ...item });
      newItem = { ...item, name: handleName, num: count };
    }

    namesMap.set(item.checkName, count);
    acc.push(newItem);

    return acc;
  }, []);

  for (let i of renamedItems) {
    const count = getNameCount(namesMap, i.checkName);
    i.num = count;
  }
  return renamedItems[0];
};
// 通过id递归获取父节点
const getParentId = (list, id) => {
  for (let i in list) {
    if (list[i].id == id) {
      return [list[i]];
    }
    if (list[i].children) {
      let node = getParentId(list[i].children, id);
      if (node !== undefined) {
        return node.concat(list[i]);
      }
    }
  }
};
// 树结构删除数据
const removeNodeInTree = async (treeList, id, name, parentId, levelType) => {
  if (!treeList || !treeList.length) {
    return;
  }
  let copyTree = JSON.parse(JSON.stringify(copyTreeData.value));
  const copyTreeTile = xeUtils.toTreeArray(copyTree);
  for (let i = 0; i < treeList.length; i++) {
    if (treeList[i].id === id && (treeList[i].whetherNew || treeList[i].whetherReplace)) {
      //只能移除被替换或者追加的元素
      const delId = [id];
      // 如果包含子级，则将子级所有单位工程全部移除
      if (levelType === 2 && treeList[i].children && treeList[i].children.length) {
        treeList[i].children.map((i) => {
          delId.push(i.id);
        });
      }
      if (levelType === 3) {
        //移除单位父级也是新增且只有这一个单位的话也移除
        let parentIsRemove = currentTreeTile.filter(
          (a) =>
            a.id === parentId &&
            a.whetherNew &&
            a.children.length === 1 &&
            a.children[0].whetherNew
        ); //父级是否满足移除条件
        delId.push(parentId);
      }
      // 获取右侧树结构
      delListId.value = delId;
      let unitObj = {};
      // 如果是单位工程
      if (treeList[i].whetherNew) {
        unitObj = {};
      } else if (treeList[i].whetherReplace) {
        let obj = copyTreeTile.find((a) => a.id === treeList[i].oldId);
        unitObj = { ...obj };
      }
      treeList.splice(i, 1);
      if (Object.keys(unitObj).length !== 0) {
        treeList.splice(i, 0, unitObj);
      }
      break;
    }
    removeNodeInTree(treeList[i].children, id, name, treeList[i].id, levelType);
  }
};
// 处理左侧树删除操作
const handleleftTree = async () => {
  let leftTree = currentTreeData.value;
  let letfChild = leftTree[0].children;
  for (let i in letfChild) {
    let rightNum = 0;
    let unitChild = letfChild[i].children;
    for (let n in unitChild) {
      // 如果是右侧导入过来的单位工程
      if (unitChild[n].whetherNew || unitChild[n].whetherReplace) {
        rightNum++;
      }
    }
    // 如果单项工程下不是全部为右侧导入的单位工程，则单项工程删除从右侧导入进来的属性
    if (rightNum !== unitChild.length) {
      delete letfChild[i].constructMajorType;
      delete letfChild[i].disableCheckbox;
      delete letfChild[i].disabled;
      delete letfChild[i].whetherNew;
      delete letfChild[i].whetherReplace;
    }
  }
};

// 循环，处理树
/*
**type addDisable  添加disable， 如果所有的子级勾选了，父级默认给勾选上
       addNew      添加new
*/
const recursionTree = (treeList, type) => {
  treeList.forEach((node) => {
    setValue(node, type);
    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => {
        setValue(child, type);
      });
      // addDisable  添加disable， 如果所有的子级勾选了，父级默认给勾选上
      if (["addDisable", "delDisable"].includes(type) && [2].includes(node.levelType)) {
        let isAllUse = node.children.every((i) => {
          return importSelected.historyKeys.includes(i.id);
        });
        switch (type) {
          case "addDisable":
            if (isAllUse) {
              node.disabled = true;
            } else {
              node.disabled = false;
            }
            break;
          case "delDisable":
            node.disabled = isAllUse && !importSelected.historyKeys.includes(node.id);
            break;
          default:
            break;
        }
      }

      recursionTree(node.children, type);
    }
  });
};

//设置值
const setValue = (data, type) => {
  if (!data.initDisable) {
    // 过滤最原始就不能编辑的值
    switch (type) {
      case "addDisable":
        data.disableCheckbox = importCheckedKeys.value.includes(data.id);
        data.disabled = importCheckedKeys.value.includes(data.id);
        break;
      case "addNew":
        data.whetherNew = importCheckedKeys.value.includes(data.id);
        break;
      case "delDisable":
        if (delParentIdList.value.includes(data.id)) {
          data.disableCheckbox = false;
          data.disabled = false;
        }
        break;
      default:
        break;
    }
  }
};
// 当前项目
const getTreeList = () => {
  expandedKeys.value = [];
  getJsAsideTreeList(route.query.constructSequenceNbr).then((res) => {
    if (res.code === 200) {
      const data = xeUtils.toArrayTree(
        Array.isArray(res.result) ? res.result : [res.result],
        {
          children: "children",
          id: "id",
          pid: "parentId",
          sort: "sort",
        }
      );

      currentTreeData.value = data;
      copyTreeData.value = JSON.parse(JSON.stringify(data));
      currentSelected.initData = data;
      // 获取导入项目
      getImportTreeList();
    }
  });
};

// 导入项目列表
const getImportTreeList = async () => {
  const data = xeUtils.toArrayTree(
    Array.isArray(importYsf.value) ? importYsf.value : [importYsf.value],
    {
      children: "children",
      id: "id",
      pid: "parentId",
      sort: "sort",
    }
  );
  const list = await flattenTree(toRaw(currentTreeData.value));
  // 判断当前项目有没有单项
  hasSingle.value = list.some((i) => {
    return [2].includes(i.levelType);
  });
  handleImportTree(data, hasSingle.value);
  importTreeData.value = data;
  // 如果是同一个预算文件生成的可以进行匹配合并 否则不可以
  if(data[0].equalJieSuanFileFlag){
    isMath.value=false
  }else{
    isMath.value=true
  }
  tileTreeList();
  nextTick(() => {
    loading.value = false;
  });
};
// status true， 保存成功
const cancel = (status = false) => {
  dialogVisible.value = false;
  if (!status) {
    csProject.deleteImportProject(route.query.constructSequenceNbr);
  }
  emits("closeDialog", { status });
};

// 点击确定按钮
const handleOk = async () => {
  const status = await handleTip();
  if (status) {
    message.error(status);
    return;
  }
  if (submitLoading.value) return;
  submitLoading.value = true;
  let endTree=JSON.parse(JSON.stringify(currentTreeData.value))
  handleImportEndTree(endTree)
  const postData = {
    constructId: route.query.constructSequenceNbr,
    projectStructureTree:endTree[0],
  };
  csProject
    .saveJsOutImportProject(postData)
    .then((res) => {
      if (res.status === 200) {
        message.success("导入成功");
        setTimeout(() => {
          location.reload();
        }, 1000);
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};
// 处理导入项目结果
const handleImportEndTree = (tree,parentId) => {
  tree.forEach((node,index) => {
    // 如果id有#则去掉
    if(node.id.includes('#')){
      node.id=node.id.split('#')[0];
    }
    if(node.parentId&&node.parentId.includes('#')){
      node.parentId=parentId
    }
    if (node.children && node.children.length > 0) {
      handleImportEndTree(node.children,node.id);
    }
  });
};
// 初始化，处理导入项目
const handleImportTree = (tree, hasSingle, parentId = null) => {
  tree.forEach((node,index) => {
    // 工程项目禁用, hasSingle,当前项目没有单项的，导入项目单项也禁用
    const DisableStatus = [1].includes(node.levelType)
    // 空的单项禁用，
    //   ||([2].includes(node.levelType) &&
    //     (!node.children || !node.children.length || !hasSingle));
    // 将导入项目id从新定义，防止移动到左侧时id重复问题
    node.id = node.id + '#'
    if(node.levelType!=1){
      node.parentId= node.parentId + '#'
    }
    node.whetherNew = false;
    node.disableCheckbox = DisableStatus;
    node.disabled = DisableStatus;
    if (DisableStatus) {
      // 设置最原始的值，默认就是不能编辑
      node.initDisable = true;
    }

    // 判断导入项目有没有单项
    if ([2].includes(node.levelType)) {
      importSingleList.value.push(node.id);
    }

    if (node.children && node.children.length > 0) {
      handleImportTree(node.children, hasSingle, node.id);
    }
  });
};

/**
 *
 * @param {*} ossPath 导入文件的路径
 */
const openDialog = (data) => {
  if (data) {
    loading.value = true;
    importYsf.value = data;
    dialogVisible.value = true;
    getTreeList();
  }
};

watchEffect(() => {
  if (props.importJsList) {
    openDialog(JSON.parse(JSON.stringify(props.importJsList)));
  }
});
</script>
<style lang="scss" scoped>
.del-icon {
  font-size: 17px;
}

.mIcon {
  margin-right: 10px;
}

.tree-content-wrap {
  width: 70vw;
  height: 100%;
  max-width: 800px;
  min-width: 700px;
  display: flex;
  flex-direction: column;
}

.tree-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dialog-content {
  background: rgba(250, 250, 250, 0.39);
  border: 1px solid #e1e1e1;
  border-radius: 2px;
  display: flex;
  flex-direction: column;
  height: 50vh;
  width: 43%;
  overflow: hidden;

  &:hover {
    overflow: auto;
  }

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 7px 13px;
    background-color: #eaeaea;

    span {
      font-size: 14px;
      font-weight: 600;
      color: #333333;
    }

    .checkhandle {
      display: flex;
      align-items: center;
    }
  }

  .list {
    flex: 1;
    padding: 16px 18px;
    background-color: #fafafa;
    overflow: hidden;

    &:hover {
      overflow: auto;
    }

    ::v-deep .ant-tree {
      background-color: #fafafa;

      .ant-tree-switcher-noop {
        opacity: 0;
      }

      .ant-tree-switcher {
        background-color: transparent;
      }
    }
  }
}

.group-list {
  margin: 12px 0 30px;
  padding: 9px 18px;
  background: rgba(250, 250, 250, 0.39);
  border: 1px solid #e1e1e1;
  border-radius: 2px;
}

.footer-btn-list {
  margin-top: 30px;
}

.conButtonBox {
  display: flex;
  align-items: center;
  flex-direction: column;

  button {
    display: inline-block;
    width: 90px;
    text-align: center;
  }
}
</style>
