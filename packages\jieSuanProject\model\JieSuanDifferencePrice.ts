export class JieSuanDifferencePrice {


    /**
     * 结算单价
     */
    public jieSuanPrice: number;
    /**
     * 结算单价来源
     */
    public jieSuanPriceSource: string;
    /**
     * 基期价
     */
    public jieSuanBasePrice: number;
    /**
     * 基期价来源
     */
    public jieSuanBasePriceSource: string;
    constructor(jieSuanPrice: number, jieSuanPriceSource: string, jieSuanBasePrice: number, jieSuanBasePriceSource: string) {
        this.jieSuanPrice = jieSuanPrice;
        this.jieSuanPriceSource = jieSuanPriceSource;
        this.jieSuanBasePrice = jieSuanBasePrice;
        this.jieSuanBasePriceSource = jieSuanBasePriceSource;
    }
}
