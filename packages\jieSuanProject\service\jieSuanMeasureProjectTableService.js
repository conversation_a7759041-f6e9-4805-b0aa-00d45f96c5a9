const {Service} = require('../../../core');
const BranchProjectLevelConstant = require("../../../electron/enum/BranchProjectLevelConstant");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const JieSuanMethodEnum = require("../enum/JieSuanMethodEnum");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const UpdateStrategy = require("../../../electron/main_editor/update/updateStrategy");
const {JieSuanRcjStageUtils} = require("../utils/JieSuanRcjStageUtils");
const gclList = require("../jsonData/zhiBiao/gcl_index");

class JieSuanMeasureProjectTableService extends Service {

    constructor(ctx) {
        super(ctx);
        this._baseBranchProjectOptionService = this.service.baseBranchProjectOptionService;
        this._itemBillProjectOptionService = this.service.itemBillProjectOptionService;
    }


    disPlayType = {
        "0": " ",
        "01":"部",
        "02":"部",
        "03":"清",
        "04":"定"
    }

    /**
     * 导入预算文件后初始化 措施项目结算字段
     * @param obj  ysf的分部分项数据
     */
    async initYsfToJieSuanCsxmData(args) {
        args.type=2;
        await  this.service.jieSuanProject.jieSuanItemBillProjectService.filterTempDel(args);
        let {constructId, singleId, unitId} = args;
        //获取单位数据
        let unitProjects = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //获取单位中的分部分项数据
        if (!ObjectUtils.isEmpty(unitProjects)) {
            let measureProjectTables = unitProjects.measureProjectTables.getAllNodes();
            //设置单位单项原始数据标识
            for (const measureProjectTable of measureProjectTables) {
                measureProjectTable.backTotal = measureProjectTable.total;
                measureProjectTable.annotations = null;
                measureProjectTable.color = null;
                //只处理清单定额
                if (measureProjectTable.kind === BranchProjectLevelConstant.qd || measureProjectTable.kind === BranchProjectLevelConstant.de) {
                    //合同工程量
                    measureProjectTable.backQuantity = measureProjectTable.quantity;
                    //合同合价
                    measureProjectTable.backPrice = measureProjectTable.total;
                    measureProjectTable.backTotal = measureProjectTable.price;
                    // measureProjectTable.backQuantityExpression = measureProjectTable.quantityExpression;
                    measureProjectTable.backQuantityExpressionNbr = measureProjectTable.quantityExpressionNbr;
                    measureProjectTable.backCreateDeId = measureProjectTable.createDeId;
                    measureProjectTable.backRelationDeId = measureProjectTable.relationDeId;
                    if (ObjectUtils.isNotEmpty(measureProjectTable.quantityExpression) && measureProjectTable.quantityExpression.toString().includes("GCLMXHJ")) {
                        measureProjectTable.quantityExpression = measureProjectTable.quantity;
                    }
                    // if( measureProjectTable.quantityExpression.includes("GCLMXHJ")){
                    //     measureProjectTable.quantityExpression = measureProjectTable.quantity;
                    // }
                    measureProjectTable.backQuantityExpression = measureProjectTable.quantityExpression;
                    if (measureProjectTable.kind === BranchProjectLevelConstant.qd) {
                        //结算方式  3.12 马璇让把默认值改为按实际发生
                        measureProjectTable.settlementMethod = JieSuanMethodEnum.METHOD3.desc;
                        measureProjectTable.settlementMethodValue = JieSuanMethodEnum.METHOD3.code;
                        // //设置是否自动计算费用数据 标识 默认是计算
                        // measureProjectTable.ifAutoCount = true;
                        // measureProjectTable.isLocked = false;
                        //量差设置默认值
                        measureProjectTable.quantityDifference = 0;
                        measureProjectTable.quantityDifferenceProportion = 0;


                        //计算下挂的定额含量
                        let filterDeList = measureProjectTables.filter(de => de.parentId === measureProjectTable.sequenceNbr);
                        //计算定额含量
                        for (const de of filterDeList) {
                            //含量k=定额or人材机的结算工程量/所属清单行的结算工程量
                            de.deProportion = NumberUtil.numberScale6(NumberUtil.divide(de.quantity, measureProjectTable.quantity));
                        }
                    }
                    // //处理定额的工程量表达式为null
                    // if (measureProjectTable.kind === BranchProjectLevelConstant.de) {
                    //     measureProjectTable.quantityExpression=null;
                    // }

                }
                //原始数据
                measureProjectTable.originalFlag = true;
                //定额没有调整系数
                if (measureProjectTable.kind != BranchProjectLevelConstant.de) {
                    //调整系数
                    measureProjectTable.adjustmentCoefficient = 1;
                }
            }
        }
    }


    /**
     * 修改结算相关的措施项目数据
     * @param args
     * @returns {Promise<void>}
     */
   async updateJieSuanCsxmData(args){
        let {constructId, singleId, unitWorkId,pointLineId,column,value,major} = args;
        let upDateInfo={
            column,value
        }

        let allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId);
        let pointLine = allData.find(e => e.sequenceNbr === pointLineId);


       //修改措施项目结算方式
       if (column=="settlementMethodValue" && value !== pointLine.settlementMethodValue) {
           //结算方式切换逻辑 处理
           await this.settlementMethodUpdate(constructId, singleId, unitWorkId, pointLine, pointLine.settlementMethodValue, value);
           pointLine.settlementMethodValue = value;
           pointLine.settlementMethod = JieSuanMethodEnum.getEnumByCode(pointLine.settlementMethodValue).desc;
           upDateInfo.adjustmentCoefficient= pointLine.adjustmentCoefficient;
           //计算量差
           await this.service.jieSuanProject.jieSuanItemBillProjectService.mathQuantityDifference(constructId, singleId, unitWorkId, pointLine);
           //费用代码记取
           await this.service.jieSuanProject.jieSuanProjectService.countFeeCodeAndMathFee(args);
       }

       //修改调整系数
       if (column=="adjustmentCoefficient" && value!== pointLine.adjustmentCoefficient) {
           pointLine.adjustmentCoefficient = upDateInfo.adjustmentCoefficient;
           //重新计算人材机消耗量
           //重新计算单价构成 以及其他数据
           await  this.rcjProcess.xsToRcj(2, pointLine.sequenceNbr, constructId, singleId, unitWorkId,pointLine.adjustmentCoefficient);
           //费用代码记取
           await this.service.jieSuanProject.jieSuanProjectService.countFeeCodeAndMathFee(args);

       }

        // 修改清单工程量表达式  ----定额不可修改
        if (column=="quantityExpression" && value !== pointLine.quantityExpression) {
            let updateStrategy =   new UpdateStrategy({constructId,singleId,unitId:unitWorkId,pageType:"csxm"});
            await updateStrategy.execute({pointLineId,upDateInfo});
            //计算量差
            await this.service.jieSuanProject.jieSuanItemBillProjectService.mathQuantityDifference(constructId, singleId, unitWorkId, pointLine);
            //处理清单下挂的定额
            await this.service.jieSuanProject.jieSuanItemBillProjectService.updateDeQuantity(constructId, singleId, unitWorkId, pointLine.sequenceNbr,"csxm");
            //修改分期工程量明细
            JieSuanRcjStageUtils.qdStageCalculate(pointLine);
            //费用代码记取
            await this.service.jieSuanProject.jieSuanProjectService.countFeeCodeAndMathFee(args);

        }

        //分部分项指标
        if (column == "indicatorName") {
            pointLine.indicatorName = value=="无"?null:value;
            pointLine.indicatorNameOfMajor = major;

        }
        //工程量指标
        if (column == "quantityIndicatorName" ) {
            if( value=="无"){
                pointLine.quantityIndicatorName =null;
                pointLine.quantitIndicatorNameOfMajor = major;
                pointLine.quantityIndicatorUnit = null;
            }else {
                pointLine.quantityIndicatorName = value;
                pointLine.quantitIndicatorNameOfMajor = major;
                //更新工程量单位
                let find = gclList[major].find(gcl=> gcl.指标名称==value );
                pointLine.quantityIndicatorUnit = ObjectUtils.isNotEmpty(find)?find.单位:null;
            }

        }


        return {
            "enableUpdatePrice": true,
            "lineInfo": pointLine
        };
   }


    /**
     * 调整系数->人才机
     * @param qdId 分部分项/措施项目清单id
     * @param branchType 1分部分项、2措施项目
     * @param constructId 工程ID
     * @param args singleId 单项工程id
     * @param args unitId 单位工程id
     * @param args adjustmentCoefficient 调整系数  没有系数的时候标识还原
     */
    async xsToRcj(branchType, qdId, constructId, singleId, unitId, adjustmentCoefficient) {
        let rcjListAll=[];
        // 通过清单id 获取下挂的定额
        let deList = PricingFileFindUtils.getDeByQdId(constructId, singleId, unitId, qdId);
        // 通过定额id 获取rcj
        if (!ObjectUtils.isEmpty(deList)) {
            for (const de of deList) {
                let rcjList = this.service.rcjProcess.listRcjData(branchType, de.sequenceNbr, constructId, singleId, unitId);
                if (rcjList && rcjList.length > 0) {
                    for (let i = 0; i < rcjList.length; i++) {
                        let rcj = rcjList[i];
                        rcjListAll.push(rcj);
                        let sequenceNbr = rcj.sequenceNbr;
                        // 子父级材料
                        let type = 1; // 父级
                        if (rcj.levelMark === 1 || rcj.levelMark === 2) {
                            type = 2; //子级
                        }
                        let resQty;
                        if(ObjectUtils.isNotEmpty(adjustmentCoefficient)){
                            // 人才机数据编辑
                            // resQty = ObjectUtils.isEmpty(rcj.resQty) ? rcj.initResQty * adjustmentCoefficient : rcj.resQty * adjustmentCoefficient;
                            resQty = rcj.initResQty * adjustmentCoefficient;
                        }else {
                            //还原人材机消耗量
                            resQty =rcj.initResQty;
                        }

                        let constructProjectRcj = {resQty};
                        let param = {constructId, singleId, unitId, sequenceNbr, type, constructProjectRcj,libraryCode:de.libraryCode};
                        await this.service.constructProjectRcjService.updateConstructAndDetailRcj(param);
                    }
                }
            }
        }
    }


    /**
     * 切换结算方式
     * @param constructId
     * @param singleId
     * @param unitId
     * @param qdId  当前清单id
     * @param selectType 选中清单的结算类型
     * @param replaceType 替换后的结算类型
     */
    async settlementMethodUpdate(constructId, singleId, unitId,qd,selectType,replaceType) {
        let changeQty=false;
        //总价包干】——>【按实际发生】
        if (selectType == JieSuanMethodEnum.METHOD2.code && replaceType == JieSuanMethodEnum.METHOD3.code) {
            //切换后清单下费用定额/含变量的装饰垂运定额/含变量的标准换算kind3关联定额恢复关联关系；
            qd.ifAutoCount = true;
            qd.adjustmentCoefficient = 1;
            this.updataDeKind3(constructId, singleId, unitId, qd.sequenceNbr, 2,true);
        }
        //总价包干】——>【可调措施】：
        if (selectType == JieSuanMethodEnum.METHOD2.code && replaceType == JieSuanMethodEnum.METHOD1.code) {
            //清单下费用定额/含变量的装饰垂运定额/含变量的标准换算kind3关联定额依旧没有关联关系
            qd.ifAutoCount = false;
            this.updataDeKind3(constructId, singleId, unitId, qd.sequenceNbr, 2,true);
        }
        //可调措施】——>【按实际发生】：  调整系数值恢复为“1”且不可修改；
        if (selectType == JieSuanMethodEnum.METHOD1.code && replaceType == JieSuanMethodEnum.METHOD3.code) {

            qd.ifAutoCount = true;
            qd.adjustmentCoefficient = 1;
            changeQty=true;
            this.updataDeKind3(constructId, singleId, unitId, qd.sequenceNbr, 2,true);
            await this.xsToRcj(2, qd.sequenceNbr, constructId, singleId, unitId, null);

        }
        //可调措施】——>【总价包干】：  调整系数值恢复为“1”且不可修改；    ● 同时定额结算单价值等于合同单价值，定额下人材机数据恢复为原始合同数据；
        //     ● 该清单结算工程量不可编辑，值恢复为原始合同工程量值
        if (selectType == JieSuanMethodEnum.METHOD1.code && replaceType == JieSuanMethodEnum.METHOD2.code) {

            qd.adjustmentCoefficient = 1;
            qd.quantity = qd.backQuantity;
            qd.ifAutoCount = false;
            changeQty=true;
            await this.xsToRcj(2, qd.sequenceNbr, constructId, singleId, unitId, null);

        }
        //按实际发生】——>【总价包干】：      ● 清单下费用定额/含变量的装饰垂运定额/含变量的标准换算kind3关联定额断开关联关系；同时定额结算单价值等于合同单价值，定额下人材机数据恢复为原始合同数据；
        //     ● 该清单结算工程量不可编辑，值恢复为原始合同工程量值
        if (selectType == JieSuanMethodEnum.METHOD3.code && replaceType == JieSuanMethodEnum.METHOD2.code) {
            qd.ifAutoCount = false;
            qd.quantity = qd.backQuantity;
            qd.adjustmentCoefficient = 1;
            changeQty=true;
            this.updataDeHeJiaByQd(constructId, singleId, unitId, qd.sequenceNbr, 2);
            this.updataDeKind3(constructId, singleId, unitId, qd.sequenceNbr, 2,false);
            await this.xsToRcj(2, qd.sequenceNbr, constructId, singleId, unitId, null);

        }
        //按实际发生】——>【可调措施】： 清单下费用定额/含变量的装饰垂运定额/含变量的标准换算kind3关联定额断开关联关系；同时定额结算单价值等于合同单价值，定额下人材机数据恢复为原始合同数据
        if (selectType == JieSuanMethodEnum.METHOD3.code && replaceType == JieSuanMethodEnum.METHOD1.code) {
            qd.ifAutoCount = false;
            this.updataDeHeJiaByQd(constructId, singleId, unitId, qd.sequenceNbr, 2);
            this.updataDeKind3(constructId, singleId, unitId, qd.sequenceNbr, 2,false);
            await this.xsToRcj(2, qd.sequenceNbr, constructId, singleId, unitId, null);
        }

    }

    /**
     * 修改清单下定额的结算单价 kind3关联关系
     * @param constructId
     * @param singleId
     * @param unitId
     * @param qdId
     * @param type
     */
    updataDeHeJiaByQd(constructId, singleId, unitId,qdId,type){
        let deList = PricingFileFindUtils.getDeByQdId(constructId, singleId, unitId,qdId,type);
        for(const de of deList){
            de.price=de.backPrice;
            de.quantity=de.backQuantity;
            de.total=de.backTotal;
            de.quantityExpression=de.backQuantityExpression;
            de.quantityExpressionNbr=de.backQuantityExpressionNbr;

        }

    }

    updataDeKind3(constructId, singleId, unitId,qdId,type,kind3HsFlag){
        let deList = PricingFileFindUtils.getDeByQdId(constructId, singleId, unitId,qdId,type);
        for(const de of deList){
            if(kind3HsFlag){
                de.createDeId= de.backCreateDeId;
                de.relationDeId=de.backRelationDeId;
            }else {
                de.createDeId=null;
                de.relationDeId=null;
            }
        }

    }


    /**
     * 批量切换结算方式
     * @param constructId
     * @param singleId
     * @param unitId
     * @param qdList  修改后的清单集合  isAll为true 时为空
     * @param isAll  true 所有清单
     * @param settlementMethodValue  结算方式类型 1 可调措施（默认）  2 总价包干 3 按实际发生
     */
    async batchSettlementMethodUpdate(args){
        let{constructId, singleId, unitId,qdList,isAll,settlementMethodValue}=args;
        //单价措施清单
        let djcsQdList = await PricingFileFindUtils.getQdByDjcs(constructId, singleId, unitId);
        //总价措施清单
        let zjcsQdList = await PricingFileFindUtils.getQdByZjcs(constructId, singleId, unitId);
        djcsQdList.push(...zjcsQdList);
        //改所有的 除安文费
        if(isAll){
            for(const qd of djcsQdList){
                await this.settlementMethodUpdate(constructId, singleId, unitId,qd,qd.settlementMethodValue,settlementMethodValue);
                //清单赋值
                qd.settlementMethodValue=settlementMethodValue;
                qd.settlementMethod=JieSuanMethodEnum.getEnumByCode(settlementMethodValue).desc;
                //计算量差
                await this.service.jieSuanProject.jieSuanItemBillProjectService.mathQuantityDifference(constructId, singleId, unitId, qd);
            }
        }else {
            for(const qd of qdList){
                //获取清单数据
                let filter = djcsQdList.filter(meas => meas.sequenceNbr==qd.sequenceNbr);
                if(!ObjectUtils.isEmpty(filter)){
                    await this.settlementMethodUpdate(constructId, singleId, unitId,filter[0],filter[0].settlementMethodValue,settlementMethodValue);
                    //清单赋值
                    filter[0].settlementMethodValue=settlementMethodValue;
                    filter[0].settlementMethod=JieSuanMethodEnum.getEnumByCode(settlementMethodValue).desc;
                    //计算量差
                    await this.service.jieSuanProject.jieSuanItemBillProjectService.mathQuantityDifference(constructId, singleId, unitId, qd);
                }
            }
        }

    }





    /**
     * 措施项目层级展示数据查询接口
     */
    async showCsxmCjjg(constructId, singleId, unitWorkId, sequenceNbr, pageNum, pageSize, isAllFlag, hierachy,colorList) {
        let mockAllData = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId);
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId);
        let mock = [];
        let obj={"data":[]};
        let datas;
        if (hierachy !== "none") {

            // mock = mockAllData;
            // mock.forEach(a => a.displayStatu = 10);
            // datas =this.service.itemBillProjectOptionService.getFbFx(constructId, singleId, unitWorkId, sequenceNbr, pageNum, pageSize,isAllFlag);
            // datas = this.service.stepItemCostService.pageSearch(constructId, singleId, unitWorkId, sequenceNbr,pageNum, pageSize,isAllFlag,unit.screenCondition);
            let csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId);
            obj.data = csxm.flattenTree(csxm.root);

            if (hierachy !== "all") {
                for (let mockAllDatum of mockAllData.getAllNodes()) {
                    if (mockAllDatum.kind === "0") {
                        sequenceNbr = mockAllDatum.sequenceNbr;
                        break;
                    }
                }
                this.service.jieSuanProject.jieSuanItemBillProjectService.handleData(hierachy, sequenceNbr, obj);

                let data = obj.data;
                let arr = [];
                data.forEach(a => arr.push(a.sequenceNbr));
                mockAllData.getAllNodes().forEach(a => {
                    if (arr.includes(a.sequenceNbr)) {
                        a.displayStatu = 10;
                    } else {
                        a.displayStatu = 9;
                    }
                })
            } else {
                let mockAllData = obj.data;
                for (let mockAllDatum of mockAllData) {
                    for (let mockAllDatum1 of mockAllData) {
                        if (mockAllDatum.sequenceNbr === mockAllDatum1.parentId) {
                            mockAllDatum.displaySign = 1;
                            break;
                        }
                    }
                }
            }

            datas={
                "data": obj.data,
                "total": obj.data.length,
                "pageNum": pageNum,
                "pageSize": pageSize
            };
        } else {
            datas = this.service.stepItemCostService.pageSearch(constructId, singleId, unitWorkId, sequenceNbr,pageNum, pageSize,isAllFlag,unit.screenCondition,colorList);
        }
        // 填充关联合同清单数据
        await this.service.jieSuanProject.jieSuanRelatedContractsQdService.setRelatedQdData(datas.data);

        return ResponseData.success(datas);
    }






}


JieSuanMeasureProjectTableService.toString = () => "[class JieSuanMeasureProjectTableService]"
module.exports = JieSuanMeasureProjectTableService