<!--
 * @Descripttion: 单价构成
 * @Author: liuxia
 * @Date: 2023-05-29 14:22:33
 * @LastEditors: wangru
 * @LastEditTime: 2024-03-26 16:16:01
-->
<template>
  <vxe-table
    ref="priceCompositionTable"
    :data="vexTableData"
    :column-config="{ resizable: true }"
    class="table-edit-common"
    :cell-class-name="cellClassName"
    :edit-config="{
      trigger: 'click',
      mode: 'cell',
      enabled: currentInfo.kind === '04' ? true : false,
    }"
    @cell-click="
      cellData => {
        useCellClickEvent(cellData, tableCellClick, ['sequenceNbr']);
      }
    "
    :menu-config="menuConfig"
    @menu-click="contextMenuClickEvent"
    @edit-closed="editClosedEvent"
    height="auto"
    :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
    keep-source
  >
    <vxe-column field="sort" :width="columnWidth(60)" title="序号"> </vxe-column>
    <vxe-column
      field="code"
      :width="columnWidth(120)"
      title="费用代号"
      :edit-render="{ autofocus: '.vxe-input--inner' }"
    >
      <template #edit="{ row }">
        <vxe-input
          :clearable="false"
          v-model.trim="row.code"
          type="text"
          @blur="clear()"
          @keyup="row.code = row.code.replace(/[^\w_]/g, '')"
        ></vxe-input>
      </template>
    </vxe-column>
    <vxe-column
      field="name"
      :min-width="columnWidth(220)"
      title="名称"
      :edit-render="{ autofocus: '.vxe-input--inner' }"
    >
      <template #default="{ row }">
        <span>{{ row.name }}</span>
      </template>
      <template #edit="{ row }">
        <cell-textarea
          :clearable="false"
          v-model.trim="row.name"
          @blur="clear()"
          placeholder="请输入名称"
          :textHeight="row.height"
          @keyup="row.name = row.name.replace(/\-|\+|\*|\/|\.|\(|\)/g, '')"
        ></cell-textarea>
      </template>
    </vxe-column>
    <vxe-column
      title="计算基数"
      :min-width="columnWidth(150)"
      field="caculateBase"
      :edit-render="{ autofocus: '.vxe-input--inner' }"
    >
      <template #default="{ row }">
        {{ row.caculateBase }}
      </template>
      <template #edit="{ row }">
        <vxeTableEditTable
          tableType="'DJGC'"
          @showTable="showTable"
          :filedValue="row.caculateBase"
          @update:filedValue="
            newValue => {
              saveCustomInput(newValue, row, 'caculateBase', $rowIndex);
            }
          "
        ></vxeTableEditTable>
      </template>
    </vxe-column>

    <vxe-column title="基数说明" :min-width="columnWidth(200)" field="desc"></vxe-column>
    <vxe-column
      field="rate"
      :width="columnWidth(120)"
      title="费率（%）"
      :edit-render="{ autofocus: '.vxe-input--inner' }"
    >
      <template #edit="{ row }">
        <vxe-input
          :clearable="false"
          v-model="row.rate"
          @blur="(row.taxRate = pureNumber(row.rate, 2)),clear()"
        ></vxe-input>
      </template>
    </vxe-column>
    <vxe-column title="单价" :width="columnWidth(80)" field="displayUnitPrice"></vxe-column>
    <vxe-column title="合价" :width="columnWidth(80)" field="displayAllPrice"></vxe-column>
    <vxe-column title="费用类别" :width="columnWidth(100)" field="type"></vxe-column>
    <vxe-column title="备注" :width="columnWidth(80)" field="notice"></vxe-column>
  </vxe-table>
  <common-modal
    className="dialog-comm confirmation-dialog"
    title="确认"
    width="520"
    v-model:modelValue="confirmationStatus"
    @close="closeApplication(false)"
    :mask="true"
  >
    <div class="content">
      <p class="tips">
        当前【{{ confirmationTitle }}】单价构成文件发生变化，要保存修改吗？
      </p>
      <div class="label-list">
        <span class="title"> 应用范围</span>
        <a-select ref="select" v-model:value="scopeValue" style="width: 60%">
          <a-select-option value="useDe">应用至当前定额</a-select-option>
          <a-select-option value="usePart">应用至当前分部</a-select-option>
          <a-select-option value="useUnit">应用至当前单位</a-select-option>
          <a-select-option value="useProject"
            >应用至当前工程项目</a-select-option
          >
        </a-select>
      </div>
    </div>
    <span class="confirmation-btns">
      <a-button @click="closeApplication(false)">取消</a-button>
      <a-button
        type="primary"
        style="margin-left: 50px"
        @click="saveApplication()"
        >确定</a-button
      >
    </span>
  </common-modal>
  <zj-mould
    ref="zjMouldRef"
    @onSuccess="onSuccessZj"
    :saveData="saveApiData"
  ></zj-mould>
</template>

<script setup>
import {
  ref,
  onMounted,
  onUnmounted,
  computed,
  onBeforeUnmount,
  onDeactivated,
  reactive,
  watch,
} from 'vue';
import { useCellClick } from '@gongLiaoJi/hooks/useCellClick';
import { onClickOutside } from '@vueuse/core';
import { globalData } from './status.js';
const { useCellClickEvent, selectedClassName } =
  useCellClick();
const props = defineProps(['tableData', 'RefreshList', 'currentInfo', 'type']);
import { columnWidth } from '@gongLiaoJi/hooks/useSystemConfig';
import { pureNumber } from '@/utils/index';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
import feePro from '@/api/feePro';
import infoMode from '@/plugins/infoMode.js';
import { message } from 'ant-design-vue';
let comModel = ref(false); //计算基数编写弹框
let textValue = ref('');
let oldValue = ref('');
const priceCompositionTable = ref(null);
const comArea = ref();
let isCurrent = ref(false);
let editCaculateBase = ref(false);
let vexTableData = ref([]);
let oldTableData = ref([]); //表格旧数据，不设置应用后的数据
const nameList = reactive(['规费', '安全生产、文明施工费', '安文费']);
let useSetDataSeq = ref(); //应用设置的范围选中的定额
let newGFandWF = reactive({
  newGF: null,
  newAWF: null,
  oldGF: null,
  oldAWF: null,
}); //修改安文费和规费后会勾选useDe和usePart会弹框
const initNewGFandWF = () => {
  newGFandWF = {
    newGF: null,
    newAWF: null,
    oldGF: null,
    oldAWF: null,
  };
};
let saveApiData = ref({}); //载入模板应用成功接口
onMounted(() => {
  initFirst();
  isCurrent.value = true;
});
watch(
  () => props.tableData,
  () => {
    initFirst();
  }
);
watch(
  () => props.currentInfo,
  (newVal, oldVal) => {
    if (confirmationStatus.value) {
      useSetDataSeq.value = oldVal.sequenceNbr;
    } else {
      useSetDataSeq.value = newVal.sequenceNbr;
    }
  }
);
const initFirst = () => {
  console.log('单价构成数据-------------initFirst', props.tableData);

  menuConfig.body.disabled = props.currentInfo.kind !== '04' ? true : false;
  dispposeData(props.tableData, true);
  console.log(
    '🚀 ~ vexTableData.value=props.tableData.map ~  props.tableData:',
    props.tableData
  );
  vexTableData.value = handleList(props.tableData);
  oldTableData.value =
    props.tableData && JSON.parse(JSON.stringify(props.tableData));
};
const dispposeData = (data, setData = false) => {
  if (!data) return;
  data.forEach(i => {
    i.oldName = i.name;
    if (setData && nameList.includes(i.oldName)) {
      // setData---为true设置规费，安文费旧数据
      i.oldName === '规费'
        ? (newGFandWF.oldGF = i.rate)
        : (newGFandWF.oldAWF = i.rate);
    }
  });
};
const showTable = bol => {
  //正在编辑计算基数弹框情况下不弹出设置应用范围
  editCaculateBase.value = bol;
};

const handleList = data => {
  return data?.map(
    ({ name, displayUnitPrice, displayAllPrice, ...otherData }) => {
      let cusData = {};
      if (props.currentInfo.kind == '04') {
        cusData = {
          name,
          displayUnitPrice,
          displayAllPrice,
          ...otherData,
        };
      } else {
        cusData = {
          name,
          displayUnitPrice,
          displayAllPrice,
        };
      }

      return { ...cusData };
    }
  );
};

const confirmationTitle = computed(() => {
  return props.currentInfo.bdName.length > 15
    ? props.currentInfo.bdName.substr(0, 15) + '...'
    : props.currentInfo.bdName;
});
// 载入模板相关
let zjMouldRef = ref(null);
let flag = ref(false);
const onSuccessZj = () => {
  flag.value = zjMouldRef.value.getIsReset();
  zjMouldRef.value.close();
  if (flag.value) {
    globalData.isEditStatus = false;
    initNewGFandWF();
  } else {
    globalData.isEditStatus = true;
  }
  console.log(
    'onSuccessZj---zjMouldRef.value.getIsReset()',
    flag.value,
    globalData.isEditStatus
  );
  refreshData();
  // getTableData();
};
//右键载入模板
const menuConfig = reactive({
  className: 'my-menus',
  body: {
    disabled: true,
    options: [
      [
        {
          name: '载入模板',
          code: 'zrmb',
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    if (!row) return;
    priceCompositionTable.value.setCurrentRow(row);
    return true;
  },
});
const contextMenuClickEvent = ({ menu, row }) => {
  if (props.currentInfo.kind !== '04') return;
  console.log('menu, row', menu, row);
  confirmationStatus.value = false;
  globalData.isEditStatus = false;
  // debugger;
  switch (menu.code) {
    case 'zrmb':
      // 复制
      saveApiData.value = {
        constructId: store.currentTreeGroupInfo?.constructId,
        singleId: store.currentTreeGroupInfo?.singleId, //单项ID
        unitId: store.currentTreeInfo?.id, //单位ID
        deId: props.currentInfo.sequenceNbr,
        pageType: props.type === 1 ? 'fbfx' : 'csxm',
      };
      zjMouldRef.value.open('djgc');
      break;
  }
};

//双击单元格-蓝框
const cellClassName = ({ $columnIndex, row, column }) => {
  if (props.currentInfo.kind !== '04') return;
  const selectName = selectedClassName({ $columnIndex, row, column });
  if (column.field === 'index') {
    return 'index-bg ' + selectName;
  }
  return selectName;
};
const editCalc = row => {
  comModel.value = true;
  textValue.value = {
    ...row,
    calculateFormula: row.caculateBase,
  };
  oldValue.value = row.caculateBase;
  globalData.isEditStatus = true;
};

//编辑计算基数弹框确认
const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
  // saveOrUpdateFeature({ data: featureData.value }, 0);
};

// 更新列表数据
const getTableData = () => {
  props.RefreshList(true);
};

const clear = () => {
  //清除编辑状态
  const $table = priceCompositionTable.value;
  $table.clearEdit();
};
const editClosedEvent = ({ row, column }) => {
  console.log('单价构成修改', row);
  const $table = priceCompositionTable.value;
  const field = column.field;
  let value = row[field];
  const reg = /[^\d\.]/g;
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  if (field === 'caculateBase' && value?.length === 0) {
    message.warn(`计算基数不可为空`);
    $table.revertData(row, field);
    return;
  }

  //修改接口
  update(row, field);
  globalData.isEditStatus = true;
};

const update = (row, field) => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    column: field,
    value: row[field],
    sequenceNbr: row.sequenceNbr,
    deId: props.currentInfo.sequenceNbr,
    pageType: props.type === 1 ? 'fbfx' : 'csxm',
  };
  if (!priceCompositionTable.value.isUpdateByRow(row, field)) return;
  if (nameList.includes(row.oldName)) {
    row.oldName === '规费'
      ? (newGFandWF.newGF = row[field])
      : (newGFandWF.newAWF = row[field]);
  }
  useSetDataSeq.value = props.currentInfo.sequenceNbr;
  feePro.cellEditorDJGC(apiData).then(res => {
    console.log('单价构成编辑保存', apiData);
    if (res.status === 200) {
      // 刷新页面数据--更新状态
      refreshData(row.sequenceNbr);
      message.success('修改成功');
    } else {
      priceCompositionTable.value.revertData(row, field);
      message.error(res.message);
    }
  });
};
const refreshData = (seq = null) => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    deId: props.currentInfo.sequenceNbr,
    pageType: props.type === 1 ? 'fbfx' : 'csxm',
  };
  feePro.queryforDeIdDJGC(apiData).then(res => {
    console.log('refreshData', res, apiData);
    if (res.status === 200) {
      dispposeData(res.result);
      vexTableData.value = handleList(res.result);

      if (seq) {
        let row = vexTableData.value.find(i => i.sequenceNbr === seq);
        row ? priceCompositionTable.value.setCurrentRow(row) : '';
      }
    } else {
      vexTableData.value = [];
    }
  });
};
const scopeValue = ref('useDe'); //默认选中当前项
const confirmationStatus = ref(false);

const closeApplication = isFlag => {
  if (!isFlag) {
    let list =
      props.tableData && JSON.parse(JSON.stringify(oldTableData.value));
    vexTableData.value = handleList([...list]);
    let apiData = {
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId, //单项ID
      unitId: store.currentTreeInfo?.id, //单位ID
      deId: props.currentInfo.sequenceNbr,
      pageType: props.type === 1 ? 'fbfx' : 'csxm',
    };
    feePro.cancelEditorDJGC(apiData).then(res => {});
  }
  console.log('closeApplication', isFlag, !isFlag);
  confirmationStatus.value = false;
  globalData.isEditStatus = false;
  initNewGFandWF();
};
const saveApplication = () => {
  //确定应用范围
  let flag = false; //判断安文费和规费是否修改
  if (
    (newGFandWF.newGF && newGFandWF.newGF !== newGFandWF.oldGF) ||
    (newGFandWF.newAWF && newGFandWF.newAWF !== newGFandWF.oldAWF)
  ) {
    flag = true;
  }
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    deId: useSetDataSeq.value,
    type: scopeValue.value,
    isUpdateAWForGF: flag,
    pageType: props.type === 1 ? 'fbfx' : 'csxm',
  };
  // const useList = ['useDe', 'usePart'];
  // let isNext = flag && useList.includes(scopeValue.value);
  flag &&
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText: '规费、安全生产、文明施工费费率的修改请于取费设置中进行操作',
      confirm: () => {
        feePro.cellEditorDJGC(apiData).then(res => {
          console.log('单价构成规费变化', apiData, res);
          if (res.status === 200) {
            infoMode.hide();
            useAll(apiData);
          } else {
            message.error(res.message);
          }
        });
      },
    });
  !flag && useAll(apiData);
};
const useAll = apiData => {
  // console.log(
  //   'useAll--- useSetDataSeq.value',
  //   useSetDataSeq.value,
  //   props.currentInfo.sequenceNbr
  // );
  console.log('useAll---应用范围', apiData);
  feePro.applyEditorDJGC(apiData).then(res => {
    console.log('useAll---应用范围返回', res);
    if (res.status === 200) {
      // 刷新页面数据--更新状态
      closeApplication(true);
      getTableData();
    }
  });
};
//如果页面编辑变化点击编辑区和切换页签弹框设置应用范围
onBeforeUnmount(() => {
  clickOutside();
});

// onDeactivated(() => {
//   clickOutside()
// })
const clickOutside = () => {
  console.log('zjMouldRef.value', zjMouldRef.value, editCaculateBase.value);
  if (
    (props.type === 2 && store.tabSelectName === '措施项目') ||
    (props.type === 1 && store.tabSelectName === '预算书')
  ) {
    if (
      !comModel.value &&
      isCurrent.value &&
      globalData.isEditStatus &&
      !editCaculateBase.value
    ) {
      confirmationStatus.value = true;
    }
  }
};
onClickOutside(priceCompositionTable, clickOutside);
</script>

<style lang="scss" scoped>
.btns {
  position: absolute;
  width: 200px;
  bottom: 10px;
  right: 40%;
  display: flex;
  justify-content: space-around;
}
.multiple-select {
  width: 50px;
  height: 30px;
  line-height: 30px;
  margin-left: -10px;
  text-indent: 10px;
  cursor: pointer;
}
// .content-project {
//   background: #ffffff;
//   height: 100%;
.table-content {
  // width: 100%;
  height: 100%;
  // overflow: hidden;
  background: #ffffff;
  user-select: none;
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  ::v-deep(.vxe-table .index-bg) {
    // background-color: rgba(243, 243, 243, 1);
    background-color: #fff;
  }
}
// }

.confirmation-dialog {
  .tips {
    font-size: 14px;
  }
  .label-list {
    margin: 16px 0 32px;
    display: flex;
    align-items: center;
    .title {
      font-weight: 400;
      font-size: 14px;
      color: #287cfa;
      margin-right: 16px;
    }
  }
  .confirmation-btns {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
