'use strict';

const fs = require("fs");
const os = require('os');

const { Service, } = require('../../core');
const {ObjectUtils} = require("../utils/ObjectUtils");
const UtilsPs = require('../../core/ps');
const {MainSetting} = require("../model/MainSetting");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {ConvertUtil} = require("../utils/ConvertUtils");

/**
 * 全局配置处理service
 */
class GlobalConfigurationService extends Service {

    constructor(ctx) {
        super(ctx);
        // global.idInformation
        this.configFilePath = `${os.homedir()}\\.xilidata\\global_config.json`;
        this.defaultConfigPath = UtilsPs.getExtraResourcesDir() + '/global_config.default.json';
        if(!UtilsPs.isPackaged()){
            try{
                fs.rmSync(this.configFilePath);
            }catch (e){
                console.log(e);
            }
        }
        console.log("--------%^&*(^%^&*________", this.defaultConfigPath)
        console.log("--------%^&*(^%^&*________", this.getGlobalConfig())
    }

    /**
     * 获取全局配置配置信息
     */
    getGlobalConfig () {
        if(ObjectUtils.isNotEmpty(global.globalConfig)){
            return global.globalConfig;
        }else{
            let globalConfig = {};
            let defaultConfig = {};
            if(fs.existsSync(this.configFilePath)){
                globalConfig = require(this.configFilePath);
            }

            if(fs.existsSync(this.defaultConfigPath)){
                defaultConfig = require(this.defaultConfigPath);
            }


            if(globalConfig?.version !== defaultConfig.version){
                global.globalConfig = ConvertUtil.recursiveCopyTo(globalConfig, defaultConfig);
                fs.writeFileSync(this.configFilePath, JSON.stringify(global.globalConfig, null, 4));
            }else{
                global.globalConfig = globalConfig;
            }

            return global.globalConfig;
        }
    }


    async initProjectConfig(constructId){
        let constructProject = await PricingFileFindUtils.getProjectObjById(constructId);
        let globalConfig = this.getGlobalConfig();

        let newProjectConfig = {
            version: globalConfig.version,
            budget: globalConfig.budget,
            project: globalConfig.project
        };

        ConvertUtil.recursiveCopyTo(constructProject.projectConfig,newProjectConfig);

        constructProject.projectConfig = newProjectConfig;

        // { // 默认存储路径
        //     let defaultStoragePath = await this.service.commonService.getSetStoragePath(null);
        //     softwareConfig.fileManagement.DEF_SAVE_PATH = defaultStoragePath;
        //     softwareConfig.fileManagement.FILE_DOWNLOAD_PATH = defaultStoragePath;
        // }

        {// 历史兼容
            this.dealBudgetNotRefresh(constructProject.projectConfig.budget, constructProject) ;
            // let is22 = PricingFileFindUtils.is22De(constructId)
            // if (!is22) {
            //     constructProject.rgfInMeasureAndRPriceInMechanicalAction = true;
            // }
        }
    }

    resetProjectConfig(constructId){
        let constructProject = PricingFileFindUtils.getProjectObjById(constructId);
        let globalConfig = this.getGlobalConfig();

        let newProjectConfig = {
            version: globalConfig.version,
            budget: globalConfig.budget,
            project: globalConfig.project
        };

        ConvertUtil.recursiveCopyTo(constructProject.projectConfig,newProjectConfig);

        constructProject.projectConfig = newProjectConfig;
    }

    /**
     * 获取全局配置配置信息
     */
     getProjectConfig(constructId){
        let constructProject =  PricingFileFindUtils.getProjectObjById(constructId);
        let globalConfig = this.getGlobalConfig();

        if(ObjectUtils.isNotEmpty(constructProject.projectConfig) && constructProject.projectConfig.version !== globalConfig.version){
             this.resetProjectConfig(constructId);
        }


        let result = {
            software: globalConfig.software,
            budget: constructProject.projectConfig ? constructProject.projectConfig.budget : globalConfig.budget
        }

        return result
    }




    /**
     * 设置配置信息
     * @param param
     * @return {boolean}
     */
    async resetGlobalConfig (param){
        let {constructId, config} = param;
        let globalConfig = this.getGlobalConfig();
        let constructProject = await PricingFileFindUtils.getProjectObjById(constructId);
        if(ObjectUtils.isEmpty(constructProject.projectConfig)){
            // 工程级别配置处理
            constructProject.projectConfig = {
                budget: globalConfig.budget,
                project: globalConfig.project
            };
        }
        if(ObjectUtils.isNotEmpty(config.software)){
            let software = config.software;

            //兼容历史
            this.dealSoftwareStorageConfig(software);

            // 软件级别配置处理  globalConfig.software
            ConvertUtil.recursiveCopyTo(software, globalConfig.software);
            fs.writeFileSync(this.configFilePath, JSON.stringify(globalConfig, null, 4));
        }

        if(ObjectUtils.isNotEmpty(config.budget)){
            // 历史兼容
            await this.dealBudgetHistoryConfig(config.budget, constructProject);

            // 文件级别配置处理 constructProject.projectConfig
            ConvertUtil.recursiveCopyTo(config.budget, constructProject.projectConfig.budget);
            if (!ObjectUtils.isEmpty(config.budget.jiSuan) && !ObjectUtils.isEmpty(config.budget.jiSuan.gcldw)){
                let gcldw = config.budget.jiSuan.gcldw;
                await this.service.constructConfigService.changeConfigGcldw(constructProject.sequenceNbr,gcldw);
            }

        }

        return true;


        let lockRcjResFlag = true;//人材机锁定消耗量标识
        if (globalConfig.budget.input.lockRcjResQty && param.budget.input.lockRcjResQty){
            lockRcjResFlag = false;
        }
        Object.assign(globalConfig, param);
        fs.writeFileSync(this.configFilePath, JSON.stringify(globalConfig, null, 4));
        global.globalConfig = globalConfig;

        //全局修改人材机消耗量
        await this.service.globalConfigurationImplementationService.lockRcjRes(lockRcjResFlag,param.budget.input.lockRcjResQty)

        return true;
    }

    async dealBudgetHistoryConfig(budgetConfig, constructProject){
        this.dealBudgetNotRefresh(budgetConfig, constructProject) ;

        if(ObjectUtils.isNotEmpty(budgetConfig.chengXianXuanXiang?.lockRcjResQty)){
            budgetConfig.input.lockRcjResQty = budgetConfig.chengXianXuanXiang.lockRcjResQty;
            //全局修改人材机消耗量
            let lockRcjResFlag = true;//人材机锁定消耗量标识
            if (budgetConfig.chengXianXuanXiang.lockRcjResQty && constructProject.projectConfig.budget.chengXianXuanXiang.lockRcjResQty){
                lockRcjResFlag = false;
            }
            await this.service.globalConfigurationImplementationService.lockRcjRes(lockRcjResFlag,budgetConfig.chengXianXuanXiang.lockRcjResQty)

        }
    }

    dealBudgetNotRefresh(budgetConfig, constructProject){
        if(ObjectUtils.isNotEmpty(budgetConfig.zhanShi?.tjxmtzhzdglzjfa)){
            constructProject.projectAttrRelateMergeScheme = budgetConfig.zhanShi.tjxmtzhzdglzjfa;
        }

        if(ObjectUtils.isNotEmpty(budgetConfig.zhanShi?.crdezsdeglzmtk)){
            constructProject.deGlTcFlag = budgetConfig.zhanShi.crdezsdeglzmtk;
        }

        if(ObjectUtils.isNotEmpty(budgetConfig.zhanShi?.crdezszycljgsztk)){
            constructProject.mainRcjShowFlag = budgetConfig.zhanShi.crdezszycljgsztk;
        }

        if(ObjectUtils.isNotEmpty(budgetConfig.zhanShi?.standardConversionShowFlag)){
            constructProject.standardConversionShowFlag = budgetConfig.zhanShi.standardConversionShowFlag;
        }

        if(ObjectUtils.isNotEmpty(budgetConfig.diQuTeXing?.yxsjsdcsxmhjxtbzdrgdjcytz)){
            constructProject.rgfInMeasureAndRPriceInMechanicalAction = budgetConfig.diQuTeXing.yxsjsdcsxmhjxtbzdrgdjcytz;
        }

        if(ObjectUtils.isNotEmpty(budgetConfig.zhanShi?.bdCode)){
            let m = constructProject.mainSetting;
            if(ObjectUtils.isEmpty(m)){
                m = new MainSetting();
                constructProject.mainSetting = m;
            }
            m.bdCode = budgetConfig.zhanShi?.bdCode;
        }
    }


    dealSoftwareStorageConfig(softwareConfig){
        const baseDataDir = `${os.homedir()}\\.xilidata\\userHistory.json`;
        //读取数据
        const data = fs.readFileSync(baseDataDir, 'utf8');
        const userHistoryData = JSON.parse(data);
        let isUserHistoryDataChanged = false;
        if(ObjectUtils.isNotEmpty(softwareConfig.fileManagement?.DEF_SAVE_PATH)){
            userHistoryData.DEF_SAVE_PATH = softwareConfig.fileManagement.DEF_SAVE_PATH;
            isUserHistoryDataChanged = true;
        }

        if(ObjectUtils.isNotEmpty(softwareConfig.fileManagement?.FILE_DOWNLOAD_PATH)){
            userHistoryData.FILE_DOWNLOAD_PATH = softwareConfig.fileManagement.FILE_DOWNLOAD_PATH;
            isUserHistoryDataChanged = true;
        }

        if(isUserHistoryDataChanged){
            let obj = ObjectUtils.toJsonString(userHistoryData);
            fs.writeFileSync(baseDataDir, obj);
        }
    }

    /**
     * 小数点保留位数默认配置
     * @return {{rcjDetailAmount: number,rcjSummaryAmount: number, qDDeAmount: number, rate: number, costPrice: number}}
     */
    getDecimalPointConfig(){
        return this.getGlobalConfig().project;
        // return {
        //     "rcjDetailAmount": 4,   //人材机明细区：消耗量、合计数量，数量类：小数点后4位、第5位小数四舍五入
        //     "rcjSummaryAmount": 4,   //人材机汇总：消耗量、合计数量，数量类：小数点后4位、第5位小数四舍五入
        //     "qDDeAmount": 3,  // 工程量，数量类：小数点后3位，第4位四舍五入
        //     "costPrice": 2, // 金额、合计，金额类：小数点后2位，第3位四舍五入
        //     "rate": 2,  // 费率、指数、比率(%)：小数点后2位，第3位四舍五入
        // };
    }

    /**
     * 获取项目级别配置信息与软件级别配置的差异
     * @param constructId
     * @return {Promise<undefined|{budget: (*[]|*)}>}
     */
    async getGlobalConfigDiff2Project(constructId){
        let globalConfig = this.getGlobalConfig();
        let constructProject = await PricingFileFindUtils.getProjectObjById(constructId);

        let diffProp = ObjectUtils.diff(globalConfig.budget, constructProject.projectConfig?.budget);

        if(!!diffProp){
            return {
                budget: diffProp
            }
        }else{
            return undefined;
        }
    }

    async isConfDiffBetweenProjectAndGlobal(constructId) {
        let diffProp = await this.getGlobalConfigDiff2Project(constructId);
        return !!diffProp;
    }

    /**
     * 获取软件配置中存在但项目配置中不存在的配置
     * @param constructId
     * @return {Promise<undefined|{budget: (*[]|*)}>}
     */
    async getConfigOnlyInGlobal(constructId){
        let globalConfig = this.getGlobalConfig();
        let constructProject = await PricingFileFindUtils.getProjectObjById(constructId);

        let diffProp = ObjectUtils.diffOnlyInA(globalConfig.budget, constructProject.projectConfig?.budget);

        if(!!diffProp){
            return {
                budget: diffProp
            }
        }else{
            return undefined;
        }
    }

    async isGlobalAddedNewConfig(constructId) {
        let diffProp = await this.getConfigOnlyInGlobal(constructId);
        return !!diffProp;
    }
}

GlobalConfigurationService.toString = () => '[class GlobalConfigurationService]';
module.exports = GlobalConfigurationService;
