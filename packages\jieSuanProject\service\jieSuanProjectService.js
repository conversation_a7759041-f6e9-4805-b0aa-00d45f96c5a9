'use strict';


const os = require('os');
const {dialog} = require('electron');
const ConstantUtil = require("../../../electron/enum/ConstantUtil");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {throws} = require("assert");
const {JieSuanFileUtils} = require("../utils/JieSuanFileUtils");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const JieSuanConstantUtil = require("../enum/JieSuanConstantUtil");
const {PricingFileWriteUtils} = require("../../../electron/utils/PricingFileWriteUtils");
const {JieSuanWinManageUtils} = require("../utils/JieSuanWinManageUtils");
const {Service} = require("../../../core");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const JieSuanSingleTypeEnum = require("../enum/JieSuanSingleTypeEnum");
const {SingleProject} = require("../../../electron/model/SingleProject");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const {UPCContext} = require("../../../electron/unit_price_composition/core/UPCContext");
const {arrayToTree} = require("../../../electron/main_editor/tree");
const {toJsonYsfString} = require("../../../electron/main_editor/util");
const JSZip = require("jszip");
const JieSuanRcjDifferenceEnum = require("../enum/JieSuanRcjDifferenceEnum");
const JieSuanPriceAdjustmentMethodEnum = require("../enum/JieSuanMethodEnum");
const {AdjustMethod} = require("../model/AdjustMethod");
const {ConstructOperationUtil} = require("../../../electron/utils/ConstructOperationUtil");
const JieSuanFeeSetEnum = require("../enum/JieSuanMethodEnum");
const {RcjDifferenceInfo} = require("../model/RcjDifferenceInfo");
const {JieSuanDifferencePrice} = require("../model/JieSuanDifferencePrice");
const {RcjApplicationContext} = require("./RcjContext");
const {RcjStageSetVo} = require("../model/RcjStageSetVo");
const {CryptoUtils} = require("../../../electron/utils/CrypUtils");
const {JieSuanRcjStageUtils} = require("../utils/JieSuanRcjStageUtils");
const BranchProjectLevelConstant = require("../../../electron/enum/BranchProjectLevelConstant");
const jbxx = require('../../jieSuanProject/jsonData/zhiBiao/jiesuan_zhibiao_jbxx.json');
const { GljProjectOverview } = require('../../gongLiaoJiProject/models/GljProjectOverview');
const { ConvertUtil } = require('../../gongLiaoJiProject/utils/ConvertUtils');
const { JieSuanProjectOverview } = require('../model/JieSuanProjectOverview');
const { JieSuanReferIndex } = require('../model/JieSuanReferIndex');
const OtherProjectCalculationBaseConstant = require('../../../electron/enum/OtherProjectCalculationBaseConstant');

/**
 * 示例服务
 * @class
 */
class JieSuanProjectService extends Service {

    constructor(ctx) {
        super(ctx);
        //this.itemBillProjectService =  this.service.jieSuanProject.itemBillProjectService;
    }

    //引用service
    jieSuanItemBillProjectService = this.service.jieSuanProject.jieSuanItemBillProjectService;
    jieSuanMeasureProjectTableService = this.service.jieSuanProject.jieSuanMeasureProjectTableService;
    unitProjectService=this.service.jieSuanProject.unitProjectService;
    projectTaxCalculationService = this.service.jieSuanProject.projectTaxCalculationService;
    constructProjectService = this.service.jieSuanProject.constructProjectService;
    jieSuanRcjStageService = this.service.jieSuanProject.jieSuanRcjStageService;
    jieSuanOtherProjectService = this.service.jieSuanProject.jieSuanOtherProjectService;
    jieSuanAutoCostMathService = this.service.jieSuanProject.jieSuanAutoCostMathService;


    /**
     * 打开选择文件路径框
     * @return
     */
    async openFileSelection(){
        let defaultStoragePath = await this.service.commonService.getSetStoragePath(null);
        const options = {
            properties: ['openFile'],
            defaultPath: defaultStoragePath, // 默认保存路径
            filters: [
                {name: '云算房', extensions: [ConstantUtil.YUSUAN_FILE_SUFFIX,ConstantUtil.YUSUAN_FILE_SUFFIX_Z]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtil.isEmpty(result)) throw new throws("文件路径不能为空");
        //获取选中的路径
        return  result[0];
    }


    async generateLevelTreeNodeStructure(arg){
        const result = await this.service.constructProjectService.generateLevelTreeNodeStructure(arg);
        let map = ConstructOperationUtil.flatConstructTreeToMapById(arg.sequenceNbr);
        result.forEach(k =>{
            let item = map.get(k.id);
            if(ObjectUtils.isNotEmpty(item.originalFlag)){
                k.originalFlag = item.originalFlag;
            }else {
                k.originalFlag = false;
            }
            k.viewScopeFlag=item.viewScopeFlag;
            //指标口径
            k.indexCountCaliber=item.indexCountCaliber;
            if (k.levelType == 1){
                k.jieSuanRcjDifferenceTypeList = item.jieSuanRcjDifferenceTypeList;
                k.riskAmplitudeRangeMin  = item.riskAmplitudeRangeMin;
                k.riskAmplitudeRangeMax  = item.riskAmplitudeRangeMax;
                let unitList = PricingFileFindUtils.getUnitList(k.id);
                const uniqueById = unitList.filter((item, index, self) =>
                    index === self.findIndex((t) => t.deStandardReleaseYear === item.deStandardReleaseYear)
                );
                let map1 = uniqueById.map(item => item.deStandardReleaseYear);
                //修改顺序
                if(map1.length==2 && map1[0] !== "12"){
                    map1.reverse();
                }
                const result = map1.join(',');
                k.tempReleaseYear  = result;
            }

            if (k.levelType == 2){
                k.type = item.type;
                k.riskAmplitudeRangeMax = item.riskAmplitudeRangeMax;
                k.riskAmplitudeRangeMin = item.riskAmplitudeRangeMin;
                let unitList = PricingFileFindUtils.getUnitListBySingle(arg.sequenceNbr,k.id);
                const uniqueById = unitList.filter((item, index, self) =>
                    index === self.findIndex((t) => t.deStandardReleaseYear === item.deStandardReleaseYear)
                );
                let map1 = uniqueById.map(item => item.deStandardReleaseYear);
                //修改顺序
                if(map1.length==2 && map1[0] !== "12"){
                    map1.reverse();
                }
                const result = map1.join(',');
                k.tempReleaseYear  = result;
                if (ObjectUtils.isEmpty(k.tempReleaseYear)){
                    k.tempReleaseYear = item.deStandardReleaseYear;
                }
            }
            if (k.levelType == 3){
                if(ObjectUtils.isEmpty(item.type)){
                    let singleProject = PricingFileFindUtils.getSingleProject(item.constructId,item.spId);
                    item.type=singleProject.type;

                }
                k.type = item.type;
                k.quantityDifferenceRangeMax = item.quantityDifferenceRangeMax;
                k.quantityDifferenceRangeMin = item.quantityDifferenceRangeMin;
                k.riskAmplitudeRangeMax = item.riskAmplitudeRangeMax;
                k.riskAmplitudeRangeMin = item.riskAmplitudeRangeMin;
                k.tempReleaseYear  = item.deStandardReleaseYear;
                k.parentProjectId  = item.parentProjectId;
                if( ObjectUtils.isEmpty(item.majorIndex)){
                    item.majorIndex=item.constructMajorType;
                    if(item.majorIndex=="建筑工程" || item.majorIndex=="装饰工程"){
                        item.majorIndex="建筑装饰工程"
                    }
                }
                k.majorIndex  = item.majorIndex;

            }

        });
        return result;
    }

    _treeToArray(tree,array){
        if (ObjectUtils.isNotEmpty(tree.children)){
            array.push(...tree.children);
            tree.children.forEach(k =>{
                this._treeToArray(k,array);
            })
        }
    }

    async handleSingleProject(arg){
        let {constructId,singleId,type,param} = arg;
        let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);
        singleProject.type = type;
        singleProject.originalFlag=false;
        if (ObjectUtils.isNotEmpty(param)){
            let array = new Array();
            this._treeToArray(param,array);
            let currentProjectObjMap = ConstructOperationUtil.flatConstructTreeToMapById(param.id);
            array.forEach(k =>{
                if (ObjectUtils.isNotEmpty(k.type)){
                    let single = currentProjectObjMap.get(k.id);
                    single.type = type;
                }
            })




        }



    }

    async handleRcj(args){
        let {constructId,singleId,unitId,rcj,param} = args;

        let keysRcj = [
            "materialName",
            "specification",
            "unit",
            "dePrice",
            "kind"
        ];

        //判断当前人材机是否是父级人材机
        let flag = true;
        if (ObjectUtils.isNotEmpty(rcj.rcjId)){
            flag = false;
        }
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        if (flag){
            unit.constructProjectRcjs.filter(k =>this.columnJoin(k,keysRcj) == this.columnJoin(rcj,keysRcj) && k.originalFlag == true).map(k =>{
                k.marketPrice = k.jieSuanMarketPrice;
            });
        }else {
            unit.rcjDetailList.filter(k =>this.columnJoin(k,keysRcj) == this.columnJoin(rcj,keysRcj) && k.originalFlag == true).map(k =>{
                k.marketPrice = k.jieSuanMarketPrice;
            });
        }

        //处理编码
        let rcjApplicationContext = new RcjApplicationContext({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId)});

        let constructProjectRcj = unit.constructProjectRcjs.find(k =>k.sequenceNbr == rcj.sequenceNbr);
        if (ObjectUtils.isNotEmpty(constructProjectRcj)){
            await rcjApplicationContext.materialCodeHandler(constructProjectRcj);
        }
        let rcjDetailList = unit.rcjDetailList.find(k =>k.sequenceNbr == rcj.sequenceNbr);
        if (ObjectUtils.isNotEmpty(rcjDetailList)){
            await rcjApplicationContext.materialCodeHandler(rcjDetailList);
        }




    }
    columnJoin(rcj,keysRcj) {
        let tempArray = [];
        for (const key of keysRcj) {
            if (ObjectUtils.isEmpty(rcj[key])) {
                tempArray.push(null);
            } else {
                tempArray.push(rcj[key]);
            }
        }
        return tempArray.sort().join(",");
    }

    async handleAddUnit(args){

        let {constructId,singleId,unitId} = args;


        //处理单位工程初始值
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        //量差范围初始值
        unit.quantityDifferenceRangeMax = 15;
        unit.quantityDifferenceRangeMin = -15;
        unit.originalFlag =false;
        let singleProject = PricingFileFindUtils.getSingleProject(constructId,unit.spId);
        unit.type=singleProject.type;
        unit.majorIndex=unit.constructMajorType;
        if(unit.majorIndex=="建筑工程" || unit.majorIndex=="装饰工程"){
            unit.majorIndex="建筑装饰工程";
        }
        // 初始化其他项目数据
        await this.jieSuanOtherProjectService.initOtherProjectAllData(args);

        //初始化调差方式
        this.rcjDifferenceUnit(args);


        //按顺序写
        //初始化费用代码
        await this.service.jieSuanProject.jieSuanUnitCostCodePriceService.initUnitCostCodePrice(args);
        //初始化费用汇总
        await this.service.jieSuanProject.jieSuanUnitCostSummaryService.initUnitCostSummary(args);

    }


    /**
     * 新建结算项目
     * @return {Promise<ResponseData>}
     */
    async creatJieSuanProJect(arg) {



        let {filePath} = arg;
        if (!JieSuanFileUtils.checkFileExistence(filePath) ||!JieSuanConstantUtil.YUSUAN_FILE_SUFFIX.includes(JieSuanFileUtils.pathSuffix(filePath))) throw new throws("文件路径不能为空");
        //项目数据处理
        //1.读取文件数据
        let obj = await JieSuanFileUtils.readLocalProjectData(filePath);
        //if (obj.deStandardReleaseYear == ConstantUtil.DE_STANDARD_22)return ResponseData.fail("不支持22定额");
        // if (obj.biddingType == 2){
        //     return ResponseData.fail("当前项目工程类型有误");
        // }
        //新建结算项目时，选择低于V1.0.38版本预算文件时，需提示用户“预算文件版本过低，请重新上传”
        if (JieSuanFileUtils.compareVersions(obj.version,JieSuanConstantUtil.TRRGET_VERSION)==-1) {
            // console.log("预算文件版本过低，请重新上传");
            return ResponseData.fail("预算文件版本过低，请重新上传");
        }
        let defaultStoragePath = await this.service.commonService.getSetStoragePath(obj.constructName);
        const dialogOptions = {
            title: '保存文件',
            defaultPath: defaultStoragePath.replace(/\.YSF(D|Z)?$/, '.YJS').toString(),
            filters: [{ name: '云算房文件', extensions: [JieSuanConstantUtil.JIESUAN_FILE_SUFFIX] }]
        };
        let path = dialog.showSaveDialogSync(null, dialogOptions);
        if (!path)new throws("保存路径不能为空");

        // 在这里处理保存文件的操作
        if (!path.endsWith("."+JieSuanConstantUtil.JIESUAN_FILE_SUFFIX)) {
            path += "."+JieSuanConstantUtil.JIESUAN_FILE_SUFFIX;
        }
        //获取结算项目名称
        const fullName = path.split("\\").pop(); // "投标项目.YSF"
        const nameWithoutExt = fullName.split(".")[0]; // "投标项目"
        if(obj.constructName!=nameWithoutExt){
            obj.constructName=nameWithoutExt;
        }
        //设置结算存储路径
        obj.path = path;
        obj.ysConstructId = obj.sequenceNbr;
        obj.sequenceNbr = Snowflake.nextId();
        obj.columnView = null;
        obj.biddingType=JieSuanConstantUtil.jsProject;
        //obj.sequenceNbr = Snowflake.nextId();
        ObjectUtils.updatePropertyValue(obj, 'constructId', obj.sequenceNbr)
        //2.用户的打开历史记录列表数据处理
        obj = JieSuanFileUtils.writeUserHistoryListFile(obj);
        //3.将项目数据写入到内存当中
        PricingFileWriteUtils.writeToMemory(obj);
        //TODO bug 使用了原来的数据  upccontext中没有新项目的id
        if(obj.UPCContext){
            // this.handleUPCContextSequenceNbr(obj);
            UPCContext.load(obj.UPCContext);
        }
        PricingFileFindUtils.getUnitList(obj.sequenceNbr).forEach(k =>{
            k.itemBillProjects = arrayToTree(k.itemBillProjects);
            k.measureProjectTables = arrayToTree(k.measureProjectTables);
        });
        //数据初始化
        await this.init(obj);
        //将项目数据写入到结算文件当中
        let jsonYsfString = toJsonYsfString(obj);
        await JieSuanFileUtils.createProjectFile(obj.path, {"file.json": jsonYsfString});
        CryptoUtils.objectHash(jsonYsfString, obj.sequenceNbr, true);
        //创建窗口
        JieSuanWinManageUtils.createWindow(obj.constructName, obj.sequenceNbr);

        return ResponseData.success(obj.sequenceNbr);
    }


    /**
     * 处理工程项目sequenceNbr发生变化后  UPCContext中原来的sequenceNbr相关数据也需要发生变化
     */
    async handleUPCContextSequenceNbr(obj) {
        if (ObjectUtils.isEmpty(obj) || ObjectUtils.isEmpty(obj.UPCContext)) {
            return;
        }
        const unitList = PricingFileFindUtils.getUnitList(obj.sequenceNbr);
        if (ObjectUtils.isEmpty(unitList)) {
            return;
        }

        function copyNewValue(sourceObj, targetObj, oldSequenceNbrStr, newSequenceNbrStr) {
            for (const [key, value] of Object.entries(sourceObj)) {
                if (key.includes(oldSequenceNbrStr)) {
                    targetObj[key.replace(oldSequenceNbrStr, newSequenceNbrStr)] = ObjectUtils.cloneDeep(value);
                }
            }
        }

        for (const unit of unitList) {
            const oldSequenceNbrStr = obj.oldSequenceNbr + ',' + unit.sequenceNbr;
            const newSequenceNbrStr = obj.sequenceNbr + ',' + unit.sequenceNbr;
            if (ObjectUtils.isNotEmpty(obj.UPCContext.qfCodeMap)) {
                copyNewValue(obj.UPCContext.qfCodeMap, obj.UPCContext.qfCodeMap, oldSequenceNbrStr, newSequenceNbrStr);
            }
            if (ObjectUtils.isNotEmpty(obj.UPCContext.feeFileMap)) {
                copyNewValue(obj.UPCContext.feeFileMap, obj.UPCContext.feeFileMap, oldSequenceNbrStr, newSequenceNbrStr);
            }
            if (ObjectUtils.isNotEmpty(obj.UPCContext.incrementTemplateListMap)) {
                if (ObjectUtils.isEmpty(obj.UPCContext.imitationTemplateListMap)) {
                    obj.UPCContext.imitationTemplateListMap = {};
                }
                copyNewValue(obj.UPCContext.incrementTemplateListMap, obj.UPCContext.imitationTemplateListMap, oldSequenceNbrStr, newSequenceNbrStr);
            }
        }
    }




    /**
     * 打开结算项目
     * @return {Promise<ResponseData>}
     */
    async openJieSuanProJect(args) {
        let {path} = args;
        if (!JieSuanFileUtils.checkFileExistence(path) || !JieSuanConstantUtil.YUSUAN_FILE_SUFFIX.includes(JieSuanFileUtils.pathSuffix(filePath))) throw new throws("文件路径有误");
        //1.读取文件数据
        let obj = await JieSuanFileUtils.readLocalProjectData(path);

        // if(obj.UPCContext){
        //     UPCContext.load(obj.UPCContext);
        // }
        // PricingFileFindUtils.getUnitList(obj.sequenceNbr).forEach(k =>{
        //     k.itemBillProjects = arrayToTree(k.itemBillProjects);
        //     k.measureProjectTables = arrayToTree(k.measureProjectTables);
        // });

        //2.刷新用户的历史记录
        obj = JieSuanFileUtils.writeUserHistoryListFile(obj);
        //3.将项目数据写入到内存当中
        PricingFileWriteUtils.writeToMemory(obj);
        //创建窗口
        JieSuanWinManageUtils.createWindow(obj.constructName, obj.sequenceNbr);
        return ResponseData.success();
    }


    /**
     * 新建结算项目后 数据初始化
     * @return {Promise<void>}
     */
    async init(obj) {

        obj.columnView = null;

        let projectMap = ConstructOperationUtil.flatConstructTreeToMapById(obj.sequenceNbr);
        let itemList = Array.from(projectMap.values());
        for (let i = 0; i < itemList.length; i++) {
            let item = itemList[i]
            //设置单位单项原始数据标识
            item.originalFlag = true;
            //指标查看范围
            item.viewScopeFlag = true;

            //工程项目
            if (item.levelType == 1){
                //初始化人材机调整类型
                //this.jieSuanRcjStageService.initRcjAdjustMethod(item);
                //设置初始化 浮动范围
                item.riskAmplitudeRangeMin = -5;
                item.riskAmplitudeRangeMax = 5;
                JieSuanRcjStageUtils.initJieSuanFee(item);
                //初始化指标分析
                await  this.service.jieSuanProject.jieSuanMajorIndexService.initIndexFenXi(item.sequenceNbr);
                item.indexCountCaliber = 0;
                item.zbUnit = 'm²';
                //初始化工程项目下 工程信息, 参考指标
                this.initDataProject(obj);
            }

            //单项
            if (item.levelType == 2){
                //let args = {constructId:obj.sequenceNbr,singleId:item.spId, unitId:item.sequenceNbr};
                //this.jieSuanRcjStageService.initRcjAdjustMethod(item);
                //设置初始化 浮动范围
                item.riskAmplitudeRangeMin = -5;
                item.riskAmplitudeRangeMax = 5;
                JieSuanRcjStageUtils.initJieSuanFee(item);
                item.indexCountCaliber = 0;
                item.zbUnit = 'm²';
            }
            //单位
            if (item.levelType == 3){
                //清空颜色设置和颜色筛选条件
                item.colorList=null;
                item.checkColorList=null;
                item.screenCondition=null;
                item.indexCountCaliber = 0;

                let args = {constructId:obj.sequenceNbr,singleId:item.spId, unitId:item.sequenceNbr};

                //初始化分部分项数据
                await this.jieSuanItemBillProjectService.initYsToJieSuanFbfxData(args);
                //初始化措施项目
                await this.jieSuanMeasureProjectTableService.initYsfToJieSuanCsxmData(args);

                // 初始化其他项目数据
                await this.jieSuanOtherProjectService.initOtherProjectAllData(args);

                //初始化调差方式
                this.rcjDifferenceUnit(args);

                //初始化所有人材机数据
                this.rcjInit(item,true);
                //初始化分期设置
                this.initStageSet(item)
                JieSuanRcjStageUtils.initJieSuanFee(item);
                //初始化费用代码——备份
                await this.service.jieSuanProject.jieSuanUnitCostSummaryService.initUnitCostSummaryBak(args);
                //初始化费用代码
                await this.service.jieSuanProject.jieSuanUnitCostCodePriceService.initUnitCostCodePrice(args);
                //初始化费用汇总
                await this.service.jieSuanProject.jieSuanUnitCostSummaryService.initUnitCostSummary(args);



            }
        }



        //初始化合同外单项
        this.initSingle(obj.sequenceNbr);

        // //初始化所有的单位量差范围值    风险幅度范围值
        // this.unitProjectService.initAllUnitQuantityDifferenceRange(obj.sequenceNbr, 15, -15);
        // //风险幅度范围值
        // this.unitProjectService.initAllUnitRiskAmplitudeRange(obj.sequenceNbr, 5, -5);
        // //初始化调差方式
        // this.rcjDifference(obj);
        // //初始化人材机调整类型
         //this.jieSuanRcjStageService.initPriceDifferenceAdjustmentMetho(obj);
        // obj.riskAmplitudeRangeMin=-5;
        // obj.riskAmplitudeRangeMax=5;
        // //获取所有的单项  初始化单位的信息
        // let singleProjectList = obj.singleProjects;
        // //循环获取单位
        // for (const singleProject of singleProjectList) {
        //     let unitProjects = singleProject.unitProjects;
        //     //设置单位单项原始数据标识
        //     singleProject.originalFlag = true;
        //     //获取单位中的分部分项数据
        //     if (!ObjectUtils.isEmpty(unitProjects)) {
        //         for (const unitProject of unitProjects) {
        //             unitProject.cgCostMathCache= null;// 超高记取参数缓存
        //             unitProject.cyCostMathCache= null;// 垂运记取参数缓存
        //             unitProject.azCostMathCache= null;// 安装记取参数缓存
        //             //其他项目初始化
        //             this.service.jieSuanProject.jieSuanOtherProjectService.initJieSuanUnitOtherProjectList(unitProject)
        //             if(ObjectUtils.isEmpty(unitProject.unitJBXX) && ObjectUtils.isEmpty(unitProject.unitGCTZ)){
        //                 //创建单位级别的工程基本信息和工程特征
        //                 this.projectTaxCalculationService.initProjectOrUnitData(unitProject, 3);
        //             }
        //             if(ObjectUtils.isEmpty(unitProject.organizationInstructions)) {
        //                 //初始化编制说明
        //                 this.projectTaxCalculationService.initProjectOrUnitBZSM(3, unitProject);
        //             }
        //             if(ObjectUtils.isEmpty(unitProject.projectTaxCalculation)){
        //                 //初始化单位的计税方式
        //                 await this.projectTaxCalculationService.initUnitTaxCalculationMethod(obj, unitProject);
        //             }
        //             // if(ObjectUtils.isEmpty(unitProject.unitCostCodePrices)){
        //                 //初始化费用代码
        //                 this.constructProjectService.initUnitCostCodePrice(unitProject);
        //             // }
        //             // if(ObjectUtils.isEmpty(unitProject.unitCostSummarys)){
        //                 //初始化费用汇总
        //                 this.constructProjectService.initUnitCostSummary(unitProject);
        //             // }
        //             if(ObjectUtils.isEmpty(unitProject.unitInputTaxAmounts)){
        //                 //一般计税
        //                 let projectTaxCalculation = unitProject.projectTaxCalculation;
        //                 if(projectTaxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.GENERAL.code){
        //                     // 初始化进项税额明细
        //                     this.constructProjectService.initUnitInputTaxAmount(unitProject);
        //                 }
        //             }
        //         }
        //     }
        // }
    }

    /**
     * 人材机数据初始化
     */
    rcjInit(unit,flag){
        // if (ObjectUtil.isNotEmpty(unit.constructProjectRcjs))this.rcjDataHandler(unit.constructProjectRcjs)
        // if (ObjectUtil.isNotEmpty(unit.rcjDetailList))this.rcjDataHandler(unit.rcjDetailList);
        if (ObjectUtil.isNotEmpty(unit.constructProjectRcjs))JieSuanRcjStageUtils.rcjDataHandler(unit.constructProjectRcjs,flag,unit)
        if (ObjectUtil.isNotEmpty(unit.rcjDetailList))JieSuanRcjStageUtils.rcjDataHandler(unit.rcjDetailList,flag,unit);
    }

    rcjDifference(obj){
        // let unitList = PricingFileFindUtils.getUnitList(obj.sequenceNbr);
        // for (let unit of unitList) {
        //     let array = new Array();
        //     let rcjDifferenceSetList = [JieSuanRcjDifferenceEnum.RENGONG.code,JieSuanRcjDifferenceEnum.CAILIAO.code
        //         ,JieSuanRcjDifferenceEnum.ZANGUJIA.code,JieSuanRcjDifferenceEnum.JIXIE.code];
        //     for (const item of rcjDifferenceSetList) {
        //
        //         let rcjDifferenceSetVo = new AdjustMethod();
        //         rcjDifferenceSetVo.kind = item;
        //         //人材机调整类型
        //         rcjDifferenceSetVo.rcjDifferenceType = JieSuanPriceAdjustmentMethodEnum.METHOD1.code;
        //         rcjDifferenceSetVo.rcjPeriodsSet = 1;
        //         rcjDifferenceSetVo.frequencyList = [];
        //         array.push(rcjDifferenceSetVo);
        //     }
        //     unit.rcjDifference = array;
        // }
    }


    rcjDifferenceUnit(obj){
        let unit = PricingFileFindUtils.getUnit(obj.constructId,obj.singleId,obj.unitId);
        //初始化单位调整法
        this.jieSuanRcjStageService.initRcjAdjustMethod(unit);
    }




    //结算中新增人材机初始化
    jszNewRcjDataHandler(rcjList){

        rcjList.forEach( k =>{
            //结算打开后 新增数据 为 false原始数据
            k.originalFlag = false;
            //风险幅度设置
            k.riskAmplitudeRangeMin = -5;
            k.riskAmplitudeRangeMax = 5;
            //取费
            k.jieSuanFee = JieSuanFeeSetEnum.METHOD3.code;
            //人材机四种调整法默认值设置
            //调整法
            let adjustmentMethodList = new Array();
            for (let i = 0; i < 4; i++) {
                let rcjDifferenceInfo = new RcjDifferenceInfo();
                rcjDifferenceInfo.rcjDifferenceType = i+1;//人材机调整类型
                rcjDifferenceInfo.jieSuanBasePrice = k.marketPrice;//结算基期价默认值
                let jieSuanDifferencePrice = new JieSuanDifferencePrice();
                jieSuanDifferencePrice.jieSuanPrice = k.marketPrice;//结算单价默认值
                jieSuanDifferencePrice.jieSuanPriceSource = k.marketSourcePrice;//结算单价来源
                rcjDifferenceInfo.jieSuanDifferencePriceList = [jieSuanDifferencePrice];//单价信息集合
                adjustmentMethodList.push(rcjDifferenceInfo);
            }
            k.jieSuanRcjDifferenceTypeList = adjustmentMethodList;
            //结算除税系数
            k.jieSuanTaxRemoval = k.taxRemoval;
            //结算合计数量
            k.jieSuanTotalNumber = k.totalNumber;
            //结算合价
            k.jieSuanTotal = k.total;
            //结算备份市场价
            k.jieSuanMarketPrice = k.marketPrice;

            //第n期除税系数
            k.jieSuanStagetaxRemovalList = [];
            //结算第n期单位价差
            k.jieSuanUnitPriceDifferencList = [];
            //变值权重B分期数
            k.jieSuanStageValuetWeightB = [];


            //结算单位价差
            k.jieSuanPriceDifferenc = 0;
            //过滤出人材机中的人工，并且编码为10000001、10000002、10000003，即综合用工一类、综合用工二类、综合用工三类设置调差默认值
            let tempMaterialCode = k.materialCode.includes('#')?k.materialCode.split('#')[0]:k.materialCode;
            if (["10000001", "10000002", "10000003"].includes(tempMaterialCode) && k.kind === 1){
                k.isDifference = true;
            }
        });


    }



    /**
     * 初始化单位工程
     */
    initSingle(constructId){
        // let singleProjectList = PricingFileFindUtils.getSingleProjectList(obj.sequenceNbr);
        //
        // if (ObjectUtil.isEmpty(singleProjectList)){
        //     singleProjectList = [];
        // }
        for (let singleTypeEnum in JieSuanSingleTypeEnum) {
            let code = JieSuanSingleTypeEnum[singleTypeEnum].code;
            let value = JieSuanSingleTypeEnum[singleTypeEnum].value;
            let arg = {constructId:constructId,singleName:value};
            const singleProject = this.service.singleProjectService.addSingleProject(arg,false);
            singleProject.type = code;
            singleProject.originalFlag = false;
        }
    }



    /**
     * 费用代码   费用记取统一调用接口
     */
    async countFeeCodeAndMathFee(args){
        let {constructId,singleId,unitId,unitWorkId} = args;
        if(ObjectUtils.isEmpty(unitId)){
            args["unitId"]=unitWorkId;
        }
        if(ObjectUtils.isNotEmpty(singleId) && ObjectUtils.isNotEmpty(unitId)){
            // 费用记取
            await this.jieSuanAutoCostMathService.autoCostMath(args);
        }
        //费用代码记取
        args.levelType=1;
        await this.service.jieSuanProject.jieSuanUnitCostCodePriceService.countCostCodePrice(args);
    }


    initStageSet(unit) {
        let rcjStageSetVo = new RcjStageSetVo();
        rcjStageSetVo.isStage = false;
        rcjStageSetVo.periods = 0;
        rcjStageSetVo.stageType = 1;
        //单位分期设置
        unit.rcjStageSet = rcjStageSetVo;

    }




    /**
     * 批量删除子目-查询列表
     */
    async jieSuanBatchDelBySeachList(args){
        let {constructId, singleId, unitId, scopeType,showType,code,name} = args;
        let projectObj = await PricingFileFindUtils.getProjectObjById(constructId);
        let  result = [];
        if(scopeType != 1){
            let projectObj = await PricingFileFindUtils.getProjectObjById(constructId);
            // result = await this.service.constructProjectService.generateLevelTreeNode(projectObj)
            args.sequenceNbr=constructId;
            result = await this.service.jieSuanProject.jieSuanProjectService.generateLevelTreeNodeStructure(args);
            result=result.filter(item=>{
                item.sequenceNbr = item.id;
                if(!item.originalFlag){
                    return true;
                }
            });
        }
        let unitList =[];
        if (scopeType == 1){
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            unitList.push(unit);
        }else {
            unitList = PricingFileFindUtils.getUnitList(constructId);
            unitList=unitList.filter(unit=>{if(!unit.originalFlag){return true}});
        }
        for (let unit of unitList) {
            let {constructId, spId, sequenceNbr} = unit;
            let fbFx = PricingFileFindUtils.getFbFx(constructId, spId, sequenceNbr);
            let csxm = PricingFileFindUtils.getCSXM(constructId, spId, sequenceNbr);
            let f = (item)=>{
                if(item.kind != BranchProjectLevelConstant.de&&!item.children.length)return  false;
                if(item.tempDeleteFlag) return false;
                if (item.kind == BranchProjectLevelConstant.de){
                    if (ObjectUtils.isNotEmpty(name)){
                        if (!item.bdName.includes(name)) return false;
                    }
                    if (ObjectUtils.isNotEmpty(code)){
                        if (!item.bdCode.includes(code)) return false;
                    }
                }
                return true;
            }
            //
            let fbFxAllData = fbFx.flattenTree(fbFx.root, f);
            let filterArr=[];
            let csxmAllData = csxm.flattenTree(csxm.root,(item)=>{
                if(item.constructionMeasureType&&item.constructionMeasureType==2){
                    filterArr.push(item.sequenceNbr);
                    return false
                }
                if(filterArr.includes(item.parentId)){
                    filterArr.push(item.sequenceNbr);
                    return false
                }
                return f(item);
            });
            //分部分项
            for (let item of fbFxAllData) {
                if(item.parentId=="0"){
                    item.parentId = sequenceNbr;
                }
                item.pageType="fbfx";
                result.push(item);
            }
            //措施项目
            for (let item of csxmAllData) {
                if(item.parentId=="0"||!item.parentId){
                    item.parentId = sequenceNbr;
                }
                item.pageType="csxm";
                result.push(item);
            }
        }
        return result;
    }


    async initUnitData(item){
        //清空颜色设置和颜色筛选条件
        item.colorList=null;
        item.checkColorList=null;
        item.screenCondition=null;

        let args = {constructId:item.constructId,singleId:item.spId, unitId:item.sequenceNbr};

        //初始化分部分项数据
        await this.jieSuanItemBillProjectService.initYsToJieSuanFbfxData(args);
        //初始化措施项目
        await this.jieSuanMeasureProjectTableService.initYsfToJieSuanCsxmData(args);

        // 初始化其他项目数据
        await this.jieSuanOtherProjectService.initOtherProjectAllData(args);

        //初始化调差方式
        this.rcjDifferenceUnit(args);

        //初始化所有人材机数据
        this.rcjInit(item,false);
        //初始化分期设置
        this.initStageSet(item)
        JieSuanRcjStageUtils.initJieSuanFee(item);


        //初始化费用代码——备份
        await this.service.jieSuanProject.jieSuanUnitCostSummaryService.initUnitCostSummaryBak(args);
        //初始化费用代码
        await this.service.jieSuanProject.jieSuanUnitCostCodePriceService.initUnitCostCodePrice(args);
        //初始化费用汇总
        await this.service.jieSuanProject.jieSuanUnitCostSummaryService.initUnitCostSummary(args);
    }

    initDataProject(project) {
        let list = [];
        let map = new Map();
        let oldJBXX = project.constructProjectJBXX;
        for (let i in jbxx) {
            jbxx[i].type = 0;
            jbxx[i].addFlag = 0;
            jbxx[i].lockFlag = 0;
            let listProjectOverviewXX = new JieSuanProjectOverview();
            ConvertUtil.setDstBySrc(jbxx[i], listProjectOverviewXX);
            if ('基本信息' === jbxx[i].name || '招标信息' === jbxx[i].name ||'投标信息' === jbxx[i].name) {
                listProjectOverviewXX.requiredFlag = 0;
            }else{
                listProjectOverviewXX.requiredFlag = 1;
            }
            listProjectOverviewXX.sequenceNbr = Snowflake.nextId();

            let item = oldJBXX.find(item => item.name === jbxx[i].name);
            if (ObjectUtils.isNotEmpty(item)){
                listProjectOverviewXX.remark = item.remark;
            }
            list.push(listProjectOverviewXX);
        }
        project.constructProjectJBXX = list;
        let jieSuanReferIndex = new JieSuanReferIndex();
        jieSuanReferIndex.projectType = '居住建筑';
        jieSuanReferIndex.buildType = '住宅';
        jieSuanReferIndex.costType = '结算价';
        jieSuanReferIndex.strType = '框架剪力墙';
        jieSuanReferIndex.madeTime = '最近六个月';
        jieSuanReferIndex.buildUnit = '';
        jieSuanReferIndex.indexCountCaliber = '';
        jieSuanReferIndex.buildHeight = '';
        project.jieSuanReferIndex = jieSuanReferIndex;
    }
}

JieSuanProjectService.toString = () => '[class JieSuanProjectService]';
module.exports = JieSuanProjectService;
