<!--
 * @Descripttion: 
 * @Author: 
 * @Date: 2024-07-05 15:09:05
 * @LastEditors: wangru
 * @LastEditTime: 2025-06-16 16:17:08
-->
<!--
 * @Descripttion: 编制页面
 * @Author: renmingming
 * @Date: 2023-05-16 14:09:29
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-02-17 14:16:13
-->
<template>
  <a-spin
    :spinning="projectStore.globalLoading.loading"
    :tip="projectStore.globalLoading.info"
    wrapperClassName="spin-yyy"
  >
    <!--  !!useSlots().default  控制是否切换到电子标功能，如果切换到 怎展示slot 下的组件 -->
    <slot name="default" v-if="!!useSlots().default"></slot>
    <operate
      v-if="!useSlots().default"
      @executeCommand="executeCommand"
      @showLoadMould="showLoadMould"
    />
    <!--    <operate-->
    <!--      @executeCommand="executeCommand"-->
    <!--      @showLoadMould="showLoadMould"-->
    <!--    />-->
    <section>
      <split
        :horizontal="false"
        :ratio="isExpand ? '1/8' : `47/${winWidth}`"
        :minHorizontalTop="isExpand ? 225 : 47"
        :maxHorizontalTop="isExpand ? 400 : 47"
        :isDrop="isExpand && biddingType !== 2 ? true : false"
        :onlyPart="
          biddingType === 2 || projectStore.standardGroupOpenInfo.isOpen ? 'Bootom' : 'all'
        "
        style="height: 100%"
        mode="vertical"
      >
        <template #one v-show="!projectStore.standardGroupOpenInfo.isOpen">
          <aside
            class="aside"
            :style="isExpand ? { minWidth: '225px' } : { width: '47px' }"
            v-if="treeListCache && treeListCache[0].biddingType !== 2"
          >
            <icon-font
              v-if="projectStore?.currentTreeGroupInfo?.optionLock"
              class="option-lock"
              type="icon-qingdan-suoding"
            ></icon-font>
            <a-tooltip>
              <template #title>{{ !isExpand ? '展开' : '收起' }}操作栏</template>
              <div class="btnExpand">
                <div
                  class="btn"
                  :style="{
                    right: !isExpand ? '-7px' : '11px',
                    zIndex: 9,
                  }"
                  @click="isExpand = !isExpand"
                >
                  <img
                    :src="!isExpand ? getUrl('expandnew.png') : getUrl('retractnew.png')"
                    alt=""
                  />
                </div>
              </div>
            </a-tooltip>
            <aside-tree
              ref="asideTreeRef"
              :isExpand="isExpand"
              :treeData="treeData"
              :treeListCache="treeListCache"
              @getTreeList="getTreeList"
              @upOrDown="upOrDown"
              @drop="drop"
            />
          </aside>
        </template>
        <template #two>
          <main style="width: 100%">
            <main-content v-if="projectStore.mainContentRefresh" ref="mainRontentRef" />
          </main>
        </template>
      </split>
      <info-modal
        v-model:infoVisible="showInfoStatus"
        :infoText="showInfoText"
        :isSureModal="false"
        @update:infoVisible="close"
        @updateCurrentInfo="showEdit"
      ></info-modal>

      <zj-mould ref="zjMouldRef" @onSuccess="onSuccessZj"></zj-mould>
      <djgc-mould
        ref="zjMouldFYHZRef"
        @onSuccess="onSuccessZj"
        :saveData="saveApiData"
      ></djgc-mould>
      <div class="open-beta-btn" v-if="showOpenBeta">
        <CloseCircleFilled @click="showOpenBeta = false" class="icon" />
        <img class="img" src="@/assets/img/hot-red.png" alt="" />
        <img class="qrcode-img" @click="openDialog" src="@/assets/img/hot-qrcode.png" alt="" />
      </div>
      <beta-recommend
        v-model:visible="recommendVisible"
        @cancel="recommendVisible = false"
      ></beta-recommend>
    </section>
    <footer>
      <PricePanel
        :priceList="priceList"
        style="width: calc(100% - 200px)"
      />
      <TableScaleOpt />
    </footer>
  </a-spin>
  <ConstructReadOnlyDialog ref="constructReadOnlyRef"></ConstructReadOnlyDialog>
</template>

<script setup>
import { generateLevelTreeNodeStructureSH } from '@/api/budgetReview';
import { constructLevelTreeStructureList } from '@/api/csProject';
import { getJsAsideTreeList } from '@/api/jiesuanApi';
import ConstructReadOnlyDialog from '@/components/ConstructReadOnlyDialog/index.vue';
import split from '@/components/split/index.vue';
import TableScaleOpt from '@/components/TableScaleOpt/index.vue';
import { useCheckBefore } from '@/hooks/useCheckBefore';
import { projectDetailStore } from '@/store/projectDetail';
import { getUrl } from '@/utils/index';
import BetaRecommend from '@/views/csProject/header/betaRecommend.vue';
import { CloseCircleFilled } from '@ant-design/icons-vue';
import { message, Spin } from 'ant-design-vue';
import { h, onBeforeUnmount, onMounted, reactive, ref, useSlots, watch, watchEffect } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import xeUtils from 'xe-utils';
import csProject from '../../../api/csProject';
import api from '@/api/projectDetail';
import infoMode from '@/plugins/infoMode.js';
import { proModelStore } from '../../../store/proModel.js';
import AsideTree from './AsideTree.vue';
import MainContent from './MainContent.vue';
import operate from './operate.vue';
import PricePanel from './PricePanel.vue';

const { checkUnitOfPro, checkUnit, showInfo } = useCheckBefore();
const isExpand = ref(true);
const projectStore = projectDetailStore();
const ModelStore = proModelStore();
let asideTreeRef = ref(null);
let recommendVisible = ref(false);
let showOpenBeta = ref(true);
const router = useRouter();

const mainRontentRef = ref();
let treeData = ref();
let treeListCache = ref();
const route = useRoute();
projectStore.SET_TYPE(route.query?.type || 'ys');
const showInfoStatus = ref(false);
let showInfoText = ref('');
let priceList = ref([]); //底部数据列表
let timer = ref(); //获取底部费用循环

Spin.setDefaultIndicator({
  indicator: h('img', {
    // src: `${getUrl('jijialoading.gif')}`,
    src: 'https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/jijiasoft/jijialoading.gif',
    style: {
      width: '130px',
      height: '130px',
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50% , -80%)',
    },
  }),
});

//更新招标书中，在点击时 进行此校验
const isItVerified = async () => {
  const { id, levelType } = projectStore.currentTreeInfo;
  const res = await csProject.getConstructUnitListTypeByConstructId({
    constructId: id,
  });
  showInfoStatus.value = !res.result;
  projectStore.asideTreeRef = asideTreeRef.value;
  showInfoText.value = '当前工程项目存在未设置工程专业的单位工程，请完成设置后查看';
  return !res.result;
};

watchEffect(async () => {
  // if (store.currentTreeInfo) {
  if (projectStore.currentTreeInfo?.levelType === 1) {
    showInfoStatus.value = ModelStore.proInfoModel;
  } else if (projectStore.currentTreeInfo?.levelType === 3) {
    showInfoStatus.value = ModelStore.onInfoModal;
  } else {
    showInfoStatus.value = false;
  }
  // showInfoStatus.value = ModelStore.onInfoModal;
  projectStore.asideTreeRef = asideTreeRef.value;
  showInfoText.value =
    projectStore.currentTreeInfo?.levelType === 1
      ? '当前工程项目存在未设置工程专业的单位工程，请完成设置后查看'
      : '当前单位工程未设置专业，请前往设置专业';
});
let winWidth = ref();

watch(
  () => isExpand.value,
  () => {
    if (!isExpand.value) {
      winWidth.value = window.innerWidth;
    }
  }
);
watch(
  () => projectStore.isRefreshProjectTree,
  async () => {
    if (projectStore.isRefreshProjectTree) {
      getTreeList();
    }
  }
);
const close = () => {
  // showInfoStatus.value = false;
  ModelStore.onInfoModal = false;
  ModelStore.proInfoModel = false;
};

const openDialog = () => {
  recommendVisible.value = true;
};

// 打开提示文字
const showEdit = () => {
  ModelStore.onInfoModal = false;
  ModelStore.onEditModal(true);
};
//获取底部数据
const getFooterValues = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.levelType === 3 ? projectStore.currentTreeInfo?.id : '',
    constructId: route.query.constructSequenceNbr,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  csProject.getBottomSummary(apiData).then(res => {
    if (res && res.status === 200 && res.result) {
      priceList.value = [
        {
          name: '工程造价（含设备费及税金）',
          value: res.result.zzj,
        },
        {
          name: '人工费',
          value: res.result.rgf,
        },
        {
          name: '机械费',
          value: res.result.jxf,
        },
        {
          name: '主材费',
          value: res.result.zcf,
        },
        {
          name: '材料费',
          value: res.result.clf,
        },
        {
          name: '管理费',
          value: res.result.glf,
        },
        {
          name: '利润',
          value: res.result.lr,
        },
        {
          name: '直接工程费',
          value: res.result.zjgcf,
        },
        {
          name: '设备费及税金',
          value: res.result.sbfsj,
        },
      ];
    } else {
      priceList.value = [];
    }
  });
};

// 新增配置提示
const isGlobalConfigDiff = async constructId => {
  projectStore.SET_GLOBAL_LOADING({
    loading: true,
    info: '',
  });
  const res = await api.isGlobalAddedNewConfig({ constructId });
  console.log('🚀 ~ api.confDiffBetweenProjectAndGlobal ~ res:', res);
  projectStore.SET_GLOBAL_LOADING({
    loading: false,
    info: '',
  });
  if (res.result) {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-qiangtixing',
      infoText: '文件格式更新，历史文件已保存至XXX位置',
      confirm: () => {
        infoMode.hide();
      },
    });
  }
};
onMounted(async () => {
  // getBSToken();
  // nextTick(() => {
  // debugger;
  let newConstructList = JSON.parse(localStorage.getItem('constructSequenceNbr')) || {};
  let newConstructId = newConstructList[route.query.constructSequenceNbr];
  if (newConstructId) {
    route.query.constructSequenceNbr = newConstructId;
    let query = {
      constructSequenceNbr: newConstructId,
    };
    router.push({
      path: route?.path || '/projectDetail/customize',
      query: { ...query },
    });
    projectStore.isFileSaveAs = true;
  }
  console.log(route, route.query.constructSequenceNbr);
  await isGlobalConfigDiff(route.query.constructSequenceNbr);
  getTreeList();
  projectStore.SET_PRO_CHECK_TAB(null); //此处初始化tab的点击记忆
  projectStore.INIT_GLOBAL_SETTING_INFO(route.query.constructSequenceNbr);
  await projectStore.NEW_INIT_GLOBAL_SETTING_INFO(route.query.constructSequenceNbr);
  getConstructConfigByConstructId();
  getFooterValues();
  startTimers();
  window.addEventListener('keydown', save);
});

const startTimers = () => {
  timer.value = setInterval(() => {
    getFooterValues();
  }, 2000);
};
watch(
  () => projectStore.startMatch,
  (newVal, oldVal) => {
    if (newVal) {
      clearTimers();
    } else {
      startTimers();
    }
  }
);
watch(
  () => projectStore.currentTreeInfo,
  () => {
    //切换侧边左侧树页刷新底部数据
    getFooterValues();
  }
);
// const getBSToken = async () => {
//   let payload = {
//     mobile: '15000000021',
//     sms_code: '123123',
//     loginChannel: 'PLATFORM_DEFAULT',
//     grant_type: 'sms',
//     scope: 'all',
//   };
//   login(payload).then(async res => {
//     const bsToken = await signIn(res.access_token.replace(/bearer\s+/g, ''));
//     console.log('bsToken', process.env.NODE_ENV, bsToken);
//   });
// };
let childItemGetList = ref(null);
let pageType = ref(null);

watchEffect(() => {
  let data = mainRontentRef.value?.childComponentRef;
  let componentId = mainRontentRef.value?.componentId;
  console.log('componentId', componentId);
  switch (componentId) {
    case 'summaryExpense':
      pageType.value = 'fyhz';
      break;
    case 'measuresItem':
      pageType.value = 'csxm';
      break;
    case 'qtxmStatistics':
      pageType.value = 'qtxm';
      break;
    default:
      pageType.value = '';
      break;
  }

  // subItemProjectAutoPosition不应该是组件单独得一个方法，暂时注释
  // if (!projectStore.subItemProjectAutoPosition && data?.posRow) {
  //   projectStore.subItemProjectAutoPosition = data?.posRow;
  // }

  if (!projectStore.measuresItemProjectAutoPosition && data?.measuresItemPosRow) {
    projectStore.measuresItemProjectAutoPosition = data?.measuresItemPosRow;
  }
});

// 载入模板相关
let zjMouldRef = ref(null);
let zjMouldFYHZRef = ref(null);
const showLoadMould = () => {
  if (pageType.value == 'fyhz') {
    zjMouldFYHZRef.value.open(pageType.value);
  } else {
    zjMouldRef.value.open(pageType.value);
  }
};

const onSuccessZj = () => {
  zjMouldRef.value.close();
  zjMouldFYHZRef.value.close();
  if (pageType.value == 'fyhz' && projectStore.summaryExpenseGetList) {
    projectStore.summaryExpenseGetList();
  } else if (pageType.value == 'csxm' && projectStore.measuresItemGetList) {
    projectStore?.measuresItemGetList();
  } else if (pageType.value == 'qtxm' && projectStore.otherProItemGetList) {
    projectStore?.otherProItemGetList();
  }
};

const executeCommand = item => {
  // console.log(mainRontentRef.value.childComponentRef[item.provideFun]())
  const child = mainRontentRef.value.childComponentRef;
  if (child && typeof child[item.provideFun] === 'function') {
    child[item.provideFun]();
  }
};

const getChange = (newVal, oldVal) => {
  console.log('getChange');
  let change;
  change = newVal.filter(x => !oldVal.some(y => y.id === x.id));
  let moreChange = oldVal.filter(x => !newVal.some(y => y.id === x.id));
  if (change.length > 0) {
    let addList = [];
    change.map(item => {
      if (item.levelType === 3) {
        addList.push({ clickTab: '项目概况', id: item.id });
      } else if (
        projectStore.type === 'ys' &&
        item.levelType === 2 &&
        item.parentId === route.query.constructSequenceNbr
      ) {
        addList.push({ clickTab: '造价分析', id: item.id });
      }
    });
    let list = [...projectStore.proCheckTab, ...addList];
    let resultList = list.filter(x => !moreChange.some(y => y.id === x.id));
    projectStore.SET_PRO_CHECK_TAB(resultList);
    // projectStore.SET_TAB_SELECT_NAME('项目概况');
  }
};
let biddingType = ref();
let moveList = reactive([]); //单项上下移动，被替换目标
let dataList = reactive([]);
let tarList = reactive([]);
const getIsFlag = type => {
  let flag = true;
  let sameLevel = dataList.filter(item => item.parentId === projectStore.currentTreeInfo.parentId);
  if (
    (sameLevel[0].id === projectStore.currentTreeInfo.id && type === 'up') ||
    (sameLevel[sameLevel.length - 1].id === projectStore.currentTreeInfo?.id && type === 'down')
  ) {
    flag = false;
  }
  return flag;
};
const drop = data => {
  //左侧树拖拽功能
  let copyTreeList = JSON.parse(JSON.stringify(treeData.value));
  let list = [];
  data.map(i => {
    list.push(copyTreeList.find(a => a.id === i));
  });
  treeData.value = list;
  dataList = xeUtils.clone(treeData.value, true);
};
const upOrDown = type => {
  //点击上下移动操作
  //同级列表
  if (!getIsFlag(type)) return;
  let index = dataList.findIndex(item => item.id === projectStore.currentTreeInfo.id);

  if (projectStore.currentTreeInfo.levelType === 3) {
    //单位之间直接上下移动
    let other;
    if (type === 'up') {
      other = dataList[index - 1];
      dataList[index - 1] = dataList[index];
      dataList[index] = other;
    } else {
      other = dataList[index + 1];
      dataList[index + 1] = dataList[index];
      dataList[index] = other;
    }
  } else if (projectStore.currentTreeInfo.levelType === 2) {
    //单项移动需要带着子级一起
    moveList = [];
    tarList = [];
    getSelfList(projectStore.currentTreeInfo);
    let tarInfo = type === 'up' ? dataList[index - 1] : dataList[index + tarList.length]; //上下移动的位置目标
    console.log('tarInfo', tarInfo);
    getMoveList(tarInfo, dataList, type, tarList[0]);
    let nextIndex = dataList.findIndex(item => item.id === moveList[0].id);
    if (type === 'up') {
      // tarList.map(a=>{if(a.children?.length>0)return a.children=[]})
      dataList.splice(index, tarList.length);
      dataList.splice(nextIndex, 0, ...xeUtils.clone(tarList, true));
    } else {
      dataList.splice(nextIndex, moveList.length);
      dataList.splice(index, 0, ...moveList);
    }
  }
  dataList.map(a => {
    if (a.children?.length > 0) return (a.children = []);
  });
  treeData.value = [...dataList];
  dataList = xeUtils.clone(treeData.value, true);
  console.log('dataList', dataList);
  saveTree(dataList);
};

const saveTree = postData => {
  csProject.postEditStructureV2(xeUtils.toArrayTree(postData)[0]).then(res => {
    console.log('保存树成功', res);
    getTreeList(true);
  });
};
const getSelfList = tar => {
  console.log(tarList);
  tarList.push(tar);
  tar.children?.map(i => {
    getSelfList(i);
  });
};
const getMoveList = (tar, list, type, move) => {
  if (type === 'up') {
    if (tar.parentId === projectStore.currentTreeInfo.parentId) {
      //同级移动切没有子级
      moveList.unshift(tar);
    } else {
      list.map(item => {
        if (item.parentId === move.parentId) {
          moveList.push(item);
        }
      });
      moveList = moveList.splice(moveList.findIndex(item => item.id === move.id) - 1, 1);
    }
  } else {
    moveList.push(tar);
    list.map(i => {
      if (i.parentId === tar.id) {
        getMoveList(i, list, type);
      }
    });
  }
};

const getTreeList = (nofresh = false) => {
  let apiName = constructLevelTreeStructureList;
  let apiData = route.query.constructSequenceNbr;
  if (projectStore.type === 'yssh') {
    apiName = generateLevelTreeNodeStructureSH;
  } else if (projectStore.type === 'jieSuan') {
    apiName = getJsAsideTreeList;
  }
  if (projectStore.updateSS) {
    apiData = projectStore.currentTreeGroupInfo?.constructId;
  }

  apiName(apiData).then(res => {
    console.log('res-getTreeList', res, '设置定额类型', projectStore.deType);
    if (res.status === 200) {
      if (res.result) treeData.value = res.result;
      treeListCache.value = xeUtils.clone(res.result, true);
      let proCheckTab;
      if (projectStore.type === 'jieSuan') {
        projectStore.SET_GLOBAL_SETTING_INFO({
          mainRcjShowFlag: res.result[0].mainRcjShowFlag,
          standardConversionShowFlag: res.result[0].standardConversionShowFlag,
        });
      } else if (projectStore.type === 'yssh') {
        projectStore.SET_TYPE('yssh');
      }
      //最初projectStore.proCheckTab没有数据时重置
      let constructPro = treeListCache.value.find(a => a.levelType === 1);
      if (res.result[0].biddingType === 2) {
        proCheckTab =
          treeListCache.value &&
          treeListCache.value.filter(
            item =>
              !(item.levelType === 2 && item.parentId !== constructPro.id) && item.biddingType !== 2
          );
        projectStore.SET_CURRENT_TREE_INFO(treeListCache.value[1]);
        projectStore.SET_PRO_BIDDINGTYPE(2);
        projectStore.SET_PROJECT_NAME(treeListCache.value[1]?.name);
        projectStore.SET_CURRENT_TREE_GROUP_INFO({
          constructId: res.result[0].id,
          name: res.result[0].name,
        });
        if (projectStore.type === 'yssh') {
          projectStore.SET_CURRENT_TREE_GROUP_INFO({
            constructId: res.result[0].id,
            name: res.result[0].name,
            ssConstructId: res.result[0].ysshConstructId,
          });
        }
      } else {
        proCheckTab =
          treeListCache.value &&
          treeListCache.value.filter(
            item => !(item.levelType === 2 && item.parentId !== constructPro.id)
          );
        res.result.forEach(item => {
          if (item.id === projectStore.currentTreeInfo?.id && projectStore.currentTreeInfo.id) {
            projectStore.SET_CURRENT_TREE_INFO(item);
          }
        });
        if (!projectStore.isRefreshProjectTree && !nofresh) {
          projectStore.SET_CURRENT_TREE_INFO(treeListCache.value[0]);
        }
        projectStore.SET_PRO_BIDDINGTYPE(1);
        projectStore.SET_PROJECT_NAME(treeListCache.value[0]?.name);
      }
      projectStore.SET_IS_REFRESH_PROJECT_TREE(false);
      let type; //打开项目类型
      switch (res.result[0].biddingType) {
        case 0:
          type = 'ZB';
          break;
        case 1:
          type = 'TB';
          break;
        case 2:
          type = 'DW';
          break;
      }
      projectStore.projectType = type;
      biddingType.value = res.result[0].biddingType;
      if (!projectStore.isRefreshProjectTree) {
        if (!projectStore.proCheckTab) {
          let list = [];
          proCheckTab.map(item => {
            list.push({
              clickTab: item.levelType === 2 ? '造价分析' : '项目概况',
              id: item.id,
            });
          });
          projectStore.SET_PRO_CHECK_TAB(list); //此处设置除单项之外的项目点击的tab栏都是项目概况
        } else {
          getChange(proCheckTab, projectStore.proCheckTab); //左侧树添加单位后 新单位页面展示为项目概况
        }
      }
      //getChange已判断，此处不需要设置
      // if (res.result[0].biddingType === 2) {
      // 	projectStore.SET_CURRENT_TREE_INFO(treeListCache.value[1]);
      // } else {
      // 	projectStore.SET_CURRENT_TREE_INFO(treeListCache.value[0]);
      // }
      console.log('projectStore', projectStore.$state);
      dataList = xeUtils.clone(treeData.value, true);
    }
  });
};

const getConstructConfigByConstructId = async () => {
  await csProject.getConstructConfigByConstructId(route.query.constructSequenceNbr).then(res => {
    if (res.status === 200) {
      projectStore.SET_CONSTRUCT_CONFIG_INFO(res.result);
      projectStore.deType = res.result.deStandardReleaseYear;
      projectStore.taxMade = res.result.taxMode && Number(res.result.taxMode);
    }
  });
};
let constructReadOnlyRef = ref();
const save = event => {
  if (event.ctrlKey && event.code == 'KeyS') {
    if (constructReadOnlyRef.value?.readOnlyTip()) return;
    projectStore.SET_GLOBAL_LOADING({
      loading: true,
      info: '正在保存中...',
    });
    csProject
      .saveYsfFile(route.query.constructSequenceNbr)
      .then(res => {
        if (res?.result) {
          message.success('保存成功');
        }
        // message.success('保存成功');
      })
      .finally(() => {
        projectStore.SET_GLOBAL_LOADING({ loading: false });
      });
  }
};

const clearTimers = () => {
  clearInterval(timer.value);
  timer.value = null;
};
onBeforeUnmount(() => {
  window.removeEventListener('keydown', save);
  clearTimers();
});
defineExpose({
  getTreeList,
  isItVerified,
});
</script>
<style lang="scss" scoped>
.open-beta-btn {
  position: absolute;
  right: 7px;
  bottom: 30%;
  z-index: 10;
  .icon {
    position: absolute;
    right: 7px;
    top: -10px;
    width: 20px;
    height: 20px;
    opacity: 0.4;
    z-index: 11;
    cursor: pointer;
  }
  .img {
    display: block;
    width: 91px;
    height: 91px;
    cursor: pointer;
    &:hover {
      & + .qrcode-img {
        display: block;
      }
    }
  }
  .qrcode-img {
    display: none;
    position: absolute;
    left: -114px;
    top: 50%;
    transform: translateY(-50%);
    width: 121px;
    height: 127px;
  }
}
section {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  height: calc(
    100vh - var(--project-detail-header-height) - var(
        --project-detail-functional-area-height
      ) - var(--project-detail-footer)
  );
}
// .expand {
//   width: 20px;
//   height: 20px;
//   box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
//   border-radius: 50%;
//   opacity: 1;
//   position: absolute;
//   top: 10px;
//   right: -10px;
//   text-align: center;
//   line-height: 16px;
//   // padding: 4px;
//   // z-index: 999;
//   z-index: 1;
//   cursor: pointer;
//   &-icon {
//     width: 12px;
//     height: 12px;
//   }
//   .isrotateY {
//     transform: rotateY(180deg);
//   }
// }
.btnExpand {
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-25%);
  width: 14px;
  font-size: 12px;
  height: 80px;
  z-index: 9; //存在弹框会出现按钮---调小
  text-align: center;
  transition: all 0.1s linear;
  cursor: pointer;
  user-select: none;
  .btn {
    display: none;
  }
  span {
    display: inline-block;
    transform: translateX(-1px);
    transition: all 0.4s;
  }
}
.btnExpand:hover .btn {
  display: block;
}
.option-lock {
  position: absolute;
  right: 0;
  top: 53px;
  background: #f2f6fc;
  padding: 5px 4px;
  font-size: 14px;
}
.option-lock:hover {
  cursor: pointer;
  background: #bae7ff;
}

footer {
  position: relative;
  width: 100%;
  height: var(--project-detail-footer);
  background-color: #d9e1ef;
}
aside {
  // min-width: 225px;
  // max-width: 100%;
  height: calc(100% - var(--project-detail-footer));
  width: 100%;
  // height: 100%;
  border-right: 2px solid #dcdfe6;
  background: #f8fbff;
  border-right: 2px solid #dcdfe6;
  position: relative;
  color: white;
  font-size: 12px;
  text-align: center;
  transition: width 0.3s linear;
  &:hover .btn {
    display: block;
  }
}
main {
  flex: 1;
  width: calc(100% - 263px);
}
</style>
