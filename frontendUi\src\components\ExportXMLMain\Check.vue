<template>
  <div class="self-check">
    <common-modal
      className="dialog-comm"
      v-model:modelValue="props.visible"
      title="校验"
      :mask="spinning"
      :lockView="false"
      :lockScroll="false"
      width="900px"
      @cancel="close"
      @close="close"
    >
      <div class="dialog-wrap">
        <a-spin :spinning="spinning">
          <div class="contentCenter">
            <div class="table">
              <img
                src="@/assets/img/data-null.png"
                alt=""
                class="noData"
                v-if="!selfTestDetailList || selfTestDetailList.length === 0"
              />
              <a-collapse v-else v-model:activeKey="activeKey">
                <a-collapse-panel
                  v-for="(item, index) in selfTestDetailList"
                  :key="item.name"
                >
                  <template #header>
                    <div>
                      {{ item.name }}
                      (<span class="count"> {{ item.count || 0 }} </span>)
                    </div>
                  </template>
                  <template
                    #extra
                    v-if="
                      ['标准清单编码重复', '补充清单编码重复或不规范'].includes(
                        item.name
                      )
                    "
                  >
                    <a-button
                      v-if="item.count && !item.checkResult"
                      size="small"
                      @click.stop="codeReset(index)"
                      >编码重刷<reload-outlined
                    /></a-button>
                    <span
                      v-if="item.count && item.checkResult"
                      style="color: green"
                      >调整完成</span
                    >
                  </template>
                  <vxe-table
                    ref="vexTable"
                    align="center"
                    :column-config="{ resizable: true }"
                    :tree-config="{
                      children: 'childrenList',
                      expandAll: true,
                      reserve: true,
                    }"
                    :data="item.childrenList"
                    :row-config="{
                      isCurrent: true,
                      keyField: 'uniqueStr',
                    }"
                    @cell-dblclick="cellDBLClickEvent"
                    :scroll-y="{
                      scrollToTopOnChange: true,
                    }"
                    show-overflow="title"
                  >
                    <vxe-column
                      tree-node
                      v-if="
                        [1, 2, 3, 4, 5, 6, 7, 8, 9, 23, 26, 27, 28].includes(
                          item.checkType
                        )
                      "
                      field="code"
                      width="150"
                      title="清单编码"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[10, 11].includes(item.checkType)"
                      field="code"
                      width="150"
                      title="定额编码"
                    >
                    </vxe-column>
                    <vxe-column
                      tree-node
                      v-if="[12, 13, 14].includes(item.checkType)"
                      field="code"
                      width="150"
                      title="材料编码"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="
                        [15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 29].includes(
                          item.checkType
                        )
                      "
                      :field="item.checkType === 29 ? 'dispNo' : 'sortNo'"
                      width="80"
                      title="序号"
                    >
                    </vxe-column>

                    <vxe-column
                      v-if="[29].includes(item.checkType)"
                      field="materialCode"
                      width="120"
                      title="编码"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="
                        [1, 2, 3, 4, 5, 6, 7, 8, 9, 23, 26, 27, 28].includes(
                          item.checkType
                        )
                      "
                      field="name"
                      width="150"
                      title="清单名称"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[10, 11].includes(item.checkType)"
                      field="name"
                      width="150"
                      title="定额名称"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[12, 13, 14].includes(item.checkType)"
                      field="name"
                      width="150"
                      title="材料名称"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[15, 16, 17, 18, 24, 25].includes(item.checkType)"
                      field="name"
                      width="150"
                      title="名称"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[19, 20, 21, 22].includes(item.checkType)"
                      field="name"
                      width="150"
                      title="项目名称"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[29].includes(item.checkType)"
                      field="materialName"
                      width="150"
                      title="项目名称"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[15, 16, 17, 18, 25, 29].includes(item.checkType)"
                      field="specification"
                      width="100"
                      title="规格型号"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="![19, 20, 21, 22, 27, 28].includes(item.checkType)"
                      field="unit"
                      width="80"
                      title="单位"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[3].includes(item.checkType)"
                      field="projectAttr"
                      width="220"
                      title="项目特征"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="
                        [1, 2, 3, 4, 5, 6, 7, 8, 9, 23, 26, 27, 28].includes(
                          item.checkType
                        )
                      "
                      field="price"
                      width="150"
                      title="综合单价"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[27, 28].includes(item.checkType)"
                      field="total"
                      width="100"
                      title="综合合价"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[27, 28].includes(item.checkType)"
                      field="ceilingPrice"
                      width="100"
                      title="最高限价"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="
                        [1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 26].includes(
                          item.checkType
                        )
                      "
                      field="quantity"
                      width="100"
                      title="工程量"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[12, 13, 14].includes(item.checkType)"
                      field="quantity"
                      width="80"
                      title="消耗量"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[10, 11, 12, 13, 14].includes(item.checkType)"
                      field="price"
                      title="单价"
                      width="150"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[19, 20, 21, 22].includes(item.checkType)"
                      field="serviceContent"
                      title="服务内容"
                      width="200"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="
                        [15, 16, 17, 18, 19, 20, 21, 22, 24, 25].includes(
                          item.checkType
                        )
                      "
                      field="amount"
                      title="数量"
                      width="80"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[19, 20, 21, 22].includes(item.checkType)"
                      field="total"
                      title="金额"
                      width="150"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[24].includes(item.checkType)"
                      field="total"
                      title="合价"
                      width="150"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="[29].includes(item.checkType)"
                      field="marketPrice"
                      title="暂定价"
                      min-width="120"
                    >
                    </vxe-column>
                    <vxe-column
                      v-if="![29].includes(item.checkType)"
                      field="remark"
                      title="备注"
                      width="200"
                    >
                    </vxe-column>
                  </vxe-table>
                </a-collapse-panel>
              </a-collapse>
            </div>
          </div>
          <div class="option-footer">
            <span
              ><icon-font
                type="icon-querenshanchu"
                style="margin-right: 5px"
              ></icon-font
              >您可通过双击清单进行项目跳转</span
            >
            <div class="btn-list">
              <a-button type="primary" @click="close">关闭</a-button>
            </div>
          </div>
        </a-spin>
      </div>
    </common-modal>
  </div>
  <QdLockedDialog
    v-model:visible="qdLockedVisible"
    :tableData="lockedList"
    @confirm="codeResetHandle"
  ></QdLockedDialog>
</template>

<script setup>
import { reactive, ref, watch } from 'vue';
import api from '@/api/projectDetail.js';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { constructLevelTreeStructureList } from '@/api/csProject.js';
import { ReloadOutlined } from '@ant-design/icons-vue';
import infoMode from '@/plugins/infoMode';
import QdLockedDialog from '../proCommonModel/selfCheck/QdLockedDialog.vue';
import { useReversePosition } from '@/hooks/useReversePosition.js';
import { projectDetailStore } from '@/store/projectDetail.js';
const { linkagePosition } = useReversePosition();

const projectStore = projectDetailStore();
const props = defineProps(['visible']);
const emits = defineEmits(['update:visible', 'next']);
const route = useRoute();
const checkStatus = ref(); // 全选反选值
const valGroup1 = ref([1]); // 默认第一个
const checkList = ref([]); // 检查项列表
const scopeVisible = ref(false); // 设置检查范围弹框是否打开
let treeData = ref([]); // 设置检查范围数据
let checkedKeys = ref([]); // 设置检查范围选中数据
let selfTestDetailList = ref([]); // 检查结果数据
let checkRowKeys = ref([]); // 默认勾选指定行
let unitIdList = ref([]);
const vexTable = ref();
let codeRefreshVisible = ref(false); // 编码重刷弹框是否展示
let codeType = ref('1'); // 编码重刷类型 1 工程项目   2 单位
const radioStyle = reactive({
  display: 'flex',
  height: '30px',
  lineHeight: '30px',
});
let spinning = ref(false); // 全局增加loading状态
let originalDetailList = ref([]); //
let resultFilterVisible = ref(false); // 检查结果筛选弹框是否展示
let resultFilterCheck = ref([]); // 检查结果筛选选中的值
let localInfo = ref(); // 双击定位信息
const activeKey = ref(); // 检查结果默认展示项
let codeLoading = ref(false); // 编码重刷loading

let qdLockedVisible = ref(false); // 清单锁定弹框是否展示
let lockedList = ref([]); // 锁定清单列表

const close = () => {
  emits('update:visible', false);
};

watch(
  () => props.visible,
  async () => {
    if (props.visible) {
      await getTreeList();
      selfCheck();
    }
  }
);

// 项目自检
const selfCheck = () => {
  console.log('==============', checkList.value);
  let apiData = {
    constructId: route.query.constructSequenceNbr,
    checkValues: JSON.parse(JSON.stringify(valGroup1.value)),
    rangeTree: JSON.parse(JSON.stringify(unitIdList.value)),
  };
  console.log('apiData', JSON.stringify(apiData));
  spinning.value = true;
  api.selfCheck(apiData).then(res => {
    console.log('111111111', res);
    if (res.status === 200 && res.result) {
      spinning.value = false;
      selfTestDetailList.value = res.result
        .filter(item => {
          return ['标准清单编码重复', '补充清单编码重复或不规范'].includes(
            item.name
          );
        })
        .map(item => {
          item.checkResult = item.count > 0 ? false : true;
          return item;
        });
      let obj = selfTestDetailList.value.find(x => x.count > 0);
      if (obj) {
        activeKey.value = obj.name;
      }
    }
  });
};

// 设置检查范围列表数据获取
const getTreeList = async () => {
  valGroup1.value = [];
  // 后端需临时处理，添加所有
  const checkRes = await api.checkItems({
    constructId: route.query.constructSequenceNbr,
  });
  if (checkRes.status === 200 && checkRes.result) {
    valGroup1.value = checkRes.result.map(item => item.checkType);
  }
  console.log('🚀 ~ getTreeList ~ checkRes:', checkRes);
  unitIdList.value = [];
  const res = await constructLevelTreeStructureList(
    route.query.constructSequenceNbr
  );
  console.log('详情res', res);
  if (res.status === 200 && res.result.length) {
    res.result.forEach(item => {
      if (item.levelType === 3 && item.constructMajorType) {
        unitIdList.value.push(item.id);
      }
    });
  }
};

let codeRefreshIndex = ref(0);
const getLockedList = (list, result = []) => {
  for (let item of list) {
    if (!item.childrenList) {
      if (item.isLocked) result.push(item);
    } else {
      getLockedList(item.childrenList, result);
    }
  }
  return result;
};
const codeResetHandle = () => {
  spinning.value = true;
  if (codeRefreshIndex.value === 0) {
    checkFfExistQdUnitDifferent();
  } else {
    const { isUnitCf, isCodeGf, isCodeCf, qdIsLocked, childrenList } =
      selfTestDetailList.value[codeRefreshIndex.value];
    if (isUnitCf || isCodeGf) {
      // 单位重复\编码规范
      infoMode.show({
        iconType: 'icon-querenshanchu',
        isSureModal: true,
        infoText:
          '存在相同补充清单编码单位不一致或编码不规范情况，是否跳过统一修改？',
        descText: '点击统一修改后将直接按照规范补充清单格式刷新清单数据',
        showCustom: {
          name: '跳过',
          type: '',
          callBack: () => {
            //type:2
            bcQdCodeReset(2);
            infoMode.hide();
          },
        },
        confirmText: '统一修改',
        confirm: () => {
          // type:3
          bcQdCodeReset(3);
          infoMode.hide();
        },
      });
      return;
    }
    if (isCodeCf) {
      // 编码重复 type:1
      bcQdCodeReset(1);
    }
  }
};
const codeReset = index => {
  codeRefreshIndex.value = index;
  const { isUnitCf, isCodeGf, isCodeCf, qdIsLocked, childrenList } =
    selfTestDetailList.value[index];
  if (qdIsLocked) {
    lockedList.value = getLockedList(childrenList);
    qdLockedVisible.value = true;
    return;
  }
  codeResetHandle();
};
const refreshSuccess = (type = 0) => {
  codeLoading.value = false;
  selfTestDetailList.value[codeRefreshIndex.value].checkResult =
    type === 2 ? false : true;
  infoMode.show({
    iconType: 'icon-ruotixing',
    isSureModal: true,
    infoText: '编码重刷已完成',
    confirm: () => {
      const checkEvery = selfTestDetailList.value.every(
        item => item.checkResult
      );
      console.log('🚀 ~ api.refreshCode ~ checkEvery:', checkEvery);
      if (checkEvery) {
        emits('next'); // 完成自检后，关闭弹窗
      }
      infoMode.hide();
    },
  });
};
const bcQdCodeReset = type => {
  //type 1  情况:仅存单位一致,编码符合规范,编码重复情况       刷新规则:只根据前9位刷新流水号
  //type 2  情况:存在相同补充清单编码 单位不一致 或补充清单编码不规范情况  刷新规则:仅刷新 单位一致且符合规范的补充清单数据
  //type 3 情况:和2一样  不过刷新规则选择和2 不一样  刷新规则: 将不符合规范的清单 改为符合规范的清单 然后刷新流水号
  codeLoading.value = true;
  let apiData = {
    isGlobal: codeType.value === '1', // 1 工程项目 2 单位
    constructId: route.query.constructSequenceNbr,
    rangeTree: JSON.parse(JSON.stringify(unitIdList.value)),
    type,
  };
  console.log('补充编码重刷', apiData);
  api.refreshBCCode(apiData).then(res => {
    spinning.value = false;
    if (res.status === 200 && res.result) {
      refreshSuccess(type);
    }
  });
};
const selfTestQdCodeResetSort = (skipFlag, type = 0) => {
  codeLoading.value = true;
  let apiData = {
    isGlobal: codeType.value === '1', // 1 工程项目 2 单位
    constructId: route.query.constructSequenceNbr,
    isSkip: skipFlag,
  };
  console.log('编码重刷', apiData);
  api.refreshCode(apiData).then(res => {
    spinning.value = false;
    if (res.status === 200 && res.result) {
      refreshSuccess(type);
    }
  });
};

// 双击事件
const cellDBLClickEvent = async ({ row }) => {
  if (!row.childrenList) {
    selfTestLocate(row);
  }
};

// 定位指定数据
const selfTestLocate = row => {
  api.selfTestLocate(JSON.parse(JSON.stringify(row))).then(res => {
    if (res.status === 200 && res.result) {
      console.log('定位数据', res.result);
      let tabMenuName = '分部分项';
      switch (res.result.bizType) {
        case 'fbfx':
          tabMenuName = '分部分项';
          break;
        case 'csxm':
          tabMenuName = '措施项目';
          break;
        case 'rcj':
          tabMenuName = '人材机';
          break;
        case 'rcj_pb':
          tabMenuName = '人材机配比';
          break;
        case 'rcj_summary_up':
          tabMenuName = '人材机汇总-单位工程级别';
          break;
        case 'rcj_summary_cp':
          tabMenuName = '人材机汇总';
          break;
        case 'jrg':
          tabMenuName = '其他项目';
          break;
        case 'zcbfwf':
          tabMenuName = '其他项目';
          break;
        case 'qtxm':
          tabMenuName = '其他项目';
          break;
      }
      if (res.result.bizType === 'jrg' || res.result.bizType === 'zcbfwf') {
        console.log('这儿进来不', res.result.bizType);
        linkagePosition({
          treeId: res.result.upId,
          tabMenuName,
          rowId: res.result.sequenceNbr,
          childrenTabName: res.result.bizType,
        });
      } else if (res.result.bizType === 'rcj_summary_cp') {
        linkagePosition({
          treeId: route.query.constructSequenceNbr,
          tabMenuName,
          rowId: res.result.sequenceNbr,
        });
      } else if (
        res.result.bizType === 'rcj' ||
        res.result.bizType === 'rcj_pb'
      ) {
        linkagePosition({
          treeId: res.result.upId,
          tabMenuName:
            res.result.moduleType === 'fbfx' ? '分部分项' : '措施项目',
          rowId: res.result.deId,
          childrenTabName: res.result.sequenceNbr,
        });
      } else {
        linkagePosition({
          treeId: res.result.upId,
          tabMenuName,
          rowId: res.result.sequenceNbr,
        });
      }
    } else {
      message.error(res.result);
    }
  });
};
const isStandardQdRepeat = () => {
  return (
    selfTestDetailList.value[codeRefreshIndex.value].name === '标准清单编码重复'
  );
};
const isSupplementQdRepeat = () => {
  return (
    selfTestDetailList.value[codeRefreshIndex.value].name === '补充清单编码重复'
  );
};
// 判断是否存在编码形同 单位不一致的数据
const checkFfExistQdUnitDifferent = () => {
  let apiData = {
    constructId: route.query.constructSequenceNbr,
    checkValues: JSON.parse(JSON.stringify(valGroup1.value)),
    rangeTree: JSON.parse(JSON.stringify(unitIdList.value)),
  };
  api.checkFfExistQdUnitDifferent(apiData).then(res => {
    if (res.status === 200) {
      console.log('res.result', res.result);
      if (res.result) {
        let skipFlag = false;
        let infoText = isStandardQdRepeat()
          ? '存在相同清单编码单位不一致情况，是否跳过统一修改？'
          : isSupplementQdRepeat()
          ? '存在相同补充清单编码单位不一致或编码不规范情况，是否跳过统一修改？'
          : '';
        infoMode.show({
          iconType: 'icon-querenshanchu',
          isSureModal: true,
          infoText,
          descText:
            '点击统一修改后将不考虑单位，直接修改所有单位工程下编码重复的清单',
          showCustom: {
            name: '跳过',
            type: '',
            callBack: () => {
              skipFlag = true;
              selfTestQdCodeResetSort(skipFlag, 2);
              infoMode.hide();
            },
          },
          confirmText: '统一修改',
          confirm: () => {
            skipFlag = false;
            selfTestQdCodeResetSort(skipFlag);
            infoMode.hide();
          },
        });
      } else {
        selfTestQdCodeResetSort(true);
      }
    }
  });
};
</script>

<style lang="scss" scoped>
.dialog-wrap {
  width: 100%;
  height: 100%;
  .option-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    padding: 0 0px;
    span {
      font-size: 12px;
      color: #2a2a2a;
    }
    .btn-list {
      button + button {
        margin-left: 10px;
      }
    }
  }
}
.contentCenter {
  color: #b9b9b9;
  // margin-top: 10px;
  height: 65vh;
  overflow: auto;
  .right {
    width: 100%;
    .btns {
      display: flex;
      width: 100%;
      justify-content: space-between;
      .name {
        font-size: 14px;
        color: #287cfa;
        border-bottom: 2px solid #287cfa;
        width: 80px;
        text-align: center;
      }
      .btnNo1 {
        margin: 0 0px;
      }
    }
    .table {
      height: 350px;
      overflow-y: auto;
      position: relative;
      border: 1px solid #eeeeee;
    }
  }
}

.count {
  color: #de3f3f;
  margin: 0 2px;
}

.footer-btn-list {
  padding-bottom: 20px;
}

.scope-content {
  .tree-list {
    height: 359px;
    padding: 2px 0;
    border: 1px solid #b9b9b9;
  }
  .btn-list {
    display: flex;
    justify-content: space-between;
    margin-top: 22px;
  }
}
.noData {
  position: absolute;
  width: 274px;
  height: auto;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.refresh-content {
  .btn-list {
    display: flex;
    justify-content: center;
    margin-top: 22px;
    button + button {
      margin-left: 10px;
    }
  }
}
.filter-content {
  .check-list {
    border: 1px solid #b9b9b9;
    .tree {
      margin: 10px 0 0 10px;
      overflow: auto;
      height: 360px;
      ::v-deep(.vxe-checkbox-group) {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        .vxe-checkbox + .vxe-checkbox {
          margin: 10px 0 0 0;
        }
      }
    }
  }
  .btn-list {
    text-align: right;
    margin-top: 15px;
  }
}
</style>
