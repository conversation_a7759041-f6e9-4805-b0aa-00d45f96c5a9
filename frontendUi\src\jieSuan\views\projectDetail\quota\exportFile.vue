<!--
 * @Descripttion: 导出项目
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: sunchen
 * @LastEditTime: 2025-04-29 14:10:38
-->
<template>
  <common-modal
    className="dialog-comm tree-dialog"
    width="auto"
    v-model:modelValue="dialogVisible"
    title="导出项目"
    @cancel="cancel"
    @close="cancel"
  >
    <div class="tree-content-wrap">
      <div class="dialog-content">
        <div class="title-wrap">
          <span class="title">导出文件类型</span>

          <a-radio-group
            size="small"
            v-model:value="useType"
            @change="changeType"
            name="radioGroup"
          >
            <a-radio :value="i.type" v-for="i of list" class="radio">
              <span class="label">{{ i.name }}</span>
            </a-radio>
          </a-radio-group>
        </div>
        <div class="list">
          <a-spin :spinning="treeLoading">
            <a-tree
              v-if="treeData"
              checkable
              show-line
              :tree-data="treeData"
              :fieldNames="{
                children: 'childrenList',
                title: 'headLine',
                key: 'sequenceNbr',
              }"
              v-model:expandedKeys="expandedKeys"
              v-model:checkedKeys="checkedKeys"
            >
              <template #switcherIcon="{ switcherCls, children }">
                <down-outlined :class="switcherCls" />
              </template>
            </a-tree>
          </a-spin>
        </div>
      </div>
      <div class="group-list">
        <a-radio-group v-model:value="dataStatus" @change="changeCheck">
          <a-radio value="all">全部</a-radio>
          <a-radio value="part">取消全部</a-radio>
        </a-radio-group>
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button
          type="primary"
          @click="save"
          :loading="submitLoading"
          :disabled="!checkedKeys.length"
          >确定</a-button
        >
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { message } from 'ant-design-vue';
import {
  ref,
  reactive,
  watch,
  nextTick,
  markRaw,
  defineExpose,
  onBeforeUnmount,
} from 'vue';
import { useRoute } from 'vue-router';
import csProject from '@/api/csProject';
import systemApi from '@/api/system';
import { DownOutlined } from '@ant-design/icons-vue';
import { projectDetailStore } from '@/store/projectDetail';
import XEUtils from 'xe-utils';

const store = projectDetailStore();
const emit = defineEmits(['handleOk']);
const route = useRoute();

const treeData = ref(null);
const submitLoading = ref(false);
const dialogVisible = ref(false);
const checkedKeys = ref([]); // 选中的数据，为了后续要处理其他字段
const dataStatus = ref(null); // 全选，单选
const useType = ref('常用报表'); // 弹窗里的文件类型
const list = ref([]); //导出文件类型列表
const useTabType = ref(); // 页面主入口导出文件类型，excel，pdf,xml
let treeLoading = ref(false);
let timer = ref(null);
let expandedKeys = ref([]);
/**
 * 切换了导出文件类型
 */
const changeType = XEUtils.debounce(() => {
  treeData.value = null;
  expandedKeys.value = [];
  checkedKeys.value = [];
  getTreeList();
}, 300);

/**
 * 获取树数据
 */
const getTreeList = () => {
  treeLoading.value = true;
  let apiFun = csProject.exportProjectTree;
  if (store.type === 'jieSuan') {
    apiFun = csProject.jieSuanExportProjectTree;
  }
  apiFun(
    route.query.constructSequenceNbr,
    useType.value
    // useTabType.value,
  )
    .then(res => {
      console.log('数数据', res.result);
      if (res.status === 200 && res.result) {
        treeData.value = [res.result];
        expandedKeys.value.push(treeData.value[0].sequenceNbr);

        // for (const item of treeData.value[0].childrenList) {
        //   expandedKeys.value.push(item.sequenceNbr)
        // }
      }
    })
    .finally(() => {
      nextTick(() => {
        treeLoading.value = false;
      });
    });
};

// 导出获取类型   "常用报表"  和 "13规范报表"
const gettype = () => {
  // list.value = [
  //   {
  //     type: '招标项目报表',
  //     name: '招标项目',
  //   },
  //   {
  //     type: '投标项目报表',
  //     name: '投标项目',
  //   },
  //   {
  //     type: '工程量清单报表',
  //     name: '工程量清单',
  //   },
  //   {
  //     type: '其他',
  //     name: '其他',
  //   },
  // ];
  list.value = [
    {
      type: '常用报表',
      name: '常用报表',
    },
    {
      type: '13规范报表',
      name: '13规范报表',
    },
  ];
  const mappingList = {
    ZB: 0,
    TB: 1,
    DW: 2,
  };

  let typeIndex = mappingList[store.projectType] || 0;
  useType.value = list.value[typeIndex].type;

  getTreeList();
};

const cancel = () => {
  treeData.value = null;
  useType.value = '常用报表';
  dialogVisible.value = false;
  checkedKeys.value = [];
  expandedKeys.value = [];
  dataStatus.value = null;
};

/**
 * 点击全选与非全选
 */
const changeCheck = () => {
  if (dataStatus.value === 'all') {
    checkedKeys.value = [treeData.value[0]?.sequenceNbr];
  } else {
    checkedKeys.value = [];
  }
};

const save = async () => {
  if (!checkedKeys.value || !checkedKeys.value.length) {
    message.error('请选择要导出的工程');
    return;
  }

  try {
    submitLoading.value = true;
    const list = flattenTree(treeData.value)[0];
    const params = {
      lanMuName: useType.value,
      params: JSON.parse(JSON.stringify(list)),
    };
    console.log(params, useTabType.value);
    let apiName = useTabType.value === 'pdf' ? 'exportPdfFile' : 'exportExcel';
    if (store.type === 'jieSuan') {
      apiName =
        useTabType.value === 'pdf'
          ? 'jieSuanExportPdfFile'
          : 'jieSuanExportExcelZip';
    }
    const res = await csProject[apiName](params);
    console.log('🚀 ~ file: exportFile.vue:185 ~ save ~ res:', res);
    if (res?.result) {
      message.success('导出成功！');
      cancel();
    }
  } catch (error) {
    console.error(error);
  } finally {
    submitLoading.value = false;
  }
};

/**
 * 将树结构里面的某个字段与选中的数据进行关联
 * @param {*} treeList
 */
const flattenTree = treeList => {
  const result = [];

  function traverse(node) {
    result.push(node); // 将当前节点添加到结果数组中

    if (node.childrenList && node.childrenList.length > 0) {
      // 递归处理子节点
      for (let i = 0; i < node.childrenList.length; i++) {
        const data = node.childrenList[i];
        data.selected =
          checkedKeys.value.includes(data.sequenceNbr) ||
          ['all'].includes(dataStatus.value);
        traverse(data);
      }
    }
  }
  // 遍历树列表中的每个根节点
  for (let i = 0; i < treeList.length; i++) {
    const root = treeList[i];
    root.selected =
      checkedKeys.value.includes(root.sequenceNbr) ||
      ['all'].includes(dataStatus.value);
    traverse(root);
  }

  return result;
};

/**
 *
 * @param {*} type 点击类型
 */
const open = type => {
  treeLoading.value = true;
  useTabType.value = type;
  dialogVisible.value = true;
  gettype();
  submitLoading.value = false;
};

onBeforeUnmount(() => {
  console.log('销毁了');
  // debugger
  treeLoading.value = false;
  timer.value = null;
});

defineExpose({
  open,
  cancel,
});
</script>

<style lang="scss" scoped>
.tree-content-wrap {
  width: 625px;
  height: 60vh;
  display: flex;
  flex-direction: column;
}
.dialog-content {
  background: rgba(250, 250, 250, 0.39);
  border: 1px solid #e1e1e1;
  display: flex;
  flex-direction: column;
  border-radius: 2px;
  flex: 1;
  overflow: hidden;
  &:hover {
    overflow: auto;
  }
  .title-wrap {
    padding: 7px 13px;
    background-color: #eaeaea;

    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 14px;
      font-weight: 600;
      color: #333333;
    }
    :deep(.ant-radio-group) {
      .ant-radio-wrapper {
        border-radius: 3px;
        padding: 8px 14px;
        color: #606060;
        background-color: #fff;
        border: 1px solid #d9d9d9;
      }
      .ant-radio-wrapper-checked {
        color: #287cfa;
        border: 1px solid #287cfa;
      }
      .label {
        font-size: 12px;
        user-select: none;
      }
    }
  }
  .list {
    flex: 1;
    padding: 16px 18px;
    background-color: #fafafa;
    overflow: hidden;
    &:hover {
      overflow: auto;
    }
    :deep(.ant-spin) {
      height: 100%;
      width: 100%;
    }

    :deep(.ant-tree) {
      background-color: #fafafa;
      .ant-tree-switcher-noop {
        opacity: 0;
      }
      .ant-tree-switcher {
        background-color: transparent;
      }
    }
  }
}

.group-list {
  margin: 14px 0 30px 16px;
}
</style>
