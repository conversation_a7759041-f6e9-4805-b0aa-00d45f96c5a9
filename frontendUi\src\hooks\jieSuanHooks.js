/*
 * @Descripttion: 关联数据
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-04 16:05:09
 * @LastEditors: kong<PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-14 14:50:25
 */

import { projectDetailStore } from '@/store/projectDetail';
import { ref, nextTick } from 'vue';


export default {
  // 统一配置表，包含原始路径集合和替换目标
  channelMappings: [
    { 
      sourcePaths: [  //
        'controller.constructCostMathController.czysCostMath',
        'controller.constructCostMathController.cgCostMath',
        'controller.constructCostMathController.azCostMath'
      ],
      target: 'controller.jieSuanProject.jieSuanConstructCostMathController.' 
    },
    { 
      sourcePaths: [  // 
        'controller.pumpingAddFeeController.calculationPumpingAddFee'
      ],
      target: 'controller.jieSuanProject.jieSuanPumpingAddFeeController.' 
    },
    { 
      sourcePaths: [  // 
        'controller.fxtjCostMatchController.fxtjCostMatch'
      ],
      target: 'controller.jieSuanProject.jieSuanFxtjCostMatchController.' 
    },
    { 
      sourcePaths: [  // 
        'controller.itemBillProjectController.batchDelBySeachList'
      ],
      target: 'controller.jieSuanProject.jieSuanFbfxController.' 
    }
  ],

  async checkChannel(channel, params) {
    return new Promise((resolve) => {
      nextTick(() => {
        const projectStore = projectDetailStore();
        if (projectStore.type !== 'jieSuan') {
          return resolve({ newPath: channel, newParams: params });
        }

        // 遍历配置表查询匹配项
        const mapping = this.channelMappings.find(({ sourcePaths }) => 
          sourcePaths.includes(channel)
        );

        const newChannel = mapping 
          ? channel.replace(/^controller\.([^.]+)\./, mapping.target)
          : channel;

        resolve({ newPath: newChannel, newParams: params });
      });
    });
  }
};

