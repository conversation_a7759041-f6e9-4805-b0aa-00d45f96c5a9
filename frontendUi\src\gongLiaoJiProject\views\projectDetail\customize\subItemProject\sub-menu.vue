<template>
  <div
    class="submenu"
    ref="submenu"
    :style="{
      position: 'fixed',
      zIndex: '998',
      boxShadow: 'var(--surely-table-popup-shadow)',
    }"
  >
    <a-menu
      style="width: 180px; font-size: 12px"
      v-for="(item, index) in menuConfig.options"
      mode="vertical"
    >
      <a-sub-menu
        :key="item.code"
        v-if="item.children?.length > 0 && item.visible"
      >
        <template #title>{{ item.name }}</template>
        <a-menu-item
          style="height: 30px; line-height: 30px; font-size: 12px"
          :style="{ display: cItem.visible ? 'block' : 'none' }"
          v-for="(cItem, cIndex) in item.children"
          :key="cItem.code"
          @click="
            emit('contextMenuClickEvent', {
              menu: cItem,
              row: props.currentInfo,
            })
          "
          :disabled="cItem.disabled"
          >{{ cItem.name }}</a-menu-item
        >
      </a-sub-menu>
      <a-menu-item
        :key="item.code"
        :style="{ display: item.visible ? 'block' : 'none' }"
        :disabled="item.disabled"
        @click="
          emit('contextMenuClickEvent', { menu: item, row: props.currentInfo })
        "
        v-if="!item.children"
      >
        {{ item.name }}
      </a-menu-item>
    </a-menu>
  </div>
</template>
<script setup>
import { reactive, ref, onMounted } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import deMapFun from '../deMap';

const projectStore = projectDetailStore();
const props = defineProps({
  args: {
    type: Object,
  },
  addRowVisible: {
    type: Function,
  },
  pasteRowVisible: {
    type: Function,
  },
  currentInfo: {
    type: Object,
  },
  copyData: {
    type: Object,
  },
});
const emit = defineEmits(['update:currentInfo', 'contextMenuClickEvent']);
let submenu = ref();
// 定位右键元素
let menuEl = ref();
onMounted(() => {
  // 获取目标元素
  let tableEl = document.querySelector('.surely-table-body-viewport-container');
  // 添加滚动事件监听器
  tableEl.addEventListener('scroll', function () {
    // 输出滚动位置
    if (submenu.value) {
      updatePosition();
    }
  });
  function calculateDistanceToViewportBottom(element) {
    const rect = element.getBoundingClientRect();
    const viewportBottom = window.innerHeight;
    const elementBottom = rect.bottom;
    return viewportBottom - elementBottom;
  }
  let isFixed = false;
  function updatePosition() {
    const distance = calculateDistanceToViewportBottom(submenu.value);
    if (!isFixed && distance <= 0) {
      submenu.value.classList.add('fixed-bottom');
      isFixed = true;
    } else if (
      isFixed &&
      calculateDistanceToViewportBottom(menuEl.value) >
        submenu.value.offsetHeight
    ) {
      // 当滚动距离大于元素高度时解锁
      submenu.value.classList.remove('fixed-bottom');
      isFixed = false;
    }
  }
  setTimeout(() => {
    updatePosition();
  }, 50);

  // 假设你有一个ID为"myElement"的元素
});
console.log('args', props.args);
const menuConfig = reactive({
  options: [
    {
      code: 'add',
      name: '插入',
      visible: true,
      children: [
        {
          code: 0,
          name: '插入分部',
          kind: '01',
          visible: true,
          disabled: false,
        },
        {
          code: 1,
          name: '插入子分部',
          kind: '02',
          visible: true,
          disabled: false,
        },
        {
          code: 2,
          name: '插入子目',
          kind: '-1',
          visible: true,
          disabled: false,
        },
      ],
    },
    {
      code: 'copy',
      name: '复制',
      visible: true,
      disabled: false,
    },
    {
      code: 'copyCell',
      name: '复制单元格内容',
      visible: true,
      disabled: false,
    },
    {
      code: 'paste',
      name: '粘贴',
      visible: true,
      disabled: true,
    },
    {
      code: 'pasteChild',
      name: '粘贴为子项',
      visible: true,
      disabled: true,
    },
    {
      code: 'deleteList',
      name: '删除',
      visible: true,
      disabled: false,
      children: [
        {
          code: 'delete',
          name: '删除行',
          visible: true,
          disabled: true,
        },
        {
          code: 'tempDelete',
          name: '临时删除',
          visible: true,
          disabled: false,
        },
        {
          code: 'cancelTempDelete',
          name: '取消临时删除',
          visible: true,
          disabled: false,
        },
        {
          code: 'batchDelete-child3',
          name: '批量取消临时删除',
          type: 3,
          visible: true,
          disabled: false,
        },
        {
          code: 'batchDelete-allDelete',
          name: '批量删除子目',
          visible: true,
          disabled: false,
        },
        {
          code: 'batchDelete-child1',
          name: '批量删除所有临时删除项',
          type: 1,
          visible: true,
          disabled: false,
        },
        {
          code: 'batchDelete-child2',
          name: '批量删除所有工程量为0项',
          type: 2,
          visible: true,
          disabled: false,
        },
        {
          code: 'subtileAllDelete',
          name: '删除所有子目',
          visible: true,
          disabled: false,
        },
      ],
    },
    {
      code: 'noteList',
      name: '批注',
      visible: false,
      disabled: false,
      children: [
        {
          code: 'add-note',
          name: '插入批注',
          type: 1,
          visible: true,
          disabled: false,
        },
        {
          code: 'edit-note',
          name: '编辑批注',
          type: 2,
          visible: true,
          disabled: false,
        },
        {
          code: 'del-note',
          name: '删除批注',
          type: 3,
          visible: true,
          disabled: false,
        },
        {
          code: 'show-note',
          name: '显示批注',
          type: 4,
          visible: true,
          disabled: false,
        },
        {
          code: 'hide-note',
          name: '隐藏批注',
          type: 5,
          visible: true,
          disabled: false,
        },
        {
          code: 'del-all-note',
          name: '删除所有批注',
          type: 6,
          visible: true,
          disabled: false,
        },
      ],
    },
    {
      code: 'generateMainMaterials',
      name: '根据定额名称生成主材',
      visible: false,
      disabled: true,
    },
    {
      code: 'generateDevice',
      name: '根据定额名称生成设备',
      visible: false,
      disabled: true,
    },
    {
      code: 'syncGenerateDevice',
      name: '同步主材/设备名称至子目',
      visible: false,
      disabled: true,
    },
  ],
});
const visibleMethod = async () => {
  let options = menuConfig.options;
  let row = props.args.record;
  Array.from(document.querySelectorAll('.surely-table-row')).map(a => {
    if (a.dataset.rowKey === row.sequenceNbr) {
      menuEl.value = a;
    }
  });
  if (!row) return;
  emit('update:currentInfo', row);
  projectStore.SET_SUB_CURRENT_INFO(row);
  console.log('props.currentInfo,row', props.currentInfo, row);
  options.forEach(async item => {
    // console.log('list', item, row);
    // if (!copyData.value && item.code === 'paste') {
    //   item.disabled = true;
    // }
    // if (copyData.value && item.code === 'paste') {
    //   item.disabled = false;
    //   try {
    //     await frameSelectRef.value.frameSelectJs.isPasteBranch(
    //       row,
    //       copyData.value
    //     );
    //     item.disabled = false;
    //   } catch (error) {
    //     item.disabled = true;
    //   }
    // }
    if (item.code === 'deleteList') {
      // 删除行操作
      if (row.kind !== '00' && row.kind !== '05') {
        item.children[0].disabled = false;
      } else {
        item.children[0].disabled = true;
      }
      // 临时删除、取消临时删除操作
      if (row.kind === '00' || row.kind === '01' || row.kind === '02') {
        item.children[1].visible = false;
        item.children[2].visible = false;
      } else if (row.isTempRemove == 1) {
        item.children[1].visible = false;
        if (row.isFirstTempRemove === 1) {
          item.children[2].visible = true;
        } else {
          item.children[2].visible = false;
        }
      } else {
        item.children[1].visible = true;
        item.children[2].visible = false;
      }
      if (projectStore.asideMenuCurrentInfo.type !== '0') {
        item.children[4].disabled = true;
      } else {
        item.children[4].disabled = false;
      }
      if (projectStore.asideMenuCurrentInfo.sequenceNbr === row.sequenceNbr) {
        item.children[0].disabled = true;
      } else {
        item.children[0].disabled = false;
      }
    }
    if (item.code === 'syncGenerateDevice') {
      // 生成主材清单
      item.visible = row.kind === '05';
      item.disabled = row.kind !== '05';
    }
    if (
      deMapFun.isDe(row.kind) &&
      (item.code === 'generateMainMaterials' || item.code === 'generateDevice')
    ) {
      item.visible = true;
      if (['03', '04', '08'].includes(row.type)) {
        item.disabled = false;
      } else {
        item.disabled = true;
      }
    }
    if (item.code === 'copy') {
      item.disabled = ['05', '07'].includes(row.kind);
    }
    if (item.code === 'paste' || item.code === 'pasteChild') {
      item.disabled =
        props.copyData && props.copyData.length > 0
          ? await props.pasteRowVisible(item.code)
          : true;
    }
    if (
      item.children &&
      !['batchDelete', 'noteList', 'deleteList'].includes(item.code)
    ) {
      // if(deMapFun.isDe(row.kind)){
      //   item.children[0].visible = false;
      //   item.children[1].visible = false;
      // }
      item.disabled = false;
      await props.addRowVisible(row);
      console.log('row------', row);
      item.children.forEach(childItem => {
        childItem.disabled = true;
        props.currentInfo.optionMenu.forEach(child => {
          if (child === childItem.code) {
            childItem.disabled = false;
          }
        });
      });
    }
  });

  if (row.kind === '05') {
    options[6].visible = false;
    // 如果批注不为空
  } else if (row.annotations !== '' && row.annotations !== undefined) {
    options[6].visible = true;
    options[6].children[0].visible = false;
    options[6].children[1].visible = true;
    options[6].children[2].visible = true;
    if (row.isShowAnnotations) {
      options[6].children[3].visible = false;
      options[6].children[4].visible = true;
    } else {
      options[6].children[3].visible = true;
      options[6].children[4].visible = false;
    }
  } else {
    options[6].visible = true;
    options[6].children[0].visible = true;
    options[6].children[1].visible = false;
    options[6].children[2].visible = false;
    options[6].children[3].visible = false;
    options[6].children[4].visible = false;
  }

  // 房修超高不能点击
  if ([11, 5].includes(row.isCostDe)) {
    options.forEach(item => {
      if (
        [
          'generateMainMaterials',
          'generateDevice',
          'syncGenerateDevice',
        ].includes(item.code)
      ) {
        item.disabled = true;
      }
    });
  }
};
visibleMethod();
</script>
<style lang="scss" scoped>
.submenu {
}
::v-deep(.ant-menu-item) {
  font-size: 11px;
  height: 25px;
  line-height: 25px;
}
::v-deep(.ant-menu-submenu-title) {
  font-size: 11px;
  height: 25px !important;
  line-height: 25px !important;
}
.fixed-bottom {
  bottom: 0;
}
</style>
