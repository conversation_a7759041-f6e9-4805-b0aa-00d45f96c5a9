/*
 * @Author: renmingming
 * @Date: 2023-05-17 09:28:32
 * @LastEditors: wangru
 * @LastEditTime: 2025-03-07 09:56:48
 */
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from './main';

export default {
  /**
   * 获取工程项目或者单位的计税方式数据
   * @param {*} params
   * @returns
   */
  getTaxCalculation(params) {
    return ipc.invoke(ipcApiRoute.taxCalculation, params);
  },
  updateTaxCalculationMethod(params) {
    return ipc.invoke(ipcApiRoute.updateTaxCalculationMethod, params);
  },
  /**
   * 获取费用总览和费率说明数据
   * @param {*} params
   * @returns
   */
  getFeeCollectionData(params) {
    return ipc.invoke(ipcApiRoute.feeCollectionData, params);
  },
  /**
   * 获取政策文件数据
   * @param {*} params
   * @returns
   */
  getPolicyDocument(params) {
    return ipc.invoke(ipcApiRoute.policyDocument, params);
  },
  /**
   * 恢复默认费率
   * @param {*} params
   * @returns
   */
  restoreDefaultFee(params) {
    return ipc.invoke(ipcApiRoute.restoreDefaultFee, params);
  },
  /**
   * 设置主取费文件
   * @param {*} params
   * @returns
   */
  setMainFeeFile(params) {
    return ipc.invoke(ipcApiRoute.setMainFeeFile, params);
  },
  /**
   * 保存政策文件数据
   * @param {*} params
   * @returns
   */
  checkPolicyDocument(params) {
    return ipc.invoke(ipcApiRoute.checkPolicyDocument, params);
  },
  /**
   * 保存计税方式数据
   * @param {*} params
   * @returns
   */
  saveTaxCalculation(params) {
    return ipc.invoke(ipcApiRoute.saveTaxCalculation, params);
  },
  /**
   * 单次保存费用总览数据，保存单位工程级别的费用总览数据
   * @param {*} params
   * @returns
   */
  saveCostOverview(params) {
    return ipc.invoke(ipcApiRoute.saveCostOverview, params);
  },
  /**
   * 单次保存单位的费用说明数据，保存单位工程级别的费用说明数据
   * @param {*} params
   * @returns
   */
  saveFeeDescription(params) {
    return ipc.invoke(ipcApiRoute.saveFeeDescription, params);
  },
  /**
   * 统一应用
   * @param {*} params
   * @returns
   */
  unifiedUse(params) {
    return ipc.invoke(ipcApiRoute.unifiedUse, params);
  },
  /**
   * 根据计税方式和纳税地区获取对应的税率
   * @param {*} params
   * @returns
   */
  getRateByMethodAndLocation(params) {
    return ipc.invoke(ipcApiRoute.getRateByMethodAndLocation, params);
  },
  /**修改造价分析
   *
   * @param {*} params
   * @returns
   */
  updateCostAnalysis(params) {
    return ipc.invoke(ipcApiRoute.updateCostAnalysis, params);
  },
  /**获取造价分析
   *
   * @param {*} params
   * @returns
   */
  getCostAnalysisData(params) {
    return ipc.invoke(ipcApiRoute.getCostAnalysisData, params);
  },
  /**查询单位工程的费用汇总数据
   *
   * @param {*} params
   * @returns
   */
  getUnitCostSummary(params) {
    return ipc.invoke(ipcApiRoute.getUnitCostSummary, params);
  },
  /**查询单位工程的费用代码和对应的金额
   *
   * @param {*} params
   * @returns
   */
  costCodePrice(params) {
    return ipc.invoke(ipcApiRoute.costCodePrice, params);
  },
  /**查询单位工程的费用代码和对应的金额 结算
   *
   * @param {*} params
   * @returns
   */
  costCodePriceJieSuan(params) {
    return ipc.invoke(ipcApiRoute.costCodePriceJieSuan, params);
  },
  /**查询单位工程的费用汇总数据列表
   *
   * @param {*} params
   * @returns
   */
  costCodeTypeList(params) {
    return ipc.invoke(ipcApiRoute.costCodeTypeList, params);
  },
  /**查询单位工程的费用汇总数据列表
   *
   * @param {*} params
   * @returns
   */
  isOnline() {
    return ipc.invoke(ipcApiRoute.isOnline);
  },
  /**修改单位工程的费用汇总数据列表
   *
   * @param {*} params
   * @returns
   */
  saveCostSummary(params) {
    return ipc.invoke(ipcApiRoute.saveCostSummary, params);
  },
  /**插入粘贴单位工程的费用汇总数据列表
   *
   * @param {*} params
   * @returns
   */
  addCostSummary(params) {
    return ipc.invoke(ipcApiRoute.addCostSummary, params);
  },
  /**费用汇总上移下移
   *
   * @param {*} params
   * @returns
   */
  moveUpDown(params) {
    return ipc.invoke(ipcApiRoute.moveUpDown, params);
  },
  /**删除单位工程的费用汇总数据列表
   *
   * @param {*} params
   * @returns
   */
  deleteCostSummary(params) {
    return ipc.invoke(ipcApiRoute.deleteCostSummary, params);
  },
  /**获取人材机汇总数据
   *
   * @param {*} params
   * @returns
   */
  queryConstructRcjByDeId(params) {
    return ipc.invoke(ipcApiRoute.queryConstructRcjByDeId, params);
  },

  /**单位人材机汇总取消排序
   *
   * @param {*} params
   * @returns
   */
  unitRcjCancelSort(params) {
    return ipc.invoke(ipcApiRoute.unitRcjCancelSort, params);
  },
  /** 单项人材机汇总取消排序
   *
   * @param {*} params
   * @returns
   */
  singleRcjCancelSort(params) {
    return ipc.invoke(ipcApiRoute.singleRcjCancelSort, params);
  },
  /** 工程项目人材机汇总取消排序
   *
   * @param {*} params
   * @returns
   */
  constructRcjCancelSort(params) {
    return ipc.invoke(ipcApiRoute.constructRcjCancelSort, params);
  },
  /** 单位人材机无价差
   *
   * @param {*} params
   * @returns
   */
  unitRcjWjc(params) {
    return ipc.invoke(ipcApiRoute.unitRcjWjc, params);
  },
  singleRcjWjc(params) {
    return ipc.invoke(ipcApiRoute.singleRcjWjc, params);
  },
  constructRcjWjc(params) {
    return ipc.invoke(ipcApiRoute.constructRcjWjc, params);
  },
  /**
   * 获取线上项目列表
   *  @param {*} params
   * @returns
   */
  getOnlineProgectList(params) {
    return ipc.invoke(ipcApiRoute.openOnlineProject, params);
  },

  /**
   * 获取线上项目列表的项目
   *  @param {*} params
   * @returns
   */
  openOnline(params) {
    return ipc.invoke(ipcApiRoute.openOnline, params);
  },
  /**
   * 获取本地项目列表
   * @param {*} params
   * @returns
   */
  openLocalFile() {
    return ipc.invoke(ipcApiRoute.openLocalFile);
  },
  /**
   * 获取更改的费率总览数据
   *  @param {*} params
   * @returns
   */
  getCostOverview(params) {
    return ipc.invoke(ipcApiRoute.getCostOverview, params);
  },
  /**
   * 获取费用汇总安文费弹框数据
   *  @param {*} params
   * @returns
   */
  getSafeFee(params) {
    return ipc.invoke(ipcApiRoute.getSafeFee, params);
  },
  /**
   * 获取费用汇总规费弹框数据
   *  @param {*} params
   * @returns
   */
  getGfeeFee(params) {
    return ipc.invoke(ipcApiRoute.getGfeeFee, params);
  },

  //检查更新
  checkForUpdater() {
    return ipc.invoke(ipcApiRoute.checkForUpdater);
  },
  //下载新版本
  downloadApp() {
    return ipc.invoke(ipcApiRoute.downloadApp);
  },

  /**关闭所有子窗口接口
   */
  closeAllChildWindow() {
    return ipc.invoke(ipcApiRoute.closeAllChildWindow);
  },

  /**获取子窗口id
   */
  // 概算未做
  getWinIdBySequenceNbr(formData) {
    return ipc.invoke(ipcApiRoute.getWinIdBySequenceNbr, formData);
  },
  /**单价工程编辑计算基数弹框左侧树
   */
  costCodeTypeListDJGC(params) {
    return ipc.invoke(ipcApiRoute.costCodeTypeListDJGC, params);
  },
  /**单价工程编辑计算基数弹框表格数据
   */
  costCodeListBytypeDJGC(params) {
    return ipc.invoke(ipcApiRoute.costCodeListBytypeDJGC, params);
  },
  /**单价工程编辑计算基数弹框表格数据
   */
  cellEditorDJGC(params) {
    return ipc.invoke(ipcApiRoute.cellEditorDJGC, params);
  },
  /**单价工程编辑后刷新数据
   */
  queryforDeIdDJGC(params) {
    return ipc.invoke(ipcApiRoute.queryforDeIdDJGC, params);
  },
  /**单价构成应用范围设置
   */
  applyEditorDJGC(params) {
    return ipc.invoke(ipcApiRoute.applyEditorDJGC, params);
  },
  cancelEditorDJGC(params) {
    return ipc.invoke(ipcApiRoute.cancelEditorDJGC, params);
  },
  /**单价构成载入模板左侧树
   */
  upcTemplatesDJGC(params) {
    return ipc.invoke(ipcApiRoute.upcTemplatesDJGC, params);
  },
  resetRateDJGC(params) {
    return ipc.invoke(ipcApiRoute.resetRateDJGC, params);
  },
  /**单价构成载入模板右侧表格
   */
  upcTemplatesByCodeDJGC(params) {
    return ipc.invoke(ipcApiRoute.upcTemplatesByCodeDJGC, params);
  },
  loadUPCtemplateDJGC(params) {
    return ipc.invoke(ipcApiRoute.loadUPCtemplateDJGC, params);
  },
  /**费用汇总进项税明细
   */
  getInputTaxDetails(params) {
    return ipc.invoke(ipcApiRoute.getInputTaxDetails, params);
  },
  /**费用汇总进项税明细编辑
   */
  updateInputTaxDetails(params) {
    return ipc.invoke(ipcApiRoute.updateInputTaxDetails, params);
  },
  /**费用汇总进项税明细编辑 结算
   */
  updateInputTaxDetailsJieSuan(params) {
    return ipc.invoke(ipcApiRoute.updateInputTaxDetailsJieSuan, params);
  },
  /**其他项目编辑计算基数弹框左侧树
   */
  costCodeTypeListQTXM(params) {
    return ipc.invoke(ipcApiRoute.costCodeTypeListQTXM, params);
  },
  /**其他项目编辑计算基数弹框表格数据
   */
  costCodePriceQTXM(params) {
    return ipc.invoke(ipcApiRoute.costCodePriceQTXM, params);
  },

  /**工程项目下才会有的统一应用的接口
   */
  updateProjectUnitCalculateBaseApply(params) {
    return ipc.invoke(ipcApiRoute.updateProjectUnitCalculateBaseApply, params);
  },
  /**修改工程项目/单位工程计算基数的接口
   */
  updateProjectUnitCalculateBaseList(params) {
    return ipc.invoke(ipcApiRoute.updateProjectUnitCalculateBaseList, params);
  },

  /**查询工程项目/单位工程的数据
   */
  queryProjectUnitCalculateBaseList(params) {
    return ipc.invoke(ipcApiRoute.queryProjectUnitCalculateBaseList, params);
  },
  /**取费表那下拉框计算基数数据
   */
  queryCalculateBaseDropDownList(params) {
    return ipc.invoke(ipcApiRoute.queryCalculateBaseDropDownList, params);
  },
  /**单价工程费用类别下拉框
   */
  upcTypesDJGC(params) {
    return ipc.invoke(ipcApiRoute.upcTypesDJGC, params);
  },
  /**单价工程保存模板
   */
  saveUPCtemplateDJGC(params) {
    return ipc.invoke(ipcApiRoute.saveUPCtemplateDJGC, params);
  },
  /**造价分析三材汇总表
   */
  getThreeMaterialsSummary(params) {
    return ipc.invoke(ipcApiRoute.getThreeMaterialsSummary, params);
  },
  // 人材机汇总来源分析
  rcjFromUnit(params) {
    return ipc.invoke(ipcApiRoute.rcjFromUnit, params);
  },
  // 单项人材机汇总来源分析
  singleRcjFromUnit(params) {
    return ipc.invoke(ipcApiRoute.singleRcjFromUnit, params);
  },
  // 人材机汇总来源分析-修改
  rcjFromUnitUpdate(params) {
    return ipc.invoke(ipcApiRoute.rcjFromUnitUpdate, params);
  },
  // 结算人材机汇总来源分析
  rcjFrom(params) {
    return ipc.invoke(ipcApiRoute.rcjFrom, params);
  },
  updateRcjWjcState(params) {
    return ipc.invoke(ipcApiRoute.updateRcjWjcState, params);
  },
  queryRcjWjcState(params) {
    return ipc.invoke(ipcApiRoute.queryRcjWjcState, params);
  },
  //保存费用汇总
  saveTemplateFYHZ(params) {
    return ipc.invoke(ipcApiRoute.saveTemplateFYHZ, { ...params });
  },
  //单位工程人材机汇总 下拉修改
  unitRcjBatchUpdate(params) {
    return ipc.invoke(ipcApiRoute.unitRcjBatchUpdate, { ...params });
  },

};
