/**
 * 全局配置实现service
 */
const CalculationTool = require("../unit_price_composition/compute/CalculationTool");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {Service} = require("../../core");
const {ObjectUtils} = require("../utils/ObjectUtils");
class GlobalConfigurationImplementationService extends Service{


    /**
     * 根据修改过配置去更新数据
     * 1历史版本根据需要修改配置去更新数据
     * @param unitProjects
     * @param constructId
     * @returns {Promise<void>}
     */
    async updateGlobalConfiguration(unitProjects,constructId){
        // let {constructId}= args
        // let project =PricingFileFindUtils.getProjectObjById(constructId);
        // let unitProjects = PricingFileFindUtils.getUnitListByConstructObj(project);
        //
        let config = await this.service.globalConfigurationService.getConfigOnlyInGlobal(constructId)
        if(ObjectUtils.isEmpty(config)){
            return
        }
        //设置功能，单位计算：自然单位、定额单位切换
        let  gcldwDesc= '2'
        for (let i = 0; i < unitProjects.length; i++) {
            let unit = unitProjects[i];
            let measureProjectTables = unit.measureProjectTables;
            let itemBillProjects = unit.itemBillProjects;
            let fbFxDeList = measureProjectTables.filter(item=>item.kind=="04").map(item => item.sequenceNbr);
            let csxmDeList = itemBillProjects.filter(item=>item.kind=="04").map(item => item.sequenceNbr);
            if(fbFxDeList&&fbFxDeList.length>0){
                let calculationTool = new CalculationTool({
                    constructId: unit.constructId,
                    singleId: unit.singleId,
                    unitId: unit.sequenceNbr,
                    allData: itemBillProjects
                });
                calculationTool.calculationDes(fbFxDeList);
            }

            if(csxmDeList&&csxmDeList.length>0){

            }

        }
        //锁定人材机人材机消耗量
        let lockRcjResFlag = true;
        await this.lockRcjRes(lockRcjResFlag)

        //设置功能，清单综合合价计算方式切换
        await this.qdTotalSwitch(unitProjects)
        //设置功能，消耗量取公式计算、取页面值计算
    }

    /**
     * 清单综合合价计算方式切换
     */
    qdTotalSwitch(unitProjects){
        for (let i = 0; i < unitProjects.length; i++) {
            let unit = unitProjects[i];
            let measureProjectTables = unit.measureProjectTables;
            let itemBillProjects = unit.itemBillProjects;
            let fbFxQdList = measureProjectTables.filter(item=>item.kind=="03").map(item => item.sequenceNbr);
            let csxmQdList = itemBillProjects.filter(item=>item.kind=="03").map(item => item.sequenceNbr);
            if(fbFxQdList&&fbFxQdList.length>0){
                let calculationTool = new CalculationTool({
                    constructId: unit.constructId,
                    singleId: unit.singleId,
                    unitId: unit.sequenceNbr,
                    allData: itemBillProjects
                });
                calculationTool.calculationDes(fbFxQdList);
            }

            if(csxmQdList&&csxmQdList.length>0){
                let calculationTool = new CalculationTool({
                    constructId: unit.constructId,
                    singleId: unit.singleId,
                    unitId: unit.sequenceNbr,
                    allData: measureProjectTables
                });
                calculationTool.calculationDes(csxmQdList);
            }

        }
    }

    /**
     * 锁定人材机消耗量
     * @param lockRcjResFlag
     */
    lockRcjRes(lockRcjResFlag,lockRcjResQty){
        if (lockRcjResFlag){
            let constructProjects = Object.values(global.constructProject);
            for (let project of constructProjects) {
                let unitList = PricingFileFindUtils.getUnitList(project.proJectData.sequenceNbr);
                unitList.forEach(unit =>{
                    let {constructProjectRcjs,rcjDetailList} = unit;
                    if (ObjectUtils.isNotEmpty(constructProjectRcjs)){
                        constructProjectRcjs.forEach( k=> k.isLock = lockRcjResQty);
                    }
                    if (ObjectUtils.isNotEmpty(rcjDetailList)){
                        rcjDetailList.forEach( k=> k.isLock = lockRcjResQty);
                    }
                })
            }


        }
    }

    
}

GlobalConfigurationImplementationService.toString = () => '[class GlobalConfigurationImplementationService]';
module.exports = GlobalConfigurationImplementationService;
