
const {ResponseData} = require("../../../common/ResponseData");
const {Controller} = require("../../../core");


/**
 * 人材调整
 */
class JieSuanRcjAdjustmentController extends Controller{


    /**
     * 人材机调整列表
     * @param args
     */
    async unitRcjQuery(args){
        let unitRcjQuery =await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        return  ResponseData.success(unitRcjQuery);
    }

    /**
     * 项目工程级别的人材机列表
     * @return {*}
     */
    async projectRcjList(args){
        let projectRcjQuery =await this.service.jieSuanProject.jieSuanRcjProcess.projectRcjList(args);
        return  ResponseData.success(projectRcjQuery);
    }


    /**
     * 人材机汇总选择
     * @param args
     */
    unitRcjCollectSelect(args){
        return  this.service.jieSuanProject.jieSuanRcjProcess.unitRcjCollectSelect(args);
    }


    /**
     * 自动过滤调差材料
     */
    async filterDifferenceRcj(args){
        return await this.service.jieSuanProject.jieSuanRcjProcess.filterDifferenceRcj(args);
    }


    /**
     * 人材机批量选择调差j
     * @param args
     */
    unitRcjCollectSelectNotarize(args){
        return  this.service.jieSuanProject.jieSuanRcjProcess.unitRcjCollectSelectNotarize(args);
    }


    //统一应用
    unifyUse(args){
        this.service.jieSuanProject.jieSuanRcjStageService.unifyUse(args);
        return  ResponseData.success(true);
    }

    /**
     * 汇总人材机修改
     * @param args
     * @returns {ResponseData}
     */
    changeRcjNewJieSuan(args){
        this.service.jieSuanProject.jieSuanRcjStageService.changeRcjHuiZongJiesuan(args);
        return  ResponseData.success(true);
    }


    /**
     * 单/多期设置
     */
    rcjPeriodsSet(args){
        let {constructId, singleId, unitId,values,type,kind} = args;
        this.service.jieSuanProject.jieSuanRcjStageService.rcjDifferenceInit(constructId, singleId, unitId,values,type,kind)
        return  ResponseData.success(true);
    }





}


JieSuanRcjAdjustmentController.toString = () => '[class JieSuanRcjAdjustmentController]';
module.exports = JieSuanRcjAdjustmentController;