'use strict';

const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const BranchProjectDisplayConstant = require("../enum/BranchProjectDisplayConstant");
const OtherProjectCalculationBaseConstant = require("../enum/OtherProjectCalculationBaseConstant");
const {NumberUtil} = require("../utils/NumberUtil");
const {Snowflake} = require("../utils/Snowflake");
const {ConstructProject} = require("../model/ConstructProject");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {ExcelSheetList} = require("../utils/ExcelSheetList");
const {ExcelUtil} = require("../utils/ExcelUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {StringUtils} = require("../utils/StringUtils");
const {DateUtils} = require("../utils/DateUtils");
const {Service} = require("../../core");
const fs = require("fs");
const ConstantUtil = require("../enum/ConstantUtil");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {arrayToTree} = require("../main_editor/tree");
const {OrganizationInstructions} = require("../model/OrganizationInstructions");

const {MeasureProjectTable} = require("../model/MeasureProjectTable");
const {OtherProjectZygcZgj} = require("../model/OtherProjectZygcZgj");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const {ItemBillProject} = require("../model/ItemBillProject");
const {OtherProjectDayWork} = require("../model/OtherProjectDayWork");
const {OtherProjectServiceCost} = require("../model/OtherProjectServiceCost");
const {OtherProjectZgj} = require("../model/OtherProjectZgj");
const {OtherProjectProvisional} = require("../model/OtherProjectProvisional");
const {OtherProject} = require("../model/OtherProject");
const {UnitProject} = require("../model/UnitProject");
const {SingleProject} = require("../model/SingleProject");
const {getUnitFormatEnum, getDeUnitFormatEnum} = require("../main_editor/rules/format");

class AnalyzingExcelService extends Service {
    constructor(ctx) {
        super(ctx);
        this.qdMap = new Map();
        this.qbExtraTableArray = new Array();
        this.baseFileLevelType = ConstantUtil.BASE_FILE_LEVEL_TYPE;
        this.projectLevelType = ConstantUtil.CONSTRUCT_LEVEL_TYPE;
        this.singleLevelType = ConstantUtil.SINGLE_LEVEL_TYPE;
        this.unitLevelType = ConstantUtil.UNIT_LEVEL_TYPE;
        this.sheetLevelType = ConstantUtil.SHEET_LEVEL_TYPE;
    }

    _resetBieProjectTree(projectNode, subLevelType, subs){

        if(ObjectUtils.isEmpty(projectNode.children)){
            return projectNode;
        }
        subs = subs || [];
        for(let i = 0; i < projectNode.children.length; i++){
            let node = projectNode.children[i];
            if(node.levelType == subLevelType){
                subs.push(node);
                if(subLevelType == this.unitLevelType) {
                    this._resetBieProjectTree(node, this.sheetLevelType, []);
                }else if(subLevelType == this.singleLevelType) {
                    this._resetBieProjectTree(node, this.singleLevelType, []);
                }else{
                    this._resetBieProjectTree(node, subLevelType+1, []);
                }
            }else if(subLevelType == this.singleLevelType && node.levelType == this.unitLevelType){
                subs.push(node);
                this._resetBieProjectTree(node, this.sheetLevelType, []);
            }else{
                this._resetBieProjectTree(node, subLevelType, subs)
            }
        }
        projectNode.children = subs;
    }

    _resetBieProjectTreeOld(projectNode, subLevelType, subs){

        if(ObjectUtils.isEmpty(projectNode.children)){
            return projectNode;
        }
        subs = subs || [];
        for(let i = 0; i < projectNode.children.length; i++){
            let node = projectNode.children[i];
            if(node.levelType == subLevelType){
                subs.push(node);
                if(subLevelType == this.unitLevelType) {
                    this._resetBieProjectTree(node, this.sheetLevelType, []);
                }else{
                    this._resetBieProjectTree(node, subLevelType+1, []);
                }
            }else{
                this._resetBieProjectTree(node, subLevelType, subs)
            }
        }
        projectNode.children = subs;
    }

    async analysis(bizTree) {
        let result = {};

        // 设置工程项目属性
        let projectNode = bizTree.fileLevelTreeNodes[0];

        this._resetBieProjectTree(projectNode, this.singleLevelType);
        await this._readConstructProject(bizTree, projectNode, result);

        const constructId = result.sequenceNbr;
        const biddingType = result.biddingType;


        // 单项处理
        let singleProjects = projectNode.children;
        let singles = result.singles = [];

        for (let i = 0; i < singleProjects.length; i++) {
            let single = await this._readSingle(singleProjects[i], i + 1, constructId, biddingType,bizTree);

            singles.push(single);
        }

        //删除文件夹
        if(fs.existsSync(projectNode.filePath)){
            fs.rm(projectNode.filePath, {recursive: true}, (err) => { });
        }

        // console.log(result);
        let constructProject = new ConstructProject();

        //此处开始组装数据

        if (ObjectUtils.isEmpty(result)) {
            return;
        }
        //1 工程项目
        await this.convertConstructProject(result, constructProject);

        constructProject.gfId = '14';
        constructProject.awfId = '44';
        constructProject.rgfId=await this.service.analyzingXMLService.getDefaultRgf();

        //初始化计税方式
        let jsType;
        if(bizTree.taxCalculationMethod === '1'){
            jsType = 1;
        }else {
            jsType = 0;
        }
        //计税方式
        await this.service.projectTaxCalculationService.importXMLInitProjectTaxCalculation(constructProject,jsType);
        //2 工程项目基本信息暂无数据
        await this.convertConstructProjectJBXX(result,constructProject);
        //3 工程项目编制说明暂无数据
        await this.convertOrganizationInstructions(result, constructProject);
        //4 解析单项工程
        let newSingles = await this.convertSingleProject(result.singles,constructProject);
        constructProject.singleProjects = newSingles;

        //放入内存
        PricingFileWriteUtils.writeToMemory(constructProject);


        return constructProject.sequenceNbr;
    }

    async analysisUnits(params) {
        if(ObjectUtils.isEmpty(params.units)){
            return 200;
        }

        let constructProject = PricingFileFindUtils.getProjectObjById(params.constructId);

        let singleProject =  PricingFileFindUtils.getSingleProject(params.constructId, params.singleId);


        let parentParam = {
            biddingType: constructProject.biddingType,
            constructId: params.constructId,
            sequenceNbr: params.singleId
        }

        let unitSortNo = 1;
        if(ObjectUtils.isNotEmpty(params.singleId)){
            unitSortNo = singleProject.unitProjects?.length || 1;
        }else{
            unitSortNo = constructProject.unitProjectArray?.length || 1;
        }

        let unitsTmp = [];

        for(let i = 0; i < params.units.length; i++){
            let oriUnit = params.units[i];
            if(ObjectUtils.isNotEmpty(oriUnit.errorMsg)){
                continue;
            }

            // 如果传入的定额标准为空，则默认取工程项目定额标准
            oriUnit.deStandardId = oriUnit.deStandardId || constructProject.deStandardId;
            let unitSwap = await this._readUnit(oriUnit, parentParam, unitSortNo++);
            unitsTmp.push(unitSwap);
        }
        if(unitsTmp.length > 0){
            let units = await this.convertUnitProject(unitsTmp, params.singleId, constructProject)

            for (let j = 0; j < units.length; j++) {
                let up = units[j];
                if(ObjectUtils.isNotEmpty(params.singleId)){
                    singleProject.unitProjects = singleProject.unitProjects || [];
                    singleProject.unitProjects.push(up);
                }else{
                    constructProject.unitProjectArray = constructProject.unitProjectArray || [];
                    constructProject.unitProjectArray.push(up);
                }

                //初始化计税方式
                await this.service.projectTaxCalculationService.initUnitTaxCalculationMethod(constructProject, up);
                //保存取费文件
                await this.service.baseFeeFileService.initFeeFile(up);

                await this.service.constructProjectService.reInitUnit(up, constructProject);

                let args = {
                    constructId: constructProject.sequenceNbr,
                    singleId: params.singleId,
                    unitId: up.sequenceNbr
                }
                await this.service.unitCostCodePriceService.countCostCodePrice(args)
            }


        }

        return 200;
    }

    /**
     * 解析工程项目
     * @param data
     * @param constructProject
     * @returns {Promise<void>}
     */
    async convertConstructProject(data, constructProject) {
        ConvertUtil.setDstBySrc(data, constructProject)
    }

    async convertConstructProjectJBXX(result,constructProject){
        //工程基本信息
        this.service.constructProjectService.initProjectOrUnitData(constructProject, 1);
        let constructProjectJBXX = constructProject.constructProjectJBXX;
        for (let i = 0; i < constructProjectJBXX.length; i++) {
            switch (constructProjectJBXX[i].name) {
                case '工程名称':
                    constructProjectJBXX[i].remark = constructProject.constructName;
                    break;
                case '招标人(发包人)':
                    constructProjectJBXX[i].remark = result.招标人;
                    break;
                case '工程造价咨询人':
                    constructProjectJBXX[i].remark = result.造价咨询人;
                    break;
                case '编制时间':
                    constructProjectJBXX[i].remark = result.编制时间;
                    break;
                case '招标人(发包人)法人或其授权人':
                    constructProjectJBXX[i].remark = result.法定代表人或委托代理人;
                    break;
                case '编制人':
                    constructProjectJBXX[i].remark = result.编制人;
                    break;
                case '核对人(复核人)':
                    constructProjectJBXX[i].remark = result.复核人;
                    break;
            }

        }
    }


    /**
     * 编制说明
     * @param result
     * @param constructProject
     * @returns {Promise<void>}
     */
    async convertOrganizationInstructions(result,constructProject){
        let o = new OrganizationInstructions();
        o.context =result.编制说明;

        constructProject.organizationInstructions = o;
    }
    /**
     * 解析单项工程
     * @param 单项工程
     * @param constructProject
     */
    async convertSingleProject(singles,constructProject) {
        let singleProjects = new Array();
        if (!ObjectUtils.isEmpty(singles)) {
            for (let i = 0; i < singles.length; i++) {
                let singleProject = new SingleProject();
                ConvertUtil.setDstBySrc(singles[i], singleProject)
                //解析单位工程
                singleProject.sequenceNbr = Snowflake.nextId();
                if(ObjectUtils.isNotEmpty(singles[i].units)){
                    let newUnits = await this.convertUnitProject(singles[i].units,singleProject.sequenceNbr,constructProject);
                    singleProject.unitProjects = newUnits;
                }else if(ObjectUtils.isNotEmpty(singles[i].subSingles)) {
                    let newSingles = await this.convertSingleProject(singles[i].subSingles,constructProject);
                    singleProject.subSingleProjects = newSingles;
                }
                singleProjects.push(singleProject);
            }
        }

        return singleProjects;
    }

    /**
     * 单位工程
     * @param units
     * @param singleProject
     * @returns {Promise<any[]>}
     */
    async convertUnitProject(units, singleId,constructProject) {
        let unitProjects = new Array();
        if (!ObjectUtils.isEmpty(units)) {
            for (let i = 0; i < units.length; i++) {
                let unitProject = new UnitProject();

                let unit = units[i];
                ConvertUtil.setDstBySrc(unit, unitProject)
                unitProject.sequenceNbr= Snowflake.nextId();
                unitProject.rgfId= constructProject.rgfId;
                // 添加工程基本信息 ---单位层级
                this.service.constructProjectService.initProjectOrUnitData(unitProject, 3);
                // 编制说明 ---单位层级
                this.service.constructProjectService.initProjectOrUnitBZSM(3, unitProject);
                // 单位工程费用汇总 调用初始化单位工程汇总表

                //分部分项
                await this.convertItemBill(unit[ExcelSheetList.SHEET6], unitProject);
                //单价措施
                await this.convertMeasureTableDJ(unit[ExcelSheetList.SHEET7], unitProject);
                //总价措施
                await this.convertMeasureTableZJ(unit[ExcelSheetList.SHEET8], unitProject);
                //其他项目
                await this.convertOtherProjects(unit[ExcelSheetList.SHEET9], unitProject);
                //暂列金额
                await this.convertProvisional(unit[ExcelSheetList.SHEET10], unitProject);
                //暂估价
                await this.convertZgjSums(unit[ExcelSheetList.SHEET11], unitProject);

                //总承包服务费
                await this.convertServiceCosts(unit[ExcelSheetList.SHEET12],unitProject);

                //签证与索赔接口
                await this.service.otherProjectQzspService.addQzsp(unit.签证与索赔计价表, unitProject);

                unitProject.otherProjects= ObjectUtils.isNotEmpty(this.qbExtraTableArray)?this.qbExtraTableArray:unitProject.otherProjects;
                //计日工
                await this.convertDayWorks(unit[ExcelSheetList.SHEET13], unitProject);
                unitProject.spId = singleId;
                unitProject.constructProjectRcjs = [];
                unitProject.rcjDetailList = [];
                unitProject.deStandardId = unit.deStandardId;
                await this._setDeStandard(unitProject, unitProject.deStandardId);

                unitProjects.push(unitProject);
            }

        }

        return unitProjects;
    }

    async convertItemBill(fbfxArray, unitProject) {
        let itemBillProjectArray = [];
        let itemBillProject = new ItemBillProject();
        let topId = Snowflake.nextId();
        itemBillProject.sequenceNbr = topId;
        itemBillProject.name = '单位工程';
        itemBillProject.name = '单位工程';
        itemBillProject.kind = BranchProjectLevelConstant.top;
        itemBillProject.constructId = unitProject.constructId;
        itemBillProject.spId = unitProject.spId;
        itemBillProject.unitId = unitProject.sequenceNbr;
        itemBillProject.displaySign = BranchProjectDisplayConstant.open;
        itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
        itemBillProjectArray.push(itemBillProject);
        let dispNo = 1;
        // 父id
        let parentId = '' + topId;
        let topFbId =''
        let qdOrFB;// 存放上条数据类型 清单 true  分布 false
        if (!ObjectUtils.isEmpty(fbfxArray)) {
            for (let i = 0; i < fbfxArray.length; i++) {
                let fbfx = fbfxArray[i];
                if(fbfx.项目名称.indexOf('分部小计')!=-1){
                    continue
                }
                let itemBillProject = new ItemBillProject();
                // 新id
                let id = Snowflake.nextId();
                itemBillProject.sequenceNbr = id;
                if (ObjectUtils.isEmpty(fbfx.序号) && ObjectUtils.isEmpty(fbfx.项目特征)) {
                    //分布或者子分部
                    if (ObjectUtils.isEmpty(qdOrFB)) {//说明是第一条数据
                        itemBillProject.kind = BranchProjectLevelConstant.fb;
                        itemBillProject.parentId = ('' + parentId);
                        topFbId  = id
                    }

                    if (qdOrFB === false) {//说明上条数据是分布
                        itemBillProject.kind = BranchProjectLevelConstant.zfb;
                        itemBillProject.parentId = ('' + topFbId);
                    } else if(qdOrFB === true){//说明上条数据是清单

                        //判断下条数据是不是分布 如果是分布就设置为fb 如果是清单设置为
                        if(fbfxArray[i+1]){
                            if (ObjectUtils.isEmpty(fbfxArray[i+1].序号) && ObjectUtils.isEmpty(fbfxArray[i+1].项目特征)) {
                                //下条数据是分布
                                parentId = '' + topId;
                                itemBillProject.kind = BranchProjectLevelConstant.fb;
                                topFbId = id //此处是为了处理碰到单独的分布直接认定为子分部所以存储顶级分部的ID
                                itemBillProject.parentId = ('' + parentId);
                            }else {
                                //下条数据是清单 当前处理为子分部（xhc） 如果当前itemBillProjectArray中没有子分部说明就是分布
                                if(itemBillProjectArray.find(item=>item.kind==BranchProjectLevelConstant.zfb)){
                                    itemBillProject.kind = BranchProjectLevelConstant.zfb;
                                    itemBillProject.parentId = ('' + topFbId);
                                }else {
                                    itemBillProject.kind = BranchProjectLevelConstant.fb;
                                    itemBillProject.parentId = ('' + topId);
                                }

                            }
                        }
                    }
                    itemBillProject.name = fbfx.项目名称;
                    itemBillProject.name = fbfx.项目名称;

                    itemBillProject.constructId = unitProject.constructId;
                    itemBillProject.spId = unitProject.spId;
                    itemBillProject.unitId = unitProject.sequenceNbr;
                    itemBillProject.displaySign = BranchProjectDisplayConstant.open;
                    itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
                    parentId = '' + id
                    qdOrFB = false;
                } else {
                    //清单
                    itemBillProject.name = fbfx.项目名称;
                    itemBillProject.name = fbfx.项目名称;
                    itemBillProject.bdCode = fbfx.项目编码;
                    itemBillProject.fxCode = fbfx.项目编码;
                    itemBillProject.unit = fbfx.计量单位;
                    // itemBillProject.quantity = fbfx.工程数量;
                    itemBillProject.quantity = NumberUtil.numberScale(fbfx.工程数量,getUnitFormatEnum(itemBillProject.unit, unitProject.constructId).value) + "";
                    //let 单位num = fbfx.计量单位.replace(/[^0-9].*/ig, '') !== '' ? fbfx.计量单位.replace(/[^0-9].*/ig, '') : 1;
                    let 单位num = 1;
                    if(!ObjectUtils.isEmpty(fbfx.计量单位) && fbfx.计量单位.replace(/[^0-9].*/ig, '') !== ''){
                        单位num =  fbfx.计量单位.replace(/[^0-9].*/ig, '');
                    }

                    // try{
                    //     itemBillProject.quantityExpression = NumberUtil.multiplyToStringExEnd0(单位num, fbfx.工程数量, ConstantUtil.QD_DE_DECIMAL_POINT);
                    // }catch (err){
                    //     console.log(err)
                    // }
                    itemBillProject.quantityExpression = NumberUtil.multiplyToStringExEnd0(单位num, fbfx.工程数量, ConstantUtil.QD_DE_DECIMAL_POINT);
                    itemBillProject.quantityExpressionNbr = NumberUtil.multiplyToStringExEnd0(单位num, fbfx.工程数量, ConstantUtil.QD_DE_DECIMAL_POINT);
                    itemBillProject.projectAttr = fbfx.项目特征;
                    itemBillProject.kind = BranchProjectLevelConstant.qd;
                    itemBillProject.dispNo = (dispNo++) + '';
                    itemBillProject.parentId = ('' + parentId);
                    itemBillProject.constructId = unitProject.constructId;
                    itemBillProject.spId = unitProject.spId;
                    itemBillProject.unitId = unitProject.sequenceNbr;
                    itemBillProject.displaySign = BranchProjectDisplayConstant.noSign;
                    itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
                    itemBillProject.isLocked = 1;
                    if(ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode))){
                        //如果map中没有去查数据库
                        let res = await this.service.baseListService.queryQdByCode(itemBillProject.fxCode);
                        if(!ObjectUtils.isEmpty(res)){
                            this.qdMap.set(itemBillProject.fxCode,res)
                        }
                    }
                    itemBillProject.standardId = !ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode))? this.qdMap.get(itemBillProject.fxCode).sequenceNbr:'';
                    itemBillProject.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode)) ? 0 : 1;
                    qdOrFB = true;
                }
                itemBillProjectArray.push(itemBillProject);
            }

        }
        unitProject.itemBillProjects = arrayToTree(itemBillProjectArray);

    }

    /**
     * 单价措施
     * @param djcsArray
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertMeasureTableDJ(djcsArray,unitProject){
        if(ObjectUtils.isEmpty(djcsArray)){
            return ;
        }
        let djMeasureProjectTableArray = new Array();
        if (!ObjectUtils.isEmpty(djcsArray)) {

            let parentId;
            let dispNo =1;
            for (let i = 0; i < djcsArray.length; i++) {
                let djcs = djcsArray[i];

                let measureProjectTable = new MeasureProjectTable();
                let id = Snowflake.nextId();
                measureProjectTable.sequenceNbr = id;
                if (ObjectUtils.isEmpty(djcs.序号) && ObjectUtils.isEmpty(djcs.项目特征)) {
                    //标题

                    measureProjectTable.name = djcs.项目名称;
                    measureProjectTable.name = djcs.项目名称;
                    measureProjectTable.constructId = unitProject.constructId;
                    measureProjectTable.spId = unitProject.spId;
                    measureProjectTable.unitId = unitProject.sequenceNbr;
                    measureProjectTable.kind = BranchProjectLevelConstant.zfb;
                    measureProjectTable.displaySign = BranchProjectDisplayConstant.open;
                    measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                    measureProjectTable.adjustmentCoefficient = 1;
                    parentId = id;
                    djMeasureProjectTableArray.push(measureProjectTable)
                }else {
                    //清单
                    measureProjectTable.dispNo = (dispNo++)+'';
                    measureProjectTable.name = djcs.项目名称;
                    measureProjectTable.name = djcs.项目名称;
                    measureProjectTable.bdCode = djcs.项目编码;
                    measureProjectTable.fxCode = djcs.项目编码;
                    measureProjectTable.projectAttr = djcs.项目特征;
                    measureProjectTable.unit = djcs.计量单位;
                    // measureProjectTable.quantity = djcs.工程数量;
                    measureProjectTable.quantity = NumberUtil.numberScale(djcs.工程数量,getUnitFormatEnum(measureProjectTable.unit, unitProject.constructId).value) ;
                    //let 单位num = djcs.计量单位.replace(/[^0-9].*/ig,'')!==''?djcs.计量单位.replace(/[^0-9].*/ig,''): 1;
                    let 单位num = 1;
                    if(!ObjectUtils.isEmpty(djcs.计量单位) && djcs.计量单位.replace(/[^0-9].*/ig, '') !== ''){
                        单位num =  djcs.计量单位.replace(/[^0-9].*/ig,'');
                    }
                    measureProjectTable.quantityExpression =NumberUtil.multiplyToStringExEnd0(单位num, djcs.工程数量,ConstantUtil.QD_DE_DECIMAL_POINT);
                    measureProjectTable.quantityExpressionNbr = NumberUtil.multiplyToStringExEnd0(单位num, djcs.工程数量,ConstantUtil.QD_DE_DECIMAL_POINT);
                    measureProjectTable.kind = BranchProjectLevelConstant.qd;
                    //单价措施类型
                    measureProjectTable.parentId =parentId;
                    measureProjectTable.constructId = unitProject.constructId;
                    measureProjectTable.spId = unitProject.spId;
                    measureProjectTable.unitId = unitProject.sequenceNbr;
                    measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
                    measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                    measureProjectTable.adjustmentCoefficient = 1;
                    measureProjectTable.isLocked = 1;

                    if(ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                        //如果map中没有去查数据库
                        let res =await this.service.baseListService.queryQdByCode(measureProjectTable.fxCode);
                        if(!ObjectUtils.isEmpty(res)){
                            this.qdMap.set(measureProjectTable.fxCode,res)
                        }
                    }
                    if(!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                        if (!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode)){
                            measureProjectTable.zjcsClassCode =  Number(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode);
                        }
                    }
                    measureProjectTable.standardId = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))? this.qdMap.get(measureProjectTable.fxCode).sequenceNbr:'';
                    measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;

                    djMeasureProjectTableArray.push(measureProjectTable);
                }


            }
        }

        unitProject.djMeasureProjectTableArray = djMeasureProjectTableArray;

    }

    /**
     * 总价措施
     * @param zjcsArray
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertMeasureTableZJ(zjcsArray,unitProject){
        if(ObjectUtils.isEmpty(zjcsArray)){
            return ;
        }
        let dispNo =1;
        let awfMeasureProjectTableArray = new Array();
        let zjMeasureProjectTableArray = new Array();
        for (let i = 0; i < zjcsArray.length; i++) {
            let zjcs = zjcsArray[i];
            if(!ObjectUtils.isEmpty(zjcs.项目编码)){
                //清单
                let measureProjectTable = new MeasureProjectTable();
                let id = Snowflake.nextId();
                measureProjectTable.sequenceNbr = id;
                measureProjectTable.dispNo = (dispNo++)+'';
                measureProjectTable.name = zjcs.项目名称;
                measureProjectTable.name = zjcs.项目名称;
                measureProjectTable.bdCode = zjcs.项目编码;
                measureProjectTable.fxCode = zjcs.项目编码;
                measureProjectTable.projectAttr = zjcs.项目特征;
                measureProjectTable.unit = '项';
                measureProjectTable.quantity = 1;
                measureProjectTable.quantityExpression = '1';
                measureProjectTable.quantityExpressionNbr = 1;
                measureProjectTable.kind = BranchProjectLevelConstant.qd;
                measureProjectTable.constructId = unitProject.constructId;
                measureProjectTable.spId = unitProject.spId;
                measureProjectTable.unitId = unitProject.sequenceNbr;
                measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
                measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                measureProjectTable.adjustmentCoefficient = 1;
                measureProjectTable.isLocked = 1;
                if(ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                    //如果map中没有去查数据库
                    let res =await this.service.baseListService.queryQdByCode(measureProjectTable.fxCode);
                    if(!ObjectUtils.isEmpty(res)){
                        this.qdMap.set(measureProjectTable.fxCode,res)
                    }
                }
                if(!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                    if (!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode)){
                        measureProjectTable.zjcsClassCode =  Number(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode);
                    }
                }
                measureProjectTable.standardId = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))? this.qdMap.get(measureProjectTable.fxCode).sequenceNbr:'';
                measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;
                if(zjcs.项目名称.indexOf('安全生产、文明施工费')!== -1){
                    measureProjectTable.unit = '项';
                    awfMeasureProjectTableArray.push(measureProjectTable);
                    dispNo =1;//序号重新排
                }else {
                    zjMeasureProjectTableArray.push(measureProjectTable);
                }

            }

        }

        unitProject.awfMeasureProjectTableArray = awfMeasureProjectTableArray;
        unitProject.zjMeasureProjectTableArray = zjMeasureProjectTableArray;

    }

    /**
     * 其他项目
     * @returns {Promise<void>}
     */
    async convertOtherProjects(qtxmArray,unitProject){
        if(ObjectUtils.isEmpty(qtxmArray)){
            // 初始化其他项目
            let res =await this.service.otherProjectService.getInitOtherProjectList();
            unitProject.otherProjects= res;
            this.qbExtraTableArray = res;
            return ;
        }
        this.qbExtraTableArray = [];
        let JE = '金额(元)';
        for (let i = 0; i < qtxmArray.length; i++) {
            let qtxm = qtxmArray[i];
            let otherProject = new OtherProject();
            otherProject.sequenceNbr = Snowflake.nextId();
            otherProject.constructId = unitProject.constructId;
            otherProject.spId = unitProject.spId;
            otherProject.unitId = unitProject.sequenceNbr;
            otherProject.sortNo = 1;
            otherProject.extraName = qtxm.项目名称;
            otherProject.dispNo = qtxm.序号;
            otherProject.total = NumberUtil.costPriceAmountFormat(qtxm[JE]);
            otherProject.unit = '项';
            otherProject.description = qtxm.备注;
            otherProject.amount =1 ;
            if('暂列金额' === qtxm.项目名称){
                otherProject.type = OtherProjectCalculationBaseConstant.zljr;
                otherProject.markSj = 1;
                otherProject.markSafa = 1;
                otherProject.putOntotalFlag = true;
            }else if('暂估价' === qtxm.项目名称){
                otherProject.markSj = 1;
                otherProject.markSafa = 1;
                otherProject.putOntotalFlag = true;
            }else if('专业工程暂估价' === qtxm.项目名称){
                otherProject.type = OtherProjectCalculationBaseConstant.zygczgj;
                otherProject.markSj = 1;
                // otherProject.markSafa = 1;
            }else if('总承包服务费' === qtxm.项目名称){
                otherProject.type = OtherProjectCalculationBaseConstant.zcbfwf;
                otherProject.markSj = 1;
                otherProject.markSafa = 1;
                otherProject.putOntotalFlag = true;
            }else if('计日工' === qtxm.项目名称){
                otherProject.type = OtherProjectCalculationBaseConstant.jrg;
                otherProject.markSj = 1;
                otherProject.markSafa = 1;
                otherProject.putOntotalFlag = true;
            }else {
                otherProject.markSj = 0;
                otherProject.markSafa = 0;
            }
           this.qbExtraTableArray.push(otherProject);
        }



    }

    /**
     * 暂列金
     * @param zljArray
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertProvisional(zljArray,unitProject){
        if(ObjectUtils.isEmpty(zljArray)){
            //暂列金初始化
            let otherProjectZljeList = this.service.otherProjectService.getInitOtherProjectZljeList(unitProject);
            unitProject.otherProjectProvisionals = otherProjectZljeList;
            return;
        }
        let otherProjectProvisionalArray = new Array();

        let qtJxTotal = 0;
        let qtCsTotal = 0;
        let dispNo = 1;
        let je = '暂定金额(元)'
        for (let i = 0; i < zljArray.length; i++) {
            let zlj = zljArray[i];
            for (let zljKey in zlj) {
                if (zljKey.includes("暂定金额")) {
                    je = zljKey;
                    break;
                }
            }
            let otherProjectProvisional = new OtherProjectProvisional();
            otherProjectProvisional.sequenceNbr = Snowflake.nextId();
            otherProjectProvisional.name = zlj.项目名称;
            otherProjectProvisional.unit = zlj.计量单位;
            otherProjectProvisional.provisionalSum = NumberUtil.costPriceAmountFormat(zlj[je]);
            otherProjectProvisional.sortNo = i+1;
            otherProjectProvisional.dispNo = (dispNo++)+'';
            otherProjectProvisional.description = zlj.备注;
            otherProjectProvisional.constructId = unitProject.constructId;
            otherProjectProvisional.spId = unitProject.spId;
            otherProjectProvisional.unitId = unitProject.sequenceNbr;


            otherProjectProvisional.amount = 1 ;
            otherProjectProvisional.price = otherProjectProvisional.provisionalSum;//单价 没有单价所以直接默认赋值暂定金额
            otherProjectProvisional.taxRemoval = 3 ; //除税系数(%)
            // 进项合计 暂定金额*除税系数
            otherProjectProvisional.jxTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectProvisional.provisionalSum,otherProjectProvisional.taxRemoval/100)) ;
            otherProjectProvisional.csPrice = NumberUtil.subtract(otherProjectProvisional.provisionalSum,otherProjectProvisional.jxTotal);
            otherProjectProvisional.csTotal = otherProjectProvisional.csPrice;

            qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectProvisional.jxTotal);
            qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectProvisional.csTotal);
            otherProjectProvisionalArray.push(otherProjectProvisional);
        }

        unitProject.otherProjectProvisionals = otherProjectProvisionalArray;
        this.qbExtraTableArray[0].jxTotal =qtJxTotal;
        this.qbExtraTableArray[0].csTotal =qtCsTotal;
    }

    /**
     * 暂估价
     * @param zgjArray
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertZgjSums(zgjArray,unitProject){
        if(ObjectUtils.isEmpty(zgjArray)){
            //专业工程暂估价初始化
            let otherProjectZygcZgjList = this.service.otherProjectService.getInitOtherProjectZygcZgjList(unitProject);
            unitProject.otherProjectZygcZgjs = otherProjectZygcZgjList;
            return;
        }

        //获取专业工程暂估价下标
        let indexZygcZgj;
        for (let i = 0; i < zgjArray.length; i++) {
            let zgj = zgjArray[i];
            if(zgj.暂估价名称 === '专业工程暂估价'){
                indexZygcZgj = i;
            }
        }
        let zygcZgjArray = zgjArray.slice(indexZygcZgj+1);
        if(ObjectUtils.isEmpty(zygcZgjArray)){
            return;
        }
        let qtJxTotal = 0;
        let qtCsTotal = 0;
        let otherProjectZgjArray =new Array();
        let JE ='暂估价(元)'
        for (let i = 0; i < zygcZgjArray.length; i++) {
            let zygcZgj = zygcZgjArray[i];
            let otherProjectZgj = new OtherProjectZygcZgj();
            otherProjectZgj.sequenceNbr = Snowflake.nextId();
            otherProjectZgj.total   =   zygcZgj[JE] === '/'? 0 :zygcZgj[JE]; //许红成说 暂估价 中 专业工程暂估价 若excel xml中 金额为“/”时 按照0记取
            otherProjectZgj.total = NumberUtil.costPriceAmountFormat(otherProjectZgj.total);
            otherProjectZgj.name   = zygcZgj.暂估价名称;
            otherProjectZgj.content   = zygcZgj.规格或工程内容;
            otherProjectZgj.unit   = zygcZgj.单位;
            otherProjectZgj.dispNo   = ''+(i+1);
            otherProjectZgj.sortNo   = i;
            otherProjectZgj.description = zygcZgj.备注;
            otherProjectZgj.constructId = unitProject.constructId;
            otherProjectZgj.spId = unitProject.spId;
            otherProjectZgj.unitId = unitProject.sequenceNbr;

            otherProjectZgj.amount = 1 ;
            otherProjectZgj.price = otherProjectZgj.total;//单价 没有单价所以直接默认赋值暂定金额
            otherProjectZgj.taxRemoval = 3 ; //除税系数(%)
            // 进项合计 暂定金额*除税系数
            otherProjectZgj.jxTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectZgj.total,otherProjectZgj.taxRemoval/100)) ;
            otherProjectZgj.csPrice = NumberUtil.subtract(otherProjectZgj.total,otherProjectZgj.jxTotal);
            otherProjectZgj.csTotal = otherProjectZgj.csPrice;

            qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectZgj.jxTotal);
            qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectZgj.csTotal);

            otherProjectZgjArray.push(otherProjectZgj);
        }
        unitProject.otherProjectZygcZgjs = otherProjectZgjArray;
        this.qbExtraTableArray[1].jxTotal =qtJxTotal;
        this.qbExtraTableArray[1].csTotal =qtCsTotal;
        this.qbExtraTableArray[4].jxTotal =qtJxTotal;
        this.qbExtraTableArray[4].csTotal =qtCsTotal;

    }


    /**
     * 总承包服务费
     * @param zcbfwfArray
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertServiceCosts(zcbfwfArray,unitProject){

        if(ObjectUtils.isEmpty(zcbfwfArray)){
            unitProject.otherProjectServiceCosts =this.service.otherProjectService.getInitOtherProjectZcbfwfList();
            return ;
        }
        // 根据类型分组

        let zygcList = new Array();
        let clList   = new Array();
        let sbList   = new Array();
        for (let i = 0; i < zcbfwfArray.length; i++) {
            if(zcbfwfArray[i].序号.substring(0,2) === '1.'){
                zygcList.push(zcbfwfArray[i]);
            }else if(zcbfwfArray[i].序号.substring(0,2) === '2.'){
                clList.push(zcbfwfArray[i]);
            }else if(zcbfwfArray[i].序号.substring(0,2) === '3.'){
                sbList.push(zcbfwfArray[i]);
            }
        }
        unitProject.otherProjectServiceCosts = new Array();
        await this.setServiceCosts(unitProject, zygcList, '1', '招标人另行发包专业工程');
        await this.setServiceCosts(unitProject, clList, '2', '招标人供应材料');
        await this.setServiceCosts(unitProject, sbList, '3', '招标人供应设备');
        let ts = unitProject.otherProjectServiceCosts.filter(item =>item.dispNo.indexOf('.')!== -1);
        this.qbExtraTableArray[5].total = Number(ts.reduce((accumulator, item) => {
            return NumberUtil.add(accumulator,item.fwje)  ;
        }, 0).toFixed(2))

        this.service.otherProjectServiceCostService.updateBiaotiTotal(unitProject);
    }


    /**
     * 总承包服务费
     * @param unitProject
     * @param list
     * @param dispNo
     * @param name
     * @returns {Promise<void>}
     */
    async  setServiceCosts(unitProject, list, dispNo, name) {
        let serviceCostsArray = new Array();
        let parentsUuid = Snowflake.nextId();
        let serviceCostsParents = new OtherProjectServiceCost();
        serviceCostsParents.sequenceNbr = parentsUuid;
        serviceCostsParents.dispNo = dispNo;
        serviceCostsParents.fxName = name;
        serviceCostsParents.sortNo = 0;
        serviceCostsParents.constructId = unitProject.constructId;
        serviceCostsParents.spId = unitProject.spId;
        serviceCostsParents.unitId = unitProject.sequenceNbr;
        serviceCostsParents.dataType = 1;

        serviceCostsArray.push(serviceCostsParents);

        let XMJE = '项目金额(元)';
        let FL = '费率(%)';
        let JE = '金额(元)';
        if (!ObjectUtils.isEmpty(list)) {
            for (let i = 0; i < list.length; i++) {
                let model = list[i];
                let uuid = Snowflake.nextId();
                let otherProjectServiceCost = new OtherProjectServiceCost();
                otherProjectServiceCost.sequenceNbr  = uuid;
                otherProjectServiceCost.xmje  = NumberUtil.costPriceAmountFormat(model[XMJE]);
                otherProjectServiceCost.dispNo  = dispNo+'.'+(i+1);
                otherProjectServiceCost.fxName  = model.项目名称;
                otherProjectServiceCost.fwje  = NumberUtil.costPriceAmountFormat(model[JE]);
                otherProjectServiceCost.rate  = model[FL];
                otherProjectServiceCost.parentId  = parentsUuid;
                otherProjectServiceCost.constructId = unitProject.constructId;
                otherProjectServiceCost.spId = unitProject.spId;
                otherProjectServiceCost.unitId = unitProject.sequenceNbr;
                otherProjectServiceCost.sortNo  = i + 1;
                otherProjectServiceCost.amount  = 1;
                otherProjectServiceCost.dataType = 2;
                serviceCostsArray.push(otherProjectServiceCost);
            }
        }
        unitProject.otherProjectServiceCosts.push(...serviceCostsArray);

    }



    /**
     * 计日工
     * @param jrgArray
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertDayWorks(jrgArray,unitProject){
        if(ObjectUtils.isEmpty(jrgArray)){
            unitProject.otherProjectDayWorks =this.service.otherProjectService.getInitOtherProjectJrgList();
            return;
        }

        let rgList = new Array();
        let clList   = new Array();
        let jxList   = new Array();
        for (let i = 0; i < jrgArray.length; i++) {
            if(jrgArray[i].序号 != undefined){
                if(jrgArray[i].序号.substring(0,2) === '1.'){
                rgList.push(jrgArray[i]);
                }else if(jrgArray[i].序号.substring(0,2) === '2.'){
                    clList.push(jrgArray[i]);
                }else if(jrgArray[i].序号.substring(0,2) === '3.'){
                    jxList.push(jrgArray[i]);
                }
            }

        }
        unitProject.otherProjectDayWorks = new Array();
        await this.setDayWorks(unitProject, rgList, '1', '人工');
        await this.setDayWorks(unitProject, clList, '2', '材料');
        await this.setDayWorks(unitProject, jxList, '3', '机械');
        let ts = unitProject.otherProjectServiceCosts.filter(item =>item.dispNo.indexOf('.')!== -1);
        this.qbExtraTableArray[6].jxTotal = Number(ts.reduce((accumulator, item) => {
            return NumberUtil.add(accumulator,item.jxTotal)  ;
        }, 0).toFixed(2))
        this.qbExtraTableArray[6].csTotal = Number(ts.reduce((accumulator, item) => {
            return NumberUtil.add(accumulator,item.csTotal)  ;
        }, 0).toFixed(2))

        this.service.otherProjectDayWorkService.updateBiaotiTotal(unitProject);
        this.service.otherProjectDayWorkService.updateHuiZongTotal(unitProject);
        //this.service.otherProjectService.updateAllOtherProjectsXml(unitProject);
    }

    async  setDayWorks(unitProject, list, dispNo, name) {
        let dayWorksArray = new Array();

        if(name === '人工'){
            //计日工汇总
            let otherProject0 = new OtherProjectDayWork();
            otherProject0.sequenceNbr =Snowflake.nextId();
            otherProject0.worksName = '计日工费用汇总';
            otherProject0.jinzhiFlag=true;
            otherProject0.dataType = 0;
            otherProject0.csTotal = 0;
            otherProject0.jxTaxAmount = 0;
            otherProject0.total = 0;
            dayWorksArray.push(otherProject0);
        }

        let parentsUuid = Snowflake.nextId();
        let otherProjectDayWork = new OtherProjectDayWork();
        otherProjectDayWork.sequenceNbr = parentsUuid;
        otherProjectDayWork.dispNo = dispNo;
        otherProjectDayWork.worksName = name;
        otherProjectDayWork.sortNo = 0;
        otherProjectDayWork.constructId = unitProject.constructId;
        otherProjectDayWork.spId = unitProject.spId;
        otherProjectDayWork.unitId = unitProject.sequenceNbr;
        otherProjectDayWork.dataType = 1;
        dayWorksArray.push(otherProjectDayWork);

        let XMJE = '项目金额(元)';
        let FL = '费率(%)';
        let ZHDJ = '综合单价(元)';
        if (!ObjectUtils.isEmpty(list)) {
            for (let i = 0; i < list.length; i++) {
                let model = list[i];
                for (let keyProperty in model) {
                    if (keyProperty.includes("综合单价")) {
                        ZHDJ = keyProperty;
                        break;
                    }
                }
                otherProjectDayWork = new OtherProjectDayWork();
                otherProjectDayWork.sequenceNbr = Snowflake.nextId();
                otherProjectDayWork.worksName =  model.名称;
                otherProjectDayWork.specification =  model.规格型号;
                otherProjectDayWork.unit = model.计量单位;
                otherProjectDayWork.tentativeQuantity = NumberUtil.qtxmAmountFormat(model.数量);


                otherProjectDayWork.price = NumberUtil.costPriceAmountFormat(model[ZHDJ]);
                otherProjectDayWork.total = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.tentativeQuantity, otherProjectDayWork.price));
                otherProjectDayWork.taxRemoval = 0;
                if(name === '材料'){
                    otherProjectDayWork.taxRemoval =  11.28;
                }
                if(name === '机械'){
                    otherProjectDayWork.taxRemoval =  8.66;
                }
                otherProjectDayWork.jxTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.total,otherProjectDayWork.taxRemoval/100));
                otherProjectDayWork.csPrice = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.price,(100-otherProjectDayWork.taxRemoval)/100)) ;
                otherProjectDayWork.csTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.total,(100-otherProjectDayWork.taxRemoval)/100)) ;


                otherProjectDayWork.dispNo = dispNo+'.'+(i+1);
                otherProjectDayWork.parentId = parentsUuid;
                otherProjectDayWork.constructId = unitProject.constructId;
                otherProjectDayWork.spId = unitProject.spId;
                otherProjectDayWork.unitId = unitProject.sequenceNbr;
                otherProjectDayWork.dataType = 2;
                dayWorksArray.push(otherProjectDayWork);
            }
        }
        unitProject.otherProjectDayWorks.push(...dayWorksArray);

    }

    _readFbFx(rows, sheetName, title) {
        if(ObjectUtils.isEmpty(rows)){
            return [];
        }
        let itemBills = [];
        const headerNumber = 4;
        const titleRows = [2, 3];

        // 表格列标题获取
        let titleMap = this._getTitleMap(rows, titleRows);

        let n = headerNumber;

        // 循环处理每行数据
        while (n < rows.length) {
            const r = rows[n];
            let newItemBill = {};
            let ifNotEmptyRow = false;

            // 处理分部分项行数据，生成目标数据
            for(let [key, val] of titleMap){
                newItemBill[val] =  ObjectUtils.isEmpty(r.get(key)) ? null : ("" + r.get(key)).replaceAll(/^\s+|\s+$/g, "");
                ifNotEmptyRow = ifNotEmptyRow || ObjectUtils.isNotEmpty(newItemBill[val]);
            }

            let xmmc = ObjectUtils.isNotEmpty(newItemBill['项目名称']) ? newItemBill['项目名称'].replaceAll(/\s/g, "") : "";
            if(xmmc == '本页小计' || xmmc == '合计'){
                let skipRows = headerNumber + 1;
                let nextRowFirstValue = null;
                if(n < rows.length - 1 && title){
                    skipRows = rows[n+1].get(1) == title ? skipRows : 1;
                }
                n += skipRows;
                continue;
            }

            //是否是拆分行（非拆分后第一行）
            let ifSplitRow = ObjectUtils.isEmpty(newItemBill['序号'])
                && (
                    ObjectUtils.isNotEmpty(newItemBill['项目特征'])
                    || ObjectUtils.isNotEmpty(newItemBill['计量单位'])
                    || ObjectUtils.isNotEmpty(newItemBill['工程数量'])
                );


            // 如果是拆分的新行，则合并到上一行
            if (ifSplitRow) {
                let prevItemBill = itemBills[itemBills.length - 1];
                for (let k in prevItemBill) {
                    if (prevItemBill.hasOwnProperty(k)) {
                        let s1 = ObjectUtils.isEmpty(prevItemBill[k]) ? "" : prevItemBill[k];
                        let s2 = ObjectUtils.isEmpty(newItemBill[k]) ? "" : newItemBill[k];
                        prevItemBill[k] = s1.concat(s2);
                    }
                }
            }

            // 如果不是拆分的新行，并且不是空行，加入到结果结果集中
            if (!ifSplitRow && ifNotEmptyRow) {
                itemBills.push(newItemBill);
            }

            n++;
        }


        return itemBills;
    }

    _getTitleMap(rows, titleRows) {
        // 表格列标题获取
        let titleMap = new Map();
        for (let i = titleRows[0]; i <= titleRows[1]; i++) {
            const r = rows[i];
            for (let [key, val] of r) {
                if (ObjectUtils.isNotEmpty(val)) {
                    val = val.replaceAll(/\s/g, "");
                    titleMap.set(key, val);
                }
            }
        }
        return titleMap;
    }

    _readReal(rows, sheetName, headerNumber, titleRows, amountTo) {
        let result = [];
        //获取数据
        //const rows = ExcelUtil.readSheetContentByWorkBook(workbook, sheetName);
        if(ObjectUtils.isEmpty(rows)){
            return result;
        }

        // 表格列标题获取
        let titleMap = this._getTitleMap(rows, titleRows);

        let n = headerNumber;

        // 循环处理每行数据
        while (n < rows.length) {
            const r = rows[n];
            let newData = {};
            let ifNotEmptyRow = false;

            // 处理分部分项行数据，生成目标数据
            for(let [key, val] of titleMap){
                newData[val] = ObjectUtils.isEmpty(r.get(key)) ? null : ("" + r.get(key)).replaceAll(/^\s+|\s+$/g, "");
                ifNotEmptyRow = ifNotEmptyRow || ObjectUtils.isNotEmpty(newData[val]);
            }


            let xmmc = ObjectUtils.isNotEmpty(newData[amountTo.col]) ? newData[amountTo.col].replaceAll(/\s/g, "") : "";
            if(amountTo.contents.includes(xmmc)){
                n += 1;
                continue;
            }


            // 如果不是拆分的新行，并且不是空行，加入到结果结果集中
            if (ifNotEmptyRow) {
                result.push(newData);
            }

            n++;
        }

        return result;
    }

    _readZjcs(rows, sheetName) {
        return this._readReal(rows, sheetName, 3, [2, 2], {col: "项目名称", contents: ["合计", "小计"]})
    }

    _readQtxm(rows, sheetName) {
        return this._readReal(rows, sheetName, 3, [2, 2], {col: "项目名称", contents: ["合计", "小计"]})
    }

    _readZlj(rows, sheetName) {
        return this._readReal(rows, sheetName, 3, [2, 2], {col: "项目名称", contents: ["合计", "小计"]})
    }

    _readZgj(rows, sheetName) {
        return this._readReal(rows, sheetName, 3, [2, 2], {col: "暂估价名称", contents: ["合计", "小计"]})
    }

    _readZcbfwf(rows, sheetName) {
        return this._readReal(rows, sheetName, 3, [2, 2], {col: "项目名称", contents: ["合计", "小计"]})
    }

    _readJrg(rows, sheetName) {
        return this._readReal(rows, sheetName, 3, [2, 2], {col: "名称", contents: ["合计", "小计"]})
    }

    _readVisaClaims(rows, sheetName) {
        return this._readReal(rows, sheetName, 3, [2, 2], {col: "签证及索赔项目", contents: ["合计", "小计"]})
    }

    async _readUnit(oriUnit, single, sortNo, bizTree) {
        let unit = {};

        unit.sequenceNbr = oriUnit.id;
        unit.deStandardId = oriUnit.deStandardId || bizTree.deStandardId;
        unit.sortNo = sortNo;
        unit.upName = oriUnit.name;
        unit.biddingType = single.biddingType;
        unit.constructMajorType = oriUnit.constructMajorType;
        unit.secondInstallationProjectName = oriUnit.secondInstallationProjectName;
        unit.spId = single.sequenceNbr;
        unit.constructId = single.constructId;
        unit.mainDeLibrary = oriUnit.libraryCode;

        if (ObjectUtils.isEmpty(oriUnit.children)) {
            return unit;
        }

        // 获取单位工程xlsx路径
        let excelPath = oriUnit.children[0].filePath;

        let excelVer = excelPath.toLowerCase().endsWith(".xlsx") ? ExcelUtil.VER_2007 : ExcelUtil.VER_2003;

        let options = {version: excelVer};

        const wb = await ExcelUtil.readToWorkBook(excelPath, options)

        for (let i = 0; i < oriUnit.children.length; i++) {
            const sheetParam = oriUnit.children[i];

            const sheetName = sheetParam.name;

            const rows = ExcelUtil.readSheetContentByWorkBook(wb, sheetName, options)

            if (sheetName.includes(ExcelSheetList.SHEET5)) {

            } else if (sheetName.includes(ExcelSheetList.SHEET6_1) && sheetName.includes(ExcelSheetList.SHEET6_2)) {
                let itemBills = this._readFbFx(rows, sheetName, "分部分项工程量清单与计价表");

                unit[ExcelSheetList.SHEET6] = itemBills;
            } else if (sheetName.includes(ExcelSheetList.SHEET7_1) && sheetName.includes(ExcelSheetList.SHEET7_2)) {
                let djcs = this._readFbFx(rows, sheetName);

                unit[ExcelSheetList.SHEET7] = djcs;
            } else if (sheetName.includes(ExcelSheetList.SHEET8_1) && sheetName.includes(ExcelSheetList.SHEET8_2)) {
                let zjcs = this._readZjcs(rows, sheetName);

                unit[ExcelSheetList.SHEET8] = zjcs;
            } else if (sheetName.includes(ExcelSheetList.SHEET9)) {
                let qtxm = this._readQtxm(rows, sheetName);
                unit[ExcelSheetList.SHEET9] = qtxm;
            } else if (sheetName.includes(ExcelSheetList.SHEET10)) {
                let zlj = this._readZlj(rows, sheetName);
                unit[ExcelSheetList.SHEET10] = zlj;
            } else if (sheetName.includes(ExcelSheetList.SHEET11)) {
                let zgj = this._readZgj(rows, sheetName);
                unit[ExcelSheetList.SHEET11] = zgj;
            } else if (sheetName.includes(ExcelSheetList.SHEET12)) {
                let zcbfwf = this._readZcbfwf(rows, sheetName);
                unit[ExcelSheetList.SHEET12] = zcbfwf;
            } else if (sheetName.includes(ExcelSheetList.SHEET13)) {
                let jrg = this._readJrg(rows, sheetName);
                unit[ExcelSheetList.SHEET13] = jrg;
            }else if(sheetName.includes(ExcelSheetList.VISACLAIMS)){
                unit[ExcelSheetList.VISACLAIMS] = this._readVisaClaims(rows, sheetName);
            }
        }

        return unit;
    }

    async _readSingle(oriSingle, sortNo, constructId, biddingType,bizTree) {
        let single = {};
        single.sequenceNbr = oriSingle.id;
        single.projectName = oriSingle.name;
        single.constructId = constructId;
        single.sortNo = sortNo;
        single.biddingType = biddingType;
        single.units = [];
        single.subSingles = [];

        // 读取单位工程
        for (let i = 0; i < oriSingle.children.length; i++) {
            let child = oriSingle.children[i];
            if(child.levelType == this.unitLevelType){
                let unit = await this._readUnit(child, single, i + 1, bizTree);
                single.units.push(unit);
            }else if(child.levelType == this.singleLevelType){
                let subSingle = await this._readSingle(child, i + 1, constructId, biddingType,bizTree);
                single.subSingles.push(subSingle);
            }

        }

        return single;
    }

    async _readSingleOld(oriSingle, sortNo, constructId, biddingType) {
        let single = {};
        single.sequenceNbr = oriSingle.id;
        single.projectName = oriSingle.name;
        single.constructId = constructId;
        single.sortNo = sortNo;
        single.biddingType = biddingType;
        single.units = [];

        // 读取单位工程
        for (let i = 0; i < oriSingle.children.length; i++) {
            let unit = await this._readUnit(oriSingle.children[i], single, i + 1);

            single.units.push(unit);
        }

        return single;
    }

    _findConstructExcelPath(path){
        let maxLoop = 20;
        let constructExcelPath = null;
        for(let i = 0; i < maxLoop; i++){
            let subs = fs.readdirSync(path, {withFileTypes: true}) ;
            if(ObjectUtils.isNotEmpty(subs)){
                if(subs.length == 1){
                    if(subs[0].isDirectory()){
                        path = path + "/" + subs[0].name;
                    }else{
                        break;
                    }
                }else{
                    let excels = subs.filter(f => (f.name.endsWith(".xlsx") || f.name.endsWith(".xls")) && !f.name.startsWith("~$"));
                    let subDirs = subs.filter(f => f.isDirectory());
                    if(excels.length > 0 && subDirs.length > 0){
                        constructExcelPath = path + "/" + excels[0].name;
                    }
                    break;
                }
            }else{
                break;
            }
        }
        return constructExcelPath;
    }

    _readBzsm(wb){
        let rows = ExcelUtil.readSheetContentByWorkBook(wb, ExcelSheetList.SHEET1);
        let result = "";

        for(let i = 0; i < rows.length; ){
            let r = rows[i];

            let value = r.get(1);

            if(ObjectUtils.isNotEmpty(value) && value.startsWith("项目名称")){
                let r2 = rows[i+1];
                if (ObjectUtils.isNotEmpty(r2.get(1))) {
                    result += r2.get(1);
                }
                i += 1;
            }

            i++;
        }

        return result;
    }

    _readMapData(contents, keySet, destData){
        for(let i = 0; i < contents.length; i++){
            let row = contents[i];
            let keyDest = null;
            for(let [key, val] of row){
                let ori = val ? "" + val : "";
                let s = StringUtils.deleteBlank(ori);
                s = s.replaceAll(/:|：/g, "")
                if(ObjectUtils.isEmpty(s)){
                    continue;
                }
                if(keySet.has(s)){
                    if(keyDest == null){
                        keyDest = s;
                    }else{
                        destData[keyDest] = destData[keyDest] || null;
                        keyDest = s;
                    }
                }else if(keyDest != null){
                    destData[keyDest] = ori;
                    keyDest = null;
                }
            }
            if(keyDest != null){
                destData[keyDest] = destData[keyDest] || null;
            }
        }
    }

    _readFm(wb, keySet, destData){
        let fmContent = ExcelUtil.readSheetContentByWorkBook(wb, ExcelSheetList.FM);
        this._readMapData(fmContent, keySet, destData);
    }

    _readFy(wb, keySet, destData){
        let fmContent = ExcelUtil.readSheetContentByWorkBook(wb, ExcelSheetList.FY);
        this._readMapData(fmContent, keySet, destData);
    }

    async _setDeStandard(obj, deStandardId){
        //设置定额标准发布年份
        let deStandard = await this.service.baseListDeStandardService.quotaStandardById(deStandardId);
        obj.deStandardReleaseYear = deStandard.releaseYear;
        if(obj.deStandardReleaseYear == ConstantUtil.DE_STANDARD_12){
            obj.rgfInMeasureAndRPriceInMechanicalAction = true;
        }
    }

    async _readConstructProject(bizTree, projectNode, data){
        let keySet = new Set();
        keySet.add("招标人");
        keySet.add("造价咨询人");
        keySet.add("编制时间");
        keySet.add("法定代表人或委托代理人");
        keySet.add("编制人");
        keySet.add("复核人");

        // 设置工程项目属性
        data.sequenceNbr = bizTree.sequenceNbr;
        data.importUrl = bizTree.importUrl;
        data.constructName = bizTree.constructName;
        data.constructCode = bizTree.constructCode;
        data.qdStandardId = bizTree.qdStandardId;
        data.deStandardId = bizTree.deStandardId;
        data.biddingType = bizTree.biddingType;
        data.ssProvince = 130000;
        data.ssProvinceName = "河北省";
        data.ssCity = 130100;
        data.ssCityName = "石家庄市";
        data.lastOpenDate = DateUtils.now("yyyy-MM-DD HH:mm:ss");
        data.projectStatus = "1";

        //设置定额标准发布年份
        await this._setDeStandard(data, bizTree.deStandardId);
        // let deStandard = await this.service.baseListDeStandardService.quotaStandardById(bizTree.deStandardId);
        // data.deStandardReleaseYear = deStandard.releaseYear;
        // if(data.deStandardReleaseYear == ConstantUtil.DE_STANDARD_12){
        //     data.rgfInMeasureAndRPriceInMechanicalAction = true;
        // }

        // 工程项目级别excel处理
        let constructExcelPath = this._findConstructExcelPath(projectNode.filePath);
        if(ObjectUtils.isNotEmpty(constructExcelPath)){
            let wb = await ExcelUtil.readToWorkBook(constructExcelPath);
            this._readFm(wb, keySet, data);
            this._readFy(wb, keySet, data);
            data["编制说明"] = this._readBzsm(wb);
        }
    }

}


AnalyzingExcelService.toString = () => '[class AnalyzingExcelService]';
module.exports = AnalyzingExcelService;

const bizTree = {
    "sequenceNbr": "1701418343996096514",
    "recUserCode": null,
    "recDate": null,
    "recStatus": null,
    "extend1": null,
    "extend2": null,
    "extend3": null,
    "description": null,
    "agencyCode": null,
    "productCode": null,
    "lastLoginTime": null,
    "recUserId": null,
    "ssProvince": null,
    "ssProvinceName": null,
    "ssCity": null,
    "ssCityName": null,
    "projectStatus": null,
    "createDate": null,
    "oflateOpenDate": null,
    "biddingType": 0,
    "coverUrl": null,
    "importUrl": "https://pricing-dev.oss-cn-hangzhou.aliyuncs.com/import/xmrdnuo2d3-%E6%9B%B9%E5%A6%83%E7%94%B8%E6%96%B0%E5%9F%8E%E5%9D%87%E5%92%8C%E4%BA%A7%E4%B8%9A%E5%9B%AD%E7%B2%BE%E7%AE%80%E7%89%88.zip",
    "xmlFactory": null,
    "reportUrl": null,
    "constructName": "曹妃甸新城均和产业园精简版",
    "constructCode": "001",
    "constructMajorType": null,
    "qdStandardId": "1657987328905158657",
    "deStandardId": "1657988044369526785",
    "libraryCode": null,
    "secondInstallationProjectName": null,
    "projectOverview": null,
    "total": null,
    "gfee": null,
    "safeFee": null,
    "sbfsj": null,
    "fddbr": null,
    "constructionUnit": null,
    "taxMode": null,
    "singleProjectList": null,
    "unitProjectDTO": null,
    "unitProjectDTOList": null,
    "constructBasicEngineeringInfoList": null,
    "constructConfig": null,
    "organizationInstructionsConstruct": null,
    "fileLevelTreeNodes": [
        {
            "level": 0,
            "sortNo": null,
            "biddingType": null,
            "libraryCode": null,
            "sheetName": null,
            "fileType": 1,
            "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版",
            "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版",
            "code": 0,
            "errorMsg": null,
            "directory": true,
            "isNew": true,
            "id": "1701418343996096514",
            "parentId": null,
            "name": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版",
            "levelType": 1,
            "children": [
                {
                    "level": 2,
                    "sortNo": null,
                    "biddingType": null,
                    "libraryCode": null,
                    "sheetName": null,
                    "fileType": 1,
                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路",
                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路",
                    "code": 0,
                    "errorMsg": null,
                    "directory": true,
                    "isNew": true,
                    "id": "1701418832888365058",
                    "parentId": "1701418343996096514",
                    "name": "一号路",
                    "levelType": 2,
                    "children": [
                        {
                            "level": 3,
                            "sortNo": null,
                            "biddingType": null,
                            "libraryCode": null,
                            "sheetName": null,
                            "fileType": 1,
                            "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程",
                            "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程",
                            "code": 0,
                            "errorMsg": null,
                            "directory": true,
                            "isNew": true,
                            "id": "1701418832888365059",
                            "parentId": "1701418832888365058",
                            "name": "道路工程",
                            "levelType": 3,
                            "constructMajorType": "建筑工程",
                            "children": [
                                {
                                    "id": "1701418832888365060",
                                    "name": "表1-5 单位工程费汇总表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-5 单位工程费汇总表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365059",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365061",
                                    "name": "表1-6 分部分项工程量清单与计价表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-6 分部分项工程量清单与计价表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365059",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365062",
                                    "name": "表1-7 单价措施项目工程量清单与计价表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-7 单价措施项目工程量清单与计价表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365059",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365063",
                                    "name": "表1-8 总价措施项目清单与计价表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-8 总价措施项目清单与计价表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365059",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365064",
                                    "name": "表1-9 其他项目清单与计价表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-9 其他项目清单与计价表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365059",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365065",
                                    "name": "表1-10 暂列金额明细表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-10 暂列金额明细表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365059",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },


                                {
                                    "id": "1701418832888365065",
                                    "name": "表1-11 暂估价表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-11 暂估价表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365059",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                }, {
                                    "id": "1701418832888365065",
                                    "name": "表1-12 总承包服务费计价表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-12 总承包服务费计价表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365059",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                }, {
                                    "id": "1701418832888365065",
                                    "name": "表1-13 计日工表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-13 计日工表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365059",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },


                                {
                                    "id": "1701418832888365066",
                                    "name": "表1-15 主要材料、设备明细表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-15 主要材料、设备明细表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365059",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365067",
                                    "name": "材料、机械、设备增值税计算表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "材料、机械、设备增值税计算表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365059",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365068",
                                    "name": "增值税进项税额计算汇总表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "增值税进项税额计算汇总表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365059",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/一号路/道路工程/道路工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                }
                            ],
                            "unitAfterSet": false
                        }
                    ],
                    "unitAfterSet": false
                },
                {
                    "level": 2,
                    "sortNo": null,
                    "biddingType": null,
                    "libraryCode": null,
                    "sheetName": null,
                    "fileType": 1,
                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路",
                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路",
                    "code": 0,
                    "errorMsg": null,
                    "directory": true,
                    "isNew": true,
                    "id": "1701418832888365069",
                    "parentId": "1701418343996096514",
                    "name": "二号路",
                    "levelType": 2,
                    "children": [
                        {
                            "level": 3,
                            "sortNo": null,
                            "biddingType": null,
                            "libraryCode": null,
                            "sheetName": null,
                            "fileType": 1,
                            "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程",
                            "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程",
                            "code": 0,
                            "errorMsg": null,
                            "directory": true,
                            "isNew": true,
                            "id": "1701418832888365070",
                            "parentId": "1701418832888365069",
                            "name": "中水工程",
                            "levelType": 3,
                            "constructMajorType": "建筑工程",
                            "children": [
                                {
                                    "id": "1701418832888365071",
                                    "name": "表1-5 单位工程费汇总表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-5 单位工程费汇总表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365070",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365072",
                                    "name": "表1-6 分部分项工程量清单与计价表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-6 分部分项工程量清单与计价表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365070",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365073",
                                    "name": "表1-7 单价措施项目工程量清单与计价表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-7 单价措施项目工程量清单与计价表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365070",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365074",
                                    "name": "表1-8 总价措施项目清单与计价表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-8 总价措施项目清单与计价表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365070",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365075",
                                    "name": "表1-9 其他项目清单与计价表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-9 其他项目清单与计价表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365070",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365076",
                                    "name": "表1-10 暂列金额明细表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-10 暂列金额明细表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365070",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365077",
                                    "name": "表1-15 主要材料、设备明细表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-15 主要材料、设备明细表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365070",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365078",
                                    "name": "材料、机械、设备增值税计算表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "材料、机械、设备增值税计算表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365070",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365079",
                                    "name": "增值税进项税额计算汇总表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "增值税进项税额计算汇总表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365070",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/中水工程/中水工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                }
                            ],
                            "unitAfterSet": false
                        },
                        {
                            "level": 3,
                            "sortNo": null,
                            "biddingType": null,
                            "libraryCode": null,
                            "sheetName": null,
                            "fileType": 1,
                            "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程",
                            "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程",
                            "code": 0,
                            "errorMsg": null,
                            "directory": true,
                            "isNew": true,
                            "id": "1701418832888365080",
                            "parentId": "1701418832888365069",
                            "name": "园林绿化工程",
                            "levelType": 3,
                            "constructMajorType": "市政工程",
                            "children": [
                                {
                                    "id": "1701418832888365081",
                                    "name": "表1-5 单位工程费汇总表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-5 单位工程费汇总表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365080",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365082",
                                    "name": "表1-6 分部分项工程量清单与计价表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-6 分部分项工程量清单与计价表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365080",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365083",
                                    "name": "表1-7 单价措施项目工程量清单与计价表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-7 单价措施项目工程量清单与计价表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365080",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365084",
                                    "name": "表1-8 总价措施项目清单与计价表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-8 总价措施项目清单与计价表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365080",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365085",
                                    "name": "表1-9 其他项目清单与计价表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-9 其他项目清单与计价表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365080",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365086",
                                    "name": "表1-10 暂列金额明细表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-10 暂列金额明细表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365080",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365087",
                                    "name": "表1-15 主要材料、设备明细表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "表1-15 主要材料、设备明细表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365080",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365088",
                                    "name": "材料、机械、设备增值税计算表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "材料、机械、设备增值税计算表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365080",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                },
                                {
                                    "id": "1701418832888365089",
                                    "name": "增值税进项税额计算汇总表",
                                    "level": null,
                                    "levelType": 5,
                                    "sortNo": null,
                                    "biddingType": null,
                                    "constructMajorType": null,
                                    "libraryCode": null,
                                    "secondInstallationProjectName": null,
                                    "sheetName": "增值税进项税额计算汇总表",
                                    "fileType": 2,
                                    "parentId": "1701418832888365080",
                                    "filePath": "D:/tmp/曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程.xlsx",
                                    "relativePath": "曹妃甸新城均和产业园一号路、二号路工程-清单-精简版/曹妃甸新城均和产业园一号路、二号路工程-清单审定/二号路/园林绿化工程/园林绿化工程",
                                    "children": [],
                                    "code": 0,
                                    "errorMsg": null,
                                    "directory": false
                                }
                            ],
                            "unitAfterSet": false
                        }
                    ],
                    "unitAfterSet": false
                }
            ],
            "unitAfterSet": false
        }
    ],
    "isZip": true
};

// new AnalyzingExcelService().analysis(bizTree)
