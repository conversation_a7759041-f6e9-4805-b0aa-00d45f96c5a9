const {Controller} = require("../../../core");
const {ResponseData} = require("../../../common/ResponseData");
const JieSuanConstructCostMathService = require("../../../packages/jieSuanProject/service/jieSuanConstructCostMathService");
const JieSuanZsProjectCgCostMathService = require("../service/jieSuanZsProjectCgCostMathService");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const JieSuanAZCostMathService = require("../service/jieSuanAZCostMathService");

/**
 *
 */
class JieSuanConstructCostMathController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);

        this.jieSuanZsProjectCgCostMathService =
            ObjectUtils.isNotEmpty(this.service.jieSuanZsProjectCgCostMathService)?this.service.jieSuanZsProjectCgCostMathService :new JieSuanZsProjectCgCostMathService(ctx);

        this.jieSuanConstructCostMathService =
            ObjectUtils.isNotEmpty(this.service.jieSuanConstructCostMathService)?this.service.jieSuanConstructCostMathService :new JieSuanConstructCostMathService(ctx);

        this.jieSuanAZCostMathService =
            ObjectUtils.isNotEmpty(this.service.jieSuanAZCostMathService)?this.service.jieSuanAZCostMathService :new JieSuanAZCostMathService(ctx);
    }


    /**
     * 安文费---一键记取总价措施
     */
    async awfCostMath (arg) {
        const result = await this.jieSuanConstructCostMathService.awfCostMath(arg);
        return ResponseData.success(true);
    }

    /**
     * 安文费-----一键记取总价措施-获取总价措施费用分类列表
     */
    async zjcsClassList (arg) {
        const result = await this.jieSuanConstructCostMathService.zjcsClassList(arg);
        return ResponseData.success(result);
    }

    /**
     * 安文费-----高台施工增加高度
     */
    async gtfResource () {
        const result = await this.jieSuanConstructCostMathService.gtfResource();
        return ResponseData.success(result);
    }



    /**
     * 安文费---明细
     */
    async awfDetails (arg) {
        const result =  this.jieSuanConstructCostMathService.awfDetails(arg);
        return ResponseData.success(result);
    }


    /**
     * 安文费总价措施----安文费总价措施参数缓存
     */
    async zjcsCostMathCache (arg) {
        const result = await this.jieSuanConstructCostMathService.zjcsCostMathCache(arg);
        return ResponseData.success(result);
    }

//--------------------------------------------------功能分割线------------------------------------------------------------

    /**
     * 装饰垂直运输----装饰垂运层高下拉框
     */
    async storeyList (args) {
        const result = await this.jieSuanConstructCostMathService.storeyList(args);
        return ResponseData.success(result);
    }

    /**
     * 装饰垂直运输----获取标准的垂运清单
     */
    async getCyQd(){
        const result = await this.jieSuanConstructCostMathService.getCyQd();
        return ResponseData.success(result);

    }

    /**
     * 装饰垂直运输----获取分部分项 单价措施下指定定额册的定额
     */
    async conditionDeList (arg) {
        const result = await this.jieSuanConstructCostMathService.conditionDeList(arg);
        return ResponseData.success(result);
    }


    /**
     * 装饰垂直运输----记取位置清单选择列表
     */
    async recordPosition (arg) {
        const result = await this.jieSuanConstructCostMathService.recordPosition(arg);
        return ResponseData.success(result);
    }

    /**
     * 装饰垂直运输/超高----判断清单下是否记取定额
     */
    async qdExistDe (arg) {
        const result = await this.jieSuanConstructCostMathService.qdExistDe(arg);
        return ResponseData.success(result);
    }



    /**
     * 装饰垂直运输----装饰垂运记取
     */
    async czysCostMath (arg) {
        const result = await this.jieSuanConstructCostMathService.czysCostMath(arg);
        return ResponseData.success(result);
    }


    /**
     * 装饰垂直运输----装饰垂运记取参数缓存
     */
    async cyCostMathCache (arg) {
        const result = await this.jieSuanConstructCostMathService.cyCostMathCache(arg);
        return ResponseData.success(result);
    }

//--------------------------------------------------功能分割线------------------------------------------------------------


    /**
     * 装饰工程超高----装饰超高层高下拉框
     */
    async cgStoreyList (args) {
        const result = await this.jieSuanZsProjectCgCostMathService.cgStoreyList(args);
        return ResponseData.success(result);
    }





    /**
     * 装饰工程超高----获取标准的超高清单
     */
    async getCgQd(){
        const result = await this.jieSuanZsProjectCgCostMathService.getCgQd();
        return ResponseData.success(result);

    }



    /**
     * 装饰工程超高----记取位置清单选择列表
     */
    async recordPositionCgList (arg) {
        const result = await this.jieSuanZsProjectCgCostMathService.recordPositionCgList(arg);
        return ResponseData.success(result);
    }


    /**
     * 装饰工程超高----装饰超高记取
     */
    async cgCostMath (arg) {
        const result = await this.jieSuanZsProjectCgCostMathService.cgCostMath(arg);
        return ResponseData.success(result);
    }

    /**
     * 装饰工程超高----装饰超高记取缓存
     */
    async cgCostMathCache (arg) {
        const result = await this.jieSuanZsProjectCgCostMathService.cgCostMathCache(arg);
        return ResponseData.success(result);
    }



//--------------------------------------------------功能分割线------------------------------------------------------------


    /**
     * 安装费用----费用记取列表
     */
    async azCostMathList (arg) {
        const result = await this.jieSuanAZCostMathService.azCostMathList(arg);
        return ResponseData.success(result);
    }

    /**
     * 安装费用----基数定额列表查询
     */
    async baseDeList (arg) {
        const result = await this.jieSuanAZCostMathService.baseDeList(arg);
        return ResponseData.success(result);
    }

    /**
     * 安装费用----清单列表查询
     */
    async qdList (arg) {
        const result = await  this.jieSuanAZCostMathService.qdList(arg);
        return ResponseData.success(result);
    }

    /**
     * 安装费用----记取接口
     */
    async azCostMath (arg) {
        const result = await  this.jieSuanAZCostMathService.azCostMath(arg);
        return ResponseData.success(result);
    }



    /**
     * 安装费用----查询清单列表对应的默认清单的值
     */
    async getDefaultQdValue (arg) {
        const result = await  this.jieSuanAZCostMathService.getDefaultQdValue(arg);
        return ResponseData.success(result);
    }



    /**
     * 安装费用----安装记取缓存
     */
    async azCostMathCache (arg) {
        const result = await  this.jieSuanAZCostMathService.azCostMathCache(arg);
        return ResponseData.success(result);
    }

    /**
     * 查询安装的基数定额对应的章节列表
     */
    async queryBaseDeChapter(args) {
        return ResponseData.success(await  this.jieSuanAZCostMathService.queryBaseDeChapter(args));
    }

//---------------------手动添加定额  费用记取--------------------

    /**
     * 手动费用记取，添加定额用
     * @param arg
     * @return {Promise<ResponseData>}
     */
    async manuallyAddCostMath (arg) {
        const result = await this.jieSuanConstructCostMathService.manuallyAddCostMath(arg);
        return ResponseData.success(result);
    }

}

JieSuanConstructCostMathController.toString = () => '[class JieSuanConstructCostMathController]';
module.exports = JieSuanConstructCostMathController;