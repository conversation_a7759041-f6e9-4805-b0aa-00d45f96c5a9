const EE = require('../../../core/ee');
const _ = require("lodash");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const {traverseRecursive} = require("../util");
const BranchProjectLevelConstant = require("../../enum/BranchProjectLevelConstant");
const RcjDeleteStrategy = require("../../rcj_handle/remove/removeRcjStrategy");
const OptionMenuHandler = require("../optionMenuHandler");
const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const BranchProjectDisplayConstant = require("../../enum/BranchProjectDisplayConstant");
const { NumberUtil } = require("../../utils/NumberUtil");


/**
 * 如理插分部删除
 */
class FbMoveToHandler{
    constructor(ctx) {
        this.ctx = ctx;
        this.pointLine = null;
        this.allData = ctx.allData;
    }
    prepare({selectId,targetId,parentId}) {
        this.pointLine = this.allData.getNodeById(selectId);
        this.targetNode = this.allData.getNodeById(targetId);
        this.parent = this.allData.getNodeById(parentId);
    }
    move({selectId,targetId,parentId}) {
        let self = this;
        this.prepare({selectId,targetId,parentId});
         //判断当前节点下挂的是不是分部
        

        //如果源分部和目标分部是同级别 
    if(this.pointLine.parentId==this.targetNode.parentId&&this.targetNode.sequenceNbr!==this.parent.sequenceNbr){
        //把sourceNode 放到targetNode 的后面
        let children = this.parent.children;
        // 把sourceNode 放到targetNode 的后面
        // 将sourceNode移动到targetNode后面
        let sourceIndex = this.pointLine.index;
        let targetIndex = this.targetNode.index;
        // 先移除sourceNode
        children.splice(sourceIndex, 1);
        // 在targetNode后面插入sourceNode
        children.splice(targetIndex + 1, 0, this.pointLine);
        //重新排序
        this.parent.refreshIndex();
        return [];
      }else{
        this._fillArrow(this.pointLine.parent);
        this.allData.removeNode(this.pointLine.sequenceNbr);
        let ids = [this.pointLine.parentId,this.parent.sequenceNbr];
        //如果不是同级别 则是在调整分部层级
        //如果目标节点 和 parent是同一个节点 则直接插入到parent的首位
        if(this.parent.kind==BranchProjectLevelConstant.top){
            this.pointLine.kind=BranchProjectLevelConstant.fb;
        }else{
            this.pointLine.kind=BranchProjectLevelConstant.zfb;
        }
        if(this.targetNode==this.parent){
            this.allData.addNodeAt(this.pointLine, this.parent, 0);
        }else{

            //如果目标节点 和 parent不是同一个节点 则插入到parent的后面
            this.allData.addNodeAt(this.pointLine, this.parent, this.targetNode.index+1);
        }
        this._fillArrow(this.parent);
        this.parent.refreshIndex();
        return ids;
      }
    }
    _fillArrow(newNode){
        newNode.displaySign = ObjectUtils.isEmpty(newNode.children) ? BranchProjectDisplayConstant.noSign : BranchProjectDisplayConstant.open;
        //处理父节点箭头
        if(this.parent){
            this.parent.displaySign = BranchProjectDisplayConstant.open;
        }
    }
    clearExtendedData(){

    }
}
module.exports = {
    FbMoveToHandler
}
