<template>
  <!-- <div class="head">
    <a-button type="text" @click="insertHandle(null)">插入</a-button>
    <a-button type="text" @click="resetLockHandle" v-if="constructLevel">
      {{ lockStatus ? '解锁' : '锁定' }}信息
    </a-button>
    <a-button type="text" @click="deleteHandle(null)">删除</a-button>
  </div> -->
  <div class="table-content table-contentgk">
    <vxe-table
      align="center"
      height="98%"
      ref="basicInfoTable"
      :loading="loading"
      style="width: 820px"
      :column-config="{ resizable: true }"
      :row-config="{
        isHover: true,
        isCurrent: true,
        keyField: 'sequenceNbr',
        useKey: true,
      }"
      v-if="tableData?.length !== 0"
      :data="tableData"
      :tree-config="{
        transform: projectStore.currentTreeInfo?.type === 3 ? false : true,
        expandAll: true,
        rowField: 'sequenceNbr',
        parentField: 'parentId',
        line: projectStore.currentTreeInfo?.type === 3 ? false : true,
        reserve: true,
        iconOpen: 'vxe-icon-square-minus',
        iconClose: 'vxe-icon-square-plus',
      }"
      :scroll-y="{ enabled: false }"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
        showIcon: false,
        showStatus: false,
      }"
      @current-change="currentChangeEvent"
      @cell-click="
        cellData => {
          useCellClickEvent(cellData);
        }
      "
      :menu-config="menuConfig"
      @keydown="handleKeyDownEvent"
      @menu-click="contextMenuClickEvent"
      class="table-scrollbar table-edit-common"
      :cell-class-name="
        ({ $columnIndex, row, column }) => {
          const selectName =
            selectedClassName({ $columnIndex, row, column }) +
            `gkVirtual-pdLeft${row.parentId ? '2' : '1'}`; //`Virtual-gsPdLeft${row.parentId?'2':''}`
          if (
            column.field === 'name' &&
            ['基本信息', '招标信息', '投标信息'].includes(row.name) &&
            row.addFlag === 0
          ) {
            return 'title-bold ' + selectName;
          }

          if (column.field === 'name' && row.type === 'title') {
            return 'title-bold ' + selectName;
          }
          if (
            column.field === 'name' &&
            ['招标人(发包人)', '招标人(发包人)法人或其授权人'].includes(
              row.name
            )
          ) {
            return 'color-red ' + selectName;
          }
          return selectName;
        }
      "
      :row-class-name="
        ({ row }) => {
          if (row.lockFlag == 1) {
            return 'row-lock-color';
          }
        }
      "
    >
      <vxe-column
        field="dispNo"
        :width="columnWidth(60)"
        title="序号"
      ></vxe-column>
      <vxe-column
        field="name"
        title="名称"
        align="left"
        tree-node
        headerAlign="center"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-if="row.addFlag && !row.lockFlag"
            placeholder="请输入名称"
            v-model="row.name"
            type="text"
            name="name"
            @blur="inputFinish(row, $event, 'name')"
          ></vxe-input>
          <span v-else>{{ row.name }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="remark"
        title="内容"
        align="left"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row }">
          <span>{{
            row.name === '工程规模'
              ? decimalFormat(row.remark, 'COST_ANALYSIS_JZGM_PATH')
              : row.remark
          }}</span>
        </template>
        <template #edit="{ row }">
          <a-input
            v-model:value="row.remark"
            v-if="row.name === '工程规模'"
            placeholder="请输入内容"
            type="number"
            :min="0"
            @blur="saveCustomInput(row.remark, row, 'remark', $rowIndex)"
          ></a-input>
          <vxeTableEditSelect
            :clearable="true"
            :filedValue="row.remark"
            :list="row.jsonStr"
            :name="row.name"
            :isNotLimit="row.name === '工程规模单位'"
            v-else-if="
              row.name !== '基本信息' &&
              row.name !== '招标信息' &&
              row.name !== '投标信息' &&
              !row.name.includes('时间') &&
              !row.name.includes('日期') &&
              ifShowSelect(row) &&
              !(
                row.name.trim() === '工程名称' &&
                projectStore.currentTreeInfo?.type === 1
              )
            "
            @update:filedValue="
              newValue => {
                saveCustomInput(newValue, row, 'remark', $rowIndex);
              }
            "
          ></vxeTableEditSelect>
          <!-- <vxe-select v-else-if="row.name === '编制依据'" v-model="row.remark"  :transfer="true" @change="saveCustomInput(row.remark, row, 'remark',  $rowIndex)">
            <vxe-option v-for="(item,index) in bzyjArr" :key="index" :value="item.label" :label="item.label"></vxe-option>
          </vxe-select> -->
          <vxe-input
            v-else-if="row.name.includes('时间') || row.name.includes('日期')"
            v-model="row.remark"
            placeholder="日期选择"
            type="datetime"
            :value-format="YYYY - MM - DD"
            @change="saveCustomInput(row.remark, row, 'remark', $rowIndex)"
          ></vxe-input>
          <span v-else>{{ row.remark }}</span>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script setup>
import {
  onMounted,
  onUpdated,
  onActivated,
  ref,
  watch,
  toRaw,
  getCurrentInstance,
  reactive,
  nextTick,
} from 'vue';
import xeUtils from 'xe-utils';
import { message, Modal } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import csProject from '@gongLiaoJi/api/csProject';
import operateList from '../operate';
import { insetBus } from '@gongLiaoJi/hooks/insetBus';

import { useCellClick } from '@gongLiaoJi/hooks/useCellClick';
import { ipcApiRoute } from '@gongLiaoJi/api/main';
import { row } from 'mathjs';
import { recordProjectData } from '@gongLiaoJi/hooks/recordProjectOperat.js';
import { useDecimalPoint } from '@gongLiaoJi/hooks/useDecimalPoint.js';
import { columnWidth } from '@gongLiaoJi/hooks/useSystemConfig.js';
const { decimalFormat } = useDecimalPoint();

let {
  // gljCheckTab,
  updateGljSelrowId,
} = recordProjectData();
const globalProperties =
  getCurrentInstance().appContext.config.globalProperties; // 获取全局挂载
const $ipc = globalProperties.$ipc;
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const props = defineProps({
  activeKey: {
    type: Number,
    default: 1,
  },
});
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
let lockData = operateList.value.find(item => item.name === 'lock');
let basicInfoTable = ref();
let loading = ref(false);
let constructLevel = ref(true);
const activeKey = ref(1);
const upProjectName = ref('');
const bzyjArr = ref([]);
const jzTypeObj = ref([]);
const inputMaxLength = ref(50);
const projectStore = projectDetailStore();

let tableData = ref([]);
// 激活表格
const focusTable = () => {
  basicInfoTable.value?.focus();
};
bus.on('basicEngineeringInformation', data => {
  focusTable();
});
const createRequestParams = () => {};

const flashFun = () => {
  console.log('*****************基本信息');
  if (
    (projectStore.asideMenuCurrentInfo?.key === '11' &&
      props.activeKey === 1 &&
      projectStore.currentTreeInfo?.type !== 2 &&
      projectStore.tabSelectName === '项目概况') ||
    projectStore.tabSelectName === '工程概况'
  ) {
    cacheAll.newAddRowSeq = null;
    cacheAll.currentRecord = null;
    getBasicInfo();
    getBzyjList();
  }
};
// 获取编制依据数据
const getBzyjList = () => {
  const postData = {
    provenceCode: 130000,
    cityCode: 130100,
  };
  $ipc
    .invoke(ipcApiRoute.getDeLibraryByDirection, postData)
    .then(function (res) {
      if (res.status === 200) {
        let arr = [];
        let datas = res.result;
        for (let i in datas) {
          arr.push({
            value: datas[i].libraryCode,
            label: datas[i].libraryName,
          });
        }
        bzyjArr.value = arr;
      }
    });
};
const ifShowSelect = row => {
  return (
    (projectStore.currentTreeInfo?.type === 1 && !row.lockFlag) ||
    row.addFlag ||
    (projectStore.currentTreeInfo?.type === 3 &&
      !['单位工程名称', '工程专业', '编制依据'].includes(row.name))
  );
};
const saveCustomInput = (newValue, row, name, index) => {
  if (row.name === '工程规模') {
    if (
      newValue.match(/\-?\d+/g) &&
      newValue.match(/\-?\d+/g).some(num => num < 0)
    ) {
      message.error('请输入大于等于0的有效数值');
      row[name] = '';
      newValue = '';
    }
  }
  if (row.name === '项目名称' || row.name === '工程名称') {
    if (newValue === '') {
      message.error('名称不允许为空！');
      row.remark = upProjectName.value;
      return;
    } else {
      const asideTreeData = projectStore.asideTreeData;
      const sameNameIndex = asideTreeData.findIndex(
        item =>
          item.parentId === projectStore.currentTreeInfo.parentId &&
          item.name === newValue &&
          item.sequenceNbr !== projectStore.currentTreeInfo.sequenceNbr
      );
      if (sameNameIndex !== -1) {
        message.error('同级名称不可重复，请重新输入名称');
        return;
      } else {
        upProjectName.value = newValue;
      }
    }
  }
  if (
    row.name === '合同号' ||
    row.name === '项目编号' ||
    row.name === '编制人资格证书编号' ||
    row.name === '编制时间' ||
    row.name === '核对人资格证书编号' ||
    row.name === '核对（复核）时间' ||
    row.name === '技术负责人资格证书编号' ||
    row.name === '编制人资格证书编号' ||
    row.name === '编制时间' ||
    row.name === '核对人资格证书编号' ||
    row.name === '核对（复核）时间' ||
    row.name === '编制人资格证书编号' ||
    row.name === '编制时间' ||
    row.name === '核对人资格证书编号' ||
    row.name === '核对（复核）时间'
  ) {
    newValue = newValue.length > length ? newValue.slice(0, 20) : newValue;
  }
  row[name] = newValue;
  saveOrUpdateBasicInfo({ data: xeUtils.clone(row, true) }, false, 'edit');
  if (row.name === '工程类型') {
    let jzflArr = tableData.value.filter(a => a.name === '建筑分类');
    if (jzflArr.length !== 0) {
      jzflArr[0].remark = jzTypeObj.value[newValue].split(',')[0];
      jzflArr[0].jsonStr = jzTypeObj.value[newValue];
    }
    saveOrUpdateBasicInfo(
      { data: xeUtils.clone(jzflArr[0], true) },
      false,
      'edit'
    );
  }
};

watch(
  () => projectStore.asideMenuCurrentInfo,
  val => {
    if (projectStore.tabSelectName === '项目概况') {
      updateGljSelrowId(val.sequenceNbr, '项目概况', 'leftTwoTreeId');
    }
    if (projectStore.tabSelectName === '工程概况') {
      updateGljSelrowId(val.sequenceNbr, '工程概况', 'leftTwoTreeId');
    }
    if (val?.name === '基本工程信息') {
      flashFun();
    }
  }
);

watch(
  () => projectStore.currentTreeInfo,
  val => {
    // 项目下基本工程信息调用
    if (val?.type === 1) {
      flashFun();
    }
  }
);
watch(() => projectStore.isRefreshBaseInfo, flashFun);
// 获取项目概况的-建筑分类
const getJzType = () => {
  csProject.getJzType().then(res => {
    if (res.status != 200) {
      return message.error(res.message);
    }
    jzTypeObj.value = res.result;
    getBasicInfo();
  });
};
const getBasicInfo = () => {
  loading.value = true;
  let levelType = projectStore.currentTreeInfo?.type;
  let apiData = {
    levelType: projectStore.currentTreeInfo?.type,
    // code: activeKey.value,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    // singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: levelType === 3 ? projectStore.currentTreeInfo?.id : null,
    type: projectStore.asideMenuCurrentInfo?.key,
  };
  console.log('getBasicInfo', apiData);
  if (!apiData.levelType) {
    return;
  }
  tableData.value = [];
  csProject
    .getBasicInfo(apiData)
    .then(res => {
      console.log('getBasicInfo222222222', res);
      if (res.status === 200) {
        loading.value = false;
        let datas = res.result;
        tableData.value = datas;
        let rowArr = datas.filter(a => a.name === '项目名称');
        if (rowArr.length !== 0) {
          upProjectName.value = rowArr[0].remark;
        } else {
          rowArr = datas.filter(a => a.name === '工程名称');
          upProjectName.value = rowArr[0]?.remark;
        }
        let gclxArr = datas.filter(a => a.name === '工程类型');
        // console.info(33333333333,jzTypeObj.value['交通建筑'])
        if (gclxArr.length !== 0) {
          let arr = gclxArr[0].jsonStr.split(',');
          console.info(33333333333, arr[0]);
          if (gclxArr[0].remark == undefined) {
            gclxArr[0].remark = arr[0];
          }
          let jzflArr = datas.filter(a => a.name === '建筑分类');
          if (jzflArr.length !== 0) {
            if (jzflArr[0].remark == undefined) {
              jzflArr[0].remark =
                jzTypeObj.value[gclxArr[0].remark].split(',')[0];
            }
            jzflArr[0].jsonStr = jzTypeObj.value[gclxArr[0].remark];
          }
        }

        basicInfoTable.value?.loadData(tableData.value);
        nextTick(() => {
          resetCurrentRow();
          focusTable();
        });
      }
    })
    .catch(err => {
      console.log(err, 'err123');
    });
};

const changeDate = val => {
  console.info(val);
};
const findLastAddRow = tree => {
  const key = 'recDate';
  let levelType = projectStore.currentTreeInfo?.type;

  let destRow = tree[0];
  let lastTime = tree[0][key];

  for (let pos = 0; pos < tree.length; pos++) {
    let node = tree[pos];
    if (node[key] > lastTime) {
      lastTime = node[key];
      destRow = node;
    }

    if (levelType !== 3) {
      for (let i = 0; i < node.children.length; i++) {
        let subNode = node.children[i];
        if (subNode[key] > lastTime) {
          lastTime = subNode[key];
          destRow = subNode;
        }
      }
    }
  }

  return destRow;
};

const deleteRowForBasicInfo = sequenceNbr => {
  let levelType = projectStore.currentTreeInfo?.type;
  let apiData = {
    levelType: levelType,
    // code: activeKey.value,
    type: projectStore.asideMenuCurrentInfo?.key,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    // singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: levelType === 3 ? projectStore.currentTreeInfo?.id : null,
    sequenceNbr: sequenceNbr,
  };

  csProject.deleteBasicInfo(apiData).then(res => {
    if (res.status === 200) {
      getBasicInfo();
    }
  });
};

const saveOrUpdateBasicInfo = (param, isUpdate = false, operateType) => {
  let levelType = projectStore.currentTreeInfo?.type;
  let apiData = {
    levelType: levelType,
    type: projectStore.asideMenuCurrentInfo?.key,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: levelType === 3 ? projectStore.currentTreeInfo?.id : null,
    orgInstruction: param.data,
    operateType: operateType,
  };
  console.log('项目概况内容修改传参', apiData);
  csProject.saveOrUpdateBasicInfo(apiData).then(res => {
    if (res.status === 200) {
      if (param.data.name === '项目名称') {
        projectStore.SET_PROJECT_NAME(param.data.remark);
        bus.emit('projectNameChange', param.data.remark);
      }
      if (param.data.name === '工程名称') {
        bus.emit('projectNameChange', param.data.remark);
      }
      if (operateType !== 'edit' || isUpdate) getBasicInfo();
    }
  });
};
let lockStatus = ref(0);
watch(
  () => lockStatus.value,
  val => {
    lockData.label = val ? '解锁' : '锁定';
    console.log(lockData);
  }
);
const cacheAll = reactive({
  newAddRowSeq: null,
  currentRecord: null,
  treeChildrenKey: 'childrenList',
  rowKeyFiled: 'sequenceNbr',
  newRecord: function (parentId, groupCode) {
    return {
      sequenceNbr: Date.now(),
      name: '',
      remark: null,
      addFlag: 1,
      lockFlag: 0,
      parentId: parentId,
      groupCode: groupCode,
    };
  },
  copyRow: function (row) {
    let newRow = {};
    Object.assign(newRow, row);
    newRow.sequenceNbr = Date.now();
    newRow.childrenList = null;
    newRow.addFlag = 1;
    newRow.lockFlag = 0;
    newRow.recDate = null;

    return newRow;
  },
});
watch(
  () => cacheAll.currentRecord,
  val => {
    console.log(
      '当前行',
      cacheAll.currentRecord,
      projectStore.currentSelectRow
    );
    if (!val) {
      projectStore.SET_CURRENTSELECTROW(null);
    } else {
      const currentSelectRow = tableData.value.findIndex(
        item => item.sequenceNbr === cacheAll.currentRecord.sequenceNbr
      );
      if (currentSelectRow !== -1) {
        projectStore.SET_CURRENTSELECTROW(val);
      } else {
        projectStore.SET_CURRENTSELECTROW(null);
      }

      if (projectStore.currentTreeInfo?.type == 3) {
        updateGljSelrowId(val.sequenceNbr, '工程概况', 'selRowId');
      } else {
        updateGljSelrowId(val.sequenceNbr, '项目概况', 'selRowId');
      }
    }
  }
);

const inputFinish = (row, e, attr) => {
  let value = xeUtils.trim(e.value);
  if (value.length > 50) {
    value = value.slice(0, 50);
    row[attr] = value;
    message.warning('输入过长，请输入50个字符范围内');
  }
  row[attr] = value;
  saveOrUpdateBasicInfo({ data: xeUtils.clone(row, true) }, false, 'edit');

  // emits('saveOrUpdateBasicInfo', { data: tableData.value });
};

const insertInData = (tree, selectNode, newNode) => {
  const key = cacheAll.rowKeyFiled;

  let levelType = projectStore.currentTreeInfo?.type;
  if (levelType === 3) {
    const index = tree.findIndex(item => item[key] === selectNode[key]) + 1;
    tree.splice(index, 0, newNode);
    return;
  }
  for (let pos = 0; pos < tree.length; pos++) {
    let node = tree[pos];
    if (node[key] === selectNode[key]) {
      node.childrenList.splice(0, 0, newNode);
      return;
    } else {
      let index = node.childrenList.findIndex(
        item => item[key] === selectNode[key]
      );
      if (index !== -1) {
        node.childrenList.splice(index + 1, 0, newNode);
        return;
      }
    }
  }
};

const resetCurrentRow = () => {
  let gljCheckTab = projectStore.gljCheckTab;
  let upSelRow = gljCheckTab[
    projectStore.currentTreeInfo?.sequenceNbr
  ]?.tabList.find(a => a.tabName == projectStore.tabSelectName);
  if (cacheAll.newAddRowSeq) {
    cacheAll.currentRecord = cacheAll.newAddRowSeq;
  } else if (upSelRow && upSelRow.selRowId !== '') {
    let obj = tableData.value?.find(a => a.sequenceNbr == upSelRow.selRowId);
    if (!obj) {
      obj = tableData.value[0];
    }
    cacheAll.currentRecord = obj;
    setTimeout(() => {
      basicInfoTable.value?.scrollToRow(obj);
    }, 100);
  } else {
    cacheAll.currentRecord = tableData.value[0];
  }
  basicInfoTable.value?.setCurrentRow(cacheAll.currentRecord);
  setMoveRowList(cacheAll.currentRecord, tableData.value);
};

function modalTip(content) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      title: '',
      content: content,
      onOk: () => {
        resolve(true);
      },
      onCancel() {
        resolve(false);
      },
    });
  });
}

async function insertHandle(posRow, newRow, type) {
  const selectRecord = posRow || basicInfoTable.value?.getCurrentRecord();
  if (!selectRecord) {
    await modalTip('请选中要插入的位置');
    return;
  }
  cacheAll.newAddRowSeq = selectRecord;
  console.log('=========11====: ', cacheAll.newAddRowSeq);
  saveOrUpdateBasicInfo(
    { data: xeUtils.clone(selectRecord, true) },
    false,
    type
  );
}
function resetLockHandle() {
  let levelType = projectStore.currentTreeInfo?.type;
  let apiData = {
    levelType: levelType,
    code: activeKey.value,
    type: 0,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: levelType === 3 ? projectStore.currentTreeInfo?.id : null,
    lockFlag: lockStatus.value ? 0 : 1,
  };
  csProject.lockBasicInfo(apiData).then(res => {
    if (res.status === 200) {
      getBasicInfo();
      lockStatus.value = !lockStatus.value;
    }
  });
}
async function deleteHandle(row) {
  const selectRecord = row || basicInfoTable.value?.getCurrentRecord();
  if (!selectRecord.addFlag) {
    //await modalTip('默认信息不能删除');
    message.warning('默认信息不能删除');
    return;
  }

  if (selectRecord.lockFlag) {
    //await modalTip('锁定行不能被删除');
    message.warning('锁定行不能被删除');
    return;
  }

  const status = await modalTip('确定要删除选中行？');
  focusTable();
  if (!status) {
    return;
  }

  deleteRowForBasicInfo(selectRecord[cacheAll.rowKeyFiled]);
}

const menuConfig = ref({
  body: {
    options: [
      [
        { code: 'insertRow', name: '插入行', disabled: false },
        { code: 'copyRow', name: '复制行', disabled: false },
        { code: 'deleteRow', name: '删除行', disabled: false },
      ],
    ],
  },
  visibleMethod({ row, type, options }) {
    basicInfoTable.value?.setCurrentRow(row);
    cacheAll.currentRecord = row;
    setMoveRowList(row, tableData.value);
    const $table = basicInfoTable.value;
    if ($table) {
      if (type === 'body') {
        options.forEach(list => {
          list.forEach(item => {
            if (item.code === 'deleteRow') {
              console.info(row);
              if (row && (!row.addFlag || row.lockFlag)) {
                item.disabled = true;
              } else {
                item.disabled = false;
              }
            }
            if (item.code === 'copyRow') {
              if (row && row.children.length > 0) {
                item.disabled = true;
              } else {
                item.disabled = false;
              }
            }
          });
        });
      }
    }
    return true;
  },
});

const contextMenuClickEvent = ({ menu, row, column }) => {
  const $table = basicInfoTable.value;
  if ($table) {
    switch (menu.code) {
      case 'insertRow':
        insertHandle(row, {}, 'insert');
        break;
      case 'copyRow':
        let newRow = cacheAll.copyRow(row);
        insertHandle(row, newRow, 'copy');
        break;
      case 'deleteRow':
        deleteHandle(row);
        break;
    }
  }
};
const handleKeyDownEvent = event => {
  let sTable = event.$table;
  let code = event.$event.code;
  let row = sTable.getCurrentRecord();
  let isEditing = sTable.isEditByRow(row);
  // 删除
  if (code == 'Delete' && !isEditing) {
    if (row && (!row.addFlag || row.lockFlag)) {
      return;
    }
    deleteHandle(row);
  }
};
const currentChangeEvent = ({ row, $rowIndex }) => {
  if (projectStore.currentTreeInfo?.type == 3) {
    updateGljSelrowId(row.sequenceNbr, '工程概况', 'selRowId');
  } else {
    updateGljSelrowId(row.sequenceNbr, '项目概况', 'selRowId');
  }
  cacheAll.currentRecord = row;
  setMoveRowList(row, tableData.value);
};

/**
 *
 * @param {*} row
 * @param {*} list
 * @param {*} resetList  只更新列表
 */
const setMoveRowList = (row, list, resetList = false) => {
  console.log('🚀 ~ setMoveRowList ~ row:', row);
  if (row?.parentId == 0) {
    row.parentId = null;
  }
  if (resetList) {
    projectStore.moveRow.tableData = list;
    return;
  }
  projectStore.moveRow = {
    tableData: toRaw(list),
    isTree: false,
    useRowList: [
      {
        ...row,
      },
    ],
  };
};

onUpdated(() => {
  //设置展开所有节点
  basicInfoTable.value?.setAllTreeExpand(true);

  // //设置选中行为第一行
  nextTick(() => {
    resetCurrentRow();
  });

  constructLevel.value = projectStore.currentTreeInfo?.type === 1;

  //重置锁定状态
  lockStatus.value = tableData.value && tableData.value[1]?.lockFlag;
});
onActivated(() => {
  cacheAll.newAddRowSeq = null;
  cacheAll.currentRecord = null;
  insetBus(bus, projectStore.componentId, 'basicInfo', async data => {
    if (data.name === 'insert') insertHandle(null, null, 'insert');
    if (data.name === 'lock') resetLockHandle(null);
    if (data.name === 'delete') deleteHandle(null);
  });
  projectStore.AsideMenuExpose['basicInfo'] = {
    insertHandle,
    resetLockHandle,
    deleteHandle,
    getTableData: getBasicInfo,
  };
});
onMounted(() => {
  getJzType();
  projectStore.AsideMenuExpose['basicInfo'] = {
    insertHandle,
    resetLockHandle,
    deleteHandle,
    getTableData: getBasicInfo,
  };
});
defineExpose({
  insertHandle,
  resetLockHandle,
  deleteHandle,
  getTableData: getBasicInfo,
});
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  padding: 0 10px;
}

.table-content {
  height: calc(100%);
}

.table-content {
  // height: calc(65%);
  height: 100%;
  //user-select: none;

  ::v-deep(.vxe-table .row-unit) {
    background: #f0ecf2;
  }

  ::v-deep(.vxe-table .row-sub) {
    background: #f9f7fa;
  }

  ::v-deep(.vxe-table .row-qd) {
    background: #e9eefa;
  }

  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }

  ::v-deep(.vxe-table .code-color) {
    color: #a73d3d;
  }

  ::v-deep(.vxe-table .index-bg) {
    background-color: #ffffff;
  }

  ::v-deep(
      .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column
    ) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }

  ::v-deep(.vxe-table--render-default.is--tree-line .vxe-header--column) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
}

.table-contentgk ::v-deep(.vxe-table--render-default .vxe-tree--node-btn) {
  color: #87b2f2 !important;
}

.table-content :deep(.vxe-table) {
  .vxe-tree--line {
    /* 修改连接线的颜色 */
    border-left: 1px solid #87b2f2;
    border-bottom: 1px solid #87b2f2;
  }

  .title-bold {
    font-weight: bold;
  }

  .color-red {
    color: #de3f3f;
  }

  .row-lock-color {
    background-color: #bfbfbf;
  }

  .vxe-cell .vxe-cell--label {
    // ::selection {
    user-select: none;
    // }
  }
}
</style>
