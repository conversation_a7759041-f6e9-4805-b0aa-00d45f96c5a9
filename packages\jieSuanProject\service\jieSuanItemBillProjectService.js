const {Service} = require("../../../core");

const BranchProjectLevelConstant = require("../../../electron/enum/BranchProjectLevelConstant");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const UpdateStrategy = require("../../../electron/main_editor/update/updateStrategy");
const {arrayToTree, treeToArray} = require("../../../electron/main_editor/tree");
const {JieSuanRcjStageUtils} = require("../utils/JieSuanRcjStageUtils");
const DePropertyTypeConstant = require('../../../electron/enum/DePropertyTypeConstant');
const JieSuanConstantUtil = require("../enum/JieSuanConstantUtil");
const FindRcjStrategy = require("../../../electron/rcj_handle/find/findRcjStrategy");
const gclList = require("../jsonData/zhiBiao/gcl_index");
/**
 * 结算分部分项service
 */
class JieSuanItemBillProjectService extends Service {
    constructor(ctx) {
        super(ctx);
        this._baseBranchProjectOptionService = this.service.baseBranchProjectOptionService;
        this._itemBillProjectOptionService = this.service.itemBillProjectOptionService;
    }

    disPlayType = {
        "0": " ",
        "01": "部",
        "02": "部",
        "03": "清",
        "04": "定"
    }


    /**
     * 导入预算文件后初始化 分部分项结算字段
     * @param obj  ysf的分部分项数据
     */
    async initYsToJieSuanFbfxData(args) {
        args.type = 1;
        await this.filterTempDel(args)
        let {constructId, singleId, unitId} = args;
        //获取单位数据
        let unitProjects = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        unitProjects.quantityDifferenceRangeMax = 15;
        unitProjects.quantityDifferenceRangeMin = -15;
        unitProjects.riskAmplitudeRangeMax = 5;
        unitProjects.riskAmplitudeRangeMin = -5;
        //获取单位中的分部分项数据
        if (!ObjectUtils.isEmpty(unitProjects)) {
            let itemBillProjects = unitProjects.itemBillProjects.getAllNodes();
            for (const itemBillProject of itemBillProjects) {
                itemBillProject.backTotal = itemBillProject.total;
                itemBillProject.annotations = null;
                itemBillProject.color = null;
                //只处理清单定额
                if (itemBillProject.kind === BranchProjectLevelConstant.qd || itemBillProject.kind === BranchProjectLevelConstant.de) {
                    //修改数据
                    //结算工程量
                    itemBillProject.backQuantity = itemBillProject.quantity;
                    //结算单价
                    itemBillProject.backPrice = itemBillProject.price;

                    itemBillProject.backQuantityExpressionNbr = itemBillProject.quantityExpressionNbr;

                    if (ObjectUtils.isNotEmpty(itemBillProject.quantityExpression) && itemBillProject.quantityExpression.toString().includes("GCLMXHJ")) {
                        itemBillProject.quantityExpression = itemBillProject.quantity;
                    }
                    //所有清单全部锁定综合单价
                    if (itemBillProject.kind === BranchProjectLevelConstant.qd) {
                        //锁定综合单价
                        itemBillProject.lockPriceFlag = true;
                        itemBillProject.lockPriceBack = itemBillProject.price;
                        itemBillProject.isLocked = false;
                        //量差设置默认值
                        itemBillProject.quantityDifference = 0;
                        itemBillProject.quantityDifferenceProportion = 0;
                        //判断清单下定额是否是超高  或者垂运
                        this.ifCostDeCgCy(itemBillProjects,itemBillProject);

                        itemBillProject.backQuantityExpression = itemBillProject.quantityExpression;
                        //计算下挂的定额含量
                        let filterDeList = itemBillProjects.filter(de => de.parentId === itemBillProject.sequenceNbr);
                        //计算定额含量
                        for (const de of filterDeList) {
                            //含量k=定额or人材机的结算工程量/所属清单行的结算工程量
                            de.deProportion = NumberUtil.numberScale6(NumberUtil.divide(de.quantity, itemBillProject.quantity));
                        }
                    }

                }
                //原始数据
                itemBillProject.originalFlag = true;
            }
        } else {
            console.log("参数数据有误--单位不存在" + unitId);
        }


    }


    ifCostDeCgCy(all,qd){
        let des = all.filter(item=>item.parentId == qd.sequenceNbr);
        if(ObjectUtils.isNotEmpty(des)){
             for(const d of des){
                 if(d.isCostDe==DePropertyTypeConstant.CG_DE ||  d.isCostDe==DePropertyTypeConstant.DS_CY_DE ||  d.isCostDe==DePropertyTypeConstant.DX_CY_DE){
                     //包含费用定额的原始清单
                    qd.containYsCyCgQd=true;
                    break;
                 }
             }
        }
    }


    /**
     * 过滤临时删除数据
     * @param args
     * @returns {Promise<void>}
     */
    async filterTempDel(args) {
        let {constructId, singleId, unitId, type} = args;
        //获取单位数据
        let unitProjects = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        if (type == 1) {
            let itemBillProjects = unitProjects.itemBillProjects.getAllNodes();
            itemBillProjects = itemBillProjects.filter(item => item.tempDeleteFlag != true);
            unitProjects.itemBillProjects = arrayToTree(itemBillProjects);

        } else {
            let measureProjectTables = unitProjects.measureProjectTables.getAllNodes();
            measureProjectTables = measureProjectTables.filter(item => item.tempDeleteFlag != true);
            unitProjects.measureProjectTables = arrayToTree(measureProjectTables);
        }
    }


    /**
     * 所选定额的清单是否为锁定综合单价
     * @param constructId
     * @param singleId
     * @param unitId
     * @param deId
     * @returns {Promise<void>}
     */
    async qdIFLockPriceByDeId(args) {
        let {constructId, singleId, unitId, deId} = args;
        let qdByDeId = PricingFileFindUtils.getQdByDeId(constructId, singleId, unitId, deId);
        if (!ObjectUtils.isEmpty(qdByDeId)) {
            if (qdByDeId.lockPriceFlag) {
                return true;
            } else {
                return false;
            }
        }
    }


    /**
     * 修改结算相关的分部分项数据
     * @param args
     * @returns {Promise<boolean>}
     */
    async updateJieSuanFbfxData(args) {
        let {constructId, singleId, unitWorkId, pointLineId, column, value,major} = args;
        args["unitId"] = unitWorkId;
        let upDateInfo = {
            column, value
        }
        //获取分部分项数据
        let allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId);
        let pointLine = allData.find(e => e.sequenceNbr === pointLineId);
        //修改锁定综合单价标识
        if (column == "lockPriceFlag" && value !== pointLine.lockPriceFlag) {
            pointLine.lockPriceFlag = upDateInfo.lockPriceFlag;
        }

        // 修改清单工程量表达式  ----定额不可修改
        if (column == "quantityExpression" && value !== pointLine.quantityExpression) {
            //计算清单工程量
            pointLine.quantityExpression = value.toUpperCase();
            this.service.baseBranchProjectOptionService.caculateQuantityExpressionAndQuantity(constructId, singleId, unitWorkId, pointLine);
            //计算量差
            await this.mathQuantityDifference(constructId, singleId, unitWorkId, pointLine);
            //处理清单下挂的定额
            await this.updateDeQuantity(constructId, singleId, unitWorkId, pointLine.sequenceNbr, "fbfx");
            if(pointLine.kind==BranchProjectLevelConstant.qd){
                //修改分期工程量明细
                JieSuanRcjStageUtils.qdStageCalculate(pointLine);
            }
            //费用代码记取
            await this.service.jieSuanProject.jieSuanProjectService.countFeeCodeAndMathFee(args);
        }
        //分部分项指标
        if (column == "indicatorName") {
            pointLine.indicatorName = value=="无"?null:value;
            pointLine.indicatorNameOfMajor = major;

        }
        //工程量指标
        if (column == "quantityIndicatorName" ) {
            if( value=="无"){
                pointLine.quantityIndicatorName =null;
                pointLine.quantitIndicatorNameOfMajor = major;
                pointLine.quantityIndicatorUnit = null;
            }else {
                pointLine.quantityIndicatorName = value;
                pointLine.quantitIndicatorNameOfMajor = major;
                //更新工程量单位
                let find = gclList[major].find(gcl=> gcl.指标名称==value );
                pointLine.quantityIndicatorUnit = ObjectUtils.isNotEmpty(find)?find.单位:null;
            }

        }

        return {
            "enableUpdatePrice": true,
            "lineInfo": pointLine
        };

    }


    /**
     * 修改清单下定额工程量
     * @param constructId
     * @param singleId
     * @param unitId
     * @param qdId  清单id
     * @param qdQuantity  清单结算工程量
     * @param type   fbfx 分部分项  csxm 措施项目
     * @returns {Promise<void>}
     */
    async updateDeQuantity(constructId, singleId, unitId, qdId, type) {
        //获取清单数据
        let qd;
        let code;
        if (type == "fbfx") {
            qd = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).find(qd => qd.sequenceNbr == qdId);
        } else {
            qd = PricingFileFindUtils.getCSXM(constructId, singleId, unitId).find(qd => qd.sequenceNbr == qdId);
        }

        //获取所有的定额
        let deList = PricingFileFindUtils.getDeByQdId(constructId, singleId, unitId, qdId);
        if (!ObjectUtils.isEmpty(deList)) {
            for (const de of deList) {
                if (type == "fbfx") {
                    code = de.bdCode;
                } else {
                    code = de.fxCode;
                }
                /* 以下几种特殊情况定额结算工程量不随之改变：
                    导入的定额工程量含有“DSZSGR”/"DXZSGR"的装饰垂运定额
                    装饰超高定额
                    安装费用定额
                    标准换算子父级定额的关联（变量关联）
                    其他总价措施费用定额
                    安文费（不会出现在分部分项）*/
                if (ObjectUtils.isNotEmpty(code)) {
                    if (!de.quantity.toString().includes("DSZSGR") && !de.quantity.toString().includes("DXZSGR") &&
                        de.isCostDe != 3 && de.isCostDe != 5 && de.isCostDe != 2 && de.isCostDe != 1 && ObjectUtils.isEmpty(de.createDeId) && ObjectUtils.isEmpty(de.relationDeId)) {
                        //定额工程量为0的话  不需要修改定额工程量数据
                        // if(de.quantity!=0){
                        //计算定额结算工程量
                        de.quantity = NumberUtil.numberScale6(NumberUtil.multiply(qd.quantity, de.deProportion));
                        de.quantityExpression = de.quantity.toString();
                        let updateStrategy = new UpdateStrategy({constructId, singleId, unitId, pageType: type});
                        let upDateInfo = {
                            "quantityExpression": de.quantityExpression
                        }
                        await updateStrategy.execute({pointLineId: de.sequenceNbr, upDateInfo});
                        de.quantityExpression = null;
                        // }
                    }
                }
            }
        }
    }


    /**
     * 工程量批量乘以系数(分部分项才有)
     * @param constructId 工程id
     * @param singleId 单项id
     * @param unitId 单位id
     * @param coefficientValue 系数值
     * @param qdList 选中的清单
     * @returns {Promise<void>}
     */
    async batchMathCoefficientAdjustment(constructId, singleId, unitId, coefficientValue, qdList) {
        //获取所有的清单
        if (!ObjectUtils.isEmpty(qdList)) {
            //获取所有的清单
            let qdFbList = await PricingFileFindUtils.getQdByfbfx(constructId, singleId, unitId);
            const ids = Object.values(qdList).map(qd => qd.sequenceNbr);
            const qdArr = qdFbList.filter(item => ids.includes(item.sequenceNbr));
            for (const qd of qdArr) {
                //组装清单结算工程量表达式  计算表达式值  结算工程量
                let quantityExpression = qd.quantityExpression;
                const regex = /[()+\-*/\*]/;
                const regex1 = /[+\-]/;
                const regex2 = /[()]/;
                //如果不含计算
                if (regex.test(quantityExpression)) {
                    //是否含有括号
                    if (regex2.test(quantityExpression)) {
                        //去掉前后括号
                        const subStr = quantityExpression.substring(1, quantityExpression.length);
                        //表示中间有括号
                        if (regex2.test(subStr)) {
                            //直接加上外括号
                            quantityExpression = "(" + quantityExpression + ")";
                        }
                    } else {
                        //没有括号 有+-号
                        if (regex1.test(quantityExpression)) {
                            quantityExpression = "(" + quantityExpression + ")";
                        }
                    }
                }
                qd.quantityExpression = quantityExpression + "*" + coefficientValue;
                this._baseBranchProjectOptionService.caculateQuantityExpressionAndQuantity(constructId, singleId, unitId, qd);
                let upDateInfo = {
                    "column":"quantityExpression",
                    "value":qd.quantityExpression
                }
                //如果清单是原始数据   按比例计算定额的工程量
                if (qd.originalFlag) {
                    //计算量差
                    await this.mathQuantityDifference(constructId, singleId, unitId, qd);
                    //处理清单下挂的定额
                    await this.updateDeQuantity(constructId, singleId, unitId, qd.sequenceNbr, "fbfx");
                    //计算该清单的分期工程量明细
                    JieSuanRcjStageUtils.qdStageCalculate(qd);
                } else {
                    // let unitWorkId=unitId;
                    // let pointLineId=qd.sequenceNbr;
                    // await this.service.itemBillProjectOptionService.updateByList(constructId, singleId, unitWorkId, pointLineId, upDateInfo);
                    // //按照正常的方式修改
                    let updateStrategy = new UpdateStrategy({constructId, singleId, unitId, pageType: "fbfx"});
                    await updateStrategy.execute({pointLineId: qd.sequenceNbr, upDateInfo});
                }
            }
        }

    }


    /**
     * 设置所有单位的量差范围
     * @param args
     * @param max
     * @param min
     */
    updateAllUnitQuantityDifferenceRange(constructId, max, min) {
        let unitList = PricingFileFindUtils.getUnitList(constructId);
        for (let unit of unitList) {
            //重置颜色
            this.updateUnitQuantityDifferenceRange(constructId, unit.spId, unit.sequenceNbr, max, min);
        }
    }

    /**
     * 设置单个单位的量差范围
     * @param args
     * @param max
     * @param min
     */
    updateUnitQuantityDifferenceRange(constructId, singleId, unitId, max, min) {
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        unit.quantityDifferenceRangeMax = max;
        unit.quantityDifferenceRangeMin = min;
        //获取所有清单
        let qdList = PricingFileFindUtils.getQdByfbfx(constructId, singleId, unitId);
        //重置量差比例颜色
        for (const qd of qdList) {
            this.setQuantityDifferenceProportionColour(qd, max, min);
        }

    }


    /**
     * 计算量差
     * @param obj
     */
    async mathQuantityDifference(constructId, singleId, unitId, obj) {
        //计算量差 和量差比例
        obj.quantityDifference = NumberUtil.subtract(obj.quantity, obj.backQuantity);
        //量差比例(%)=量差/合同工程量*100%    合同工程量可能是0
        obj.quantityDifferenceProportion = obj.backQuantity != 0 ? (NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.divide(obj.quantityDifference, obj.backQuantity), 100))) : 0;
        //设计量差颜色
        //获取当前单位的量差范围
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let quantityDifferenceRangeMax = unit.quantityDifferenceRangeMax;
        let quantityDifferenceRangeMin = unit.quantityDifferenceRangeMin;
        await this.setQuantityDifferenceProportionColour(obj, quantityDifferenceRangeMax, quantityDifferenceRangeMin);
    }

    /**
     * 量差比例颜色设置
     * @param obj
     * @param max
     * @param min
     */
    async setQuantityDifferenceProportionColour(obj, max, min) {
        if (obj.quantityDifferenceProportion < min) {
            obj.quantityDifferenceProportionColour = 2;
        }
        if (obj.quantityDifferenceProportion > max) {
            obj.quantityDifferenceProportionColour = 1;
        }
        if (obj.quantityDifferenceProportion >= min && obj.quantityDifferenceProportion <= max) {
            obj.quantityDifferenceProportionColour = 0;
        }
    }


    /**
     * 分部分项层级展示数据查询接口
     */
    async showFbfxCjJg(constructId, singleId, unitWorkId, sequenceNbr, pageNum, pageSize, isAllFlag,colorList) {
        let datas = this.service.itemBillProjectOptionService.getFbFx(constructId, singleId, unitWorkId, sequenceNbr, pageNum, pageSize, isAllFlag,colorList);
        // //处理定额下挂的主材设备数据
        this.handleZjSb(datas.data,constructId, singleId, unitWorkId);
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId);
        //补充定额  处理新增后的人材机初始化
        for(const de of datas.data){
            if(de.kind==BranchProjectLevelConstant.de){
                let res = unit.constructProjectRcjs.filter(rcj=> rcj.deId==de.sequenceNbr && !rcj.hasOwnProperty("jieSuanBasePrice"));
                //初始化人材机结算字段主要是补充的人材机数据
                if(ObjectUtils.isNotEmpty(res)){
                    JieSuanRcjStageUtils.rcjDataHandler(res, false,unit);
                }
                //处理子集人材机数据
                let rcjDetailList = unit.rcjDetailList.filter(rcj=> rcj.deId==de.sequenceNbr && !rcj.hasOwnProperty("jieSuanBasePrice"));
                if (ObjectUtils.isNotEmpty(rcjDetailList)) {
                    JieSuanRcjStageUtils.rcjDataHandler(rcjDetailList, false,unit);
                }
            }
        }

        // 填充关联合同清单数据
        await this.service.jieSuanProject.jieSuanRelatedContractsQdService.setRelatedQdData(datas.data);

        return datas;
    }


    handleZjSb(list,constructId, singleId, unitWorkId){
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId);
        for(const item  of list){
            if(item.kind==JieSuanConstantUtil.ZHUCAI || item.kind==JieSuanConstantUtil.SHEBEI){
                let constructProjectRcj = unit.constructProjectRcjs.find(rcj=>rcj.sequenceNbr==item.sequenceNbr);
                //获取定额
                let de = list.find(de=>de.sequenceNbr==item.parentId);
                //合同内定额下挂主材设备
                if(de.originalFlag){
                    item.backQuantity=constructProjectRcj.backQuantity;
                }else {
                    item.backQuantity=0;
                }
                item.total=null;

            }
        }

    }

    handleData(hierachy, sequenceNbr, datas) {
        let mockAllData = datas.data;
        // 当前层级
        let num = 0;
        // 保留列表数据
        let zhanArr = [];
        // 底层列表数据
        let arr = [];
        zhanArr.push(sequenceNbr);
        // 展开到分部
        if (this.isNumber(hierachy)) {
            hierachy = parseInt(hierachy, 10);

            let zhanArr1 = [];
            zhanArr1.push(sequenceNbr);
            this.foraaa(zhanArr1, mockAllData, arr, num, hierachy, zhanArr);


            for (let i = 0; i < mockAllData.length; i++) {
                let itemBillProject = mockAllData[i];

                if (zhanArr.includes(itemBillProject.sequenceNbr)) {
                    for (let mockAllDatum of mockAllData) {
                        if (itemBillProject.sequenceNbr === mockAllDatum.parentId) {
                            itemBillProject.displaySign = 1;
                            break;
                        }
                    }
                } else if (arr.includes(itemBillProject.sequenceNbr)) {
                    for (let mockAllDatum of mockAllData) {
                        if (itemBillProject.sequenceNbr === mockAllDatum.parentId && !zhanArr.includes(itemBillProject.sequenceNbr)) {
                            itemBillProject.displaySign = 2;
                            break;
                        }
                    }
                } else {
                    mockAllData.splice(i, 1);
                    i--;
                }
            }

        } else {
            // 展开到清单
            if (hierachy === "qd") {
                mockAllData.forEach(a => {
                    if (a.kind === "03") {
                        arr.push(a.sequenceNbr);
                    }
                })
            } else if (hierachy === "de") {
                // 展开到定额
                mockAllData.forEach(a => {
                    if (a.kind === "04") {
                        arr.push(a.sequenceNbr);
                    }
                })
            }
            for (let noArrElement of arr) {
                this.forAte(noArrElement, mockAllData, zhanArr);
            }

            for (let i = 0; i < mockAllData.length; i++) {
                let itemBillProject = mockAllData[i];

                if (zhanArr.includes(itemBillProject.sequenceNbr)) {
                    for (let mockAllDatum of mockAllData) {
                        if (itemBillProject.sequenceNbr === mockAllDatum.parentId) {
                            if (arr.includes(itemBillProject.sequenceNbr)) {
                                itemBillProject.displaySign = 2;
                            } else {
                                itemBillProject.displaySign = 1;
                            }
                            break;
                        }
                    }
                } else {
                    mockAllData.splice(i, 1);
                    i--;
                }
            }

        }
    }


    isNumber(str) {
        // 使用正则表达式判断是否为数字（包括整数和小数）
        if (!/^-?\d+(\.\d+)?$/.test(str)) return false;
        // 使用Number.isInteger()判断是否为整数
        if (Number.isInteger(+str)) return true;
        // 如果是整数则返回true，否则返回false
        return false;
    }

    forAte(a, arr, zhanArr) {
        for (let arrElement of arr) {
            if (a === arrElement.sequenceNbr) {
                zhanArr.push(arrElement.sequenceNbr);
                this.forAte(arrElement.parentId, arr, zhanArr);
                break;
            }
        }
    }

    async foraaa(zhanArr1, mockAllData, arr, num, hierachy, zhanArr) {
        let aaa = [];
        for (let zhanArrElement of zhanArr1) {
            for (let mockAllDatum of mockAllData) {
                if (mockAllDatum.parentId === zhanArrElement) {
                    aaa.push(mockAllDatum.sequenceNbr);
                }
            }
        }
        if (num === hierachy - 1) {
            aaa.forEach(a => arr.push(a));
            return;
        } else {
            num++;
            aaa.forEach(a => zhanArr.push(a));
            this.foraaa(aaa, mockAllData, arr, num, hierachy, zhanArr);
        }
    }


    /**
     *
     * @param id  新增数据id
     * @param type  数据类型  1 分部分项  2 措施项目
     * @returns {Promise<void>}
     */
    async updateNewAddHtData(args) {
        let {constructId, singleId, unitId, id, type} = args;
        //获取分部分项数据
        let allData;
        if (type == 1) {
            allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes();
        } else {
            allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId).getAllNodes();
        }
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let pointLine = allData.find(e => e.sequenceNbr === id);
        if (pointLine.kind == BranchProjectLevelConstant.qd || pointLine.kind == BranchProjectLevelConstant.de) {
            pointLine.backQuantity = 0;
            pointLine.backPrice = 0;
            pointLine.backTotal = 0;
            pointLine.quantityDifference = null;
            //量差比例(%)=量差/合同工程量*100%    合同工程量可能是0
            pointLine.quantityDifferenceProportion = null;
            pointLine.quantityDifferenceProportionColour = 0;
            //新增的人材机数据  只有合同内的需要计算含量   4.11
            if(pointLine.kind == BranchProjectLevelConstant.de && unit.originalFlag){
                //含量k=定额or人材机的结算工程量/所属清单行的结算工程量
                let qd = allData.find(e => e.sequenceNbr === pointLine.parentId);
                pointLine.deProportion = NumberUtil.numberScale6(NumberUtil.divide(pointLine.quantity, qd.quantity));
            }

        }
        // //计算量差  7.8李志豪说的 新增清单：量差比例。量差都为空
        // if(pointLine.kind==BranchProjectLevelConstant.qd){
        //     this.mathQuantityDifference(constructId, singleId, unitId, pointLine);
        // }

        //人材机初始化
        await this.initDeRcjList(constructId, singleId, unitId, pointLine);


        return true;

    }


    /**
     * 处理合同内的粘贴数据
     * @param args
     * @returns {Promise<void>}
     */
    async updatePasteLineHtData(args) {
        let {type, constructId, singleId, unitId} = args;
        //获取缓存数据
        let copeUnitProject = PricingFileFindUtils.getProjectBuffer();
        let coverData = {
            itemBillProjects: [],//分部分项
            measureProjectTables: [],//措施项目
            otherProjects: [],//其他项目
            unitCostSummarys: [],
            constructProjectRcjs: [],
            rcjDetailList: [],
            obj: {constructId, singleId, unitId}
        }

        if (type == 1) {
            let itemBillProjects = copeUnitProject.itemBillProjects;
            itemBillProjects.forEach(item => {
                clean(item);
                this.countCalculateCoefficient(item,itemBillProjects);
            });
            coverData.itemBillProjects = itemBillProjects;
        } else {
            let measureProjectTables = copeUnitProject.measureProjectTables;
            measureProjectTables.forEach(m => {
                clean(m);
                this.countCalculateCoefficient(m,measureProjectTables);
            });
            coverData.measureProjectTables = measureProjectTables;
        }

        copeUnitProject.constructProjectRcjs.forEach(k => {
            k.originalFlag = false;
        });
        coverData.constructProjectRcjs = copeUnitProject.constructProjectRcjs;

        copeUnitProject.rcjDetailList.forEach(k => {
            k.originalFlag = false;
        })
        coverData.rcjDetailList = copeUnitProject.rcjDetailList;

        //更新缓存数据
        PricingFileFindUtils.setProjectBuffer(Object.assign({}, copeUnitProject, coverData));

        function clean(obj) {
            obj.backQuantity = 0;
            obj.backPrice = 0;
            obj.backTotal = 0;
            obj.originalFlag = false;
            obj.quantityDifference = null;
            obj.lockPriceFlag = false;
            // obj.quantityExpression =obj.quantity.toString();
            //量差比例(%)=量差/合同工程量*100%    合同工程量可能是0
            obj.quantityDifferenceProportion = null;
            obj.quantityDifferenceProportionColour = 0;
        }
    }


    /**
     * 计算系数  转换定额工程量表达式
     */
    countCalculateCoefficient(obj, list) {
        //如果是定额    并且表达式不含QDL
        if (obj.kind == BranchProjectLevelConstant.de ) {
            if(ObjectUtils.isEmpty(obj.quantityExpression) || !obj.quantityExpression.toUpperCase().includes("QDL")){
                //获取对应清单
                let find = list.find(o=>o.sequenceNbr==obj.parentId);
                obj.quantityExpression="QDL*"+Number(NumberUtil.divide(obj.quantity,find.quantity).toFixed(17));

            }

        }
    }





    /**
     * 处理粘贴数据
     1. 合同内复制：复制原始清单，粘贴后为新增清单/定额/人材机，保留关联关系及结算工程量，费用定额过滤不复制，带有DSZSGR/DXZSGR复制保留变量
     2. 合同内复制到合同外：粘贴后为新增清单/定额/人材机保留关联关系及结算工程量，费用定额过滤不复制，带有DSZSGR/DXZSGR复制保留变量
     3. 合同外复制到合同内：粘贴后为新增清单/定额/人材机保留关联关系及结算工程量，费用定额过滤不复制，带有DSZSGR/DXZSGR复制保留变量
     * @param args
     * @returns {Promise<void>}
     */
    async updatePasteData(args) {
        let {type, constructId, singleId, unitId} = args;
        //获取目标单位数据
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //获取缓存数据
        let copeUnitProject = PricingFileFindUtils.getProjectBuffer();
        let coverData = {
            itemBillProjects: [],//分部分项
            measureProjectTables: [],//措施项目
            otherProjects: [],//其他项目
            unitCostSummarys: []
        }

        let sourceUnit;
        let flag = false;

        if (type == 1) {
            let itemBillProjects = copeUnitProject.itemBillProjects;

            sourceUnit = PricingFileFindUtils.getUnit(copeUnitProject.obj.constructId, copeUnitProject.obj.singleId, copeUnitProject.obj.unitId);
            if (ObjectUtils.isNotEmpty(sourceUnit) && ObjectUtils.isNotEmpty(unit)) {
                //如果是合同外复制到合同外 不做任何处理  否则将过滤掉出垂运外的费用定额
                if (sourceUnit.originalFlag == true || unit.originalFlag == true) {
                    coverData.itemBillProjects = itemBillProjects.filter(de => (de.isCostDe != 3 && de.isCostDe != 5 && de.isCostDe != 2 && de.isCostDe != 1));
                    flag = true;
                }
            } else {
                console.log("请求参数有误")
            }
        } else {
            let measureProjectTables = copeUnitProject.measureProjectTables;
            sourceUnit = PricingFileFindUtils.getUnit(copeUnitProject.obj.constructId, copeUnitProject.obj.singleId, copeUnitProject.obj.unitId);
            if (ObjectUtils.isNotEmpty(sourceUnit) && ObjectUtils.isNotEmpty(unit)) {
                //如果是合同外复制到合同外 不做任何处理  否则将过滤掉出垂运外的费用定额
                if (sourceUnit.originalFlag == true || unit.originalFlag == true) {
                    coverData.itemBillProjects = measureProjectTables.filter(de => (de.isCostDe != 3 && de.isCostDe != 5 && de.isCostDe != 2 && de.isCostDe != 1));
                    flag = true;
                }
            } else {
                console.log("请求参数有误")
            }
        }
        if (flag) {
            //更新缓存数据
            PricingFileFindUtils.setProjectBuffer(Object.assign({}, copeUnitProject, coverData));
        }


    }


    /**
     * 结算中添加定额后初始化人材机
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine
     */
    async initDeRcjList(constructId, singleId, unitId, pointLine) {
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        if (ObjectUtils.isEmpty(unit) || ObjectUtils.isEmpty(pointLine)) {
            return;
        }
        if (pointLine.kind == "03" && ObjectUtils.isEmpty(pointLine.children)) {
            return;
        }

        let deId = new Array();

        if (pointLine.kind == "03") {
            let ids = pointLine.children.map(k => k.sequenceNbr);
            deId.push(...ids);
        }
        if (pointLine.kind == "04") {
            deId.push(pointLine.sequenceNbr);
        }

        for (let sequenceNbr of deId) {
            let constructProjectRcjs = unit.constructProjectRcjs;
            if (!ObjectUtils.isEmpty(constructProjectRcjs)) {
                let constructProjectRcjs1 = constructProjectRcjs.filter(i => i.deId == sequenceNbr);
                JieSuanRcjStageUtils.rcjDataHandler(constructProjectRcjs1,false,unit);
            }

            let rcjDetailList = unit.rcjDetailList;
            if (!ObjectUtils.isEmpty(rcjDetailList)) {
                let rcjDetailList1 = rcjDetailList.filter(i => i.deId == sequenceNbr);
                if (!ObjectUtils.isEmpty(rcjDetailList1)) {
                    JieSuanRcjStageUtils.rcjDataHandler(rcjDetailList1,false,unit);
                }
            }

        }


    }


}

JieSuanItemBillProjectService.toString = () => '[class JieSuanItemBillProjectService]';
module.exports = JieSuanItemBillProjectService;




