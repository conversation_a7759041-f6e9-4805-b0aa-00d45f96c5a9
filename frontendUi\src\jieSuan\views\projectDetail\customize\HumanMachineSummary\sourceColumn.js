import { ref } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { includes } from 'lodash';
const store = projectDetailStore();

const getTableColumnsJieSuan = (showInfo) => {
  // 公共列定义
  const commonColumns = [
    {
      title: '来源工程',
      field: 'location',
      width: 200,
      classType: 1,
      fixed: 'left',
      slot: false,
      type: [0, 1, 2, 3, 8, 9],
      deStandardReleaseYear: ['12', '22'],
      taxMode: [0, 1],
      levelType: [1, 2],
      contract: [1, 2],
    },
    {
      title: '名称',
      field: 'materialName',
      width: 150,
      classType: 1,
      fixed: 'left',
      slot: true,
      type: [0, 1, 2, 3, 8, 9],
      deStandardReleaseYear: ['12', '22'],
      taxMode: [0, 1],
      levelType: [1, 2],
      contract: [1, 2],
    },
    {
      title: '规格型号',
      field: 'specification',
      width: 100,
      classType: 1,
      slot: true,
      type: [0, 1, 2, 3, 8, 9],
      deStandardReleaseYear: ['12', '22'],
      taxMode: [0, 1],
      levelType: [1, 2],
      contract: [1, 2],
    }
  ];

  // 列配置定义
  const columnConfigs = {
    // 合同内所有人材机
    engineeringProject: [
      ...commonColumns,
      { title: '结算数量', field: 'totalNumber', width: 100, classType: 1 },
      { title: '单位价差', field: 'jieSuanPriceDifferenc', width: 100, classType: 1 },
      { title: '价差合计', field: 'jieSuanPriceDifferencSum', width: 100, classType: 1 }
    ],

    // 12一般计税/12简易计税
    general12And12Simple: [
      ...commonColumns,
      { title: '调差工程量', field: 'jieSuanStageDifferenceQuantity', width: 100, classType: 1 },
      { title: '基期价', field: 'jieSuanBasePrice', width: 100, classType: 1 },
      { title: '结算单价', field: 'jieSuanPrice', width: 100, classType: 1 },
      { title: '风险幅度范围(%)', field: 'riskAmplitudeRangeMin', width: 100, classType: 1 },
      { title: '单位价差', field: 'jieSuanPriceDifferenc', width: 100, classType: 1 },
      { title: '价差合计', field: 'jieSuanPriceDifferencSum', width: 100, classType: 1 },
      { title: '取费', field: 'jieSuanFee', width: 120, classType: 1 },
      { title: '结算除税系数', field: 'taxRemoval', width: 100, classType: 1 },
      { title: '调差方法', field: 'adjustMethod', width: 200, classType: 1 },
    ],

    // 22一般计税/12+22一般计税
    general22And12_22General: [
      ...commonColumns,
      { title: '调差工程量', field: 'jieSuanStageDifferenceQuantity', width: 100, classType: 1 },
      { title: '不含税基期价', field: 'priceBaseJournal', width: 100, classType: 1 },
      { title: '结算含税单价', field: 'jieSuanPriceMarketTax', width: 100, classType: 1 },
      { title: '结算不含税单价', field: 'priceMarket', width: 100, classType: 1 },
      { title: '结算税率(%)', field: 'taxRate', width: 100, classType: 1 },
      { title: '风险幅度范围(%)', field: 'riskAmplitudeRangeMin', width: 100, classType: 1 },
      { title: '单位价差', field: 'jieSuanPriceDifferenc', width: 100, classType: 1 },
      { title: '价差合计', field: 'jieSuanPriceDifferencSum', width: 100, classType: 1 },
      { title: '取费', field: 'jieSuanFee', width: 100, classType: 1 },
      { title: '调差方法', field: 'adjustMethod', width: 200, classType: 1 },
    ],

    // 22简易计税/12+22简易计税
    simple22And12_22Simple: [
      ...commonColumns,
      { title: '调差工程量', field: 'jieSuanStageDifferenceQuantity', width: 100, classType: 1 },
      { title: '基期价', field: 'jieSuanBasePrice', width: 100, classType: 1 },
      { title: '结算含税单价', field: 'jieSuanPriceMarketTax', width: 100, classType: 1 },
      { title: '结算不含税单价', field: 'priceMarket', width: 100, classType: 1 },
      { title: '结算税率(%)', field: 'taxRate', width: 100, classType: 1 },
      { title: '风险幅度范围(%)', field: 'riskAmplitudeRangeMin', width: 100, classType: 1 },
      { title: '单位价差', field: 'jieSuanPriceDifferenc', width: 100, classType: 1 },
      { title: '价差合计', field: 'jieSuanPriceDifferencSum', width: 100, classType: 1 },
      { title: '取费', field: 'jieSuanFee', width: 100, classType: 1 },
      { title: '调差方法', field: 'adjustMethod', width: 200, classType: 1 },
    ],

    // 合同外12一般计税/12简易计税
    general12And12SimpleOriginal: [
      ...commonColumns,
      { title: '数量', field: 'totalNumber', width: 100, classType: 1 },
      { title: '合同/确认单价', field: 'marketPrice', width: 100, classType: 1 },
      { title: '单位价差', field: 'jieSuanPriceDifferenc', width: 100, classType: 1 },
      { title: '价差合计', field: 'jieSuanPriceDifferencSum', width: 100, classType: 1 },
    ],

    // 合同外22一般计税/12+22一般计税/22简易计税/12+22简易计税
    general22And12_22GeneralAndSimple: [
      ...commonColumns,
      { title: '数量', field: 'totalNumber', width: 100, classType: 1 },
      { title: '合同/确认单价', field: 'marketPrice', width: 100, classType: 1 },
      { title: '合同/确认不含税单价', field: 'priceMarket', width: 140, classType: 1 },
      { title: '合同/税率', field: 'jieSuantaxRate', width: 100, classType: 1 },
      { title: '单位价差', field: 'jieSuanPriceDifferenc', width: 100, classType: 1 },
      { title: '价差合计', field: 'jieSuanPriceDifferencSum', width: 100, classType: 1 },
    ],

    // 价差12一般计税/12简易计税
    priceDifferenceG12AndS22: [
      ...commonColumns,
      { title: '调差工程量', field: 'jieSuanStageDifferenceQuantity', width: 150, classType: 1 },
      { title: '基期价', field: 'jieSuanBasePrice', width: 150, classType: 1 },
      { title: '结算单价', field: 'marketPrice', width: 100, classType: 1 },
      { title: '风险幅度范围(%)', field: 'riskAmplitudeRangeMin', width: 150, classType: 1 },
      { title: '单位价差', field: 'jieSuanPriceDifferenc', width: 150, classType: 1 },
      { title: '价差合计', field: 'jieSuanPriceDifferencSum', width: 100, classType: 1 },
      { title: '结算除税系数(%)', field: 'taxRemoval', width: 150, classType: 1 },
      { title: '取费', field: 'jieSuanFee', width: 150, classType: 1 },
    ],

    // 价差22一般计税/12+22一般计税
    priceDifferenceG22AndG12_22: [
      ...commonColumns,
      { title: '调差工程量', field: 'jieSuanStageDifferenceQuantity', width: 150, classType: 1 },
      { title: '不含税基期价', field: 'priceBaseJournal', width: 150, classType: 1 },
      { title: '结算含税单价', field: 'jieSuanPriceMarketTax', width: 150, classType: 1 },
      { title: '结算不含税单价', field: 'priceMarket', width: 150, classType: 1 },
      { title: '结算税率(%)', field: 'taxRate', width: 150, classType: 1 },
      { title: '风险幅度范围(%)', field: 'riskAmplitudeRangeMin', width: 150, classType: 1 },
      { title: '单位价差', field: 'jieSuanPriceDifferenc', width: 150, classType: 1 },
      { title: '价差合计', field: 'jieSuanPriceDifferencSum', width: 150, classType: 1 },
      { title: '取费', field: 'jieSuanFee', width: 150, classType: 1 },
    ],

    // 价差22简易计税/12+22简易计税
    priceDifferenceS22AndS12_22: [
      ...commonColumns,
      { title: '调差工程量', field: 'jieSuanStageDifferenceQuantity', width: 150, classType: 1 },
      { title: '基期价', field: 'jieSuanBasePrice', width: 150, classType: 1 },
      { title: '结算含税单价', field: 'jieSuanPriceMarketTax', width: 150, classType: 1 },
      { title: '结算不含税单价', field: 'priceMarket', width: 150, classType: 1 },
      { title: '结算税率(%)', field: 'taxRate', width: 150, classType: 1 },
      { title: '风险幅度范围(%)', field: 'riskAmplitudeRangeMin', width: 150, classType: 1 },
      { title: '单位价差', field: 'jieSuanPriceDifferenc', width: 150, classType: 1 },
      { title: '价差合计', field: 'jieSuanPriceDifferencSum', width: 150, classType: 1 },
      { title: '取费', field: 'jieSuanFee', width: 150, classType: 1 },
    ]
  };

  // 获取当前菜单键
  const menuKey = String(store.asideMenuCurrentInfo.key);
  const taxMode = String(store.taxMade);
  const is12 = String(store.deType) === '12';
  const is22 = String(store.deType) === '22';
  const is12And22 = true //;['12', '22'].includes(showInfo.deStandardReleaseYear);
  // 根据条件选择列配置
  let tableColumns = [];
  if(String(store.currentTreeInfo.levelType) === '1' ){
    //合同内所有人材机页面来源分析展示
    if (menuKey === '0') {
      tableColumns = columnConfigs.engineeringProject;
    }  // 合同内人工、材料、机械、暂估价调差
    else if (['1', '2', '3', '8'].includes(menuKey)) {
      //12一般计税/12简易计税显示
      if (is12 && (taxMode === '1' || taxMode === '0')) {
        tableColumns = taxMode === '0'
            ? columnConfigs.general12And12Simple.filter(val => val.field !== 'taxRemoval')
            : columnConfigs.general12And12Simple;
      } //22一般计税/12+22一般计税显示
      else if ((is22 && taxMode === '1') || (is22 && taxMode === '1' && is12And22)) {
        tableColumns = columnConfigs.general22And12_22General;
      }//22简易计税/12+22简易计税显示
      else if ((is22 && taxMode === '0') || (is22 && taxMode === '0' && is12And22)) {
        tableColumns = columnConfigs.simple22And12_22Simple;
      }
    }// 合同外所有人材机
    else  if (menuKey === '21') {
      //i. 12一般计税/12简易计税显示
      if ((is12 && (taxMode === '1' || taxMode === '0'))) {
        tableColumns = columnConfigs.general12And12SimpleOriginal;
      }//ii. 22一般计税/12+22一般计税显示/22简易计税/12+22简易计税显示
      else if (
          (is12 && taxMode === '1') ||
          (is22 && taxMode === '1' && is12And22) ||
          (is12 && taxMode === '0') ||
          (is22 && taxMode === '0' && is12And22)
      ) {
        tableColumns = columnConfigs.general22And12_22GeneralAndSimple;
      }
    }// 价差页面
    else if (menuKey === '20') {
      //12一般计税/12简易计税显示
      if ((is12 && (taxMode === '1' || taxMode === '0'))) {
        tableColumns = taxMode === '0'
            ? columnConfigs.priceDifferenceG12AndS22.filter(val => val.field !== 'taxRemoval')
            : columnConfigs.priceDifferenceG12AndS22;
      }//22一般计税/12+22一般计税显示
      else if ((is22 && taxMode === '1') || (is22 && taxMode === '1' && is12And22)) {
        tableColumns = columnConfigs.priceDifferenceG22AndG12_22;
      }//22简易计税/12+22简易计税显示
      else if ((is22 && taxMode === '0') || (is22 && taxMode === '0' && is12And22)) {
        tableColumns = columnConfigs.priceDifferenceS22AndS12_22;
      }
    }
  }else if(String(store.currentTreeInfo.levelType) === '2' ){
    if(store.currentTreeInfo?.originalFlag){
      //合同内所有人材机页面来源分析展示
      if (menuKey === '0') {
        tableColumns = columnConfigs.engineeringProject;
      }  // 合同内人工、材料、机械、暂估价调差
      else if (['1', '2', '3', '8'].includes(menuKey)) {
        //12一般计税/12简易计税显示
        if (is12 && (taxMode === '1' || taxMode === '0')) {
          tableColumns = taxMode === '0'
              ? columnConfigs.general12And12Simple.filter(val => val.field !== 'taxRemoval')
              : columnConfigs.general12And12Simple;
        } //22一般计税/12+22一般计税显示
        else if ((is22 && taxMode === '1') || (is22 && taxMode === '1' && is12And22)) {
          tableColumns = columnConfigs.general22And12_22General;
        }//22简易计税/12+22简易计税显示
        else if ((is22 && taxMode === '0') || (is22 && taxMode === '0' && is12And22)) {
          tableColumns = columnConfigs.simple22And12_22Simple;
        }
      }
    }else{
      //合同外所有人材机、人、材、机、设备、主材、预拌混凝土页面来源分析展示
      console.log(menuKey,'menuKey')
       if (['0','1', '2', '3', '6','5','4'].includes(menuKey)) {
        //i. 12一般计税/12简易计税显示
        if ((is12 && (taxMode === '1' || taxMode === '0'))) {
          tableColumns = columnConfigs.general12And12SimpleOriginal;
        }//ii. 22一般计税/12+22一般计税显示/22简易计税/12+22简易计税显示
        else if (
            (is12 && taxMode === '1') ||
            (is22 && taxMode === '1' && is12And22) ||
            (is12 && taxMode === '0') ||
            (is22 && taxMode === '0' && is12And22)
        ) {
          tableColumns = columnConfigs.general22And12_22GeneralAndSimple;
        }
      }// 价差页面
      else if (menuKey === '20') {
        //12一般计税/12简易计税显示
        if ((is12 && (taxMode === '1' || taxMode === '0'))) {
          tableColumns = taxMode === '0'
              ? columnConfigs.priceDifferenceG12AndS22.filter(val => val.field !== 'taxRemoval')
              : columnConfigs.priceDifferenceG12AndS22;
        }//22一般计税/12+22一般计税显示
        else if ((is22 && taxMode === '1') || (is22 && taxMode === '1' && is12And22)) {
          tableColumns = columnConfigs.priceDifferenceG22AndG12_22;
        }//22简易计税/12+22简易计税显示
        else if ((is22 && taxMode === '0') || (is22 && taxMode === '0' && is12And22)) {
          tableColumns = columnConfigs.priceDifferenceS22AndS12_22;
        }
      }

    }

  }
  return tableColumns || [];
};

export default getTableColumnsJieSuan;