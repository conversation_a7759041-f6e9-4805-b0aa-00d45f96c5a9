import {Entity, PrimaryGeneratedColumn, Column, PrimaryColumn} from "typeorm";
import {PrimaryGeneratedColumnType} from "typeorm/driver/types/ColumnTypes";

@Entity()
export class BaseModel {
    //@PrimaryGeneratedColumn({name:"sequence_nbr"}) //@PrimaryGeneratedColumn的PrimaryGeneratedColumnType中没有字符串所以改为@PrimaryColumn
    @PrimaryColumn({name:"sequence_nbr"})
    public sequenceNbr : string; // 'id'
    @Column({nullable:true,name:"rec_user_code"})
    public recUserCode : string;     // '创建人'
    @Column({nullable:true,
        name:"rec_status",
        default:"A"
    })
    public recStatus : string;     // '数据状态'
    @Column({nullable:true,name:"rec_date"})
    public recDate : string;  // '创建时间'
    @Column({nullable:true})
    public extend1 : string;      // '扩展字段1'
    @Column({nullable:true})
    public extend2 : string;      // '扩展字段2'
    @Column({nullable:true})
    public extend3 : string;      // '扩展字段3'
    @Column({nullable:true})
    public description : string;       // '备注'
    // public levelType :string ; //工程级别类型： 工程项目  单项   单位
    // public bizId : string; // 工程项目树所选择的id：工程项目id/单项id/单位id



    constructor(sequenceNbr?: string, recUserCode?: string, recStatus?: string, recDate?: string, extend1?: string, extend2?: string, extend3?: string, description?: string) {
        this.sequenceNbr = sequenceNbr;
        this.recUserCode = recUserCode;
        this.recStatus = recStatus;
        this.recDate = recDate;
        this.extend1 = extend1;
        this.extend2 = extend2;
        this.extend3 = extend3;
        this.description = description;
    }
}

