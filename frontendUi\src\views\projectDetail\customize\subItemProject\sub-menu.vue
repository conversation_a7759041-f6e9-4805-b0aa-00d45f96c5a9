<template>
  <teleport to="body">
    <div
      class="submenu"
      ref="submenu"
      :style="{
        position: 'fixed',
        zIndex: '9998',
        boxShadow: 'var(--surely-table-popup-shadow)',
        maxHeight: subMenuStyle.maxHeight,
        overflowY: 'auto',
        left: subMenuStyle.left,
        top: subMenuStyle.top,
      }"
      v-if="isMenuShow"
      @click.stop
    >
      <a-menu
        style="width: 180px; font-size: 12px"
        v-for="(item, index) in menuConfig.options"
        mode="vertical"
      >
        <a-sub-menu
          :key="item.code"
          v-if="item.children?.length > 0 && item.visible"
        >
          <template #title>{{ item.name }}</template>
          <a-menu-item
            style="height: 30px; line-height: 30px; font-size: 12px"
            :style="{ display: cItem.visible ? 'block' : 'none' }"
            v-for="(cItem, cIndex) in item.children"
            :key="cItem.code"
            @click="
              emit('contextMenuClickEvent', {
                menu: cItem,
                row: props.currentInfo,
              }),
                props.args.hidePopup()
            "
            :disabled="cItem.disabled"
          >{{ cItem.name }}</a-menu-item>
        </a-sub-menu>
        <a-menu-item
          :key="item.code"
          :disabled="item.disabled"
          @click="
            emit('contextMenuClickEvent', {
              menu: item,
              row: props.currentInfo,
            }),
              hiddenHandler(),
              props.args.hidePopup()
          "
          v-if="!item.children && item.visible"
        >
          {{ item.name }}
        </a-menu-item>
      </a-menu>
    </div>
  </teleport>
</template>
<script setup>
import { reactive, ref, onMounted, nextTick, watch } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { useSubItem } from '@/hooks/useSubItemStable.js';

const projectStore = projectDetailStore();
const props = defineProps({
  args: {
    type: Object,
  },
  deleteStateFn: {
    type: Function,
  },
  pasteRowVisible: {
    type: Function,
  },
  type: {
    type: String,
    default: 'fbfx',
  },
  hangMenuList: {
    type: Array,
  },
  currentInfo: {
    type: Object,
  },
  tableData: {
    type: Object,
  },
  copyData: {
    type: Object,
  },
  selectState: {
    type: Object,
  },
  contractOriginalFlagInsert: {
    type: Function,
  },
  commonJieSuan: {
    type: Function,
  }
});
let isMenuShow = ref(true);
const emit = defineEmits([
  'update:currentInfo',
  'contextMenuClickEvent',
  'handleNote',
  'handleMainList',
]);
let submenu = ref();
// 定位右键元素
let menuEl = ref();

let subMenuStyle = reactive({
  left: 'auto',
  top: 'auto',
  maxHeight: '85vh',
});

const SAGE_DISTANCE = 185; // 头部加底部安全距离
const SAGE_DISTANCE_BOTTOM = 33; // 底部安全距离
const hiddenHandler = () => {
  isMenuShow.value = false;
  document.removeEventListener('click', hiddenHandler);
};
watch(
  () => projectStore.asideMenuCurrentInfo?.sequenceNbr,
  (val, old) => {
    hiddenHandler();
  }
);
onMounted(() => {
  document.addEventListener('click', hiddenHandler);
  subMenuStyle.left = props.args.event.x + 'px';
  // 获取目标元素
  let tableEl = document.querySelector('.surely-table-body-viewport-container');
  // 添加滚动事件监听器
  tableEl.addEventListener('scroll', function () {
    // 输出滚动位置
    if (submenu.value) {
      // updatePosition();
    }
  });

  function updatePosition() {
    const rect = submenu.value.getBoundingClientRect();
    const viewportBottom = window.innerHeight;
    const elementHeight = rect.height;
    const y = props.args.event.y;
    const yViewportDistance = viewportBottom - y; // 鼠标y点距离可视底部距离

    const viewportHeight = viewportBottom - elementHeight - SAGE_DISTANCE; // 除去不可占用的元素高度和el高度之外的可用高度
    if (yViewportDistance >= elementHeight) {
      subMenuStyle.top = y + 'px';
    } else if (yViewportDistance < elementHeight && viewportHeight > 0) {
      // y轴一下的可视区域小于菜单元素高度 && 页面整体可用高度大于菜单元素时，将菜单top往上移动，减去底部33
      subMenuStyle.top =
        y - (elementHeight - yViewportDistance + SAGE_DISTANCE_BOTTOM) + 'px';
    } else {
      subMenuStyle.maxHeight = viewportHeight + 'px';
    }
  }
  nextTick(() => {
    updatePosition();
  });
});
let { needAddQDandFB } = useSubItem({
  pageType: props.type,
});
const menuConfig = reactive({
  options: [
    {
      code: 'add',
      name: '插入',
      visible: true,
      disabled: false,
      children: [
        {
          code: 0,
          name: `添加${props.type === 'fbfx' ? '分部' : '标题'}`,
          kind: '01',
          visible: true,
          disabled: true,
        },
        {
          code: 1,
          name: `添加${props.type === 'fbfx' ? '子分部' : '子项'}`,
          kind: '02',
          visible: true,
          disabled: true,
        },
        {
          code: 2,
          name: '添加清单',
          kind: '03',
          visible: true,
          disabled: true,
        },
        {
          code: 3,
          name: '添加子目',
          kind: '04',
          visible: true,
          disabled: true,
        },
      ],
    },
    {
      code: 'zcsbAdd',
      name: '插入',
      visible: false,
      disabled: true,
    },
    {
      code: 'supplement',
      name: '补充',
      visible: true,
      disabled: false,
      children: [
        {
          code: 'supplement-qd',
          type: 2,
          name: '补充清单',
          kind: '03',
          visible: true,
          disabled: true,
        },
        {
          code: 'supplement-de',
          type: 3,
          name: '补充子目',
          kind: '04',
          visible: true,
          disabled: true,
        },
        {
          code: 'supplement-rcj',
          type: 3,
          name: '补充人材机',
          kind: '05',
          visible: true,
          disabled: true,
        },
      ],
    },
    {
      code: 'cut',
      name: '剪切',
      visible: true,
      disabled: false,
    },
    {
      code: 'copy',
      name: '复制',
      visible: true,
      disabled: false,
    },
    {
      code: 'copyCell',
      name: '复制单元格内容',
      visible: true,
      disabled: false,
    },
    {
      code: 'paste',
      name: '粘贴',
      visible: true,
      disabled: false,
    },
    {
      code: 'delete',
      name: '删除',
      visible: true,
      disabled: true,
    },
    {
      code: 'delBlan',
      name: '删除空白行',
      visible: true,
      disabled: false,
    },
    {
      code: 'lock',
      name: '清单锁定',
      visible: true,
      disabled: false,
    },
    {
      code: 'lockPrice',
      name: '锁定综合单价',
      visible: true,
      disabled: false,
    },
    {
      code: 'tempDelete',
      name: '临时删除',
      visible: true,
      disabled: false,
    },
    {
      code: 'MainList',
      name: '主要清单',
      visible: true,
      disabled: false,
      children: [
        {
          code: 'set-list',
          name: '设置主要清单',
          type: 1,
          visible: true,
          disabled: false,
        },
        {
          code: 'del-current',
          name: '取消当前行',
          type: 2,
          visible: true,
          disabled: false,
        },
        {
          code: 'del-all',
          name: '取消所有清单',
          type: 3,
          visible: true,
          disabled: false,
        },
      ],
    },
    {
      code: 'noteList',
      name: '批注',
      visible: true,
      disabled: false,
      children: [
        {
          code: 'add-note',
          name: '插入批注',
          type: 1,
          visible: true,
          disabled: false,
        },
        {
          code: 'edit-note',
          name: '编辑批注',
          type: 2,
          visible: true,
          disabled: false,
        },
        {
          code: 'del-note',
          name: '删除批注',
          type: 3,
          visible: true,
          disabled: false,
        },
        {
          code: 'show-note',
          name: '显示批注',
          type: 4,
          visible: true,
          disabled: false,
        },
        {
          code: 'hide-note',
          name: '隐藏批注',
          type: 5,
          visible: true,
          disabled: false,
        },
        {
          code: 'del-all-note',
          name: '删除所有批注',
          type: 6,
          visible: true,
          disabled: false,
        },
      ],
    },
    {
      code: 'batchDelete',
      name: '批量删除',
      visible: true,
      disabled: false,
      children: [
        {
          code: 'batchDelete-child1',
          name: '批量删除所有临时删除项',
          type: 1,
          visible: true,
          disabled: false,
        },
        {
          code: 'batchDelete-child2',
          name: '批量删除所有工程量为0项',
          type: 2,
          visible: true,
          disabled: false,
        },
        {
          code: 'batchDelete-child3',
          name: '批量删除子目',
          type: 3,
          visible: true,
          disabled: false,
        },
      ],
    },
    ...props.hangMenuList,
    {
      code: 'pageColumnSetting',
      name: '页面显示列设置',
      visible: true,
      disabled: false,
    },
  ],
});
/**
 * 是否费用定额
 * 12的垂运，22的装饰超高、垂运不属于费用定额
 * isCostDe是否是费用定额 0不是  1 安文费 2 总价措施 3 超高 4 垂运 5 安装费'
 * return 返回 false 是普通定额； true  费用定额
 */
const isNotCostDe = (currentInfo = {}) => {
  const { kind, isCostDe } = currentInfo;
  return (
    kind === '04' &&
    (!isCostDe ||
      isCostDe === 4 ||
      (projectStore.deStandardReleaseYear === '22' &&
        currentInfo.isCostDe === 3))
  );
};
const jieSuanFilter = (item, row) => {
  let originalFlag = projectStore.currentTreeInfo?.originalFlag;
  // 合同内过滤右键菜单
  let originalArr = ['tempDelete','MainList','noteList','batchDelete','QDGenerateZC','DEGenerateZC','syncToDE','QDGenerateSB','DEGenerateSB','delBlan','lock','lockPrice','MainList']
  // 合同外过滤右键菜单
  let nooOiginalArr = ['tempDelete','MainList','QDGenerateZC','DEGenerateZC','syncToDE','QDGenerateSB','DEGenerateSB']
    
  if(originalFlag ){
    if(item.code === 'MainList'){
      setTimeout(() => {
        item.visible = false;
      }, 1);
    }
    if(originalArr.includes(item.code)){
      item.visible = false;
    }
    if(props.type !== 'fbfx'){
      if (item.code === 'pageColumnSetting') {
        item.disabled = false;
      } else {
        item.disabled = true;
      }
      if (item.children && Array.isArray(item.children)) {
          setDisabledRecursive(item.children);
      }
      if(['add','supplement'].includes(item.code)){
        setTimeout(() => {
          item.disabled = true;
        }, 1);
      }
    }
  }
  if(!originalFlag){
    if(nooOiginalArr.includes(item.code)){
      item.visible = false;
    }
  }

  
  function setDisabledRecursive(arr) {
    for (const item of arr) {
      // 设置当前对象的 disabled 属性
      if (item.code === 'pageColumnSetting') {
        item.disabled = false;
      } else {
        item.disabled = true;
      }

      // 递归处理子对象
      if (item.children && Array.isArray(item.children)) {
        setDisabledRecursive(item.children);
      }
    }
  }
}
const visibleMethod = async () => {
  let options = menuConfig.options;
  let row = props.args.record;
  if (projectStore.type === 'jieSuan') {
    await props.commonJieSuan.originalFlagMenuConfigHandler(menuConfig);
  }

  Array.from(document.querySelectorAll('.surely-table-row')).map(a => {
    if (a.dataset.rowKey === row.sequenceNbr) {
      menuEl.value = a;
    }
  });
  if (!row) return;
  emit('update:currentInfo', row);
  projectStore.SET_SUB_CURRENT_INFO(row);
  props.deleteStateFn();
  console.log('row', row);
  options.forEach(async (item) => {
    if (projectStore.type === 'jieSuan' && props.type === 'fbfx' && projectStore.currentTreeInfo?.originalFlag) {
      await props.contractOriginalFlagInsert(item, props.currentInfo);
    }
    item.disabled = false;
    item.children?.forEach(childItem => {
      childItem.disabled = false;
    });
    if (item.code === 'add') item.visible = ![94, 95].includes(row.kind);
    if (item.code === 'zcsbAdd') item.visible = [94, 95].includes(row.kind);
    if (
      (!props.copyData || props.copyData?.length === 0) &&
      item.code === 'paste'
    ) {
      item.disabled = true;
    }
    if (props.copyData?.length > 0 && item.code === 'paste') {
      item.disabled = false;
      if (projectStore.type === 'jieSuan' && props.currentInfo.originalFlag) {
        item.disabled = true
      }
      //粘贴代码中已经有此逻辑判断-注释
      // try {
      //   await frameSelectRef.value.frameSelectJs.isPasteBranch(
      //     row,
      //     props.copyData
      //   );
      //   item.disabled = false;
      // } catch (error) {
      //   item.disabled = true;
      // }
    }
    if (item.code === 'cut') {
      console.log(props.selectState, 333333);
      // if( props.copyData?.length ||
      //     ( !props.copyData &&
      //     ( row.kind === '03' || (row.kind === '04' && isNotCostDe(row)) ))   ) {
      //   item.disabled = false;
      // }
      //控制剪切时和快捷键时的逻辑保持一致
      const copyDataSet = new Set(props.selectState.selectedRowKeys);
      item.disabled = !props.tableData.some(item => {
        if (copyDataSet.has(item.sequenceNbr)) {
          return (
            item.kind === '03' || (item.kind === '04' && isNotCostDe(item))
          );
        }
        return false;
      });
    }

    if (item.code === 'delete') {
      if (props.type === 'fbfx') {
        if (
          (row.optionMenu.includes(4) || row.optionMenu.includes(5)) &&
          !row.isLocked
        ) {
          item.disabled = false;
        } else {
          item.disabled = true;
        }
      } else {
        if (
          (row.optionMenu.includes(4) || row.optionMenu.includes(5)) &&
          ((row.kind === '03' &&
            row.hasOwnProperty('zjcsClassCode') &&
            row.zjcsClassCode !== null &&
            row.zjcsClassCode !== undefined &&
            Number(row.zjcsClassCode) === 0) ||
            row.constructionMeasureType === 2)
        ) {
          item.disabled = true;
        } else {
          item.disabled = row.isLocked;
        }
        if (
          (row.optionMenu.includes(4) || row.optionMenu.includes(5)) &&
          row.name !== '安全生产、文明施工费' &&
          !row.isLocked
        ) {
          item.disabled = false;
        } else {
          item.disabled = true;
        }
      }
      if (projectStore.type === 'jieSuan' && props.currentInfo.originalFlag) {
        item.disabled = true
      }
    }
    if (item.code === 'lock') {
      if (projectStore.standardGroupOpenInfo.isOpen) {
        item.disabled = true;
      } else {
        if (row.kind === '03') {
          item.disabled = false;
        } else {
          item.disabled = true;
        }
        if (row.isLocked) {
          item.name = '清单解锁';
        } else {
          item.name = '清单锁定';
        }
      }
    } else if (item.code === 'lockPrice') {
      if (['01', '02', '03'].includes(row.kind) && !row.tempDeleteFlag) {
        item.disabled = false;
      } else {
        item.disabled = true;
      }
      if (row.lockPriceFlag) {
        item.name = '取消锁定综合单价';
      } else {
        item.name = '锁定综合单价';
      }
    } else if (item.code === 'tempDelete') {
      let parentInfo = props.tableData.filter(
        x => x.sequenceNbr === row.parentId
      )[0];
      if (props.type === 'fbfx') {
        if (
          (row.kind === '03' && !row.isLocked) ||
          (row.kind === '04' && !parentInfo.tempDeleteFlag)
        ) {
          item.disabled = false;
        } else {
          item.disabled = true;
        }
      } else {
        if (
          ((row.kind === '03' && !row.isLocked) ||
            (row.kind === '04' && !parentInfo.tempDeleteFlag)) &&
          row.name !== '安全生产、文明施工费'
        ) {
          item.disabled = false;
        } else {
          item.disabled = true;
        }
      }

      if (row.tempDeleteFlag) {
        item.name = '取消临时删除';
      } else {
        item.name = '临时删除';
      }
    }
    if (
      item.children &&
      !['batchDelete', 'noteList', 'MainList'].includes(item.code)
    ) {
      item.disabled = false;
      item.children.forEach(childItem => {
        childItem.disabled = true;
        row.optionMenu.forEach(child => {
          if (child === childItem.code) {
            childItem.disabled = false;
          }
        });
      });
    }
    if (item.code === 'supplement') {
      item.children.forEach(childItem => {
        childItem.disabled = true;
        row.optionMenu.forEach(child => {
          if (
            child === childItem.type ||
            ([94, 95].includes(row.kind) && childItem.name === '补充人材机')
          ) {
            childItem.disabled = false;
          }
        });
      });
    }
    if (item.code === 'add') needAddQDandFB(item, row);
    if (projectStore.type === 'jieSuan') {
        jieSuanFilter(item, row);
    }
    if (projectStore.type === 'jieSuan' && props.type === 'fbfx' && projectStore.currentTreeInfo?.originalFlag) {
      await props.contractOriginalFlagInsert(item, props.currentInfo);
    }
    emit('handleNote', item, row);
    emit('handleMainList', item, row);
    emit('hangMenuDisabledHandler', item, row);
  });
  console.log(options);
};
visibleMethod();
</script>
<style lang="scss" scoped>
.submenu {
}
::v-deep(.ant-menu-item) {
  font-size: 11px;
  height: 24px;
  line-height: 24px;
}
::v-deep(.ant-menu-submenu-title) {
  font-size: 11px;
  height: 24px !important;
  line-height: 24px !important;
}
.fixed-bottom {
  bottom: 33px;
  // top: 10px;
}
</style>
