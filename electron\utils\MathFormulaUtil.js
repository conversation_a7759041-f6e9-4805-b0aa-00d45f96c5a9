
class MathFormulaUtil {

    constructor() {
        this.MULTIPLY_OPRATOR = 'multiply';
        this.DIVIDE_OPRATOR = 'divide';
    }

    /**
     * 处理公式除以一个数
     * @param formula
     * @param k
     */
    processDivide(formula, k){
        return this.processFormula(formula, k, this.DIVIDE_OPRATOR);
    }

    /**
     * 处理公式乘以一个数
     * @param formula
     * @param k
     */
    processMultiply(formula, k){
        return this.processFormula(formula, k, this.MULTIPLY_OPRATOR);
    }

    /**
     * 处理公式乘以/除以一个数
     * @param formula
     * @param k
     * @param operation
     * @return {*}
     */
    processFormula(formula, k, operation) {
        // 尝试合并操作，若成功返回新公式；否则返回null
        let processed = this.tryMergeOperation(formula, k, operation);
        if (processed !== null) {
            return this.removeRedundantOuterParentheses(this.removeEmptyChars(processed));
        }
        // 无法合并时，在尾部新增操作
        processed = this.maybeAddParentheses(formula);

        processed = operation === this.MULTIPLY_OPRATOR
            ? `${processed} * ${k}`
            : `${processed} / ${k}`;

        return this.removeRedundantOuterParentheses(processed);
    }

    /**
     * 删除字符串中所有空格
     * @param str
     * @return {*}
     */
    removeEmptyChars(str) {
        return str.replace(/\s+/g, '');
    }



// 核心函数：移除外层冗余括号
    removeRedundantOuterParentheses(expr) {
        // 移除非字符串输入
        if (typeof expr !== 'string') return expr;

        // 空字符串或长度小于2，无法优化
        if (expr.length < 2) return expr;

        // 检查首尾字符是否为括号
        if (expr[0] !== '(' || expr[expr.length - 1] !== ')') {
            return expr;
        }

        // 检查括号是否匹配
        let count = 0;
        let canRemove = true;

        // 遍历整个表达式（除了最后一个字符）
        for (let i = 0; i < expr.length - 1; i++) {
            if (expr[i] === '(') {
                count++;
            } else if (expr[i] === ')') {
                count--;
                // 在到达最后一个字符前括号已关闭，不能移除外层括号
                if (count === 0 && i < expr.length - 1) {
                    canRemove = false;
                    break;
                }
            }
        }

        // 检查最后一个字符
        if (expr[expr.length - 1] === ')') {
            count--;
        }

        // 如果括号不匹配，返回原始表达式
        if (count !== 0) {
            return expr;
        }

        // 如果可以安全移除外层括号
        if (canRemove) {
            return expr.substring(1, expr.length - 1);
        }

        return expr;
    }


    /**
     * 尝试合并运算符
     * @param formula
     * @param k
     * @param operation
     * @return {string|null|*}
     */
    tryMergeOperation(formula, k, operation) {
        // 移除外层冗余括号
        let current = this.removeOuterParentheses(formula);

        // 查找最外层运算符位置（从右向左扫描）
        const opIndex = this.findOperatorIndex(current);
        if (opIndex === -1) {
            // 无运算符，直接检查数字是否匹配
            const num = this.parseNumber(current);
            if (operation === this.DIVIDE_OPRATOR && num === k) return '1'; // k/k=1
            return null;
        }

        const op = current[opIndex];
        const left = this.removeOuterParentheses(current.substring(0, opIndex)).trim();
        const right = this.removeOuterParentheses(current.substring(opIndex + 1)).trim();

        // 处理乘法操作（除以k时）
        if (operation === this.DIVIDE_OPRATOR && op === '*') {
            if (this.isNumberEqual(left, k)) return right; // 左操作数为k，移除乘法
            if (this.isNumberEqual(right, k)) return left; // 右操作数为k，移除乘法
        }
        // 处理除法操作（乘以k时）
        if (operation === this.MULTIPLY_OPRATOR && op === '/') {
            if (this.isNumberEqual(right, k)) return left; // 除数为k，移除除法
        }

        // 递归处理子表达式
        const leftProcessed = this.tryMergeOperation(left, k, operation);
        const rightProcessed = this.tryMergeOperation(right, k, operation);

        // 根据递归结果重构表达式
        if (leftProcessed !== null || rightProcessed !== null) {
            const newLeft = leftProcessed ?? left;
            const newRight = rightProcessed ?? right;
            return `${newLeft}${op}${newRight}`;
        }

        return null;
    }

// 辅助函数：移除外层冗余括号
    removeOuterParentheses(expr) {
        let start = 0, end = expr.length - 1;
        while (start < end && expr[start] === '(' && expr[end] === ')') {
            let count = 0;
            for (let i = start; i <= end; i++) {
                if (expr[i] === '(') count++;
                else if (expr[i] === ')') {
                    if (--count === 0 && i !== end) return expr; // 括号未完整包围
                }
            }
            start++;
            end--;
        }
        return expr.substring(start, end + 1);
    }

// 辅助函数：查找最外层运算符位置（从右向左）
    findOperatorIndex(expr) {
        let depth = 0;
        // 优先级：先找加减，再找乘除
        for (let i = expr.length - 1; i >= 0; i--) {
            const c = expr[i];
            if (c === ')') depth++;
            else if (c === '(') depth--;
            else if (depth === 0) {
                if (c === '+' || c === '-') return i; // 优先匹配加减
            }
        }
        // 未找到加减，查找乘除
        depth = 0;
        for (let i = expr.length - 1; i >= 0; i--) {
            const c = expr[i];
            if (c === ')') depth++;
            else if (c === '(') depth--;
            else if (depth === 0) {
                if (c === '*' || c === '/') return i;
            }
        }
        return -1; // 无运算符
    }

// 辅助函数：解析字符串为数字（失败返回NaN）
    parseNumber(str) {
        const num = parseFloat(str);
        return isNaN(num) ? NaN : num;
    }

// 辅助函数：判断字符串是否等于数字k
    isNumberEqual(str, k) {
        const num = this.parseNumber(str);
        return !isNaN(num) && num === k;
    }



    maybeAddParentheses(expr) {
        // 处理空字符串
        if (!expr) return expr;

        // 移除首尾空格
        expr = expr.trim();

        // 检查表达式是否已被括号包裹
        if (this.isWrapped(expr)) {
            return expr;
        }

        // 检查表达式中是否存在需要括号的运算符（在顶层作用域的加减法）
        if (this.hasTopLevelOperator(expr)) {
            return `(${expr})`;
        }

        return expr;
    }

    // 检查表达式是否被完整的括号包裹
    isWrapped(expr) {
        if (expr[0] !== '(' || expr[expr.length - 1] !== ')') {
            return false;
        }

        let count = 0;
        for (let i = 0; i < expr.length; i++) {
            if (expr[i] === '(') count++;
            else if (expr[i] === ')') count--;

            // 遇到不匹配的括号或提前闭合的情况
            if (count < 0) return false;
            // 在末尾前括号已闭合（说明未完整包裹）
            if (count === 0 && i < expr.length - 1) return false;
        }

        return count === 0;
    }

    // 检查是否存在需要括号的运算符（顶层加减法）
    hasTopLevelOperator(expr) {
        let count = 0;  // 括号嵌套计数器

        for (let i = 0; i < expr.length; i++) {
            const char = expr[i];

            // 更新括号计数器
            if (char === '(') count++;
            else if (char === ')') count--;

            // 忽略括号内的内容
            if (count !== 0) continue;

            // 检查加减号（需要处理负号特殊情况）
            if (char === '+' || char === '-') {
                // 跳过开头的负号（如 "-5"）
                if (i === 0) continue;

                // 检查前一个非空格字符（判断是负号还是减号）
                let j = i - 1;
                while (j >= 0 && expr[j] === ' ') j--; // 跳过空格

                // 确定前一个有效字符的类型
                if (j >= 0) {
                    const prevChar = expr[j];
                    // 如果前一个字符是运算符，当前符号是负号（如 "1*-2"）
                    if (prevChar === '*' || prevChar === '/' || prevChar === '+' || prevChar === '-') {
                        continue; // 负号不需要触发括号
                    }
                }

                // 找到需要括号的运算符
                return true;
            }
        }

        return false;
    }
}

module.exports = {
    MathFormulaUtil: new MathFormulaUtil()
};
