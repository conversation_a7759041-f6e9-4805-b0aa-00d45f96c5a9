<template>
  <common-modal
    className="dialog-comm resizeClass"
    title="单期/多期调差设置"
    width="700"
    height="620"
    v-model:modelValue="props.visible"
    :mask="false"
    @close="cancel"
  >
    <div class="range-content">
      <div class="adjustment-select">
        <div class="name">
          <icon-font
            class="icon-font"
            type="icon-tiaochaxuanzeshezhi"
          ></icon-font
          >调差选择设置
        </div>
        <div class="radio-list">
          <a-radio-group v-model:value="codeType">
            <p>
              <a-radio :style="radioStyle" :value="1">
                <span class="radio-name">单期调差</span>
                <span class="desc"
                  >每个计量分期进行一次调差，每期结算单价分别输入，最终计算总价差</span
                >
              </a-radio>
            </p>
            <p>
              <a-radio :style="radioStyle" :value="2"
                ><span class="radio-name">多期（季度、年度）调差</span
                ><span class="desc"
                  >如季度、半年、年度等几期进行一次调差，结算单价在载价中进行量价加权，最终计算总价差</span
                ></a-radio
              >
            </p>
          </a-radio-group>
        </div>
      </div>
      <div class="setting-content" v-if="codeType === 2">
        <div v-for="(item, index) in adjustmentList" :key="index">
          <span>第{{ index + 1 }}次调差：</span>
          第<vxe-select
            v-model="item[0]"
            :transfer="true"
            @change="selectChange(item, 0)"
          >
            <vxe-option
              v-for="item of projectStore.stageCount"
              :key="item"
              :value="item"
              :label="item"
            ></vxe-option> </vxe-select
          >期 <span class="to">至</span>第<vxe-select
            v-model="item[1]"
            :transfer="true"
            @change="selectChange(item, 1)"
          >
            <vxe-option
              v-for="item of projectStore.stageCount"
              :key="item"
              :value="item"
              :label="item"
            ></vxe-option> </vxe-select
          >期
          <icon-font
            v-if="index !== 0"
            @click="deleteItem(index)"
            class="icon-font"
            type="icon-shanchu"
          ></icon-font>
          <icon-font
            v-if="index === adjustmentList.length - 1"
            @click="addItem"
            class="icon-font"
            type="icon-zengjia"
          ></icon-font>
        </div>
      </div>
      <div class="tips">
        <p class="name">
          <icon-font
            class="icon-font"
            type="icon-jiachajisuanshuoming"
          ></icon-font
          >价差计算说明
        </p>
        <div class="sing-item">
          <span class="title">单期调整</span>
          <span class="desc">价差= ∑（分期发生量 x 当期单位价差)</span>
        </div>
        <div class="sing-item">
          <span class="title">多期调差</span>
          <div>
            <span>价差=结算工程量 x 单位价差</span>
            <span>或价差=∑（几期发生量之和 x 几期加权后计算的单位价差)</span>
          </div>
        </div>
      </div>
    </div>
    <div class="footer-btn-list">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="rcjPeriodsSet">确定</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import { reactive, ref, watch } from 'vue';
import api from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
import IconFonts from '@/components/global/infoModal/index.vue';

const props = defineProps(['visible']);
const emits = defineEmits(['update:visible', 'updateData']);
const projectStore = projectDetailStore();

let codeType = ref(); // 单期多期设置
const radioStyle = reactive({
  display: 'flex',
  height: '30px',
  lineHeight: '30px',
});
let adjustmentList = ref([[]]);

watch(
  () => props.visible,
  () => {
    if (props.visible) {
      codeType.value =
        projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.batchType;
      if (
        projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.batchType === 2
      ) {
        adjustmentList.value = [];
        projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.frequencyList.forEach(
          item => {
            adjustmentList.value.push(item);
          }
        );
      } else {
        adjustmentList.value = [[]];
      }
    }
  }
);
const cancel = () => {
  emits('update:visible', false);
};

const rcjPeriodsSet = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: codeType.value,
    kind: Number(projectStore.asideMenuCurrentInfo?.key),
    values:
      codeType.value === 2
        ? JSON.parse(JSON.stringify(adjustmentList.value))
        : [],
  };
  console.log('apiData', apiData);
  api.rcjPeriodsSet(apiData).then(res => {
    console.log('44444444', res);
    if (res.status === 200 && res.result) {
      message.success('调差设置成功');
      cancel();
      emits('updateData');
    }
  });
};

const deleteItem = index => {
  adjustmentList.value.splice(index, 1);
};

const addItem = () => {
  adjustmentList.value.push([]);
};

const selectChange = (item, index) => {
  if (item[1] < item[0]) {
    message.error('开始分期不能大于结束分期');
    item[index] = '';
  }
};
</script>

<style lang="scss" scoped>
.range-content {
  .icon-font {
    font-size: 16px;
    margin-right: 6px;
  }
  .adjustment-select {
    border: 1px solid #d9d9d9;
    padding: 11px 15px;
    margin-bottom: 15px;
    .name {
      font-size: 12px;
      color: #287cfa;
    }
    p {
      height: 65px;
    }
    .radio-name {
      font-size: 14px;
      color: #2a2a2a;
      font-weight: bold;
    }
    .radio-list {
      .desc {
        display: block;
      }
    }
  }
  .setting-content {
    font-size: 14px;
    color: #2a2a2a;
    border: 1px solid #d9d9d9;
    padding: 11px 15px;
    margin-bottom: 15px;
    div {
      margin-bottom: 10px;
    }
    .to {
      margin: 0 5px;
    }
    .ant-input {
      width: 100px;
      margin: 0 5px;
    }
  }
  .tips {
    border: 1px solid #d9d9d9;
    padding: 11px 15px;
    margin-bottom: 30px;
    color: #2a2a2a;
    .name {
      font-size: 12px;
      color: #287cfa;
    }
    .sing-item {
      display: flex;
      .title {
        font-size: 14px;
        color: #2a2a2a;
        font-weight: bold;
        margin-right: 25px;
      }
      div {
        span {
          display: block;
        }
      }
    }
    .sing-item:nth-of-type(2) {
      margin-top: 10px;
    }
  }
}
</style>
