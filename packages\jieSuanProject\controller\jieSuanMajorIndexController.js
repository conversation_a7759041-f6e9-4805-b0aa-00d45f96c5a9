const {Controller} = require("../../../core");
const {ResponseData} = require("../../../electron/utils/ResponseData");


/**
 * 结算指标
 */
class JieSuanMajorIndexController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 查看指标查看范围
     * @return
     */
    async queryIndexViewScopeColl(args) {
        let {sequenceNbr}=args;
        let result= await this.service.jieSuanProject.jieSuanMajorIndexService.queryIndexViewScope(args);
        return  ResponseData.success(result);
    }


    /**
     * 设置指标查看范围
     * @param data 平铺的修改后的整个结构
     * @return
     */
    async setIndexViewScopeColl(args) {
        let {data,constructId}=args;
        await this.service.jieSuanProject.jieSuanMajorIndexService.setIndexViewScope(args);
        return  ResponseData.success(true);
    }



    /**
     *  获取指标对应的分析方式
     * @param  type  1 主要经济指标  2 主要工程量指标  3 主要工料指标
     * @return
     */
    async getIndexOfFenXiColl(args) {
        let{type,constructorId}=args;
        let indexOfFenXi = await this.service.jieSuanProject.jieSuanMajorIndexService.getIndexOfFenXi(args);
        return  ResponseData.success(indexOfFenXi);
    }


    /**
     *  修改指标对应的分析方式
     * @param  type  1 主要经济指标  2 主要工程量指标  3 主要工料指标
     * @param  method  1 按工程分析  2 按专业分析  3 按费用专业
     * @return
     */
    async updateIndexOfFenXiColl(args) {
        let{constructorId,type,method}=args;
        await this.service.jieSuanProject.jieSuanMajorIndexService.updateIndexOfFenXi(args);
        return  ResponseData.success(true);
    }


    /**
     *  指标专业切换
     * @return
     */
    async updateIndexMajorColl(args) {
        let {constructId, singleId, unitId, majorName} =args;
        await this.service.jieSuanProject.jieSuanMajorIndexService.updateIndexMajorName(args);
        return  ResponseData.success(true);
    }



    /**
     *  指标专业切换下拉列表
     * @return
     */
    async unitMajorDropdownListColl() {
        let list = await this.service.jieSuanProject.jieSuanMajorIndexService.unitMajorDropdownList();
        return  ResponseData.success(list);
    }



    /*===============================指标匹配================================*/
    /**
     *  指标匹配
     * @param matchType  匹配类型   1 分部分项  2 措施项目  3 主要工料指标
     * @returns {Promise<ResponseData>}
     */
    async queryUnitIndexMatchColl(args){
        let {constructId, singleId, unitId, matchType} =args;
        let data = await this.service.jieSuanProject.jieSuanMajorIndexService.queryUnitIndexMatch(args);
        return  ResponseData.success(data);
    }

    /**
     * 初始化所有指标匹配数据
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async initAllUnitIndexMatchColl(args){
        let {constructId} =args;
        await this.service.jieSuanProject.jieSuanMajorIndexService.initAllUnitIndexMatch(args);
        return  ResponseData.success(true);
    }



    /**
     * 分部分项指标下拉列表
     * @returns {Promise<ResponseData>}
     */
    async querFbfxIndicatorListColl(){
        let data = await this.service.jieSuanProject.jieSuanMajorIndexService.querFbfxIndicatorList();
        return  ResponseData.success(data);
    }

    /**
     * 工程量指标下拉列表
     * @returns {Promise<ResponseData>}
     */
    async querGclIndicatorListColl(){
        let data = await this.service.jieSuanProject.jieSuanMajorIndexService.querGclIndicatorList();
        return  ResponseData.success(data);
    }

    /**
     * 措施项目指标下拉列表
     * @returns {Promise<ResponseData>}
     */
    async querCsxmIndicatorListColl(){
        let data = await this.service.jieSuanProject.jieSuanMajorIndexService.querCsxmIndicatorList();
        return  ResponseData.success(data);
    }


    /**
     * 工料指标下拉列表
     * @returns {Promise<ResponseData>}
     */
    async querGongLiaoIndicatorListColl(){
        let data = await this.service.jieSuanProject.jieSuanMajorIndexService.querGongLiaoIndicatorList();
        return  ResponseData.success(data);
    }


    /**
     * 开启匹配弹框页面的时候缓存所有单位数据
     */
    async indexCachAllUnitDataColl(args){
        await this.service.jieSuanProject.jieSuanMajorIndexService.indexCachAllUnitData(args);
        return  ResponseData.success(true);
    }

    /**
     * 指标数据恢复
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async restIndexCachAllUnitDataColl(args){
        await this.service.jieSuanProject.jieSuanMajorIndexService.restIndexCachAllUnitData(args);
        return  ResponseData.success(true);
    }

    /**
     * 应用修改
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async useUpdateColl(args){
        await this.service.jieSuanProject.jieSuanMajorIndexService.useUpdate(args);
        return  ResponseData.success(true);
    }



    /*===============================指标分析================================*/
    /**
     *  指标分析
     * @param fenXiType  指标分析类型   1 主要经济指标  2 主要工程量指标  3 主要工料指标
     * @param fenXiMethod  指标分析类型   1 按工程分析  2 按专业分析  3 按费用专业
     * @returns {Promise<ResponseData>}
     *
     *
     */
    async queryUnitIndexFenXiColl(args){
        let {constructId, singleId, unitId, fenXiType,fenXiMethod} =args;
        let data = await this.service.jieSuanProject.jieSuanMajorIndexService.queryUnitIndexFenXi(args);
        return  ResponseData.success(data);
    }


    /**
     * 查询 指标: 关键信息-工程信息
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async queryIndexProjectOverview(args){
        const res = await this.service.jieSuanProject.jieSuanMajorIndexService.queryIndexProjectOverview(args);
        return ResponseData.success(res);
    }

    /**
     * 保存 指标: 关键信息-工程信息
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async saveIndexProjectOverview(args){
        const res = await this.service.jieSuanProject.jieSuanMajorIndexService.saveIndexProjectOverview(args);
        return ResponseData.success(res);
    }


    /**
     * 查询 指标: 底部工程项目、单项口径值
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async queryIndexCaliber(args){
        const res = await this.service.jieSuanProject.jieSuanMajorIndexService.queryIndexCaliber(args);
        return ResponseData.success(res);
    }

    /**
     * 保存 指标: 底部工程项目、单项口径值
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async saveIndexCaliber(args){
        const res = await this.service.jieSuanProject.jieSuanMajorIndexService.saveIndexCaliber(args);
        return ResponseData.success(res);
    }

    /**
     * 保存 参考指标设置
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async saveReferIndex(args){
        const res = await this.service.jieSuanProject.jieSuanMajorIndexService.saveReferIndex(args);
        return ResponseData.success(res);
    }

    /**
     * 查询 参考指标设置
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async queryReferIndex(args){
        const res = await this.service.jieSuanProject.jieSuanMajorIndexService.queryReferIndex(args);
        return ResponseData.success(res);
    }


    /**
     * 关键信息: 工程类型下拉
     * @returns {Promise<ResponseData>}
     */
    async queryGCLXListColl(){
        let data = await this.service.jieSuanProject.jieSuanMajorIndexService.queryGCLXListColl();
        return  ResponseData.success(data);
    }
    /**
     * 关键信息: 建筑分类下拉
     * @returns {Promise<ResponseData>}
     */
    async queryJZFLListColl(){
        let data = await this.service.jieSuanProject.jieSuanMajorIndexService.queryJZFLListColl();
        return  ResponseData.success(data);
    }
    /**
     * 关键信息: 计算口径下拉
     * @returns {Promise<ResponseData>}
     */
    async queryJSKJListColl(){
        let data = await this.service.jieSuanProject.jieSuanMajorIndexService.queryJSKJListColl();
        return  ResponseData.success(data);
    }
    /**
     * 关键信息: 造价类别下拉
     * @returns {Promise<ResponseData>}
     */
    async queryZJLBListColl(){
        let data = await this.service.jieSuanProject.jieSuanMajorIndexService.queryZJLBListColl();
        return  ResponseData.success(data);
    }
    /**
     * 关键信息: 结构类型下拉
     * @returns {Promise<ResponseData>}
     */
    async queryJGLXListColl(){
        let data = await this.service.jieSuanProject.jieSuanMajorIndexService.queryJGLXListColl();
        return  ResponseData.success(data);
    }
    /**
     * 关键信息: 是否有人防下拉
     * @returns {Promise<ResponseData>}
     */
    async queryRFListColl(){
        let data = await this.service.jieSuanProject.jieSuanMajorIndexService.queryRFListColl();
        return  ResponseData.success(data);
    }

    /**
     * 参考指标: 建筑分类
     * @returns {Promise<ResponseData>}
     */
    async queryJZFL2ListColl(){
        let data = await this.service.jieSuanProject.jieSuanMajorIndexService.queryJZFL2ListColl();
        return  ResponseData.success(data);
    }

}

JieSuanMajorIndexController.toString = () => '[class JieSuanMajorIndexController]';
module.exports = JieSuanMajorIndexController;

