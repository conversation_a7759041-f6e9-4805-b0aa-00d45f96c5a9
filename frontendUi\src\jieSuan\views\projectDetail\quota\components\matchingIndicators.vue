<!--
 * @Descripttion: 匹配指标
 * @Author: renmingming
 * @Date: 2023-05-22 15:36:24
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-24 15:50:49
-->
<template>
  <common-modal className="dialog-comm" v-model:modelValue="dialogVisible" title="匹配指标" width="auto" @cancel="cancel"
    @close="cancel" :loadingModal="loadingModal">

    <div class="edit-wrap">
      <split :ratio="'1/2'" :onlyPart="'all'" style="height: 100%">
        <template #one>
          <div class="table-wrap">
            <vxe-table ref="vexTable" border="full" height="100%" align="center"
              show-overflow="tooltip" :scroll-y="{ gt: 0 }" :column-config="{ resizable: true }"
              :row-config="{ isCurrent: true, keyField: 'id' }" :edit-config="{
                trigger: 'dblclick',
                mode: 'cell',
                showIcon: false,
                showStatus: false,
              }" :tree-config="{
                children: 'children',
                expandAll: true,
                transform: true,
              }" :data="treeList" :row-class-name="({ row }) => {
                if (row.originalFlag || row.parentProjectId) {
                  return 'selRow'
                } else {
                  return 'cancelRow'
                }
              }"
              @cell-click="
                cellData => {
                  rowClick(cellData);
                }
              "
              >
              <vxe-column width="70" tree-node></vxe-column>
              <vxe-column field="name" title="项目结构"></vxe-column>
              <vxe-column field="majorIndex" title="专业切换">
                <template #default="{ row }">
                  <a-select ref="select" v-if="row.levelType === 3 && engineerMajorList.length > 0&&(row.originalFlag||(!row.originalFlag&&row.parentProjectId))"
                    v-model:value="row.majorIndex" @change="changeConstruct(row)">
                    <a-select-option v-for="(item, index) in engineerMajorList" :key="index" :value="item">{{ item
                      }}</a-select-option>
                  </a-select>
                  <a-select v-if="row.levelType === 3 && !row.originalFlag&&row.parentProjectId==null"
                    v-model:value="row.majorIndex" @click="majorClick(row)" :open="false"></a-select>
                </template>
              </vxe-column>
            </vxe-table>
          </div>
        </template>
        <template #two>
          <div class="rightBox">
            <div class="tab-menu">
              <a-tabs v-model:activeKey="activeKey" type="card" @change="tabsChange">
                <a-tab-pane :key="tab.code" :tab="tab.value" v-for="tab in tabs"></a-tab-pane>
              </a-tabs>
              <a-checkbox v-model:checked="checked" @change="showNotSet" style="margin:10px 0;">只显示未设置指标项</a-checkbox>
              <!-- 分部分项指标\措施项目指标\主要工料指标 -->
              <fbcsTable v-if="currentInfo" ref="fbcsTableRef" :currentInfo="currentInfo"></fbcsTable>
            </div>
          </div>
        </template>
      </split>
      <div class="footer-btn-list">
        <a-button type="primary" @click="handleOk" :loading="submitLoading">应用修改</a-button>
        <a-button @click="cancel">取消</a-button>
      </div>
    </div>
    <setProjectBelong
      @closeDialog="()=>{isProjectRevert=false;getTreeList(true)}"
      :belongData="currentInfo"
      v-if="isProjectRevert"
    >
    </setProjectBelong>
  </common-modal>
</template>

<script setup>
import { message } from 'ant-design-vue';
import { ref, reactive, watch, nextTick, toRaw, onMounted } from 'vue';
import jiesuanApi from '@/api/jiesuanApi';
import { getJsAsideTreeList } from '@/api/jiesuanApi';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail';
import fbcsTable from './fbcsTable.vue';
import setProjectBelong from '@/components/fileSection/setJsProjectBelong.vue';
import xeUtils from "xe-utils";
import { VXETable } from 'vxe-table';
const store = projectDetailStore();
const emits = defineEmits(['refresh']);
const activeKey = ref(1);
const checked = ref(false);
const isProjectRevert = ref(false);
const isFbfxCheck = ref(false);
const isCsxmCheck = ref(false);
const isZyglCheck = ref(false);
const fbcsTableRef = ref();
const currentInfo = ref({});
const tabs = ref([
  {
    code:1,
    value:'分部分项指标',
  },
  {
    code:2,
    value:'措施项目指标',
  },
  {
    code:3,
    value:'主要工料指标',
  }
]);
let dialogVisible = ref(false);
const cancel = (refresh = false) => {
  let apiData={
    constructId: store.currentTreeGroupInfo?.constructId
  }
  jiesuanApi.restIndexCachAllUnitDataColl(apiData).then(res => {
    if(res.code!==200){
      return message.error(res.message)
    }
    dialogVisible.value = false;
  });
};
const open = k => {
  dialogVisible.value = true;
  currentInfo.value={}
  let apiData={
    constructId: store.currentTreeGroupInfo?.constructId
  }
  jiesuanApi.indexCachAllUnitDataColl(apiData).then(res => {
    if(res.code!==200){
      return message.error(res.message)
    }
    getTreeList();
  });
};
let treeList = ref([]);
const route = useRoute();
const getTreeList = async (type=false) => {
  const res = await getJsAsideTreeList(route.query.constructSequenceNbr);
  if (res.status !== 200) {
    message.error(res.message);
    return;
  }
  console.info('匹配指标左侧树列表',res.result)
  treeList.value = res.result
  
  for(let item of res.result){
    if(type){
      if(item.id==currentInfo.value.id){
        vexTable.value.setCurrentRow(item)
        rowClick({row:item},true)
        break
      }
    }else if(item.levelType==3){
      vexTable.value.setCurrentRow(item)
      rowClick({row:item})
      break
    }
  }
};
let loading = ref(false);
let submitLoading = ref(false);
let vexTable = ref();
let engineerMajorList = ref([]);
const handleOk = () => {
  let apiData={
    constructId: store.currentTreeGroupInfo?.constructId
  }
  jiesuanApi.useUpdateColl(apiData).then(res => {
    if(res.code!==200){
      return message.error(res.message)
    }
    emits('refresh')
    dialogVisible.value = false;
  });
};
onMounted(() => {
  queryEngineerMajorList();
});
// 点击行
const rowClick = (cellData,type=false) => {
  let row=cellData.row
  if(currentInfo.value.id==row.id&&!type){
    return;
  }
  if(row.levelType<3){
    return;
  }
  currentInfo.value=row
  fbcsTableRef.value.getDataList(row,activeKey.value,checked.value);
}

//获取专业列表下拉列表
const queryEngineerMajorList = () => {
  jiesuanApi.unitMajorDropdownListColl().then(res => {
    engineerMajorList.value=res.result
  });
};
//切换专业
const changeConstruct = (row) => {
  let apiData={
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: row.parentId,
    unitId: row.id,
    majorName:row.majorIndex
  }
  jiesuanApi.updateIndexMajorColl(apiData).then(res => {});
  fbcsTableRef.value.getDataList(row,activeKey.value,checked.value);
};
// 点击未归属工程的专业切换
const majorClick = (row) =>{
  new Promise((resolve, reject) => {
    VXETable.modal
      .confirm({
        content: '当前单位工程未进行工程归属设置无法进行此操作操作',
        className: 'dialog-comm confirm-dialog',
        status: 'error',
        title:'提示',
        iconStatus: 'vxe-icon-info-circle',
        resetButtonText:'关闭',
        confirmButtonText:'设置工程归属'
      })
      .then(res => {
        if (res === 'confirm') {
          isProjectRevert.value=true
          resolve(true);
        } else {
          resolve(false);
        }
      });
  });
}
// 切换右侧顶部tab签
const tabsChange = (val) => {
  console.info(val)
  if(val==1){
    checked.value=isFbfxCheck.value
  }else if(val==2){
    checked.value=isCsxmCheck.value
  }else{
    checked.value=isZyglCheck.value
  }
  fbcsTableRef.value.getDataList(currentInfo.value,val,checked.value);
}
// 只显示未设置指标
const showNotSet = (val) => {
  if(activeKey.value==1){
    isFbfxCheck.value=val.target.checked
  }else if(activeKey.value==2){
    isCsxmCheck.value=val.target.checked
  }else{
    isZyglCheck.value=val.target.checked
  }
  fbcsTableRef.value.getDataList(currentInfo.value,activeKey.value,val.target.checked);
}
defineExpose({
  open,
  cancel,
});
</script>
<style lang="scss" scoped>
.edit-wrap {
  width: 80vw;
  height: 60vh;
  display: flex;
  flex-direction: column;
}

.head {
  .ant-btn {
    margin-right: 10px;
  }
}

.table-wrap {
  flex: 1;
  height: calc(100% - 30px);
  overflow: hidden;
}

.table-wrap :deep(.vxe-table) {
  .selRow {
    background-color: #E2EFFC;
  }

  .cancelRow {
    background-color: white;
  }

  .ant-select-selector {
    border: none !important;
    background-color: transparent !important;
  }

  .vxe-body--column,
  .vxe-header--column,
  .vxe-footer--column {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }

  .row--current {
    background-color: #a6c3fa;

    .ant-select,
    .ant-select-selection-placeholder,
    .ant-select-arrow {
      color: var(--primary-color);
    }
  }
}

.rightBox {
  flex: 1;
  height: calc(100% - 30px);
  overflow: hidden;
  margin-left: 10px;
}

.tab-menu :deep(.ant-tabs) {
  height: var(--project-detail-main-content-tabs-menu-height);
  position: relative;
  top: 1px;
  box-sizing: border-box;
  font-size: 12px;

  .ant-tabs-nav {
    margin-bottom: 0;
    // padding: 0 11px;
    height: 100%;

    .ant-tabs-tab {
      padding: 5px 20px;
      border: none;
      border-radius: 4px;
      background-color: transparent;
      font-size: 12px;
      border-right: 1px solid #d6d6d6;
      margin-left: 0;
    }

    .ant-tabs-nav-more {
      display: none !important;
    }

    .ant-tabs-tab-active {
      background-color: #deeaff;

      .ant-tabs-tab-btn {
        color: #333333;
      }
    }
  }
}

:deep(.ant-select-clear) {
  border-radius: 50%;
}
</style>
