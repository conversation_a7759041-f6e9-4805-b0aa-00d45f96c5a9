/*
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-07-03 11:24:30
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2025-03-25 16:24:47
 */
import { ref } from 'vue';
import { useSubItem } from '@/hooks/useSubItemStable.js';
import { hangZCSB } from '@/views/projectDetail/customize/subItemProject/hangZCSB.js';




const { isSpecificationEdit } = hangZCSB({});
const typeList = ref([
  {
    name: '材料费',
    value: 2,
  },
  {
    name: '主材费',
    value: 5,
  },
  {
    name: '设备费',
    value: 4,
  },
]);
const getTableColumns = (emits, type, original) => {
  let { editClosedEvent } = useSubItem({
    emits,
    pageType: type,
  });
  let codeMap = {
    fbfx: 'bdCode',
    csxm: 'fxCode',
  };
  
  const originalTableColumns = ref([
  {
    title: '序号',
    field: 'dispNo',
    dataIndex: 'dispNo',
    width: 50,
    align: 'center',
    autoHeight: true,
    classType: 1,
    fixed: 'left',
  },
  {
    title: '项目编码',
    field: codeMap[type],
    dataIndex: codeMap[type],
    align: 'left',
    headerAlign: 'center',
    width: 170,
    autoHeight:true,
    editRender: { autofocus: '.vxe-input--inner' },
    slot: true,
    classType: 1,
    fixed: 'left',
    editableTrigger: 'click',
    editable: 'cellEditorSlot',
  },
  {
    title: '类型',
    field: 'type',
    width: 50,
    align: 'center',
    fixed: 'left',
    classType: 1,
    editable: ({ record: row }) => {
      if (typeList.value.map(item => item.name).includes(row.type)) {
        return 'cellEditorSlot';
      } else {
        return false;
      }
    },
  },
  {
    title: '项目名称',
    field: 'name',
    width: 200,
    align: 'center',
    slot: true,
    autoHeight:true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    fixed: 'left',
    editable: ({ record: row }) => {
      // console.log(record)
      if (!['00', '07'].includes(row.kind)) {
        return 'cellEditorSlot';
      } else {
        return false;
      }
    },
  },
  {
    title: '工作内容',
    field: 'workContent',
    width: 120,
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    visible: false,
    initialize: false
  },
  {
    title: '项目特征',
    field: 'projectAttr',
    width: 120,
    slot: true,
    autoHeight:true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    visible: false,
    initialize: false,
    editable: ({ record: row }) => {
      if (['03'].includes(row.kind)) {
        return 'cellEditorSlot';
      } else {
        return false;
      }
    },
  },
  // {
  //   title: '规格型号',
  //   field: 'specification',
  //   width: 120,
  //   slot: true,
  //   editRender: { autofocus: '.vxe-input--inner' },
  //   classType: 1,
  // },
  {
    title: '单位',
    field: 'unit',
    width: 60,
    slot: true,
    classType: 1,
    editable: ({ record: row }) => {
      // console.log(record)
      if (
        ['03', '04', 94, 95].includes(row.kind) &&
        row[codeMap[type]] &&
        !['07'].includes(row.kind)
      ) {
        return 'cellEditorSlot';
      } else {
        return false;
      }
    },
  },
  // {
  //   title: '含量',
  //   field: 'deProportion',
  //   slot: true,
  //   editRender: { autofocus: '.vxe-input--inner' },
  //   classType: 1,
  //   initialize: false
  // },
  {
    title: '合同工程量',
    field: 'backQuantity',
    width: 100,
    classType: 3,
  },
  {
    title: '工程量表达式',
    field: 'quantityExpression',
    width: 100,
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
    classType: 1,
    editable: ({ record: row }) => {
      if (['03', '04'].includes(row.kind) && row.isCostDe !== 1 && !(row.originalFlag && row.containYsCyCgQd )) {
        return 'cellEditorSlot';
      } else {
        return false;
      }
    },
  },
  {
    title: '锁定综合单价',
    field: 'lockPriceFlag',
    slot: true,
    classType: 1,
  },
 
  {
    title: '结算工程量',
    field: 'quantityEdit',
    width: 100,
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
    classType: 1,
    initialize: true,
    editable: ({ record: row }) => {
      if (['03', '04', 94, 95].includes(row.kind) && row.isCostDe !== 1 && !(row.originalFlag && row.containYsCyCgQd )) {
        return 'cellEditorSlot';
      } else {
        return false;
      }
    },
  },
  {
    title: '合同单价',
    field: 'backPrice',
    width: 100,
    classType: 2,
    initialize: true
  },
  {
    title: '合同合价',
    field: 'backTotal',
    width: 100,
    classType: 2,
    visible: false,
    initialize: false
  },
  {
    title: '结算单价',
    field: 'price',
    width: 100,
    classType: 1,
    visible: false,
    initialize: false
  },
  {
    title: '结算合价',
    field: 'total',
    classType: 2,
    width: 100,
    initialize: true
  },
  {
    title: '量差',
    field: 'quantityDifference',
    classType: 3,
    initialize: true
  },
  {
    title: '量差比例(%)',
    field: 'quantityDifferenceProportion',
    slot: true,
    classType: 3,
    initialize: true
  },
  {
    title: '取费专业',
    field: 'costMajorName',
    classType: 1,
    visible: false,
    initialize: false,
    ellipsis: true,
  },
  {
    title: '防寒子目',
    field: 'coldResistantSuborder',
    slot: true,
    classType: 1,
  },
  {
    title: '备注',
    field: 'description',
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
    classType: 1,
    initialize: true,
    ellipsis: true,
    editable: ({ record: row }) => {
      if ([94, 95].includes(row.kind)) {
        return false;
      } else {
        return true;
      }
    },
    valueParser: ({ newValue, oldValue, record: row, column }) => {
      if (newValue === oldValue) return newValue;
      editClosedEvent({ row, column }, newValue, oldValue);
      return newValue;
    },
  },
]);
  const tableColumns = ref([
    // 常用项

    {
      title: '序号',
      field: 'dispNo',
      dataIndex: 'dispNo',
      width: 50,
      align: 'center',
      autoHeight: true,
      classType: 1,
      fixed: 'left',
    },
    {
      title: '项目编码',
      field: codeMap[type],
      dataIndex: codeMap[type],
      align: 'left',
      headerAlign: 'center',
      width: 170,
      editRender: { autofocus: '.vxe-input--inner' },
      edit: true,
      classType: 1,
      fixed: 'left',
      editableTrigger: 'click',
      editable: 'cellEditorSlot',
    },
    {
      title: '类型',
      field: 'type',
      dataIndex: 'type',
      width: 60,
      align: 'center',
      slot: true,
      classType: 1,
      fixed: 'left',
      tooltip: true,
      editable: ({ record: row }) => {
        if (typeList.value.map(item => item.name).includes(row.type)) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '项目名称',
      field: 'name',
      dataIndex: 'name',
      width: 200,
      align: 'center',
      slot: true,
      autoHeight: true,
      editRender: { autofocus: '.vxe-textarea--inner' },
      classType: 1,
      tooltip: true,
      fixed: 'left',
      editable: ({ record: row }) => {
        // console.log(record)
        if (!['00', '07'].includes(row.kind)) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '项目特征',
      field: 'projectAttr',
      width: 180,
      slot: true,
      autoHeight: true,
      editRender: { autofocus: '.vxe-textarea--inner' },
      classType: 1,
      editable: ({ record: row }) => {
        if (['03'].includes(row.kind)) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    // {
    //   title: '规格型号',
    //   field: 'specification',
    //   width: 80,
    //   slot: true,
    //   autoHeight: true,
    //   classType: 1,
    //   editRender: { autofocus: '.vxe-input--inner' },
    //   editable: ({ record: row }) => {
    //     if (isSpecificationEdit(row)) {
    //       return 'cellEditorSlot';
    //     } else {
    //       return false;
    //     }
    //   },
    // },
    {
      title: '单位',
      field: 'unit',
      width: 65,
      slot: true,
      classType: 1,
      editRender: { autofocus: '.custom--inner' },
      editable: ({ record: row }) => {
        // console.log(record)
        if (
          ['03', '04', 94, 95].includes(row.kind) &&
          row[codeMap[type]] &&
          !['07'].includes(row.kind)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '工程量表达式',
      field: 'quantityExpression',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
      editable: ({ record: row }) => {
        if (['03', '04'].includes(row.kind) && row.isCostDe !== 1) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '结算工程量',
      field: 'quantityEdit',
      width: 80,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
      classType: 1,
      editable: ({ record: row }) => {
        if (['03', '04', 94, 95].includes(row.kind) && row.isCostDe !== 1) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },

    {
      title: '锁定综合单价',
      field: 'lockPriceFlag',
      width: 100,
      classType: 1,
    },
    {
      title: '单价',
      field: 'zjfPrice',
      width: 80,
      classType: 1,
      slot: true,
      editable: ({ record: row }) => {
        if (
          [94, 95].includes(row.kind) ||
          (row.kind == '04' && row.rcjFlag && Number(row.isChangeAva) !== 0)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '合价',
      field: 'zjfTotal',
      width: 80,
      slot: true,
      classType: 1,
    },
    {
      title: '结算单价',
      field: 'price',
      width: 80,
      classType: 1,
      slot: true,
    },
    {
      title: '结算合价',
      field: 'total',
      width: 80,
      classType: 1,
    },

    // {
    //   title: '单价构成文件',
    //   field: 'qfCode',
    //   width: 80,
    //   editRender: {},
    //   slot: true,
    //   ellipsis: true,
    //   classType: 1,
    //   editable: ({ record: row }) => {
    //     if (
    //       ['04'].includes(row.kind) &&
    //       row[codeMap[type]] &&
    //       !row.zjcsClassCode
    //     ) {
    //       return 'cellEditorSlot';
    //     } else {
    //       return false;
    //     }
    //   },
    // },
    {
      title: '取费专业',
      field: 'costMajorName',
      width: 80,
      editRender: { autofocus: '.vxe-select' },
      slot: true,
      classType: 1,
      visible: false,
      ellipsis: true,
      editable: ({ record: row }) => {
        if (
          ['04'].includes(row.kind) &&
          row[codeMap[type]] &&
          !row.zjcsClassCode
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '施工组织措施类别',
      field: 'measureType',
      width: 80,
      editRender: { autofocus: '.vxe-select' },
      slot: true,
      classType: 1,
      visible: false,
      ellipsis: true,
      editable: ({ record: row }) => {
        if (
          row.kind === '04' &&
          row[codeMap[type]] &&
          !row.zjcsClassCode &&
          ![2, 5].includes(Number(row.isCostDe))
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '关联合同清单',
      field: 'relevanceName',
      slot: true,
      classType: 3,
      initialize: true,
      ellipsis: true,
    },
    {
      title: '依据文件',
      field: 'accordingDocument',
      classType: 3,
      slot: true,
      initialize: true
    },
    {
      title: '归属',
      field: 'parentProjectName',
      classType: 3,
      initialize: true
    },
    {
      title: '防寒子目',
      field: 'coldResistantSuborder',
      slot: true,
      classType: 1,
    },
    {
      title: '备注',
      field: 'description',
      slot: true,
      editRender: { autofocus: '.vxe-textarea--inner' },
      classType: 1,
      ellipsis: true,
      editable: ({ record: row }) => {
        if ([94, 95].includes(row.kind)) {
          return false;
        } else {
          return true;
        }
      },
      valueParser: ({ newValue, oldValue, record: row, column }) => {
        if (newValue === oldValue) return newValue;
        editClosedEvent({ row, column }, newValue, oldValue);
        return newValue;
      },
    },
    // 费用细项
    {
      title: '人工费单价',
      field: 'rfee',
      classType: 2,
      visible: false,
    },
    {
      title: '人工费合价',
      field: 'totalRfee',
      visible: false,
      classType: 2,
    },
    {
      title: '材料费单价',
      field: 'cfee',
      visible: false,
      classType: 2,
    },
    {
      title: '材料费合价',
      field: 'totalCfee',
      visible: false,
      classType: 2,
    },
    {
      title: '机械费单价',
      field: 'jfee',
      visible: false,
      classType: 2,
    },
    {
      title: '机械费合价',
      field: 'totalJfee',
      visible: false,
      classType: 2,
    },
    {
      title: '主材费单价',
      field: 'zcfee',
      visible: false,
      classType: 2,
    },
    {
      title: '主材费合价',
      field: 'totalZcfee',
      visible: false,
      classType: 2,
    },
    {
      title: '设备费单价',
      field: 'sbfPrice',
      visible: false,
      classType: 2,
    },
    {
      title: '设备费合价',
      field: 'sbfTotal',
      visible: false,
      classType: 2,
    },
    // {
    //   title: '暂估单价',
    //   field: 'zgfPrice',
    //   visible: false,
    //   classType: 2,
    // },
    // {
    //   title: '暂估合价',
    //   field: 'zgfTotal',
    //   visible: false,
    //   classType: 2,
    // },
    {
      title: '管理费单价',
      field: 'managerFee',
      visible: false,
      classType: 2,
    },
    {
      title: '管理费合价',
      field: 'totalManagerFee',
      visible: false,
      classType: 2,
    },
    {
      title: '利润单价',
      field: 'profitFee',
      classType: 2,
      visible: false,
    },
    {
      title: '利润合价',
      field: 'totalProfitFee',
      visible: false,
      classType: 2,
    },
    // {
    //   title: '规费单价',
    //   field: 'gfPrice',
    //   visible: false,
    //   classType: 2,
    // },
    // {
    //   title: '规费合价',
    //   field: 'gfTotal',
    //   visible: false,
    //   classType: 2,
    // },
    //此处缺少直接费单价和直接费合价
  ]);
  if (type === 'csxm') {
    let index = tableColumns.value.findIndex(a => a.field === 'measureType');
    tableColumns.value.splice(index + 1, 0, {
      title: '措施类别',
      field: 'itemCategory',
      editRender: { autofocus: '.vxe-select' },
      slot: true,
      classType: 1,
      editable: ({ record: row }) => {
        if (row.kind === '01' && row.constructionMeasureType !== 2) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    });
    // 措施项目 底下才有
    let newObject = {
      title: '调整系数',
      field: 'adjustmentCoefficient',
      visible: true,
      editRender: { autofocus: '.vxe-textarea--inner' },
      classType: 1,
      editable: ({ record: row }) => {
        if (row.kind === '03' && row.zjcsClassCode !== '0') {
          return 'cellEditorSlot';
        }
        return false;
      },
    };
    tableColumns.value.splice(7, 0, newObject);
  }
  return original? originalTableColumns.value : tableColumns.value;
};
export default getTableColumns;
