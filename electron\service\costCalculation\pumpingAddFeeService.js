'use strict';

const { Service, Log } = require('../../../core');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const { PricingFileFindUtils } = require('../../utils/PricingFileFindUtils');
const { BaseDeBs2022 } = require('../../model/BaseDeBs2022');
const { BaseDeBs } = require('../../model/BaseDeBs');
const { BaseDeRcjBs2022 } = require('../../model/BaseDeRcjBs2022');
const { BaseDeRcjBs } = require('../../model/BaseDeRcjBs');
const DePropertyTypeConstant = require('../../enum/DePropertyTypeConstant');
const StepItemCostLevelConstant = require('../../enum/StepItemCostLevelConstant');
const { ItemBillProject } = require('../../model/ItemBillProject');
const RemoveStrategy = require('../../main_editor/remove/removeStrategy');
const BranchProjectLevelConstant = require('../../enum/BranchProjectLevelConstant');
const InsertStrategy = require('../../main_editor/insert/insertStrategy');
const { BaseDeBsStandard2022 } = require('../../model/BaseDeBsStandard2022');
const { BaseDeBsStandard } = require('../../model/BaseDeBsStandard');
const PumpingAddFeeExpressionConstant = require('../../enum/PumpingAddFeeExpressionConstant');
const { ObjectUtils } = require('../../utils/ObjectUtils');
const { ConstructProjectRcj } = require('../../model/ConstructProjectRcj');
const { isSameUnit, getUnitNum } = require('../../main_editor/util');
const { CostMatchUtil } = require('./CostMatchUtil');
const { NumberUtil } = require('../../utils/NumberUtil');
const { getDeUnitFormatEnum } = require('../../main_editor/rules/format');

/**
 * 泵送增加费
 */
class PumpingAddFeeService extends Service {

  constructor(ctx) {
    super(ctx);
    this.baseDeBsArr12 = [];
    this.baseDeBsArr22 = [];
    this.baseDeRcjBs12 = [];
    this.baseDeRcjBs22 = [];
    this.baseDeBsStandardArr12 = [];
    this.baseDeBsStandardArr22 = [];
  }

  async initData(defaultDataSource) {
    this.baseDeBsArr12 = await defaultDataSource.getRepository(BaseDeBs).find({
      order: { ykHigh: 'asc' }
    });
    this.baseDeBsArr22 = await defaultDataSource.getRepository(BaseDeBs2022).find({
      order: { ykHigh: 'asc' }
    });
    this.baseDeRcjBs12 = await defaultDataSource.getRepository(BaseDeRcjBs).find();
    this.baseDeRcjBs22 = await defaultDataSource.getRepository(BaseDeRcjBs2022).find();
    this.baseDeBsStandardArr12 = await defaultDataSource.getRepository(BaseDeBsStandard).find();
    this.baseDeBsStandardArr22 = await defaultDataSource.getRepository(BaseDeBsStandard2022).find();
  }

  /**
   * 获取泵送增加费弹窗数据
   */
  async getPumpingAddFeeViewData(args) {
    const { constructId, singleId, unitId } = args;
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const is22Unit = PricingFileFindUtils.is22Unit(unit);
    const result = {
      'pumpingType': 1,           // 泵送方式：2 泵车  1 输送泵
      'eavesHeight': null,        // 输送泵地上檐高类别选择的选项的sequenceNbr
      'eavesHeightList': [],      // 输送泵地上檐高类别所有选项
      'hntCalculationFlag': 1,    // 预拌混凝土是否计算泵送增加费 1：预拌混凝土计算泵送增加费  2：预拌混凝土不计算泵送增加费
      'xjhntCalculationFlag': 1,  // 现浇混凝土是否计算泵送增加费 1：现浇混凝土计算泵送增加费  2：现浇混凝土不计算泵送增加费
      'dsCalculationFlag': 1,     // 地上参与泵送费计算 1：参与  2：不参与
      'dxCalculationFlag': 1,     // 地上参与泵送费计算 1：参与  2：不参与
      'baseDeList': []            // 泵送费基数定额列表
    };
    // 根据【泵送方式】查询输送泵地上檐高类别所有选项
    let eavesHeightData;
    if (is22Unit) {
      if (ObjectUtil.isNotEmpty(this.baseDeBsArr22)) {
        eavesHeightData = this.baseDeBsArr22;
      } else {
        eavesHeightData = await this.app.appDataSource.manager.getRepository(BaseDeBs2022).find({
          order: { ykHigh: 'asc' }
        });
      }
    } else {
      if (ObjectUtil.isNotEmpty(this.baseDeBsArr12)) {
        eavesHeightData = this.baseDeBsArr12;
      } else {
        eavesHeightData = await this.app.appDataSource.manager.getRepository(BaseDeBs).find({
          order: { ykHigh: 'asc' }
        });
      }
    }
    result.eavesHeightList = eavesHeightData;
    result.eavesHeight = result.eavesHeightList[0].sequenceNbr;
    if (ObjectUtil.isNotEmpty(unit.pumpingAddFeeCache)) {
      // 如果有缓存  那么覆盖显示的数据
      result.pumpingType = unit.pumpingAddFeeCache.pumpingType;
      result.eavesHeight = unit.pumpingAddFeeCache.eavesHeight;
      result.hntCalculationFlag = unit.pumpingAddFeeCache.hntCalculationFlag;
      result.xjhntCalculationFlag = unit.pumpingAddFeeCache.xjhntCalculationFlag;
      result.dsCalculationFlag = unit.pumpingAddFeeCache.dsCalculationFlag;
      result.dxCalculationFlag = unit.pumpingAddFeeCache.dxCalculationFlag;
    }
    // 查询泵送费的所有基数人材机
    let baseDeRcjBsList;
    if (is22Unit) {
      if (ObjectUtil.isNotEmpty(this.baseDeRcjBs22)) {
        baseDeRcjBsList = this.baseDeRcjBs22;
      } else {
        baseDeRcjBsList = await this.app.appDataSource.manager.getRepository(BaseDeRcjBs2022).find();
      }
    } else {
      if (ObjectUtil.isNotEmpty(this.baseDeRcjBs12)) {
        baseDeRcjBsList = this.baseDeRcjBs12;
      } else {
        baseDeRcjBsList = await this.app.appDataSource.manager.getRepository(BaseDeRcjBs).find();
      }
    }
    const baseRcjCode = baseDeRcjBsList.map(item => item.materialCode);

    // 取单位中所有的人材机数据(包括编辑区人材机)进行过滤
    // 这里有个情况需要记录：
    //   泵送费的基数人材机包含两个地方的：1、人材机明细区的非子级人材机(就是没有子级的人材机 和 父级人材机) 2、定额类型的人材机
    //   在数据存储方面：
    //     人材机明细区的非子级人材机：本来就在unitProject.constructProjectRcjs中，而且里面没有子级数据，所以直接取
    //     定额类型的人材机：这一部分数据在目前的存储结构中，他在unitProject.constructProjectRcjs中存在，这个时候他算人材机
    //                    但是你通过unitProject.constructProjectRcjs中数据的deId在定额数据中也能找到这条数据，这个时候他算定额
    //   所以说，定额类型的人材机既存在于人材机中，也存在于定额数据中，因此此处直接通过unitProject.constructProjectRcjs取数据并关联出定额就可以得到泵送费基数人材机对应的所有定额数据(包含了定额人材机)
    const rcjList = PricingFileFindUtils.getRcjList(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(rcjList)) {
      return result;
    }
    // 筛选出所有的基数人材机
    const unitBaseRcj = rcjList.filter(item => {
      let code = item.materialCode;
      if (code.includes('#')) {
        code = code.split('#')[0];
      }
      return baseRcjCode.includes(code);
    });
    if (ObjectUtil.isEmpty(unitBaseRcj)) {
      return result;
    }
    // 获取到所有的基数定额id
    const baseDeIds = [...new Set(unitBaseRcj.map(item => item.deId))];
    let baseDeList = [];
    for (let baseDeId of baseDeIds) {
      // 只取分部分项的   不取措施项目的   泵送费基数定额不包含措施项目
      const fbfxNode = unit.itemBillProjects.getNodeById(baseDeId);
      if (ObjectUtil.isNotEmpty(fbfxNode) && (fbfxNode.libraryCode == '2022-JZGC-DEY' || fbfxNode.libraryCode == '2012-JZGC-DEY')) {
        baseDeList.push(fbfxNode);
      }
    }

    // 根据所有的基数定额  获取显示时需要的树结构数据集合    同时进行一次克隆 以免影响原来的数据
    result.baseDeList = ObjectUtil.cloneDeep(await CostMatchUtil.getTreeList(baseDeList, unit));
    let baseDeBsStandardList;
    if (is22Unit) {
      if (ObjectUtil.isNotEmpty(this.baseDeBsStandardArr22)) {
        baseDeBsStandardList = this.baseDeBsStandardArr22;
      } else {
        baseDeBsStandardList = await this.app.appDataSource.manager.getRepository(BaseDeBsStandard2022).find();
      }
    } else {
      if (ObjectUtil.isNotEmpty(this.baseDeBsStandardArr12)) {
        baseDeBsStandardList = this.baseDeBsStandardArr12;
      } else {
        baseDeBsStandardList = await this.app.appDataSource.manager.getRepository(BaseDeBsStandard).find();
      }
    }
    // 根据缓存数据  处理【地上地下】的缓存值   没有缓存就给默认值
    for (const item of result.baseDeList) {
      delete item.parent;
      delete item.children;
      delete item.prev;
      delete item.next;
      if (item.kind == BranchProjectLevelConstant.de) {
        item.upFloor = 1;// 默认 1：地上    2：地下
        let cacheNode = null;
        if (ObjectUtil.isNotEmpty(unit.pumpingAddFeeCache) && ObjectUtil.isNotEmpty(unit.pumpingAddFeeCache.baseDeList)) {
          cacheNode = unit.pumpingAddFeeCache.baseDeList.find(cacheItem => cacheItem.sequenceNbr == item.sequenceNbr);
        }
        if (ObjectUtil.isNotEmpty(cacheNode)) {
          item.upFloor = cacheNode.upFloor;
        } else {
          const findObj = baseDeBsStandardList.find(baseDeBsStandard => baseDeBsStandard.libraryCode == item.libraryCode && baseDeBsStandard.deCode == item.bdCode);
          if (ObjectUtil.isNotEmpty(findObj)) {
            item.upFloor = findObj.upFloor;
          }
        }
      }

    }
    return result;
  }

  async getTreeList(baseDeList, unit) {
    let arr = [];
    for (const de of baseDeList) {
      this.getParentData(arr, de, unit);
    }
    return arr;
  }

  getParentData(arr, data, unit) {
    if (!arr.find(item => item.sequenceNbr == data.sequenceNbr)) {
      arr.push(data);
    }
    if (ObjectUtil.isEmpty(data.parentId) || data.parentId == '0') {
      return;
    }
    this.getParentData(arr, unit.itemBillProjects.getNodeById(data.parentId), unit);
  }

  /**
   * 计算泵送增加费
   */
  async calculationPumpingAddFee(args) {
    const {
      constructId,
      singleId,
      unitId,
      pumpingType,          // 泵送方式：2 泵车  1 输送泵
      eavesHeight,          // 输送泵地上檐高类别选择的选项的sequenceNbr
      hntCalculationFlag,   // 预拌混凝土是否计算泵送增加费 1：预拌混凝土计算泵送增加费  2：预拌混凝土不计算泵送增加费
      xjhntCalculationFlag, // 现浇混凝土是否计算泵送增加费 1：现浇混凝土计算泵送增加费  2：现浇混凝土不计算泵送增加费
      dsCalculationFlag,    // 地上参与泵送费计算 1：参与  2：不参与
      dxCalculationFlag,    // 地上参与泵送费计算 1：参与  2：不参与
      baseDeList            // 泵送费基数定额列表
    } = args;
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const is22Unit = PricingFileFindUtils.is22Unit(unit);
    const baseDeBs2022s = await this.app.appDataSource.manager.getRepository(is22Unit ? BaseDeBs2022 : BaseDeBs).find();
    // 删除历史的泵送费定额
    await this.clearPumpingAddFeeDe(unit, baseDeBs2022s);
    // 设置缓存
    unit.pumpingAddFeeCache = {
      pumpingType: pumpingType,
      eavesHeight: eavesHeight,
      hntCalculationFlag: hntCalculationFlag,
      xjhntCalculationFlag: xjhntCalculationFlag,
      dsCalculationFlag: dsCalculationFlag,
      dxCalculationFlag: dxCalculationFlag,
      baseDeList: baseDeList
    };
    if (ObjectUtil.isEmpty(baseDeList)) {
      return;
    }

    // 本次记取参与计算的基数人材机code
    let hntRcjCode = [];
    // 取出所有基数人材机的数据
    const baseDeRcjBs = await this.app.appDataSource.manager.getRepository(is22Unit ? BaseDeRcjBs2022 : BaseDeRcjBs).find();
    for (const deRcjBs of baseDeRcjBs) {
      if (deRcjBs.materialName.includes('预拌混凝土')) {
        if (hntCalculationFlag == 1) {
          // 预拌混凝土参与计算
          hntRcjCode.push(deRcjBs.materialCode);
        }
        continue;
      }
      if (deRcjBs.materialName.includes('现浇混凝土')) {
        if (xjhntCalculationFlag == 1) {
          // 现浇混凝土参与计算
          hntRcjCode.push(deRcjBs.materialCode);
        }
        continue;
      }
      hntRcjCode.push(deRcjBs.materialCode);
    }

    // 筛选出定额
    let baseDeArr = baseDeList.filter(item => item.kind == StepItemCostLevelConstant.de);

    // 泵送费定额记取的对应范围是【对应分部分项清单】  也就是说泵送费定额对应的基数定额只在所属分部分项清单下
    let deGroupByQdId = baseDeArr.reduce((map, de) => {
      if (!map.has(de.parentId)) {
        map.set(de.parentId, []);
      }
      map.get(de.parentId).push(de);
      return map;
    }, new Map());
    // 把定额根据清单id分组后  遍历每一个清单
    for (const qdId of deGroupByQdId.keys()) {
      const qdNode = unit.itemBillProjects.getNodeById(qdId);
      // 获取到这个清单下的基数定额
      let qdDeArr = deGroupByQdId.get(qdId);
      let deMap = new Map();
      // 根据地上地下分组
      for (const item of qdDeArr) {
        const upFloorArr = deMap.get(item.upFloor) || [];
        upFloorArr.push(item);
        deMap.set(item.upFloor, upFloorArr);
      }
      // 根据页面选择的檐高类别 以及当前清单下的基数定额所选择的地上地下 获取到泵送费费用定额的基础定额数据(就是每个对应分部分项清单下需要添加几个泵送费定额)
      let costDeArr = await this.getBaseCosetDeArr(pumpingType, baseDeBs2022s, is22Unit, eavesHeight, deMap);

      if (dsCalculationFlag == 2 && ObjectUtil.isNotEmpty(qdDeArr)) {
        // 【地上】不参与计算  所以排除选择地上的
        qdDeArr = qdDeArr.filter(item => item.upFloor != 1);
      }
      if (dxCalculationFlag == 2 && ObjectUtil.isNotEmpty(qdDeArr)) {
        // 【地下】不参与计算  所以排除选择地下的
        qdDeArr = qdDeArr.filter(item => item.upFloor != 2);
      }
      // dsBaseValue、dxBaseValue 为初始化的两个地上泵送费的工程量      就是这个清单下分地上、地下两组的基数定额的人材机合计数量之和
      let dsBaseValue = 0;
      let dxBaseValue = 0;
      if (ObjectUtil.isNotEmpty(qdDeArr)) {
        for (const de of qdDeArr) {
          if (de.upFloor == 1) {
            // 如果是地上  那么这个定额下的【基数人材机的合计数量累加值】就是地上对应泵送费定额的工程量
            dsBaseValue = NumberUtil.add(dsBaseValue, this.sumRcjBaseValue(de, hntRcjCode, unit));
          } else {
            // 如果是地下  那么这个定额下的【基数人材机的合计数量累加值】就是地下对应泵送费定额的工程量
            dxBaseValue = NumberUtil.add(dxBaseValue, this.sumRcjBaseValue(de, hntRcjCode, unit));
          }
        }
      }

      let newDeArr = [];
      if (costDeArr.length == 1) {
        // 生成对应的泵送费定额
        const newCostDe = this.getCostDeData(costDeArr[0]);
        // 当泵送费定额只有一条的时候 直接使用dsBaseValue+dxBaseValue  原因如下：
        //    只有一条时，有四种情况：
        //      1. 泵送方式选择的【泵车】 不区分地上地下，所以值全部都在dxBaseValue，dsBaseValue为0，相加不会有影响
        //      2. 檐高选择的【40m】  不管是地上地下都算40m的，所以直接使用dsBaseValue+dxBaseValue
        //      3. 基数定额全是【地上】   那么dxBaseValue的值为0，相加不会有影响
        //      4. 基数定额全是【地下】   那么dsBaseValue的值为0，相加不会有影响
        newCostDe.quantityExpressionNbr = NumberUtil.add(dsBaseValue, dxBaseValue);
        if (pumpingType == 2) {
          // 泵车
          newCostDe.quantityExpression = 'BSHNTL_BC';
        } else {
          // 输送泵
          newCostDe.quantityExpression = 'BSHNTL_' + costDeArr[0].ykHigh;
        }
        // bsfBaseValue 表示泵送费定额的基础工程量值  用于后续工程量表达式计算
        newCostDe.bsfBaseValue = newCostDe.quantityExpressionNbr;
        newDeArr.push(newCostDe);
      } else {
        // 循环本次要生成的泵送费定额
        for (const costDe of costDeArr) {
          const newCostDe = this.getCostDeData(costDe);
          if (costDe.upFloor == 2) {
            // 40m地下
            newCostDe.quantityExpressionNbr = dxBaseValue;
          } else {
            // 地上
            newCostDe.quantityExpressionNbr = dsBaseValue;
          }
          // bsfBaseValue 表示泵送费定额的基础工程量值  用于后续工程量表达式计算
          newCostDe.bsfBaseValue = newCostDe.quantityExpressionNbr;
          newCostDe.quantityExpression = 'BSHNTL_' + costDe.ykHigh;
          newDeArr.push(newCostDe);
        }
      }
      if (ObjectUtil.isNotEmpty(newDeArr)) {
        let insertStrategy = new InsertStrategy({
          constructId,
          singleId: singleId,
          unitId: unitId,
          pageType: 'fbfx'
        });
        for (const newDe of newDeArr) {
          await insertStrategy.execute({
            pointLine: qdNode,
            newLine: newDe,
            option: 'insert',
            skip: { jiqu: true, huizong: true },
            indexId: newDe.standardId
          });
        }
      }
    }
    await this.service.autoCostMathService.autoCostMath({
      constructId: constructId,
      singleId: singleId,
      unitId: unitId,
      countCostCodeFlag: true
    });
  }

  /**
   * 根据泵送费基数定额获取其下基数人材机的合计数量之和
   */
  sumRcjBaseValue(de, hntRcjCode, unit) {
    let baseValue = 0;
    const deRcjList = PricingFileFindUtils.getDeRcjList(null, null, null, de.sequenceNbr, unit);
    // 根据本次页面选择后符合的基数人材机code进行基数人材机的筛选
    let rcjList = deRcjList.filter(item => {
      let code = item.materialCode.includes('#') ? item.materialCode.split('#')[0] : item.materialCode;
      return hntRcjCode.includes(code);
    });
    if (ObjectUtil.isNotEmpty(rcjList)) {
      rcjList.map(item => baseValue = NumberUtil.add(baseValue, item.totalNumber));
    }
    return baseValue;
  }

  /**
   * 根据参数  确定本次要添加的费用定额都有哪些
   */
  async getBaseCosetDeArr(pumpingType, baseDeBs2022s, is22Unit, eavesHeight, deMap) {
    let costDeArr = [];
    if (pumpingType == 2) {
      // 如果选择的是【泵车】 那么就不管【地上】、【地下】，直接从数据库中取【A4-313】这一条定额  因为泵车只有这一条定额 在库中标识为bsType=2
      const baseDe = baseDeBs2022s.filter(item => item.bsType == 2)[0];
      costDeArr.push(baseDe);
    } else {
      // 如果选择的是【输送泵】
      //    就需要区分【地上】、【地下】，根据选择的定额来取定额数据
      //        如果基数定额有选择地下，那么不考虑檐高类别，一定会记出来一条【40m】的
      //        如果基数定额选择的全是【地下】，但是檐高选择的非【40m】，那么只能记出来【地下40m】
      //        如果基数定额有选择地上，那么根据选择的檐高类别记出对应的定额
      //        如果檐高类别选择的是【40m】，那么只能记出来一条费用定额
      const baseDe = baseDeBs2022s.filter(item => item.sequenceNbr == eavesHeight)[0];
      costDeArr.push(baseDe);
      // 如果不是【40m(地下)】的
      // 那就要区分地上、地下  根据选择的地上地下去确定费用定额
      // 并且看基数定额是不是全是【地下】  如果全是地下只能记出来一条【40m】
      if (baseDe.upFloor != 2) {
        if (deMap.size == 1 && ObjectUtil.isNotEmpty(deMap.get(2))) {
          // 檐高选择非【40m】 并且基数定额全是地下
          costDeArr = [baseDeBs2022s.find(item => item.upFloor == 2)];
        } else {
          if (deMap.has(2)) {
            // 如果有【地下】的  那么就一定会记出来一条【40m】的
            costDeArr.push(baseDeBs2022s.find(item => item.upFloor == 2));
          }
        }
      }
    }
    return costDeArr;
  }

  getCostDeData(costDe) {
    let itemBillProject = new ItemBillProject();
    itemBillProject.bdName = costDe.deName;
    itemBillProject.bdCode = costDe.deCode;
    itemBillProject.name = costDe.deName;
    itemBillProject.fxCode = costDe.deCode;
    itemBillProject.kind = StepItemCostLevelConstant.de;
    itemBillProject.unit = costDe.unit;
    itemBillProject.standardId = costDe.deId;
    itemBillProject.isCostDe = DePropertyTypeConstant.NON_COST_DE;
    itemBillProject.isStandard = DePropertyTypeConstant.STANDARD;//定额数据位标准定额数据
    itemBillProject.libraryCode = costDe.libraryCode;
    // itemBillProject.quantityExpression = deItem.quantityExpression;//工程量表达式展示用
    // itemBillProject.quantityExpressionNbr = deItem.quantityExpressionNbr;//工程量表达式计算用
    // itemBillProject.quantity = deItem.quantity;
    return itemBillProject;
  }

  /**
   * 删除单位中的所有泵送费定额
   */
  async clearPumpingAddFeeDe(unit, baseDeBs2022s) {
    const allNodes = unit.itemBillProjects.getAllNodes();
    if (ObjectUtil.isEmpty(allNodes)) {
      return;
    }
    // 泵送费定额的code集合
    let deCodeArr = new Set();
    let libraryCodeArr = new Set();
    baseDeBs2022s.map(item => {
      deCodeArr.add(item.deCode);
      libraryCodeArr.add(item.libraryCode);
    });
    // 筛选所有的泵送费定额
    const pumpingAddFeeDeArr = allNodes.filter(item => ObjectUtil.isNotEmpty(item.bdCode) && libraryCodeArr.has(item.libraryCode) && ObjectUtil.isNotEmpty(item.bdCode) && deCodeArr.has(item.bdCode));
    if (ObjectUtil.isEmpty(pumpingAddFeeDeArr)) {
      return;
    }
    let fbfxRemoveStrategy = new RemoveStrategy({
      constructId: unit.constructId,
      singleId: unit.spId,
      unitId: unit.sequenceNbr,
      pageType: 'fbfx'
    });
    for (const de of pumpingAddFeeDeArr) {
      await fbfxRemoveStrategy.execute({
        pointLine: de,
        isBlock: de.kind !== BranchProjectLevelConstant.de
      });
    }
  }

  /**
   * 获取泵送费定额
   */
  async getPumpingAddFeeDe(unit, baseDeBs2022s) {
    const allNodes = unit.itemBillProjects.getAllNodes();
    if (ObjectUtil.isEmpty(allNodes)) {
      return [];
    }
    // 泵送费定额的code集合
    let deCodeArr = new Set();
    let libraryCodeArr = new Set();
    baseDeBs2022s.map(item => {
      deCodeArr.add(item.deCode);
      libraryCodeArr.add(item.libraryCode);
    });
    // 筛选所有的泵送费定额
    return allNodes.filter(item => {
      if (ObjectUtil.isNotEmpty(item.bdCode) && libraryCodeArr.has(item.libraryCode) && ObjectUtil.isNotEmpty(item.bdCode) && deCodeArr.has(item.bdCode)) {
        for (const str of PumpingAddFeeExpressionConstant.BSHNTL_LIST) {
          if (ObjectUtil.isNotEmpty(item.quantityExpression) && item.quantityExpression.toString().includes(str)) {
            return true;
          }
        }
      }
      return false;
    });
  }

  /**
   * 自动计算泵送费定额
   */
  async autoCalculatePumpingAddFee(constructId, singleId, unitId) {
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const is22Unit = PricingFileFindUtils.is22Unit(unit);
    if (ObjectUtil.isEmpty(unit.pumpingAddFeeCache)) {
      // 没有缓存就不计算  没有缓存肯定没有泵送费定额
      return;
    }
    let baseDeBs2022s;
    if (is22Unit) {
      if (ObjectUtil.isNotEmpty(this.baseDeBsArr22)) {
        baseDeBs2022s = this.baseDeBsArr22;
      } else {
        baseDeBs2022s = await this.app.appDataSource.manager.getRepository(BaseDeBs2022).find();
      }
    } else {
      if (ObjectUtil.isNotEmpty(this.baseDeBsArr12)) {
        baseDeBs2022s = this.baseDeBsArr12;
      } else {
        baseDeBs2022s = await this.app.appDataSource.manager.getRepository(BaseDeBs).find();
      }
    }
    // 获取所有的需要自动计算的泵送费定额
    const costDeArr = await this.getPumpingAddFeeDe(unit, baseDeBs2022s);
    if (ObjectUtil.isEmpty(costDeArr)) {
      return;
    }
    const {
      pumpingType,          // 泵送方式：2 泵车  1 输送泵
      eavesHeight,          // 输送泵地上檐高类别选择的选项的sequenceNbr
      hntCalculationFlag,   // 预拌混凝土是否计算泵送增加费 1：预拌混凝土计算泵送增加费  2：预拌混凝土不计算泵送增加费
      xjhntCalculationFlag, // 现浇混凝土是否计算泵送增加费 1：现浇混凝土计算泵送增加费  2：现浇混凝土不计算泵送增加费
      dsCalculationFlag,    // 地上参与泵送费计算 1：参与  2：不参与
      dxCalculationFlag,    // 地上参与泵送费计算 1：参与  2：不参与
      baseDeList            // 泵送费基数定额列表
    } = unit.pumpingAddFeeCache;
    // 上次选的檐高类别
    const baseDeBs2022Cache = baseDeBs2022s.find(item => item.sequenceNbr == eavesHeight);
    // 上次的基数定额地上地下缓存
    let deDsDxCacheMap = baseDeList.filter(item => item.kind == BranchProjectLevelConstant.de).reduce((map, de) => {
      map.set(de.sequenceNbr, de.upFloor);
      return map;
    }, new Map());
    // 把费用定额根据清单id分组
    let costDeMapByQdId = costDeArr.reduce((map, de) => {
      if (!map.has(de.parentId)) {
        map.set(de.parentId, []);
      }
      map.get(de.parentId).push(de);
      return map;
    }, new Map());
    // 基数定额的默认地上地下数据
    let baseDeBsStandardList;
    if (is22Unit) {
      if (ObjectUtil.isNotEmpty(this.baseDeBsStandardArr22)) {
        baseDeBsStandardList = this.baseDeBsStandardArr22;
      } else {
        baseDeBsStandardList = await this.app.appDataSource.manager.getRepository(BaseDeBsStandard2022).find();
      }
    } else {
      if (ObjectUtil.isNotEmpty(this.baseDeBsStandardArr12)) {
        baseDeBsStandardList = this.baseDeBsStandardArr12;
      } else {
        baseDeBsStandardList = await this.app.appDataSource.manager.getRepository(BaseDeBsStandard).find();
      }
    }
    // 所有基数人材机的code
    let rcjCodes = [];
    // 取出所有基数人材机的数据
    let baseDeRcjBs;
    if (is22Unit) {
      if (ObjectUtil.isNotEmpty(this.baseDeRcjBs22)) {
        baseDeRcjBs = this.baseDeRcjBs22;
      } else {
        baseDeRcjBs = await this.app.appDataSource.manager.getRepository(BaseDeRcjBs2022).find();
      }
    } else {
      if (ObjectUtil.isNotEmpty(this.baseDeRcjBs12)) {
        baseDeRcjBs = this.baseDeRcjBs12;
      } else {
        baseDeRcjBs = await this.app.appDataSource.manager.getRepository(BaseDeRcjBs).find();
      }
    }
    // 根据缓存内容取基数人材机code
    for (const deRcjBs of baseDeRcjBs) {
      if (deRcjBs.materialName.includes('预拌混凝土')) {
        if (hntCalculationFlag == 1) {
          // 预拌混凝土参与计算
          rcjCodes.push(deRcjBs.materialCode);
        }
        continue;
      }
      if (deRcjBs.materialName.includes('现浇混凝土')) {
        if (xjhntCalculationFlag == 1) {
          // 现浇混凝土参与计算
          rcjCodes.push(deRcjBs.materialCode);
        }
        continue;
      }
      rcjCodes.push(deRcjBs.materialCode);
    }
    for (const qdId of costDeMapByQdId.keys()) {
      // 当前清单下的泵送费定额
      const qdCostDeArr = costDeMapByQdId.get(qdId);
      let costDeIds = qdCostDeArr.map(item => item.sequenceNbr);
      // 筛选出这个清单下的基数定额  并取出基数定额对应的基数人材机
      const deBaseRcjMap = new Map();
      const baseDeList = unit.itemBillProjects.getAllNodes().filter(item => {
        if (item.kind == BranchProjectLevelConstant.de
          && item.parentId == qdId
          && (item.libraryCode == '2022-JZGC-DEY' || item.libraryCode == '2012-JZGC-DEY')
          && !costDeIds.includes(item.sequenceNbr)
        ) {
          const deRcjList = PricingFileFindUtils.getDeRcjList(null, null, null, item.sequenceNbr, unit);
          if (ObjectUtil.isNotEmpty(deRcjList)) {
            const baseRcjList = deRcjList.filter(rcj => {
              let code = rcj.materialCode.includes('#') ? rcj.materialCode.split('#')[0] : rcj.materialCode;
              return rcjCodes.includes(code);
            });
            if (ObjectUtil.isNotEmpty(baseRcjList)) {
              // 记录这个基数定额的基数人材机
              deBaseRcjMap.set(item.sequenceNbr, baseRcjList);
              return true;
            }
          }
        }
        return false;
      });

      // 筛出基数定额之后  根据这个清单下的泵送费定额 和 基数定额的地上地下 以及 基数人材机重新计算泵送费定额的工程量
      if (ObjectUtil.isNotEmpty(baseDeList)) {
        let dsBaseDeArr = [];
        let dxBaseDeArr = [];
        // 基数定额根据缓存和默认数据进行地上地下分组 分别存入dsBaseDeArr和dxBaseDeArr
        this.baseDeDxDxGroup(baseDeList, deDsDxCacheMap, dsBaseDeArr, dxBaseDeArr, baseDeBsStandardList);
        if (pumpingType == 2) {
          // 如果上次页面选择的是泵车 那么这个清单下的泵送费定额肯定只有一条泵车的【60m】  这个不管地上地下直接根据基数定额计算
          let deBaseValue = 0;
          for (const baseDe of baseDeList) {
            deBaseValue = NumberUtil.add(deBaseValue, this.sumRcjBaseValue(baseDe, rcjCodes, unit));
          }
          await this.updateDeQuantityExpressionNbr(constructId, singleId, unitId, qdCostDeArr[0], deBaseValue);
        } else if (baseDeBs2022Cache.upFloor == 2) {
          // 如果上次页面选择的是【输送泵】檐口高度选择的是【40m】  那么这个清单下的泵送费定额肯定只有一条输送泵的【40m】
          // 这个需要注意上次记取是选择的【地上】【地下】是否计算
          let baseDeArr = [];
          if (dsCalculationFlag == 1) {
            // 地上参与
            baseDeArr = baseDeArr.concat(dsBaseDeArr);
          }
          if (dxCalculationFlag == 1) {
            // 地下参与
            baseDeArr = baseDeArr.concat(dxBaseDeArr);
          }
          let deBaseValue = 0;
          for (const baseDe of baseDeArr) {
            deBaseValue = NumberUtil.add(deBaseValue, this.sumRcjBaseValue(baseDe, rcjCodes, unit));
          }
          await this.updateDeQuantityExpressionNbr(constructId, singleId, unitId, qdCostDeArr[0], deBaseValue);
        } else {
          // 如果上次记取选的【不是泵车】 也【不是檐口高度40m】   那么此时可能有2条泵送费定额   也可能只有1条泵送费定额(删除一个剩一个了)
          // 把泵送费定额分为【地上(非40m)】和【地下(40m)】
          let dsCostDe = null;
          let dxCostDe = null;
          for (const costDe of qdCostDeArr) {
            // 根据泵送费定额的standardId去基础数据中查询对应的泵送费基础定额的地上地下  从而确定这个定额是地上还是地下
            const filterBaseDeBs2022s = baseDeBs2022s.find(item => item.deId == costDe.standardId);
            if (filterBaseDeBs2022s.upFloor == 2) {
              dxCostDe = costDe;
            } else {
              dsCostDe = costDe;
            }
          }
          let dsValue = 0;
          let dxValue = 0;
          if (ObjectUtil.isNotEmpty(dsCostDe)) {
            // 如果地上参与计算  并且有地上的泵送费定额  那么取地上的基数定额计算工程量
            if (dsCalculationFlag == 1 && ObjectUtil.isNotEmpty(dsBaseDeArr)) {
              for (const baseDe of dsBaseDeArr) {
                dsValue = NumberUtil.add(dsValue, this.sumRcjBaseValue(baseDe, rcjCodes, unit));
              }
            }
            await this.updateDeQuantityExpressionNbr(constructId, singleId, unitId, dsCostDe, dsValue);
          }
          if (ObjectUtil.isNotEmpty(dxCostDe)) {
            // 如果地下参与计算  并且有地下的泵送费定额  那么取地下的基数定额计算工程量
            if (dxCalculationFlag == 1 && ObjectUtil.isNotEmpty(dxBaseDeArr)) {
              for (const baseDe of dxBaseDeArr) {
                dxValue = NumberUtil.add(dxValue, this.sumRcjBaseValue(baseDe, rcjCodes, unit));
              }
            }
            await this.updateDeQuantityExpressionNbr(constructId, singleId, unitId, dxCostDe, dxValue);
          }
        }
      }

      // if (ObjectUtil.isNotEmpty(updateCostDeArr)) {
      //   // 根据计算完成后的工程量  更新updateCostDeArr中的泵送费定额
      //   let updateStrategy = new UpdateStrategy({ constructId, singleId, unitId, pageType: 'fbfx' });
      //   for (const updateCostDe of updateCostDeArr) {
      //     await updateStrategy.execute({
      //       pointLineId: updateCostDe.sequenceNbr,
      //       upDateInfo: updateCostDe
      //     });
      //   }
      // }

    }

  }

  /**
   * 更新定额的工程量
   */
  async updateDeQuantityExpressionNbr(constructId, singleId, unitId, de, quantityExpressionNbr) {
    // bsfBaseValue 表示泵送费定额的基础工程量值  用于后续工程量表达式计算
    de.bsfBaseValue = quantityExpressionNbr;
    let expression = '';
    for (const str of PumpingAddFeeExpressionConstant.BSHNTL_LIST) {
      if (de.quantityExpression.includes(str)) {
        expression = str;
        break;
      }
    }
    let unitNum = getDeUnitFormatEnum(de.unit, constructId).value;
    let s = de.quantityExpression.replaceAll(expression, quantityExpressionNbr).replace(/（/g, '(').replace(/）/g, ')');
    de.quantityExpressionNbr = eval(s);
    de.quantity = NumberUtil.numberScale((de.quantityExpressionNbr / getUnitNum(de)), unitNum);

    let rcjs = this.service.rcjProcess.getRcjListByDeId(de.sequenceNbr, constructId, singleId, unitId);
    if (!ObjectUtils.isEmpty(rcjs)) {
      this.service.rcjProcess.reCaculateRcjPrice(de, rcjs, constructId, singleId, unitId);

      let rcj = rcjs[0];
      let constructProjectRcj = new ConstructProjectRcj();
      constructProjectRcj.type = rcj.type;
      constructProjectRcj.materialName = rcj.materialName;
      constructProjectRcj.specification = rcj.specification;
      constructProjectRcj.unit = rcj.unit;
      constructProjectRcj.dePrice = rcj.dePrice;
      this.service.unitPriceService.caculateDeByRcj(constructId, singleId, unitId, rcj);
    }
  }

  /**
   * 基数定额根据缓存和默认数据进行地上地下分组
   */
  baseDeDxDxGroup(baseDeList, deDsDxCacheMap, dsBaseDeArr, dxBaseDeArr, baseDeBsStandardList) {
    for (const baseDe of baseDeList) {
      // 先看这个定额在不在之前记取的缓存中  如果在就按照之前的地上地下记取
      const cacheBaseDeDsDx = deDsDxCacheMap.get(baseDe.sequenceNbr);
      if (ObjectUtil.isNotEmpty(cacheBaseDeDsDx)) {
        if (cacheBaseDeDsDx == 1) {
          dsBaseDeArr.push(baseDe);
        } else {
          dxBaseDeArr.push(baseDe);
        }
        continue;
      }
      // 如果不在缓存中   就去默认地上地下数据中查看  如果有默认就取默认数据   如果没有就默认地上
      const findObj = baseDeBsStandardList.find(baseDeBsStandard => baseDeBsStandard.libraryCode == baseDe.libraryCode && baseDeBsStandard.deCode == baseDe.bdCode);
      if (ObjectUtil.isNotEmpty(findObj)) {
        if (findObj.upFloor == 1) {
          dsBaseDeArr.push(baseDe);
        } else {
          dxBaseDeArr.push(baseDe);
        }
      } else {
        dsBaseDeArr.push(baseDe);
      }
    }
  }
}

PumpingAddFeeService.toString = () => '[class PumpingAddFeeService]';
module.exports = PumpingAddFeeService;
