const {ObjectUtils} = require("../../../../electron/utils/ObjectUtils");
const {NumberUtil} = require("../../../../electron/utils/NumberUtil");
const ConstructProjectRcjService = require("../../../../electron/service/rcj/constructProjectRcjService");
const {PricingFileFindUtils} = require("../../../../electron/utils/PricingFileFindUtils");
const {ObjectUtil} = require("../../../../common/ObjectUtil");
const {JieSuanRcjStageUtils} = require("../../utils/JieSuanRcjStageUtils");
const gongLiaoList = require("../../jsonData/zhiBiao/gongLiao_index");


/**
 * 修改甲供数量
 * @param de
 * @param rcj
 * @param ctx
 */
function updateDonorMaterialNumber(de,rcj,ctx,isParent) {

	let {constructProjectRcjs,rcjDetailList}= ctx;
	if (ObjectUtils.isEmpty(de))return;
	//甲供数量处理
	let coefficient = NumberUtil.multiply(rcj.resQty,de.quantity);
	if (rcj.ifDonorMaterial == 1 && rcj.donorMaterialNumber == rcj.totalNumber) {
		if (!isParent){
			let parentRcj = constructProjectRcjs.find(i => i.sequenceNbr === rcj.rcjId);
			coefficient = NumberUtil.multiply(parentRcj.resQty,coefficient);
		}
		rcj.donorMaterialNumber = NumberUtil.numberScale4(coefficient);
	}
	if (rcj.levelMark == RcjLevelMarkConstant.SINK_JX || rcj.levelMark == RcjLevelMarkConstant.SINK_PB) {
		let rcjDetails = rcjDetailList.filter(i => i.rcjId === rcj.sequenceNbr);
		if (!ObjectUtils.isEmpty(rcjDetails)) {
			for (let rcjDetail of rcjDetails) {
				//甲供数量处理
				if (rcjDetail.ifDonorMaterial == 1 && rcjDetail.donorMaterialNumber == rcjDetail.totalNumber) {
					rcjDetail.donorMaterialNumber = NumberUtil.numberScale4(NumberUtil.multiply(coefficient, rcjDetail.resQty));
				}
			}
		}
	}
}

/**
 * 更新5要素相同 市场价和暂估价标示
 * @param constructProjectRcj
 * @param list
 * @param sourcePrice
 */
function changeSimilarlyRcj(constructProjectRcj,list,type,isSimple){
	let changeDeList = [];
	if (!ObjectUtils.isEmpty(list)) {
		for (let listElement of list) {
			if (constructProjectRcj.sequenceNbr !== listElement.sequenceNbr) {
				let specificationBooble = false;
				if (ObjectUtils.isEmpty(constructProjectRcj.specification) && ObjectUtils.isEmpty(listElement.specification)){
					specificationBooble = true;
				}
				if (constructProjectRcj.materialName === listElement.materialName
					&& constructProjectRcj.materialCode === listElement.materialCode
					&& (constructProjectRcj.specification === listElement.specification||specificationBooble)
					&& constructProjectRcj.unit === listElement.unit
					&& constructProjectRcj.dePrice === listElement.dePrice
					&& constructProjectRcj.markSum === listElement.markSum) {

					listElement.marketPrice = constructProjectRcj.marketPrice;
					//添加12定额后，修改定额的市场价，另一条定额的人材机明细合价没有自动计算 BUG修复
					listElement.total = NumberUtil.multiplyToString(listElement.marketPrice, listElement.totalNumber,2);

					//if (!ObjectUtils.isEmpty(constructProjectRcj.sourcePrice)) {
						listElement.sourcePrice = constructProjectRcj.sourcePrice;
					//}
					listElement.ifProvisionalEstimate = constructProjectRcj.ifProvisionalEstimate;
					listElement.highlight = constructProjectRcj.highlight;
					if (ObjectUtils.isNotEmpty(type)){
						if (type == 1){
							listElement.priceMarket = constructProjectRcj.priceMarket;
							listElement.priceMarketTax = NumberUtil.numberScale2(NumberUtil.multiply(listElement.priceMarket,NumberUtil.add(1+NumberUtil.divide100(listElement.taxRate))));
						}else {
							listElement.priceMarketTax = constructProjectRcj.priceMarketTax;
							listElement.priceMarket = NumberUtil.numberScale2(NumberUtil.divide(listElement.priceMarketTax,NumberUtil.add(1+NumberUtil.divide100(listElement.taxRate))));
						}
						listElement.marketPrice = isSimple?listElement.priceMarketTax:listElement.priceMarket;
						//添加12定额后，修改定额的市场价，另一条定额的人材机明细合价没有自动计算 BUG修复
						listElement.total = NumberUtil.multiplyToString(listElement.marketPrice, listElement.totalNumber,2);
					}
					changeDeList.push(listElement.deId);
					// listElement.total = NumberUtil.multiply(listElement.marketPrice,
					// 	listElement.totalNumber).toFixed(2);
				}
			}
		}
	}
	return changeDeList;
}


let jieSuanupdateRcjRules = {
	//不含税市场价

	//是否调差
	isDifference: ({rcj,ctx}) => {
		let {unit} = ctx;
		let {isDifference} = rcj;


		if (unit.isDifference){
			rcj.jieSuanPrice = rcj.marketPrice;
		}
		if (isDifference){
			//勾选价差调差标识
			rcj.updateIsDifference = true;
		}

		if (!JieSuanRcjStageUtils.isStage(unit)){
			return;
		}

		let num = null;
		if (isDifference){
			let rcjAdjustMethod = JieSuanRcjStageUtils.getRcjAdjustMethodByKind(unit,rcj.kind);
			num = rcjAdjustMethod.frequencyList.length;
		}else {
			num = JieSuanRcjStageUtils.periods(unit);
		}
		JieSuanRcjStageUtils.stageSetRcjInit(rcj,num);


	},

	//税率
	taxRate: ({rcj,ctx}) => {
		//修改税率后，通过含税市场价=不含税市场价*（1+税率%），
		let {taxRate} = rcj;
		let {isSimple} = ctx;

		if (isSimple){
			rcj.priceMarket = NumberUtil.numberScale2(NumberUtil.multiply(100,
				NumberUtil.divide(rcj.priceMarketTax,NumberUtil.add(100,taxRate))));
		}else {
			rcj.priceMarketTax = NumberUtil.numberScale2(NumberUtil.multiply(
				0.01,NumberUtil.multiply(rcj.priceMarket,NumberUtil.add(100,taxRate))));
		}
		//rcj.priceMarketTax = NumberUtil.numberScale2(NumberUtil.multiply(rcj.priceMarket,NumberUtil.add(1+NumberUtil.divide100(taxRate))));
		rcj.marketPrice = isSimple?rcj.priceMarketTax:rcj.priceMarket;

		if (rcj.priceMarketTax == rcj.priceBaseJournalTax){
			rcj.sourcePrice = null;
		}else {
			rcj.sourcePrice = "自行询价";
		}
	},
	//市场价
	marketPrice:async ({rcj,ctx,updateRule}) => {
		//判断当前人材机数据是 22还是12
		let {unit,arg,is2022,isSimple} = ctx;
		let {marketPrice} = rcj;
		//rcj.jieSuanPriceSource="自行询价";

		let differencePrice = null;
		if (ObjectUtils.isEmpty(arg.num)){
			differencePrice = rcj;
		}else {
			differencePrice = rcj.jieSuanRcjDifferenceTypeList[arg.num-1];
		}



		differencePrice.marketPrice=marketPrice;
		differencePrice.jieSuanPriceSource="自行询价";
		differencePrice.sourcePrice="自行询价";

		if (unit.isDifference){
			if (ObjectUtils.isEmpty(rcj.updateState)){
				rcj.jieSuanPrice = marketPrice;
			}
			rcj.jieSuanBasePrice = marketPrice;
		}
		//if (is2022) {
			isSimple ? differencePrice.priceMarketTax = differencePrice.marketPrice : differencePrice.priceMarket = differencePrice.marketPrice;
			if (ObjectUtils.isNotEmpty(arg.num)){
				isSimple ? rcj.priceMarketTax = rcj.marketPrice : rcj.priceMarket = rcj.marketPrice;
			}
			let fn = null;
			if (isSimple) {
				fn = updateRule["priceMarketTax"];
			} else {
				fn = updateRule["priceMarket"];
			}
			if (fn) {
				fn({rcj, ctx});
			}
		//}


	},

	//名称
	materialName: async({rcj,ctx}) => {
		let {constructProjectRcjs,rcjDetailList}= ctx;
		//原来逻辑 刷新甲供数量
		//刷新甲供数量
		ctx.jiagong_num = true;
		ctx.alike_material = true;
		////修改市场价
		//         await this.service.rcjProcess.marketPriceAlreadyHaveRcj(rcj,constructProjectRcjs,rcjDetailList);
	},

	//复用
	specification: async({rcj,ctx}) => {
		if (rcj.specification == ""){
			rcj.specification = null;
		}
		//原来逻辑 刷新甲供数量
		ctx.jiagong_num = true;
		ctx.updateCode = true;
		ctx.alike_material = true;
	},
	//复用
	kind: async({rcj,ctx}) => {
		let {kind} = rcj;
		let { service } = EE.app;
		let {constructProjectRcjs,rcjDetailList}= ctx;
		//材料
		if (kind == 2 || kind == 5){
			rcj.taxRemoval = rcj.taxRemovalBackUp;
			//设备
		}else if (kind == 4){
			rcj.taxRemoval = ConstructProjectRcjService.sbfTaxRemoval;
		}
		rcj.type = service.baseRcjService.getRcjTypeEnumDescByCode(kind);
		//刷新甲供数量
		ctx.jiagong_num = true;

	},
	//是否是暂估(0:不是，1：是) 复用
	ifProvisionalEstimate: async({rcj,ctx,isParent}) => {
		ctx.alike_material = true;
		//人材机 暂估价表处理
		ctx.zg_material = true;

	},
	//复用
	unit: async({rcj,ctx,de,oldValue,isParent,updateRule}) => {
		//修改rcj单位
		let orgUnitNum = Number.parseInt(oldValue);
		if (Number.isNaN(orgUnitNum)) {
			orgUnitNum = 1;
		}
		let nowUnitNum = Number.parseInt(rcj.unit);
		if (Number.isNaN(nowUnitNum)) {
			nowUnitNum = 1;
		}
		if (orgUnitNum !== nowUnitNum) {
			nowUnitNum = nowUnitNum ? nowUnitNum : 1;
			let resQty= NumberUtil.divide(
				rcj.resQty,
				nowUnitNum
			);
			rcj.resQty = resQty;
			let fn = updateRule["resQty"];
			if(fn){
				fn({rcj,ctx,de,isParent});
			}
		}
		//刷新甲供数量
		ctx.jiagong_num = true;
		ctx.updateCode = true;
		ctx.alike_material = true;
	},

	showMarketPrice: async({rcj,ctx,de,oldValue,isParent,updateRule}) => {
		rcj.marketPrice = rcj.showMarketPrice;
		rcj.total = NumberUtil.multiplyToString(rcj.showMarketPrice,
			rcj.totalNumber, 2);
		rcj.jieSuanPriceSource = "自行询价";
		if (rcj.marketPrice != rcj.dePrice){
			rcj.sourcePrice = '自行询价';
		}else {
			rcj.sourcePrice = null;
		}
		rcj.highlight = null;
	},
	totalNumber: async ({rcj,ctx,de,isParent}) => {
		let {constructProjectRcjs}= ctx;
		let { service } = EE.app;
		let coefficient = NumberUtil.divide(rcj.totalNumber,de.quantity);
		rcj.beforQty = rcj.resQty;
		if (isParent){
			rcj.resQty = NumberUtil.formatNumber(coefficient,4);
			rcj.consumerResQty = rcj.resQty;
		}else {
			let parentRcj = constructProjectRcjs.find(i => i.sequenceNbr === rcj.rcjId);
			rcj.resQty = NumberUtil.formatNumber(NumberUtil.multiply(parentRcj.resQty,coefficient),4);
		}
		updateDonorMaterialNumber(de,rcj,ctx,isParent);
		//修改合计数量会重新计算消耗量
		service.rcjProcess.updateRcjSyncDeConversionInfo(ctx.constructId, ctx.singleId, ctx.unitId,de.sequenceNbr,rcj,"updateQty",null,null,1);
	},

	resQty: ({rcj,ctx,de,isParent})=>{
		let { unit } = ctx;
		let { service } = EE.app;

		//todo 修改消耗量
		rcj.beforQty=rcj.consumerResQty?rcj.consumerResQty:rcj.initResQty;
		rcj.lastResQty = rcj.resQty;
		rcj.consumerResQty = rcj.resQty;
		rcj.consumerResQty = rcj.resQty;

		//todo 不知道此方法是否还有用
		service.conversionDeService.freezeRuleIds(rcj)
		if (rcj.kind3dType == 1) {
			rcj.resQtyChangeType = 1;
		}
		//todo 不知道此方法是否还有用
		if (!ObjectUtils.isEmpty(unit.conversionInfoList)) {
			let rules = unit.conversionInfoList.filter(f => f.deId === rcj.deId);
			if (rules && rules.length > 0) {
				if (!rcj.ruleDeActive) {
					rcj.ruleDeActive = {};
				}
				for (let i = 0; i < rules.length; ++i) {
					rcj.ruleDeActive[rules[i].ruleId] = true;
				}
			}
		}
		updateDonorMaterialNumber(de,rcj,ctx,isParent);
		//同步换算信息
		//处理人材机换算信息
		if(ObjectUtils.isEmpty(rcj.rcjId)){
			service.rcjProcess.updateRcjSyncDeConversionInfo(ctx.constructId, ctx.singleId, ctx.unitId,de.sequenceNbr,rcj,"updateQty",null,null,1);
		}

	},

	//是否是甲供(0:不是，1：是 2:甲定)
	ifDonorMaterial: ({rcj,ctx,de,isParent})=>{
		let {ifDonorMaterial} = rcj;
		if (ifDonorMaterial == 0 || ifDonorMaterial == 2) {
			//不是甲供 或者 甲定 数量为null
			rcj.donorMaterialNumber = null;
			rcj.donorMaterialNumberManage = null;
		} else if (ifDonorMaterial == 1) {
			//甲供 数量等于 人材机数量
			rcj.donorMaterialNumber = rcj.totalNumber;
			rcj.donorMaterialNumberManage = -1;
		}

	},

	//是否汇总(解析) 1代表解析 0代表不解决  默认是1 解析
	markSum: async({rcj,ctx,de,isParent})=>{
		// let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
		// await this.service.rcjProcess.changeMarkSum(unit, constructRcjArrayElement);

	},
	//甲供数量
	donorMaterialNumber: async({rcj,ctx,de,isParent})=>{
		let {donorMaterialNumber} = rcj;
		if (donorMaterialNumber === '') {
			rcj.ifDonorMaterial = 0;//是否是甲供(0:不是，1：是)
			rcj.donorMaterialNumber = null;
			rcj.donorMaterialNumberManage = null;
		} else {
			rcj.ifDonorMaterial = 1;
			//updateDonorMaterialNumber = donorMaterialNumber;
			if (donorMaterialNumber == rcj.totalNumber){
				rcj.donorMaterialNumberManage = -1;
			}else {
				rcj.donorMaterialNumberManage = donorMaterialNumber;
			}
			//暂存 符合甲供的人材机
			//donorMaterialList.push(constructRcjArrayElement);
		}

	},

	//基本价格指数F0
	jieSuanBasePriceF0: async({rcj,ctx,de,isParent})=>{
		let {num} = ctx.arg;
		let {jieSuanBasePriceF0} = rcj;

		if (ObjectUtils.isNotEmpty(num)){
			let rcjDifference = rcj.jieSuanRcjDifferenceTypeList[num -1];
			rcjDifference.jieSuanBasePriceF0=jieSuanBasePriceF0;
		}
	},

	//现行价格指数Ft
	jieSuanCurrentPriceFt: async({rcj,ctx,de,isParent})=>{
		let {num} = ctx.arg;
		let {jieSuanCurrentPriceFt} = rcj;
		if (ObjectUtils.isNotEmpty(num)){
			let rcjDifference = rcj.jieSuanRcjDifferenceTypeList[num -1];
			rcjDifference.jieSuanCurrentPriceFt=jieSuanCurrentPriceFt;
		}
	},

	//基期价
	jieSuanBasePrice: async({rcj,ctx,updateRule})=>{
		rcj.jieSuanBasePriceSource = "自行询价";
		let {unit,arg,is2022,isSimple} = ctx;
		let {jieSuanBasePrice} = rcj;

		if (is2022) {
			isSimple ? rcj.priceBaseJournalTax = rcj.jieSuanBasePrice : rcj.priceBaseJournal = rcj.jieSuanBasePrice;
			let fn = null;
			if (isSimple) {
				fn = updateRule["priceBaseJournalTax"];
			} else {
				fn = updateRule["priceBaseJournal"];
			}
			if (fn) {
				fn({rcj, ctx});
			}
		}




	},


	//基期价来源
	jieSuanBasePriceSource: async({rcj,ctx})=>{
		let {num} = ctx.arg;
		let {jieSuanBasePriceSource} = rcj;
		if (ObjectUtil.isEmpty(num)){
			rcj.jieSuanBasePriceSource=jieSuanBasePriceSource;
		}else {
			let rcjDifference = rcj.jieSuanRcjDifferenceTypeList[num -1];
			rcjDifference.jieSuanBasePriceSource=jieSuanBasePriceSource;
		}
	},


	//结算单价
	jieSuanPrice: async({rcj,ctx,de,isParent,updateRule})=>{
		let {num} = ctx.arg;
		let {jieSuanPrice} = rcj;
		let {unit,arg,is2022,isSimple} = ctx;
		if (ObjectUtils.isNotEmpty(num)){
			let rcjDifference = rcj.jieSuanRcjDifferenceTypeList[num -1];
			rcjDifference.jieSuanPrice=jieSuanPrice;
			rcjDifference.jieSuanPriceSource="自行询价";
		}

		isSimple ? rcj.jieSuanPriceMarketTax = rcj.jieSuanPrice : rcj.jieSuanPriceMarket = rcj.jieSuanPrice;
		let fn = null;
		if (isSimple) {
			fn = updateRule["jieSuanPriceMarketTax"];
		} else {
			fn = updateRule["jieSuanPriceMarket"];
		}
		if (fn) {
			fn({rcj, ctx});
		}
		//价差修改结算单价标识
		rcj.updateState = true;

	},
	//结算单价来源
	jieSuanPriceSource: async({rcj,ctx,de,isParent})=>{
		let {arg} = ctx;
		let {jieSuanPriceSource} = rcj;
		if (ObjectUtils.isNotEmpty(arg.num)){
			let rcjDifference = rcj.jieSuanRcjDifferenceTypeList[arg.num -1];
			rcjDifference.jieSuanPriceSource=jieSuanPriceSource;
		}
	},

	//结算第n期除税系数 taxRate
	jieSuanStagetaxRemoval: async({rcj,ctx,de,isParent})=>{
		let {num} = ctx.arg;
		let {jieSuanStagetaxRemoval} = rcj;
		if (ObjectUtils.isNotEmpty(num)){
			let rcjDifference = rcj.jieSuanRcjDifferenceTypeList[num -1];
			rcjDifference.jieSuanStagetaxRemoval=jieSuanStagetaxRemoval;
		}
	},

	//结算除税系数
	taxRemoval: async({rcj,ctx,de,isParent})=>{
		let {num} = ctx.arg;
		let {taxRemoval} = rcj;
		if (ObjectUtils.isNotEmpty(num)){
			let rcjDifference = rcj.jieSuanRcjDifferenceTypeList[num -1];
			rcjDifference.taxRemoval=taxRemoval;
		}
	},



	//不含税基期价'
	priceBaseJournal: async({rcj,ctx})=>{
		let {is2022,isSimple} = ctx;
		//含税基期价=不含税基期价（1+合同税率）
		rcj.priceBaseJournalTax = NumberUtil.multiply(rcj.priceBaseJournal,NumberUtil.add(1,NumberUtil.divide100(rcj.jieSuantaxRate)));
		rcj.jieSuanBasePriceSource = "自行询价";
		if (is2022){
			if (isSimple){
				rcj.jieSuanBasePrice =rcj.priceBaseJournalTax;
			}else {
				rcj.jieSuanBasePrice =rcj.priceBaseJournal;
			}
		}
	},

	//'含税基期价',
	priceBaseJournalTax: async({rcj,ctx})=>{
		let {is2022,isSimple} = ctx;
		//不含税基期价=含税基期价/（1+结算税率））
		rcj.priceBaseJournal = NumberUtil.numberScale(NumberUtil.divide(rcj.priceBaseJournalTax,NumberUtil.add(1,NumberUtil.divide100(rcj.taxRate))),2);
		rcj.jieSuanBasePriceSource = "自行询价";
		if (is2022){
			if (isSimple){
				rcj.jieSuanBasePrice =rcj.priceBaseJournalTax;
			}else {
				rcj.jieSuanBasePrice =rcj.priceBaseJournal;
			}
		}
	},


	//第n期含税单价'
	priceMarketTax: async({rcj,ctx,updateRule})=>{
		let {unit,arg,is2022,isSimple} = ctx;
		let {priceMarketTax} = rcj;
		//let rcjDifference = rcj.jieSuanRcjDifferenceTypeList.find(k =>k.rcjDifferenceType == arg.adjustMethod);
		if (ObjectUtil.isEmpty(arg.num)){
			rcj.priceMarketTax=priceMarketTax;
			rcj.jieSuanPriceSource="自行询价";
			rcj.sourcePrice="自行询价";
			rcj.priceMarket = NumberUtil.numberScale2(NumberUtil.divide(rcj.priceMarketTax,NumberUtil.add(1+NumberUtil.divide100(rcj.taxRate))));
			rcj.marketPrice = isSimple?rcj.priceMarketTax:rcj.priceMarket;
			if (ObjectUtils.isEmpty(unit.originalFlag) || unit.originalFlag == false){
				if (ObjectUtils.isNotEmpty(rcj.updateIsDifference)&&rcj.updateIsDifference == true){
					rcj.priceBaseJournalTax = priceMarketTax;
					fn = updateRule["priceBaseJournalTax"];
					if (fn) {
						fn({rcj, ctx});
					}
				}
			}

			return;
		}
		let differencePrice = rcj.jieSuanRcjDifferenceTypeList[arg.num-1];
		differencePrice.priceMarketTax=priceMarketTax;
		differencePrice.jieSuanPriceSource="自行询价";
		differencePrice.priceMarket = NumberUtil.numberScale2(NumberUtil.divide(differencePrice.priceMarketTax,
			NumberUtil.add(1+NumberUtil.divide100(differencePrice.taxRate))));
		differencePrice.marketPrice = isSimple?differencePrice.priceMarketTax:differencePrice.priceMarket;

	},

	//'第n期不含税单价',
	priceMarket: async({rcj,ctx,de,updateRule})=>{
		let {unit,arg,isSimple} = ctx;
		let {priceMarket} = rcj;
		if (ObjectUtil.isEmpty(arg.num)){
			rcj.priceMarket=priceMarket;
			rcj.jieSuanPriceSource="自行询价";
			rcj.sourcePrice="自行询价";
			rcj.priceMarketTax = NumberUtil.numberScale2(NumberUtil.multiply(rcj.priceMarket,NumberUtil.add(1+NumberUtil.divide100(rcj.taxRate))));
			rcj.marketPrice = isSimple?rcj.priceMarketTax:rcj.priceMarket;
			if (ObjectUtils.isEmpty(unit.originalFlag) || unit.originalFlag == false) {
				if (ObjectUtils.isNotEmpty(rcj.updateIsDifference)&&rcj.updateIsDifference == true){
					rcj.priceBaseJournal = priceMarket;
                    fn = updateRule["priceBaseJournal"];
                    if (fn) {
                        fn({rcj, ctx});
                    }
			    }
			}

			return;
		}
		let differencePrice = rcj.jieSuanRcjDifferenceTypeList[arg.num-1];
		differencePrice.priceMarket=priceMarket;
		differencePrice.jieSuanPriceSource="自行询价";
		differencePrice.priceMarketTax = NumberUtil.numberScale2(NumberUtil.multiply(differencePrice.priceMarket,NumberUtil.add(1+NumberUtil.divide100(differencePrice.taxRate))));
		differencePrice.marketPrice = isSimple?differencePrice.priceMarketTax:differencePrice.priceMarket;

	},



	//结算含税单价'
	jieSuanPriceMarketTax: async({rcj,ctx})=>{
		let {unit,arg,is2022,isSimple} = ctx;
		let {jieSuanPriceMarketTax} = rcj;
		rcj.jieSuanPriceMarket = NumberUtil.numberScale2(NumberUtil.divide(rcj.jieSuanPriceMarketTax,
			NumberUtil.add(1+NumberUtil.divide100(rcj.taxRate))));
		rcj.jieSuanPrice = isSimple?rcj.jieSuanPriceMarketTax:rcj.jieSuanPriceMarket;
	},

	//结算不含税单价',
	jieSuanPriceMarket: async({rcj,ctx,de,isParent})=>{
		let {unit,arg,isSimple} = ctx;
		let {jieSuanPriceMarket} = rcj;
		rcj.jieSuanPriceMarketTax = NumberUtil.numberScale2(NumberUtil.multiply(rcj.jieSuanPriceMarket,NumberUtil.add(1+NumberUtil.divide100(rcj.taxRate))));
		rcj.jieSuanPrice = isSimple?rcj.jieSuanPriceMarketTax:rcj.jieSuanPriceMarket;
	},

	//人材机工料指标
	indicatorName: async({rcj,ctx,updateRule})=>{

		//处理单位
		let find = gongLiaoList[rcj.indicatorNameOfMajor].find(gcl=> gcl.工程量指标列表==rcj.indicatorName );
		if(rcj.indicatorName=="无"){
			rcj.indicatorName=null;
		}
		rcj.quantityIndicatorUnit = ObjectUtils.isNotEmpty(find)?find.工程量指标单位:null;

	},
	jieSuantaxRate: ({rcj,ctx}) => {
		//修改税率后，通过含税市场价=不含税市场价*（1+税率%），
		let {jieSuantaxRate} = rcj;
		let {isSimple} = ctx;

		if (isSimple){
			rcj.jieSuanPriceMarket = NumberUtil.numberScale2(NumberUtil.multiply(100,
				NumberUtil.divide(rcj.jieSuanPriceMarketTax,NumberUtil.add(100,jieSuantaxRate))));
		}else {
			rcj.jieSuanPriceMarketTax = NumberUtil.numberScale2(NumberUtil.multiply(
				0.01,NumberUtil.multiply(rcj.jieSuanPriceMarket,NumberUtil.add(100,jieSuantaxRate))));
		}
		//rcj.priceMarketTax = NumberUtil.numberScale2(NumberUtil.multiply(rcj.priceMarket,NumberUtil.add(1+NumberUtil.divide100(taxRate))));
		rcj.jieSuanPrice = isSimple?rcj.jieSuanPriceMarketTax:rcj.jieSuanPriceMarket;

		if (rcj.jieSuanPriceMarketTax == rcj.priceBaseJournalTax){
			rcj.jieSuanPriceSource = null;
		}else {
			rcj.jieSuanPriceSource = "自行询价";
		}
	},
	// indicatorUnit: async({rcj,ctx})=>{
	// 	//处理单位
	// 	let find = gongLiaoList[rcj.indicatorNameOfMajor].find(gcl=> gcl.工程量指标列表==rcj.indicatorName );
	// 	pointLine.quantityIndicatorUnit = ObjectUtils.isNotEmpty(find)?find.工程量指标单位:null;
	// },

};




module.exports = {jieSuanupdateRcjRules};
