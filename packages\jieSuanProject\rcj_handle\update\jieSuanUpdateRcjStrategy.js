const {RcjApplicationContext} = require("../../../../electron/rcj_handle/RcjContext");
const {updateRcjRulesDetails} = require("../../../../electron/rcj_handle/rules/updateRcjRules");
const {ObjectUtils} = require("../../../../electron/utils/ObjectUtils");
const {jieSuanupdateRcjRules} = require("../rules/jieSuanupdateRcjRules");
const {RcjHandleFactory} = require("../RcjHandleFactory");
const {PricingFileFindUtils} = require("../../../../electron/utils/PricingFileFindUtils");
const {JieSuanRcjStageUtils} = require("../../utils/JieSuanRcjStageUtils");
const {ObjectUtil} = require("../../../../common/ObjectUtil");
const {UnitRcjCacheUtil} = require("../../../../electron/rcj_handle/cache/UnitRcjCacheUtil");


class JieSuanUpdateRcjStrategy extends RcjApplicationContext {
    constructor({constructId, singleId, unitId,projectObj,arg}) {
        super({constructId, singleId, unitId, projectObj});
        this.rcj = null;
        //this.de = this.allData.getNodeById(upDateInfo.pointLine.sequenceNbr);
        //this.allData= this.unit[arg.pageType == "fbfx"?"itemBillProjects":"measureProjectTables"];
        //编码修改
        this.updateCode = false;
        this.arg = arg;
        //计算人材机
        this.calculate = false;
        //更新相同材料
        this.alike_material = false;
        //刷新甲供数量
        this.jiagong_num = false;
        //需要处理的人材机集合
        this.handleRcjList = [];


    }
    getHandler(updateInfo){
        return new JieSuanCollectRcjUpdateHandler(this,updateInfo);
    }

    getDeForRcj(constructId, singleId,unitId,constructProjectRcj){
        let unit = this.unit;
        let itemBillProjects = unit.itemBillProjects;
        let t = itemBillProjects.find(i=>i.sequenceNbr === constructProjectRcj.deId);
        if (!ObjectUtils.isEmpty(t)){
            return t;
        }else {
            let measureProjectTables = unit.measureProjectTables;
            let t1 = measureProjectTables.find(i=>i.sequenceNbr === constructProjectRcj.deId);

            if (!ObjectUtils.isEmpty(t1)){
                return t1;
            }else {
                return null;
            }

        }
    }

    //获取需要处理的人材机集合
    getHandleRcjList(rcj){
        let {levelType} = this.arg;
        let rcjArray = [];
        //查询 单位工程汇总
        if (levelType == 3) {
            let unit = PricingFileFindUtils.getUnit(this.constructId, this.singleId, this.unitId);
            if (!ObjectUtils.isEmpty(unit)) {
                if (!ObjectUtils.isEmpty(unit.constructProjectRcjs)) {
                    rcjArray.push(...unit.constructProjectRcjs);
                }
                if (!ObjectUtils.isEmpty(unit.rcjDetailList)) {
                    rcjArray.push(...unit.rcjDetailList);
                }
            }
        }
        if (levelType == 1) {
            //查询工程项目汇总
            let unitList = PricingFileFindUtils.getUnitList(this.constructId);
            for (let unitListKey of unitList) {
                if (!ObjectUtils.isEmpty(unitListKey.constructProjectRcjs)) {
                    rcjArray.push(...unitListKey.constructProjectRcjs);
                }
                if (!ObjectUtils.isEmpty(unitListKey.rcjDetailList)) {
                    rcjArray.push(...unitListKey.rcjDetailList);
                }
            }
        }
        if (levelType == 2) {
            //查询单项项目汇总
            let unitList = PricingFileFindUtils.getUnitListBySingle(this.constructId,this.singleId);
            for (let unitListKey of unitList) {
                if (!ObjectUtils.isEmpty(unitListKey.constructProjectRcjs)) {
                    rcjArray.push(...unitListKey.constructProjectRcjs);
                }
                if (!ObjectUtils.isEmpty(unitListKey.rcjDetailList)) {
                    rcjArray.push(...unitListKey.rcjDetailList);
                }
            }
        }

        //let copyRcjArray = ObjectUtil.cloneDeep(rcjArray);
        let tempArray = [];
        //拼接相同材料
        for (let item of rcjArray) {
            if (JieSuanRcjStageUtils.indexJoint(levelType,item,this.arg.kind,this.arg.type) == JieSuanRcjStageUtils.indexJoint(levelType,rcj,this.arg.kind,this.arg.type)){
                //this.handleRcjList.push(item);
                tempArray.push(item);
            }
        }
        return tempArray;

    }




   async execute({pointLine,upDateInfo,type,initCode, initRcj}) {
        this.handleRcjList = this.getHandleRcjList(pointLine);
        let handler = this.getHandler(upDateInfo);
        await handler.update();

       for (let rcj of this.handleRcjList) {
           await this.after(rcj,type,initCode, initRcj);
       }
    }
    async after(rcj,type,initCode, initRcj) {

        // let { service } = EE.app;
        //
        // //单位工程整体刷新甲供数量
        // await service.rcjProcess.donorMaterialNumberRefresh(this.constructId,this.singleId,this.unitId);
        //更新相同材料
        if (this.alike_material){
            let temp = [];
            temp.push(...this.constructProjectRcjs);
            temp.push(...this.rcjDetailList);
            let changeRcjDeId = super.changeSimilarlyRcj(rcj,temp,this.is2022,this.isSimple);
            if (ObjectUtils.isNotEmpty(changeRcjDeId)){
                this.changeDeList = changeRcjDeId;
            }
            this.alike_material = false;
        }
        //暂估价表处理
        if (this.zg_material){
            let temp = [];
            temp.push(...this.constructProjectRcjs);
            //temp.push(...this.rcjDetailList);
            super.changeRcjZg(rcj, temp);

        }

        let {constructId, singleId, unitId,deId} = rcj;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        //计算定额
        let fbfxDe = unit.itemBillProjects.getNodeById(deId);
        if (ObjectUtils.isNotEmpty(fbfxDe))await super.calculate(fbfxDe);
        let csxmDe = unit.measureProjectTables.getNodeById(deId);
        if (ObjectUtils.isNotEmpty(csxmDe))await super.calculate(csxmDe);


        //重新计算人材机
        // for (let i = 0; i < this.changeDeList.length; i++) {
        //     //计算定额
        //     let fbfxDe = this.unit.itemBillProjects.getNodeById(this.changeDeList[i]);
        //     if (ObjectUtils.isNotEmpty(fbfxDe))await super.calculate(fbfxDe);
        //     let csxmDe = this.unit.measureProjectTables.getNodeById(this.changeDeList[i]);
        //     if (ObjectUtils.isNotEmpty(csxmDe))await super.calculate(csxmDe);
        // }
        if(ObjectUtils.isEmpty(initRcj)){
            initRcj=ObjectUtils.cloneDeep(rcj);
        }
        //处理编码
        //await super.materialCodeHandler(rcj);


    }
}


class JieSuanCollectRcjUpdateHandler extends JieSuanUpdateRcjStrategy{
    constructor(ctx,upDateInfo) {
        let {constructId, singleId, unitId,arg} =ctx;
        super({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId),arg});
        this.ctx = ctx;
        this.unit = ctx.unit;
        this.rcj = null;
        this.handleRcjList = ctx.handleRcjList;
        this.upDateInfo = upDateInfo;
        this.updateRule = jieSuanupdateRcjRules;
        //this.ctx.changeDeList.push(this.rcj.deId);
    }
    async update() {
        const keys = Object.keys(this.upDateInfo);
        for (const key of keys) {
            for (let rcj of this.handleRcjList) {
                this.rcj = rcj;
                //this.oldValue = this.rcj[key];
                if (ObjectUtils.isNumberStr(this.upDateInfo[key])){
                    this.rcj[key] = +this.upDateInfo[key];
                }else {
                    this.rcj[key] = this.upDateInfo[key];
                }
                let fn  =this.updateRule[key];
                if('kind'==key){
                    //await this.handleKindUpdate(key);
                }
                if(fn){
                    await fn(this);
                }
            }
        }
        //return this.rcj;
    }

    async updateByRcj(rcj) {
        const keys = Object.keys(this.upDateInfo);
        this.rcj = rcj;
        for (const key of keys) {
            if (ObjectUtils.isNumberStr(this.upDateInfo[key])){
                this.rcj[key] = +this.upDateInfo[key];
            }else {
                this.rcj[key] = this.upDateInfo[key];
            }
            let fn  =this.updateRule[key];
            if('kind'==key){
                //await this.handleKindUpdate(key);
            }
            if(fn){
                await fn(this);
            }
        }
        super.after(rcj);

    }

}
module.exports = {JieSuanUpdateRcjStrategy,JieSuanCollectRcjUpdateHandler};
