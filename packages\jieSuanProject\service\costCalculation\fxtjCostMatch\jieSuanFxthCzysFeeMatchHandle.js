'use strict';

const FxthCzysFeeMatchHandle = require("../../../../../electron/service/costCalculation/fxtjCostMatch/fxthCzysFeeMatchHandle");
const {PricingFileFindUtils} = require("../../../../../electron/utils/PricingFileFindUtils");
const {JieSuanCostMatchUtil} = require("../jieSuanCostMatchUtil");
const { BaseDe2022, BaseDe } = require('../../../../../electron/model/BaseDe');
const {BaseDeFwxsCgRelation2022} = require("../../../../../electron/model/BaseDeFwxsCgRelation2022");
const {BaseDeFwxsCgRelation} = require("../../../../../electron/model/BaseDeFwxsCgRelation");
const FxtjCostConstants = require("../../../../../electron/service/costCalculation/fxtjCostMatch/FxtjCostConstants");
const DePropertyTypeConstant = require("../../../../../electron/enum/DePropertyTypeConstant");
const EE = require("../../../../../core/ee");

/**
 * 房修土建  垂直运输费 记取
 */
class JieSuanFxthCzysFeeMatchHandle extends FxthCzysFeeMatchHandle {

  constructor() {
    super();
  }

  async customizeConfirmCostDe(unit, baseDeArr, args, qdNode) {
    // 垂直运输费只会添加一条费用定额
    const is22Unit = PricingFileFindUtils.is22Unit(unit);
    let result = {
      'costDeArr': [],
      'costDeBaseDe': {}
    };
    let costDeArr = [];
    let costDeBaseDe = {};
    // 本次要添加的费用定额对应的基数定额
    const baseDeFwxsCgRelation = await EE.app.appDataSource.manager.getRepository(is22Unit ? BaseDeFwxsCgRelation2022 : BaseDeFwxsCgRelation).findOne({
      where: { value: FxtjCostConstants.CZYS }
    });
    const baseDe = await EE.app.appDataSource.manager.getRepository(is22Unit ? BaseDe2022 : BaseDe).findOne({
      where: {
        libraryCode: baseDeFwxsCgRelation.libraryCode,
        deCode: baseDeFwxsCgRelation.deCode,
        deName: baseDeFwxsCgRelation.deName
      }
    });
    let costDe = JieSuanCostMatchUtil.getFxtjCostDeByBaseDe(baseDe, DePropertyTypeConstant.FXTJ_CZYS);
    const newCostDe = await JieSuanCostMatchUtil.confirmQdAddCostDe(unit, qdNode, costDe, args.constructionMeasureType);
    costDeArr.push(newCostDe);
    costDeBaseDe[newCostDe.sequenceNbr] = baseDeArr;

    result.costDeArr = costDeArr;
    result.costDeBaseDe = costDeBaseDe;
    return result;
  }


}

JieSuanFxthCzysFeeMatchHandle.toString = () => '[class JieSuanFxthCzysFeeMatchHandle]';
module.exports = JieSuanFxthCzysFeeMatchHandle;
