

const OtherProjectCalculationBaseConstant = require("../enum/OtherProjectCalculationBaseConstant");
const { OtherProjectQzAndSuoPei } = require('../model/OtherProjectQzAndSuoPei');
const {Service} = require("../../core");
const {Snowflake} = require("../utils/Snowflake");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {NumberUtil} = require("../utils/NumberUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ConvertUtil} = require("../utils/ConvertUtils");

const { getConnection ,getRepository,getManager  } =require('typeorm');
class OtherProjectQzspService extends Service{

    //默认数量
    static defaultAmount = 1.0;
    //默认单价
    static defaultPrice  = 0;

    //默认除税系数
    static defaultTaxRemoval = 3;
    //默认进项合计
    static defaultJxTotal = 0;
    //默认除税单价
    static defaultCsPrice  = 0 ;
    //默认除税合价
    static defaultCsTotal = 0;
    //默认单位
    static defaultUnit  = "项";

    //计算保留小数位
    static decimalPlaces = 2;

    constructor(ctx) {
        super(ctx);
    }

    //专业工程暂估价操作
    otherProjectQzsp(arg){

        //操作 类型  1:插入 2:粘贴 3删除 4 修改
        let operateType = arg.operateType;

        switch (operateType) {
            case 1:
                this.addOtherProjectQzsp(arg);
                break;
            case 2:
                this.pasteOtherProjectQzsp(arg);
                break;
            case 3:
                this.delectOtherProjectQzsp(arg);
                break;
            case 4:
                this.updateOtherProjectQzsp(arg);
                break;
        }

    }


    //插入
    addOtherProjectQzsp(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        this.addOtherProjectQzspReal(unit, targetSequenceNbr)
    }

    async addQzsp(qzsp, unitProject){
        let otherProjectQzAndSuoPeisArr = new Array();
        if(qzsp !== undefined){
            for (let i = 0; i < qzsp.length; i++) {
                let model = qzsp[i];
                let otherProjectQzAndSuoPei = new OtherProjectQzAndSuoPei();
                otherProjectQzAndSuoPei.sequenceNbr = Snowflake.nextId();
                otherProjectQzAndSuoPei.dispNo = model.序号;
                otherProjectQzAndSuoPei.project = model.签证及索赔项目;
                otherProjectQzAndSuoPei.unit = model.计量单位;
                otherProjectQzAndSuoPei.type = model.签证及索赔项目;
                otherProjectQzAndSuoPei.amount = model.数量;
                otherProjectQzAndSuoPei.price = model.综合单价;
                otherProjectQzAndSuoPei.zhPrice = model.合价;
                otherProjectQzAndSuoPei.total = model.合价;
                otherProjectQzAndSuoPei.qzspRelyOn = model.签证及索赔依据;
                otherProjectQzAndSuoPeisArr.push(otherProjectQzAndSuoPei);
            }
        }else{
            let otherProjectQzAndSuoPei = new OtherProjectQzAndSuoPei();
            otherProjectQzAndSuoPei.sequenceNbr = Snowflake.nextId();
            otherProjectQzAndSuoPei.amount = 1;
            otherProjectQzAndSuoPei.price = 0;
            otherProjectQzAndSuoPei.zhPrice = 0;
            otherProjectQzAndSuoPei.total = 0;
            otherProjectQzAndSuoPeisArr.push(otherProjectQzAndSuoPei);
        }
        unitProject.otherProjectQzAndSuoPeis = otherProjectQzAndSuoPeisArr;
    }

    addOtherProjectQzspReal(unit, targetSequenceNbr){
        let list = unit.otherProjectQzAndSuoPeis;

        let number;
        if (!ObjectUtils.isEmpty(targetSequenceNbr) && list.length !== 0) {
            number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);
        }else {
            number = null;
        }
        // let iOtherProjectQzSpJjb = this.service.otherProjectService.getIOtherProjectQzSpJjb();
        let otherProjectQzAndSuoPei = new OtherProjectQzAndSuoPei();
        otherProjectQzAndSuoPei.sequenceNbr = Snowflake.nextId();
        otherProjectQzAndSuoPei.amount = 1;
        otherProjectQzAndSuoPei.price = 0;
        otherProjectQzAndSuoPei.zhPrice = 0;
        otherProjectQzAndSuoPei.total = 0;

        if ( number !== null) {
            list.splice(number+1, 0, otherProjectQzAndSuoPei);
        }else {
            if (ObjectUtils.isEmpty(list)){
                unit.otherProjectQzAndSuoPeis = new Array();
                unit.otherProjectQzAndSuoPeis.push(otherProjectQzAndSuoPei);

            }else {
                list.push(otherProjectQzAndSuoPei) ;
            }
        }

    }

    //粘贴
    pasteOtherProjectQzsp(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;
        let projectQzsp = arg.projectQzsp;
        let otherProjectQzAndSuoPei = new OtherProjectQzAndSuoPei();
        ConvertUtil.setDstBySrc(projectQzsp,otherProjectQzAndSuoPei);
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectQzAndSuoPeis;


        let number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);

        otherProjectQzAndSuoPei.sequenceNbr = Snowflake.nextId();
        list.splice(number +1,0,otherProjectQzAndSuoPei);

        // this.updateOtherProjectQzspTotal(arg);
        this.service.otherProjectService.updateAllOtherProjects(arg);

    }



    //删除
    delectOtherProjectQzsp(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectQzAndSuoPeis;
        let number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);
        list.splice(number,1);

        // this.updateOtherProjectQzspTotal(arg);
        this.service.otherProjectService.updateAllOtherProjects(arg);
    }


    //编辑
    updateOtherProjectQzsp(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;

        let project = arg.projectQzsp.project;
        let price = arg.projectQzsp.price;
        let zhPrice = arg.projectQzsp.zhPrice;
        let amount =  NumberUtil.qDDeAmountFormat(arg.projectQzsp.amount);
        let dispNo = arg.projectQzsp.dispNo;
        let description = arg.projectQzsp.description;
        let unitBj = arg.projectQzsp.unit;
        let qzspRelyOn = arg.projectQzsp.qzspRelyOn;
        let type = arg.projectQzsp.type;


        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list =  unit.otherProjectQzAndSuoPeis;
        let projectQzsp = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
        if (!ObjectUtils.isEmpty(project)){
            projectQzsp.project = project;
        }

        if (!ObjectUtils.isEmpty(price)){
            projectQzsp.price = price;
        }

        if (!ObjectUtils.isEmpty(zhPrice)){
            projectQzsp.zhPrice = zhPrice;
        }

        if (!ObjectUtils.isEmpty(amount)){
            projectQzsp.amount = amount;
        }

        if (!ObjectUtils.isEmpty(type)){
            projectQzsp.type = type;
        }

        if (!ObjectUtils.isEmpty(dispNo)){
            projectQzsp.dispNo = dispNo;
        }

        if (!ObjectUtils.isEmpty(description)){
            projectQzsp.description = description;
        }

        if (!ObjectUtils.isEmpty(unitBj)){
            projectQzsp.unit = unitBj;
        }

        if (!ObjectUtils.isEmpty(qzspRelyOn)){
            projectQzsp.qzspRelyOn = qzspRelyOn;
        }
        //计算
        //计算合价【金额】=【综合单价】*【数量】
        projectQzsp.total = NumberUtil.costPriceAmountFormat(NumberUtil.multiplyParams(projectQzsp.zhPrice,projectQzsp.amount));

        // //除税系数默认=3%，【进项税合计】=暂列金额*除税系数%；【除税单价】=单价*（1-除税系数%）；【除税合价】=【暂列金额】*（1-除税系数%）
        // let multiply = NumberUtil.multiply(projectQzsp.taxRemoval,0.01);
        // //进项税合计
        // projectQzsp.jxTotal = NumberUtil.multiplyToString(multiply,projectZygcZgjs.total,OtherProjectZgjService.decimalPlaces);
        // let subtract = NumberUtil.subtract(1,multiply);
        // //除税单价
        // projectQzsp.csPrice = NumberUtil.multiplyToString(projectZygcZgjs.price,subtract,OtherProjectZgjService.decimalPlaces);
        // //除税合价
        // projectQzsp.csTotal = NumberUtil.multiplyToString(projectZygcZgjs.total,subtract,OtherProjectZgjService.decimalPlaces);

        // this.updateOtherProjectQzspTotal(arg);
        this.service.otherProjectService.updateAllOtherProjects(arg);
    }


}
OtherProjectQzspService.toString = () => '[class OtherProjectQzspService]';
module.exports = OtherProjectQzspService;