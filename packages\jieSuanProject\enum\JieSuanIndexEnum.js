class JieSuanIndexEnum {

    static TYPE1 = {code:1,desc:"主要经济指标"};
    static TYPE2 = {code:2,desc:"主要工程量指标"};
    static TYPE3 = {code:3,desc:"主要工料指标"};


    // 静态方法：根据code获取枚举对象
   static getEnumByCode(code) {
        for (let key in JieSuanIndexEnum) {
            if (JieSuanIndexEnum.hasOwnProperty(key) && typeof JieSuanIndexEnum[key] === 'object' && JieSuanIndexEnum[key].code === code) {
                return JieSuanIndexEnum[key];
            }
        }
        return null; // 如果找不到匹配的code，返回null
    }
}
module.exports = JieSuanIndexEnum
