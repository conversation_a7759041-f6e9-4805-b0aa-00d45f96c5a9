// const {ResponseData} = require("../../../electron/utils/ResponseData");
// const {Controller} = require("../../../core");
// const JieSuanOtherProjectQzspService = require("../service/JieSuanOtherProjectQzspService");
// class JieSuanOtherProjectQzspController extends Controller{
//
//
//     /**
//      * 构造函数
//      * @param ctx
//      */
//     constructor(ctx) {
//         super(ctx);
//         this.jieSuanOtherProjectQzspService = new JieSuanOtherProjectQzspService(ctx);
//     }
//
//     /**
//      * 签证与索赔计价表 操作
//      */
//     async otherProjectQzsp(arg) {
//
//         await this.jieSuanOtherProjectQzspService.otherProjectQzsp(arg);
//
//         return ResponseData.success(null);
//     }
//
// }
//
// JieSuanOtherProjectQzspController.toString = () => '[class JieSuanOtherProjectQzspController]';
// module.exports = JieSuanOtherProjectQzspController;