<!--
 * @Descripttion: 载价编辑
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2025-04-08 10:39:52
-->
<template>
  <div class="content">
    <div style="display: flex;align-items: center">
      <priority-list class="priority" :priorityData="props.propsData.priorityData" @getFinList="getFinList" ></priority-list>
      <!--  && store.deStandardReleaseYear === '22' -->
      <template v-if="store.type === 'jieSuan' && taxList.length > 0 " >
        <a-checkbox-group v-model:value="taxList">
          <a-checkbox value="tax" v-if="taxList.includes('tax')" disabled>载入含税价</a-checkbox>
          <a-checkbox value="noTax" v-if="taxList.includes('noTax')" disabled>载入不含税价</a-checkbox>
          <a-checkbox value="taxRate" v-if="taxList.includes('taxRate')" disabled>载入税率</a-checkbox>
        </a-checkbox-group>
      </template>
    </div>
    <div class="typeLine">
      <p class="typeSelect">
        <span> 类型筛选 </span>
        <a-checkbox-group
          @change="typeChange"
          v-model:value="typeSelectValue"
          name="checkboxgroup"
          :options="plainOptions"
        />
      </p>
      <p class="total">
        执行载价条数/总条数：<span>{{ editInfo.loadNumber }}/{{ editInfo.total }}</span>
      </p>
    </div>

    <vxe-table
      ref="tableEdit"
      align="center"
      :column-config="{ resizable: true, isCurrent: false }"
      :row-config="{ isHover: false, isCurrent: true }"
      :mouse-config="{ selected: true }"
      :edit-config="{ mode: 'cell',trigger: 'click' }"
      :data="tableData"
      height="70%"
      :cell-class-name="cellClassName"
      @cell-dblclick="dblclickItem"
      @checkbox-all="updateLoadNum"
      @checkbox-change="updateLoadNum"
      :scroll-y="{ scrollToTopOnChange: true }"
      keep-source
    >
      <vxe-column
        type="checkbox"
        min-width="91"
        title="执行载价"
      ></vxe-column>
      <vxe-column
        type="seq"
        min-width="45"
        title="序号"
      > </vxe-column>
      <vxe-column
        field="materialCode"
        min-width="75"
        title="材料编码"
      >
      </vxe-column>
      <vxe-column
        field="type"
        min-width="60"
        title="类型"
      > </vxe-column>
      <vxe-column
        field="materialName"
        min-width="80"
        title="名称"
        show-overflow
      >
      </vxe-column>
      <vxe-column
        field="specification"
        min-width="70"
        title="规格型号"
        show-overflow
      >
      </vxe-column>

      <vxe-column
        field="unit"
        min-width="50"
        title="单位"
      > </vxe-column>
      <vxe-column
        field="totalNumber"
        min-width="80"
        title="数量"
        sortable
        :title-help="{
          message: '展示工程项目或当前单位工程该材料的使用数量',
        }"
      >
      </vxe-column>

      <vxe-column
          field="loadPrice"
          min-width="110"
          :title="loadPriceTitle"
          sortable
          :title-help="{ message: '优先匹配信息价 您可通过双击价格更新待载价格' }"
          :edit-render="{}"
      >
        <template #default="{ row, column }">
          <span style="float:right">{{ row.loadPrice }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-input
            v-model="row.loadPrice"
            type="text"
            @blur="updateBlurEvent({ row,column })"
          ></vxe-input>
        </template>
      </vxe-column>
      <!--  结算中 税率添加  && store.deStandardReleaseYear === '22' -->
      <vxe-column  field="taxRate"  min-width="110" title="税率" v-if="store.type === 'jieSuan'">
        <template #default="{ row, column }">
          <span v-if="taxList.includes('taxRate')  && store.deStandardReleaseYear === '22'">{{ row.taxRate }}</span>
        </template>
      </vxe-column>

      <vxe-column
        field="sourcePrice"
        min-width="200"
        title="价格来源"
        show-overflow
      >
      </vxe-column>

      <vxe-column
        field="informationPrice"
        width="75"
        title="信息价"
        sortable
      >
        <template #default="{ row, column }">
          <span style="float:right">
            {{ row.informationPrice
          }}<icon-font
              type="icon-sousuoicon"
              class="iconFont"
              v-if="row.informationPriceList?.length > 0"
              @click="openPriceSelect(row, column, '信息价')"
            ></icon-font>
          </span>
        </template>
      </vxe-column>
      <vxe-column
        field="marketPrice"
        width="75"
        title="市场价"
        sortable
      >
        <template #default="{ row, column }">
          <span style="float:right">
            {{ row.marketPrice }}
            <icon-font
              type="icon-sousuoicon"
              class="iconFont"
              v-if="row.marketPriceList?.length > 0"
              @click="openPriceSelect(row, column, '市场价')"
            ></icon-font>
          </span>
        </template></vxe-column>
      <vxe-column
        field="recommendPrice"
        width="75"
        title="推荐价"
        sortable
      >
        <template #default="{ row, column }">
          <span style="float:right">
            {{ row.recommendPrice }}
            <icon-font
              type="icon-sousuoicon"
              class="iconFont"
              @click="openPriceSelect(row, column, '推荐价')"
              v-if="row.recommendPriceList?.length > 0"
            ></icon-font>
          </span>
          <!-- v-if="row.recommendPriceList?.length > 0" -->
        </template></vxe-column>
    </vxe-table>
    <div class="totalStaic">
      <div>
        <p>
          <icon-font
            type="icon-tishi"
            class="iconFont"
          ></icon-font>载入前人材机汇总价(元)：<span class="red">{{
            editInfo.totalBefore
          }}</span>
        </p>
        <p>
          <icon-font
            type="icon-ruotixing"
            class="iconFont"
          ></icon-font>载入后人材机汇总价(元)：<span class="red">{{
            editInfo.totalAfter
          }}</span>
        </p>
      </div>
      <a-button
        type="primary"
        @click="selectAllUse"
      >批量应用</a-button>
    </div>
  </div>
  <common-modal
    className="dialog-comm priceSelect"
    :title="priceSelectTitle"
    width="900"
    height="430"
    v-model:modelValue="selectPrice"
    @cancel="cancel"
    @close="selectPrice = false"
    :mask="false"
    :lockView="false"
  >
    <vxe-table
      align="center"
      :column-config="{ resizable: true }"
      :data="tablePriceData"
      height="320"
      style="position: relative"
      :row-config="{
        isCurrent: true,
      }"
      @cell-dblclick="dblclickPrice"
      :scroll-y="{ scrollToTopOnChange: true }"
    >
      <vxe-column
        type="seq"
        min-width="50"
        title="序号"
      > </vxe-column>

      <vxe-column
        field="materialName"
        min-width="50"
        title="名称"
      >
      </vxe-column>
      <vxe-column
        field="specification"
        min-width="80"
        title="规格型号"
      >
      </vxe-column>

      <vxe-column
        field="unit"
        min-width="60"
        title="单位"
      > </vxe-column>
      <vxe-column
        field="marketPrice"
        min-width="70"
        :title="activeKey===0?'含税市场价':activeKey===1?'工程价':'推荐价'"
      >
      </vxe-column>
      <vxe-column
        field="notIncludingTaxMarketPrice"
        min-width="90"
        title="不含税市场价"
        v-if="activeKey===0"
      >
      </vxe-column>
      <vxe-column
        field="taxRemoval"
        min-width="90"
        title="税率"
      > </vxe-column>

      <vxe-column
        field="sourcePrice"
        min-width="80"
        title="地区/场地产家"
      >
      </vxe-column>
      <vxe-column
        field="priceDate"
        min-width="80"
        title="期数/报价时间"
      >
      </vxe-column>
    </vxe-table>
    <p class="footer">
      <icon-font
        type="icon-querenshanchu"
        class="iconFont"
      ></icon-font>双击数据行即可应用载价
    </p>
  </common-modal>
  <common-modal
    className="dialog-comm priceSelect"
    title="单位转换设置"
    width="530"
    height="330"
    v-model:modelValue="setUnitModal"
    @cancel="cancel"
    @close="setUnitModal = false"
    :mask="false"
  >
    <div style="padding: 0 30px 0">
      <h3 style="margin: 0 0 15px 0">
        待载入的材料与当前材料单位不一致，需设置转换系数后进行操作
      </h3>
      <div class="unitContent">
        <p>
          当前材料单位：<span class="blue">{{ unitModalInfo.nowUnit }}</span>
        </p>
        <p class="special">
          转换公式：<span class="blue"><a-input-number
              :max="1000"
              :min="0"
              v-model:value="unitModalInfo.num"
              placeholder="请输入转换系数"
              style="display: inline-block; width: 130px; margin-right: 5px"
              @blur="inputChange"
            />{{ unitModalInfo.beforeUnit }}</span>
        </p>
        <p>
          待载材料价格(元)：<span class="blue">{{
            unitModalInfo.marketPrice
          }}</span>
        </p>
        <p>
          转换后材料价格(元)：<span class="red">{{
            unitModalInfo.marketPriceAfter
          }}</span>
        </p>
      </div>
      <p class="btns">
        <a-button @click="setUnitModal = false">取消</a-button>
        <a-button
          type="primary"
          @click="sureChangePrice"
        >确定</a-button>
      </p>
    </div>
  </common-modal>
</template>
<script setup>
import { ref, watch, onMounted, reactive, nextTick, inject , toRaw, computed} from 'vue';
import api from '../../../../api/loadPrice';
import jsApi from '@/api/jiesuanApi.js';
import { projectDetailStore } from '@/store/projectDetail';
import PriorityList from './PriorityList.vue';
import { message } from 'ant-design-vue';
import { useDecimalPoint } from '@/hooks/useDecimalPoint';
const { costPriceFormat } = useDecimalPoint();
const store = projectDetailStore();
let tableData = ref([]); //编辑弹框数据
let tablePriceData = ref([]); //价格选择弹框数据
const props = defineProps(['propsData', 'isOriginalFlag', 'priceType']);
let selectPrice = ref(false); //价格选择弹框
let setUnitModal = ref(false); //单位转换设置弹框
let priceSelectTitle = ref(''); //价格选择弹框标题
let tableEdit = ref();
let unitModalInfo = ref();
let priceSelectRow = ref();
let selectRow = ref();
let tableDetail = ref();
let priorityData = reactive([]);
let priorty = reactive([]);
let editInfo = ref({
  totalAfter: '0',
  totalBefore: '0',
  loadNumber: '0',
  total: '0',
});
let taxList = ref([]);
//编辑弹框展示信息数据
const emits = defineEmits(['close']);
let propsFun = inject('nextStep');
let loadPriortyList = ref(null);
let rcjIdList = ref([]); //勾选的人材机idList
let checkRow = ref(); //执行载价勾选中的行---刷新定位
let typeSelectValue = ref([
  1, 2, 3, 4, 5, 6, 7, 8, 9,
  // , 10
]);
const plainOptions = [
  {
    label: '人工',
    value: 1,
  },
  {
    label: '材料',
    value: 2,
  },
  {
    label: '机械',
    value: 3,
  },
  {
    label: '设备',
    value: 4,
  },
  {
    label: '主材',
    value: 5,
  },
  {
    label: '商砼',
    value: 6,
  },
  {
    label: '砼',
    value: 7,
  },
  {
    label: '浆',
    value: 8,
  },
  {
    label: '商浆',
    value: 9,
  },
  // {
  //   label: '配比',
  //   value: 10,
  // },
];
// let allRcjIdList = ref([]);
const setDisabled = (select, index) => {
  //优先级互斥关系
  let selectedArr = [];
  priorityData.map(item => {
    if (item.value !== '空' || item.value !== select.value) {
      selectedArr.push(item.value);
    }
  });
  selectedArr.splice(index, 1);
  return selectedArr.includes(select.value);
};

const selectAllUse = () => {
  //批量应用接口更新市场价、价格来源
  let apiData = {
    type: store.currentTreeInfo.levelType,
  };
  apiData = getParamsData(apiData);
  // store.SET_GLOBAL_LOADING(true);
  store.SET_GLOBAL_LOADING({
    loading: true,
    info: '载价编辑批量应用中，请稍后...',
  });
  emits('close', false);
  let apiFun = api.applyLoadingPriceInRcjDetails;
  if (props.isOriginalFlag) {
    apiFun = jsApi.applyLoadingPriceInRcjDetailsOriginal;
    const { num = null } = store.currentStageInfo || {};
    const {
      key,
      defaultFeeFlag: { frequencyList },
    } = store.asideMenuCurrentInfo || {};
    Object.assign(apiData, {
      priceType: props.priceType,
      num: frequencyList && frequencyList.length ? num : null,
      kind: Number(key),
    });
  }
  //  && store.deStandardReleaseYear === '22'
  if(store.type === "jieSuan"){
    apiData.priceMethod = {
      tax: taxList.value.includes('tax'),
      noTax: taxList.value.includes('noTax'),
      taxRate: taxList.value.includes('taxRate')
    }
  }
  console.log('批量应用----apiData', apiData);
  apiFun(apiData).then(res => {
    console.log('点击批量应用了', res);
    if (res.status === 200 && res.result) {
      let propsData = {
        nextType: '载价报告'
      };
      setTimeout(() => {
        // && store.deStandardReleaseYear === '22'
        if(store.type === "jieSuan" ){
          propsFun({ ...propsData, taxList: toRaw(taxList.value) });
        }else{
          propsFun(propsData);
        }
        // store.SET_GLOBAL_LOADING(false);
        store.SET_GLOBAL_LOADING({
          loading: false,
          info: '载价编辑批量应用中，请稍后...',
        });
      }, 200);
    }
  });
};

onMounted(() => {
  priorityData = props.propsData.priorityData;
  loadPriortyList.value = props.propsData.priorty;
  console.log('props.propsData', props.propsData);
  rcjIdList.value = props.propsData.rcjList;

  if(store.type === "jieSuan"){
    taxList.value = props.propsData.taxList || [];
  }

  getTableData(true);
});

const getTableData = (bol = false) => {
  //取消勾选及类型并返回载价编辑弹窗数据
  let apiData = {
    type: store.currentTreeInfo.levelType,
    pageSize: 9999,
    pageNum: 1,
    kindList: JSON.parse(JSON.stringify(typeSelectValue.value)),
    rcjIdList: JSON.parse(JSON.stringify(rcjIdList.value)),
    loadPriortyList: JSON.parse(JSON.stringify(loadPriortyList.value)),
  };
  apiData = getParamsData(apiData);
  console.log('弹框数据获取传参getTableData----apiData', apiData);
  let apiFun = api.loadPriceEditPage;
  // 如果是结算并且为合同内项目
  if (store.type == "jieSuan" && props.isOriginalFlag) {
    apiFun = jsApi.jieSuanLoadPriceEditPage;
    const { num = null } = store.currentStageInfo || {};
    const {
      key,
      defaultFeeFlag: { frequencyList },
    } = store.asideMenuCurrentInfo || {};
    Object.assign(apiData, {
      priceType: props.priceType,
      num: frequencyList && frequencyList.length ? num : null,
      kind: Number(key),
    });

  }
  apiFun(apiData).then(res => {
    if (res.status === 200 && res.result) {
      loadPriortyList.value = null;
      console.log('res.result', apiData, res);
      tableData.value = res.result.data;
      editInfo.value = {
        totalAfter: res.result?.totalAfter,
        totalBefore: res.result?.totalBefore,
        loadNumber: res.result?.loadNumber,
        total: res.result?.total,
      };
      let checkList = tableData.value.filter(
        item => item.isExecuteLoadPrice === true
      );
      nextTick(() => {
        setTimeout(() => {
          if (selectRow.value) {
            let row = tableData.value.find(
              item => item.sequenceNbr === selectRow.value
            );
            row ? tableEdit.value.setCurrentRow(row) : '';
            row ? tableEdit.value.scrollToRow(row, 'loadPrice') : '';
          }
          if (checkRow.value) {
            let row = tableData.value.find(
              item => item.sequenceNbr === checkRow.value
            );
            row ? tableEdit.value.setCurrentRow(row) : '';
            row ? tableEdit.value.scrollToRow(row) : '';
            checkRow.value = null;
          }
          console.log(checkList, 'checkList');
          checkList && checkList.length > 0
            ? tableEdit.value && tableEdit.value.setCheckboxRow(checkList, true)
            : '';
        }, 10);
      });
    }
  });
};
const getShowInfo = (data, bol = false) => {
  //获取页面展示人材机载入前后总价+载入数量
  let list = [];
  data.map(item => {
    if (!bol) {
      if (rcjIdList.value.includes(item.sequenceNbr)) {
        list.push(item);
      }
    }
  });
  let checkList = data.filter(item => item.isExecuteLoadPrice === true);
  nextTick(() => {
    setTimeout(() => {
      tableEdit.value.setCheckboxRow(checkList, true);
    }, 10);
  });
  if (bol) {
    nextTick(() => {
      setTimeout(() => {
        tableEdit.value.setAllCheckboxRow(true);
      }, 10);
    });
  } else {
    console.log('list', list);
    nextTick(() => {
      setTimeout(() => {
        tableEdit.value.setCheckboxRow(list, true);
      }, 10);
    });
  }
};
let activeKey = ref(0);

const openPriceSelect = (row, column, type) => {
  //价格选择弹框打开
  // selectPrice.value = true;
  let data = [];
  priceSelectTitle.value = `价格选择-${type}`;
  switch (type) {
    case '信息价':
      // 市场价
      data = row.informationPriceList;
      activeKey.value = 0;
      break;
    case '市场价':
      data = row.marketPriceList;
      activeKey.value = 1;
      break;
    case '推荐价':
      data = row.recommendPriceList;
      activeKey.value = 2;
      break;
  }
  tablePriceData.value = data;
  selectPrice.value = true;
  tableDetail.value = row;
  selectRow.value = row.sequenceNbr;
};
const dblclickPrice = async ({ row, column }) => {
  //双击价格选择弹框中的数据行替换载价编辑弹框市场价
  if (tableDetail.value.unit === row.unit) {
    let newData = {
      sourcePrice: row.sourcePrice,
      price: row.marketPrice,
      sequenceNbr: row.sequenceNbr,
      vagueSourcePrice: row.sourcePrice,
    };
    update(newData, tableDetail.value, 'price');
    selectPrice.value = false;
  } else {
    let res = null;
    // 是否存在默认系数，如果存在，则默认计算
    try {
      console.log('api.queryUnitConversion=>res', {
        desc1: row.unit,
        desc2: tableDetail.value.unit,
      });
      // 传参：desc1 之前单位 ;传参：desc2 之后单位
      unitModalInfo.value = {
        nowUnit: row.unit,
        beforeUnit: tableDetail.value.unit,
        num: null,
        marketPrice:
          activeKey.value === 0 &&
          Number(store.deStandardReleaseYear) === 22 &&
          Number(store?.taxMade) === 1
            ? row.notIncludingTaxMarketPrice
            : row.marketPrice,
        marketPriceAfter: '',
      };
      res = await api.queryUnitConversion({
        desc1: row.unit,
        desc2: tableDetail.value.unit,
      });
      console.log('api.queryUnitConversion=>res', res);
      if (res.code === 200 && res.result) {
        unitModalInfo.value.num = res.result;
        inputChange();
      }
    } catch (e) {
      console.error('api.queryUnitConversion', e);
    } finally {
      setUnitModal.value = true;
      priceSelectRow.value = row;
    }
  }
};
const sureChangePrice = () => {
  if (!unitModalInfo.value.num) {
    message.error('请输入转换系数');
    return;
  }
  let newData = {
    sourcePrice: priceSelectRow.value.sourcePrice,
    price: unitModalInfo.value.marketPriceAfter,
    sequenceNbr: priceSelectRow.value.sequenceNbr,
    vagueSourcePrice: priceSelectRow.value.sourcePrice,
  };
  setUnitModal.value = false;
  selectPrice.value = false;
  update(newData, tableDetail.value, 'price');
};
const inputChange = eve => {
  if (unitModalInfo.value.num <= 0) {
    unitModalInfo.value.num = null;
    unitModalInfo.value.marketPriceAfter = '';
    message.error('请输入规范的转换系数');
    return;
  }
  console.log('unitModalInfo.num', unitModalInfo.value.num);
  let price =
    Number(unitModalInfo.value.num) * Number(unitModalInfo.value.marketPrice);
  unitModalInfo.value.marketPriceAfter = parseFloat(price.toFixed(2));
};
const limitNum = value => {
  if (typeof value !== 'string') return value;
  return value.replace(/[^(-?\d+)\.?(\d*)$]/g, '');
}; //限制数字
const updateBlurEvent = ({ row, column }) => {
  row.loadPrice = limitNum(row.loadPrice); //数字
  row.loadPrice = costPriceFormat(row.loadPrice);
  let newPriceObj = {
    loadPrice: row.loadPrice,
  };

  update(newPriceObj, row, 'loadPrice');
};
const dblclickItem = ({ row, column }) => {
  //双击信息价，市场价，推荐价更改待载价格
  let newPriceObj = {};
  if (column.field == 'informationPrice') {
    newPriceObj.sourcePrice = row.informationSourcePrice;
  } else if (column.field == 'marketPrice') {
    newPriceObj.sourcePrice = row.marketSourcePrice;
  } else if (column.field == 'recommendPrice') {
    newPriceObj.sourcePrice = row.recommendSourcePrice;
  } else {
    return;
  }
  newPriceObj.price = row[column.field];
  console.log(column.field, row[column.field], 'newPriceObj', newPriceObj);
  if (!newPriceObj.price) {
    message.info('无法进行替换待载价格，请重新选择');
    return;
  }
  selectRow.value = row.sequenceNbr;
  update(newPriceObj, row, 'edit');
};
const update = (newPriceObj, row, type) => {
  //手动更新或双击价格选择弹窗的数据行更新待载价格
  let apiData = {
    type: store.currentTreeInfo.levelType,
    rcjId: row.sequenceNbr,
  };
  //去无效0
  newPriceObj.price = parseFloat(newPriceObj.price);
  apiData = getParamsData(apiData);
  switch (type) {
    case 'edit':
      apiData.updatePrice = newPriceObj.price;
      apiData.sourcePrice = newPriceObj.sourcePrice;
      break;
    case 'loadPrice':
      apiData[type] = newPriceObj.loadPrice;
      break;
    case 'price':
    default:
      apiData.updatePrice = newPriceObj.price;
      apiData.popUpDataId = newPriceObj.sequenceNbr;
      apiData.vagueSourcePrice = newPriceObj.vagueSourcePrice;
  }
  if(store.type === 'jieSuan' && taxList.value.includes('taxRate') && priceSelectRow.value){
    apiData.updateRate = priceSelectRow.value?.taxRemoval;
  }
  // if (type === 'edit') {
  //   apiData.updatePrice = newPriceObj.price;
  //   apiData.sourcePrice = newPriceObj.sourcePrice;
  // } else {
  //   apiData.updatePrice = newPriceObj.price;
  //   apiData.popUpDataId = newPriceObj.sequenceNbr;
  // }
  console.log('更新待载价格', apiData);
  let apiFun = api.updateLoadPrice;
  if (props.isOriginalFlag) {
    apiFun = jsApi.updateLoadPriceOriginal;
  }
  console.log(apiFun);
  apiFun(apiData).then(res => {
    if (res.status === 200 && res.result) {
      getTableData();
    }
  });
};
const typeChange = () => {
  //类型筛选-表格数据过滤
  getTableData();
};
const getFinList = data => {
  console.log('data-----------------', data);
  priorityData = data.priorityData;
  loadPriortyList.value = data.priorty;
  getTableData();
};

const cellClassName = ({ $columnIndex, row, column }) => {
  if (column.field === 'loadPrice' && row.highlight) {
    return 'color-red';
  }
  if (
    column.field === 'informationPrice' ||
    column.field === 'marketPrice' ||
    column.field === 'recommendPrice'
  ) {
    return 'hasSelect'; //信息价，市场价，推荐价选中背景色变化
  }
};
const updateLoadNum = ({ row }) => {
  const records = tableEdit.value.getCheckboxRecords();
  let rcjlIST = [];
  if (row) {
    checkRow.value = row.sequenceNbr;
  } else {
    checkRow.value = null;
  }
  records.forEach(item => {
    rcjlIST.push(item.sequenceNbr);
  });
  console.log(row, 'row', records);
  rcjIdList.value = rcjlIST;
  editInfo.value.loadNumber = records.length;
  getTableData();
};
const getParamsData = data => {
  let apiData = { ...data };
  apiData.constructId =
    store.currentTreeInfo.levelType === 1
      ? store.currentTreeInfo?.id
      : store.currentTreeGroupInfo?.constructId;
  if (store.currentTreeInfo.levelType === 2) {
    apiData.singleId = store.currentTreeInfo?.id; //单项ID
  }
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  return apiData;
};

// 根据载入时，选项显示字段
const loadPriceTitle = computed(() => {
  if (store.type !== 'jieSuan'){
    return '待载价格'
  }else{
    return String(props.priceType) === '1' ? '结算单价' : '基期价';
  }
});


watch([() => store.currentTreeInfo, () => store.tabSelectName], value => {});
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  .priority {
    height: 30px;
    width: 600px;
  }
  .typeLine {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    .typeSelect {
      // width: 745px;
      width: 675px;
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      span {
        font-weight: 700;
        // margin-right: 60px;
      }
    }
    .total {
      font-weight: 400;
      line-height: 17px;
      span {
        color: #ff9000;
      }
    }
  }
  .totalStaic {
    margin-top: 20px;
    height: 60px;
    // margin-top: 2%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    p {
      max-width: 600px;
      overflow: hidden;
    }
    .iconFont {
      margin-right: 5px;
    }
    .red {
      color: #de3f3f;
    }
  }
}
.unitContent {
  p {
    font-size: 14px;
    margin: 0 0 18px 0;
    color: #000000;
    .blue {
      color: #287cfa;
      margin-left: 5px;
    }
    .red {
      color: #de3f3f;
      margin-left: 5px;
    }
  }
  .special {
    margin: -10px 0 17px 0;
  }
}
.btns {
  margin: auto;
  width: 150px;
  display: flex;
  justify-content: space-between;
}
.footer {
  position: absolute;
  bottom: -27px;
  left: 0px;
  .iconFont {
    margin-right: 5px;
  }
}

::v-deep(.vxe-table .color-red) {
  color: #de3f3f;
}
::v-deep(.vxe-table .color-blue) {
  color: #287cfa;
}

::v-deep(.vxe-table .hasSelect.col--selected) {
  background-color: lightgrey;
}
::v-deep(.vxe-table--render-default .vxe-body--column.col--selected) {
  box-shadow: none;
}
</style>
