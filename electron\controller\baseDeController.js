const {Controller} = require("../../core");
const {ResponseData} = require("../utils/ResponseData");
const {SqlUtils} = require("../utils/SqlUtils");
const {ObjectUtils} = require("../utils/ObjectUtils");
const ConstantUtil = require('../enum/ConstantUtil');
const QdDeStandardEnum = require("../enum/QdDeStandardEnum");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const UnitConversionEnum = require("../enum/UnitConversionEnum");
/**
 * 国标定额controller
 */
class BaseDeController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
        this._baseDeProcess = this.service.baseDeProcess; // 国标定额process
        this.baseDeRelation2022Service=this.service.baseDeRelation2022Service;
        this.baseDeRelationService=this.service.baseDeRelationService;
        this.baseDeRelationVariableCoefficient2022Service=this.service.baseDeRelationVariableCoefficient2022Service;
        this.baseDeRelationVariableCoefficientService=this.service.baseDeRelationVariableCoefficientService;
    }

    /**
     * 获取定额分类目录树
     * @param libraryCode 定额册code
     */
    async queryDeListTree(args){

        // 原12定额标准逻辑： const result = await this.service.baseDeService.listTreeByLibraryCode(libraryCode);

        // 22定额标准逻辑
        const result = await this._baseDeProcess.listTreeByLibraryCode(args);

        return ResponseData.success(result);
    }

    async queryDeByRcj(params){
        let {constructId,singleId,unitId, libraryCode, rcjCode} = params;
        let is22 = PricingFileFindUtils.is22UnitById(constructId,singleId,unitId);
        if(is22){
            let des = await this.service.baseDe2022Service.queryDeByRcj(params);
            return ResponseData.success(des);
        }else{
            let des = await this.service.baseDeService.queryDeByRcj(params);
            return ResponseData.success(des);
        }
    }

    /**
     * 模糊查定额
     * @param qdDeParam 定额编码、名称、分类名
     * @see ../params/QdDeParam
     */
    async likeDeByCodeOrName(qdDeParam) {
        if (ObjectUtils.isEmpty(qdDeParam.page)){
            qdDeParam.page = 1;
        }
        if (ObjectUtils.isEmpty(qdDeParam.limit)){
            qdDeParam.limit = 20;
        }

        // 原12定额标准逻辑： const result =  await this.service.baseDeService.queryDeByBdCodeAndName(qdDeParam);

        // 22定额标准逻辑
        const result = await this._baseDeProcess.queryDeByBdCodeAndName(qdDeParam);

        return ResponseData.success(result);
    }

    /**
     * 定位定额
     * @param params
     * @returns {Promise<BaseDe|BaseDe2022>}
     */
    async queryDeById(params) {
        // base定额id，定额册code
        let {standardId, libraryCode} = params;
        const result = await this.service.baseDeService.queryDeById(params);
        return ResponseData.success(result);
    }

    /**
     * 定位人材机
     * @param params
     * @returns {Promise<BaseRcj|BaseRcj2022>}
     */
    queryRcjById(params) {
        // base人材机id，定额册code
        let {standardId, libraryCode, constructId, singleId, unitId} = params;

        let querySql;
        let is22de=PricingFileFindUtils.is22UnitById(constructId,singleId,unitId);
        if (is22de) {
            // 22定额标准逻辑
            querySql = "select * from base_rcj_2022 where sequence_nbr = ?";
        } else {
            // 原12定额标准逻辑
            querySql = "select * from base_rcj where sequence_nbr = ?";
        }

        let sqlRes = this.app.betterSqlite3DataSource.prepare(querySql).all(standardId);
        let convertRes = SqlUtils.convertToModel(sqlRes);
        let baseRcj = convertRes[0];

        return ResponseData.success(baseRcj);
    }

    /**
     * 查询所有定额计量单位
     * @return {string[]}
     */
    queryUnit(){
        const result = ["m", "m2", "m3", "台班", "工日", "t", "kg", "座", "组", "宗", "元", "套", "台", "千米", "千克", "辆", "块", "公斤", "付", "处", "部", "10t", "10m2", "10m3", "10m", "100m2", "100m3", "100m", "1000块", "立方米", "平方米", "株","项"];
        return ResponseData.success(result);
    }

    /**
     * 模糊查询定额（快速组价用）
     * @param args
     */
    selectDeByBdName(args){

        // 是22定额标准执行22定额标准
        let is22de=PricingFileFindUtils.is22UnitById(args.constructId,args.spId,args.upId);
        if (is22de) {
            return  this.service.baseDe2022Service.selectDeByBdName(args);
        }

        // 原12定额标准逻辑
        return  this.service.baseDeService.selectDeByBdName(args);
    }


    /**
     * 获取子目指引
     * @param  parentDe父定额
     */
    async getSubitemGuidanceColl(args){
        let {parentDe,constructId, singleId, unitId} = args;
        let is22 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        let deList;
        if(is22){
            deList = await this.baseDeRelation2022Service.getSubitemGuidance(parentDe);
        }else {
            deList = await this.baseDeRelationService.getSubitemGuidance(parentDe);
        }

        return ResponseData.success(deList);
    }


    /**
     *  获取父定额规则
     * @param  parentDe父定额
     */
    async queryVariableCoefficientColl(args){
        let {parentDe,constructId, singleId, unitId} = args;
        let is22 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        let qd = PricingFileFindUtils.getDeById(constructId, singleId,unitId, parentDe.parentId);
        let queryVariableCoefficient;
        if(is22){
            //获取规则组数据
            queryVariableCoefficient =await this.baseDeRelationVariableCoefficient2022Service.queryVariableCoefficient(parentDe,qd);
        }else {
            queryVariableCoefficient =await this.baseDeRelationVariableCoefficientService.queryVariableCoefficient(parentDe,qd);
        }

        return ResponseData.success(queryVariableCoefficient);
    }

    /**
     *  修改父级规则数据
     * @param  queryVariableCoefficient  规则数据
     */
    async updateVariableCoefficientColl(args){
        let {queryVariableCoefficient,constructId, singleId, unitId,parentDe} = args;
        let is22 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        if(is22){
            //获取规则组数据
            queryVariableCoefficient =await this.baseDeRelationVariableCoefficient2022Service.updateVariableCoefficient(queryVariableCoefficient);
        }else {
            queryVariableCoefficient =await this.baseDeRelationVariableCoefficientService.updateVariableCoefficient(queryVariableCoefficient);
        }

        let find = queryVariableCoefficient.find(gz=>gz.variableCode=="GCL" && gz.quantityExpression.toUpperCase().includes("QDL"));
        if (!ObjectUtils.isEmpty(find)){
            let qd = PricingFileFindUtils.getDeById(constructId, singleId,unitId, parentDe.parentId);
            let de = PricingFileFindUtils.getDeById(constructId, singleId,unitId, parentDe.sequenceNbr);

            queryVariableCoefficient = await this.baseDeRelationVariableCoefficient2022Service.updateVariableCoefficientByQdl(queryVariableCoefficient,qd,de, constructId);
        }


        return ResponseData.success(queryVariableCoefficient);
    }

    /**
     * 获取子定额数据
     * @param parentDe   父定额
     * @param deCoefficient  父定额规则
     * @param relationContent 字母指引选项
     */
    async  queryChildrenDeColl(args){
        let {parentDe,deCoefficient,relationContent,updateDeList,constructId, singleId, unitId} = args;
        let is22 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        let qd = PricingFileFindUtils.getDeById(constructId, singleId, unitId,parentDe.parentId);
        let yChildrenDeList;
        if(is22){
            yChildrenDeList = await this.baseDeRelation2022Service.queryChildrenDe(parentDe,deCoefficient,relationContent,updateDeList,qd);
        }else {
            yChildrenDeList = await this.baseDeRelationService.queryChildrenDe(parentDe,deCoefficient,relationContent,updateDeList,qd);
        }

        return ResponseData.success(yChildrenDeList);
    }


    /**
     * 保存子定额数据 (参考清单指引的saveDeArray接口)
     * @param parentDe  子目工程量
     * @returns {Promise<void>}
     */
    async batchSaveChildrenDeListColl(params){
        let {deArray, constructId, singleId, unitId, pointLine, kind, indexId, unit, rootLineId, rcjFlag, fbfxOrCsxm,zmQuantity,deCoefficient} = params;
        let is22 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        let result;
        if(ObjectUtils.isNotEmpty(deArray)){
            if(is22){
                result = await this.baseDeRelation2022Service.saveGlDeArray(params);
            }else {
                result = await this.baseDeRelationService.saveGlDeArray(params);
            }

            //当修改子目工程量的时候  需要修改一下父级定额的工程量
            // 如果输入的值和父定额的工程量不一样的时候）
            if(pointLine.quantityExpression.includes('QDL') && ObjectUtils.isNotEmpty(zmQuantity) &&  zmQuantity!=pointLine.quantityExpressionNbr){
                if (!ObjectUtils.isEmpty(deCoefficient) && !ObjectUtils.isEmpty(deCoefficient[0]) && !ObjectUtils.isEmpty(deCoefficient[0].quantityExpression)){
                    zmQuantity = deCoefficient[0].quantityExpression;
                }
                let upDateInfo={
                    column: "quantityExpression",
                    //避免前端输入的是一个数字
                    value : zmQuantity.toString()
                }
                if(fbfxOrCsxm=="fbfx"){
                    await  this.service.itemBillProjectOptionService.updateByList(constructId, singleId, unitId, pointLine.sequenceNbr, upDateInfo);
                }else {
                    await  this.service.stepItemCostService.upDateOnList(constructId, singleId, unitId, pointLine.sequenceNbr, upDateInfo);
                }
            }
        }else {

            result=[pointLine];
        }

        if(pointLine.quantityExpression.includes('QDL') ){
            if (!ObjectUtils.isEmpty(deCoefficient) && !ObjectUtils.isEmpty(deCoefficient[0]) && !ObjectUtils.isEmpty(deCoefficient[0].quantityExpression)){
                zmQuantity = deCoefficient[0].quantityExpression;
            }
            let upDateInfo={
                column: "quantityExpression",
                //避免前端输入的是一个数字
                value : zmQuantity.toString()
            }
            if(fbfxOrCsxm=="fbfx"){
                await  this.service.itemBillProjectOptionService.updateByList(constructId, singleId, unitId, pointLine.sequenceNbr, upDateInfo);
            }else {
                await  this.service.stepItemCostService.upDateOnList(constructId, singleId, unitId, pointLine.sequenceNbr, upDateInfo);
            }
        }
        return ResponseData.success(result);
    }

    queryUnitConversion(args){
        let{desc1,desc2}= args;
        return ResponseData.success(UnitConversionEnum.getTypeByDesc(desc1,desc2));
    }



}

BaseDeController.toString = () => '[class BaseDeController]';
module.exports = BaseDeController;
