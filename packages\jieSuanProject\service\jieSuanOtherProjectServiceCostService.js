const OtherProjectCalculationBaseConstant = require("../../../electron/enum/OtherProjectCalculationBaseConstant");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const {Service} = require("../../../electron/../core");
const {OtherProjectServiceCost} = require("../../../electron/model/OtherProjectServiceCost");
const {zcbfwf} = require("../../../electron/jsonData/zcbfwf.json");
const { getConnection ,getRepository,getManager  } =require('typeorm');
class JieSuanOtherProjectServiceCostService extends Service{


    //数据行 类型 标题行
    static datyTypeBiaoTi = 1;

    //数据行 类型 数据行
    static datyTypeShuJu = 2;


    //默认数量
    static defaultAmount = 1;

    //默认项目价值
    static defaultXmje = 0;

    //默认费率
    static defaultRate = 0;

    //计算保留小数位
    static decimalPlaces = 2;

    constructor(ctx) {
        super(ctx);
    }

    getDefaultOtherProjectServiceCost(){
        let array = new Array();
        for (let i in zcbfwf) {
            let obj = new OtherProjectServiceCost();
            ConvertUtil.setDstBySrc(zcbfwf[i], obj)
            obj.sequenceNbr = Snowflake.nextId();
            array.push(obj);
        }
        return array;
    }

    getOtherProjectServiceCost(args){
        return  PricingFileFindUtils.getOtherProjectServiceCost(args.constructId,args.singleId, args.unitId);
    }



    //总承包服务费 操作
    otherProjectServiceCost(arg){

        //操作 类型  1:插入 2:粘贴 3删除 4 修改
        let operateType = arg.operateType;

        switch (operateType) {
            case 1:
                this.addProjectServiceCost(arg);
                break;
            case 2:
                this.pasteProjectServiceCost(arg);
                break;
            case 3:
                this.delectProjectServiceCost(arg);
                break;
            case 4:
                this.updateProjectServiceCost(arg);
                break;
            /*case 5:
                this.select(arg);
                break;*/
        }
    }


    //插入
    addProjectServiceCost(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;
        let dataType = arg.dataType;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectServiceCosts;

        let number;
        if (!ObjectUtils.isEmpty(targetSequenceNbr)) {
            number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);

        }else {
            number = 0;
        }
        let otherProjectServiceCost1 = new OtherProjectServiceCost();
        otherProjectServiceCost1.sequenceNbr = Snowflake.nextId();

        otherProjectServiceCost1.fwje = OtherProjectServiceCostService.defaultXmje;

        otherProjectServiceCost1.dataType = dataType;

        if (dataType === OtherProjectServiceCostService.datyTypeShuJu){
            //otherProjectServiceCost1.amount = Number(1).toFixed(6);
            otherProjectServiceCost1.rate = OtherProjectServiceCostService.defaultRate;
            otherProjectServiceCost1.xmje = OtherProjectServiceCostService.defaultRate;
            if (!ObjectUtils.isEmpty(targetSequenceNbr)) {
                let projectService = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
                if (projectService.dataType === OtherProjectServiceCostService.datyTypeBiaoTi){
                    otherProjectServiceCost1.parentId = targetSequenceNbr;
                    number = number +1;
                }else {
                    otherProjectServiceCost1.parentId = projectService.parentId;

                }
            }
        }

        list.splice(number+1,0,otherProjectServiceCost1);
    }



    //粘贴
    pasteProjectServiceCost(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectServiceCosts;

        let projectServiceCostList = arg.projectServiceCostList;
        if(ObjectUtils.isEmpty(projectServiceCostList)){
            return ResponseData.fail('粘贴数据不能为空');
        }

        let projectService = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
        /*if(ObjectUtils.isEmpty(projectService)){
            return ResponseData.fail('插入时所选参照目标数据不存在');
        }*/

        //数据转换
        let array = new Array();
        let nextId = Snowflake.nextId();
        for (let i = 0; i < projectServiceCostList.length; i++) {
            let otherProjectServiceCost1 = new OtherProjectServiceCost();
            ConvertUtil.setDstBySrc(projectServiceCostList[i],otherProjectServiceCost1);
            if (i===0){
                otherProjectServiceCost1.sequenceNbr = nextId;
            }else {
                otherProjectServiceCost1.sequenceNbr = Snowflake.nextId();
                otherProjectServiceCost1.parentId = nextId;
            }
            array.push(otherProjectServiceCost1);
        }

        if (array[0].dataType ===OtherProjectServiceCostService.datyTypeBiaoTi  && !ObjectUtils.isEmpty(projectService) && projectService.dataType ===OtherProjectServiceCostService.datyTypeShuJu){
            return ResponseData.fail('无法在数据行上粘贴标题行数据');
        }

        //复制对象是标题行
        if (array[0].dataType ===OtherProjectServiceCostService.datyTypeBiaoTi){
            let number;
            if (!ObjectUtils.isEmpty(projectService)) {
                number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);
                let ts = list.filter(item => item.parentId === targetSequenceNbr);
                number = number + ts.length + 1;

            }else {
                number = 0;
            }
            list.splice(number,0,...array);

        }else {
            //复制对象是数据行
            if(ObjectUtils.isEmpty(projectService)){
                return ResponseData.fail('插入时所选参照目标数据不存在');
            }
            if (projectService.dataType === OtherProjectServiceCostService.datyTypeBiaoTi){
                array[0].parentId = targetSequenceNbr;
            }else {
                array[0].parentId = projectService.parentId;
            }

            let number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);

            list.splice(number +1,0,array[0]);

        }

        this.updateBiaotiTotal(unit);
        // this.updateOtherProjectServiceCost(arg);
        this.service.otherProjectService.updateAllOtherProjects(arg);

    }


    //删除
    delectProjectServiceCost(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectServiceCosts;
        let projectService = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
        if (projectService.dataType === OtherProjectServiceCostService.datyTypeShuJu){
            let number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);
            list.splice(number,1);
        }else {
            let ts = list.filter(item => item.sequenceNbr !== targetSequenceNbr).filter(item => item.parentId !== targetSequenceNbr);
            unit.otherProjectServiceCosts = ts;
        }

        this.updateBiaotiTotal(unit);
        // this.updateOtherProjectServiceCost(arg);
        this.service.otherProjectService.updateAllOtherProjects(arg);
    }


    //编辑
    updateProjectServiceCost(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;

        let fxName = arg.projectServiceCost.fxName;
        let xmje = arg.projectServiceCost.xmje;
        let amount = arg.projectServiceCost.amount;
        let rate = arg.projectServiceCost.rate;
        let dispNo = arg.projectServiceCost.dispNo;
        let unitBj = arg.projectServiceCost.unit;
        let serviceContent = arg.projectServiceCost.serviceContent;
        let description = arg.projectServiceCost.description;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectServiceCosts;
        let projectServiceCost = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
        if (!ObjectUtils.isEmpty(fxName)){
            projectServiceCost.fxName = fxName;
        }

        if (!ObjectUtils.isEmpty(xmje)){
            projectServiceCost.xmje = xmje;
        }

        if (!ObjectUtils.isEmpty(amount)){
            projectServiceCost.amount =amount;
        }

        if (!ObjectUtils.isEmpty(rate)){
            projectServiceCost.rate = rate;
        }

        if (!ObjectUtils.isEmpty(dispNo)){
            projectServiceCost.dispNo = dispNo;
        }

        if (!ObjectUtils.isEmpty(unitBj)){
            projectServiceCost.unit = unitBj;
        }

        if (!ObjectUtils.isEmpty(serviceContent)){
            projectServiceCost.serviceContent = serviceContent;
        }

        if (!ObjectUtils.isEmpty(description)){
            projectServiceCost.description = description;
        }


        //计算
        //计算暂列金额  【金额】=【项目价值】*【数量】*【费率】%

        projectServiceCost.fwje = NumberUtil.removeExtraZerosAndDot(NumberUtil.multiplyToString(projectServiceCost.xmje,
            NumberUtil.multiply(projectServiceCost.rate,0.01),OtherProjectServiceCostService.decimalPlaces));

        this.updateBiaotiTotal(unit);
        // this.updateOtherProjectServiceCost(arg);
        this.service.otherProjectService.updateAllOtherProjects(arg);
    }

    //计算标题行数据
    updateBiaotiTotal(unit){
        /*let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);*/
        let list = unit.otherProjectServiceCosts;

        let otherProjectServiceCosts = list.filter(item => item.dataType === OtherProjectServiceCostService.datyTypeBiaoTi );
        for (let otherProjectServiceCost1 of otherProjectServiceCosts) {

            let otherProjectServiceCostsList = list.filter(item => item.parentId === otherProjectServiceCost1.sequenceNbr );
            let fwje = 0;
            for (let otherProjectServiceCost2 of otherProjectServiceCostsList) {
                //计算暂列金额  【金额】=【项目价值】*【数量】*【费率】%
                otherProjectServiceCost2.fwje = NumberUtil.removeExtraZerosAndDot(NumberUtil.multiplyToString(otherProjectServiceCost2.xmje,
                    NumberUtil.multiply(otherProjectServiceCost2.rate,0.01), OtherProjectServiceCostService.decimalPlaces));
                fwje =  NumberUtil.add(fwje,otherProjectServiceCost2.fwje);
            }
            otherProjectServiceCost1.fwje = NumberUtil.removeExtraZerosAndDot(fwje.toFixed(2));
        }

    }

    //汇总到 其他项目汇总表
    updateOtherProjectServiceCost(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        let list = unit.otherProjectServiceCosts;
        let otherProjectServiceCosts = list.filter(item => item.dataType === OtherProjectServiceCostService.datyTypeBiaoTi );
        let fwje = 0;
        for (let otherProjectServiceCost1 of otherProjectServiceCosts) {
            fwje =  NumberUtil.add(fwje,otherProjectServiceCost1.fwje);
        }


        let otherProjects = unit.otherProjects;
        let t = otherProjects.find(j => j.calculationBase === OtherProjectCalculationBaseConstant.zcbfwf);

        t.total = NumberUtil.multiplyToString(fwje,t.amount,OtherProjectServiceCostService.decimalPlaces);
    }

    /*select(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;
        let dataType = arg.dataType;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectServiceCosts;

        let number;
    }*/

}
JieSuanOtherProjectServiceCostService.toString = () => '[class JieSuanOtherProjectServiceCostService]';
module.exports = JieSuanOtherProjectServiceCostService;