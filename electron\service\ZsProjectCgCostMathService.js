'use strict';

const RcjTypeEnum = require('../enum/RcjTypeEnum');
const { Service, Log } = require('../../core');
const { PricingFileFindUtils } = require('../utils/PricingFileFindUtils');
const GroundTypeConstant = require('../enum/GroundTypeConstant');
const { BaseList } = require('../model/BaseList');
const ConstructionMeasureTypeConstant = require('../enum/ConstructionMeasureTypeConstant');
const { ObjectUtils } = require('../utils/ObjectUtils');
const BranchProjectLevelConstant = require('../enum/BranchProjectLevelConstant');
const DePropertyTypeConstant = require('../enum/DePropertyTypeConstant');
const { BaseDe, BaseDe2022 } = require('../model/BaseDe');
const { In } = require('typeorm');
const StepItemCostLevelConstant = require('../enum/StepItemCostLevelConstant');
const { ItemBillProject } = require('../model/ItemBillProject');
const { NumberUtil } = require('../utils/NumberUtil');
const { ArrayUtil } = require('../utils/ArrayUtil');
const { CostUtils } = require('../utils/CostUtils');
const InsertStrategy = require('../main_editor/insert/insertStrategy');
const ConstantUtil = require('../enum/ConstantUtil');
const { getDeUnitFormatEnum } = require('../main_editor/rules/format');

/**
 * 装饰工程超高费用记取
 * @class
 */
class ZsProjectCgCostMathService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  /**
   * 装饰工程超高----装饰超高层高下拉框
   */
  async cgStoreyList(args) {
    let storeyList = await this.service.constructCostMathService.storeyList(args);
    return storeyList[GroundTypeConstant.UP];
  }

  /**
   * 获取标准的超高清单
   * @return {Promise<*>}g
   */
  async getCgQd() {
    let cgQd = await this.app.appDataSource.getRepository(BaseList).findOne({
      where: { sequenceNbr: '1658012394317811727' }
    });
    return cgQd;
  }


  /*
  记取位置清单下拉框
  */
  async recordPositionCgList(arg) {
    let { unitId, singleId, constructId } = arg;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let result = [];
    //分部分项
    let fbfx = this.cgQdDataHandler(unit, ConstructionMeasureTypeConstant.FBFX);
    if (fbfx.isExist) {
      result.push({ className: '分部分项', classCode: ConstructionMeasureTypeConstant.FBFX, data: fbfx.data });
    }
    //单价措施
    let djcs = this.cgQdDataHandler(unit, ConstructionMeasureTypeConstant.DJCS);
    if (djcs.isExist) {
      result.push({
        className: '措施项目-单价措施',
        classCode: ConstructionMeasureTypeConstant.DJCS,
        data: djcs.data
      });
    }
    //去除所有定额数据
    if (!ObjectUtils.isEmpty(result)) {
      for (const item of result) {
        item.data = item.data.filter(i => BranchProjectLevelConstant.de !== i.kind);
      }
    }

    return result;
  }

  /**
   * 超高清单处理
   * @param unit
   * @param type
   * @return {{}}
   */
  cgQdDataHandler(unit, type) {
    let result = {};
    let array = [];
    //处理分部分项
    if (ConstructionMeasureTypeConstant.FBFX === type) {
      let { itemBillProjects } = unit;
      array = array.concat(itemBillProjects.getAllNodes());
    }
    //处理单价措施
    if (ConstructionMeasureTypeConstant.DJCS === type) {
      let { measureProjectTables } = unit;
      array.push(measureProjectTables.root);
      //获取到所有的单价措施标题下数据
      let measure = measureProjectTables.filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === type);
      for (const item of measure) {
        let { datas } = this.service.baseBranchProjectOptionService.getDJCSConditionList(measureProjectTables, item);
        if (!ObjectUtils.isEmpty(datas)) {
          array.push(...datas);
        }
      }
    }
    // 处理其他总价措施数据
    if (ConstructionMeasureTypeConstant.ZJCS === type) {
      let { measureProjectTables } = unit;
      array.push(measureProjectTables.root);
      // 取到其他总价措施的标题数据
      let measure = measureProjectTables.filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === type);
      if (ObjectUtils.isNotEmpty(measure)) {
        array.push(...(measure[0].children));
      }
    }

    let flag = false;
    for (let i = array.length - 1; i >= 0; i--) {
      let item = array[i];
      let { kind, fxCode } = item;
      if (BranchProjectLevelConstant.qd === kind) {
        if (!ObjectUtils.isEmpty(fxCode) && fxCode.startsWith('011704001')) {
          flag = true;
          item.bdCode = fxCode;
        } else {
          array.splice(i, 1);
        }
      }
    }
    result.isExist = flag;
    result.data = array;
    return result;
  }

  /**
   * 装饰超高缓存
   */
  cgCostMathCache(arg) {
    let { unitId, singleId, constructId } = arg;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    return unit.cgCostMathCache;
  }

  /**
   * 装饰工程超高----装饰超高记取
   */
  async cgCostMath(arg) {
    let { unitId, singleId, constructId, optionType, qdId, constructionMeasureType, data } = arg;
    let changeDeIds = new Set();
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let is22De = PricingFileFindUtils.is22Unit(unit);
    //记取参数缓存
    unit.cgCostMathCache = arg;
    //删除装饰超高定额
    await this.service.constructCostMathService.clearCostDe(unit, [DePropertyTypeConstant.CG_DE], changeDeIds);
    //默认值的设置
    if (ObjectUtils.isEmpty(constructionMeasureType)) {
      constructionMeasureType = ConstructionMeasureTypeConstant.DJCS;
    }
    data = data.filter(k => k.kind === StepItemCostLevelConstant.de && !ObjectUtils.isEmpty(k.up) && k.value !== 0);
    if (ObjectUtils.isEmpty(data)) {
      return;
    }

    //获取到前端选择的檐高/层数选择的定额
    let upDeList = data.filter(k => !ObjectUtils.isEmpty(k.up) && k.value !== 0).map(k => k.up);
    if (ObjectUtils.isEmpty(upDeList)) {
      return;
    }

    //根据前端选择的费用定额给基数定额分组
    let groupByUp = ArrayUtil.group(data, 'up');

    //去重
    upDeList = [...new Set(upDeList)];
    //根据前端选择的地上定额查询实际的定额数据
    let deArray = [];
    if (!ObjectUtils.isEmpty(upDeList)) {
      deArray = await this.app.appDataSource.getRepository(is22De ? BaseDe2022 : BaseDe).find({
        where: { deCode: In(upDeList), value: 120 }
      });

    }
    //查询表里的超高清单数据
    let cgQd = await this.getCgQd();
    let code = this.service.baseBranchProjectOptionService.getQdCode(constructId, singleId, unitId, cgQd.bdCodeLevel04);

    let lineData = {
      'name': cgQd.bdNameLevel04,
      'bdName': cgQd.bdNameLevel04,
      'kind': StepItemCostLevelConstant.qd,
      'fxCode': code,
      'bdCode': code,
      'standardId': cgQd.sequenceNbr,
      'unit': cgQd.unit,
      'libraryCode': cgQd.libraryCode
    };

    //根据标题类型获取获取标题下的数据
    let titleByQdData = this.cgQdDataHandler(unit, constructionMeasureType);

    let qd = null;
    if (titleByQdData.isExist) {
      qd = titleByQdData.data.find(k => k.kind === BranchProjectLevelConstant.qd && k.sequenceNbr === qdId);
      if (ObjectUtils.isEmpty(qd)) {
        qd = await this.createQd(unit, constructionMeasureType, lineData);
        //添加清单特征以及内容
        await this.service.listFeatureProcess.saveBatchToFbFxQdFeature(qd.libraryCode, qd.fxCode,
          qd.sequenceNbr,
          constructId, singleId, unitId);
      }
    } else {
      qd = await this.createQd(unit, constructionMeasureType, lineData);
      //添加清单特征以及内容
      await this.service.listFeatureProcess.saveBatchToFbFxQdFeature(qd.libraryCode, qd.fxCode,
        qd.sequenceNbr,
        constructId, singleId, unitId);
    }
    let newTitleByQdData = this.cgQdDataHandler(unit, constructionMeasureType);
    // 确定好清单后添加定额
    await this.addToExistingQd(qd.sequenceNbr, unit, newTitleByQdData.data, constructionMeasureType, deArray, groupByUp, is22De);

    //费用定额自动记取计算
    await this.service.autoCostMathService.autoCostMath({
      constructId: constructId,
      singleId: singleId,
      unitId: unitId,
      countCostCodeFlag: true,
      changeDeIdArr: changeDeIds
    });

    let result = '已成功记取至';
    let { fxCode, bdCode, name } = qd;
    if (constructionMeasureType === ConstructionMeasureTypeConstant.FBFX) {
      result = result.concat('分部分项-').concat(ObjectUtils.isEmpty(bdCode) ? fxCode : bdCode).concat(name).concat('下');
    }
    if (constructionMeasureType === ConstructionMeasureTypeConstant.DJCS) {
      result = result.concat('单价措施-').concat(ObjectUtils.isEmpty(fxCode) ? bdCode : fxCode).concat(name).concat('下');
    }
    if (constructionMeasureType === ConstructionMeasureTypeConstant.ZJCS) {
      result = result.concat('其他总价措施-').concat(ObjectUtils.isEmpty(fxCode) ? bdCode : fxCode).concat(name).concat('下');
    }
    return result;
  }


  /**
   * 新建清单
   * @param unit
   * @param arg
   * @param lineData
   */
  async createQd(unit, constructionMeasureType, qdData, deList) {

    /**
     * （1）    措施清单-单价措施（默认）；选取后新增垂运清单至单价措施最后一行若为空清单行则填充（若不存在单价措施标题行则新增，若存在多个记取至行号较小的标题行下）
     * （2）    措施清单-其他总价措施；选取后新增垂运清单至其他总价措施下最后一行（若不存在其他总价措施标题行则新增，若存在多个记取至行号较小的标题行下）
     *          分部分项，选取后新增至最后一行
     */
      //返回的清单数据
    let qDresult = {};
    let {
      constructId,
      spId,
      sequenceNbr,
      qdIdList,
      measureProjectTables,
      itemBillProjects,
      constructMajorType
    } = unit;
    if (constructionMeasureType === ConstructionMeasureTypeConstant.DJCS || constructionMeasureType === ConstructionMeasureTypeConstant.ZJCS) {
      //获取到所有的单价措施标题或者总价措施
      let measure = measureProjectTables.find(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === constructionMeasureType);

      //构建返回参数值

      if (ObjectUtils.isEmpty(measure)) {
        //新增单价措施标题行
        let defaultLine = {};
        if (constructionMeasureType === ConstructionMeasureTypeConstant.DJCS) {
          defaultLine = this.service.stepItemCostService.Default_THR_Line();
        }
        if (constructionMeasureType === ConstructionMeasureTypeConstant.ZJCS) {
          defaultLine = this.service.stepItemCostService.Default_SEC_Line();
        }
        let {
          data,
          index
        } = await this.service.stepItemCostService.save(constructId, spId, sequenceNbr,
          this.service.stepItemCostService.getInsertPointLineByCurrentName(defaultLine.name), defaultLine);
        let save = await this.service.stepItemCostService.save(constructId, spId, sequenceNbr, data, qdData);
        qDresult = save.data;
      } else {
        let qdList = PricingFileFindUtils.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.qd, measure.sequenceNbr);
        //获取到空数据行的清单
        let emptQd = qdList.find(k => k.kind === BranchProjectLevelConstant.qd && ObjectUtils.isEmpty(k.fxCode) && ObjectUtils.isEmpty(k.bdCode));
        if (ObjectUtils.isEmpty(emptQd)) {
          let {
            data,
            index
          } = await this.service.stepItemCostService.save(constructId, spId, sequenceNbr, measure, qdData);
          qDresult = data;
        } else {
          emptQd.name = qdData.name;
          emptQd.bdName = qdData.name;
          emptQd.kind = StepItemCostLevelConstant.qd;
          emptQd.fxCode = qdData.fxCode;
          emptQd.bdCode = qdData.fxCode;
          emptQd.standardId = qdData.standardId;
          emptQd.unit = qdData.unit;
          emptQd.libraryCode = qdData.libraryCode;
          emptQd.isEmpData = 1;
          qDresult = emptQd;
        }
      }
    }
    //分部分项
    if (constructionMeasureType === ConstructionMeasureTypeConstant.FBFX) {
      let allData = itemBillProjects.getAllNodes();
      let {
        data,
        index
      } = await this.service.itemBillProjectOptionService.insertLine(constructId, spId, sequenceNbr, allData[allData.length - 1], qdData);
      qDresult = data;
    }

    return qDresult;

  }


  /**
   * 装饰超高---记取至已有清单
   */
  async addToExistingQd(qdId, unit, allList, constructionMeasureType, deList, groupByUp, is22De) {
    if (ObjectUtils.isEmpty(deList)) {
      return;
    }
    //根据包含超高清单的所有数据
    let qd = allList.find(k => k.sequenceNbr === qdId);
    await this.cgQdAddDe(unit, qd, deList, constructionMeasureType, groupByUp, is22De);

  }

  /**
   * 超高清单下挂上超高定额
   * @param qdList 原始的清单数据，一定是从分部分项或者措施项目中取出来的数据
   * @param deList 定额数据
   */
  async cgQdAddDe(unit, qd, deList, constructionMeasureType, groupByUp, is22De) {
    let { constructId, spId, sequenceNbr } = unit;

    if (ObjectUtils.isEmpty(qd)) {
      return;
    }
    if (ObjectUtils.isEmpty(deList)) {
      return;
    }
    //获取分组后的费用定额编码集合
    const coseDeCodeList = Array.from(groupByUp.keys());
    //循环前端分组
    for (const deCode of coseDeCodeList) {
      //获取费用定额数据
      let costDe = deList.find(k => k.deCode === deCode);
      // deCode对应的页面传来的 基数定额
      const baseDeByPage = groupByUp.get(deCode);
      let titleDeList = null;

      // 构建基础的要添加的定额数据
      let addDe = this.dataHandler(costDe, is22De, baseDeByPage, unit);
      //循环基数定额
      let baseDeList = groupByUp.get(deCode);

      //获取单位下所有人材机数据
      let rcjList = PricingFileFindUtils.getRcjList(constructId, spId, sequenceNbr);
      //重新人材机计算合计数量以及合价
      let deMathBase = 1;
      if (!is22De) {
        // 12的超高费计算基数需要根据基数定额的数据计算
        deMathBase = this.updateTotalNumber(baseDeList, unit, rcjList, addDe);
      }
      //赋值计算基数
      addDe.formula = deMathBase;
      addDe.caculatePrice = 1;
      addDe.baseNum = { def: deMathBase };

      if (constructionMeasureType === ConstructionMeasureTypeConstant.FBFX) {
        // 检查要添加的定额所属清单是不是临时删除的  如果是临时删除的就需要恢复这个清单和清单下的所有数据
        this.service.itemBillProjectOptionService.cleanQdDelTempStatus({
          constructId: constructId,
          singleId: spId,
          unitId: sequenceNbr,
          id: qd.sequenceNbr,
          modelType: 1,
          tempDeleteFlag: false
        });
        let insertStrategy = new InsertStrategy({
          constructId,
          singleId: spId,
          unitId: sequenceNbr,
          pageType: 'fbfx'
        });
        addDe = await insertStrategy.execute({
          pointLine: qd,
          newLine: addDe,
          indexId: costDe.sequenceNbr,
          libraryCode: costDe.libraryCode,
          option: 'insert',
          skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
          overwriteColumn: false
        });
        titleDeList = PricingFileFindUtils.getUnit(constructId, spId, sequenceNbr).itemBillProjects;
      } else {
        // 检查要添加的定额所属清单是不是临时删除的  如果是临时删除的就需要恢复这个清单和清单下的所有数据
        this.service.itemBillProjectOptionService.cleanQdDelTempStatus({
          constructId: constructId,
          singleId: spId,
          unitId: sequenceNbr,
          id: qd.sequenceNbr,
          modelType: 2,
          tempDeleteFlag: false
        });
        //插入定额
        let insertStrategy = new InsertStrategy({
          constructId,
          singleId: spId,
          unitId: sequenceNbr,
          pageType: 'csxm'
        });
        addDe = await insertStrategy.execute({
          pointLine: qd,
          newLine: addDe,
          indexId: costDe.sequenceNbr,
          libraryCode: costDe.libraryCode,
          option: 'insert',
          skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
          overwriteColumn: false
        });
        titleDeList = PricingFileFindUtils.getUnit(constructId, spId, sequenceNbr).measureProjectTables;
      }

      //费用定额的人材机数据
      let deRcj = rcjList.filter(k => k.deId === addDe.sequenceNbr);

      // 在超高的工程量发生变化后  重新计算其下的人材机的合计数量和合价
      await this.computeCgRcjTotalNumberAndTotal(is22De, deRcj, addDe, deMathBase);

      await this.service.cszRgfAdjustProcess.checkDeRgfAdjustAndAddIfNecessary(constructId, spId, sequenceNbr, addDe);

      //计算单价构成
      this.service.unitPriceService.caculataDEUnitPrice(constructId, spId, sequenceNbr,
        addDe.sequenceNbr, true, titleDeList, false);
    }
  }

  async computeCgRcjTotalNumberAndTotal(is22De, deRcj, cgCostDe, deMathBase) {
    if (is22De) {
      // 如果是22超高的费用定额  那么其下的人材机每一条数据都要重新计算【合计数量】和【合价】   因为22的超高费用定额属于普通定额  人材机数据可以新增和修改
      // 这个时候如果22超高的费用定额工程量发生变化后   其下的人材机每一条数据都要重新计算【合计数量】和【合价】
      for (const k of deRcj) {
        if (ConstantUtil.SPECIAL_RCJ.includes(k.materialCode) && k.unit == "%") {
          //“其他材料”、“其他材料费”、“其他机械费”、“其他机械“ 特殊编码   的人材机不计算
          continue;
        }
        //合计数量
        const value = NumberUtil.multiplyParams(k.resQty, cgCostDe.quantity);
        k.totalNumber = NumberUtil.rcjDetailAmountFormat(k.unit == '%' ? NumberUtil.divide100(value) : value);
        //合价
        if (k.materialCode === ConstantUtil.CODE_RGF_ADJUST) {
          // 超高中  如果是超高费用定额的措施中人工费调整这一条人材机  那么“合价”=该条费用人材机的“合计数量”*其“市场价”
          k.total = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(k.totalNumber, k.marketPrice));
        } else {
          k.total = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(k.totalNumber, k.marketPrice));
        }
      }
    } else {
      // 12超高的费用定额人材机不能修改  并且只能记出人工费和机械费  不过理论来说12的超高费用定额下的每个人材机也都需要重新计算【合计数量】和【合价】
      deRcj.forEach(k => {
        //合计数量
        const value = NumberUtil.multiplyParams(k.resQty, cgCostDe.quantity, deMathBase);
        k.totalNumber = NumberUtil.rcjDetailAmountFormat(k.unit == '%' ? NumberUtil.divide100(value) : value);
        //合价
        if (k.materialCode === ConstantUtil.CODE_RGF_ADJUST) {
          // 超高中  如果是超高费用定额的措施中人工费调整这一条人材机  那么“合价”=该条费用人材机的“合计数量”*其“市场价”
          k.total = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(k.totalNumber, k.marketPrice));
        } else {
          k.total = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(k.totalNumber, k.dePrice));
        }
      });
    }
  }


  //重新修改人材机计算合计数量以及合价
  updateTotalNumber(baseDeList, unit, rcjList, addDe) {
    let { sequenceNbr, spId, constructId } = unit;
    //循环基数定额
    //计算基数
    let deMathBase = 0;
    for (const de of baseDeList) {
      //记取类型
      let { value } = de;
      const deNode = PricingFileFindUtils.getDeById(constructId, spId, sequenceNbr, de.sequenceNbr);
      //人工费定额价合价 + 机械费定额价合价
      deMathBase += NumberUtil.multiply(NumberUtil.add(deNode.totalRfeeDe, deNode.totalJfeeDe), value);
      // //获取基础定额人工和机械的明细
      // let rjTypes = [RcjTypeEnum['Rengong'].code, RcjTypeEnum['Jixie'].code];
      //
      // let rcjs = rcjList.filter(k => k.deId === de.sequenceNbr && rjTypes.includes(k.kind));
      // if (!ObjectUtils.isEmpty(rcjs)) {
      //
      //   let rSum = 0;
      //   let jSum = 0;
      //
      //   //计算基础定额人工和机械的定额价合计
      //   rcjs.forEach(k => {
      //     //机械
      //     if (k.kind === 3) {
      //       //解析并且下沉
      //       if (k.levelMark != 0 && k.markSum == 1) {
      //         let { rDetail, cDetail, jDetail } = PricingFileFindUtils.getRcjDetailGroup(unit, k);
      //         //人
      //         if (!ObjectUtils.isEmpty(rDetail)) {
      //           const RSum = rDetail.reduce((total, item) => total + NumberUtil.multiplyParams(item.dePrice, item.resQty, k.resQty, de.quantity), 0);
      //           rSum = NumberUtil.add(NumberUtil.numberScale(RSum, 2), rSum);
      //         }
      //         //机 如果是22定额 则不合计机械费用
      //         if (!ObjectUtils.isEmpty(jDetail) && !PricingFileFindUtils.is22UnitById(constructId, spId, sequenceNbr)) {
      //           const JSum = jDetail.reduce((total, item) => total + NumberUtil.multiplyParams(item.dePrice, item.resQty, k.resQty, de.quantity), 0);
      //           jSum = NumberUtil.add(NumberUtil.numberScale(JSum, 2), jSum);
      //         }
      //       } else {
      //         const JSum = NumberUtil.multiplyParams(k.dePrice, k.resQty, de.quantity);
      //         jSum = NumberUtil.add(JSum, jSum);
      //       }
      //     }
      //
      //
      //     if (k.kind === 1) {
      //       //人 const RSum = NumberUtil.multiplyParams(k.dePrice, k.resQty, de.quantity)
      //       const RSum = NumberUtil.multiplyParams(k.dePrice, k.resQty, de.quantity);
      //       rSum = NumberUtil.add(RSum, rSum);
      //     }
      //
      //   });
      //   //计算费用定额的计算基数
      //   deMathBase += NumberUtil.multiply(rSum + jSum, value);
      // }
    }
    // 如果定额的单位为 “100工日” 这种， 需要给计算基数除100
    // const regex = /\b\d+\b/;
    // let match = addDe.unit.match(regex);
    // if (match) {
    //   const number = parseInt(match[0]);
    //   deMathBase = NumberUtil.numberScale(NumberUtil.divide(deMathBase, number), 2);
    // }
    return deMathBase;
  }


  /**
   * 处理定额数据
   */
  dataHandler(deItem, is22De, baseDeByPage, unit) {
    let itemBillProject = new ItemBillProject();
    itemBillProject.bdName = deItem.deName;
    itemBillProject.bdCode = deItem.deCode;
    itemBillProject.name = deItem.deName;
    itemBillProject.fxCode = deItem.deCode;
    itemBillProject.kind = StepItemCostLevelConstant.de;
    itemBillProject.unit = deItem.unit;
    itemBillProject.standardId = deItem.sequenceNbr;
    itemBillProject.isCostDe = DePropertyTypeConstant.CG_DE; //添加定额标识 超高定额
    itemBillProject.libraryCode = deItem.libraryCode;
    itemBillProject.isStandard = DePropertyTypeConstant.STANDARD;//定额数据位标准定额数据
    itemBillProject.quantityExpression = '1';//工程量表达式展示用
    itemBillProject.quantityExpressionNbr = 1;//工程量表达式计算用
    itemBillProject.quantity = 1;
    if (is22De) {
      let unitNum = getDeUnitFormatEnum(itemBillProject.unit, unit.constructId).value;
      // 如果是22的超高定额  那么可以在添加时直接计算好工程量
      // 22超高定额的工程量为：基数定额的人材机中单位为工日的人工费的合计数量(totalNumber)之和
      const baseDeRcjList = PricingFileFindUtils.batchGetDeRcjList(unit.constructId, unit.spId, unit.sequenceNbr, baseDeByPage.map(de => de.sequenceNbr));
      if (ObjectUtils.isNotEmpty(baseDeRcjList)) {
        const grRcjList = baseDeRcjList.filter(item => item.kindBackUp == RcjTypeEnum['Rengong'].code && item.unit == ConstantUtil.UNIT_RENGONG);
        if (ObjectUtils.isNotEmpty(grRcjList)) {
          const sum = grRcjList.reduce((accumulator, currentValue) => accumulator + currentValue.totalNumber, 0);
          itemBillProject.quantityExpression = ConstantUtil.EXP_CG_RGHJ;
          itemBillProject.quantityExpressionNbr = sum;
          if (itemBillProject.quantityExpression) {
            const regex = /\b\d+\b/;
            let match = itemBillProject.unit.match(regex);
            if (match) {
              const number = parseInt(match[0]);
              itemBillProject.quantity = NumberUtil.numberScale(NumberUtil.divide(itemBillProject.quantityExpressionNbr, number), unitNum);
            } else {
              itemBillProject.quantity = NumberUtil.numberScale(itemBillProject.quantityExpressionNbr, unitNum);
            }
          }
        }
      }
    }
    return itemBillProject;
  }


}

ZsProjectCgCostMathService.toString = () => '[class ZsProjectCgCostMathService]';
module.exports = ZsProjectCgCostMathService;
