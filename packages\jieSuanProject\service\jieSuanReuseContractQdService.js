'use strict';


const { ObjectUtil } = require('../../../common/ObjectUtil');
const { ResponseData } = require('../../../electron/utils/ResponseData');
const { Service } = require('../../../core');
const { PricingFileFindUtils } = require('../../../electron/utils/PricingFileFindUtils');
const BranchProjectLevelConstant = require('../../../electron/enum/BranchProjectLevelConstant');
const { NumberUtil } = require('../../../electron/utils/NumberUtil');
const { RcjCalculateHandler } = require('../../../electron/rcj_handle/calculate/RcjCalculateHandler');
const DePropertyTypeConstant = require('../../../electron/enum/DePropertyTypeConstant');

/**
 * 结算 复用合同清单 Service
 */
class JieSuanReuseContractQdService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  /**
   * 复用合同清单弹窗 清单列表数据查询
   */
  async queryReuseContractQdData(args) {
    const { constructId, singleId, unitId, quantityDifferenceRangeMin, quantityDifferenceRangeMax, qdName } = args;
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(unit)) {
      return null;
    }
    let fbfxArr = [];
    const fbfx = unit.itemBillProjects.getAllNodes();
    // 再放入分部分项的清单
    fbfxArr = await this.filterArrayByQueryParam(fbfx, quantityDifferenceRangeMin, quantityDifferenceRangeMax, qdName);

    let csxmArr = [];
    let csxm = unit.measureProjectTables.getAllNodes();
    const qdByAwf = PricingFileFindUtils.getQdByAwf(constructId, singleId, unitId);
    if (ObjectUtil.isNotEmpty(qdByAwf)) {
      const awfQdIds = qdByAwf.map(item => item.sequenceNbr);
      csxm = csxm.filter(item => !awfQdIds.includes(item.sequenceNbr));
    }
    // 再放入措施项目中有定额的所有清单
    csxmArr = await this.filterArrayByQueryParam(csxm, quantityDifferenceRangeMin, quantityDifferenceRangeMax, qdName);

    let fbfxDataArr = [];
    // 放入分部分项顶级
    fbfxDataArr.push(fbfx[0]);
    // 放入清单和清单下的定额
    for (const item of fbfxArr) {
      fbfxDataArr.push(item);
      fbfxDataArr = fbfxDataArr.concat(item.children);
    }
    let csxmDataArr = [];
    // 放入措施项目的顶级
    csxmDataArr.push(csxm[0]);
    for (const item of csxmArr) {
      csxmDataArr.push(item);
      csxmDataArr = csxmDataArr.concat(item.children);
    }
    return { csxm: csxmDataArr, fbfx: fbfxDataArr };
  }

  async filterArrayByQueryParam(array, quantityDifferenceRangeMin, quantityDifferenceRangeMax, qdName) {
    if (ObjectUtil.isEmpty(array)) {
      return [];
    }
    return array.filter(item => {
      if (item.kind != BranchProjectLevelConstant.qd) {
        return false;
      }
      if (ObjectUtil.isEmpty(item.bdCode) && ObjectUtil.isEmpty(item.bdName)) {
        // 过滤空清单
        return false;
      }
      let flag = true;
      if (ObjectUtil.isNotEmpty(qdName)) {
        if (!item.bdName.includes(qdName) && !item.bdCode.startsWith(qdName)) {
            flag = false;
        }
      }
      if (ObjectUtil.isNotEmpty(quantityDifferenceRangeMin) && ObjectUtil.isNotEmpty(quantityDifferenceRangeMax)) {
        if (!(item.quantityDifferenceProportion < quantityDifferenceRangeMin || item.quantityDifferenceProportion > quantityDifferenceRangeMax)) {
          flag = false;
        }
      }
      return flag;
    });
  }

  countCalculateCoefficientV2(de, qd) {
    //如果是定额    并且表达式不含QDL
    if (de.kind == BranchProjectLevelConstant.de) {
      if( typeof de.quantityExpression === 'number'){
        de.quantityExpression="QDL*"+Number(NumberUtil.divide(de.quantity,qd.quantity).toFixed(17));
        return;
      }
      if(  ObjectUtil.isEmpty(de.quantityExpression)  || !de.quantityExpression.toUpperCase().includes('QDL')){
         de.quantityExpression="QDL*"+Number(NumberUtil.divide(de.quantity,qd.quantity).toFixed(17));
      }

    }
  }


  /**
   * 复用合同清单接口
   */
  async handleReuseContractQd(args) {
    const {
      constructId,        // 工程项目id
      singleId,           // 单项id
      unitId,             // 单位id
      reuseConstructId,   // 被复用的清单所属工程项目
      reuseSingleId,      // 被复用的清单所属单项
      reuseUnitId,        // 被复用的清单所属单位
      fbfxQdArr,          // 列表选择的分部分项清单
      csxmQdArr,          // 列表选择的措施项目清单
      qdReuseRule,        // 清单复用规则          1：只复制清单  2：清单和组价全部复制
      quantityReuseRule,  // 工程量复用规则        1: 量差幅度以外的工程量   2：工程量全部复制  3：工程量为0
      quantityDeductFlag  // 是否将复用部分工程量在原清单中扣除  0：否 1：是
    } = args;
    // 合同外 单位工程
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    // 合同内 单位工程
    const reuseUnit = PricingFileFindUtils.getUnit(reuseConstructId, reuseSingleId, reuseUnitId);
    if (ObjectUtil.isNotEmpty(fbfxQdArr)) {
      const targetNode = unit.itemBillProjects.root;
      for (const item of fbfxQdArr) {
        let itemBillProjects = [];
        let node = reuseUnit.itemBillProjects.getNodeById(item);
        if (node.kind != BranchProjectLevelConstant.qd) {
          continue;
        }
        if (ObjectUtil.isNotEmpty(node)) {
          node = ObjectUtil.cloneDeep(node);
          itemBillProjects.push({
            ...node,
            parent: null,
            children: null,
            prev: null,
            next: null,
            quantityDifferenceProportionColour: null,
            settlementMethod: null,
            originalFlag: false,
            lockPriceFlag: false
          });
          if (qdReuseRule == 2) {
            // 清单和组价全部复制
            if (ObjectUtil.isNotEmpty(node.children)) {
              for (const child of node.children) {
                if (child.isCostDe == DePropertyTypeConstant.AWF_DE || child.isCostDe == DePropertyTypeConstant.ZJCS_DE
                  || child.kisCostDeind == DePropertyTypeConstant.CG_DE || child.isCostDe == DePropertyTypeConstant.AZ_DE) {
                  // 不复制 【安文费、总价措施、超高、安装】的费用定额
                  continue;
                }
                this.countCalculateCoefficientV2(child,node);
                itemBillProjects.push({
                  ...child,
                  parent: null,
                  children: null,
                  prev: null,
                  next: null,
                  quantityDifferenceProportionColour: null,
                  originalFlag: false
                });
              }
            }
          }
        }
        // 调用预算的复制方法  处理复用逻辑
        let newDataMap = {};
        await this.service.itemBillProjectOptionService.addDataByQdDe(Object.assign({}, reuseUnit, {
          itemBillProjects: itemBillProjects,
          // 废除  -》 ：：设置取费文件为空  因为这个方法里面会设置单位的主取费文件为null  这里需要设置为[]  不影响合同内的单位主取费文件
          // 新的 -》：： 获取原单位的取费文件  用于拷贝之后 获取主取费文件
          feeFiles: ObjectUtil.cloneDeep(reuseUnit.feeFiles)
        }), constructId, singleId, unitId, targetNode, newDataMap);
        if (Object.keys(newDataMap).length < 1) {
          throw new Error('复用失败');
        }
        let qdNewNode = null;
        for (let key in newDataMap) {
          // 取清单数据
          if (newDataMap[key].kind = BranchProjectLevelConstant.qd) {
            qdNewNode = newDataMap[key];
            break;
          }
        }
        if (ObjectUtil.isEmpty(qdNewNode)) {
          throw new Error('复用失败');
        }
        //初始化定额人材机的结算字段
        let all = Object.values(newDataMap);
        for(const i of all){
           if(i.kind==BranchProjectLevelConstant.de){
             //人材机初始化
             await this.service.jieSuanProject.jieSuanItemBillProjectService.initDeRcjList(constructId, singleId, unitId, i);
           }
        }

        // 设置合同清单关联
        await this.service.jieSuanProject.jieSuanRelatedContractsQdService.saveRelatedQd({
          constructId: constructId,
          singleId: singleId,
          unitId: unitId,
          sequenceNbr: qdNewNode.sequenceNbr,
          bizType: 'fbfx',
          relatedConstructId: reuseConstructId,
          relatedSingleId: reuseSingleId,
          relatedUnitId: reuseUnitId,
          relatedQdId: item
        });
        if (quantityReuseRule == 1) {
          // 量差幅度以外的工程量
          let value = 0;
          if (node.quantityDifferenceProportion > 0) {
            // 如果量差比例大于0   合同外工程量=结算工程量-原工程量*(1+K%)
            value = NumberUtil.subtract(node.quantity, NumberUtil.multiply(node.backQuantity, NumberUtil.add(1, NumberUtil.divide100(reuseUnit.quantityDifferenceRangeMax))));
            // 修改复用后新清单的工程量
            await this.service.itemBillProjectOptionService.updateByList(constructId, singleId, unitId, qdNewNode.sequenceNbr, {
              column: 'quantityExpression',
              value: value + ''
            });
            if (quantityDeductFlag == 1) {
              // 扣除原合同内清单的工程量
              let node = reuseUnit.itemBillProjects.getNodeById(item);
              const reuseValue = NumberUtil.subtract(node.quantity, value);
              if ((node.isCostDe == DePropertyTypeConstant.DS_CY_DE || node.isCostDe == DePropertyTypeConstant.DX_CY_DE)
                && (node.quantityExpression == 'DSZSGR' || node.quantityExpression == 'DXZSGR')) {
                await this.service.jieSuanProject.jieSuanItemBillProjectService.updateJieSuanFbfxData({
                  constructId: reuseConstructId,
                  singleId: reuseSingleId,
                  unitWorkId: reuseUnitId,
                  pointLineId: item,
                  column: 'quantity',
                  value: reuseValue
                });
              } else {
                await this.service.jieSuanProject.jieSuanItemBillProjectService.updateJieSuanFbfxData({
                  constructId: reuseConstructId,
                  singleId: reuseSingleId,
                  unitWorkId: reuseUnitId,
                  pointLineId: item,
                  column: 'quantityExpression',
                  value: reuseValue + ''
                });
              }
            }
          } else {
            // 如果量差比例小于0   合同外工程量=合同内结算工程量
            value = node.quantity;
            // 修改复用后新清单的工程量
            await this.service.itemBillProjectOptionService.updateByList(constructId, singleId, unitId, qdNewNode.sequenceNbr, {
              column: 'quantityExpression',
              value: value + ''
            });
            if (quantityDeductFlag == 1) {
              // 扣除原合同内清单的工程量
              await this.service.jieSuanProject.jieSuanItemBillProjectService.updateJieSuanFbfxData({
                constructId: reuseConstructId,
                singleId: reuseSingleId,
                unitWorkId: reuseUnitId,
                pointLineId: item,
                column: 'quantityExpression',
                value: '0'
              });
            }
          }
          if (ObjectUtil.isNotEmpty(qdNewNode.children)) {
            // 重新计算人材机
            let rcjCalculateHandler = new RcjCalculateHandler({
              constructId: constructId,
              singleId: singleId,
              unitId: unitId,
              projectObj: PricingFileFindUtils.getProjectObjById(constructId)
            });
            for (const child of qdNewNode.children) {
              await rcjCalculateHandler.calculate(child);
            }
          }
        } else if (quantityReuseRule == 2) {
          // 工程量全部复制   默认就是全部复制的

        } else if (quantityReuseRule == 3) {
          // 工程量为0
          for (let key in newDataMap) {
            await this.service.itemBillProjectOptionService.updateByList(constructId, singleId, unitId, key, {
              column: 'quantityExpression',
              value: '0'
            });
            const nodeById = unit.measureProjectTables.getNodeById(key);
            if (ObjectUtil.isNotEmpty(nodeById) && nodeById.kind == BranchProjectLevelConstant.de) {
              let rcjCalculateHandler = new RcjCalculateHandler({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                projectObj: PricingFileFindUtils.getProjectObjById(constructId)
              });
              await rcjCalculateHandler.calculate(nodeById);
            }
          }
        }
      }
    }
    if (ObjectUtil.isNotEmpty(csxmQdArr)) {
      const targetNode = unit.measureProjectTables.root;

      for (const item of csxmQdArr) {
        let measureProjects = [];
        let node = reuseUnit.measureProjectTables.getNodeById(item);
        if (node.kind != BranchProjectLevelConstant.qd) {
          continue;
        }
        if (ObjectUtil.isNotEmpty(node)) {
          node = ObjectUtil.cloneDeep(node);
          measureProjects.push({
            ...node,
            parent: null,
            children: null,
            prev: null,
            next: null,
            quantityDifferenceProportionColour: null,
            settlementMethod: null,
            originalFlag: false,
            lockPriceFlag: false
          });
          if (qdReuseRule == 2) {
            // 清单和组价全部复制
            if (ObjectUtil.isNotEmpty(node.children)) {
              for (const child of node.children) {
                if (child.isCostDe == DePropertyTypeConstant.AWF_DE || child.isCostDe == DePropertyTypeConstant.ZJCS_DE
                  || child.kisCostDeind == DePropertyTypeConstant.CG_DE || child.isCostDe == DePropertyTypeConstant.AZ_DE) {
                  // 不复制 【安文费、总价措施、超高、安装】的费用定额
                  continue;
                }
                this.countCalculateCoefficientV2(child,node);
                measureProjects.push({
                  ...child,
                  parent: null,
                  children: null,
                  prev: null,
                  next: null,
                  quantityDifferenceProportionColour: null,
                  originalFlag: false
                });
              }
            }
          }
        }
        let newDataMap = {};
        await this.service.stepItemCostService.addDataByQdDe(Object.assign({}, reuseUnit, {
          measureProjectTables: measureProjects,
          // 废除  -》 ：：设置取费文件为空  因为这个方法里面会设置单位的主取费文件为null  这里需要设置为[]  不影响合同内的单位主取费文件
          // 新的 -》：： 获取原单位的取费文件  用于拷贝之后 获取主取费文件
          feeFiles: reuseUnit.feeFiles
        }), constructId, singleId, unitId, targetNode, newDataMap);
        if (Object.keys(newDataMap).length < 1) {
          throw new Error('复用失败');
        }
        let qdNewNode = null;
        for (let key in newDataMap) {
          // 取清单数据
          if (newDataMap[key].kind = BranchProjectLevelConstant.qd) {
            qdNewNode = newDataMap[key];
            break;
          }
        }
        if (ObjectUtil.isEmpty(qdNewNode)) {
          throw new Error('复用失败');
        }
        // 设置合同清单关联
        await this.service.jieSuanProject.jieSuanRelatedContractsQdService.saveRelatedQd({
          constructId: constructId,
          singleId: singleId,
          unitId: unitId,
          sequenceNbr: qdNewNode.sequenceNbr,
          bizType: 'csxm',
          relatedConstructId: reuseConstructId,
          relatedSingleId: reuseSingleId,
          relatedUnitId: reuseUnitId,
          relatedQdId: item
        });
        if (quantityReuseRule == 1) {
          // 量差幅度以外的工程量
          let value = 0;
          if (node.quantityDifferenceProportion > 0) {
            // 如果量差比例大于0   合同外工程量=结算工程量-原工程量*(1+K%)
            value = NumberUtil.subtract(node.quantity, NumberUtil.multiply(node.backQuantity, NumberUtil.add(1, NumberUtil.divide100(reuseUnit.quantityDifferenceRangeMax))));
            // 修改复用后新清单的工程量
            await this.service.stepItemCostService.upDateOnList(constructId, singleId, unitId, qdNewNode.sequenceNbr, {
              column: 'quantityExpression',
              value: value + ''
            });
            if (quantityDeductFlag == 1) {
              // 扣除原合同内清单的工程量
              let node = reuseUnit.itemBillProjects.getNodeById(item);
              const reuseValue = NumberUtil.subtract(node.quantity, value);

              if ((node.isCostDe == DePropertyTypeConstant.DS_CY_DE || node.isCostDe == DePropertyTypeConstant.DX_CY_DE)
                && (node.quantityExpression == 'DSZSGR' || node.quantityExpression == 'DXZSGR')) {
                await this.service.jieSuanProject.jieSuanMeasureProjectTableService.updateJieSuanCsxmData({
                  constructId: reuseConstructId,
                  singleId: reuseSingleId,
                  unitWorkId: reuseUnitId,
                  pointLineId: item,
                  column: 'quantity',
                  value: reuseValue
                });
              } else {
                await this.service.jieSuanProject.jieSuanMeasureProjectTableService.updateJieSuanCsxmData({
                  constructId: reuseConstructId,
                  singleId: reuseSingleId,
                  unitWorkId: reuseUnitId,
                  pointLineId: item,
                  column: 'quantityExpression',
                  value: reuseValue + ''
                });
              }
            }
          } else {
            // 如果量差比例小于0   合同外工程量=合同内结算工程量
            value = node.quantity;
            // 修改复用后新清单的工程量
            await this.service.stepItemCostService.upDateOnList(constructId, singleId, unitId, qdNewNode.sequenceNbr, {
              column: 'quantityExpression',
              value: value + ''
            });
            if (quantityDeductFlag == 1) {
              // 扣除原合同内清单的工程量
              await this.service.jieSuanProject.jieSuanMeasureProjectTableService.updateJieSuanCsxmData({
                constructId: reuseConstructId,
                singleId: reuseSingleId,
                unitWorkId: reuseUnitId,
                pointLineId: item,
                column: 'quantityExpression',
                value: '0'
              });
            }
          }
          if (ObjectUtil.isNotEmpty(qdNewNode.children)) {
            // 重新计算人材机
            let rcjCalculateHandler = new RcjCalculateHandler({
              constructId: constructId,
              singleId: singleId,
              unitId: unitId,
              projectObj: PricingFileFindUtils.getProjectObjById(constructId)
            });
            for (const child of qdNewNode.children) {
              await rcjCalculateHandler.calculate(child);
            }
          }
        } else if (quantityReuseRule == 2) {
          // 工程量全部复制   默认就是全部复制的

        } else if (quantityReuseRule == 3) {
          // 工程量为0
          for (let key in newDataMap) {
            await this.service.stepItemCostService.upDateOnList(constructId, singleId, unitId, key, {
              column: 'quantityExpression',
              value: '0'
            });
            const nodeById = unit.measureProjectTables.getNodeById(key);
            if (ObjectUtil.isNotEmpty(nodeById) && nodeById.kind == BranchProjectLevelConstant.de) {
              let rcjCalculateHandler = new RcjCalculateHandler({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                projectObj: PricingFileFindUtils.getProjectObjById(constructId)
              });
              await rcjCalculateHandler.calculate(nodeById);
            }
          }
        }
      }

    }

    //计算费用代码
    args.levelType = 3;
    await this.service.jieSuanProject.jieSuanUnitCostCodePriceService.countCostCodePrice(args);
  }

}

JieSuanReuseContractQdService.toString = () => '[class JieSuanReuseContractQdService]';
module.exports = JieSuanReuseContractQdService;
