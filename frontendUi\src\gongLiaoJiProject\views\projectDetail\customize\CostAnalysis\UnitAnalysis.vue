<!--
 * @Author: wangru
 * @Date: 2023-05-29 09:34:55
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-04-24 11:24:07
-->
<template>
  <vxe-table
    align="center"
    :column-config="{ resizable: true }"
    :row-config="{ isHover: true, isCurrent: true }"
    :data="tableData"
    :edit-config="{
      trigger: 'click',
      mode: 'cell',
      beforeEditMethod({ columnIndex, rowIndex }) {
        if (!cellBeforeEditMethod()) return false;
        if (rowIndex === editIndex && columnIndex === 2) {
          return true;
        }
        return false;
      },
    }"
    ref="unitTable"
    keep-source
    @edit-closed="editClosedEvent"
    :height="stableHeight"
    :tree-config="{
      children: 'childrenList',
      line: false,
      expandAll: true,
    }"
    @cell-click="useCellClickEvent"
    :scroll-y="{ enabled: true, gt: 30 }"
    show-overflow
    :cell-class-name="selectedClassName"
    @current-change="rowChange"
    class="table-edit-common table-content"
  >
    <vxe-column type="seq" :min-width="columnWidth(120)" title="序号" tree-node> </vxe-column>
    <vxe-column field="name" title="名称" :min-width="columnWidth(400)"> </vxe-column>
    <vxe-column field="context" title="内容" :min-width="columnWidth(400)">
      <template #edit="{ row }">
        <vxe-input
          :clearable="false"
          v-model="row.context"
          :maxlength="10"
          @blur="(row.context = pureNumber(row.context, 2)), clear()"
        ></vxe-input>
      </template>
    </vxe-column>
  </vxe-table>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import { pureNumber } from '@/utils/index';
import feePro from '@/gongLiaoJiProject/api/feePro';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@gongLiaoJi/hooks/useCellClick';
import { useDecimalPoint } from '@gongLiaoJi/hooks/useDecimalPoint.js';
const { decimalFormat } = useDecimalPoint();
import { columnWidth } from '@gongLiaoJi/hooks/useSystemConfig';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick({ rowKey: 'dispNo' });
import { recordProjectData } from '@gongLiaoJi/hooks/recordProjectOperat.js';
let { updateGljSelrowId } = recordProjectData();
const store = projectDetailStore();
let editIndex = ref(); //设置只有建筑面积内容可编辑-----------单位工程
let tableData = ref([]);
let average = ref();
let unitTable = ref();
let currentChange = ref(null);
const props = defineProps({
  tableColumns: {
    type: Array,
    default: () => [],
  },
  stableHeight: {
    type: Number,
    default: 400,
  },
});
const clear = () => {
  //清除编辑状态
  const $table = unitTable.value;
  $table.clearEdit();
};
// 递归函数，通过 id 获取节点
function getNodeById(tree, id) {
  for (const node of tree) {
    if (node.dispNo === id) {
      return node;
    }
    if (node.childrenList && node.childrenList.length > 0) {
      const result = getNodeById(node.childrenList, id);
      if (result) {
        return result;
      }
    }
  }
  return null;
}
const getTableData = () => {
  let apiData = {
    type: store.currentTreeInfo.type,
    // code: store.queryCostAnalysisCode,
    // bizId: store.currentTreeInfo?.id,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeInfo?.parentId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  let averge;
  let unitCost;
  let total;
  console.log('获取单位造价分析', apiData);
  if (!apiData.type) {
    //levelType不存在不掉接口，这个是偶现，一次性调好多次
    return;
  }
  feePro.getCostAnalysisData(apiData).then(res => {
    if (res.status === 200) {
      res.result.costAnalysisUnitVOList.map(a => {
        props.tableColumns.map(b => {
          if (a.name === b.title) {
            a.visible = b.visible;
          }
          if (a.childrenList) {
            a.childrenList.map(c => {
              if (a.name + '，' + c.name === b.field) {
                c.visible = b.visible;
              }
            });
          }
        });
      });
      tableData.value = res.result.costAnalysisUnitVOList
        .map(a => {
          if (a.childrenList) {
            a.childrenList = a.childrenList.filter(b => b.visible);
          }
          if (a.visible) return a;
        })
        .filter(a => a);
      console.log('单位获取造价分析返回数据', tableData.value);
      //需要确定这里的建筑面积是否可以修改
      tableData.value &&
        tableData.value.map((item, index) => {
          if (item.name.includes('工程总造价(不含设备费及其税金 小写)')) {
            item.context = item.context ? parseFloat(item.context) : '';
            total = item.context;
          }
          if (item.name.includes('单方造价')) {
            item.context = item.context
              ? decimalFormat(item.context, 'COST_SUMMARY_JE_PATH')
              : '0.00';
          }

          if (
            ['企业管理费', '利润', '安全生产、文明施工费', '税金'].includes(
              item.name
            )
          ) {
            item.context = decimalFormat(item.context, 'COST_SUMMARY_JE_PATH');
          }
          if (
            ['1', '4', '4.1', '4.2', '4.3', '4.4', '4.5', '5', '6'].includes(
              item.dispNo
            )
          ) {
            item.context = decimalFormat(item.context, 'COST_ANALYSIS_JE_PATH');
          }
        });
      setTimeout(async () => {
        const $table = unitTable.value;
        $table.loadData(tableData.value);
        if ($table) {
          let gljCheckTab = store.gljCheckTab;
          let selRow = tableData.value[0];
          let upSelRow = gljCheckTab[
            store.currentTreeInfo.sequenceNbr
          ]?.tabList.find(a => a.tabName == '造价分析');
          if (upSelRow && upSelRow.selRowId !== '') {
            let tableList = JSON.parse(JSON.stringify(tableData.value));
            let obj = getNodeById(tableList, upSelRow.selRowId);
            selRow = obj;
          }
          currentChange.value = selRow;
          $table.setAllTreeExpand(true);
          $table.setCurrentRow(selRow);
          setTimeout(async () => {
            $table.scrollToRow(selRow);
          }, 100);
        }
      }, 1);
    }
  });
};
const rowChange = ({ row }) => {
  currentChange.value = row;
  updateGljSelrowId(row.dispNo, '造价分析', 'selRowId');
};
watch(
  () => store.currentTreeInfo,
  () => {
    if (
      store.tabSelectName === '造价分析' &&
      store.currentTreeInfo.type === 3
    ) {
      getTableData();
    }
  }
);
watch(
  () => store.tabSelectName,
  () => {
    if (
      store.tabSelectName === '造价分析' &&
      store.currentTreeInfo.type === 3
    ) {
      getTableData();
    }
  }
);
// watch(
//   () => props.tableColumns,
//   () => {
//     if (
//       store.tabSelectName === '造价分析' &&
//       store.currentTreeInfo.type === 3
//     ) {
//       getTableData();
//     }
//   },
//   {
//     deep: true,
//   }
// );
onMounted(() => {
  if (store.tabSelectName === '造价分析' && store.currentTreeInfo.type === 3) {
    getTableData();
  }
});
const upDateAverage = () => {
  // let unitcost;
  // tableData.value &&
  //   tableData.value.map((item, index) => {
  //     if (item.name.includes('单方造价')) {
  //       unitcost = item.context ? item.context : 0;
  //     }
  //   });
  let apiData = {
    type: store.currentTreeInfo.type,
    average: average.value ? Number(average.value) : 0,
    // bizId: store.currentTreeInfo.id,
    // unitcost: unitcost,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeInfo?.parentId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    flag: false,
  };
  console.log('修改单位造价分析', apiData);

  feePro.updateCostAnalysis(apiData).then(res => {
    console.log('修改成功单位造价分析', res);
    if (res.status === 200) {
      getTableData();
    }
  });
};
const editClosedEvent = ({ row, column }) => {
  const $table = unitTable.value;
  const field = column.field;
  if (!row[field]) {
    row[field] = '0.00';
  }
  currentChange.value = row;
  // 判断单元格值是否被修改
  if ($table.isUpdateByRow(row, field)) {
    average.value = row[field];
    upDateAverage();
  }
};
defineExpose({
  getTableData,
});
</script>
<style lang="scss" scoped>
.table-content {
  max-width: 940px;
  //overflow-x: scroll;
}
</style>
