'use strict';



const {Service} = require("../../../core");
const _ = require('lodash');
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const JieSuanIndexEnum= require("../enum/JieSuanIndexEnum");
const JieSuanIndexFenXiEnum = require("../enum/JieSuanIndexFenXiEnum");
const JieSuanConstantUtil = require("../enum/JieSuanConstantUtil");
const { ConstructOperationUtil } = require('../../../electron/utils/ConstructOperationUtil');
const ConstantUtil = require('../../../electron/enum/ConstantUtil');
const majorList = require("../jsonData/zhiBiao/major_list");
const fbfxIndexList = require("../jsonData/zhiBiao/fbfx_index");
const csxmIndexList = require("../jsonData/zhiBiao/csxm_index");
const gclList = require("../jsonData/zhiBiao/gcl_index");
const indexOfMajor = require("../jsonData/zhiBiao/indexOfMajor");
const gclxIndexList = require("../jsonData/zhiBiao/jiesuan_zhibiao_gjxx_gclx.json");
const jglxIndexList = require("../jsonData/zhiBiao/jiesuan_zhibiao_gjxx_jglx.json");
const jskjIndexList = require("../jsonData/zhiBiao/jiesuan_zhibiao_gjxx_jskj.json");
const jzflIndexList = require("../jsonData/zhiBiao/jiesuan_zhibiao_gjxx_jzfl.json");
const jzfl2IndexList = require("../jsonData/zhiBiao/jiesuan_zhibiao_gjxx_jzfl2.json");
const rfIndexList = require("../jsonData/zhiBiao/jiesuan_zhibiao_gjxx_rf.json");
const zjlbIndexList = require("../jsonData/zhiBiao/jiesuan_zhibiao_gjxx_zjlb.json");
const BranchProjectLevelConstant = require('../../../electron/enum/BranchProjectLevelConstant');
const projectLevelConstant = require('../../../electron/enum/ProjectLevelConstant');
const { ConvertUtil } = require('../../../electron/utils/ConvertUtils');
const {JieSuanMainJingJiIndex}= require("../model/JieSuanMainJingJiIndex");
const {JieSuanReferIndex}= require("../model/JieSuanReferIndex");
const {JieSuanMainGclIndex}= require("../model/JieSuanMainGclIndex");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const { NumberUtil } = require('../../../electron/utils/NumberUtil');
const { UnitCostCodePrice } = require('../../../electron/model/UnitCostCodePrice');
const jbxx = require('../jsonData/zhiBiao/jiesuan_zhibiao_jbxx.json');
const gjxx = require('../../jieSuanProject/jsonData/zhiBiao/jiesuan_zhibiao_gjxx.json');
const { JieSuanProjectOverview } = require('../model/JieSuanProjectOverview');
const { StringUtils } = require('../../../electron/utils/StringUtils');


/**
 * 结算指标业务
 * @class
 */
class JieSuanMajorIndexService extends Service {

    constructor(ctx) {
        super(ctx);
        this.jeNum=2;
        this. slNum=3;
        this. flNum=2;
    }



    /**
     * 设置指标范围
     * @param args  data 平铺的修改后的整个结构
     * @returns {Promise<ResponseData>}
     */
    async setIndexViewScope(args) {
        let {data,constructId}=args;
        let constructFlatMap = ConstructOperationUtil.flatConstructTreeToMapById(constructId);
        for (let item of data) {
            let node = constructFlatMap.get(item.id);
            node.viewScopeFlag=item.viewScopeFlag;
            node.halfCheckFlag=item.halfCheckFlag;
        }
    }


    /**
     * 查看指标范围
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async queryIndexViewScope(args) {
        let result = await this.service.jieSuanProject.jieSuanProjectService.generateLevelTreeNodeStructure(args);
        //只要合同内的
        result=result.filter(i=> i.originalFlag);
        return result;
    }

    /**
     * 初始化项目指标分析
     */
    async initIndexFenXi(constructorId){
        let projectObj = PricingFileFindUtils.getProjectObjById(constructorId);
        let indexFenXi={
            "主要经济指标": JieSuanIndexFenXiEnum.METHOD1.code,
            "主要工程量指标" : JieSuanIndexFenXiEnum.METHOD1.code,
            "主要工料指标" : JieSuanIndexFenXiEnum.METHOD1.code
        };
        projectObj.indexFenXi=indexFenXi;
    }

    /**
     * 获取指标对应的分析方式
     */
    async getIndexOfFenXi(args){
        let code;
        let{type,constructId}=args;
        let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
        if(ObjectUtils.isNotEmpty(projectObj.indexFenXi)){
            let enumByCode = JieSuanIndexEnum.getEnumByCode(type).code;
            code=projectObj.indexFenXi[enumByCode];
        }
        return code;
    }

    /**
     * 修改指标对应的分析方式
     */
    async updateIndexOfFenXi(args){
        let{type,constructId,method}=args;
        let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
        if(ObjectUtils.isNotEmpty(projectObj.indexFenXi)){
            projectObj.indexFenXi[type]=method;
        }
    }


    async updateIndexMajorName(args){
        let {constructId, singleId, unitId, majorName} =args;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        unit.majorIndex=majorName;
    }

    /**
     * 指标匹配专业下拉列表
     * @param args
     * @returns {Promise<void>}
     */
    async unitMajorDropdownList(){
        let map = majorList.map(major=>major.name);
        return  map;
    }


    /**
     *指标匹配
     * @param args
     * @returns {Promise<null>}
     */
    async queryUnitIndexMatch(args){
        let {constructId, singleId, unitId, matchType,showFlag} =args;
        let data;
        switch (matchType) {
            case JieSuanConstantUtil.FB_ZHIBIAO:
                data=await this.getFbZhiBiaoPiPeiData(constructId, singleId, unitId,showFlag);
                break;
            case  JieSuanConstantUtil.CS_ZHIBIAO:
                data=await this.getCsZhiBiaoPiPeiData(constructId, singleId, unitId,showFlag);
                break;
            case  JieSuanConstantUtil.GL_ZHIBIAO:
                args.kind=0;
                args.showFlag=showFlag;
                data=await this.getZYGLZhiBiaoPiPeiData(args);
                break;
        }
        let disp=1;
        for(const i of data){
            i.dispNo=disp;
            disp++;
        }
        return  data;
    }


    async initAllUnitIndexMatch(args){
        let {constructId} =args;
        //获取所有单位
        let unitList = PricingFileFindUtils.getUnitList(constructId);
        for(const unit of unitList){
            //分部分项
            let qdList = unit.itemBillProjects.filter(qd=>qd.kind==BranchProjectLevelConstant.qd);
            for(const qd of qdList){
                if(ObjectUtils.isEmpty(qd.indicatorNameOfMajor) || ObjectUtils.isEmpty(qd.quantitIndicatorNameOfMajor)){
                    if(qd.isSupplement==0){
                        let code2=qd.bdCode.substring(0,2);
                        let code4=qd.bdCode.substring(0,4);
                        let major = indexOfMajor[code2];
                        qd.indicatorNameOfMajor=major;
                        let find = fbfxIndexList[major].find(index=>index.清单前四位==code4);
                        qd.indicatorName=(ObjectUtils.isEmpty(find) ||find.分部分项指标=="无")?null:find.分部分项指标;
                        qd.quantitIndicatorNameOfMajor=major;
                        qd.quantityIndicatorName=null;
                        qd.quantityIndicatorUnit=null;
                    }else {
                        qd.indicatorNameOfMajor=unit.majorIndex;
                        qd.indicatorName=null;
                        qd.quantitIndicatorNameOfMajor=unit.majorIndex;
                        qd.quantityIndicatorName=null;
                        qd.quantityIndicatorUnit=null;
                    }
                }
                if(ObjectUtils.isEmpty(qd.conversionCoefficient)){
                    qd.conversionCoefficient=1;
                }
            }
            //措施项目
            let qdListCs = unit.measureProjectTables.filter(qd=>qd.kind==BranchProjectLevelConstant.qd);
            for(const qd of qdListCs){
                if(ObjectUtils.isEmpty(qd.indicatorNameOfMajor) || ObjectUtils.isEmpty(qd.quantitIndicatorNameOfMajor)){
                    if(qd.isSupplement==0){
                        let code2=qd.bdCode.substring(0,2);
                        let code9=qd.bdCode.substring(0,9);
                        let major = indexOfMajor[code2];
                        qd.indicatorNameOfMajor=major;
                        let find = csxmIndexList[major].find(index=>index.对应清单前九位==code9);
                        qd.indicatorName=(ObjectUtils.isEmpty(find) || find.措施项目指标列表=="无")?null:find.措施项目指标列表;
                        qd.quantitIndicatorNameOfMajor=major;
                        qd.quantityIndicatorName=null;
                        qd.quantityIndicatorUnit=null;
                    }else {
                        qd.indicatorNameOfMajor=unit.majorIndex;
                        qd.indicatorName=null;
                        qd.quantitIndicatorNameOfMajor=unit.majorIndex;
                        qd.quantityIndicatorName=null;
                        qd.quantityIndicatorUnit=null;
                    }
                }
                if(ObjectUtils.isEmpty(qd.conversionCoefficient)){
                    qd.conversionCoefficient=1;
                }
            }
            //人材机
            let constructProjectRcjs = unit.constructProjectRcjs;
            let rcjDetailList = unit.rcjDetailList;
            for(const parentRcj of constructProjectRcjs){
                if(ObjectUtils.isEmpty(parentRcj.indicatorNameOfMajor)) {
                    parentRcj.indicatorNameOfMajor=unit.majorIndex;
                    parentRcj.indicatorName=null;
                    parentRcj.indicatorUnit=null;
                }
                if(ObjectUtils.isEmpty(parentRcj.conversionCoefficient)){
                    parentRcj.conversionCoefficient=1;
                }
            }
            for(const childrenRcj of rcjDetailList){
                if(ObjectUtils.isEmpty(childrenRcj.indicatorNameOfMajor)) {
                    childrenRcj.indicatorNameOfMajor=unit.majorIndex;
                    childrenRcj.indicatorName=null;
                    childrenRcj.indicatorUnit=null;
                }
                if(ObjectUtils.isEmpty(childrenRcj.conversionCoefficient)){
                    childrenRcj.conversionCoefficient=1;
                }
            }



        }
    }



    disPlayType = {
        "0": " ",
        "01": "部",
        "02": "部",
        "03": "清",
        "04": "定"
    }

    async getFbZhiBiaoPiPeiData(constructId, singleId, unitId,showFlag){
        // let datas =  PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes();
        let datas = await this.service.baseBranchProjectOptionService.pageSearch(1, 3000, null,
            PricingFileFindUtils.getFbFx(constructId, singleId, unitId),
            this.disPlayType, null, null).data;
        // let datas = await this.service.jieSuanProject.jieSuanItemBillProjectService.showFbfxCjJg(constructId, singleId, unitId, null, 1, 3000, null,null);
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //过滤掉定额  以及合同单价为0的数据
        if(unit.originalFlag){
            datas=datas.filter(fb=> {
                if(fb.kind==BranchProjectLevelConstant.top || fb.kind==BranchProjectLevelConstant.fb ||fb.kind==BranchProjectLevelConstant.zfb || fb.kind==BranchProjectLevelConstant.qd){
                    if(fb.kind==BranchProjectLevelConstant.qd && (ObjectUtils.isEmpty(fb.backPrice) || fb.backPrice==0 )){
                        return false;
                    }
                    return true;
                }
            });
        }else {
            datas=datas.filter(fb=> {
                if(fb.kind==BranchProjectLevelConstant.top || fb.kind==BranchProjectLevelConstant.fb ||fb.kind==BranchProjectLevelConstant.zfb || fb.kind==BranchProjectLevelConstant.qd){
                    if(fb.kind==BranchProjectLevelConstant.qd && (ObjectUtils.isEmpty(fb.total) || fb.total==0 )){
                        return false;
                    }
                    return true;
                }
            });
        }

        //只显示未设置指标项
        if(ObjectUtils.isNotEmpty(showFlag) && showFlag==true){
            datas= datas.filter(qd=>{
                if( qd.kind!=BranchProjectLevelConstant.qd){
                     return true;
                }
                if(qd.kind==BranchProjectLevelConstant.qd && (ObjectUtils.isEmpty(qd.indicatorName) || ObjectUtils.isEmpty(qd.quantityIndicatorName))) {
                    return  true;
                }
            });
        }
        //处理清单指标数据
        for (const qd of datas) {
            if(qd.kind==BranchProjectLevelConstant.qd){
                let deById = PricingFileFindUtils.getDeById(constructId, singleId, unitId,qd.sequenceNbr);
                qd.displaySign=0;
                if(ObjectUtils.isEmpty(qd.indicatorNameOfMajor) || ObjectUtils.isEmpty(qd.quantitIndicatorNameOfMajor)){
                       if(qd.isSupplement==0){
                           let code2=qd.bdCode.substring(0,2);
                           let code4=qd.bdCode.substring(0,4);
                           let major = indexOfMajor[code2];
                           qd.indicatorNameOfMajor=major;
                           let find = fbfxIndexList[major].find(index=>index.清单前四位==code4);
                           qd.indicatorName=(ObjectUtils.isEmpty(find) ||find.分部分项指标=="无")?null:find.分部分项指标;
                           qd.quantitIndicatorNameOfMajor=major;
                           qd.quantityIndicatorName=null;
                           qd.quantityIndicatorUnit=null;
                       }else {
                           qd.indicatorNameOfMajor=unit.majorIndex;
                           qd.indicatorName=null;
                           qd.quantitIndicatorNameOfMajor=unit.majorIndex;
                           qd.quantityIndicatorName=null;
                           qd.quantityIndicatorUnit=null;
                       }
                    deById.indicatorNameOfMajor=qd.indicatorNameOfMajor;
                    deById.indicatorName=qd.indicatorName;
                    deById.quantitIndicatorNameOfMajor=qd.quantitIndicatorNameOfMajor;
                    deById.quantityIndicatorName=qd.quantityIndicatorName;
                    deById.quantityIndicatorUnit=qd.quantityIndicatorUnit;
                }
                if(ObjectUtils.isEmpty(qd.conversionCoefficient)){
                    qd.conversionCoefficient=1;
                    deById.conversionCoefficient=qd.conversionCoefficient;
                }

            }
        }
        // //深拷贝一份  处理清单标识
        // datas = _.cloneDeep(datas);
        // //处理清单指标数据
        // for (const qd of datas) {
        //     if(qd.kind==BranchProjectLevelConstant.qd){
        //         qd.displaySign=0;
        //     }
        // }
        return datas;
    }


    async getCsZhiBiaoPiPeiData(constructId, singleId, unitId,showFlag){

        let datas = await this.service.stepItemCostService.pageSearch(constructId, singleId, unitId, null,1, 3000,null,null,null).data;
        // let datas =  PricingFileFindUtils.getCSXM(constructId, singleId, unitId).getAllNodes();
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        if(unit.originalFlag){
            datas=datas.filter(fb=> {
                if(fb.kind==BranchProjectLevelConstant.top || fb.kind==BranchProjectLevelConstant.fb ||fb.kind==BranchProjectLevelConstant.zfb || fb.kind==BranchProjectLevelConstant.qd){
                    if(fb.kind==BranchProjectLevelConstant.qd && (ObjectUtils.isEmpty(fb.backPrice) || fb.backPrice==0 )){
                        return false;
                    }
                    return true;
                }
            });
        }else {
            datas=datas.filter(fb=> {
                if(fb.kind==BranchProjectLevelConstant.top || fb.kind==BranchProjectLevelConstant.fb ||fb.kind==BranchProjectLevelConstant.zfb || fb.kind==BranchProjectLevelConstant.qd){
                    if(fb.kind==BranchProjectLevelConstant.qd && (ObjectUtils.isEmpty(fb.total) || fb.total==0 )){
                        return false;
                    }
                    return true;
                }
            });
        }
        //只显示未设置指标项
        if(ObjectUtils.isNotEmpty(showFlag) && showFlag==true){
            datas= datas.filter(qd=>{
                if( qd.kind!=BranchProjectLevelConstant.qd){
                    return true;
                }
                if(qd.kind==BranchProjectLevelConstant.qd && (ObjectUtils.isEmpty(qd.indicatorName) || ObjectUtils.isEmpty(qd.quantityIndicatorName))) {
                    return  true;
                }
            });
        }
        //处理清单指标数据
        for (const qd of datas) {
            if(qd.kind==BranchProjectLevelConstant.qd){
                qd.displaySign=0;
                let deById = PricingFileFindUtils.getDeById(constructId, singleId, unitId,qd.sequenceNbr);
                if(ObjectUtils.isEmpty(qd.indicatorNameOfMajor) || ObjectUtils.isEmpty(qd.quantitIndicatorNameOfMajor)){
                    if(qd.isSupplement==0){
                        let code2=qd.bdCode.substring(0,2);
                        let code9=qd.bdCode.substring(0,9);
                        let major = indexOfMajor[code2];
                        qd.indicatorNameOfMajor=major;
                        let find = csxmIndexList[major].find(index=>index.对应清单前九位==code9);
                        qd.indicatorName=(ObjectUtils.isEmpty(find) || find.措施项目指标列表=="无")?null:find.措施项目指标列表;
                        qd.quantitIndicatorNameOfMajor=major;
                        qd.quantityIndicatorName=null;
                        qd.quantityIndicatorUnit=null;
                    }else {
                        qd.indicatorNameOfMajor=unit.majorIndex;
                        qd.indicatorName=null;
                        qd.quantitIndicatorNameOfMajor=unit.majorIndex;
                        qd.quantityIndicatorName=null;
                        qd.quantityIndicatorUnit=null;
                    }
                    deById.indicatorNameOfMajor=qd.indicatorNameOfMajor;
                    deById.indicatorName=qd.indicatorName;
                    deById.quantitIndicatorNameOfMajor=qd.quantitIndicatorNameOfMajor;
                    deById.quantityIndicatorName=qd.quantityIndicatorName;
                    deById.quantityIndicatorUnit=qd.quantityIndicatorUnit;
                }
                if(ObjectUtils.isEmpty(qd.conversionCoefficient)){
                    qd.conversionCoefficient=1;
                    deById.conversionCoefficient=qd.conversionCoefficient;
                }
            }
        }
        // //深拷贝一份  处理清单标识
        // datas = _.cloneDeep(datas);
        // //处理清单指标数据
        // for (const qd of datas) {
        //     if(qd.kind==BranchProjectLevelConstant.qd){
        //
        //     }
        // }
        return datas;
    }

    async getZYGLZhiBiaoPiPeiData(args){
        let {constructId, singleId, unitId,showFlag}=args;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let unitRcjQuery =await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        //过滤结算合价为0  jieSuanTotal
        unitRcjQuery = unitRcjQuery.filter(rcj=>rcj.jieSuanTotal !=0);
        //处理指标数据
        for (const rcj of unitRcjQuery) {
                if(ObjectUtils.isEmpty(rcj.indicatorNameOfMajor)) {
                    rcj.indicatorNameOfMajor=unit.majorIndex;
                    rcj.indicatorName=null;
                    rcj.indicatorUnit=null;
                }
                if(ObjectUtils.isEmpty(rcj.conversionCoefficient)){
                    rcj.conversionCoefficient=1;
                }
        }
        //只显示未设置指标项
        if(ObjectUtils.isNotEmpty(showFlag) && showFlag==true){
            unitRcjQuery= unitRcjQuery.filter(qd=> ObjectUtils.isEmpty(qd.indicatorName));
        }
        return unitRcjQuery;
    }


    /**
     * 指标分析
     * @param args
     * @returns {Promise<null>}
     */
    async queryUnitIndexFenXi(args){
        let {constructId, fenXiType,fenXiMethod} =args;
        let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
        let data;
        switch (fenXiType) {
            case 1:
                data=await this.getMainJingJiIndexData(args);
                break;
            case  2:
                data=await this.getMainGclIndexData(args);
                break;
            case  3:
                args.kind=0;
                data=await this.getMainGongLiaoIndexData(args);
                break;
        }
        return  data;
    }





    async getMainJingJiIndexData(args){
        let {constructId, singleId, unitId, fenXiType,fenXiMethod} =args;
        //获取项目结构树
        let projectObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
        let list=[];
        switch (fenXiMethod) {
            case 1:
                list = await this.generateLevelTreeNodeByGongCheng(projectObj);
                //处理指标计算数据
                await this.countIndexData(list);
                break;
            case  2:
                list = await this.generateLevelTreeNodeByZhuanYe(projectObj);
                //处理指标计算数据
                await this.countIndexData(list);
                break;
            case  3:
                list = await this.generateLevelTreeNodeByFeiYong(projectObj);
                //处理指标计算数据
                await this.countIndexDataFeiYong(list);
                break;
        }
        return  list;
    };

    async countIndexData(list){
        if(ObjectUtils.isEmpty(list)){
            return;
        }
        //计算造价
        let obj = list.find(c=>c.levelType==1);
        let unitList = PricingFileFindUtils.getUnitList(obj.id);
        for(const qgf of list){
            if(qgf.levelType==3){
                //找到被归属的所有单位
                let filter1 = unitList.filter(u=> u.parentProjectId==qgf.id);
                let filter2 = unitList.filter(u=> u.sequenceNbr==qgf.id);
                let node = unitList.find(u=> u.sequenceNbr==qgf.id);
                let b = PricingFileFindUtils.is22Unit(node);
                filter2.push(...filter1);
                let qPrice=0;
                let gPrice=0;
                let sPrice=0;
                let qt = list.find(q=>q.levelType==6 && q.parentId==qgf.id);
                let gf = list.find(q=>q.levelType==7 && q.parentId==qgf.id);
                let sj = list.find(q=>q.levelType==8 && q.parentId==qgf.id);
                for(const u of filter2){
                    let unitCostSummarys = u.unitCostSummarys;
                    //其他项目
                    qPrice=qPrice+unitCostSummarys.find(fy=>fy.name=="其他项目清单计价合计").price;
                    sPrice=sPrice+unitCostSummarys.find(fy=>fy.name=="税金").price;
                    //规费
                    //税金
                    if(!b){
                        let find = unitCostSummarys.find(fy=>fy.name=="规费");
                        let price=ObjectUtils.isEmpty(find)?0:find.price;
                        gPrice=gPrice+price;
                        gf.price=gPrice;
                    }
                }
                qt.price=qPrice;
                sj.price=sPrice;
            }
        }
        const qdList = [...new Set(list.filter(qd=>qd.levelType==9).map(i=>i.parentId))];
        qdList.forEach(qd=>{
            this.mainIndexZjHz(qd,list);
        })
        //
        await this.countUnitPriceAndBili(list);
    }

    async countIndexDataFeiYong(list){
        if(ObjectUtils.isEmpty(list)){
            return;
        }
        //计算口径
        //获取所有的单位
        let obj = list.find(c=>c.levelType==1);
        let args={
            sequenceNbr:obj.id
        }
        const result = await this.service.jieSuanProject.jieSuanProjectService.generateLevelTreeNodeStructure(args);
        let projectObjById = PricingFileFindUtils.getProjectObjById(obj.id);
        let unitList = PricingFileFindUtils.getUnitList(obj.id);
        //所有合同内
        let htnUnit = unitList.filter(u=> u.originalFlag);
        //所有关联合同外数据
        let htwUnit = unitList.filter(u=> ObjectUtils.isNotEmpty(u.parentProjectId));
        htnUnit.push(...htwUnit);
        let allSingleHj =await this.getAllSingleHj(htnUnit,result);

        // 其他项目   规费  税金
        let qt = list.find(q=>q.levelType==6);
        let gf = list.find(q=>q.levelType==7);
        let sj = list.find(q=>q.levelType==8);
        let qPrice=0;
        let gPrice=0;
        let sPrice=0;
        for(const u of htnUnit){
            let unitCostSummarys = u.unitCostSummarys;
            //其他项目
            qPrice=NumberUtil.add(qPrice,unitCostSummarys.find(fy=>fy.name=="其他项目清单计价合计").price);
            sPrice=NumberUtil.add(sPrice,unitCostSummarys.find(fy=>fy.name=="税金").price);
            //规费
            //税金
            if(projectObjById.deStandardReleaseYear=="12"){
                let find = unitCostSummarys.find(fy=>fy.name=="规费");
                let price=ObjectUtils.isEmpty(find)?0:find.price;
                gPrice=NumberUtil.add(gPrice,price);
            }
        }
        qt.price=NumberUtil.numberScale(qPrice,this.jeNum);
        if(ObjectUtils.isNotEmpty(gf)){
            gf.price=NumberUtil.numberScale(gPrice,this.jeNum);;
        }
        sj.price=NumberUtil.numberScale(sPrice,this.jeNum);;


        //处理口径
        for(const d of list){
             if( d.levelType==4 || d.levelType==5  || d.levelType==6 || d.levelType==7 || d.levelType==8 ){
                 d.indexCountCaliber=allSingleHj;
             }
        }





        const qdList = [...new Set(list.filter(qd=>qd.levelType==9).map(i=>i.parentId))];
        qdList.forEach(qd=>{
            this.ainIndexZjHzFeiYong(qd,list);
        })
        this.countUnitPriceAndBili(list);
    }

    //计算指标分析父级数据
    mainIndexZjHz(parentId,list){
        let parent =list.find(i=>i.id==parentId);
        if(ObjectUtils.isNotEmpty(parent)){
            //获取子集分部
            parent.price=0;
            let children = list.filter(i=>i.parentId==parentId);
            if(ObjectUtils.isNotEmpty(children)){
                for(const c of children){
                    parent.price=NumberUtil.numberScale(NumberUtil.add(parent.price,c.price),this.jeNum);
                }
                this.mainIndexZjHz(parent.parentId,list);
            }

        }
    }

    ainIndexZjHzFeiYong(parentId,list){
        let parent =list.find(i=>i.id==parentId);
        if(ObjectUtils.isNotEmpty(parent) && (parent.levelType==4 || parent.levelType==5 || parent.levelType==3 || parent.levelType==1)){
            //获取子集分部
            parent.price=0;
            let children = list.filter(i=>i.parentId==parentId);
            if(ObjectUtils.isNotEmpty(children)){
                for(const c of children){
                    parent.price=NumberUtil.numberScale(NumberUtil.add(parent.price,c.price),this.jeNum);
                }
                this.ainIndexZjHzFeiYong(parent.parentId,list);
            }

        }
    }

    //
    countUnitPriceAndBili(list){
        let disp=1;
        for(const i of list){
            //计算单方造价
            i.unitPrice=NumberUtil.numberScale(NumberUtil.divide(i.price,i.indexCountCaliber),this.jeNum);
            i.disp=disp;
            disp++;
            if(ObjectUtils.isEmpty(i.price)){
                i.price=0;
            }
            //造价占比%
            if(i.levelType==1){
                i.proportionPrice=100;
            }else {
                let parent = list.find(j=> j.id==i.parentId);
                i.proportionPrice=NumberUtil.numberScale(NumberUtil.divide(i.price,parent.price)*100,this.flNum);
            }
        }

    }


/*=======================================按工程分析===============================================*/

    async generateLevelTreeNodeByGongCheng(projectObj) {
        let result = new Array();
        // 查询工程项目，生成树节点
        let constructTreeNode = new JieSuanMainJingJiIndex();
        constructTreeNode.id = projectObj.sequenceNbr;
        constructTreeNode.name = projectObj.constructName;
        constructTreeNode.indexCountCaliber = projectObj.indexCountCaliber;
        constructTreeNode.levelType = 1;
        //如果工程项目不允许查看
        if(projectObj.viewScopeFlag || projectObj.halfCheckFlag ){
            result.push(constructTreeNode);
        }else {
            return result;
        }

        //获取单项工程集合
        let singleProjects = projectObj.singleProjects;
        if(ObjectUtils.isEmpty(singleProjects)){
            return result;
        }
        for (const i of singleProjects) {
            if(i.originalFlag &&  (i.viewScopeFlag || i.halfCheckFlag)){
                let singleTreeArray =await this._generateSingleLevelTreeNode(i, constructTreeNode.id,projectObj);
                result.push(...singleTreeArray);
            }

        }
        return result;
    }

    async _generateSingleLevelTreeNode(singleProject, parentId,constructorProject) {
        let singleTreeArray = [];
        //每一个单项数据
        let singleNode = new JieSuanMainJingJiIndex();
        singleNode.id = singleProject.sequenceNbr;
        singleNode.name = singleProject.projectName;
        singleNode.indexCountCaliber = singleProject.indexCountCaliber;
        singleNode.levelType = 2;
        singleNode.parentId = parentId;
        if(singleProject.viewScopeFlag || singleProject.halfCheckFlag){
            singleTreeArray.push(singleNode);
        }
        // 子单项处理
        let subSingleProjects = singleProject.subSingleProjects;
        if (!ObjectUtils.isEmpty(subSingleProjects)) {
            for (let i = 0; i < subSingleProjects.length; i++) {
                let subTree = await this._generateSingleLevelTreeNode(subSingleProjects[i], singleNode.id,constructorProject);
                singleTreeArray.push(...subTree);
            }
        } else {
            //单位
            let unitProjects = singleProject.unitProjects;
            // let allSingleHjAll = await this.getAllSingleHjAll(unitProjects,constructorProject.sequenceNbr);
            let allSingleHjAll = singleProject.indexCountCaliber;
            for (let i in unitProjects) {
                let unitTreeNode = new JieSuanMainJingJiIndex();
                let unitProject = unitProjects[i];
                unitTreeNode.id = unitProject.sequenceNbr;
                unitTreeNode.name = unitProject.upName;
                unitTreeNode.parentId = singleProject.sequenceNbr;
                unitTreeNode.indexCountCaliber = allSingleHjAll;
                unitTreeNode.levelType = 3;
                if(unitProject.viewScopeFlag || unitProject.halfCheckFlag){
                    singleTreeArray.push(unitTreeNode);
                    await this.addMainNode(singleTreeArray,unitTreeNode,PricingFileFindUtils.is22Unit(unitProject),unitProject);
                }
            }
        }
        return singleTreeArray;
    }


    async addMainNode(list,unit,is22,unitProject){
        if(ObjectUtils.isNotEmpty(list)){
            let indexNodeFb = new JieSuanMainJingJiIndex();
            indexNodeFb.id = Snowflake.nextId();
            indexNodeFb.name = "分部分项";
            indexNodeFb.indexCountCaliber = unit.indexCountCaliber;
            indexNodeFb.levelType = 4;
            indexNodeFb.parentId = unit.id;
            list.push(indexNodeFb);
            //分部分项指标专业
            await this.indexMajorGrougBy(list,indexNodeFb,unitProject,"fbfx")
            let indexNodeCs = new JieSuanMainJingJiIndex();
            indexNodeCs.id = Snowflake.nextId();
            indexNodeCs.name = "措施项目";
            indexNodeCs.indexCountCaliber = unit.indexCountCaliber;
            indexNodeCs.levelType = 5;
            indexNodeCs.parentId = unit.id;
            list.push(indexNodeCs);
            await this.indexMajorGrougBy(list,indexNodeCs,unitProject,"csxm")
            //措施项目指标专业
            let indexNodeQt = new JieSuanMainJingJiIndex();
            indexNodeQt.id = Snowflake.nextId();
            indexNodeQt.name = "其他项目";
            indexNodeQt.indexCountCaliber = unit.indexCountCaliber;
            indexNodeQt.levelType = 6;
            indexNodeQt.parentId = unit.id;
            list.push(indexNodeQt);
            //规费
            if(!is22){
                let indexNodeGf = new JieSuanMainJingJiIndex();
                indexNodeGf.id = Snowflake.nextId();
                indexNodeGf.name = "规费";
                indexNodeGf.indexCountCaliber = unit.indexCountCaliber;
                indexNodeGf.levelType = 7;
                indexNodeGf.parentId = unit.id;
                list.push(indexNodeGf);
            }
            let indexNodeSj = new JieSuanMainJingJiIndex();
            indexNodeSj.id = Snowflake.nextId();
            indexNodeSj.name = "税金";
            indexNodeSj.indexCountCaliber = unit.indexCountCaliber;
            indexNodeSj.levelType = 8;
            indexNodeSj.parentId = unit.id;
            list.push(indexNodeSj);


        }

    }



    //指标专业数据分类
    async indexMajorGrougBy(list,node,unitProject,type){
        let arr=new Array;
       if(type=="fbfx"){
           //分部分项
           let itemBillProjects = unitProject.itemBillProjects.getAllNodes().filter(qd=>qd.kind==BranchProjectLevelConstant.qd);
           itemBillProjects.forEach(m=>{
               if(m.indicatorName=="undefined" || m.indicatorName==null || m.indicatorName=="null" || m.indicatorName==undefined){
                   m.indicatorName=null;
               }
           });
           this.getGuiShuUnitData(itemBillProjects,1,unitProject);
           let groupFbfx=this._grougBy(itemBillProjects,"indicatorName");
           let keys = Object.keys(groupFbfx);
           for(const k of keys){
               //计算清单结算合价
               let priceTotal = this.countQdJieSuanTotal(groupFbfx[k]);
               if(priceTotal!=0){
                   let fbfx = new JieSuanMainJingJiIndex();
                   fbfx.id = Snowflake.nextId();
                   if(k=="undefined" || k==null || k=="null"){
                       fbfx.name = "未匹配工程";
                   }else {
                       fbfx.name = k;
                   }
                   fbfx.indexCountCaliber = node.indexCountCaliber;
                   fbfx.levelType = 9;
                   fbfx.parentId = node.id;
                   fbfx.price=priceTotal;
                   // fbfx.unitPrice=NumberUtil.numberScale2(NumberUtil.divide(fbfx.pirce,fbfx.indexCountCaliber));
                   arr.push(fbfx);
               }

           }
           let fb=arr.filter(m=> m.name=="未匹配工程" );
           arr=arr.filter(m=>m.name!="未匹配工程");
           arr.push(...fb);

       }

        //措施项目
        if(type=="csxm"){
            let measureProjectTables = unitProject.measureProjectTables.getAllNodes().filter(qd=>qd.kind==BranchProjectLevelConstant.qd);
            measureProjectTables.forEach(m=>{
                if(m.indicatorName=="undefined" || m.indicatorName==null || m.indicatorName=="null" || m.indicatorName==undefined){
                    m.indicatorName=null;
                }
            });
            this.getGuiShuUnitData(measureProjectTables,2,unitProject);
            let groupedCsxm=this._grougBy(measureProjectTables,"indicatorName");
            let keysCs = Object.keys(groupedCsxm);
            for(const k of keysCs){
                //计算清单结算合价
                let priceTotal = this.countQdJieSuanTotal(groupedCsxm[k]);
                if(priceTotal!=0){
                    let fbfx = new JieSuanMainJingJiIndex();
                    fbfx.id = Snowflake.nextId();
                    if(k=="undefined" || k==null || k=="null"){
                        fbfx.name = "未匹配措施项目";
                    }else {
                        fbfx.name = k;
                    }
                    fbfx.indexCountCaliber = node.indexCountCaliber;
                    fbfx.levelType = 9;
                    fbfx.parentId = node.id;
                    fbfx.price=priceTotal;
                    // fbfx.unitPrice=NumberUtil.numberScale2(NumberUtil.divide(fbfx.pirce,fbfx.indexCountCaliber));
                    arr.push(fbfx);
                }
            }
            let cs=arr.filter(m=>m.name=="未匹配措施项目");
            arr=arr.filter(m=>m.name!="未匹配措施项目");
            arr.push(...cs);
        }


        // const qdList = [...new Set(arr.map(i=>i.parentId))];
        list.push(...arr);
        // qdList.forEach(qd=>{
        //     this.mainIndexHz(qd,list);
        // })


    }

     //根据字段分组
    _grougBy(list,cloum){
        const group = list.reduce((acc, item) => {
            const key = item[cloum]; // 按 category 分组
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(item);
            return acc;
        }, {});

        return group;
    }

    //获取归属的单位数据
    getGuiShuUnitData(list,type,unit){
        //获取所有的单位
        let unitList = PricingFileFindUtils.getUnitList(unit.constructId);
        for(const u of unitList){
            if(u.parentProjectId==unit.sequenceNbr){
                if(type==1){
                    list.push(... u.itemBillProjects.getAllNodes().filter(qd=>qd.kind==BranchProjectLevelConstant.qd && ObjectUtils.isNotEmpty(qd.quantityIndicatorName)));
                }else {
                    list.push(... u.measureProjectTables.getAllNodes().filter(qd=>qd.kind==BranchProjectLevelConstant.qd && ObjectUtils.isNotEmpty(qd.quantityIndicatorName)));
                }
            }
        }
    }

    async getGuiShuUnitRcjData(list,unit){
        //获取所有的单位
        let unitList = PricingFileFindUtils.getUnitList(unit.constructId);
        for(const u of unitList){
            if(u.parentProjectId==unit.sequenceNbr){
                let args={
                    constructId:u.constructId,
                    singleId:u.spId,
                    unitId:u.sequenceNbr,
                    kind:0

                }
                let unitRcjQuery =await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
                list.push(...unitRcjQuery);
            }

        }

    }

    countQdJieSuanTotal(qdList){
          let priceTotal=0;
          for(const qd of qdList){
              priceTotal=NumberUtil.numberScale(NumberUtil.add(priceTotal,qd.total),this.slNum);
          }
          return priceTotal;
    }


    async getMainGclIndexByGongCheng(projectObj) {
        let result = new Array();
        // 查询工程项目，生成树节点
        let constructTreeNode = new JieSuanMainGclIndex();
        constructTreeNode.id = projectObj.sequenceNbr;
        constructTreeNode.name = projectObj.constructName;
        constructTreeNode.indexCountCaliber = null;
        constructTreeNode.levelType = 1;
        //如果工程项目不允许查看
        if(projectObj.viewScopeFlag || projectObj.halfCheckFlag ){
            result.push(constructTreeNode);
        }else {
            return result;
        }
        //获取单项工程集合
        let singleProjects = projectObj.singleProjects;
        if(ObjectUtils.isEmpty(singleProjects)){
            return result;
        }
        for (const i of singleProjects) {
            if(i.originalFlag){
                let singleTreeArray =await this._generateSingleLevelTreeNodeGcl(i, projectObj.sequenceNbr,projectObj);
                result.push(...singleTreeArray);
            }

        }
        return result;
    }

    async _generateSingleLevelTreeNodeGcl(singleProject, parentId,constructorProject) {
        let singleTreeArray = [];
        //每一个单项数据
        let singleNode = new JieSuanMainGclIndex();
        singleNode.id = singleProject.sequenceNbr;
        singleNode.name = singleProject.projectName;
        singleNode.indexCountCaliber = null;
        singleNode.levelType = 2;
        singleNode.parentId = parentId;
        if(singleProject.viewScopeFlag || singleProject.halfCheckFlag){
            singleTreeArray.push(singleNode);
        }
        // 子单项处理
        let subSingleProjects = singleProject.subSingleProjects;
        if (!ObjectUtils.isEmpty(subSingleProjects)) {
            for (let i = 0; i < subSingleProjects.length; i++) {
                let subTree = await this._generateSingleLevelTreeNodeGcl(subSingleProjects[i], singleNode.id,constructorProject);
                singleTreeArray.push(...subTree);
            }
        } else {
            //单位
            let unitProjects = singleProject.unitProjects;
            //获取单位计算口径
            let allSingleHjAll = singleProject.indexCountCaliber;
            for (let i in unitProjects) {
                let unitTreeNode = new JieSuanMainGclIndex();
                let unitProject = unitProjects[i];
                unitTreeNode.id = unitProject.sequenceNbr;
                unitTreeNode.name = unitProject.upName;
                unitTreeNode.parentId = singleProject.sequenceNbr;
                unitTreeNode.indexCountCaliber = null;
                unitTreeNode.levelType = 3;
                if(unitProject.viewScopeFlag || unitProject.halfCheckFlag){
                    singleTreeArray.push(unitTreeNode);
                    await this.indexMajorGrougByGcl(singleTreeArray,unitTreeNode,unitProject,allSingleHjAll);
                }
            }
        }
        return singleTreeArray;
    }



    async indexMajorGrougByGcl(list,node,unitProject,allSingleHjAll){
        let arr=new Array;
        unitProject.itemBillProjects.getAllNodes().forEach(m=>{
            if(m.quantityIndicatorName=="undefined" || m.quantityIndicatorName==null || m.quantityIndicatorName=="null" || m.quantityIndicatorName==undefined){
                m.quantityIndicatorName=null;
            }
        });
        //分部分项
        let itemBillProjects = unitProject.itemBillProjects.getAllNodes().filter(qd=>qd.kind==BranchProjectLevelConstant.qd && ObjectUtils.isNotEmpty(qd.quantityIndicatorName)  );

        this.getGuiShuUnitData(itemBillProjects,1,unitProject);

        unitProject.measureProjectTables.getAllNodes().forEach(m=>{
            if(m.quantityIndicatorName=="undefined" || m.quantityIndicatorName==null || m.quantityIndicatorName=="null" || m.quantityIndicatorName==undefined){
                m.quantityIndicatorName=null;
            }
        });
        let measureProjectTables = unitProject.measureProjectTables.getAllNodes().filter(qd=>qd.kind==BranchProjectLevelConstant.qd && ObjectUtils.isNotEmpty(qd.quantityIndicatorName)  );

        this.getGuiShuUnitData(measureProjectTables,2,unitProject);
        //工程量指标专业去重
        let arr1 = itemBillProjects.map(fb=>fb.quantityIndicatorName);
        let arr2 = measureProjectTables.map(cs=>cs.quantityIndicatorName);
        arr1.push(...arr2);
        let gczy= [...new Set(arr1)];
        //组装数据
        for(const m of gczy){
            let unitTreeNode = new JieSuanMainGclIndex();
            unitTreeNode.id = Snowflake.nextId();
            unitTreeNode.name = m;
            unitTreeNode.parentId = node.id;
            unitTreeNode.indexCountCaliber = allSingleHjAll;
            unitTreeNode.levelType = 9;
            unitTreeNode.quantity = 0;
            let ts = itemBillProjects.filter(qd=> qd.quantityIndicatorName==m);
            for(const qd of ts){
                unitTreeNode.quantity=NumberUtil.add(unitTreeNode.quantity,qd.quantity);
            }
            let cs = measureProjectTables.filter(qd=> qd.quantityIndicatorName==m);
            for(const qd of cs){
                unitTreeNode.quantity=NumberUtil.add(unitTreeNode.quantity,qd.quantity);
            }
            unitTreeNode.quantity=NumberUtil.numberScale( unitTreeNode.quantity,this.slNum);
            unitTreeNode.unitQuantity = NumberUtil.numberScale(NumberUtil.divide(unitTreeNode.quantity,unitTreeNode.indexCountCaliber ),this.slNum);
            list.push(unitTreeNode);
        }


    }


    async getMainGclIndexByZhuanYe(projectObj) {
        let list = new Array();
        // 查询工程项目，生成树节点
        let constructTreeNode = new JieSuanMainGclIndex();
        constructTreeNode.id = projectObj.sequenceNbr;
        constructTreeNode.name = projectObj.constructName;
        constructTreeNode.indexCountCaliber = null;
        constructTreeNode.levelType = 1;
        list.push(constructTreeNode);
        await this.mainJingJiIndexDataGcl(projectObj,constructTreeNode,list)
        return list;
    }

    async mainJingJiIndexDataGcl(projectObj,constructTreeNode,list){
        //获取所有的单位
        let unitList = PricingFileFindUtils.getUnitList(constructTreeNode.id);
        let args={
            sequenceNbr:constructTreeNode.id
        }
        const result = await this.service.jieSuanProject.jieSuanProjectService.generateLevelTreeNodeStructure(args);
        //获取所有合同内单位
        let htnUnit = unitList.filter(u=> u.originalFlag);
        //获取所有关联单位
        let  majorOfUnit=new Array();
        for(const unit of htnUnit){
            let newObj={
                major:unit.majorIndex,
                unit:unit
            };
            majorOfUnit.push(newObj);
        }
        let grouped = majorOfUnit.reduce((acc, item) => {
            const key = item.major; // 按 category 分组
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(item);
            return acc;
        }, {});
        let strings = Object.keys(grouped);
        for(const m of strings){
            let majorTreeNode = new JieSuanMainGclIndex();
            majorTreeNode.id = Snowflake.nextId();
            majorTreeNode.name = m;
            majorTreeNode.levelType = 2;
            majorTreeNode.parentId = constructTreeNode.id;
            list.push(majorTreeNode);
            //处理单位
            let units = grouped[m].map(u=>u.unit);

            let gsUnit = unitList.filter(u=> {
                let map = units.map(u=>u.sequenceNbr);
                if(map.includes(u.parentProjectId)){
                     return true;
                }
            });
            units.push(...gsUnit)
            let indexCountCaliber=await this.getAllSingleHj(units,result);
            // majorTreeNode.indexCountCaliber=indexCountCaliber;
            // majorTreeNode.quantity = 0;
            let fbList=[];
            let csList=[];
            for(const u of units){
                let itemBillProjects = u.itemBillProjects.getAllNodes().filter(qd=>qd.kind==BranchProjectLevelConstant.qd && ObjectUtils.isNotEmpty(qd.quantityIndicatorName));
                itemBillProjects.forEach(m=>{
                    if(m.quantityIndicatorName=="undefined" || m.quantityIndicatorName==null || m.quantityIndicatorName=="null" || m.quantityIndicatorName==undefined){
                        m.quantityIndicatorName=null;
                    }
                });
                let measureProjectTables = u.measureProjectTables.getAllNodes().filter(qd=>qd.kind==BranchProjectLevelConstant.qd && ObjectUtils.isNotEmpty(qd.quantityIndicatorName));
                measureProjectTables.forEach(m=>{
                    if(m.quantityIndicatorName=="undefined" || m.quantityIndicatorName==null || m.quantityIndicatorName=="null" || m.quantityIndicatorName==undefined){
                        m.quantityIndicatorName=null;
                    }
                });
                fbList.push(...itemBillProjects);
                csList.push(...measureProjectTables);
            }



            //工程量指标专业去重
            let arr1 = fbList.map(fb=>fb.quantityIndicatorName);
            let arr2 = csList.map(cs=>cs.quantityIndicatorName);
            arr1.push(...arr2);
            let gczy= [...new Set(arr1)];
            //组装数据
            for(const m of gczy){
                let unitTreeNode = new JieSuanMainGclIndex();
                unitTreeNode.id = Snowflake.nextId();
                unitTreeNode.name = m;
                unitTreeNode.parentId = majorTreeNode.id;
                unitTreeNode.indexCountCaliber = indexCountCaliber;
                unitTreeNode.levelType = 9;
                unitTreeNode.quantity = 0;
                let ts = fbList.filter(qd=> qd.quantityIndicatorName==m);
                for(const qd of ts){
                    unitTreeNode.quantity=NumberUtil.add(unitTreeNode.quantity,qd.quantity);
                }
                let cs = csList.filter(qd=> qd.quantityIndicatorName==m);
                for(const qd of cs){
                    unitTreeNode.quantity=NumberUtil.add(unitTreeNode.quantity,qd.quantity);
                }
                unitTreeNode.quantity=NumberUtil.numberScale( unitTreeNode.quantity,this.slNum);
                unitTreeNode.unitQuantity = NumberUtil.numberScale(NumberUtil.divide(unitTreeNode.quantity,unitTreeNode.indexCountCaliber ),this.slNum);
                list.push(unitTreeNode);
            }

        }


    }



    /*=======================================按专业分析===============================================*/
    async generateLevelTreeNodeByZhuanYe(projectObj) {
        let result = new Array();
        // 查询工程项目，生成树节点
        let constructTreeNode = new JieSuanMainJingJiIndex();
        constructTreeNode.id = projectObj.sequenceNbr;
        constructTreeNode.name = projectObj.constructName;
        constructTreeNode.indexCountCaliber = projectObj.indexCountCaliber;
        constructTreeNode.levelType = 1;
        //如果工程项目不允许查看
        result.push(constructTreeNode);
        //获取所有单位
       await this.mainJingJiIndexData(projectObj,constructTreeNode,result);

        return result;
    }

    //主要经济指标 按专业分析结构数据
    async mainJingJiIndexData(projectObj,constructTreeNode,list){
          //获取所有的单位
        let unitList = PricingFileFindUtils.getUnitList(constructTreeNode.id);
        let args={
            sequenceNbr:constructTreeNode.id
        }
        const result = await this.service.jieSuanProject.jieSuanProjectService.generateLevelTreeNodeStructure(args);
        //获取所有合同内单位
        let htnUnit = unitList.filter(u=> u.originalFlag);
        //获取所有关联单位
        let  majorOfUnit=new Array();
        for(const unit of htnUnit){
            let newObj={
                major:unit.majorIndex,
                unit:unit
            };
            majorOfUnit.push(newObj);
        }
        let grouped = majorOfUnit.reduce((acc, item) => {
            const key = item.major; // 按 category 分组
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(item);
            return acc;
        }, {});
        let strings = Object.keys(grouped);
        for(const m of strings){
            let majorTreeNode = new JieSuanMainJingJiIndex();
            majorTreeNode.id = Snowflake.nextId();
            majorTreeNode.name = m;
            // majorTreeNode.indexCountCaliber = projectObj.indexCountCaliber;
            majorTreeNode.levelType = 2;
            majorTreeNode.parentId = constructTreeNode.id;
            list.push(majorTreeNode);
            //处理单位
            let unitList = grouped[m];
            let indexCountCaliber=0;
            let indexCountCaliberUnit=0;
            let ulist=[];
            for (const u of unitList) {
                ulist.push(u.unit)
            }
            indexCountCaliber = await this.getAllSingleHjAll(ulist,constructTreeNode.id);
            for (const u of unitList) {
                let unit=u.unit;
                indexCountCaliberUnit = await this.getSingleKjByUnit(result,unit.sequenceNbr);
                // indexCountCaliber=await this.diGuiSingleIndexCountCaliberHj(result,unit.sequenceNbr,indexCountCaliber);
                let unitTreeNode = new JieSuanMainJingJiIndex();
                unitTreeNode.id = unit.sequenceNbr;
                let singlePath = await this.getSinglePathByUnit(unit,result);
                unitTreeNode.name = singlePath;
                unitTreeNode.parentId = majorTreeNode.id;
                unitTreeNode.indexCountCaliber = indexCountCaliberUnit;
                unitTreeNode.levelType = 3;
                list.push(unitTreeNode);
                await this.addMainNode(list,unitTreeNode,PricingFileFindUtils.is22Unit(unit),unit);
                // indexCountCaliber=await this.diGuiSingleIndexCountCaliberHj(result,unitTreeNode.id,indexCountCaliber);
            }
            majorTreeNode.indexCountCaliber = indexCountCaliber;

        }


    }

    //获取单位的相对路径  到顶级单项
    async getSinglePathByUnit(unit,result){
        let path=unit.upName;
        path =await this.diGuiSingle(result,unit.sequenceNbr,path);
        return  path;
    }

    async diGuiSingle(result,unitId,path){
         //获取单项
        let find = result.find(i=>i.id==unitId);
        //找父级
        let parent = result.find(i=>i.id==find.parentId);
        if(parent.levelType!=1){
            path=parent.name+"/"+path;
            this.diGuiSingle(result,parent.id,path);
        }
        return path;
    }

    async getAllSingleHjAll(unitList,id){
        let args={
            sequenceNbr:id
        }
        const result = await this.service.jieSuanProject.jieSuanProjectService.generateLevelTreeNodeStructure(args);
        let allSingleHj =await this.getAllSingleHj(unitList,result);
        return  allSingleHj;
    }


    async getSingleKjByUnit(result,unitId){
        let find = result.find(i=>i.id==unitId);
        //找父级
        let parent = result.find(i=>i.id==find.parentId);
        return  parent.indexCountCaliber;
    }



    async getAllSingleHj(unitList,result){
        let map = unitList.map(u=>u.spId);
        const sIdList = [...new Set(map)];
        let indexCountCaliber=0;
        for (const s of sIdList) {
            indexCountCaliber=await this.diGuiSingleIndexCountCaliberHj(result,s,indexCountCaliber);
        }
        return NumberUtil.numberScale(indexCountCaliber,this.slNum);
    }

    //单位所在单项的口径合计
    // async diGuiSingleIndexCountCaliberHj(result,unitId,indexCountCaliber){
    //     //获取单项
    //     let find = result.find(i=>i.id==unitId);
    //     // //找父级
    //     // let parent = result.find(i=>i.id==find.parentId);
    //     if(ObjectUtils.isNotEmpty(find) && find.levelType!=1){
    //         indexCountCaliber=NumberUtil.add(indexCountCaliber,find.indexCountCaliber);
    //         this.diGuiSingleIndexCountCaliberHj(result,find.parentId,indexCountCaliber);
    //         return indexCountCaliber;
    //     }
    //
    // }

    async diGuiSingleIndexCountCaliberHj(result, unitId, indexCountCaliber = 0) {
        // 获取当前节点
        const find = result.find(i => i.id == unitId);
        // 如果节点不存在或到达顶层（levelType==1），返回当前累加值
        if (ObjectUtils.isEmpty(find) || find.levelType == 1) {
            return indexCountCaliber;
        }
        // 累加当前节点的值
        const currentSum = NumberUtil.add(indexCountCaliber, find.indexCountCaliber);
        // 递归父节点，并返回最终的累加结果
        return await this.diGuiSingleIndexCountCaliberHj(result, find.parentId, currentSum);
    }


    /*=======================================主要经济指标按费用分析===============================================*/
    async generateLevelTreeNodeByFeiYong(projectObj) {
        let result = new Array();
        // 查询工程项目，生成树节点
        let constructTreeNode = new JieSuanMainJingJiIndex();
        constructTreeNode.id = projectObj.sequenceNbr;
        constructTreeNode.name = projectObj.constructName;
        constructTreeNode.indexCountCaliber = projectObj.indexCountCaliber;
        constructTreeNode.levelType = 1;
        //如果工程项目不允许查看
        result.push(constructTreeNode);
        let indexNodeFb = new JieSuanMainJingJiIndex();
        indexNodeFb.id = Snowflake.nextId();
        indexNodeFb.name = "分部分项";
        indexNodeFb.levelType = 4;
        indexNodeFb.parentId = constructTreeNode.id;
        result.push(indexNodeFb);
        await this.batchFeiYongInde(result, constructTreeNode.id,indexNodeFb.levelType);
        let indexNodeCs = new JieSuanMainJingJiIndex();
        indexNodeCs.id = Snowflake.nextId();
        indexNodeCs.name = "措施项目";
        // indexNodeCs.indexCountCaliber = unit.indexCountCaliber;
        indexNodeCs.levelType = 5;
        indexNodeCs.parentId = constructTreeNode.id;
        result.push(indexNodeCs);
        await this.batchFeiYongInde(result, constructTreeNode.id,indexNodeCs.levelType);
        //措施项目指标专业
        let indexNodeQt = new JieSuanMainJingJiIndex();
        indexNodeQt.id = Snowflake.nextId();
        indexNodeQt.name = "其他项目";
        // indexNodeQt.indexCountCaliber = constructTreeNode.indexCountCaliber;
        indexNodeQt.levelType = 6;
        indexNodeQt.parentId = constructTreeNode.id;
        result.push(indexNodeQt);
        //规费
        if(projectObj.deStandardReleaseYear=="12"){
            let indexNodeGf = new JieSuanMainJingJiIndex();
            indexNodeGf.id = Snowflake.nextId();
            indexNodeGf.name = "规费";
            // indexNodeGf.indexCountCaliber = constructTreeNode.indexCountCaliber;
            indexNodeGf.levelType = 7;
            indexNodeGf.parentId = constructTreeNode.id;
            result.push(indexNodeGf);
        }
        let indexNodeSj = new JieSuanMainJingJiIndex();
        indexNodeSj.id = Snowflake.nextId();
        indexNodeSj.name = "税金";
        // indexNodeSj.indexCountCaliber = constructTreeNode.indexCountCaliber;
        indexNodeSj.levelType = 8;
        indexNodeSj.parentId = constructTreeNode.id;
        result.push(indexNodeSj);


        return result;
    }


    async batchFeiYongInde(list,constructId,levelType){
        let args={
            sequenceNbr:constructId
        }
        //获取所有单位
        let resultTree = await this.service.jieSuanProject.jieSuanProjectService.generateLevelTreeNodeStructure(args);
        let unitList = PricingFileFindUtils.getUnitList(constructId);
        //获取所有合同内单位
        let htnUnit = unitList.filter(u=> u.originalFlag);
        for (const node of list) {
            if(node.levelType==levelType){
                for (const unit of htnUnit) {
                    let allSingleHjAll =resultTree.find(s=>s.id==unit.spId).indexCountCaliber;
                    let unitTreeNode = new JieSuanMainJingJiIndex();
                    unitTreeNode.id = Snowflake.nextId();
                    let singlePath = await this.getSinglePathByUnit(unit,resultTree);
                    unitTreeNode.name = singlePath;
                    unitTreeNode.parentId = node.id;
                    unitTreeNode.indexCountCaliber = allSingleHjAll;
                    unitTreeNode.levelType = 3;
                    list.push(unitTreeNode);
                    // await this.addMainNode(list,unitTreeNode,PricingFileFindUtils.is22Unit(unit),unit);
                    if(levelType==4){
                        await this.indexMajorGrougBy(list,unitTreeNode,unit,"fbfx")
                    }else {
                        await this.indexMajorGrougBy(list,unitTreeNode,unit,"csxm")
                    }

                }
            }

        }

    }

    async getMainGclIndexData(args){
        // let result= await this.service.jieSuanProject.jieSuanMajorIndexService.queryIndexViewScope(args);
        let {constructId, singleId, unitId, fenXiType,fenXiMethod} =args;
        //获取项目结构树
        let projectObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
        let list=[];
        switch (fenXiMethod) {
            case 1:
                list = await this.getMainGclIndexByGongCheng(projectObj);
                break;
            case  2:
                list = await this.getMainGclIndexByZhuanYe(projectObj);
                break;
        }
        let disp=1;
        for(const i of list){
            i.disp=disp;
            disp++;
        }
        return  list;
    };


    async getMainGongLiaoIndexData(args){
        // let result= await this.service.jieSuanProject.jieSuanMajorIndexService.queryIndexViewScope(args);
        // let result= await this.service.jieSuanProject.jieSuanMajorIndexService.queryIndexViewScope(args);
        let {constructId, singleId, unitId, fenXiType,fenXiMethod} =args;
        //获取项目结构树
        let projectObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
        let list=[];
        switch (fenXiMethod) {
            case 1:
                list = await this.getMainGongLiaoIndexByGongCheng(projectObj);
                break;
            case  2:
                list = await this.getMainGongLiaoIndexByZhuanYe(projectObj);
                break;
        }
        let disp=1;
        for(const i of list){
            i.disp=disp;
            disp++;
        }
        return  list;
    };


    async getMainGongLiaoIndexByGongCheng(projectObj) {
        let result = new Array();
        // 查询工程项目，生成树节点
        let constructTreeNode = new JieSuanMainGclIndex();
        constructTreeNode.id = projectObj.sequenceNbr;
        constructTreeNode.name = projectObj.constructName;
        constructTreeNode.indexCountCaliber = null;
        constructTreeNode.levelType = 1;
        //如果工程项目不允许查看
        if(projectObj.viewScopeFlag || projectObj.halfCheckFlag ){
            result.push(constructTreeNode);
        }else {
            return result;
        }
        //获取单项工程集合
        let singleProjects = projectObj.singleProjects;
        if(ObjectUtils.isEmpty(singleProjects)){
            return result;
        }
        for (const i of singleProjects) {
            if(i.originalFlag){
                let singleTreeArray =await this._generateSingleLevelTreeNodeGl(i, projectObj.sequenceNbr,projectObj);
                result.push(...singleTreeArray);
            }

        }
        return result;
    }

    async _generateSingleLevelTreeNodeGl(singleProject, parentId,constructorProject) {
        let singleTreeArray = [];
        //每一个单项数据
        let singleNode = new JieSuanMainGclIndex();
        singleNode.id = singleProject.sequenceNbr;
        singleNode.name = singleProject.projectName;
        singleNode.indexCountCaliber = null;
        singleNode.levelType = 2;
        singleNode.parentId = parentId;
        if(singleProject.viewScopeFlag || singleProject.halfCheckFlag){
            singleTreeArray.push(singleNode);
        }
        // 子单项处理
        let subSingleProjects = singleProject.subSingleProjects;
        if (!ObjectUtils.isEmpty(subSingleProjects)) {
            for (let i = 0; i < subSingleProjects.length; i++) {
                let subTree = await this._generateSingleLevelTreeNodeGl(subSingleProjects[i], singleNode.id,constructorProject);
                singleTreeArray.push(...subTree);
            }
        } else {
            //单位
            let unitProjects = singleProject.unitProjects;
            let allSingleHjAll = singleProject.indexCountCaliber;
            for (let i in unitProjects) {
                let unitTreeNode = new JieSuanMainGclIndex();
                let unitProject = unitProjects[i];
                unitTreeNode.id = unitProject.sequenceNbr;
                unitTreeNode.name = unitProject.upName;
                unitTreeNode.parentId = singleProject.sequenceNbr;
                unitTreeNode.indexCountCaliber = null;
                unitTreeNode.levelType = 3;
                if(unitProject.viewScopeFlag || unitProject.halfCheckFlag){
                    singleTreeArray.push(unitTreeNode);
                    let args={
                        constructId:constructorProject.sequenceNbr,
                        singleId:singleProject.sequenceNbr,
                        unitId:unitTreeNode.id,
                        kind:0

                    }
                    await this.indexMajorGrougByGongLiao(singleTreeArray,unitTreeNode,unitProject,args,allSingleHjAll);
                }
            }
        }
        return singleTreeArray;
    }

    async indexMajorGrougByGongLiao(list,node,unitProject,args,allSingleHjAll){
        let arr=new Array;
        //分部分项
        let unitRcjQuery =await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        await this.getGuiShuUnitRcjData(unitRcjQuery,unitProject);
        //过滤调结算数量为0
        unitRcjQuery=unitRcjQuery.filter(r=>r.totalNumber!=0);
        unitRcjQuery.forEach(m=>{
            if(m.indicatorName=="undefined" || m.indicatorName==null || m.indicatorName=="null" || m.indicatorName==undefined){
                m.indicatorName=null;
            }
        });
        //工程量指标专业去重
        unitRcjQuery=unitRcjQuery.filter(rcj=>ObjectUtils.isNotEmpty(rcj.indicatorName));
        if(ObjectUtils.isEmpty(unitRcjQuery)){
              return ;
        }
        let arr1 = unitRcjQuery.map(rcj=>rcj.indicatorName);
        let gczy= [...new Set(arr1)];
        //组装数据
        for(const m of gczy){
            let unitTreeNode = new JieSuanMainGclIndex();
            unitTreeNode.id = Snowflake.nextId();
            unitTreeNode.name = m;
            unitTreeNode.parentId = node.id;
            unitTreeNode.indexCountCaliber = allSingleHjAll;
            unitTreeNode.levelType = 9;
            unitTreeNode.quantity = 0;
            let arr=unitRcjQuery.filter(rcj=>rcj.indicatorName==m);
            for(const rcj of arr){
                unitTreeNode.quantity=NumberUtil.add(unitTreeNode.quantity,rcj.totalNumber);
            }
            unitTreeNode.quantity=NumberUtil.numberScale(unitTreeNode.quantity,this.slNum);
            unitTreeNode.unitQuantity = NumberUtil.numberScale(NumberUtil.divide(unitTreeNode.quantity,unitTreeNode.indexCountCaliber ),this.slNum);
            list.push(unitTreeNode);
        }


    }

    async getMainGongLiaoIndexByZhuanYe(projectObj) {
        let list = new Array();
        // 查询工程项目，生成树节点
        let constructTreeNode = new JieSuanMainGclIndex();
        constructTreeNode.id = projectObj.sequenceNbr;
        constructTreeNode.name = projectObj.constructName;
        constructTreeNode.indexCountCaliber = null;
        constructTreeNode.levelType = 1;
        list.push(constructTreeNode);
        await this.mainIndexDataGongLiao(projectObj,constructTreeNode,list)
        return list;
    }

    async mainIndexDataGongLiao(projectObj,constructTreeNode,list){
        //获取所有的单位
        let unitList = PricingFileFindUtils.getUnitList(constructTreeNode.id);
        let args={
            sequenceNbr:constructTreeNode.id
        }
        const result = await this.service.jieSuanProject.jieSuanProjectService.generateLevelTreeNodeStructure(args);
        //获取所有合同内单位
        let htnUnit = unitList.filter(u=> u.originalFlag);
        //获取所有关联单位
        let  majorOfUnit=new Array();
        for(const unit of htnUnit){
            let newObj={
                major:unit.majorIndex,
                unit:unit
            };
            majorOfUnit.push(newObj);
        }
        let grouped = majorOfUnit.reduce((acc, item) => {
            const key = item.major; // 按 category 分组
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(item);
            return acc;
        }, {});
        let strings = Object.keys(grouped);
        for(const m of strings){
            let majorTreeNode = new JieSuanMainGclIndex();
            majorTreeNode.id = Snowflake.nextId();
            majorTreeNode.name = m;
            majorTreeNode.levelType = 2;
            majorTreeNode.parentId = constructTreeNode.id;
            list.push(majorTreeNode);
            //处理单位
            let units = grouped[m].map(u=>u.unit);

            let gsUnit = unitList.filter(u=> {
                let map = units.map(u=>u.sequenceNbr);
                if(map.includes(u.parentProjectId)){
                    return true;
                }
            });
            units.push(...gsUnit)
            //计算口径合计
            let indexCountCaliber=await this.getAllSingleHj(units,result);
            let rcjList=[];
            for(const u of units){
                let args={
                    constructId: constructTreeNode.id,
                    singleId:u.spId,
                    unitId:u.sequenceNbr,
                    kind:0
                }
                let unitRcjQuery =await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
                //过滤调结算数量为0
                unitRcjQuery=unitRcjQuery.filter(r=>r.totalNumber!=0);
                unitRcjQuery.forEach(m=>{
                    if(m.indicatorName=="undefined" || m.indicatorName==null || m.indicatorName=="null" || m.indicatorName==undefined){
                        m.indicatorName=null;
                    }
                });
                unitRcjQuery = unitRcjQuery.filter(rcj=>ObjectUtils.isNotEmpty(rcj.indicatorName));
                rcjList.push(...unitRcjQuery);
            }

            if(ObjectUtils.isEmpty(rcjList)){
                continue ;
            }

            let arr1 = rcjList.map(rcj=>rcj.indicatorName);
            let gczy= [...new Set(arr1)];
            //组装数据
            for(const m of gczy){
                let unitTreeNode = new JieSuanMainGclIndex();
                unitTreeNode.id = Snowflake.nextId();
                unitTreeNode.name = m;
                unitTreeNode.parentId = majorTreeNode.id;
                unitTreeNode.indexCountCaliber =indexCountCaliber;
                unitTreeNode.levelType = 9;
                unitTreeNode.quantity = 0;
                let arr=rcjList.filter(rcj=>rcj.indicatorName==m);
                for(const rcj of arr){
                    // unitTreeNode.quantity=NumberUtil.add(unitTreeNode.quantity,rcj.quantity);
                    unitTreeNode.quantity=NumberUtil.add(unitTreeNode.quantity,rcj.totalNumber);
                }
                unitTreeNode.quantity=NumberUtil.numberScale(unitTreeNode.quantity,this.slNum);
                unitTreeNode.unitQuantity = NumberUtil.numberScale(NumberUtil.divide(unitTreeNode.quantity,unitTreeNode.indexCountCaliber ),this.slNum);
                // unitTreeNode.unitQuantity = NumberUtil.numberScale4(NumberUtil.divide(unitTreeNode.quantity,unitTreeNode.indexCountCaliber ));
                list.push(unitTreeNode);
            }

        }


    }




    async querFbfxIndicatorList(){
        return  fbfxIndexList;
    }

    async querCsxmIndicatorList(){
        return  csxmIndexList;
    }
    async querGongLiaoIndicatorList(){
        return  gclList;
    }


    async querGclIndicatorList(){
        return  gclList;
    }


    async indexCachAllUnitData(args){
        let projectObj = PricingFileFindUtils.getProjectObjById(args.constructId);
        let unitList = PricingFileFindUtils.getUnitList(args.constructId);
        projectObj.unitListCach=_.cloneDeep(unitList);
    }


    async restIndexCachAllUnitData(args){
        let projectObj = PricingFileFindUtils.getProjectObjById(args.constructId);
        let unitList = PricingFileFindUtils.getUnitList(args.constructId);
        if(ObjectUtils.isEmpty(projectObj.unitListCach)){
            return ;
        }
        for(const unit of unitList){
            let u=projectObj.unitListCach.find(u=>u.sequenceNbr==unit.sequenceNbr);
            unit.majorIndex=u.majorIndex;
            unit.itemBillProjects=u.itemBillProjects;
            unit.measureProjectTables=u.measureProjectTables;
            unit.constructProjectRcjs=u.constructProjectRcjs;
            unit.rcjDetailList=u.rcjDetailList;
        }

        delete   projectObj.unitListCach;
    }

    async useUpdate(args){
        let projectObj = PricingFileFindUtils.getProjectObjById(args.constructId);
        delete   projectObj.unitListCach;
    }


    async queryIndexProjectOverview(arg){
        let list = [];
        let project = [];
        if(arg.levelType === projectLevelConstant.construct){
            //工程项目  只有基本信息
            project = PricingFileFindUtils.getConstructProjectJBXX(arg.constructId);
            //拿到基本信息后, 查询出关键信息需要的
            for (let i in gjxx) {
                let name = gjxx[i].name;
                let item = project.find(item=> item.name === name);
                if(item!==undefined){
                    let listProjectOverviewXX = new JieSuanProjectOverview();
                    listProjectOverviewXX.requiredFlag = item.requiredFlag;
                    listProjectOverviewXX.name = item.name;
                    listProjectOverviewXX.dispNo = parseInt(i)+1;
                    listProjectOverviewXX.remark = item.remark;
                    listProjectOverviewXX.context = item.context;
                    listProjectOverviewXX.type = 0;
                    listProjectOverviewXX.addFlag = 0;
                    listProjectOverviewXX.lockFlag = 0;
                    listProjectOverviewXX.sequenceNbr = Snowflake.nextId();
                    listProjectOverviewXX.jsonStr = gjxx[i].jsonStr;
                    list.push(listProjectOverviewXX);
                }
            }
        }
        return list;
    }

    async saveIndexProjectOverview(arg){
        //基本信息
        let jbxxList = this.dataHandler(arg.projectOverviewList);
        let projectObj = PricingFileFindUtils.getProjectObjById(arg.constructId);
        let oldList = projectObj.constructProjectJBXX;
        for (const item of oldList) {
            let result = jbxxList.find(i=> i.name === item.name);
            if(!ObjectUtils.isEmpty(result)){
                item.remark = result.remark;
            }

        }
        //保存之前的list
        projectObj.constructProjectJBXX = oldList;
        //联动修改下面树结构
        let list = await this.queryIndexCaliber(arg);
        for (const jbxx of jbxxList) {
            if (jbxx.name === '计算口径') {
                let jskj = jbxx.remark;
                if(jskj === '建筑面积(㎡)'){
                    jskj = '㎡';
                }
                if(jskj === '建筑长度(m)'){
                    jskj = 'm';
                }
                if(jskj === '建筑体积(m³)'){
                    jskj = 'm³';
                }
                for (const i of list) {
                    if (i.levelType === 1) {
                        if(projectObj.isUpdate !== 1){
                            projectObj.zbUnit = jskj;
                        }

                    } else {
                        let singleProject = PricingFileFindUtils.getSingleProject(arg.constructId, i.id);
                        if(singleProject.isUpdate !== 1){
                            singleProject.zbUnit = jskj;
                        }
                    }
                }
            }
            if (jbxx.name === '工程规模') {
                for (const i of list) {
                    if (i.levelType === 1) {
                        projectObj.indexCountCaliber = jbxx.remark;
                    }
                }
            }
        }
    }
    async queryIndexCaliber(arg){
        let projectObj = PricingFileFindUtils.getProjectObjById(arg.constructId);
        let result = new Array();
        // 查询工程项目，生成树节点
        let constructTreeNode = new JieSuanMainJingJiIndex();
        constructTreeNode.id = projectObj.sequenceNbr;
        constructTreeNode.name = projectObj.constructName;
        constructTreeNode.indexCountCaliber = projectObj.indexCountCaliber;
        constructTreeNode.levelType = 1;
        constructTreeNode.parentId = '';
        constructTreeNode.disNo = 1;
        constructTreeNode.zbUnit = projectObj.zbUnit; //指标单位
        //如果工程项目不允许查看
        if(projectObj.viewScopeFlag || projectObj.halfCheckFlag ){
            result.push(constructTreeNode);
        }else {
            return result;
        }

        //获取单项工程集合
        let singleProjects = projectObj.singleProjects;
        if(ObjectUtils.isEmpty(singleProjects)){
            return result;
        }
        for (const i of singleProjects) {
            if(i.originalFlag){
                let singleTreeArray = await this._generateSingleLevelTreeNode_queryIndexCaliber(i, projectObj.sequenceNbr,projectObj);
                result.push(...singleTreeArray);
            }

        }
        for (let i in result) {
            result[i].disp = parseInt(i)+1;
        }

        return result;
    }



    async saveIndexCaliber(arg){
        let projectObj = PricingFileFindUtils.getProjectObjById(arg.constructId);
        let list = arg.indexCaliberList;
        for (const i of list) {
            if(i.levelType === 1){
                projectObj.indexCountCaliber = i.indexCountCaliber;
                projectObj.zbUnit = i.zbUnit;
                projectObj.isUpdate = 1;
                let jbxx = projectObj.constructProjectJBXX;
                for (const item of jbxx) {
                    if(item.name === '工程规模'){
                        item.remark = i.indexCountCaliber;
                    }
                }
                projectObj.constructProjectJBXX = jbxx;
            }else{
                let singleProject = PricingFileFindUtils.getSingleProject(arg.constructId, i.id);
                singleProject.indexCountCaliber = i.indexCountCaliber;
                singleProject.zbUnit = i.zbUnit;
                singleProject.isUpdate = 1;
                //又不影响子单项单位了, 如果后面修改单项后需要影响子单项和单位, 放开即可
                /*if(ObjectUtils.isNotEmpty(i.children)){
                    this._querySingle(i.children, i.indexCountCaliber, i.zbUnit, arg.constructId);
                }else {
                    let unitList = PricingFileFindUtils.getUnitList(arg.constructId);
                    for (const unit of unitList) {
                        if(unit.spId === i.id){
                            unit.indexCountCaliber = i.indexCountCaliber;
                            unit.zbUnit = i.indexCountCaliber;
                        }
                    }
                }*/
            }
        }
    }
    _querySingle(item, indexCountCaliber, zbUnit, constructId) {
        for (const i of item) {
            let singleProject = PricingFileFindUtils.getSingleProject(constructId, i.id);
            singleProject.indexCountCaliber = indexCountCaliber;
            singleProject.zbUnit = zbUnit;
            if(ObjectUtils.isNotEmpty(i.children)){
                this._querySingle(i.children, indexCountCaliber, zbUnit);
            }else{
                let unitList = PricingFileFindUtils.getUnitList(constructId);
                for (const unit of unitList) {
                    if(unit.spId === i.id){
                        unit.indexCountCaliber = indexCountCaliber;
                        unit.zbUnit = zbUnit;
                    }
                }
            }
        }
    }

    async _generateSingleLevelTreeNode_queryIndexCaliber(singleProject, parentId,constructorProject) {
        let singleTreeArray = [];
        //每一个单项数据
        let singleNode = new JieSuanMainJingJiIndex();
        singleNode.id = singleProject.sequenceNbr;
        singleNode.name = singleProject.projectName;
        singleNode.indexCountCaliber = singleProject.indexCountCaliber;
        singleNode.levelType = 2;
        singleNode.parentId = parentId;
        singleNode.zbUnit = singleProject.zbUnit;
        if(singleProject.viewScopeFlag || singleProject.viewScopeFlag){
            singleTreeArray.push(singleNode);
        }
        // 子单项处理
        let subSingleProjects = singleProject.subSingleProjects;
        if (!ObjectUtils.isEmpty(subSingleProjects)) {
            for (let i = 0; i < subSingleProjects.length; i++) {
                let subTree = await this._generateSingleLevelTreeNode_queryIndexCaliber(subSingleProjects[i], singleNode.id,constructorProject);
                singleTreeArray.push(...subTree);
            }
        }
        return singleTreeArray;
    }

    /**
     * 前端传递的数据拉平
     * @param arr
     * @param result
     * @return {[]}
     */
    dataHandler(arr) {
        let projectOverviewList = arr;
        let result = new Array();
        for (let i = 0; i < projectOverviewList.length; i++) {
            const item = projectOverviewList[i];
            let childrenList = item.childrenList;
            result.push(item);
            item.childrenList = null;
            if (!ObjectUtils.isEmpty(childrenList)){
                result = result.concat(childrenList);
            }
        }
        return result;
    }

    async saveReferIndex(arg){
        let projectObj = PricingFileFindUtils.getProjectObjById(arg.constructId);
        let jieSuanReferIndex = new JieSuanReferIndex();
        jieSuanReferIndex.projectType = arg.projectType;
        jieSuanReferIndex.buildType = arg.buildType;
        jieSuanReferIndex.costType = arg.costType;
        jieSuanReferIndex.strType = arg.strType;
        jieSuanReferIndex.madeTime = arg.madeTime;
        jieSuanReferIndex.buildUnit = arg.buildUnit;
        jieSuanReferIndex.indexCountCaliber = arg.indexCountCaliber;
        jieSuanReferIndex.buildHeight = arg.buildHeight;
        projectObj.jieSuanReferIndex = jieSuanReferIndex;

    }

    async queryReferIndex(arg){
        let projectObj = PricingFileFindUtils.getProjectObjById(arg.constructId);
        return projectObj.jieSuanReferIndex;
    }

    async queryGCLXListColl(){
        return  gclxIndexList;
    }

    async queryJZFLListColl(){
        return  jzflIndexList;
    }

    async queryJSKJListColl(){
        return  jskjIndexList;
    }

    async queryZJLBListColl(){
        return  zjlbIndexList;
    }

    async queryJGLXListColl(){
        return  jglxIndexList;
    }

    async queryRFListColl(){
        return  rfIndexList;
    }

    async queryJZFL2ListColl(){
        return  jzfl2IndexList;
    }

}

JieSuanMajorIndexService.toString = () => '[class JieSuanMajorIndexService]';
module.exports = JieSuanMajorIndexService;
