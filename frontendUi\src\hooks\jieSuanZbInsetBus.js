/*
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-08-25 17:05:34
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-04-17 11:02:15
 */
import operateList from '@/jieSuan/views/projectDetail/quota/operate';

export const insetBus = (bus, storeComponentId, componentId, callback) => {
  let busList = operateList.value.filter(item =>
    item.components.includes(componentId)&&!item.public
  );
  let publicBus = operateList.value.filter(item => item.public === true);
  if (busList && storeComponentId === componentId) {
    // 防止重复触发
    busList.map((busItem, index) => {
      bus.off(busItem.name);
      bus.on(busItem.name, data => {
        callback(data);
      });
      console.log(busItem);
      if (['select', 'selectCheck', 'selectRadio'].includes(busItem.type)) {
        busItem.options.map(option=>{
          bus.off(busItem.name + option.kind);
          bus.on(busItem.name + option.kind, data => {
            callback({...data,...{activeKind:option.kind}});
          });
        })
      }
    });
  }
  if(!storeComponentId&&!componentId){
    console.log('a1',publicBus)
    publicBus.map((busItem, index) => {
      bus.off(busItem.name );
      bus.on(busItem.name, data => {
        callback(data);
      });
      if (['select', 'selectCheck', 'selectRadio'].includes(busItem.type)) {
        busItem.options.map(option=>{
          bus.off(busItem.name + option.kind);
          bus.on(busItem.name + option.kind, data => {
            callback({...data,...{activeKind:option.kind}});
          });
        })
      }
    });
   
  }
  function setIndex(index) {
    return index < 9 ? `0${index + 1}` : index + 1;
  }
};
