<!--
 * @Descripttion: 人材机汇总
-->
<template>
  <!-- :mouse-config="mouseConfig" -->
  <div class="table-content">
    <vxe-table
      align="center"
      height="100%"
      :loading="loading"
      :column-config="{ resizable: true }"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      :data="tableData"
      ref="dataSourceRef"
      border="full"
      keep-source
      class="table-edit-common"
      @edit-closed="editClosedEvent"
      @cell-click="useCellClickEvent"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
        enabled: false,
      }"
      :cell-class-name="cellClassName"
      :scroll-x="{ enabled: true, gt: 15 }"
      :scroll-y="{ enabled: true, gt: 30 }"
      @current-change="currentChange"
      show-overflow
    >
      <vxe-column
        field="unitName"
        :width="columnWidth(200)"
        title="所在单位工程"
        fixed="left"
      ></vxe-column>
      <vxe-column
        field="materialName"
        :width="columnWidth(200)"
        title="名称"
        fixed="left"
      >
        <template #default="{ row }">
          <span>{{ row.materialName }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-input
            v-if="
              row.isFyrcj == 1 &&
              Number(row.edit) !== 1 &&
              !deMapFun.isOtherMaterial(row.materialCode)
            "
            :clearable="false"
            v-model.trim="row.materialName"
            type="text"
            @blur="clear()"
          ></vxe-input>
          <span v-else>{{ row.materialName }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="specification"
        :width="columnWidth(100)"
        title="规格型号"
      >
        <template #default="{ row }">
          <span>{{ row.specification }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.specification"
            type="text"
            @blur="clear()"
            v-if="
              (row.isFyrcj == 1 &&
                Number(row.edit) !== 1 &&
                !deMapFun.isTz(row.materialCode) &&
                !deMapFun.isJxxs(row.materialCode) &&
                row.supplementDeRcjFlag != 1) ||
              ['BCRGF', 'BCCLF', 'BCSBF', 'BCJXF', 'BCZCF'].includes(
                row.materialCode.split('#')[0]
              )
            "
          ></vxe-input>
          <span v-else>{{ row.specification }}</span>
        </template>
      </vxe-column>

      <vxe-column field="unit" :width="columnWidth(70)" title="单位">
        <template #default="{ row }">
          <span>{{ row.unit }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select
            v-model="row.unit"
            transfer
            v-if="
              ['BCRGF', 'BCCLF', 'BCSBF', 'BCJXF', 'BCZCF'].includes(
                row.materialCode.split('#')[0]
              )
            "
          >
            <vxe-option
              v-for="item in unitList"
              :key="item"
              :value="item"
              :label="item"
            ></vxe-option>
          </vxe-select>
          <span v-else>{{ row.unit }}</span>
        </template>
      </vxe-column>
      <vxe-column field="totalNumber" :width="columnWidth(80)" title="数量">
        <template #default="{ row }">
          {{ decimalFormat(row.totalNumber, 'RCJ_COLLECT_TOTALNUMBER_PATH') }}
        </template>
      </vxe-column>
      <vxe-column
        v-if="projectStore.taxMade == 1"
        field="marketPrice"
        :width="columnWidth(100)"
        title="不含税市场价"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row, column }">
          <span>{{
            isChangeAva(row)
              ? '-'
              : decimalFormat(row.marketPrice, 'RCJ_COLLECT_MARKETPRICE_PATH')
          }}</span>
        </template>

        <template #edit="{ row, column }">
          <vxe-input
            v-if="
              row.isFyrcj == 1 &&
              !row.ifProvisionalEstimate &&
              row.ifLockStandardPrice !== 1 &&
              isPartEdit &&
              !(
                row.markSum == 1 &&
                (row.levelMark == 1 || row.levelMark == 2)
              ) &&
              Number(row.edit) !== 1 &&
              !deMapFun.isTz(row.materialCode) &&
              !deMapFun.isJxxs(row.materialCode) &&
              !deMapFun.isQtclf(row.materialCode) &&
              !deMapFun.isTz(row.materialCode) &&
              !isChangeAva(row)
            "
            :clearable="false"
            v-model.trim="row.marketPrice"
            type="text"
            @blur="
              row.marketPrice = pureNumber(row.marketPrice, 2);
              materialCodeChange(row);
            "
          ></vxe-input>
          <span v-else>{{
            isChangeAva(row)
              ? '-'
              : decimalFormat(row.marketPrice, 'RCJ_COLLECT_MARKETPRICE_PATH')
          }}</span>
        </template>
      </vxe-column>
      <vxe-column
        v-else
        field="marketTaxPrice"
        :width="columnWidth(110)"
        title="含税市场价"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row, column }">
          <span>{{
            isChangeAva(row)
              ? '-'
              : decimalFormat(
                  row.marketTaxPrice,
                  'RCJ_COLLECT_MARKETTAXPRICE_PATH'
                )
          }}</span>
        </template>
        <template #edit="{ row, column }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.marketTaxPrice"
            type="text"
            @blur="
              row.marketTaxPrice = pureNumber(row.marketTaxPrice, 2);
              materialCodeChange(row, 'marketTaxPrice');
            "
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        v-if="projectStore.taxMade == 1"
        field="total"
        :width="columnWidth(110)"
        title="不含税市场价合计"
      >
        <template #default="{ row, column }">
          <span>{{
            isChangeAva(row)
              ? '-'
              : decimalFormat(row.total, 'RCJ_COLLECT_TOTAL_PATH')
          }}</span>
        </template>
      </vxe-column>
      <vxe-column
        v-else
        field="totalTax"
        :width="columnWidth(100)"
        title="含税市场价合计"
      >
        <template #default="{ row, column }">
          <span>{{
            isChangeAva(row)
              ? '-'
              : decimalFormat(row.totalTax, 'RCJ_COLLECT_TOTALTAX_PATH')
          }}</span>
        </template>
      </vxe-column>

      <vxe-column
        field="ifDonorMaterial"
        :width="columnWidth(100)"
        title="供应方式"
        ><template #default="{ row }">
          {{
            cellectType?.supplyType?.find(a => a.value == row.ifDonorMaterial)
              ?.label
          }}
        </template>
        <template #edit="{ row }">
          <vxe-select
            v-if="row.checkIsShow"
            v-model="row.ifDonorMaterial"
            transfer
            @change="ifDonorMaterialChange(row)"
          >
            <vxe-option
              v-for="item in cellectType.supplyType"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
          <span v-else>{{
            cellectType?.supplyType?.find(a => a.value == row.ifDonorMaterial)
              ?.label
          }}</span>
        </template>
      </vxe-column>

      <vxe-column
        field="sourcePrice"
        :width="columnWidth(150)"
        title="价格来源"
      ></vxe-column>

      <vxe-column
        field="donorMaterialNumber"
        :width="columnWidth(100)"
        title="甲供数量"
      >
        <template #default="{ row }">
          <span v-if="row.checkIsShow">{{
            decimalFormat(
              row.ifDonorMaterial == 1 ? row.donorMaterialNumber : '',
              'RCJ_COLLECT_DONORMATERIALNUMBER_PATH'
            )
          }}</span>
          <span v-else></span>
        </template>
        <template #edit="{ row }">
          <vxe-input
            v-if="
              row.checkIsShow &&
              row.ifDonorMaterial == 1 &&
              !deMapFun.isJxxs(row.materialCode) &&
              row?.supplementDeRcjFlag != 1
            "
            :clearable="false"
            v-model.trim="row.donorMaterialNumber"
            @blur="clear()"
            @keyup="keyupDonorMaterialNumber"
          ></vxe-input>
          <span v-else-if="row.checkIsShow">{{
            row.ifDonorMaterial == 1 ? row.donorMaterialNumber : ''
          }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="producer"
        :width="columnWidth(100)"
        title="产地"
        :edit-render="{
          autofocus: '.vxe-input--inner',
        }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.producer"
            type="text"
            @blur="clear()"
            v-if="
              !deMapFun.isJxxs(row.materialCode) &&
              row?.supplementDeRcjFlag != 1
            "
          ></vxe-input>
          <span v-else>{{ row.producer }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="manufactor"
        :width="columnWidth(100)"
        title="厂家"
        :edit-render="{
          autofocus: '.vxe-input--inner',
        }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.manufactor"
            type="text"
            @blur="clear()"
            v-if="
              !deMapFun.isJxxs(row.materialCode) &&
              row?.supplementDeRcjFlag != 1
            "
          ></vxe-input>
          <span v-else>{{ row.manufactor }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="brand"
        :width="columnWidth(100)"
        title="品牌"
        :edit-render="{
          autofocus: '.vxe-input--inner',
        }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.brand"
            type="text"
            @blur="clear()"
            v-if="
              !deMapFun.isJxxs(row.materialCode) &&
              row?.supplementDeRcjFlag != 1
            "
          ></vxe-input>
          <span v-else>{{ row.producer }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="deliveryLocation"
        :width="columnWidth(100)"
        title="送达地点"
        :edit-render="{
          autofocus: '.vxe-input--inner',
        }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.deliveryLocation"
            type="text"
            @blur="clear()"
            v-if="
              !deMapFun.isJxxs(row.materialCode) &&
              row?.supplementDeRcjFlag != 1
            "
          ></vxe-input>
          <span v-else>{{ row.producer }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="qualityGrade"
        :width="columnWidth(100)"
        title="质量等级"
        :edit-render="{
          autofocus: '.vxe-input--inner',
        }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.qualityGrade"
            type="text"
            @blur="clear()"
            v-if="
              !deMapFun.isJxxs(row.materialCode) &&
              row?.supplementDeRcjFlag != 1
            "
          ></vxe-input>
          <span v-else>{{ row.producer }}</span>
        </template>
      </vxe-column>

      <template #empty>
        <span
          style="
            color: #898989;
            font-size: 14px;
            display: block;
            margin: 25px 0;
          "
        >
          <img :src="getUrl('newCsProject/none.png')" />
        </span>
      </template>
    </vxe-table>
  </div>
</template>

<script setup>
import {
  onMounted,
  onActivated,
  onDeactivated,
  onUpdated,
  ref,
  toRaw,
  watch,
  getCurrentInstance,
  provide,
  inject,
  reactive,
  defineAsyncComponent,
  nextTick,
  computed,
  watchEffect,
} from 'vue';
import xeUtils from 'xe-utils';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import feePro from '@gongLiaoJi/api/feePro';
import loadApi from '../../../../api/loadPrice';
import infoMode from '@/plugins/infoMode.js';
import { disposeDeTypeData } from '@gongLiaoJi/hooks/publicApiData';
import csProject from '@gongLiaoJi/api/csProject';
import { getUrl, pureNumber, pureNumber0 } from '@/utils/index';
import { insetBus } from '@gongLiaoJi/hooks/insetBus';
import { useDecimalPoint } from '@gongLiaoJi/hooks/useDecimalPoint.js';
const { decimalFormat } = useDecimalPoint();

import operateList, { updateOperateByName } from '../operate';
import deMapFun from '../deMap';
import gSdetailApi from '@/gongLiaoJiProject/api/projectDetail.js';
import { useFormatTableColumns } from '@gongLiaoJi/hooks/useGljFormatTableColumns.js';
import NumberUtil from '@/components/qdQuickPricing/utils/NumberUtil';
import Decimal from 'decimal.js';
import { useCellClick } from '@gongLiaoJi/hooks/useCellClick';
import { columnWidth } from '@gongLiaoJi/hooks/useSystemConfig.js';
const {
  useCellClickEvent,
  cellBeforeEditMethod,
  selectedClassName,
  resetIsRunEvent,
} = useCellClick();

const cellClassName = ({
  column,
  columnIndex,
  $columnIndex,
  row,
  rowIndex,
  $rowIndex,
}) => {
  let className = selectedClassName({ $columnIndex, row, column });

  return className;
};

const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const projectStore = projectDetailStore();
let dataSourceRef = ref(null);
let currentInfo = ref();
let loading = ref(false);
let prevTaxRate = ref(null);
const props = defineProps({
  originalCurrentData: {
    type: Object,
    default: () => {
      return null;
    },
  },
});
const mouseConfig = {
  selected: true,
};

let tableData = ref([]);
const selectOptions = [
  { type: '主材费' },
  { type: '材料费' },
  { type: '设备费' },
];

const clear = () => {
  //清除编辑状态
  const $table = dataSourceRef.value;
  $table.clearEdit();
};

// 历史数据
let historyCondition = new Map();

const getList = () => {
  let rcj = JSON.parse(JSON.stringify(props.originalCurrentData));

  let hasHis = historyCondition.get(rcj.sequenceNbr);
  if (hasHis && hasHis.length) {
    tableData.value = hasHis;
    return;
  }

  if (rcj.hasOwnProperty('ifDonorMaterial')) {
    rcj.ifDonorMaterial = +rcj.ifDonorMaterial;
  }

  let postData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    levelType: +projectStore.currentTreeInfo.type,
    isShowAnnotations: false,
    kind: projectStore.asideMenuCurrentInfo?.code,
    rcj,
  };

  if (postData.kind === undefined) {
    return;
  }

  if (projectStore.currentTreeInfo.type == 3) {
    postData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  loading.value = true;
  csProject
    .getRcjCellectAnalyseData(postData)
    .then(res => {
      if (res.result) {
        if (!hasHis) {
          let num = 1;
          tableData.value = res.result;
          checkBoxIsShow();
          historyCondition.set(rcj.sequenceNbr, tableData.value);
        }
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const clearHistoryCondition = () => {
  historyCondition.clear();
};

// 更新数据
// updata:{
//   'marketPrice':2,
//   'marketTaxPrice'：2
// }
const updateData = (rcjSequenceNbr, updata) => {
  let historyData = historyCondition.get(rcjSequenceNbr);
  let upList = Object.keys(updata);
  upList.forEach(e => {
    if (historyData) {
      historyData.forEach(j => {
        j[e] = updata[e];
      });
    }
  });

  historyCondition.set(rcjSequenceNbr, historyData);
  tableData.value = historyData;
};

const checkBoxIsShow = () => {
  tableData.value &&
    tableData.value.map(item => {
      //有父子级关系的父级二次解析，父级暂估，甲供，市场价锁定不可勾选
      if (item.markSum == 1 && (item.levelMark == 1 || item.levelMark == 2)) {
        item.checkIsShow = false;
      } else {
        item.checkIsShow = true;
      }
    });
};

// 基期价、市场价为“-
const isChangeAva = row => {
  return Number(row.isDataTaxRate) == 0;
};

const editClosedEvent = async e => {
  const { $table, row, column } = e;
  const field = column.field;

  if (field == 'donorMaterialNumber' && row?.donorMaterialNumberCopy) {
    row.donorMaterialNumber = row.donorMaterialNumberCopy;
  }

  console.log('field------------', row, field);
  // 判断单元格值没有修改;
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }

  if (column.field == 'donorMaterialNumber' && isNaN(row.donorMaterialNumber)) {
    $table.revertData(row, field);
    return;
  }

  // 市场价、甲供价以前是数字类型，编辑框后返回的是字符串，需要处理
  if (['marketPrice', 'donorMaterialPrice', 'marketTaxPrice'].includes(field)) {
    row[field] = +row[field];
  }

  let value = row[field];
  switch (field) {
    case 'unit':
    case 'materialName':
    case 'specification':
    case 'producer':
    case 'manufactor':
    case 'brand':
    case 'deliveryLocation':
    case 'qualityGrade':
      //输入长度不超过2000字符
      if (value && value.length > 2000) {
        message.warn('输入内容长度应在2000字符以内');
        row[field] = value.slice(0, 2000);
      }

      break;
  }
  row.ifDonorMaterial = row.ifDonorMaterial;
  if (field == 'marketPrice' && value < 0) {
    $table.revertData(row, field);
  } else if (
    field == 'marketPrice' &&
    value > 0 &&
    (row.marketPrice + '').length > 20
  ) {
    row.marketPrice = +(value + '').slice(0, 20);
  }

  if (field == 'marketTaxPrice' && value < 0) {
    $table.revertData(row, field);
  } else if (
    field == 'marketTaxPrice' &&
    value > 0 &&
    (row.marketTaxPrice + '').length > 20
  ) {
    row.marketTaxPrice = +(value + '').slice(0, 20);
  }

  if (field == 'donorMaterialNumber') {
    value = +value;
    console.log('value', value, row.totalNumber);
    if (row.ifDonorMaterial == 1 && value > row.totalNumber) {
      $table.revertData(row, field);
      message.warn(`甲供数量>数量，请重新输入`);
      return;
    } else if (row.ifDonorMaterial != 1 && value > row.totalNumber) {
      $table.revertData(row, field);
      message.warn(`甲供数量>数量，请重新输入`);
      return;
    } else if (
      row.ifDonorMaterial != 1 &&
      value <= row.totalNumber &&
      value > 0
    ) {
      row.ifDonorMaterial = '1';
    } else if (row.ifDonorMaterial == 1 && (value <= 0 || value == '')) {
      row.ifDonorMaterial = '0';
      row.donorMaterialNumber = '';
    }
  }
  if (field == 'kindSc' && [1, 2].includes(projectStore.currentTreeInfo.type)) {
    if (!row.transferFactor) {
      row.transferFactor = row[field] != '' ? 1 : 0;
    }
    if (row[field] == '' || row[field] == '空') {
      row.transferFactor = 0;
    }
  }
  if (field == 'transferFactor') {
    if (row.transferFactor === '') {
      row.transferFactor = 0;
    }
  }
  console.info('---------打印编辑结束------------', row);
  upDate(row, field);
};

const upDate = (row, field) => {
  console.log('1-------------1', row, field);
  let constructProjectRcj = {};
  if (
    field == 'materialName' ||
    field == 'specification' ||
    field == 'unit' ||
    field == 'ifProvisionalEstimate' ||
    field == 'ifLockStandardPrice' ||
    field == 'markSum' ||
    field == 'donorMaterialNumber' ||
    field == 'producer' ||
    field == 'manufactor' ||
    field == 'brand' ||
    field == 'deliveryLocation' ||
    field == 'annotations' ||
    field == 'isShowAnnotations' ||
    field == 'annotationsPro' ||
    field == 'annotationsSingle' ||
    field == 'isShowAnnotationsPro' ||
    field == 'isShowAnnotationsSingle' ||
    field == 'qualityGrade' ||
    field == 'kindSc' ||
    field == 'transferFactor' ||
    field == 'remark' ||
    field == 'supplyTime' ||
    field == 'donorMaterialPrice' ||
    field == 'taxRate' ||
    ['marketTaxPrice'].includes(field)
  ) {
    let rowData = ['marketTaxPrice', 'donorMaterialNumber'].includes(field)
      ? +row[field]
      : row[field];
    constructProjectRcj[field] = rowData;
  } else if (field == 'type') {
    constructProjectRcj.kind = getKind(row.type);
  } else if (field == 'ifDonorMaterial') {
    constructProjectRcj.ifDonorMaterial = row[field] ?? 0;
    console.log('row[field] ', row[field], row[field] == 1);
    if (row[field] == 1) {
      constructProjectRcj.totalNumber = row.totalNumber;
    }
  } else if (field == 'marketPrice') {
    constructProjectRcj.marketPrice = row[field];
  }
  if (field == 'donorMaterialNumber') {
    constructProjectRcj.totalNumber = row.totalNumber;
  }
  if (field == 'kindSc') {
    constructProjectRcj[field] = row[field];
    if (!row.transferFactor) {
      constructProjectRcj.transferFactor = row[field] != '' ? 1 : 0;
    }
    if (row[field] == '' || row[field] == '空') {
      constructProjectRcj.transferFactor = 0;
    }
  }
  // apiData.libraryCode = row.libraryCode;

  if (!['donorMaterialPrice'].includes(field)) {
    // 甲供价不需要这个字段ifDonorMaterial
    constructProjectRcj.ifDonorMaterial = Number(row.ifDonorMaterial ?? 0);
  }

  let typeData = {};

  typeData['constructProjectRcj'] = constructProjectRcj;

  let apiData = {
    levelType: 3,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    sequenceNbr: row.sequenceNbr,
    ...typeData,
  };
  console.log('修改人材机数据', apiData);

  if (projectStore.currentTreeInfo.type == 2) {
    apiData.singleId = projectStore.currentTreeGroupInfo?.singleId;
  }

  apiData.unitId = row.unitId; //单位ID

  let infoText = '人材机数据已修改，是否应用整个工程项目?';
  if (projectStore.currentTreeInfo.type == 2) {
    infoText = '人材机数据已修改，是否应用整个单项工程?';
  }
  infoMode.show({
    isSureModal: false,
    iconType: 'icon-querenshanchu',
    infoText,
    confirm: () => {
      updateFn(apiData);
      infoMode.hide();
    },
    close: () => {
      getList();
      infoMode.hide();
    },
  });
};

const updateFn = apiData => {
  console.log('修改人材机数据返回结果', apiData);
  csProject.updateRcjCellect(apiData).then(res => {
    console.log('修改人材机数据返回结果', res);
    if (res.status == 200) {
      bus.emit('saveSource');
      getList();
    }
  });
};

const isPartEdit = computed(() => {
  console.log(' isCurrent.value', currentInfo.value);
  return ![
    'QTCLFBFB',
    '34000001-2',
    'J00004',
    'J00031',
    'J00031',
    'C11384',
    'C00007',
    'C000200',
  ].includes(currentInfo.value?.materialCode);
});

// 市场价修改
const materialCodeChange = (row, type = 'marketPrice') => {
  row.donorMaterialPrice = '';
  if (projectStore.taxMade == 1) {
    // 不含税基期价
    if (row.marketPrice == row.baseJournalPrice) {
      row.sourcePrice = '';
    } else {
      row.sourcePrice = '自行询价';
    }
  } else {
    if (row.marketTaxPrice == row.baseJournalTaxPrice) {
      row.sourcePrice = '';
    } else {
      row.sourcePrice = '自行询价';
    }
  }
  clear();
};

const currentChange = ({ row }) => {
  currentInfo.value = { ...toRaw(row) };
  prevTaxRate.value = row?.taxRate;
};

// 切换供应方式
const ifDonorMaterialChange = row => {
  if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
    if (row.ifDonorMaterial == '0') {
      row.donorMaterialNumber = '';
    } else {
      row.donorMaterialNumber = row.totalNumber;
    }
    if (row.ifDonorMaterial == '1') {
      const taxMethod = +projectStore.taxMade;
      const pricingMethod = +projectStore.pricingMethod;
      if (taxMethod === 1 && pricingMethod === 0) {
        // 一般计税且非市场价组价，取不含税基期价
        row.donorMaterialPrice = row.baseJournalPrice;
      } else if (taxMethod === 1 && pricingMethod === 1) {
        // 一般计税且市场价组价，取不含税市场价
        row.donorMaterialPrice = row.marketPrice;
      } else if (taxMethod === 0 && pricingMethod === 0) {
        // 简易计税且非市场价组价，取含税基期价
        row.donorMaterialPrice = row.baseJournalTaxPrice;
      } else if (taxMethod === 0 && pricingMethod === 1) {
        // 简易计税且市场价组价，取含税市场价
        row.donorMaterialPrice = row.marketTaxPrice;
      }
    } else {
      row.donorMaterialPrice = '';
    }
  }
  clear();
};

const keyupDonorMaterialNumber = ({ value }) => {
  const data = value.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/, '$1$2.$3');
  currentInfo.value.donorMaterialNumber = data;
  for (let i of tableData.value) {
    if (i.sequenceNbr == currentInfo.value.sequenceNbr) {
      // 退出循环
      i.donorMaterialNumberCopy = data;
      break;
    }
  }
};

let cellectType = ref([]);
// 获取分类汇总信息
const getRadioList = () => {
  let apiData = {
    type: 1,
    levelType: 3,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo.id,
  };
  csProject.getRcjCellectTypeData(apiData).then(res => {
    console.log('getRcjCellectTypeData', res);
    cellectType.value = {
      expenseType: res.expenseType.map(item => {
        const key = Object.keys(item)[0];
        const value = item[key];
        return { label: value, value: key };
      }),
      matchingType: res.matchingType.map(item => {
        const key = Object.keys(item)[0];
        const value = item[key];
        return { label: value, value: key };
      }),
      supplyType: res.supplyType.map(item => {
        const key = +Object.keys(item)[0];
        const value = item[key];
        return { label: value, value: key };
      }),
    };
  });
};

getRadioList();
watchEffect(() => {
  if (props.originalCurrentData) {
    getList();
  }
});

defineExpose({
  updateData,
  clearHistoryCondition,
});
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  // height: 40px;
  padding: 0 10px;
}

.table-content {
  width: 100%;
  // min-height: 450px !important;
  height: calc(100% - 45px);
  background: white;
}

.table-content :deep(.vxe-table) {
  .title-bold {
    font-weight: bold;
  }

  .color-red {
    color: #de3f3f;
  }
}

::v-deep(.ant-empty) {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(25%, -25%);
}
.sortImg {
  position: relative;
  right: 3px;
  width: 22px;
}
.adjustFactorMoadl {
  .title {
    font-size: 14px;
  }
  div {
    display: flex;
    width: 100%;
    padding-bottom: 20px;
    // padding: 20px 0px 20px 5px;
    // border: 1px solid #6666;
    .ant-input {
      width: 78%;
    }
    span {
      width: 21%;
      margin: auto;
    }
  }
  .footor {
    display: flex;
    justify-content: space-between;
    width: 150px;
    margin: 10px auto 0;
  }
}
</style>
