<!--
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2024-06-11 16:20:01
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-05-15 19:21:55
-->
<template>
  <div>
    <dataReplacement v-model:visible="viList.dataReplacementvisible" />
    <fixAWF
      v-model:visible="viList.fixFWFModal"
      @refreshTableList="refreshTableList"
    ></fixAWF>
    <selfCheck
      v-model:visible="viList.checkVisible"
      @refreshTableList="refreshTableList"
    ></selfCheck>
    <rateList
      v-model:visible="viList.isRate"
      @refreshTableList="refreshTableList"
    ></rateList>
    <viewAssociations
      title="查看关联"
      v-model:visible="viList.associationsVisible"
    ></viewAssociations>
    <dataConversion
      :convertToType="activeKind"
      v-model:visible="viList.dataConversionVisible"
    >
    </dataConversion>
    <convertTo
      :convertToType="activeKind"
      v-model:visible="viList.convertToVisible"
    >
    </convertTo>
    <keyItemFiltering
      title="重点项过滤审核"
      v-model:visible="viList.keyItemFilteringVisible"
    ></keyItemFiltering>
    <retrieval-fee
      title="一键审取费"
      v-model:visible="viList.retrievalFeeVisible"
      @successCallback="refreshTableList"
    >
    </retrieval-fee>
    <modifySubmissionForReview
      v-model:visible="viList.modifyReviewVisible"
    ></modifySubmissionForReview>
    <compareMatch
      v-model:visible="viList.compareMatchVisible"
      :sequenceNbr="store.currentTreeGroupInfo?.constructId"
      :original="original"
    >
    </compareMatch>
    <!-- <modifySubmissionForReview v-model:visible="viList.modifyReviewVisible"></modifySubmissionForReview> -->
    <stageAdjustment
      v-model:visible="viList.adjustmentVisible"
      @successCallback="refreshTableList"
    ></stageAdjustment>
    <setQuantityDifference
      v-model:visible="viList.diffSettingVisible"
    ></setQuantityDifference>
    <setQuantityCoefficient
      v-model:visible="viList.quantityBatchVisible"
    ></setQuantityCoefficient>
    <multiplexContracts
      v-model:visible="viList.multiplexContractsVisible"
      @successCallback="refreshTableList"
    ></multiplexContracts>
    <viewAssociationContracts
      v-model:visible="viList.viewAssociationVisible"
      @successCallback="refreshTableList"
    ></viewAssociationContracts>
    <stageMeasurement
      v-model:visible="viList.stageMeasurementVisible"
    ></stageMeasurement>
    <batchSetTaxRemoval
      v-model:visible="viList.batchSetTaxRemovalVisible"
      @updateData="refreshTableList"
    ></batchSetTaxRemoval>
    <batchAdjustmentMaterial
      v-model:visible="viList.batchAdjustmentMaterialVisible"
      @updateData="refreshTableList"
    ></batchAdjustmentMaterial>
    <adjustmentSetting
      v-model:visible="viList.adjustmentSettingVisible"
      @updateData="refreshTableListAndAsideMenu"
    ></adjustmentSetting>
    <filterAdjustmentMaterial
      v-model:visible="viList.filterAdjustmentMaterialVisible"
      @updateData="refreshTableList"
    ></filterAdjustmentMaterial>
    <diffPriceSetting
      v-model:visible="viList.diffPriceSettingVisible"
      @updateData="refreshTableList"
    ></diffPriceSetting>
    <civilConstructionJq
      :activeKind="activeKind"
      v-model:visible="viList.civilConstructionJqVisible"
      @refreshTableList="refreshTableList"
    ></civilConstructionJq>
    <ViewFee v-model:visible="viList.viewFee"></ViewFee>
    <MainMaterials
      v-model:visible="viList.mainMaterials"
      @refreshTableList="refreshTableList"
    ></MainMaterials>
    <UnifiedPriceAdjustment
      v-model:visible="viList.unifiedPriceAdjustmentVisible"
      @refreshTableList="refreshTableList"
    ></UnifiedPriceAdjustment>
  </div>
</template>
<script setup>
import { ref, getCurrentInstance, reactive, watch, inject } from 'vue';
import { insetBus } from '@/hooks/insetBus';
import { projectDetailStore } from '@/store/projectDetail';
import projectDetailApi from '@/api/projectDetail.js';
import { message } from 'ant-design-vue';
const store = projectDetailStore();
const emits = defineEmits('updateMenuList');
const viList = reactive({
  dataReplacementvisible: false,
  fixFWFModal: false,
  checkVisible: false,
  isRate: false,
  associationsVisible: false,
  dataConversionVisible: false,
  convertToVisible: false,
  keyItemFilteringVisible: false,
  retrievalFeeVisible: false,
  modifyReviewVisible: false,
  adjustmentVisible: false,
  diffSettingVisible: false,
  quantityBatchVisible: false,
  compareMatchVisible: false,
  multiplexContractsVisible: false,
  viewAssociationVisible: false,
  stageMeasurementVisible: false,
  batchSetTaxRemovalVisible: false,
  batchAdjustmentMaterialVisible: false,
  adjustmentSettingVisible: false,
  filterAdjustmentMaterialVisible: false,
  diffPriceSettingVisible: false,
  civilConstructionJqVisible: false,
  viewFee: false,
  mainMaterials: false,
  unifiedPriceAdjustmentVisible: false,
});

// const { costAnalysisComponentRef } = inject('mainData');
let activeKind = ref();
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
insetBus(bus, null, null, async data => {
  console.log('==============1111111111', data);
  let map = {
    dataReplacement: 'dataReplacementvisible',
    'fixed-awf': 'fixFWFModal',
    viewAssociations: 'associationsVisible',
    dataConversion: 'dataConversionVisible',
    convertTo: 'convertToVisible',
    'key-item-filtering': 'keyItemFilteringVisible',
    'retrieval-fee': 'retrievalFeeVisible',
    'modify-submission-for-review': 'modifyReviewVisible',
    'comparative-match': 'compareMatchVisible',
    'stage-adjustment': 'adjustmentVisible',
    diffSetting: 'diffSettingVisible',
    quantityBatch: 'quantityBatchVisible',
    multiplexContracts: 'multiplexContractsVisible',
    'staged-dose': 'stageMeasurementVisible',
    viewAssociation: 'viewAssociationVisible',
    'batch-set-taxRemoval': 'batchSetTaxRemovalVisible',
    'batch-adjustment-material': 'batchAdjustmentMaterialVisible',
    'adjustment-setting': 'adjustmentSettingVisible',
    'filter-adjustment-material': 'filterAdjustmentMaterialVisible',
    'diff-price-setting': 'diffPriceSettingVisible',
    selfCheck: 'checkVisible',
    taxation: 'isRate',
    'civil-construction-costs-of-houses-JQ': 'civilConstructionJqVisible',
    'view-fee': 'viewFee',
    mainMaterials: 'mainMaterials',
    unifiedPriceAdjustment: 'unifiedPriceAdjustmentVisible',
  };
  const key = map[data.name];
  if (key) {
    if (data.type === 'select') {
      if (!data.activeKind) return;
      activeKind.value = data.activeKind;
    }
    if (data.name === 'modify-submission-for-review') {
      store.SET_TYPE('ys');
      const { ssConstructId, ssSingleId, constructId, singleId } =
        store.currentTreeGroupInfo;
      Object.assign(store.$state.currentTreeGroupInfo, {
        constructIdYSSH: constructId,
        singleIdYSSH: singleId,
        constructId: ssConstructId,
        singleId: ssSingleId,
      });
      const { ysshUnitId, id } = store.currentTreeInfo;
      Object.assign(store.$state.currentTreeInfo, {
        idYSSH: id,
        id: ysshUnitId,
      });
    }
    const list = {
      分部分项: 1,
      措施项目: 2,
      其他项目: 3,
      人材机汇总: 4,
      费用汇总: 5,
    };
    let code = list[store.tabSelectName];
    open(key, code);
  }
});

const refreshTableList = () => {
  if (store.componentId === 'subItemProject') {
    store.subItemProjectAutoPosition?.queryBranchDataById();
  }
  if (store.componentId === 'measuresItem') {
    store.measuresItemProjectAutoPosition?.queryBranchDataById();
  }
  if (store.componentId === 'humanMachineSummary') {
    if (store.type === 'jieSuan') {
      store.summaryProjectAutoPosition?.getInitList();
    } else {
      store.summaryProjectAutoPosition?.getHumanMachineData();
    }
  }
  if (store.componentId === 'feeWithDrawalTable') {
    store.feeWithDrawalAutoPosition?.refresh();
  }
  if (store.componentId === 'CostAnalysis') {
    store.costAnalysisComponentRef?.refreshList();
  }
  if (store.componentId === 'summaryExpense') {
    store.summaryExpenseGetList();
  }
};
const refreshTableListAndAsideMenu = () => {
  emits('updateMenuList');
};
let original = ref(null);

const open = (viName, code = null) => {
  if (viName === 'isRate') {
    get22ConstructUnit12de(viName, code);
    return;
  }
  viList[viName] = true;
  console.log(viList[viName], viList, activeKind.value);
  original.value = code;
};

const get22ConstructUnit12de = (viName, code) => {
  projectDetailApi
    .get22ConstructUnit12de({
      constructId: store.currentTreeGroupInfo?.constructId,
    })
    .then(res => {
      console.log('res', res);
      if (res.status === 200) {
        if (!res.result) {
          viList[viName] = true;
          original.value = code;
        } else {
          message.warn('该工程项目存在12定额单位工程，不可切换计税方式~');
        }
      }
    });
};
</script>
<style lang="scss" scoped></style>
