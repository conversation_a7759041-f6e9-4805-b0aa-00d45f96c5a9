<!--
 * @Descripttion: 编制页面
 * @Author: renmingming
 * @Date: 2023-05-16 14:09:29
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-06-20 11:34:08
-->
<template>
  <a-spin
    :spinning="projectStore.globalLoading.loading"
    :tip="projectStore.globalLoading.info"
    wrapperClassName="spin-yyy"
  >
    <operate
      @executeCommand="executeCommand"
      @showLoadMould="showLoadMould"
      @onCostView="onCostView"
      @onChangeTaxation="onChangeTaxation"
      @showDialog="showDialog"
    />
    <section>
      <split
        :horizontal="false"
        :ratio="isExpand ? '1/8' : `47/${winWidth}`"
        :minHorizontalTop="isExpand ? 225 : 47"
        :maxHorizontalTop="isExpand ? 400 : 47"
        :isDrop="isExpand && biddingType !== 2 ? true : false"
        :onlyPart="biddingType === 2 ? 'Bootom' : 'all'"
        style="height: 100%"
        mode="vertical"
      >
        <template #one>
          <aside
            class="aside"
            :style="isExpand ? { minWidth: '225px' } : { width: '47px' }"
            v-if="treeListCache && treeListCache[0].biddingType !== 2"
          >
            <a-tooltip>
              <template #title
                >{{ !isExpand ? '展开' : '收起' }}操作栏</template
              >
              <div class="btnExpand">
                <div
                  class="btn"
                  :style="{
                    right: !isExpand ? '-7px' : '11px',
                    zIndex: 9,
                  }"
                  @click="isExpand = !isExpand"
                >
                  <img
                    :src="
                      !isExpand
                        ? getUrl('expandnew.png')
                        : getUrl('retractnew.png')
                    "
                    alt=""
                  />
                </div>
              </div>
            </a-tooltip>
            <aside-tree
              v-if="treeData.length > 0"
              ref="asideTreeRef"
              :isExpand="isExpand"
              :treeData="treeData"
              :treeListCache="treeListCache"
              @getTreeList="getTreeList"
              @upOrDown="upOrDown"
              @drop="drop"
            />
          </aside>
        </template>
        <template #two>
          <main style="width: 100%">
            <main-content ref="mainRontentRef" />
          </main>
        </template>
      </split>
      <!-- <footer><PricePanel :priceList="priceList" /></footer> -->
      <info-modal
        v-model:infoVisible="showInfoStatus"
        :infoText="showInfoText"
        :isSureModal="false"
        @update:infoVisible="close"
        @updateCurrentInfo="showEdit"
      ></info-modal>

      <zj-mould ref="zjMouldRef" @onSuccess="onSuccessZj"></zj-mould>
      <div class="open-beta-btn" v-if="showOpenBeta">
        <CloseCircleFilled @click="showOpenBeta = false" class="icon" />
        <img class="img" src="@/assets/img/hot-red.png" alt="" />
        <img
          class="qrcode-img"
          @click="openDialog"
          src="@/assets/img/hot-qrcode.png"
          alt=""
        />
      </div>
      <!-- 费用查看 -->
      <costViewMould ref="costViewRef" />
      <beta-recommend
        v-model:visible="recommendVisible"
        @cancel="recommendVisible = false"
      ></beta-recommend>

      <!-- 切换计税方式 -->
      <gljRateList
        v-model:visible="showRateList"
        @refreshTableList="refreshTableList"
      ></gljRateList>

      <UnifiedPriceAdjustmentGlj
        v-model:visible="viList.unifiedPriceAdjustmentVisible"
        @refreshTableList="refreshTableList"
      ></UnifiedPriceAdjustmentGlj>
    </section>

    <footer>
      <TableScaleOpt />
    </footer>
  </a-spin>
</template>

<script setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watch,
  reactive,
  watchEffect,
  defineAsyncComponent,
  getCurrentInstance,
  h,
} from 'vue';
import { CloseCircleFilled } from '@ant-design/icons-vue';
import AsideTree from './AsideTree.vue';
import MainContent from './MainContent.vue';
import PricePanel from './PricePanel.vue';
import { projectDetailStore } from '@/store/projectDetail';
import { projectStore as projectStoreGlj } from '@gongLiaoJi/store/project';
import { proModelStore } from '@/store/proModel.js';
import xeUtils from 'xe-utils';
import { getUrl } from '@/utils/index';
import { useRoute } from 'vue-router';
import { message, Spin } from 'ant-design-vue';
import costViewMould from '@/components/costViewMouldGlj/index.vue';
import BetaRecommend from '@/views/csProject/header/betaRecommend.vue';
import { constructLevelTreeStructureList } from '@gongLiaoJi/api/csProject';
import csProject from '@gongLiaoJi/api/csProject';
import systemApi from '@gongLiaoJi/api/system.js';
import split from '@/components/split/index.vue';
import operate from './operate.vue';
import { nextTick } from 'process';
import { decimalDigitStore } from '@gongLiaoJi/store/decimalDigit.js';
import TableScaleOpt from '@/components/TableScaleOpt/index.vue';
import UnifiedPriceAdjustmentGlj from '@/components/UnifiedPriceAdjustmentGlj/index.vue';
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const decimalStore = decimalDigitStore();
const gljRateList = defineAsyncComponent(() =>
  import('./gljRateList/index.vue')
);
const isExpand = ref(true);
const projectStore = projectDetailStore();
const store = projectStoreGlj();
const ModelStore = proModelStore();
let asideTreeRef = ref(null);
let recommendVisible = ref(false);
let showOpenBeta = ref(true);

const mainRontentRef = ref();
let treeData = ref([]);
let treeListCache = ref();
const route = useRoute();
const showInfoStatus = ref(false);
let showInfoText = ref('');
let priceList = ref([]); //底部数据列表
let timer = ref(); //获取底部费用循环
console.log('constructSequenceNbr----', route.query.constructSequenceNbr);
Spin.setDefaultIndicator({
  indicator: h('img', {
    // src: `${getUrl('jijialoading.gif')}`,
    src: 'https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/jijiasoft/jijialoading.gif',
    style: {
      width: '130px',
      height: '130px',
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50% , -80%)',
    },
  }),
});

watchEffect(() => {
  showInfoStatus.value = ModelStore.onInfoModal;
  projectStore.asideTreeRef = asideTreeRef.value;
  showInfoText.value =
    projectStore.currentTreeInfo?.type === 1
      ? '当前工程项目存在未设置工程专业，请前往设置专业'
      : '当前单位工程未设置专业，请前往设置专业';
});
let winWidth = ref();
watch(
  () => isExpand.value,
  () => {
    if (!isExpand.value) {
      winWidth.value = window.innerWidth;
    }
  }
);
watch(
  () => projectStore.isRefreshProjectTree,
  () => {
    if (projectStore.isRefreshProjectTree) {
      getTreeList();
    }
  }
);
watch(
  () => treeData.value,
  val => {
    projectStore.SET_ASIDETREE_DATA(val);
  }
);

watchEffect(() => {
  console.log('sffdsfdsfdsfsdfsdfsf', projectStore.currentTreeGroupInfo);
});
const close = () => {
  ModelStore.onInfoModal = false;
};

const openDialog = () => {
  recommendVisible.value = true;
};
// 打开提示文字
const showEdit = () => {
  ModelStore.onInfoModal = false;
  ModelStore.onEditModal(true);
};

let childItemGetList = ref(null);
let pageType = ref(null);

watchEffect(() => {
  let data = mainRontentRef.value?.childComponentRef;
  let componentId = mainRontentRef.value?.componentId;

  switch (componentId) {
    case 'summaryExpense':
      pageType.value = 'fyhz';
      break;
    case 'measuresItem':
      pageType.value = 'csxm';
      break;
    default:
      pageType.value = '';
      break;
  }

  if (!projectStore.subItemProjectAutoPosition && data?.posRow) {
    projectStore.subItemProjectAutoPosition = data?.posRow;
  }

  if (
    !projectStore.measuresItemProjectAutoPosition &&
    data?.measuresItemPosRow
  ) {
    projectStore.measuresItemProjectAutoPosition = data?.measuresItemPosRow;
  }
});

// 费用查看
let costViewRef = ref(null);
const onCostView = () => {
  costViewRef.value.open();
};

// 载入模板相关
let zjMouldRef = ref(null);
const showLoadMould = () => {
  zjMouldRef.value.open(pageType.value);
};

const onSuccessZj = () => {
  zjMouldRef.value.close();
  if (pageType.value == 'fyhz' && projectStore.summaryExpenseGetList) {
    projectStore.summaryExpenseGetList();
  } else if (pageType.value == 'csxm' && projectStore.measuresItemGetList) {
    projectStore?.measuresItemGetList();
  }
};

const executeCommand = item => {
  // console.log(mainRontentRef.value.childComponentRef[item.provideFun]())
  const child = mainRontentRef.value.childComponentRef;
  if (child && typeof child[item.provideFun] === 'function') {
    child[item.provideFun]();
  }
};

const getChange = (newVal, oldVal) => {
  let change;
  change = newVal.filter(x => !oldVal.some(y => y.id === x.id));
  let moreChange = oldVal.filter(x => !newVal.some(y => y.id === x.id));
  if (change.length > 0) {
    let addList = [];
    change.map(item => {
      if (item.type === 3) {
        addList.push({ clickTab: '项目概况', id: item.id });
      }
    });
    let list = [...projectStore.proCheckTab, ...addList];
    let resultList = list.filter(x => !moreChange.some(y => y.id === x.id));
    projectStore.SET_PRO_CHECK_TAB(resultList);
    // projectStore.SET_TAB_SELECT_NAME('项目概况');
  }
};
let biddingType = ref();
let moveList = reactive([]); //单项上下移动，被替换目标
let dataList = reactive([]);
let tarList = reactive([]);
const getIsFlag = type => {
  let flag = true;
  let sameLevel = dataList.filter(
    item => item.parentId === projectStore.currentTreeInfo.parentId
  );
  if (
    (sameLevel[0].id === projectStore.currentTreeInfo.id && type === 'up') ||
    (sameLevel[sameLevel.length - 1].id === projectStore.currentTreeInfo?.id &&
      type === 'down')
  ) {
    flag = false;
  }
  return flag;
};
const drop = data => {
  //左侧树拖拽功能
  let copyTreeList = JSON.parse(JSON.stringify(treeData.value));
  let list = [];
  data.map(i => {
    list.push(copyTreeList.find(a => a.id === i));
  });
  treeData.value = list;
  dataList = xeUtils.clone(treeData.value, true);
};
const upOrDown = type => {
  //点击上下移动操作
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo.constructId,
    sequenceNbr: projectStore.currentTreeInfo.sequenceNbr,
    type,
  };
  csProject.moveUpAndDown(apiData).then(res => {
    if (res.status === 200) {
      message.success('操作成功');
      getTreeList(true);
    }
  });
};
const getSelfList = tar => {
  tarList.push(tar);
  tar.children.map(i => {
    getSelfList(i);
  });
};
const getMoveList = (tar, list, type) => {
  if (type === 'up') {
    if (tar.parentId === projectStore.currentTreeInfo.parentId) {
      //同级移动切没有子级
      moveList.unshift(tar);
    } else {
      list.map(item => {
        if (item.id === tar.id || item.parentId === tar.parentId) {
          moveList.push(item);
        }
      });
      let nextTar = list.find(i => i.id === tar.parentId);
      getMoveList(nextTar, list);
    }
  } else {
    moveList.push(tar);
    list.map(i => {
      if (i.parentId === tar.id) {
        getMoveList(i, list, type);
      }
    });
  }
};
const getTreeList = (isMove = false, isDelete = false, delIndex = 0) => {
  constructLevelTreeStructureList(route.query.constructSequenceNbr).then(
    res => {
      // console.log('res-getTreeList', res, '设置定额类型', projectStore.deType);
      if (res.status !== 200) {
        message.error(res.message);
        return false;
      }
      console.info('左侧树结构数据', res.result);
      if (res.status === 200) {
        projectStore.SET_TYPE('glj');
        let datas = res.result;
        for (let i in datas) {
          datas[i].id = datas[i].sequenceNbr;
        }
        treeListCache.value = xeUtils.clone(datas, true);
        let proCheckTab;
        proCheckTab =
          treeListCache.value &&
          treeListCache.value.filter(item => Number(item.type) !== 2);
        let resData = null;
        csProject
          .getTableSettingCache({ constructId: datas[0].sequenceNbr })
          .then(getRes => {
            console.log(
              '-----getTableSettingCache-----',
              getRes,
              isMove,
              isDelete,
              datas
            );
            if (getRes.status !== 200) {
              return message.error(getRes.message);
            }
            resData = getRes.result;
            if (!isMove && !isDelete) {
              // 如果是第一次打开项目需要初始化
              if (!resData) {
                let gljSelData = {
                  selLeftTreeId: datas[0].sequenceNbr,
                };
                let selTreeRow = treeListCache.value[0];
                if (
                  projectStore.currentTreeInfo &&
                  datas?.length > 0 &&
                  datas.find(
                    item =>
                      item.sequenceNbr ===
                      projectStore.currentTreeInfo.sequenceNbr
                  )
                ) {
                  selTreeRow = projectStore.currentTreeInfo;
                }
                let selTabName = '项目概况';
                if (selTreeRow.type == 1) {
                  selTabName = '项目概况';
                } else if (selTreeRow.type == 2) {
                  selTabName = '造价分析';
                } else {
                  selTabName = '工程概况';
                }
                gljSelData[datas[0].sequenceNbr] = {
                  selTabName,
                  tabList: [],
                  reportTreeId: '',
                };
                projectStore.SET_CURRENT_TREE_INFO(selTreeRow, 1);
                projectStore.SET_PRO_BIDDINGTYPE(2);
                projectStore.SET_PROJECT_NAME(datas[0]?.name);
                if (datas?.length > 0) {
                  store.SET_PROJECT_PATH(datas[0]?.path);
                }
                projectStore.SET_CURRENT_TREE_GROUP_INFO({
                  constructId: datas[0].sequenceNbr,
                  singleId: datas[0].sequenceNbr,
                  name: datas[0].name,
                });
                projectStore.SET_GLJ_CHECK_TAB(gljSelData);
              } else {
                projectStore.SET_GLJ_CHECK_TAB(resData);
                let selTreeObj = datas.find(
                  a => a.sequenceNbr == resData.selLeftTreeId
                );
                projectStore.SET_CURRENT_TREE_INFO(selTreeObj, 1);
                projectStore.SET_PRO_BIDDINGTYPE(2);
                projectStore.SET_PROJECT_NAME(datas[0]?.name);
                if (datas?.length > 0) {
                  store.SET_PROJECT_PATH(datas[0]?.path);
                }
                projectStore.SET_CURRENT_TREE_GROUP_INFO({
                  constructId: datas[0].sequenceNbr,
                  singleId: datas[0].sequenceNbr,
                  name: datas[0].name,
                });
              }
              projectStore.SET_IS_REFRESH_PROJECT_TREE(false);
              getSettingData(datas[0].sequenceNbr);
            }
            // 如果是删除之后调用，则默认选中删除的上一个工程
            if (isDelete) {
              projectStore.SET_CURRENT_TREE_INFO(datas[delIndex - 1], 1);
              projectStore.SET_PROJECT_NAME(datas[delIndex - 1]?.name);
            }
            if (!projectStore.isRefreshProjectTree) {
              // if (!projectStore.proCheckTab) {
              let list = [];
              proCheckTab.map(item => {
                list.push({
                  clickTab:
                    projectStore.gljCheckTab[
                      projectStore.gljCheckTab.selLeftTreeId
                    ].selTabName,
                  // clickTab: '项目概况',
                  id: item.id,
                });
              });
              projectStore.SET_PRO_CHECK_TAB(list); //此处设置除单项之外的项目点击的tab栏都是项目概况
              // } else {
              //   getChange(proCheckTab, projectStore.proCheckTab);
              // }
            }
            treeData.value = datas;
            dataList = xeUtils.clone(datas, true);
          });
      }
    }
  );
};
const getSettingData = sequenceNbr => {
  csProject
    .getGljSetUp({
      constructId: sequenceNbr,
    })
    .then(res => {
      if (res.status === 200) {
        projectStore.SET_CONVENIENCE_SETTINGS(res.result);
      }
    });
};
// 概算未做
const getConstructConfigByConstructId = () => {
  // csProject
  //   .getConstructConfigByConstructId(route.query.constructSequenceNbr)
  //   .then(res => {
  //     if (res.status === 200) {
  //       projectStore.SET_CONSTRUCT_CONFIG_INFO(res.result);
  //       projectStore.deType = res.result.deStandardReleaseYear;
  //       projectStore.taxMade = res.result.taxMode && Number(res.result.taxMode);
  //     }
  //   });

  let apiData = {
    constructId: route.query.constructSequenceNbr,
  };

  csProject.gljProjectPricingMethod(apiData).then(res => {
    if (res.status !== 200) {
      return message.error(res.message);
    }
    projectStore.pricingMethod = res.result;
    projectStore.setOption.isScj = res.result;
  });

  csProject.gljProjectTaxCalculationMethod(apiData).then(res => {
    if (res.status !== 200) {
      return message.error(res.message);
    }
    projectStore.taxMade = res.result;
  });
};

const save = event => {
  if (event.ctrlKey && event.code == 'KeyS') {
    let gljSelData = JSON.parse(JSON.stringify(projectStore.gljCheckTab));
    let params = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      tableSetting: gljSelData,
      currentId: projectStore.currentTreeInfo?.id,
    };
    csProject.setTableSettingCache(params).then(res => {
      if (res.status === 200) {
        console.log('设置成功6666', store.currentTreeInfo);
      }
    });
    projectStore.SET_GLOBAL_LOADING({
      loading: true,
      info: '正在保存中...',
    });
    csProject
      .saveGljfFile(route.query.constructSequenceNbr)
      .then(res => {
        message.success('保存成功');
      })
      .finally(() => {
        projectStore.SET_GLOBAL_LOADING({ loading: false });
      });
  }
};

// 获取小数点精度设置
systemApi
  .getPrecisionSetting({
    constructId: route.query.constructSequenceNbr,
  })
  .then(res => {
    console.log('小数点精度设置返回值', res.result);
    decimalStore.setProjectDecimalConfig(
      route.query.constructSequenceNbr,
      res.result
    );
  });

let viList = reactive({
  unifiedPriceAdjustmentVisible: false,
});
const showDialog = data => {
  let map = {
    unifiedPriceAdjustmentGLJ: 'unifiedPriceAdjustmentVisible',
  };
  const key = map[data.name];
  if (key) {
    if (data.type === 'select') {
      if (!data.activeKind) return;
    }
    viList[key] = true;
  }
};

// 切换计税方式
const onChangeTaxation = () => {
  showRateList.value = true;
};

let showRateList = ref(false);
const refreshTableList = () => {
  mainRontentRef.value?.refreshTableList();
};
onMounted(() => {
  getTreeList();
  projectStore.SET_PRO_CHECK_TAB(null); //此处初始化tab的点击记忆
  timer.value = setInterval(() => {}, 2000);
  // 概算未做
  getConstructConfigByConstructId();
  window.addEventListener('keydown', save);
});
onBeforeUnmount(() => {
  window.removeEventListener('keydown', save);
  clearInterval(timer.value);
  timer.value = null;
});
</script>
<style lang="scss" scoped>
.open-beta-btn {
  position: absolute;
  right: 7px;
  bottom: 30%;
  z-index: 10;
  .icon {
    position: absolute;
    right: 7px;
    top: -10px;
    width: 20px;
    height: 20px;
    opacity: 0.4;
    z-index: 11;
    cursor: pointer;
  }
  .img {
    display: block;
    width: 91px;
    height: 91px;
    cursor: pointer;
    &:hover {
      & + .qrcode-img {
        display: block;
      }
    }
  }
  .qrcode-img {
    display: none;
    position: absolute;
    left: -114px;
    top: 50%;
    transform: translateY(-50%);
    width: 121px;
    height: 127px;
  }
}
section {
  display: flex;
  flex-wrap: wrap;
  height: calc(100vh - 154px);
}
// .expand {
//   width: 20px;
//   height: 20px;
//   box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
//   border-radius: 50%;
//   opacity: 1;
//   position: absolute;
//   top: 10px;
//   right: -10px;
//   text-align: center;
//   line-height: 16px;
//   // padding: 4px;
//   // z-index: 999;
//   z-index: 1;
//   cursor: pointer;
//   &-icon {
//     width: 12px;
//     height: 12px;
//   }
//   .isrotateY {
//     transform: rotateY(180deg);
//   }
// }
.btnExpand {
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-25%);
  width: 14px;
  font-size: 12px;
  height: 80px;
  z-index: 9; //存在弹框会出现按钮---调小
  text-align: center;
  transition: all 0.1s linear;
  cursor: pointer;
  user-select: none;
  .btn {
    display: none;
  }
  span {
    display: inline-block;
    transform: translateX(-1px);
    transition: all 0.4s;
  }
}
.btnExpand:hover .btn {
  display: block;
}
footer {
  position: relative;
  width: 100%;
  height: 33px;
  background-color: #d9e1ef;
}
aside {
  // min-width: 225px;
  // max-width: 100%;
  height: calc(100% - 33px);
  width: 100%;
  // height: 100%;
  border-right: 2px solid #dcdfe6;
  background: #f8fbff;
  border-right: 2px solid #dcdfe6;
  position: relative;
  color: white;
  font-size: 12px;
  text-align: center;
  transition: width 0.3s linear;
  &:hover .btn {
    display: block;
  }
}
main {
  flex: 1;
  width: calc(100% - 263px);
}
.spin-yyy {
  // z-index: 99 !important;
  :deep(.ant-spin) {
    // top: 20% !important;
  }
}
</style>
