<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-19 10:12:32
-->
<template>
  <div class="table-content">
    <child-page-table
      v-if="originalFlag"
      :pageType="'jrg'"
      :columnList="createList()"
    ></child-page-table>
    <child-page-table
      v-else
      :pageType="'jrg'"
      :columnList="createList()"
    ></child-page-table>
  </div>
</template>
<script setup>
import ChildPageTable from './childPageTable.vue';
import { projectDetailStore } from '@/store/projectDetail';
const projectStore = projectDetailStore();
import { onActivated, ref,watch } from 'vue';
import { createListHooks } from './columns.js';
const { createList } = createListHooks('jrg');
const originalFlag = ref(false);
const columnListInner = [
  {
    field: 'dispNo',
    title: '序号',
    minWidth: 60,
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'dispNo_edit' },
  },
  {
    field: 'worksName',
    title: '名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'worksName_edit' },
  },
  {
    field: 'specification',
    title: '规格型号',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'spec_edit' },
  },
  {
    field: 'unit',
    title: '单位',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'unit_edit' },
  },
  {
    field: 'jieSuanTentativeQuantity',
    minWidth: 80,
    title: '合同数量',
  },
  {
    field: 'jieSuanPrice',
    title: '合同综合单价',
    minWidth: 130,
  },
  {
    field: 'jieSuanTotal',
    minWidth: 80,
    title: '合同合价',
  },
  {
    field: 'jieSuanTaxRemoval',
    title: '合同除税系数(%)',
    minWidth: 180,
  },
  {
    field: 'jieSuanJxTotal',
    minWidth: 100,
    title: '合同进项合计',
  },
  {
    field: 'jieSuanCsPrice',
    minWidth: 100,
    title: '合同除税单价',
  },
  {
    field: 'jieSuanCsTotal',
    minWidth: 100,
    title: '合同除税合价',
  },
  {
    field: 'quantitativeExpression',
    title: '结算数量表达式',
    minWidth: 180,
    type: 'text',
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'jiesuanQuant_edit' },
  },
  {
    field: 'tentativeQuantity',
    minWidth: 100,
    title: '结算数量',
    // editRender: { autofocus: '.vxe-input--inner' },
    // type: 'text',
    // slots: { edit: 'jiesuanTotal_edit' },
  },
  {
    field: 'price',
    minWidth: 130,
    title: '结算综合单价',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanPrice_edit' },
  },
  {
    field: 'total',
    minWidth: 100,
    title: '结算合价',
    // editRender: { autofocus: '.vxe-input--inner' },
    // type: 'text',
    // slots: { edit: 'jiesuanAmount_edit' },
  },
  {
    field: 'taxRemoval',
    minWidth: 130,
    title: '结算除税系数(%)',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanTaxRemoval_edit' },
  },
  {
    field: 'jxTotal',
    minWidth: 130,
    title: '结算进项合计',
  },
  {
    field: 'csPrice',
    minWidth: 130,
    title: '结算除税单价',
  },
  {
    field: 'csTotal',
    minWidth: 130,
    title: '结算除税合价',
  },
];
const columnListOut = [
  {
    field: 'dispNo',
    title: '序号',
    minWidth: 60,
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'dispNo_edit' },
  },
  {
    field: 'worksName',
    title: '名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'worksName_edit' },
  },
  {
    field: 'specification',
    title: '规格型号',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'spec_edit' },
  },
  {
    field: 'unit',
    title: '单位',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'unit_edit' },
  },
  {
    field: 'quantitativeExpression',
    title: '结算数量表达式',
    minWidth: 180,
    type: 'text',
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'jiesuanQuant_edit' },
  },
  {
    field: 'jieSuanAmount',
    minWidth: 100,
    title: '结算数量',
    // editRender: { autofocus: '.vxe-input--inner' },
    // type: 'text',
    // slots: { edit: 'jiesuanTotal_edit' },
  },
  {
    field: 'tentativeQuantity',
    minWidth: 80,
    title: '暂定数量',
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'tentativeQuantity_edit' },
  },
  {
    field: 'price',
    minWidth: 130,
    title: '结算综合单价',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanPrice_edit' },
  },
  {
    field: 'total',
    minWidth: 100,
    title: '结算合价',
    // editRender: { autofocus: '.vxe-input--inner' },
    // type: 'text',
    // slots: { edit: 'jiesuanAmount_edit' },
  },
  {
    field: 'taxRemoval',
    minWidth: 130,
    title: '结算除税系数(%)',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jiesuanTaxRemoval_edit' },
  },
  {
    field: 'jxTotal',
    minWidth: 130,
    title: '结算进项合计',
  },
  {
    field: 'csPrice',
    minWidth: 130,
    title: '结算除税单价',
  },
  {
    field: 'csTotal',
    minWidth: 130,
    title: '结算除税合价',
  },
];
onActivated(() => {
  originalFlag.value = projectStore.currentTreeInfo.originalFlag;
});
</script>
<style lang="scss" scoped>
@import './otherProject.scss';
</style>
