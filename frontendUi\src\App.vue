<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-07 14:19:05
 * @LastEditors: wangru
 * @LastEditTime: 2025-06-27 14:38:37
-->
<template>
  <div id="pricing_body">
    <div
      v-if="hasPermission()"
      class="noPermission"
      @click="openWarn"
    ></div>
    <div
      v-if="loginStatus()"
      class="noLogin"
      @click="openLoginTip"
    ></div>
    <a-config-provider
      :locale="zhCn"
      :getPopupContainer="getPopupContainer"
    >
      <router-view />
    </a-config-provider>
  </div>
  <!-- 加密狗驱动检测结果异常 -->
  <common-modal
    className="dialog-comm dog-err-dialog"
    v-model:modelValue="showModal"
    title="提示"
    width="530"
    height="280"
    @close="closeWarn"
    :position="{ top: '32vh' }"
  >
    <!-- <div class="contentModal">
      <img
        src="../src/assets/img/compute.png"
        alt=""
        style="width: 115px"
      />
      <div style="font-size: 14px; width: 250px">
        <p>
          <icon-font
            class="icon"
            type="icon-querenshanchu"
            style="margin-right: 5px"
          />
          <span v-if="dogStatus === -1">加密狗驱动端未启动</span>
          <span v-if="dogStatus === 0">未监测到加密锁，请插入后重试</span>
          <span v-if="dogStatus === 2">加密锁：{{ dogSortNo }} —已过期</span>
          <span v-if="dogStatus === 98">加密锁：{{ dogSortNo }} —已挂失</span>
          <span v-if="dogStatus === 99"
            >加密锁：{{ dogSortNo }} —未绑定设备</span
          >
          <span v-if="dogStatus === 3"
            >加密锁：{{ dogSortNo }} —绑定设备不一致</span
          >
          <span v-if="dogStatus === ''">加密锁检测异常</span>
        </p>
        <div v-if="dogStatus > 1">
          <span>可联系客服进行续订：400-1234-1234</span><br />
          <span
            >或登录官网查看：<a href="https://www.yunsuanfang.com"
              >www.yunsuanfang.com</a
            ></span
          >
        </div>
      </div>
    </div> -->
    <div class="contentModal">
      <img
        src="../src/assets/img/compute.png"
        alt=""
        style="width: 140px"
      />
      <div style="font-size: 14px; width: 275px; display: flex; flex-wrap: wrap">
        <div class="content">
          <icon-font
            class="icon"
            type="icon-querenshanchu"
            style="margin-right: 8px; margin-top: 7px"
          />
          <div class="text">
            <span style="line-height: 2; color: #000">未检测到软件授权，无法进行使用</span>
            <div>解决方案：</div>
            <div>1、使用账号授权，请确认登录账号已进行云授权；</div>
            <div>2、使用单机锁授权，请点击下方按钮激活加密锁驱动</div>
          </div>
        </div>
        <div class="activate-btn">
          <a-button
            type="primary"
            @click="activeDog"
            :loading="activeDogLoading"
          >
            激活驱动
          </a-button>
        </div>
      </div>
    </div>
  </common-modal>
  <!-- 学习版账号没权限 -->
  <common-modal
    className="account-err-dialog"
    v-model:modelValue="showNoPermissionModal"
    width="500"
    height="280"
    @close="closeWarn"
    :position="{ top: '32vh' }"
    :showHeader="false"
  >
    <div style="width: 100%; height: 145px"></div>
    <div class="contentModal">
      <div style="
          font-size: 14px;
          width: 400px;
          display: flex;
          flex-wrap: wrap;
          height: 92px;
          flex-direction: column;
          justify-content: space-between;
        ">
        <div class="content">
          <div
            class="text"
            v-if="store.daysRemaining < 1"
          >
            <div>
              尊敬的
              {{
                store.loginUserInfo?.userInfo?.showName
              }}，您的试用已到期，请升级正式版解锁全部权益！
            </div>
          </div>
          <div
            class="text"
            v-if="store.daysRemaining == 1"
          >
            <div>
              尊敬的
              {{ store.loginUserInfo?.userInfo?.showName }}，您的试用仅剩<span style="color: #de4747">1天</span>，为了不影响您的业务，请升级正式版解锁全部权益！
            </div>
          </div>
        </div>
        <div class="activate-btn">
          <a-button
            type="primary"
            @click="closeWarn"
          > 知道了</a-button>
        </div>
      </div>
    </div>
  </common-modal>
  <!-- 加密狗驱动在软件运行过程中检测异常-----确定是否重新检测 -->
  <common-modal
    className="titleNoColor noHeader"
    v-model:modelValue="reChenkModal"
    title=" "
    width="400"
    height="230"
    @close="cancelCheck"
    :position="{ top: '32vh' }"
  >
    <div class="reCheck">
      <p style="font-weight: 600">
        <icon-font
          class="icon"
          type="icon-querenshanchu"
          style="margin-right: 5px"
        />加密锁检测异常
      </p>
      <p style="padding-left: 20px; margin-bottom: 26px">
        您可点击【重新检测】确定加密锁状态，或保存当前项目并退出
      </p>
      <a-button
        type="primary"
        @click="reCheck"
        style="margin: 0 30px 0 50px"
        :loading="reCheckLoading"
      >
        {{ reCheckLoading ? '...检测中' : '重新检测' }}</a-button>
      <a-button @click="cancelCheck">保存并关闭</a-button>
    </div>
  </common-modal>
  <!-- 登录 -->
  <a-modal
    dialogClass="modal"
    destroyOnClose
    :style="{ width: modalWidth }"
    :bodyStyle="{ padding: 0, width: '200px' }"
    centered
    :title="null"
    :footer="null"
    v-model:visible="visible"
    @cancel="closeLogin"
    :maskClosable="false"
  >
    <login-modal
      :visible="visible"
      @offlineLoginOprate="offlineLoginOprate"
      @loginSuccess="loginSuccess"
    />
  </a-modal>
  <!-- 增量更新 -->
  <common-modal
    className="titleNoColor noHeader "
    title=" "
    width="700"
    height="200"
    v-model:modelValue="dataUpdateModal"
    :mask="false"
  >
    <div class="progressDiv">
      <p>检测到系统更新，正在更新中，请稍后…</p>
      <p class="noClose">请勿关闭当前页面</p>
      <a-progress
        :percent="percent"
        :showInfo="true"
      />
    </div>
  </common-modal>
  <!-- 是否需要检测加密狗 -->
  <!-- <common-modal
    className="titleNoColor noHeader"
    v-model:modelValue="isCheckDog"
    title=" "
    width="350"
    height="150"
  >
    <p style="font-size: 16px; margin-top: -10px">
      <icon-font
        class="icon"
        type="icon-querenshanchu"
        style="margin-right: 5px"
      />是否需要检测加密狗状态？
    </p>

    <p style="float: right; margin: 15px 78px 0px 0px">
      <a-button style="margin: 0 30px 0 20px" @click="needDogFn(false)"
        >不检测
      </a-button>
      <a-button type="primary" @click="needDogFn(true)"> 检测</a-button>
    </p>
  </common-modal> -->
  <!-- <notice-beta
    v-model:noticeBetaModal="noticeBetaModal"
    @openDialog="openDialog"
    @cancel="betaCancel"
  ></notice-beta>  -->
  <!-- <common-modal
    className="titleNoColor betaHeader"
    v-model:modelValue="tipsVisible"
    title=" "
    width="580"
    height="300"
    :show-close="softWareInfo?.isUse && softWareInfo?.differenceDay > 0"
  >
    <div class="notice-tips">
      <div class="header">
        <img
          src="@/assets/img/tips.png"
          alt
          v-if="softWareInfo?.differenceDay > 0"
        />
        <img src="@/assets/img/tipsElse.png" alt v-else />
        <span
          class="title"
          v-if="softWareInfo?.isUse && softWareInfo?.differenceDay > 0"
          >温馨提示</span
        >
      </div>
      <div class="content" v-if="!softWareInfo?.isUse">
        平台公测已到期，为不影响您的项目正常操作使用， 请前往官网下载正式版。
        <div>
          下载链接：<a
            @click="openExternal('https://www.yunsuanfang.com/download')"
            >https://www.yunsuanfang.com/download</a
          >
        </div>
      </div>
      <div
        class="content-day"
        v-if="softWareInfo?.isUse && softWareInfo?.differenceDay > 0"
      >
        系统检测到您有<span>{{ softWareInfo?.differenceDay }}</span
        >天的免费使用权限，系统已为您自动延期。
      </div>
      <div class="footer" v-if="!softWareInfo?.isUse">
        <a-button type="primary" @click="closeNotice">知道了</a-button>
      </div>
    </div>
  </common-modal> -->
  <beta-recommend
    v-model:visible="recommendVisible"
    @cancel="recommendVisible = false"
  ></beta-recommend>
  <selfCheck v-model:visible="checkVisible"></selfCheck>
  <info-modal
    v-model:infoVisible="checkFileChange"
    :infoText="fileInfo"
    :isSureModal="true"
  ></info-modal>
</template>

<script setup>
import zhCn from 'ant-design-vue/es/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { proModelStore } from '@/store/proModel.js';
import {
  onBeforeMount,
  onMounted,
  watch,
  ref,
  getCurrentInstance,
  createVNode,
  onBeforeUnmount,
  nextTick,
} from 'vue';
import system from '@/api/system';
import feePro from '@/api/feePro.js';
import { Modal, message } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
const { ipcRenderer, BrowserWindow } = require('electron');
import csProject from './api/csProject';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail';
import { delay } from '@/utils/index';
import infoMode from '@/plugins/infoMode';
import { checkUserIsAvailable } from '@/api/auth';
const store = projectDetailStore();
const route = useRoute();
const proStore = proModelStore();
const globalProperties = getCurrentInstance().appContext.config.globalProperties; // 获取全局挂载
const $ipc = globalProperties.$ipc;
let showModal = ref(false); //定时器弹框
let showInfoText = ref(''); //弹框内容
let flag = ref(0); //是否是第一次检测数据
let dogStatus = ref(''); //加密锁状态
let dogSortNo = ref(null); //加密锁序列号
let reChenkModal = ref(false); //重新检测弹框
let reCheckIsClick = ref(false); //重新检测弹框点击重新检测按钮
let childList = ref([]);
let nowWinId = ref();
let isNeedCheck = ref(false); //是否需要检测加密狗
let mainWidMsg = ref(false); //是否获取过主窗口消息
let reCheckLoading = ref(false); //重新检测按钮loading展示
let dataUpdateModal = ref(false); //增量更新进度条
let percent = ref(0); // 进度
let loginTypes = ['offlineLogin', 'inlineLogin'];
import { ipcApiRoute, specialIpcRoute } from '@/api/main';
import loginModal from './views/csProject/header/login-modal.vue';
import NoticeBeta from '@/views/csProject/header/noticeBeta.vue';
import BetaRecommend from '@/views/csProject/header/betaRecommend.vue';
import { getEnterprise, checkNormalAgency, userInfo, registerInfo } from '@/api/auth';
let loginIn = ref(false);
let saveDog = ref(false); //保存加密狗状态
let isgetPro = ref(true);
let newProPath = ref(null);
let noticeBetaModal = ref(false);
let tipsVisible = ref(false);
let softWareInfo = ref(null);
let recommendVisible = ref(false); // 公测推荐弹框
let userDetail = ref(null); // 用户详细信息
const defaultValidLock = ref(null); //能检测到的第一把有效锁
let visible = ref(false); //登录弹框
const activeDogLoading = ref(false);
const showNoPermissionModal = ref(false);

const closeLogin = () => {
  // const obj = {
  //   loginType: 'offlineLogin',
  //   userList: [],
  // };
  // localStorage.setItem('loginType', 'offlineLogin');
  // store.SET_IS_USER_INFO_LIST(obj);
  // store.SET_IS_LOGIN_USER_INFO({ loginType: 'offlineLogin' }); //存储当前登录的用户信息
  store.SET_LOGIN_PHONE(null);
  localStorage.removeItem('token');
  //离线登录后更新最近打开项目列表
  // proStore.SET_Refresh(true);
  store.SET_LOGIN_VISIBLE(false); //离线登录以一种另外的身份登录
  flag.value = 0;
  // store.SET_OVERALL_PERMISSION(true);

  localStorage.removeItem('searchHistory'); // 清除人材机索引搜索历史
};
const closeWarn = () => {
  // showModal.value = false;
  showNoPermissionModal.value = false;
};
const closeAll = () => {
  localStorage.removeItem('searchHistory'); // 清除人材机索引搜索历史
  // logout().then(res => {
  localStorage.removeItem('loginType');
  defaultValidLock.value = null;
  ipcRenderer.send('window-close', true);
  system.closeAllChildWindow().then(res => {});
  // });
};
let noticeModalHasOpen = ref(false); //是否打开过公测公告
const { shell } = require('electron');
// 假设你有一个链接地址
// 使用 shell 模块的 openExternal 方法打开链接
let checkVisible = ref(false);

const openExternal = link => {
  shell.openExternal(link);
};

const closeNotice = () => {
  tipsVisible.value = false;
  if (!softWareInfo.value?.isUse) {
    closeAll();
  }
};

const openDialog = () => {
  recommendVisible.value = true;
};

const betaCancel = () => {
  noticeBetaModal.value = false;
};
watch(
  () => store.loginVisible,
  value => {
    console.log('store.loginVisible', value);
    if (value) {
      visible.value = true;
    } else {
      visible.value = false;
    }
  },
  {
    immediate: true,
  }
);
// watch(
//   () => store.accountHasValidResource,
//   value => {
//     if (value) {
//       showNoPermissionModal.value = false;
//     } else {
//       showNoPermissionModal.value = true;
//     }
//   },
//   {
//     immediate: true,
//   }
// );
// watch(
//   () => store.daysRemaining,
//   value => {
//     if(nowWinId.value !== 1) return;
//     console.log('剩余天数值的变化', value )
//     if (store.daysRemaining <= 1) {
//       showNoPermissionModal.value = true;
//     } else {
//       showNoPermissionModal.value = false;
//     }
//   },
//   {
//     immediate: true,
//   }
// );

watch(
  () => childList.value,
  val => {
    if (nowWinId.value !== 1) return;
    if (val?.length > 0) {
      val.forEach(item => {
        $ipc.sendTo(
          item,
          specialIpcRoute.window1ToWindow2,
          `daysRemaining,${JSON.stringify({
            value: store.daysRemaining,
          })}`
        );
      });
    }
  },
  {
    immediate: true,
  }
);

watch(
  () => store.checkVisible,
  value => {
    if (value) {
      checkVisible.value = true;
    } else {
      checkVisible.value = false;
    }
  }
);
watch(
  () => store.loginUserInfo,
  async (val, oldVal) => {
    // if (!oldVal && val) {
    //每次重新登录都要监听获取登录时限是否超时
    // setCheckLoginEffective();
    setTimeout(() => {
      proStore.SET_Refresh(!proStore.isRefresh); //登录信息更新重新获取最近项目列表
    }, 3000);
    // }
  }
);
dayjs.locale(zhCn.locale);
console.log('import.meta.env', import.meta.env);
function getPopupContainer() {
  return document.getElementById('pricing_body');
}
const modalWidth = ref('1300px'); //登录弹框宽
const getLastUser = async sequenceNbr => {
  await csProject
    .getLastIdInformation({
      sequenceNbr: sequenceNbr,
    })
    .then(res => {
      console.log('获取上次登录身份返回信息', res);
      if (res.status === 200 && res.result) {
        store.SET_LAST_INFO({ agencyCode: res.result });
      } else {
        store.SET_LAST_INFO({ agencyCode: null });
      }
    });
};
const getUserInfo = async tokens => {
  const token = tokens.token_type + ' ' + tokens.access_token;
  const expires = +(tokens.expires_in / 86400).toFixed(2);
  localStorage.setItem('token', token);
  const tokenIsEff = await checkToken();
  let userDetatil = {};
  if (tokenIsEff) {
    userDetatil = await userInfo(token); //设置的默认身份-获取到登录用户sequenceNbr
    console.log(userDetatil, '用户信息userDetatil');
    if (userDetatil.status === 200 && userDetatil.result?.mobile) {
      store.SET_LOGIN_PHONE(userDetatil.result?.mobile);
    }
    await getLastUser(userDetatil && userDetatil.result && userDetatil.result.sequenceNbr);
    let userInfoList = await getEnterprise(token); //获取所有身份列表
    getUserDetail(userInfoList.result, userDetatil);
  } else {
    message.error(isNormalAgency.message);
  }
};
const getUserDetail = async (data, user) => {
  data && delete data.delAgency;
  const keys = Object.keys(data);
  const values = Object.values(data);
  let infoList = [];
  for (let i = 0; i < keys.length; i++) {
    values[i] &&
      values[i].map(item => {
        let type = keys[i];
        item.type = type;
        item.sequenceNbr = user && user.result && user.result.sequenceNbr;
        item.openId = user && user.result && user.result.openId;
        if (type === 'normalAgency') {
          item.showName = item.userName;
          item.imgSrc =
            'https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/YYFUPT/Not%20certified.png';
        } else if (type === 'personalAgency') {
          item.showName = item.realName;
          item.imgSrc =
            'https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/YYFUPT/attestation.png';
        } else if (type === 'enterpriseAgency') {
          item.showName = item.enterpriseName;
          item.imgSrc =
            'https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/YYFUPT/attestation.png';
        }
        !item.logo ? (item.logo = item.imgSrc) : '';
      });
    infoList.push(...values[i]);
  }
  infoList = infoList.filter(item => Number(item.extend2) === 0);
  const obj = {
    userList: infoList,
    loginType: 'login',
  };
  store.SET_IS_USER_INFO_LIST(obj);
};
const checkToken = async () => {
  //检测用户token是否有效
  const isNormalAgency = await checkNormalAgency(); //登录后检查是否存在普通用户身份，不存在则添加普通身份
  return isNormalAgency.status === 200;
};
const checkAccountPermission = async () => {
  const availableRes = await checkUserIsAvailable();
  // console.log('availableRes', availableRes);
  if (availableRes.status == 200) {
    store.SET_DAYS_REMAINING(availableRes.result);
  }
};
const isCheckAutoLogin = async () => {
  console.log('********1自动登录');
  // if (localStorage.getItem('winId') == 1) {
  //   return;
  // }
  //判断是否可以自动登录
  console.log(route.query.constructSequenceNbr);
  let list = JSON.parse(localStorage.getItem('savedPasList'));
  if (list && list[0] && list[0].autoLogin) {
    //设置了自动登录--第一项设置了自动登录就不需要重启登陆框
    // 判断时间戳--是否超出限制时间
    let startTime = list[0].loginTime;
    let now = Date.parse(new Date()) / 1000;
    let diff = (now - startTime) * 1000; //时间差的毫秒数
    let hours = diff / (3600 * 1000); //相差小时数
    let min = diff / (60 * 1000); //分钟数
    console.log('isCheckAutoLogin', diff, hours, now, min);
    if (hours > 48) {
      //超出48小时清空用户登录信息---方便测试改成3分钟
      delLoginInfo();
    } else {
      const tokenIsEff = await checkToken();
      if (!tokenIsEff) {
        //token登录失效需要重新登录，清除登录信息
        delLoginInfo();
        return;
      }
      //token失效的话需要重新登录
      store.SET_IS_LOGIN_USER_INFO(JSON.parse(localStorage.getItem('autoLoginInfo')));
      localStorage.setItem('loginUserInfo', localStorage.getItem('autoLoginInfo')); //用来登录使用
      let userInfo = JSON.parse(localStorage.getItem('autoLoginInfo'));
      await csProject
        .getLastIdInformation({
          sequenceNbr: userInfo.userInfo.sequenceNbr,
        })
        .then(res => {
          console.log('获取上次登录身份返回信息Res', res);
          if (res.status === 200 && res.result) {
            store.SET_LAST_INFO({ agencyCode: res.result });
          } else {
            store.SET_LAST_INFO({ agencyCode: null });
          }
        });
      let obj = {
        sequenceNbr: userInfo.userInfo.sequenceNbr,
        identity: userInfo.userInfo.agencyCode,
        loginType: 'inlineLogin',
      };
      loginIn.value = true; //登录弹框是否弹过
      //定时器调用检查账号权限接口
      await checkAccountPermission();
      setInterval(() => {
        checkAccountPermission();
      }, 5000);
      //自动登录--获取账号权限
      // const accountPermissionRes = await accountPermission();
      // let hasValidResource;
      // if (accountPermissionRes.status == 200) {
      //   if (
      //     Array.isArray(accountPermissionRes.result) &&
      //     accountPermissionRes.result.length > 0
      //   ) {
      //     const currentTime = Date.now();
      //     // 资源未过期且是计价软甲
      //     hasValidResource = accountPermissionRes.result.some(item => {
      //       const isJJRJ = item.key?.find(k => k.type == 'YSF-JJRJ')
      //       if(isJJRJ) {
      //         if (!item.end_time) return true;
      //         const et = Number(item.end_time);
      //         return et > currentTime;
      //       }
      //     });
      //   } else {
      //     hasValidResource = false;
      //   }
      //   store.SET_ACCOUNT_HAS_VALID_RESOURCE(hasValidResource);
      //   // if (!hasValidResource) {
      //   //   store.SET_LOGIN_VISIBLE(false);
      //   // }
      // } else {
      //   // store.SET_LOGIN_VISIBLE(false);
      //   store.SET_ACCOUNT_HAS_VALID_RESOURCE(false);
      // }
      // // 锁中无有效资源，账号中也无有效资源
      // if(!hasValidResource && !store.lockHasValidResource) {
      //   store.SET_OVERALL_PERMISSION(false);
      // }
      //保存用户登录信息
      await csProject.saveIdInformation(JSON.stringify(obj)).then(res => {
        proStore.SET_Refresh(!proStore.isRefresh); //登录信息更新重新获取最近项目列表
      });
    }
  } else {
    delLoginInfo(true, false);
  }
};

const setCheckLoginEffective = async () => {
  //检测当前存储账号是否可以登录（超时+token有效判断）
  //设置了自动登录--第一项设置了自动登录就不需要重启登陆框
  // 判断时间戳--是否超出限制时间
  let timer = setInterval(async () => {
    let list = JSON.parse(localStorage.getItem('savedPasList')) || [];
    if (nowWinId.value === 1 && list?.length > 0 && !visible.value) {
      let startTime = list[0].loginTime;
      let now = Date.parse(new Date()) / 1000;
      let diff = (now - startTime) * 1000; //时间差的毫秒数
      let hours = diff / (3600 * 1000); //相差小时数
      const tokenIsEff = await checkToken();
      //超过时限或者token失效重新登录
      if (hours > 48 || !tokenIsEff) {
        delLoginInfo(false);
        store.SET_LOGIN_VISIBLE(true);
        clearInterval(timer);
      }
    }
  }, 60 * 5 * 1000); //初步设定5分钟一次
};
const delLoginInfo = (flag = true, isDelId = true) => {
  localStorage.removeItem('loginUserInfo'); //清除用户信息
  localStorage.removeItem('autoLoginInfo'); //清除用户信息
  isDelId ? localStorage.removeItem('constructSequenceNbr') : ''; //清除工作台存储信息
  localStorage.removeItem('token'); //清除用户信息
  // visible.value = true; //打开软件先打开登录
  if (flag) {
    loginIn.value = false;
    isNeedLogin.value = true;
  } else {
    store.SET_IS_LOGIN_USER_INFO(null);
  }
};
let isNeedLogin = ref(true); //默认需要弹出登录弹框
onMounted(async () => {
  window.addEventListener('resize', isMaxFun);
  //未登录状态下设置页面无法点击
  document.getElementById('pricing_body').className += 'notclick';
  //刚启动项目清空登录信息
  store.SET_IS_LOGIN_USER_INFO(null);
  $ipc.on('token', (event, arg) => {
    console.log('-----------------------********11111', arg);
    if (arg?.access_token) {
      store.SET_LOGIN_VISIBLE(false);
      isNeedLogin.value = false;
      loginIn.value = true; //登录弹框是否弹过
      getUserInfo(arg);
    } else {
      isNeedLogin.value = true;
    }
    $ipc.removeAllListeners('token'); //加密锁异常
  });
  isMaxFun();
  // userDetailInfo();
  getFileStatus();
  nextTick(() => {
    const globalloading = requestIdleCallback(() => {
      document.getElementById('body-loading').style.display = 'none';
    });
    clearTimeout(globalloading);
    setTimeout(() => {
      if (route.query.constructSequenceNbr) {
        //子窗口的登录信息根据loginUserInfo获取
        store.SET_IS_LOGIN_USER_INFO(JSON.parse(localStorage.getItem('loginUserInfo')));
        window.addEventListener('storage', function (e) {
          if (e.newValue && localStorage.getItem('loginUserInfo')) {
            store.SET_IS_LOGIN_USER_INFO(JSON.parse(localStorage.getItem('loginUserInfo')));
          } else {
            store.SET_IS_LOGIN_USER_INFO(null);
          }
        });
      }
      if (route.query.constructSequenceNbr && !isLogin()) {
        // 子窗口没有登录信息
        // 概算未做
        if (['yssh', 'ys', 'jiesuan']) getchildWinLogin();
      }
      checkDbVersion();

      // setTimeout(() => {
      //   if (nowWinId.value === 1) {
      //     noticeBetaModal.value = true;
      //   }
      // }, 2000);
    }, 2000);
  });
});
//打开软件检测是否需要自动登录
onBeforeMount(() => {
  isCheckAutoLogin();
});
const sendChildUserInfo = target => {
  //给子窗口传递用户登录信息
  if (nowWinId.value === 1) {
    $ipc.sendTo(
      Number(target),
      specialIpcRoute.window1ToWindow2,
      `loginUserInfo-${JSON.stringify(JSON.parse(JSON.stringify(store.loginUserInfo)))}`
    );
  }
};
// 概算未做
// const getchildWinLogin = async () => {
//   //获取当前子窗口id，向主窗口获取登录信息
//   let winId = await feePro
//     .getWinIdBySequenceNbr({
//       sequenceNbr: route.query.constructSequenceNbr,
//     })
//     .then();
//   console.log('winId.result', winId.result);
//   if (winId.result) {
//     $ipc.sendTo(
//       1,
//       specialIpcRoute.window2ToWindow1,
//       `getLoginInfo,${winId.result}`
//     );
//   }
// };
const getchildWinLogin = async () => {
  //获取当前子窗口id，向主窗口获取登录信息
  let winId = await feePro
    .getWinIdBySequenceNbr({
      sequenceNbr: route.query.constructSequenceNbr,
    })
    .then();
  console.log('winId.result', winId.result);
  if (winId.result) {
    $ipc.sendTo(1, specialIpcRoute.window2ToWindow1, `getLoginInfo,${winId.result}`);
    //子窗口获取登录信息是可以开始点击操作
    // removeClass();
  }
};
onBeforeUnmount(() => {
  window.removeEventListener('resize', isMaxFun);
});
//登录弹框收放
const isMaxFun = () => {
  setTimeout(() => {
    let { innerWidth, innerHeight } = window;
    modalWidth.value = innerWidth < 1366 ? '453px' : '1300px';
  }, 100);
};
const handleInput = e => {
  const { target } = e;
  const isInput = target;
  if (isInput) {
    const spellCheck = target.getAttribute('spellCheck');
    if (spellCheck || spellCheck === null) {
      target.setAttribute('spellCheck', 'false');
    }
  }
};

const cancelCheck = () => {
  //取消检测
  if (nowWinId.value !== 1) {
    saveAndClose();
  } else {
    if (childList.value.length > 0) {
      childList.value.forEach(item => {
        $ipc.sendTo(item, specialIpcRoute.window1ToWindow2, 'saveClose');
      });
    } else {
      closeAll();
    }
  }
};

//保存并关闭
const saveAndClose = () => {
  csProject.saveYsfFile(route.query.constructSequenceNbr).then(res => {
    message.success('保存成功');
    closeAll();
  });
};
const reCheck = () => {
  //重新检测
  reCheckIsClick.value = true;
  reCheckLoading.value = true;
};
const offlineLoginOprate = () => {
  flag.value = 0;
  store.SET_OVERALL_PERMISSION(true);
};
let index = ref(0);
const validDogInfo = arg => {
  //  const ranom = Math.random();
  // if(index.value < 20) {
  //   arg.data[0].info.resource = [];
  // }
  let hasValidResource = false;
  if (arg.status == 200) {
    const validLock = arg.data?.filter(item => item.status == 1);
    if (validLock.length > 0) {
      validLock.forEach(lock => {
        const currentTime = Date.now();
        const currentLockValidResource = lock.info?.resource.some(item => {
          const isJJRJ = item.key?.find(k => k.type == 'YSF-JJRJ');
          if (isJJRJ) {
            if (!item.end_time) return true;
            const et = Number(item.end_time);
            const st = Number(item.str_time);
            return currentTime >= st && currentTime <= et;
          } else {
            return false;
          }
        });
        if (currentLockValidResource) {
          hasValidResource = true;
          if (!defaultValidLock.value) {
            defaultValidLock.value = lock;
          }
          // offLineLoginHasResource.value = true;
        }
      });
    }
  }
  // index.value++;
  return hasValidResource;
};
const showDogWarn = () => {
  store.SET_OVERALL_PERMISSION(false);
  // localStorage.removeItem('loginUserInfo'); //清除用户信息
  // store.SET_IS_LOGIN_USER_INFO(null);
};
const getInfo = arg => {
  // 打开软件检测加密狗状态不成功弹框展示信息

  if (arg.status === 500) {
    dogStatus.value = -1; //驱动端没启动
  } else if (arg.status === 200 && arg.data.length === 0) {
    dogStatus.value = 0; //没有可用加密狗
  } else if (arg.status === 200 && arg.data.length > 0) {
    let badDog = arg.data && arg.data.filter(item => item.status !== 1); //状态不为1的加密狗
    dogStatus.value = badDog[0].status;
    dogSortNo.value = badDog[0].serial;
  }
  // 弹出加密锁异常弹框, 不应该总弹
  showModal.value = true;
  // $ipc.removeAllListeners('softdog.status'); //加密锁异常
};
const removeClass = () => {
  const target = document.getElementById('pricing_body');
  let classVal = target.getAttribute('class');
  classVal = classVal.replace('notclick', '');
  target.setAttribute('class', classVal);
};
const activeDog = () => {
  showModal.value = false;
  activeDogLoading.value = true;
  csProject.activeSoftdog().then(res => {
    activeDogLoading.value = false;
    console.log('激活加密狗返回值', res);
    if (res && res.status !== 200) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-querenshanchu',
        infoText: '未自动激活驱动，请进行安装或手动激活',
        confirm: () => {
          infoMode.hide();
        },
      });
    }
  });
  // system.closeAllChildWindow().then(res => {});
  // localStorage.removeItem('loginUserInfo'); //清除用户信息
  // localStorage.removeItem('loginType');
  // defaultValidLock.value = null;
  // store.SET_IS_LOGIN_USER_INFO(null);
  // store.SET_OVERALL_PERMISSION(true);
  // showModal.value = false;
  // noticeModalHasOpen.value = false;
  // if (!visible.value) store.SET_LOGIN_VISIBLE(true);
};
// let isCheckDog = ref(false);
const needDogFn = bol => {
  if (bol) {
    isNeedCheck.value = true;
    flag.value = 0;
  } else {
    isNeedCheck.value = false;
  }
  // isCheckDog.value = false;
  localStorage.setItem('isNeedCheck', isNeedCheck.value ? '1' : '0');
  removeClass();
};
const showCheck = () => {
  if (nowWinId.value === 1) {
    // isCheckDog.value = true;
    needDogFn(true);
    // localStorage.setItem('isNeedCheck', '0');
    // removeClass();
    // Modal.confirm({
    //   title: '是否需要检测加密狗状态',
    //   okText: '检测',
    //   cancelText: '不检测',
    //   onOk() {
    //     isNeedCheck.value = true;
    //     flag.value = 0;
    //   },
    //   onCancel() {
    //     isNeedCheck.value = false;
    //   },
    // });
    // localStorage.setItem('isNeedCheck', isNeedCheck.value ? '1' : '0');
  } else {
    //向主窗口发送获取是否需要检测的请求
    $ipc.sendTo(1, specialIpcRoute.window2ToWindow1, `isNeedDog,${nowWinId.value}`);
  }
  mainWidMsg.value = true;
};
const isLogin = () => {
  let loginUserInfo = JSON.parse(localStorage.getItem('loginUserInfo'));
  let hasLogin = loginTypes.includes(loginUserInfo?.loginType) ? true : false;
  return hasLogin;
};
const loginStatus = () => {
  if (nowWinId.value === 1) {
    return !store.loginUserInfo?.userInfo?.showName;
  } else {
    return false;
  }
};
const loginSuccess = () => {
  if (store.daysRemaining <= 1) {
    showNoPermissionModal.value = true;
  }
};
let dataAllCount = ref(0);
const checkDbVersion = () => {
  $ipc.on('dataupdate.status', (event, arg) => {
    console.log('checkDbVersion', arg);
    let { status, count } = arg;
    switch (status) {
      case -1: {
        softdog();
        break;
      }
      case 1: {
        //开始执行
        dataUpdateModal.value = true;
        dataAllCount.value = count;
        break;
      }
      case 2: {
        //执行中
        percent.value = (count / dataAllCount.value) * 100;
        break;
      }
      case 99: {
        percent.value = 100;
        dataUpdateModal.value = false;
        setTimeout(() => {
          softdog();
        }, 2000);
        break;
      }
      case 98: {
        //异常---使用旧版本
        dataUpdateModal.value = false;
        setTimeout(() => {
          softdog();
        }, 2000);
        break;
      }
    }
  });
  system.updaterData();
};

const hasPermission = () => {
  // if(localStorage.getItem('loginType') === 'offlineLogin') {
  //   // console.log('离线登录点击', 'store.lockHasValidResource', store.lockHasValidResource)
  //   // 离线登录，锁无资源，计价软件不可使用
  //   return !store.lockHasValidResource;
  // } else if(localStorage.getItem('loginType') === 'inlineLogin') {
  //   // console.log('------------账号登录资源', {'store.lockHasValidResource': store.lockHasValidResource,
  //   // 'store.accountHasValidResource': store.accountHasValidResource
  //   // })
  //   // 账号登录，锁无资源且账号无资源才不可使用
  //   return !store.lockHasValidResource && !store.accountHasValidResource;
  // }
  return store.daysRemaining < 1;
};

const softdog = () => {
  $ipc.on('softdog.status', (event, arg) => {
    // console.log('softdog.status', arg);
    // console.log('arg-----------------', arg, isLogin());
    arg.flag = validDogInfo(arg);
    store.SET_LOCK_HAS_VALID_RESOURCE(arg.flag);
    if (arg.flag && !saveDog.value) {
      saveDog.value = true;
      const params = {
        busName: defaultValidLock.value?.info?.main.busName,
        serial: defaultValidLock.value?.serial,
      };
      console.log('保存加密锁params', params);
      csProject.saveMicroDog(JSON.parse(JSON.stringify(params)));
    }

    childList.value = arg.list.filter(item => item !== 1); //
    let List = [1, ...childList.value];
    nowWinId.value = arg.winId;
    store.SET_WIN_ID(arg.winId);
    if (!localStorage.getItem('winId') && arg.winId === 1) {
      console.log('********,设置winId');
      localStorage.setItem('winId', arg.winId);
    }
    // console.log(
    //   'nowWinId.value',
    //   nowWinId.value,
    //   route.query.constructSequenceNbr
    // );
    //子窗口需要存储当前窗口winId-route.query.constructId---为了区分
    store.SET_IS_OPEN_WIN_LIST(arg.list);
    // console.log('layoutOrChange********11', store.hasOpenWinList);
    if (arg.list.length > List?.length && arg.winId === 1) {
      //ysf文件打开刷新最近项目列表--打开工作台展示头像
      proStore.SET_Refresh(!proStore.isRefresh);
    }

    //打开软件弹出登陆框---------为方便开发先注释
    let hasLogin = isLogin();
    if (
      !hasLogin &&
      !loginIn.value &&
      nowWinId.value === 1 &&
      isNeedLogin.value &&
      !visible.value &&
      !dataUpdateModal.value &&
      !store.isOpenUpdateModal
    ) {
      loginIn.value = true; //登录弹框是否弹过
      store.SET_LOGIN_VISIBLE(true);
      return;
    }
    if (isLogin() && nowWinId.value === 1 && !noticeModalHasOpen.value) {
      //公测公告打开
      console.log('公测公告打开111111');
      localStorage.removeItem('constructSequenceNbr'); //清除页面信息
      noticeModalHasOpen.value = true; //打开过公告，下次就不打开了
      noticeBetaModal.value = true; //不检测公测弹框-弹框弹公告平台
      // getSoftwareExpirationTime();
    }
    if (!isLogin() && nowWinId.value !== 1) {
      //非子窗口没有登录信息，就向主窗口获取
      // message.info('向主窗口获取登录信息' + nowWinId.value);
      $ipc.sendTo(1, specialIpcRoute.window2ToWindow1, `getLoginInfo,${nowWinId.value}`);
    }
    //下面两个if语句是为了加检测加密狗入口的
    if (flag.value === 0 && !mainWidMsg.value && !visible.value) {
      flag.value = -1;
      // if(localStorage.getItem('loginType') !== 'inlineLogin'){
      // showCheck(); //增加是否检测加密狗的入口
      // }
    }
    if (hasLogin && isgetPro.value && nowWinId.value === 1) {
      isgetPro.value = false;
      $ipc.invoke(ipcApiRoute.getLoginPath).then(response => {
        if (response.status === 200 && response.result) {
          $ipc.invoke(ipcApiRoute.openProject, { path: response.result }).then(response => {
            if (response.status === 200) {
              proStore.SET_Refresh(!proStore.isRefresh);
            }
          });
        }
      });
    }
    //主窗口已经点击不检测-跳出检测
    if (!isNeedCheck.value) {
      return;
    }

    // checkDogInfo(arg);
    flag.value++;
  });
};
const openOtherPro = () => {
  $ipc.on('login', (event, arg) => {
    if (arg) {
      console.log('监听的消息参数是---------------', arg);
      newProPath.value = arg;
    }
    $ipc.removeAllListeners('login'); //加密锁异常
  });
};
let loopWatchBate = ref(false); // 是否开始轮询监听公测状态

let timer = ref();
const getSoftwareExpirationTime = () => {
  system.getSoftwareExpirationTime().then(res => {
    console.log('公测接口返回', res);
    if (res) {
      noticeBetaModal.value = true;
    } else {
      tipsVisible.value = true;
      softWareInfo.value = {
        isUse: false,
      };
    }
  });
};
const getSoftwareIndate = () => {
  console.log('********111111', store.loginPhone);
  if (timer.value) {
    clearTimeout(timer.value);
    console.log('清除', timer.value);
  }
  system.getSoftwareIndate({ telNumber: store.loginPhone }).then(res => {
    console.log('getSoftwareIndate', res);
    if (res) {
      softWareInfo.value = res;
      if (res.differenceDay > 0 || !res.isUse) {
        if (loopWatchBate.value) {
          if (!res.isUse) {
            tipsVisible.value = true;
            childList.value?.forEach(item => {
              $ipc.sendTo(item, specialIpcRoute.window1ToWindow2, 'betaRecommend');
            });
          }
        } else {
          tipsVisible.value = true;
        }
      }
      timer.value = setTimeout(() => {
        console.log('5分钟执行一次公测时间检测', loopWatchBate.value, timer.value);
        if (!tipsVisible.value) {
          loopWatchBate.value = true;
          getSoftwareIndate();
        }
      }, 1000 * 60 * 5);
    }
  });
};
const checkDogInfo = arg => {
  // console.log('checkDogInfo', {
  //   'flag.value': flag.value,
  //   'arg.winId': arg.winId,
  //   'arg.flag': arg.flag,
  //   'showModal.value': showModal.value
  // })
  if (flag.value === 0 && arg.winId === 1 && !arg.flag && !showModal.value) {
    //主窗口初次检测加密狗 状态异常获取异常信息并弹框提示
    if (localStorage.getItem('loginType') !== 'inlineLogin') {
      // getInfo(arg);
      showDogWarn();
    }
  }
  // console.log('arg.winId', arg.winId, showModal.value, reChenkModal.value)
  // if (arg.winId === 1 && showModal.value && !reChenkModal.value) {
  //   //主窗口弹出加密锁异常提示时，所有打开子窗口也需要弹出不可进行别的操作
  //   childList.value.forEach(item => {
  //     $ipc.sendTo(item, specialIpcRoute.window1ToWindow2, 'dogError');
  //   });
  //   return;
  // }
  //   console.log('是否弹起重新检测',{
  //   'flag.value': flag.value,
  //   'arg.winId': arg.winId,
  //   'arg.flag': arg.flag,
  //   'loginType': localStorage.getItem('loginType'),
  //   'accountHasValidResource': store.accountHasValidResource
  // })
  // 离线登录只有锁中途没有资源就可以弹重新检查，在线登录需要锁没资源并且账号也内资源
  if (
    flag.value > 0 &&
    childList.value.length > 0 &&
    arg.winId === 1 &&
    !arg.flag &&
    !showModal.value &&
    !reChenkModal.value &&
    (localStorage.getItem('loginType') === 'offlineLogin' ||
      (localStorage.getItem('loginType') === 'inlineLogin' && !store.accountHasValidResource))
  ) {
    //主窗口弹出重新检测加密锁弹出
    reChenkModal.value = true;
  }
  if (arg.winId === 1 && reChenkModal.value) {
    //主窗口弹出重新检测加密锁弹出，所有打开子窗口也需要弹出重新检测
    childList.value.forEach(item => {
      $ipc.sendTo(item, specialIpcRoute.window1ToWindow2, 'openReCheck');
    });
  }
  // console.log('检测中',{
  //   'flag.value': flag.value,
  //   'reCheckIsClick.value': reCheckIsClick.value,
  //   'arg.flag': arg.flag,
  // })
  if (flag.value > 0 && reCheckIsClick.value) {
    if (!arg.flag) {
      reCheckIsClick.value = false;
      reCheckLoading.value = false;
      $ipc.sendTo(1, specialIpcRoute.window2ToWindow1, 'dogError');
    } else {
      //向主窗口发消息
      if (arg.winId !== 1) {
        $ipc.sendTo(1, specialIpcRoute.window2ToWindow1, 'checkDogTrue');
      } else {
        reChenkModal.value = false;
        childList.value.forEach(item => {
          $ipc.sendTo(item, specialIpcRoute.window1ToWindow2, 'checkDogTrue');
        });
      }
      reCheckIsClick.value = false;
      reCheckLoading.value = false;
    }
  }
};
$ipc.on('update.status', (event, arg) => {
  console.log('判断是否有未保存数据', event, arg);
});
let checkFileChange = ref(false);
let fileInfo = ref(null);
const getFileStatus = () => {
  $ipc.on('judgeSuffix', (event, arg) => {
    console.log('获取监听消息', event, arg);
    if (!route?.query?.constructSequenceNbr && !checkFileChange.value) {
      //主窗口监听
      fileInfo.value = arg;
      checkFileChange.value = true;

      console.log('judgeSuffix关闭');
    }
  });
};

//监听子窗口向主窗口发送检测过消息
$ipc.on(specialIpcRoute.window2ToWindow1, (event, arg) => {
  if (arg.indexOf('updateLogin') !== -1) {
    const arr = arg.split('updateLogin,');
    const res = arr[1];
    const target = JSON.parse(res)?.value;
    if (target?.access_token) {
      store.SET_LOGIN_VISIBLE(false);
      isNeedLogin.value = false;
      loginIn.value = true; //登录弹框是否弹过
      getUserInfo(target);
    }
  }
  if (arg === 'dogError' && !showModal.value) {
    showModal.value = true;
    reChenkModal.value = false;
    childList.value.forEach(item => {
      $ipc.sendTo(item, specialIpcRoute.window1ToWindow2, 'dogError');
    });
  }
  if (arg === 'checkDogTrue') {
    reChenkModal.value = false;
    childList.value.forEach(item => {
      console.log('子窗口Id', item);
      $ipc.sendTo(item, specialIpcRoute.window1ToWindow2, 'checkDogTrue');
    });
  }
  if (arg.indexOf('getLoginInfo') !== -1) {
    let target = arg.split(',');
    if (Number(target[1]) > 1) {
      // message.success(`主窗口收到getLoginInfo---------------${target[1]}`);
      sendChildUserInfo(target[1]);
    }
  }
  if (arg === 'SingOut') {
    //接收到子窗口的退出信息---删除登录信息重新登录
    noticeModalHasOpen.value = true; //--重新登录不需要打开公测弹框
    store.SET_IS_LOGIN_USER_INFO(null);
    store.SET_LOGIN_VISIBLE(true);
  }
  //获取到子窗口的询问检测加密狗信息，向子窗口发送是否需要检测
  if (arg.indexOf('isNeedDog') !== -1) {
    let info = arg.split(',');
    if (isNeedCheck.value) {
      $ipc.sendTo(Number(info[1]), specialIpcRoute.window1ToWindow2, 'needDog');
    } else {
      $ipc.sendTo(Number(info[1]), specialIpcRoute.window1ToWindow2, 'noNeedDog');
    }
  }
  //获取到子窗口的刷新最近列表信息，
  if (arg.indexOf('refreshAsideList') !== -1) {
    proStore.SET_Refresh(!proStore.isRefresh); //另存为更新重新获取最近项目列表
  }
});
//监听主窗口消息
$ipc.on(specialIpcRoute.window1ToWindow2, (event, arg) => {
  // console.log('specialIpcRoute.window1ToWindow2', arg)
  if (arg === 'checkDogTrue') {
    reChenkModal.value = false;
  }
  if (arg === 'saveClose') {
    saveAndClose();
  }
  //主窗口第一次检测异常，子窗口也需要
  if (arg === 'dogError') {
    showModal.value = true;
    reChenkModal.value = false;
  }
  //接收主窗口的是否检测加密狗的回答
  //主窗口消息-检测
  if (arg === 'needDog') {
    isNeedCheck.value = true;
    flag.value = 0;
  }
  //主窗口消息-不检测
  if (arg === 'noNeedDog') {
    isNeedCheck.value = false;
  }
  if (arg === 'openReCheck' && !reChenkModal.value) {
    reChenkModal.value = true;
    showModal.value = false;
  }
  if (arg === 'betaRecommend') {
    // 公测接受主窗口
    // if (!tipsVisible.value) {
    //   console.log('公测接受主窗口');
    //   getSoftwareIndate();
    // }
  }
  if (arg.indexOf('loginUserInfo') !== -1) {
    //获取主窗口传来的用户登录信息
    let hasLogin = loginTypes.includes(store.loginUserInfo?.loginType) ? true : false;
    if (nowWinId.value !== 1 && !hasLogin) {
      // message.success('子窗口获取到登录信息');
      arg = eval('(' + arg.substring(14) + ')');
      if (!localStorage.getItem('loginUserInfo')) {
        store.SET_IS_LOGIN_USER_INFO(arg);
        localStorage.setItem('loginUserInfo', JSON.stringify(store.loginUserInfo));
      }
    }
  }
});
$ipc.on('window-all-closed', (event, arg) => {
  //监听软件关闭---清除登录信息
  localStorage.clear();
});
$ipc.on('window-before-closed', () => {
  // 关闭之前处理事件
  localStorage.removeItem('winId');
  localStorage.removeItem('searchHistory'); // 清除人材机索引搜索历史
});
document.addEventListener('focus', handleInput, true);

const openLink = () => {
  window.open('https://www.yunsuanfang.com/home');
};

const openWarn = () => {
  // showModal.value = true;
  showNoPermissionModal.value = true;
};
const openLoginTip = () => {
  message.info('未登录，请先登录');
};
</script>

<style lang="scss">
.modal {
  :deep(.ant-modal) {
    zoom: 0.8;
  }
}
body {
  font-family: Source Han Sans CN;
}
.contentModal {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 5px 20px 0 0;
  div {
    p {
      // font-weight: 600;
    }
    div {
      // margin-left: 20px;
      width: 400px;
    }
  }
  .content {
    display: flex;
    .text {
      color: #333;
    }
  }
  .activate-btn {
    display: flex;
    // padding-top: 32px;
    justify-content: center;
  }
}
.footer {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}
.reCheck {
  width: 320px;
  margin: -15px 22px;
}
.ant-message div div:nth-of-type(2) {
  display: none !important;
}
.ant-message div div:nth-of-type(3) {
  display: none !important;
}
.ant-message div div:nth-of-type(4) {
  display: none !important;
}
.ant-message div div:nth-of-type(5) {
  display: none !important;
}
.ant-message div div:nth-of-type(6) {
  display: none !important;
}
.ant-message div div:nth-of-type(7) {
  display: none !important;
}
.ant-message div div:nth-of-type(8) {
  display: none !important;
}
.ant-message div div:nth-of-type(9) {
  display: none !important;
}
.ant-cascader-menu-item {
  padding: 3px 12px !important;
}
.progressDiv {
  width: 90%;
  margin: auto;
  font-size: 14px;
  color: #000000;
  .noClose {
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
    color: #ff9000;
  }
}

.notclick {
  //ointer-events: none;
}
.notice-tips {
  .header {
    img {
      width: 100%;
    }
    .title {
      display: block;
      font-size: 22px;
      color: #000000;
      text-align: center;
      margin-top: -70px;
    }
  }
  .content {
    margin-top: -120px;
    font-size: 14px;
    padding-left: 40px;
    font-weight: 600;
    color: #000;

    // span {
    //   color: #000000;
    // }
    // div {
    //   color: #000000;
    // }
  }
  .content-day {
    margin-top: 13px;
    font-size: 14px;
    color: #000;
    text-align: center;
    font-weight: 500;
    span {
      color: #3a82f5;
    }
  }
  .footer {
    margin-top: 34px;
    text-align: center;
  }
}
.noPermission {
  width: 100vw;
  height: 100vh;
  position: absolute;
  z-index: 9;
}
.noLogin {
  width: 100vw;
  height: 100vh;
  position: absolute;
  z-index: 10;
}
.account-err-dialog {
  .vxe-modal--body {
    background: url(@/assets/img/tipsElse.png) no-repeat center;
    background-size: cover;
  }
}
</style>
