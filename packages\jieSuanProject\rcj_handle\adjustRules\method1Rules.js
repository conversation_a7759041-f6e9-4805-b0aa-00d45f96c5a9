const {Organ, Gene} = require("@valuation/rules-engine");
const {JieSuanRcjStageUtils} = require("../../utils/JieSuanRcjStageUtils");
const {PricingFileFindUtils} = require("../../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../../electron/utils/ObjectUtils");
const {NumberUtil} = require("../../../../common/NumberUtil");
const JieSuanRcjDifferenceEnum = require("../../enum/JieSuanRcjDifferenceEnum");
const {baseRules, getjieSuanStageDifferenceQuantityRule, getjieSuanBasePrice, getjieSuanPrice, getjieSuanTaxRemoval,
	getSettlementPriceDifferencInputTax, getjieSuanTotalNumber, gettotalNumber, getjieSuanPriceMarketTotal,
	getjieSuanPriceBaseJournal, getjieSuanPriceBaseJournalTax, getpriceBaseJournal, getpriceBaseJournalTax,
	getpriceMarketTax, getpriceMarket, getmarketPrice, getjieSuanPriceSource, gettaxRemoval, getBasePriceFloatRate,
	getjieSuanTotal, getJieSuanPriceDifferencSum, gettotal, getjieSuanJxTotal, getjxTotal
} = require("./baseRules");

//造价信息价格与差额调整法


//单位价差
let getjieSuanPriceDifferenc=(seq)=>{
	return Organ.create({name:seq+"_jieSuanPriceDifferenc",description:"单位价差",gene:Gene.from([seq+"_DWJC_ZDF"],(ctx)=>{
			return ctx[seq+"_DWJC_ZDF"].jieSuanPriceDifferenc;
		})})
};
//涨跌幅以及单位价差计算
let getDWJCZDF=(seq)=>{
	return Organ.create({name:seq+"_DWJC_ZDF",description:"涨跌幅以及单位价差计算",gene:Gene.from(["CONTEXT",seq+"_marketPrice"],
			(ctx)=>{
				const result = {
					jieSuanPriceDifferenc:0,
					jieSuanPriceLimit:0
				};
				let {rcj,arg,obj} = ctx.CONTEXT;

				let tempJieSuanPrice = ctx[seq+"_marketPrice"];//结算单价
				let tempJieSuanMarketPrice = rcj.jieSuanMarketPrice;//合同价
				// if (arg.kind == 20){
				// //if (arg.kind == 20 || (ObjectUtils.isNotEmpty(rcj.isDifference) && rcj.isDifference)){
				// 	tempJieSuanPrice = rcj.jieSuanPrice;
				// 	tempJieSuanMarketPrice = rcj.marketPrice;
				// }
				if (!obj.originalFlag){
					tempJieSuanPrice = rcj.jieSuanPrice;
					tempJieSuanMarketPrice = rcj.marketPrice;
				}
				if ((ObjectUtils.isNotEmpty(arg.type) && arg.type ==2) && arg.levelType ==1){
					tempJieSuanPrice = rcj.jieSuanPrice;
					tempJieSuanMarketPrice = rcj.marketPrice;
				}
				let tempJieSuanBasePrice = rcj.jieSuanBasePrice;//基期价




				if (rcj.ifProvisionalEstimate == 1 || rcj.ifDonorMaterial ==1){
					//暂估价调差无调差方法及风险范围，其单位价差=结算单价-合同价
					result.jieSuanPriceDifferenc =NumberUtil.subtract(tempJieSuanPrice,rcj.jieSuanMarketPrice);
					return result;
				}
				if (rcj.jieSuanMarketPrice < rcj.jieSuanBasePrice) {
					if (NumberUtil.divide(NumberUtil.subtract(tempJieSuanPrice, tempJieSuanBasePrice), tempJieSuanBasePrice) > NumberUtil.divide(rcj.riskAmplitudeRangeMax,100)) {
						result.jieSuanPriceDifferenc = NumberUtil.subtract(tempJieSuanPrice, NumberUtil.multiply(tempJieSuanBasePrice, NumberUtil.add(1 , NumberUtil.divide(rcj.riskAmplitudeRangeMax,100))));
						//计算涨跌幅
						result.jieSuanPriceLimit=NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(tempJieSuanPrice, tempJieSuanBasePrice), tempJieSuanBasePrice),100);
					} else if (NumberUtil.divide(NumberUtil.subtract(tempJieSuanPrice, tempJieSuanMarketPrice), tempJieSuanMarketPrice) < NumberUtil.divide(rcj.riskAmplitudeRangeMin,100)) {
						result.jieSuanPriceDifferenc = NumberUtil.subtract(tempJieSuanPrice, NumberUtil.multiply(tempJieSuanMarketPrice, NumberUtil.add(1 , NumberUtil.divide(rcj.riskAmplitudeRangeMin,100))));
						result.jieSuanPriceLimit=NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(tempJieSuanPrice, tempJieSuanMarketPrice),tempJieSuanMarketPrice),100);
					}
				}
				if (tempJieSuanMarketPrice > tempJieSuanBasePrice) {
					if (NumberUtil.divide(NumberUtil.subtract(tempJieSuanPrice, tempJieSuanMarketPrice), tempJieSuanMarketPrice) > NumberUtil.divide(rcj.riskAmplitudeRangeMax)) {
						result.jieSuanPriceDifferenc = NumberUtil.subtract(tempJieSuanPrice, NumberUtil.multiply(tempJieSuanMarketPrice, NumberUtil.add(1 , NumberUtil.divide(rcj.riskAmplitudeRangeMax,100))));
						result.jieSuanPriceLimit=NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(tempJieSuanPrice, tempJieSuanMarketPrice), tempJieSuanMarketPrice),100);

					} else if (NumberUtil.divide(NumberUtil.subtract(tempJieSuanPrice, tempJieSuanBasePrice), tempJieSuanBasePrice) < NumberUtil.divide(rcj.riskAmplitudeRangeMin,100)) {
						result.jieSuanPriceDifferenc = NumberUtil.subtract(tempJieSuanPrice, NumberUtil.multiply(tempJieSuanBasePrice, NumberUtil.add(1 , NumberUtil.divide(rcj.riskAmplitudeRangeMin,100))));
						result.jieSuanPriceLimit=NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(tempJieSuanPrice, tempJieSuanBasePrice), tempJieSuanBasePrice),100);

					}
				}
				if (tempJieSuanMarketPrice == tempJieSuanBasePrice) {
					if (NumberUtil.divide(NumberUtil.subtract(tempJieSuanPrice, tempJieSuanBasePrice), tempJieSuanBasePrice) > NumberUtil.divide(rcj.riskAmplitudeRangeMax,100)) {
						result.jieSuanPriceDifferenc = NumberUtil.subtract(tempJieSuanPrice, NumberUtil.multiply(tempJieSuanBasePrice, NumberUtil.add(1 , NumberUtil.divide(rcj.riskAmplitudeRangeMax,100))));
						result.jieSuanPriceLimit=NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(tempJieSuanPrice,tempJieSuanBasePrice),tempJieSuanBasePrice),100);

					} else if (NumberUtil.divide(NumberUtil.subtract(tempJieSuanPrice, tempJieSuanBasePrice), tempJieSuanBasePrice) <rcj.riskAmplitudeRangeMin) {
						result.jieSuanPriceDifferenc = NumberUtil.subtract(tempJieSuanPrice, NumberUtil.multiply(tempJieSuanBasePrice, NumberUtil.add(1 , NumberUtil.divide(rcj.riskAmplitudeRangeMin,100))));
						result.jieSuanPriceLimit=NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(tempJieSuanPrice, tempJieSuanBasePrice), tempJieSuanBasePrice),100);
					}
				}
				return result;
			})});



};
//涨跌幅
let getjieSuanPriceLimit=(seq)=>{
	return Organ.create({name:seq+"_jieSuanPriceLimit",description:"涨跌幅",gene:Gene.from([seq+"_DWJC_ZDF"],
			(cxt)=>{
				return cxt[seq+"_DWJC_ZDF"].jieSuanPriceLimit;
			})})
}




let  calculateRules = {
	jieSuanPriceDifferencSum:getJieSuanPriceDifferencSum,
	jieSuanStageDifferenceQuantity:getjieSuanStageDifferenceQuantityRule,
	jieSuanPriceDifferenc:getjieSuanPriceDifferenc,
	jieSuanPriceLimit:getjieSuanPriceLimit,
	DWJC_ZDF:getDWJCZDF,
	settlementPriceDifferencInputTax:getSettlementPriceDifferencInputTax,
	basePriceFloatRate:getBasePriceFloatRate,
	jieSuanBasePrice:getjieSuanBasePrice,
	marketPrice:getmarketPrice,
	jieSuanPriceSource:getjieSuanPriceSource,
	//jieSuanTaxRemoval:getjieSuanTaxRemoval,
	jieSuanJxTotal:getjieSuanJxTotal,
	jxTotal:getjxTotal,
	total:gettotal,
	jieSuanTotalNumber:getjieSuanTotalNumber,
	totalNumber:gettotalNumber,
	jieSuanPriceMarketTotal:getjieSuanPriceMarketTotal,
	// priceBaseJournal:getpriceBaseJournal,
	// priceBaseJournalTax:getpriceBaseJournalTax,
	priceMarketTax:getpriceMarketTax,
	priceMarket:getpriceMarket,
	taxRemoval:gettaxRemoval,
	jieSuanTotal:getjieSuanTotal


};

//不分期字段
let method1RulesField_12 = {
	"jieSuanStageDifferenceQuantity":4,//调差工程量
	"jieSuanBasePrice":2,//基期价
	"basePriceFloatRate":2,// 基期价浮动率(%)
	"marketPrice":2,//结算单价
	"jieSuanPriceSource":2,//结算单价来源
	"jieSuanPriceLimit":2,// 单价涨/跌幅(%)
	"jieSuanPriceDifferenc":2,//单位价差
	"jieSuanPriceDifferencSum":2,	//价差合计
	"taxRemoval":2,//结算除税系数(%)
	"settlementPriceDifferencInputTax":2,//结算价差进项税额
	"jieSuanJxTotal":2,//结算进项税额
	"jxTotal":2,//合同进项税额
	"total":2,//结算市场价合价
	"jieSuanTotalNumber":4,//合同数量
	"totalNumber":4,//结算数量
	"jieSuanPriceMarketTotal":2,//合同不含税市场价合价(一般计税)/合同不含税市场价合价(一般计税)
	"priceMarketTax":2,//第n期含税单价
	"priceMarket":2,//第n期不含税单价
	"jieSuanTotal":2,//合同合价
}


/**
 * 根据类型获取到具体的列表数据
 */
let getMethod1Rule=(ctx)=>{
	let {arg} = ctx;

	let rulesField = method1RulesField_12;
	//分期量查看
	if (arg.onlyStages){
		rulesField = {
			"jieSuanStageDifferenceQuantity":4,//调差工程量
			"jieSuanTotalNumber":4,//合同数量
			"totalNumber":4,//结算数量
		}
	}
	ctx.calculateField = rulesField;
	ctx.rules = {...baseRules};
	ctx.calculateRules = calculateRules;


}


module.exports = {getMethod1Rule};
