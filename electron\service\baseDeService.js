'use strict';

const { Service,Log } = require('../../core');
const {BaseDe, BaseDe2022 } = require("../model/BaseDe");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {SqlUtils} = require("../utils/SqlUtils");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {BaseDeCslbRelation} = require("../model/BaseDeCslbRelation");
const {In} = require("typeorm");
const {NumberUtil} = require("../utils/NumberUtil");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const { CommandUtils } = require('typeorm/commands/CommandUtils');
const LibraryCodeConstant = require('../enum/LibraryCodeConstant');
const ConstantUtil = require('../enum/ConstantUtil');

/**
 * 国标定额service
 */
class BaseDeService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    baseDeDao = this.app.appDataSource.manager.getRepository(BaseDe);
    baseDeLibraryService = this.service.baseDeLibraryService;
    baseDe2022Service = this.service.baseDe2022Service;



    async selectDeByLibraryCode(libraryCode,list) {
        return await this.baseDeDao.findBy({libraryCode: libraryCode, deCode: In(list)});
    }

    async queryDeByRcj(libraryCode, rcjCode) {
        let selectSql = "SELECT\n" +
            "\tde.de_code AS deCode,\n" +
            "\tde.de_name AS deName,\n" +
            "\tde.unit AS unit,\n" +
            "\tde.price AS price, \n" +
            "\tde.sequence_nbr AS sequenceNbr, \n" +
            "\tde.library_code AS libraryCode \n" +
            "FROM\n" +
            "\tbase_de de,\n" +
            "\tbase_de_rcj_relation drr,\n" +
            "\tbase_rcj rcj \n" +
            "WHERE\n" +
            "\tde.sequence_nbr = drr.quota_id \n" +
            "\tAND drr.rcj_id = rcj.sequence_nbr \n" +
            "\tAND rcj.library_code = ? \n" +
            "\tAND rcj.material_code = ?\n" +
            "ORDER BY\n" +
            "\tde.sort_no";

        let deArrays = this.app.betterSqlite3DataSource.prepare(selectSql).all(libraryCode, rcjCode);

        return deArrays;
    }


    /**
     * 获取定额分类目录树
     * @param libraryCode 清单册code
     * @returns {Promise<*[]>}
     */
    async listTreeByLibraryCode(libraryCode) {
        // 定额arrays
        // let deArrays = (await this.baseDeDao.find({
        //     where: {libraryCode: libraryCode},
        //     order: {sortNo: "ASC"}
        // }));
        // let selectSql = "SELECT\n" +
        //     "\tsequence_nbr as sequenceNbr,\n" +
        //     "\tlibrary_code as libraryCode,\n" +
        //     "\tlibrary_name as libraryName,\n" +
        //     "\tqf_code as qf_code,\n" +
        //     "\tde_name as deName,\n" +
        //     "\tde_code as deCode,\n" +
        //     "\tclassify_level1 as classifyLevel1,\n" +
        //     "\tclassify_level2 as classifyLevel2,\n" +
        //     "\tclassify_level3 as classifyLevel3,\n" +
        //     "\tclassify_level4 as classifyLevel4\n" +
        //     "FROM\n" +
        //     "\t\"base_de\" \n" +
        //     "WHERE\n" +
        //     "\tlibrary_code = ? \n" +
        //     "ORDER BY\n" +
        //     "\tsort_no ASC";
        let selectSql = "SELECT\n" +
            "\tsequence_nbr as sequenceNbr,\n" +
            "\tlibrary_code as libraryCode,\n" +
            "\tlibrary_name as libraryName,\n" +
            "\tclassify_level1 as classifyLevel1,\n" +
            "\tclassify_level2 as classifyLevel2,\n" +
            "\tclassify_level3 as classifyLevel3,\n" +
            "\tclassify_level4 as classifyLevel4\n" +
            "FROM\n" +
            "\tbase_de_chapter\n" +
            "WHERE\n" +
            "\tlibrary_code = ?\n" +
            "ORDER BY\n" +
            "\tsort_no ASC";
        let deArrays = this.app.betterSqlite3DataSource.prepare(selectSql).all(libraryCode);
        // 去重取 classify_level1/classify_level2/classify_level3/classify_level4的list
        let level1AllList = this.distinctList(deArrays, "classifyLevel1");
        let level2AllList = this.distinctList(deArrays, "classifyLevel1","classifyLevel2");
        let level3AllList = this.distinctList(deArrays, "classifyLevel1","classifyLevel2","classifyLevel3");
        let level4AllList = this.distinctList(deArrays, "classifyLevel1","classifyLevel2","classifyLevel3","classifyLevel4");

        let sort = (ll1, ll2)=>ll1.sortNo - ll2.sortNo;
        level1AllList = level1AllList.sort(sort);
        // 给前端作为唯一标识
        let sequenceNbr = 1;
        // level1
        level1AllList.forEach(level1Item => {
            // 给前端统一code和name以及唯一标识
            this.modifyingAttributeValue(level1Item, 1, sequenceNbr++);
            let leve2SubList = level2AllList.filter(level2AllItem => level2AllItem.classifyLevel1 === level1Item.classifyLevel1);
            level1Item.childrenList = leve2SubList;
            // level2
            leve2SubList.forEach(level2Item => {
                // 给前端统一code和name以及唯一标识
                this.modifyingAttributeValue(level2Item, 2, sequenceNbr++);
                let leve3SubList = level3AllList.filter(level3AllItem => (level3AllItem.classifyLevel2 === level2Item.classifyLevel2 && level3AllItem.classifyLevel1 === level2Item.classifyLevel1));
                level2Item.childrenList = leve3SubList;
                // level3
                leve3SubList.forEach(level3Item => {
                    // 给前端统一code和name以及唯一标识
                    this.modifyingAttributeValue(level3Item, 3, sequenceNbr++);
                    // level4
                    let level4SubList = level4AllList.filter(level4AllItem => (level4AllItem.classifyLevel3 === level3Item.classifyLevel3 && level4AllItem.classifyLevel2 === level3Item.classifyLevel2 && level4AllItem.classifyLevel1 === level3Item.classifyLevel1));
                    level3Item.childrenList = level4SubList;
                    level4SubList.forEach(level4Item => {
                        // 给前端统一code和name以及唯一标识
                        this.modifyingAttributeValue(level4Item, 4, sequenceNbr++);
                    })
                });
            });
        });

        //return level1AllList;

        // 将顶层树节点改为定额册，与BS保持一致
        // 根据libraryCode查定额册名字
        let baseDeLibraryModel = await this.baseDeLibraryService.getByLibraryCode(libraryCode);
        let topLibraryNode = {sequenceNbr: sequenceNbr++, name: baseDeLibraryModel.libraryName};
        topLibraryNode.childrenList = level1AllList;
        return topLibraryNode;
    }

    /**
     * 修改属性值
     * @param baseDe 定额
     * @param level 清单专业level
     * @param sequenceNbr 重置后的sequenceNbr
     */
    modifyingAttributeValue(baseDe, level, sequenceNbr) {
        /*
         * 1.赋值name
         * 2.置空level下有误的属性值
         * 3.重置sequenceNbr
         */
        baseDe.sequenceNbr = sequenceNbr;
        switch (level) {
            case 1:
                // level1
                baseDe.name = baseDe.classifyLevel1;
                baseDe.classifyLevel2 = null;
                baseDe.classifyLevel3 = null;
                baseDe.classifyLevel4 = null;
                break;
            case 2:
                // level2
                baseDe.name = baseDe.classifyLevel2;
                baseDe.classifyLevel3 = null;
                baseDe.classifyLevel4 = null;
                break;
            case 3:
                // level3
                baseDe.name = baseDe.classifyLevel3;
                baseDe.classifyLevel4 = null;
                break;
            case 4:
                // level4
                baseDe.name = baseDe.classifyLevel4;
                break;
            default:
                // ...
                break;
        }
    }

    /**
     * list去重
     * @param list 要分组的list
     * @param distinctColumn 分组字段名str
     * @returns {*[]} 新的list
     */
    distinctList(list, ...distinctColumn) {
        if (!Array.isArray(list)) {
            return [];
        }
        let groupArray = [];
        list.forEach(item => {
            // 是否为空
            for (let i = 0 ; i < distinctColumn.length ; ++i) {
                if (ObjectUtils.isEmpty(item[distinctColumn[i]])) {
                    return;
                }
            }
            // 是否存在
            let isExist = groupArray.some(g => {
                let exist = true;
                for (let i = 0 ; i < distinctColumn.length ; ++i) {
                    if (g[distinctColumn[i]] !== item[distinctColumn[i]]) {
                        exist = false;
                        break;
                    }
                }

                return exist;
            });
            // 不存在add
            if (!isExist) {
                groupArray.push(Object.assign({}, item));
            }
        })
        return groupArray;
    }

    /**
     * 模糊查定额
     * @param qdDeParam 定额编码、名称、分类名
     * @see QdDeParam
     * @returns {Promise<ObjectLiteral[]>}
     */
    async queryDeByBdCodeAndName(qdDeParam) {
        // 选中定额册下的数据
        if(ObjectUtils.isEmpty(qdDeParam.libraryCode)){
            throw new Error("定额册编码不可为空");
        }

        let paramsTmp = qdDeParam;

        //对应定额索引输入查询
        if(ObjectUtils.isNotEmpty(qdDeParam.bdName)){
            paramsTmp = {libraryCode: qdDeParam.libraryCode, bdName: qdDeParam.bdName}
        }

        let data = await this.selectDesWhenSelect(paramsTmp);
        if (ObjectUtils.isNotEmpty(data)) {
            for (const de of data) {
                de.price = NumberUtil.numberScale2(de.price);
            }
        }
        let length = data.length;
        const startIndex = parseInt((qdDeParam.page-1)*qdDeParam.limit);
        const endIndex = startIndex + qdDeParam.limit;
        data = data.slice( startIndex,endIndex);
        return {"total":length,"data":data};

    }

    async queryDeByBdCodeAndName_bak(qdDeParam) {
        // 选中定额册下的数据情况下，直接返回
        if (((qdDeParam.classifyLevel1 && qdDeParam.classifyLevel1 !== "" ) ||
            (qdDeParam.classifyLevel2 && qdDeParam.classifyLevel2 !== "") ||
            (qdDeParam.classifyLevel3 && qdDeParam.classifyLevel3 !== "") ||
            (qdDeParam.classifyLevel4 && qdDeParam.classifyLevel4 !== "") ||
            (qdDeParam.libraryCode && qdDeParam.libraryCode !== "")) && (!qdDeParam.bdName || qdDeParam.bdName === "") ){
            let data = await this.selectDesWhenSelect(qdDeParam);
            let length = data.length;
            const startIndex = parseInt((qdDeParam.page-1)*qdDeParam.limit);
            const endIndex = startIndex + qdDeParam.limit;
            data = data.slice( startIndex,endIndex);
            return {"total":length,"data":data};
        }
        // let selectSql = "select *\n" +
        //     "from (select *\n" +
        //     "      from base_de\n" +
        //     "      where library_code = ?\n" +
        //     "        and (de_name LIKE '%' || ? || '%' OR de_code LIKE '%' || ? || '%')\n" +
        //     "      order by de_code, sort_no asc)\n" +
        //     "union all\n" +
        //     "select *\n" +
        //     "from (select *\n" +
        //     "      from base_de\n" +
        //     "      where library_code != ?\n" +
        //     "        and (de_name LIKE '%' || ? || '%' OR de_code LIKE '%' || ? || '%')\n" +
        //     "      order by de_code, sort_no asc)";
        // let sqlRes = this.app.betterSqlite3DataSource.prepare(selectSql).all(qdDeParam.libraryCode, qdDeParam.bdName, qdDeParam.bdName,
        //     qdDeParam.libraryCode, qdDeParam.bdName, qdDeParam.bdName);
        let selectSqlCount = "select COUNT(1) as total from ( select d.de_name= ? as nameRes, d.de_code= ? as codeRes, d.library_code= ? as aa, d.*\n" +
            "      from base_de d\n" +
            "      where (de_name LIKE '%' || ? || '%' OR de_code LIKE '%' || ? || '%')\n" +
            "      order by nameRes desc, codeRes desc, aa desc, de_code, sort_no asc )";

        let selectSql = "select d.de_name= ? as nameRes, d.de_code= ? as codeRes, d.library_code= ? as aa, d.*\n" +
            "      from base_de d\n" +
            "      where (de_name LIKE '%' || ? || '%' OR de_code LIKE '%' || ? || '%')\n" +
            "      order by nameRes desc, codeRes desc, aa desc, de_code, sort_no asc LIMIT ? ,?";
        //查看编码的like里 是否有主定额册的  现在是只要没有精准  就返回两个字段like的 数据  而是否主定额册则是乱的

        //查询当前单位工程的主定额册对应的 libraryCode
        let unit = PricingFileFindUtils.getUnit(qdDeParam.constructId,qdDeParam.spId,qdDeParam.upId);
        let sqlResCount = this.app.betterSqlite3DataSource.prepare(selectSqlCount).all(qdDeParam.bdName,qdDeParam.bdName,unit.mainDeLibrary,
            qdDeParam.bdName,qdDeParam.bdName);
        let sqlRes = this.app.betterSqlite3DataSource.prepare(selectSql).all(qdDeParam.bdName,qdDeParam.bdName,unit.mainDeLibrary,
            qdDeParam.bdName,qdDeParam.bdName,
            parseInt((qdDeParam.page-1)*qdDeParam.limit),
            qdDeParam.limit);
        let convertRes = SqlUtils.convertToModel(sqlRes);
        return {"total":sqlResCount[0].total,"data":convertRes};
    }

    async selectDesWhenSelect(qdDeParam) {
        // 判断入参是否为空，不为空拼接sql
        let whereSql = "";
        let whereParams = {};
        // 清单册
        if (qdDeParam.libraryCode) {
            // 拼接and
            if (whereSql.length !== 0) whereSql += " and ";
            // 拼接sql和参数
            whereSql += "baseDe.libraryCode = :libraryCode"
            whereParams.libraryCode = qdDeParam.libraryCode;
        }
        // 清单名称
        if (qdDeParam.bdName) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "(baseDe.de_code like :bdName OR baseDe.de_name like :bdName)"
            whereParams.bdName = "%" + qdDeParam.bdName + "%";
        }
        // 一级分类
        if (qdDeParam.classifyLevel1) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe.classifyLevel1 = :classifyLevel1"
            whereParams.classifyLevel1 = qdDeParam.classifyLevel1;
        }
        // 二级分类
        if (qdDeParam.classifyLevel2) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe.classifyLevel2 = :classifyLevel2"
            whereParams.classifyLevel2 = qdDeParam.classifyLevel2;
        }
        // 三级分类
        if (qdDeParam.classifyLevel3) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe.classifyLevel3 = :classifyLevel3"
            whereParams.classifyLevel3 = qdDeParam.classifyLevel3;
        }
        // 四级分类
        if (qdDeParam.classifyLevel4) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe.classifyLevel4 = :classifyLevel4"
            whereParams.classifyLevel4 = qdDeParam.classifyLevel4;
        }
        // 五级分类
        if (qdDeParam.classifyLevel5) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe2022.classifyLevel5 = :classifyLevel5"
            whereParams.classifyLevel5 = qdDeParam.classifyLevel5;
        }
        // 六级分类
        if (qdDeParam.classifyLevel6) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe2022.classifyLevel6 = :classifyLevel6"
            whereParams.classifyLevel6 = qdDeParam.classifyLevel6;
        }
        // 七级分类
        if (qdDeParam.classifyLevel7) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe2022.classifyLevel7 = :classifyLevel7"
            whereParams.classifyLevel7 = qdDeParam.classifyLevel7;
        }
        if (!whereSql) {
            console.log("error,参数为空");
        }

        let baseDes = await this.baseDeDao
            .createQueryBuilder("baseDe")
            .where(whereSql, whereParams)
            .orderBy("baseDe.sortNo", "ASC")
            .getMany();
        return baseDes;
    }

    /**
     * 定位定额
     * @param params
     * @returns {Promise<BaseDe|BaseDe2022>}
     */
    async queryDeById(params) {
        // base定额id，定额册code
        let {standardId, libraryCode,constructId, singleId, unitId} = params;
        let is22de=PricingFileFindUtils.is22UnitById(constructId,singleId,unitId);
        if (is22de) {
            // 22定额标准
            return await this.baseDe2022Service.queryDeById(standardId);
        }
        // 原12定额标准
        return await this.baseDeDao.findOneBy({sequenceNbr: standardId});
    }

    /**
     * selectOne
     * @param sequenceNbr sequenceNbr
     * @return {Promise<BaseDe>}
     */
    async selectOne(sequenceNbr) {
        return await this.baseDeDao.findOneBy({sequenceNbr: sequenceNbr});
    }


    /**
     * 查询河北省装饰装修工程消耗量定额（2012）下挂的定额
     * @param sequenceNbr sequenceNbr
     * @return {Promise<BaseDe>}
     */
    async selectDecorationDe(constructId, singleId, unitId) {
      let is22De = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
      let deList = await this.app.appDataSource.getRepository(BaseDe).find({
        where: { libraryCode: LibraryCodeConstant.ZSZXDEY_2012 }
      });
      let array12 = ['B.8垂直运输及超高增加费', 'B.9其它可竞技措施项', '装饰工程绿建补充'];
      if (is22De) {
        if (ObjectUtils.isEmpty(deList)) {
          deList = [];
        }

        deList = deList.concat(await this.app.appDataSource.getRepository(BaseDe2022).find({
          where: { libraryCode: LibraryCodeConstant.ZSZXDEY_2022 }
        }));
            //to do 22章節範圍
        let array22 = ['第八章 垂直运输工程及超高措施', '装饰装修工程其它措施项目'];///** 22的过滤章节**/
        deList = deList.filter(k => !array22.includes(k.classifyLevel2));
      }

      return deList.filter(k => !array12.includes(k.classifyLevel1));
    }

    /**
     * 模糊查询定额（快速组价用）
     * @param args
     */
    selectDeByBdName(args){
        let selectSqlCount = "select COUNT(1) as total from ( select d.de_name= ? as nameRes, d.de_code= ? as codeRes, d.library_code= ? as aa, d.*\n" +
            "      from base_de d\n" +
            "      where (de_name LIKE '%' || ? || '%' OR de_code LIKE '%' || ? || '%')\n" +
            "      order by nameRes desc, codeRes desc, aa desc, de_code, sort_no asc )";

        let selectSql = "select d.de_name= ? as nameRes, d.de_code= ? as codeRes, d.library_code= ? as aa, d.sequence_nbr as quota_id , d.*\n" +
            "      from base_de d\n" +
            "      where (de_name LIKE '%' || ? || '%' OR de_code LIKE '%' || ? || '%')\n" +
            "      order by nameRes desc, codeRes desc, aa desc, de_code, sort_no asc LIMIT ? ,?";
        //查看编码的like里 是否有主定额册的  现在是只要没有精准  就返回两个字段like的 数据  而是否主定额册则是乱的

        //查询当前单位工程的主定额册对应的 libraryCode
        let unit = PricingFileFindUtils.getUnit(args.constructId,args.spId,args.upId);
        let sqlResCount = this.app.betterSqlite3DataSource.prepare(selectSqlCount).all(args.bdName,args.bdName,unit.mainDeLibrary,
            args.bdName,args.bdName);
        let sqlRes = this.app.betterSqlite3DataSource.prepare(selectSql).all(args.bdName,args.bdName,unit.mainDeLibrary,
            args.bdName,args.bdName,
            parseInt((args.page-1)*args.limit),
            args.limit);
        let convertRes = SqlUtils.convertToModel(sqlRes);
        return {"total":sqlResCount[0].total,"data":convertRes};
    }
}

BaseDeService.toString = () => '[class BaseDeService]';
module.exports = BaseDeService;
