const debug = require('debug')('core:ipcServer');
const is = require('is-type-of');
const { ipcMain, app} = require('electron');
const path = require('path');
const fs = require('fs');
const globby = require('globby');
const Utils = require('../core/lib/utils');
const Wrap = require('../utils/wrap');
const Log = require('../log');
const {ParamUtils} = require("../core/lib/utils/ParamUtils");
const { memoize } = require('../../electron/utils');
class IpcServer {
  constructor (app) {
    this.app = app;
    this.register();
  }

  register () {
    const self = this;
    // 遍历方法
    const files = Utils.filePatterns();

    //const directory = path.join(this.app.config.baseDir, 'controller');
    const arrfilepaths=[{name:"default",directory:path.join(this.app.config.baseDir, 'controller')}];
    //const filepaths = globby.sync(files, { cwd: directory });
    let buffer = fs.readFileSync(path.join(app.getAppPath(), 'packages', "worker.json"));
    let worker=JSON.parse(buffer.toString());
    if(worker.length>0){
      worker.forEach((item)=>{
        const workerPropaths = path.join(app.getAppPath(), 'packages', item,'controller');
        arrfilepaths.push({name:item,directory:workerPropaths});
      });
    }
    for (const {directory,name} of arrfilepaths) {
      const filepaths = globby.sync(files, { cwd: directory });
      for (const filepath of filepaths) {
        const fullpath = path.join(directory, filepath);
        if (!fs.statSync(fullpath).isFile()) continue;
        const properties = Wrap.getProperties(filepath, {caseStyle: 'lower'});
        let pathName = directory.split(/[/\\]/).slice(-1) + '.' + properties.join('.');
        if(name!="default"){
          pathName = directory.split(/[/\\]/).slice(-1) + '.'+name+ '.'+ properties.join('.');
        }
        let fileObj = Utils.loadFile(fullpath);
        const fns = {};
        // 为了统一，仅支持class文件
        if (is.class(fileObj) || Utils.isBytecodeClass(fileObj)) {
          let proto = fileObj.prototype;
          // 不遍历父类的方法
          //while (proto !== Object.prototype) {
          const keys = Object.getOwnPropertyNames(proto);
          for (const key of keys) {
            if (key === 'constructor') {
              continue;
            }
            const d = Object.getOwnPropertyDescriptor(proto, key);
            if (is.function(d.value) && !fns.hasOwnProperty(key)) {
              fns[key] = 1;
            }
          }
          //proto = Object.getPrototypeOf(proto);
          //}
        }

        debug('register class %s fns %j', pathName, fns);

        for (const key in fns) {
          let channel = pathName + '.' + key;
          debug('register channel %s', channel);
          const findFn = (c) => {
            try {
              // 找函数
              const cmd = c;
              let fn = null;
              if (is.string(cmd)) {
                const actions = cmd.split('.');
                let obj = this.app;
                actions.forEach(key => {
                  obj = obj[key];
                  if (!obj) throw new Error(`class or function '${key}' not exists`);
                });
                fn = obj;
              }
              if (!fn) throw new Error('function not exists');

              return fn;
            } catch (err) {
              Log.coreLogger.error('[core] [socket/IpcServer] throw error:', err);
            }
            return null;
          }
          const memoizeFindFn = memoize(findFn)

          // send/on 模型
          ipcMain.on(channel, async (event, params) => {
            const fn = memoizeFindFn(channel);
            const result = await fn.call(this.app, params, event);

            event.returnValue = result;
            event.reply(`${channel}`, result);
          });

          // invoke/handle 模型
          ipcMain.handle(channel, async (event, params) => {
            let whiteList = [
              'controller.commonController.diffProject',
              'controller.constructProjectController.getBottomSummary',
            ];
            //if (!whiteList.includes(channel))
              // console.log(
              //     '拦截到的数据:',
              //     '请求接口:', channel,
              //     '请求参数', params
              // );
            const fn = memoizeFindFn(channel);
            // constructId, singleId, unitId
            if (params) {
              ParamUtils.setParam("commonParam", {
                "constructId":params.constructId?params.constructId:params.id,
                "singleId":params.singleId,
                "unitId":params.unitId?params.unitId:params.unitWorkId
              });
            }
            var startTime = performance.now();
            // console.log(channel);
            const result = await fn.call(this.app, params, event);
            var endTime = performance.now();
            // 计算执行时间
            var executionTime = endTime - startTime;
            // console.log(channel + '代码执行时间：', executionTime.toFixed(2), '毫秒');
            return result;
          });
        }
      }
    }

  }
}

module.exports = IpcServer;
