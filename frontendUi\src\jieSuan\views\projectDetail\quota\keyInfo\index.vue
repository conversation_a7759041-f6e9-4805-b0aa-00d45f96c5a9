<template>
  <div style="width:100%;">
    <split horizontal ratio="2/1" :horizontalBottom="35" style="height: 100%" mode="vertical"
      @onDragHeight="dragHeight">
      <template #one>
        <div class="table-top-content">
          <vxe-table align="center" height="98%" :style="{ width: columnWidth(550) + 'px', maxWidth: '100%' }"
            ref="basicInfoTable" :loading="loading" :column-config="{ resizable: true }" :row-config="{
              isHover: true,
              isCurrent: true,
              keyField: cacheAll.rowKeyFiled,
            }" :data="tableData" :tree-config="{
              expandAll: true,
              children: cacheAll.treeChildrenKey,
              reserve: true,
            }" :edit-config="{
              trigger: 'click',
              mode: 'cell',
              beforeEditMethod: cellBeforeEditMethod,
              showIcon: false,
              showStatus: false,
            }" @cell-click="
              cellData => {
                useCellClickEvent(cellData);
              }
            " class="table-edit-common table-no-outer-border"
            :cell-class-name="({ $columnIndex, row, column }) => {
              const selectName = selectedClassName({ $columnIndex, row, column });
              if (
                column.field === 'name' &&
                ['基本信息', '投标信息', '招标信息'].includes(row.name) &&
                row.addFlag === 0
              ) {
                return 'title-bold ' + selectName;
              }

              if (column.field === 'name' && row.type === 'title') {
                return 'title-bold ' + selectName;
              }
              if (
                column.field === 'name' &&
                colorFieldList.includes(
                  row.name
                )
              ) {
                return 'color-red ' + selectName;
              }
              return selectName;
            }
              " :row-class-name="({ row }) => {
                if (row.lockFlag == 1) {
                  return 'row-lock-color';
                }
              }
                ">
            <vxe-column field="dispNo" :width="columnWidth(50)" title="序号"></vxe-column>
            <vxe-column field="name" align="left" title="名称" :width="columnWidth(196)"
              :edit-render="{ autofocus: '.vxe-input--inner' }">
              <template #default="{ row }">
                <span>{{ row.name }}</span>
              </template>
              <template #edit="{ row }">
                <vxe-input :clearable="false" v-if="row.addFlag && !row.lockFlag" placeholder="请输入名称" v-model="row.name"
                  type="text" name="name" @blur="inputFinish(row, $event, 'name')"></vxe-input>
                <span v-else>{{ row.name }}</span>
              </template>
            </vxe-column>
            <vxe-column field="remark" align="left" title="备注" :min-width="columnWidth(250)"
              :edit-render="{ autofocus: '.vxe-input--inner' }">
              <template #edit="{ row }">
                <vxeTableEditSelect :filedValue="row.remark" :list="row.jsonStr" :isNotLimit="true"
                  v-if="ifShowSelect(row) && ![...avergeList, ...dateSelect].includes(row.name.trim())"
                  @update:filedValue="
                    newValue => {
                      saveCustomInput(newValue, row, 'remark', $rowIndex);
                    }
                  "></vxeTableEditSelect>
                <vxe-input v-else-if="ifShowSelect(row) && dateSelect.includes(row.name)" v-model="row.remark"
                  @change="timeSelect(row, { $event })" placeholder="日期选择" type="date"></vxe-input>
                <vxe-input v-else-if="ifShowSelect(row) && avergeList.includes(row.name.trim())" v-model="row.remark"
                  @blur="averageBlur(row, $event, 'remark')" @keyup="row.remark = limitNum(row.remark)"></vxe-input>
                <span v-else>{{ row.remark }}</span>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </template>
      <template #two>
        <div class="quota-info">
          <div class="head-action">
            <a-tabs v-model:activeKey="tabSel" type="card" :hideAdd="true">
              <a-tab-pane :key="1" tab="计算口径设置"></a-tab-pane>
            </a-tabs>
          </div>
          <div class="table-content">
            <s-table 
              v-if="loadingTable"
              :columns="columnArr" 
              size="small"
              ref="stableRef"
              class="s-table"
              row-key="id"
              :virtual-scroll="false"
              :defaultExpandAllRows="true"
              bordered
              @mousedown="mousedownHandle"
              :delay="200"
              :rangeSelection="true"
              :scroll="{ y: stableHeight }"
              :animateRows="false"
              :pagination="false"
              :data-source="caliberData"
            >
             <!-- 自定义头部 -->
             <template #headerCell="{ title, column }">
                <!-- {{column}} -->
                <span class="custom-header" style="font-weight: bold">
                  <i class="vxe-icon-edit" v-show="column.editable"></i>&nbsp;{{
                    title
                  }}
                </span>
              </template>
              <!--自定义编辑 -->
              <template
                #cellEditor="{
                  column,
                  modelValue,
                  save,
                  closeEditor,
                  record: row,
                  editorRef,
                  getPopupContainer,
                  recordIndexs,
                }"
              >
                <template v-if="column.dataIndex=='indexCountCaliber'">
                  <a-input
                    :bordered="false"
                    :value="modelValue.value"
                    :get-popup-container="getPopupContainer"
                    @update:value="
                    v => {
                      modelValue.value=v
                      row.indexCountCaliber = v;
                    }
                  "
                    @blur="
                      () => {
                        let value = modelValue.value;
                        if (!/^(0|[1-9]\d*)(\.\d+)?$/.test(value)) {
                          if (value !== '') {
                            value = 0;
                          }
                        }
                        modelValue.value = qdDeAmountFormat(value);
                        row.indexCountCaliber = modelValue.value;
                        handleChange(row);
                        closeEditor();
                      }
                    "
                  />
                </template>
                <template v-if="column.dataIndex=='zbUnit'">
                  <a-select 
                    :bordered="false"
                    v-model:value="row.zbUnit" 
                    :options="unitList" 
                    :fieldNames="{
                      label: 'name',
                      value: 'name',
                    }"
                    @change="
                      () => {
                        handleChange(row);
                        closeEditor();
                      }
                    "
                    @blur="
                        () => {
                          closeEditor();
                        }
                      "
                    >
                  </a-select>
                </template>
              </template>
            </s-table>
          </div>
        </div>
      </template>
    </split>
    <!-- 设置查看范围 -->
    <setViewingRange ref="setViewingRangeRef" @refresh="refreshData"></setViewingRange>
    <!-- 参考指标设置 -->
    <referenceIndicator ref="referenceIndicatorRef"></referenceIndicator>
    <!-- 匹配指标 -->
    <matchingIndicators ref="matchingIndicatorsRef" @refresh="refreshData"></matchingIndicators>
  </div>
</template>

<script setup>
import {
  onMounted,
  onUpdated,
  onActivated,
  ref,
  watch,
  inject,
  toRaw,
  getCurrentInstance,
} from 'vue';
import xeUtils from 'xe-utils';
import { message, Modal } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import setViewingRange from '../components/setViewingRange.vue';
import referenceIndicator from '../components/referenceIndicator.vue';
import matchingIndicators from '../components/matchingIndicators.vue';
import jiesuanApi from '@/api/jiesuanApi';
import { insetBus } from '@/hooks/jieSuanZbInsetBus';
import { columnWidth } from '@/hooks/useSystemConfig';
import { useDecimalPoint } from '@/hooks/useDecimalPoint';
import { updateOperateByName } from '@/jieSuan/views/projectDetail/quota/operate';
const { qdDeAmountFormat } = useDecimalPoint();
const setViewingRangeRef = ref()
const referenceIndicatorRef = ref()
const matchingIndicatorsRef = ref()
const loadingTable = ref(false)
const stableRef = ref()
const stableHeight = ref(400);
const unitList = ref([
  {name:'m'},
  {name:'m²'},
  {name:'m³'},
]);
const columnArr=ref([
  {
    title: '序号',
    dataIndex: 'disp',
    key: 'disp',
    width:120,
    fixed: 'left',
  },
  {
    title: '单体工程名称',
    dataIndex: 'name',
    key: 'name',
    autoHeight:true,
    width:180,
    fixed: 'left',
  },
  {
    title: '计算口径',
    dataIndex: 'indexCountCaliber',
    key: 'indexCountCaliber',
    width:170,
    align: 'center',
    editRender: { autofocus: '.vxe-select' },
    ellipsis: true,
    visible: true,
    slot: true,
    editable: 'cellEditorSlot',
  },
  {
    title: '单位',
    dataIndex: 'zbUnit',
    key: 'zbUnit',
    width:100,
    align: 'center',
    editRender: { autofocus: '.vxe-select' },
    ellipsis: true,
    visible: true,
    slot: true,
    editable: ({ record: rows }) => {
      if(rows.levelType === 2){
        return 'cellEditorSlot';
      }else{
        return false
      }
    },
  },
])
import { useCellClick } from '@/hooks/useCellClick';
import { refresh } from 'less';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } = useCellClick();
onActivated(() => {
  insetBus(bus, projectStore.componentId?projectStore.componentId:'keyInfo' , 'keyInfo', async data => {
    //匹配指标
    if (data.name === 'matching-indicators'){
      matchingIndicatorsRef.value.open(true);
    };
    //设置查看范围
    if (data.name === 'set-viewing-range'){
      setViewingRangeRef.value.open(true);
    };
    //参考指标设置
    if (data.name === 'reference-indicator-setting'){
      referenceIndicatorRef.value.open(true);
    };
  });
  getBasicInfo();
  getCaliber()
  updateOperateByName('set-viewing-range', info => {
    info.disabled = false
  });
});
//字体标红字段列表
const colorFieldList = [
  // '建筑面积',
];
const dateSelect = ['开工日期', '竣工日期', '编制时间', '核对(复核)时间']; //时间选择器设置字段列表
const avergeList = ['建筑面积', '工程规模']; //建筑面积列表
const notEditList = ['工程名称','基本信息', '工程所在地', '招标信息', '投标信息']; //不可编辑字段
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
let basicInfoTable = ref();
let loading = ref(false);
let constructLevel = ref(true);
const projectStore = projectDetailStore();
const tableData = ref([]);
const caliberData = ref([]);
const tabSel = ref(1);
const flashFun = () => {
  console.log('*****************基本信息');
  if (
    projectStore.asideMenuCurrentInfo?.key === '11' &&
    projectStore.currentTreeInfo?.levelType !== 2 &&
    ['项目概况', '工程概况'].includes(projectStore.tabSelectName)
  ) {
    getBasicInfo();
    getCaliber()
  }
};
const refreshData = () => {
  getBasicInfo();
  getCaliber()
}
const dragHeight = h => {
  let tableEl = document.querySelector('.table-content');
  stableHeight.value = tableEl.clientHeight - 80;
};
const ifShowSelect = row => {
  return (
    (projectStore.currentTreeInfo?.levelType === 1 &&
        !row.lockFlag &&
        !notEditList.includes(row.name)) ||
    row.addFlag ||
    (projectStore.currentTreeInfo?.levelType === 3 &&
        !['工程专业'].includes(row.name))
  );
};
const limitNum = value => {
  if (typeof value !== 'string') return value;
  return value.replace(/[^(-?\d+)\.?(\d*)$]/g, '');
};
const averageBlur = (row, e, attr) => {
  let value = xeUtils.trim(e.value);
  if (row[attr] !== '') {
    row[attr] = qdDeAmountFormat(row[attr]);
  }
  saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true);
};
let isRefresh = ref(false);
const saveCustomInput = (newValue, row, name, index) => {
  const list = [null, undefined, ''];
  if (
    (list.includes(row[name]) && list.includes(newValue)) ||
    row[name] === newValue
  )
    return;
  row[name] = newValue;
  if (row.name === '工程名称' || row.name === '单位工程名称') {
    isRefresh.value = true;
  } else {
    isRefresh.value = false;
  }
  saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true);
};
watch(() => projectStore.currentTreeInfo, flashFun);
const timeSelect = (row, { $event }) => {
  // debugger;
  console.log('timeSelect', $event);
  row['remark'] = $event.value;
  saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true);
};
const getBasicInfo = async () => {
  let apiData={
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    levelType:1,
    type:0
  }
  jiesuanApi.queryIndexProjectOverview(apiData).then((res) => {
    console.info('关键信息上方列表返回结果',res)
    if(res.code!==200){
      return message.error(res.message)
    }
    loading.value = false;
    tableData.value = res.result;
    cacheAll.currentRecord =
      cacheAll.newAddRowSeq === null
        ? tableData.value
          ? tableData.value[0]
          : null
        : findLastAddRow(tableData.value);
    resetCurrentRow();
    cacheAll.newAddRowSeq = null;
  });
};
const findLastAddRow = tree => {
  console.log('cacheAll.newAddRowSeq', cacheAll.newAddRowSeq, tree);
  let targetBeforeIdx = tree.findIndex(
    i => i.sequenceNbr === cacheAll.newAddRowSeq
  );
  let target = tree[targetBeforeIdx];
  return target;
};
// 获取计算口径列表
const getCaliber = async () => {
  let apiData={
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    levelType:1,
    type:0
  }
  jiesuanApi.queryIndexCaliber(apiData).then((res) => {
    console.info('关键信息计算口径列表返回结果',res)
    if(res.code!==200){
      return message.error(res.message)
    }
    let testTreeData = arrayToTree(res.result)
    caliberData.value = testTreeData;
    loadingTable.value=true
  });
};
function arrayToTree(items, id = 'id', parentId = 'parentId', children = 'children') {
  const result = []; // 存放结果集
  const itemMap = new Map(); // 用于存储所有项的映射
  
  // 首先将所有项存入Map
  items.forEach(item => {
    itemMap.set(item[id], item);
  });
  
  // 遍历所有项
  items.forEach(item => {
    const parent = itemMap.get(item[parentId]);
    if (parent) {
      // 如果存在父节点，则将当前项添加到父节点的children数组中
      (parent[children] || (parent[children] = [])).push(item);
    } else {
      // 否则，将当前项添加到结果集中
      result.push(item);
    }
  });
  
  return result;
}
const saveOrUpdateBasicInfo = async (param, isUpdate = false) => {
  let apiData = {
    levelType: projectStore.currentTreeInfo?.levelType,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId:null,
    unitId:null,
    type:0,
    code: 1,
    projectOverviewList: toRaw(param.data),
  };
  jiesuanApi.saveIndexProjectOverview(apiData).then((res) => {
    res.status === 200 ? message.success('操作成功') : '';
    if (res.status === 200) {
      refreshData()
    }
    if (res.status === 200 && isRefresh.value) {
      projectStore.SET_IS_REFRESH_PROJECT_TREE(true);
    }
  }) 
};
// 切换下拉修改
const handleChange=(row,field)=> {
  let apiData={
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    indexCaliberList:[row]
  }
  console.info('计算口径修改参数',apiData)
  jiesuanApi.saveIndexCaliber(apiData).then((res) => {
    if(res.code!==200){
      return message.error(res.message)
    }
    refreshData()
  });
}
const cacheAll = {
  newAddRowSeq: null,
  currentRecord: null,
  treeChildrenKey: 'childrenList',
  rowKeyFiled: 'sequenceNbr',
  newRecord: function (parentId, groupCode) {
    return {
      sequenceNbr: Date.now(),
      name: '',
      remark: null,
      addFlag: 1,
      lockFlag: 0,
      parentId: parentId,
      groupCode: groupCode,
    };
  },
  copyRow: function (row) {
    let newRow = {};
    Object.assign(newRow, row);
    newRow.sequenceNbr = Date.now();
    newRow.childrenList = null;
    newRow.addFlag = 1;
    newRow.lockFlag = 0;
    newRow.recDate = null;

    return newRow;
  },
};
const inputFinish = (row, e, attr) => {
  let value = xeUtils.trim(e.value);
  if (value.length > 50) {
    value = value.slice(0, 50);
    row[attr] = value;
    message.warning('输入过长，请输入50个字符范围内');
  }
  row[attr] = value;
  saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true);
};
const resetCurrentRow = () => {
  cacheAll.currentRecord =
    cacheAll.currentRecord || (tableData.value ? tableData.value[0] : null);
  basicInfoTable.value?.setCurrentRow(cacheAll.currentRecord);
};
onUpdated(() => {
  //设置展开所有节点
  basicInfoTable.value.setAllTreeExpand(true);
  //设置选中行为第一行
  resetCurrentRow();
  constructLevel.value = projectStore.currentTreeInfo?.levelType === 1;
});
const tooltipConfig = {
};

// 表格上任意地方点击时捕获，目前控制 移除cut 时的样式
const mousedownHandle = e => {
  stableRef.value.closeEditor();
};
onMounted(() => {
});
defineExpose({
});
</script>
<style lang="scss" src="@/assets/css/subItemProject.scss" scoped></style>
<style lang="scss" scoped>
@import '@/views/projectDetail/customize/subItemProject/s-table.scss';
.table-content{
  width:650px;
}
.head {
  display: flex;
  align-items: center;
  padding: 0 10px;
}

.table-top-content {
  height: calc(100%);
}

.table-top-content :deep(.vxe-table) {
  .title-bold {
    font-weight: bold;
  }

  .color-red {
    color: #de3f3f;
  }

  .row-lock-color {
    background-color: #bfbfbf;
  }

  .vxe-cell .vxe-cell--label {
    // ::selection {
    user-select: none;
    // }
  }
}

.quota-info {
  height: 100%;
  ::v-deep .surely-table-cell-content{
    min-height:35px !important;
  }
  .head-action {
    margin-bottom: 5px;
    height: 35px;
    background: #e7e7e7;
    flex: 1;

    :deep(.ant-tabs-tab) {
      height: 35px;
      background: transparent;
      border: none;
      color: #7c7c7c;
    }

    :deep(.ant-tabs-tab-active) {
      background: #ffffff;
      border-top: 2px solid #4786ff;

      .ant-tabs-tab-btn {
        color: #000000;
      }
    }

    button {
      float: right;
      margin-right: 15px;
    }
  }

  .content {
    height: calc(100% - 40px);
  }
}
</style>
