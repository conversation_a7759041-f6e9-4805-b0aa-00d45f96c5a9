import { ref } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
const projectStore = projectDetailStore();

const getTableColumns = () => {
  /**
   * 合同内人材机明细
   */
  const originalMaterialTableColumns = ref([
    {
      treeNode: true,
      title: '编码',
      field: 'materialCode',
      width: '120',
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '类别',
      field: 'type',
      editRender: {},
      width: '100',
      slot: true,
    },
    {
      title: '名称',
      field: 'materialName',
      editRender: { autofocus: '.vxe-input--inner' },
      width: '100',
      slot: true,
    },
    {
      title: '规格型号',
      field: 'specification',
      editRender: { autofocus: '.vxe-input--inner' },
      width: '100',
      slot: true,
    },
    {
      title: '单位',
      field: 'unit',
      editRender: {},
      width: '70',
      slot: true,
    },
    {
      title: '消耗量',
      field: 'resQtyBds',
      editRender: { autofocus: '.vxe-input--inner' },
      width: '90',
      slot: true,
    },
    {
      title: '合同合计数量',
      field: 'jieSuanTotalNumber',
      width: '100',
    },
    {
      title: '结算合计数量',
      field: 'totalNumber',
      editRender: { autofocus: '.vxe-input--inner' },
      slot: true,
      width: '100',
    },
    {
      title:
        projectStore.deStandardReleaseYear === '12'
          ? '预算价'
          : Number(projectStore.taxMade) === 1
          ? '合同/不含税基期价'
          : '合同/含税基期价',
      field: 'dePrice',
      width: '110',
    },

   
    {
      title: '合同合价',
      field: 'jieSuanTotal',
      width: '70',
    },
    {
      title: '结算合价',
      field: 'total',
      width: '70',
    },
  ]);

  if (projectStore.deStandardReleaseYear === '22') {
    let index = originalMaterialTableColumns.value.findIndex(
      a => a.field === 'dePrice'
    );
    originalMaterialTableColumns.value.splice(
      index + 1,
      0,
      ...[
        {
          title: '合同/不含税市场价',
          field: 'priceMarket',
          editRender: { autofocus: '.vxe-input--inner' },
          slot: true,
          width: '130',
        },
        {
          title: '合同/含税市场价',
          field: 'priceMarketTax',
          editRender: { autofocus: '.vxe-input--inner' },
          slot: true,
          width: '120',
        },
        {
          title: '合同/税率',
          field: 'taxRate',
          slot: true,
          width: '100',
        },
      ]
    );
  }
  if (projectStore.deStandardReleaseYear === '12') {
    let index = originalMaterialTableColumns.value.findIndex(
      a => a.field === 'dePrice'
    );
    originalMaterialTableColumns.value.splice(
      index + 1,
      0,
      {
        title: '合同/确认单价',
        field: 'jieSuanMarketPrice',
        editRender: {},
        width: '120',
        slot: true,
      },
    );
  }
  /**
   * 合同外人材机明细
   */
  const materialTableColumns = ref([
    {
      treeNode: true,
      title: '编码',
      field: 'materialCode',
      width: '120',
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '类别',
      field: 'type',
      editRender: {},
      width: '100',
      slot: true,
    },
    {
      title: '名称',
      field: 'materialName',
      editRender: { autofocus: '.vxe-input--inner' },
      width: '100',
      slot: true,
    },
    {
      title: '规格型号',
      field: 'specification',
      editRender: { autofocus: '.vxe-input--inner' },
      width: '100',
      slot: true,
    },
    {
      title: '单位',
      field: 'unit',
      editRender: {},
      width: '70',
      slot: true,
    },
    {
      title: '消耗量',
      field: 'resQtyBds',
      editRender: { autofocus: '.vxe-input--inner' },
      width: '90',
      slot: true,
    },
    {
      title: '锁定消耗量',
      field: 'isLock',
      width: '90',
      slot: true,
    },
    // {
    //   title: '合同合计数量',
    //   field: 'totalNumber',
    //   width:"100",
    // },
    {
      title: '合计数量',
      field: 'totalNumber',
      editRender: { autofocus: '.vxe-input--inner' },
      width: '100',
      slot: true,
    },
    {
      title:
        projectStore.deStandardReleaseYear === '12'
          ? '预算价'
          : Number(projectStore.taxMade) === 1
          ? '合同/不含税基期价'
          : '合同/含税基期价',
      field: 'dePrice',
      width: '110',
    },
    {
      title: '结算合价',
      field: 'total',
      width: '70',
    },
  ]);
  if (projectStore.deStandardReleaseYear === '22') {
    let index = materialTableColumns.value.findIndex(
      a => a.field === 'dePrice'
    );
    materialTableColumns.value.splice(
      index + 1,
      0,
      ...[
        {
          title: '合同/不含税市场价',
          field: 'priceMarket',
          width: '130',
          editRender: { autofocus: '.vxe-input--inner' },
          slot: true,
        },
        {
          title: '合同/含税市场价',
          field: 'priceMarketTax',
          width: '120',
          editRender: { autofocus: '.vxe-input--inner' },
          slot: true,
        },
        {
          title: '合同/税率',
          field: 'taxRate',
          width: '100',
          editRender: { autofocus: '.vxe-input--inner' },
          slot: true,
        },
      ]
    );
  }
  if (projectStore.deStandardReleaseYear === '12') {
    let index = materialTableColumns.value.findIndex(
      a => a.field === 'dePrice'
    );
    materialTableColumns.value.splice(
      index + 1,
      0,
      {
        title: '合同/确认单价',
        field: 'marketPrice',
        editRender: {},
        width: '120',
        slot: true,
      },
    );
  }
  return projectStore.currentTreeInfo?.originalFlag
    ? originalMaterialTableColumns.value
    : materialTableColumns.value;
};

export default getTableColumns;
