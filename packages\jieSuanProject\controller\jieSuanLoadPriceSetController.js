/**
 * 载价条件设置
 */
const {ResponseData} = require("../../../common/ResponseData");
const {Controller} = require("../../../core");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");

class JieSuanLoadPriceSetController extends Controller{
    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 批量应用接口更新市场价、价格来源
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async applyLoadingPriceInRcjDetails(args) {

        await this.service.jieSuanProject.jieSuanLoadPriceSetService.applyLoadingPriceInRcjDetails(args);

        /*if(args.priceType!==2&& args.type===2){
            await this.service.jieSuanProject.management.sycnTrigger("unitDeChange");
        }*/
        return ResponseData.success(true);
    }


    /**
     * 点击批量载价
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async jieSuanLoadingPrice(args) {
        let result = await this.service.jieSuanProject.jieSuanLoadPriceSetService.loadingPrice(args);
        if ( ObjectUtils.isNotEmpty(result) && result.length > 0) {
            return ResponseData.success(result);
        }else {  //没有数据  返回fail
            return ResponseData.fail(result);
        }
    }

    // /**
    //  * 双击价格选择弹窗的数据行 更新待载价格
    //  * @param args
    //  * @returns {Promise<ResponseData>}
    //  */
    // async updateLoadPrice(args) {
    //     await this.service.jieSuanProject.jieSuanLoadPriceSetService.updateLoadPrice(args);
    //     return ResponseData.success(true);
    // }
    /**
     * 返回载价编辑弹窗数据
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async jieSuanLoadPriceEditPage(args) {
        const result =await this.service.jieSuanProject.jieSuanLoadPriceSetService.jieSuanloadPriceEditPage(args);
        // const result = await this.service.loadPriceSetService.jieSuanloadPriceEditPage(args);
        return ResponseData.success(result);
    }






    /**
     * 结算 鼠标右键清除载价
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async jiesuanClearLoadPriceUse(args) {
        let result = await this.service.jieSuanProject.jieSuanLoadPriceSetService.jiesuanClearLoadPriceUse(args);

        return ResponseData.success(result);
    }



    /**
     * 结算中鼠标右键查询人材机关联定额
     * @param args
     * @return {Promise<ResponseData>}
     */
    async jieSuanGetRcjDe(args){

        const result = await this.service.jieSuanProject.jieSuanLoadPriceSetService.jieSuanGetRcjDe(args);

        return ResponseData.success(result);

    }


    /**
     * 查询载价报告明细列表
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async loadPriceList(args){

        let list =  await this.service.jieSuanProject.jieSuanLoadPriceSetService.loadPriceList(args);

        return ResponseData.success(list);

    }


    /**
     * 查询载价报告 -扇形图
     * @returns {Promise<ResponseData>}
     */
    async queryLoadPriceReportRcj(args){

        let result = await this.service.loadPriceSetService.queryLoadPriceReportRcj(args);

        return ResponseData.success(result);
    }


    /**
     * 查询载价报告 -柱状图
     * @returns {Promise<ResponseData>}
     */
    async queryLoadPriceReportTarget(args){

        let result = await this.service.loadPriceSetService.queryLoadPriceReportTarget(args);

        return ResponseData.success(result);
    }


}

JieSuanLoadPriceSetController.toString = () => '[class JieSuanLoadPriceSetController]';
module.exports = JieSuanLoadPriceSetController;