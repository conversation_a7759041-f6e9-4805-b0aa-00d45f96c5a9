/*
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-05-05 14:52:20
 * @LastEditors: wangru
 * @LastEditTime: 2025-06-17 09:54:43
 */
import { ipc } from '@/utils/ipcRenderer';
import antd from 'ant-design-vue';
import 'ant-design-vue/dist/antd.css';
import mitt from 'mitt';
import { createApp } from 'vue';
import './assets/css/common.scss';
import './assets/css/viewscal.scss';
import router from './router';
import './style.css';
// 引入vxe-table
import { setupVxeTable } from '@/plugins/vxeTable';

import { createFromIconfontCN } from '@ant-design/icons-vue';
import '@surely-vue/table/dist/index.less';
import { createPinia } from 'pinia';
import 'vxe-table/lib/style.css';
import MainApp from './App.vue';
import './assets/css/common.scss';
import directives from './directives/index';
import echarts from './utils/echarts';

import STable, { setLicenseKey } from '@surely-vue/table';
import '@surely-vue/table/dist/index.less';
import { MD5 as md5 } from 'crypto-js';
import { encode as encodeBase64 } from 'js-base64';
import './assets/css/common.scss';

let app = createApp(MainApp);
app.config.globalProperties.$echarts = echarts;

app.config.globalProperties.$bus = mitt();
app.config.globalProperties.$ipc = ipc;

const IconFont = createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_4199803_zvxp79w6wm.js',
});

/**
 * 破解 \@surely-vue/table 授权。
 * @param  [options] 配置项。
 * @param  [options.hostname] 授权域名（默认值：`location.hostname`）。
 */
const hackLicenseKey = options => {
  const domain = options?.hostname ?? globalThis.location.hostname;
  console.log('domain', domain);
  const key = encodeBase64(
    `ORDER:00001,EXPIRY=33227712000000,DOMAIN=${domain},ULTIMATE=1,KEYVERSION=1`,
  );
  const sign = md5(key).toString().toLowerCase();
  setLicenseKey(`${sign}${key}`);
};
hackLicenseKey();
//获取public目录下所有的文件信息
const modulesFiles = import.meta.globEager('@/components/global/*/index.vue');
const proModulesFiles = import.meta.globEager('@/components/proCommonModel/*/index.vue');
const pathList = [];
//遍历拿到所有的文件名称
for (const path in { ...modulesFiles, ...proModulesFiles }) {
  pathList.push(path);
}
//全局批量注册components下所有组件
pathList.forEach(path => {
  const componentEl = (modulesFiles[path] || proModulesFiles[path]).default;
  if (app && componentEl.name) {
    app.component(componentEl.name, componentEl);
  }
});
//增加全局监听--localStorage中用户不可手动修改
// window.addEventListener('storage', e => {
//   localStorage.setItem(e.key, e.oldValue); //重新赋值修改前的值
// });
app.config.warnHandler = () => null; //清除警告
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true'; //控制台屏蔽警告信息
app.component('IconFont', IconFont);
setupVxeTable(app);
app.use(createPinia());
app.use(antd);
app.use(router);
app.use(directives);
app.use(STable);
app.mount('#app');
