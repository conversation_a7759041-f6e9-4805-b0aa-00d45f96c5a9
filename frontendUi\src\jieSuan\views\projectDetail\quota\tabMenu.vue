<!--
 * @Descripttion: tab顶部菜单
 * @Author: renmingming
 * @Date: 2023-05-17 10:10:08
 * @LastEditors: k<PERSON><PERSON><PERSON>ang
 * @LastEditTime: 2025-03-31 17:10:27
-->
<template>
  <div class="tab-menu">
    <a-tabs
      v-model:activeKey="activeKey"
      type="card"
      @change="tabsChange"
    >
      <a-tab-pane
        :key="tab.code"
        :tab="tab.value"
        v-for="tab in tabs"
      ></a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import csProject from '@/api/csProject';
import { onMounted, ref, watch, markRaw } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { proModelStore } from '@/store/proModel.js';
import { message, Modal } from 'ant-design-vue';
import infoMode from '@/plugins/infoMode.js';
import jiesuanApi from '@/api/jiesuanApi';
const store = projectDetailStore();
const ModelStore = proModelStore();
import operateList from './operate';
const tabs = ref([
  {
    value:'关键信息',
    code:1
  },
  {
    value:'主要经济指标',
    code:2
  },
  {
    value:'主要工程量指标',
    code:3
  },
  {
    value:'主要工料指标',
    code:4
  },
]);
import feePro from '@/api/feePro';
const activeKey = ref(1);
let unifyData = ref(); //统一应用按钮是否禁用
import { setGlobalLoading } from '@/hooks/publicApiData';

const emit = defineEmits(['getActiveKey', 'getTitle']);

const authTab = ref([]); // 权限tab,单位工程，调用查询是否单位工程完善. 如果为空，则默认全部都可以查看，

const fefxActive = markRaw({
  activeKey: 1,
});
onMounted(() => {
  emit('getActiveKey', '关键信息');
});
let loading = ref(false);
const tabsChange = async (val) => {
  const selectTabName = tabs.value.filter(item => item.code === val)[0].value;
  emit('getActiveKey', selectTabName);
};

const tabChangeByValue = (val, callBack = null) => {
  if (loading.value) {
    setTimeout(() => {
      tabChangeByValue(val, callBack);
    }, 1000);
    return;
  }
  const selectTab = tabs.value.find(item => {
    return item.value === val;
  });
  if (!selectTab) {
    return;
  }
  activeKey.value = selectTab.code;
  tabsChange(selectTab.code);
  if (callBack) callBack();
};
/**
 * 根据名称获取codeKey
 * @param {*} val
 */
const getKeyByName = val => {
  const selectTab = tabs.value.find(item => {
    console.log('item', item, val);
    return item.value === val;
  });
  return selectTab?.code;
};

defineExpose({
  tabChangeByValue,
  getKeyByName,
});
</script>
<style lang="scss" scoped>
.tab-menu :deep(.ant-tabs) {
  height: var(--project-detail-main-content-tabs-menu-height);
  position: relative;
  top: 1px;
  box-sizing: border-box;
  font-size: 12px;
  .ant-tabs-nav {
    margin-bottom: 0;
    // padding: 0 11px;
    height: 100%;
    .ant-tabs-tab {
      padding: 5px 20px;
      border: none;
      border-radius: 4px;
      background-color: transparent;
      font-size: 12px;
      border-right: 1px solid #d6d6d6;
      margin-left: 0;
    }
    .ant-tabs-nav-more {
      display: none !important;
    }
    .ant-tabs-tab-active {
      background-color: #deeaff;
      .ant-tabs-tab-btn {
        color: #333333;
      }
    }
  }
}
</style>
