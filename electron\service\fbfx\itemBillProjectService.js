const {Service} = require("../../../core");
const {ItemBillProject} = require("../../model/ItemBillProject");
const {Snowflake} = require("../../utils/Snowflake");
const BranchProjectLevelConstant = require("../../enum/BranchProjectLevelConstant");
const BranchDirectoryTree = require("../../vo/BranchDirectoryTree");
const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const {PricingFileWriteUtils} = require("../../utils/PricingFileWriteUtils");
const {ArrayUtil} = require("../../utils/ArrayUtil");
const {ConvertUtil} = require("../../utils/ConvertUtils");
const {NumberUtil} = require("../../utils/NumberUtil");
const {number} = require("is-type-of");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const TreeListVo = require("../../vo/TreeListVo");
const _ = require("lodash");
/**
 * 分部分项service
 */
class ItemBillProjectService extends Service {
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 初始化分部分项数据
     * @param constructId 工程id
     * @param singleId 单项id
     * @param unitId 单位id
     * @returns {Promise<void>}
     */
    initUnitFb(constructId, singleId, unitId) {
        // 生成ID
        let initTopData = new ItemBillProject();
        initTopData.sequenceNbr = Snowflake.nextId();
        initTopData.bdName = "单位工程";
        initTopData.kind = "0";
        initTopData.unitId = unitId;
        initTopData.sortNo = 1;

        // 读出来放到内存
        let fbFx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        if (ObjectUtils.isEmpty(fbFx)) {
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            unit.itemBillProjects = [];
            unit.itemBillProjects.push(initTopData);
        } else {
            fbFx.push(initTopData);
        }
    }
    async queryUnitBranchTree(args) {
        let {constructId, singleId, unitId} = args;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let allData = unit.itemBillProjects;
        //获取到根节点数据
        let newData = [];
        let root = allData.root;
        function getAllChildValues(obj,newData) {
            if ([BranchProjectLevelConstant.top,BranchProjectLevelConstant.fb,BranchProjectLevelConstant.zfb].includes(obj.kind)){
                newData.push(obj);
                if (ObjectUtils.isNotEmpty(obj.children)) {
                    if(ObjectUtils.isNotEmpty(obj.children)){
                        if(obj.children[0].kind==BranchProjectLevelConstant.qd){
                            obj.childrenIsQd=true;
                        }
                    }
                    obj.children.forEach(item => {
                        getAllChildValues(item,newData)
                    });
                }
            }
        }
        getAllChildValues(root,newData);
        if (ObjectUtils.isEmpty(newData)) {
            throw new Error("error, 分部分项数据为空");
        }
        if (ObjectUtils.isEmpty(newData)) {
            throw new Error("error, 分部分项数据为空");
        }
        // 转为tree
        //let treeList = this.convertToBranchDirectoryTreeList(newData).sort((a, b) => a.sortNo - b.sortNo);
        let treeList = this.convertToBranchDirectoryTreeList(newData);
        // 顶级单位工程
        let topNode = treeList.filter(i => i.kind === BranchProjectLevelConstant.top);
        // 单位下的分部
        let fbTree = treeList.filter(i => i.kind === BranchProjectLevelConstant.fb);
        // 封装返回的树结构
        let topTree = topNode[0];
        if (fbTree.length < 1) {
            // 没有分部直接return顶层单位
            //return topTree;
            // 返回vo与bs保持一致
            let treeListVO = new TreeListVo();
            treeListVO.treeModel = topTree;
            return treeListVO;
        }
        // 单位下的分部
        topTree.childTreeModel = fbTree;
        // 递归给分部添加其下的子分部、及子子分部、子子子分部等
        this._recursionAddChildTree(topTree.childTreeModel, treeList);
        let treeListVO = new TreeListVo();
        treeListVO.treeModel = topTree;
        return treeListVO;
    }
    // 树节点
    _recursionAddChildTree(treeNode, allTreeList) {
        // 参数验证
        if (ObjectUtils.isEmpty(treeNode) || ObjectUtils.isEmpty(allTreeList)) {
            return;
        }

        // 预先按 parentId 分组，提高性能，兼容 parentId 为 null/undefined/'0'
        const childrenByParentId = allTreeList.reduce((acc, item) => {
            // 顶级节点统一用 'root' 作为 key
            const key = (item.parentId === null || item.parentId === undefined || item.parentId === '0' || item.parentId === 0) ? 'root' : item.parentId;
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(item);
            return acc;
        }, {});

        // 递归构建树
        const buildTree = (parentId) => {
            const key = (parentId === null || parentId === undefined || parentId === '0' || parentId === 0) ? 'root' : parentId;
            const children = childrenByParentId[key] || [];
            return children
                .filter(node => (node.kind === BranchProjectLevelConstant.zfb || node.kind === BranchProjectLevelConstant.fb))
                .map(node => {
                    const childNodes = buildTree(node.sequenceNbr);
                    if (childNodes.length > 0) {
                        node.childTreeModel = childNodes;
                    }
                    return node;
                });
        };

        // 获取顶级节点（parentId为null/undefined/'0'/0的节点）
        const topLevelNodes = childrenByParentId['root'] || [];
        if (topLevelNodes.length === 0) {
            return;
        }

        // 从顶级节点开始构建树
        const topNode = topLevelNodes[0];
        const childNodes = buildTree(topNode.sequenceNbr);
        if (childNodes.length > 0) {
            topNode.childTreeModel = childNodes;
        }

        // 更新传入的treeNode
        treeNode.length = 0;
        treeNode.push(topNode);
    }
    /**
     * 分部分项目录树
     * @param constructId
     * @param singleId
     * @param unitId 单位id
     *
     */
    async queryUnitBranchTreeOld(args) {
        let {constructId, singleId, unitId} = args;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let allData = unit.itemBillProjects;
        //获取到根节点数据
        let newData = [];
        let root = allData.root;
        function getAllChildValues(obj,newData) {
            if ([BranchProjectLevelConstant.top,BranchProjectLevelConstant.fb,BranchProjectLevelConstant.zfb].includes(obj.kind)){
                newData.push(obj);
                if (ObjectUtils.isNotEmpty(obj.children)) {
                    if(ObjectUtils.isNotEmpty(obj.children)){
                        if(obj.children[0].kind==BranchProjectLevelConstant.qd){
                            obj.childrenIsQd=true;
                        }
                    }
                    obj.children.forEach(item => {
                        getAllChildValues(item,newData)
                    });
                }
            }
        }
        getAllChildValues(root,newData);
        if (ObjectUtils.isEmpty(newData)) {
            throw new Error("error, 分部分项数据为空");
        }
        if (ObjectUtils.isEmpty(newData)) {
            throw new Error("error, 分部分项数据为空");
        }
        // 转为tree
        //let treeList = this.convertToBranchDirectoryTreeList(newData).sort((a, b) => a.sortNo - b.sortNo);
        let treeList = this.convertToBranchDirectoryTreeList(newData);
        // 顶级单位工程
        let topNode = treeList.filter(i => i.kind === BranchProjectLevelConstant.top);
        // 单位下的分部
        let fbTree = treeList.filter(i => i.kind === BranchProjectLevelConstant.fb);
        // 封装返回的树结构
        let topTree = topNode[0];
        if (fbTree.length < 1) {
            // 没有分部直接return顶层单位
            //return topTree;
            // 返回vo与bs保持一致
            let treeListVO = new TreeListVo();
            treeListVO.treeModel = topTree;
            return treeListVO;
        }
        // 单位下的分部
        topTree.childTreeModel = fbTree;
        // 递归给分部添加其下的子分部、及子子分部、子子子分部等
        this._recursionAddChildTree(topTree.childTreeModel, treeList);
        let treeListVO = new TreeListVo();
        treeListVO.treeModel = topTree;
        return treeListVO;
    }

    /* // 树节点
    _recursionAddChildTree(treeNode, allTreeList) {
        // 参数验证
        if (ObjectUtils.isEmpty(treeNode) || ObjectUtils.isEmpty(allTreeList)) {
            return;
        }

        treeNode.forEach(fbItem => {
            fbItem.childTreeModel = allTreeList.filter(i => i.kind === BranchProjectLevelConstant.zfb && i.parentId === fbItem.sequenceNbr);
            this._recursionAddChildTree(fbItem.childTreeModel, allTreeList);
        });
    } */

    /**
     * 转换为BranchDirectoryTree
     * @param itemBillList 分部分项modelList
     * @returns {*[]}
     */
    convertToBranchDirectoryTreeList(itemBillList) {
        if (null == itemBillList || itemBillList.length === 0) {
            return [];
        }
        let treeList = [];
        itemBillList.forEach((item, index, array) => {
            let treeNode = new BranchDirectoryTree();
            treeNode.sequenceNbr = item.sequenceNbr;
            treeNode.bdName = item.name;
            treeNode.sortNo = item.sortNo;
            treeNode.parentId = item.parentId;
            treeNode.kind = item.kind;
            treeNode.kind = item.kind;
            treeNode.childrenIsQd=item.childrenIsQd;
            treeNode.displaySign=item.displaySign;
            treeNode.displayStatu=item.displayStatu;
            treeNode.optionMenu=item.optionMenu;
            treeList.push(treeNode);
        });
        return treeList;
    }

    /**
     * 查分部分项
     * @deprecated
     * @param treeSequenceNbr 分部分项左侧树的seq
     * @param kind 0查所有 ; 01 04 查分部子分部及其下的清单定额
     * @param page 第几页 目前该字段没有用到，预留
     * @param limit 查几条数据
     * @param endRowSequenceNbr 结束行的seq,用于判断从第几行开始截取数据
     * @param constructId 工程id，用于查ysf文件中的分部分项
     * @param singleId 单项id，用于查ysf文件中的分部分项
     * @param unitId 单位id，用于查ysf文件中的分部分项
     * @see ItemBillProject#kind
     * @returns {Promise<ItemBillProject[]>}
     */
    async selectByKind(treeSequenceNbr, kind, page, limit, endRowSequenceNbr, constructId, singleId, unitId) {
        /*// 自测数据
        global.constructProject = [];
        global.constructProject[1] = {"constructName":"ysb测试项目","fileCode":"1660814140593278976","path":"C:\\Users\\<USER>\\.xilidata\\ysb测试项目.ysf","singleProjects":[{"sequenceNbr":"1","unitProjects":[{"itemBillProjects":[{"bdName":"单位工程","kind":"0","sequenceNbr":"1659383061407444994","unitId":"1658367326819016727"},{"bdName":"分部","kind":"01","parentId":"1659383061407444994","sequenceNbr":"1659384089636773889","unitId":"1658367326819016727"},{"bdName":"子分部","kind":"02","parentId":"1659384089636773889","sequenceNbr":"1659384255106260994","unitId":"1658367326819016727"},{"bdName":"清单1","closeFlag":true,"kind":"03","parentId":"1659384255106260994","sequenceNbr":"1660579763674288128","unitId":"1658367326819016727"},{"bdName":"定额1","kind":"04","parentId":"1660579763674288128","sequenceNbr":"1660579957178503168","unitId":"1658367326819016727"},{"bdName":"清单2","kind":"03","parentId":"1659384255106260994","sequenceNbr":"1660580315070074880","unitId":"1658367326819016727"},{"bdName":"定额1","kind":"04","parentId":"1660580315070074880","sequenceNbr":"1660581320901922816","unitId":"1658367326819016727"},{"bdName":"定额2","kind":"04","parentId":"1660580315070074880","sequenceNbr":"1660581517463785472","unitId":"1658367326819016727"}],"sequenceNbr":"1658367326819016727"}]}]};
        constructId = "1";
        singleId = "1";
        unitId = "1658367326819016727";
        kind = "01";
        page =1;
        limit =10;
        treeSequenceNbr = "1659384089636773889";
        //endRowSequenceNbr = "1660579763674288128";*/

        // 获取该单位下的所有分部分项
        let upAllFbFxList = await PricingFileFindUtils.getFbFx(constructId, singleId, unitId);

        // 判断是查所有(kind 0) 还是查某个分部数据
        if (BranchProjectLevelConstant.top === kind) {
            // 0 单位工程
            // 分页起始行
            let rowStartIndex = -1;
            // 赋值起始行rowStartIndex
            if (!endRowSequenceNbr) {
                // 为空时默认结束行为-1,即起始行为0，从第0行开始查
                rowStartIndex = 0;
            } else {
                let endRowIndex = upAllFbFxList.findIndex(i => i.sequenceNbr === endRowSequenceNbr);
                if (endRowIndex >= 0) {
                    rowStartIndex = endRowIndex + 1;
                } else {
                    throw new Error("结尾行的sequenceNbr未找到");
                }
            }
            // 将该单位下的数据分页并过滤收起的元素
            let pageData = this.pageAndFilterCloseElement(rowStartIndex, limit, upAllFbFxList);
            // 转换为树结构返回
            return this.convertToParentChildTree(pageData, upAllFbFxList, BranchProjectLevelConstant.top);
        }

        if (!treeSequenceNbr) {
            throw new Error("必传参数treeSequenceNbr为空");
        }

        // 查分部及其下清单定额（需包括自身）
        let itemBillList = [];
        // 查自身
        let topNode = upAllFbFxList.find(i => i.sequenceNbr === treeSequenceNbr); // await this.itemBillProjectDao.findOneBy({sequenceNbr: sequenceNbr});
        itemBillList.push(topNode)
        // 子集
        this.recursionSelectChildren(treeSequenceNbr, itemBillList, upAllFbFxList);

        // 计算分页起始行：endRowIndex+1
        let rowStartIndex = -1;
        // 赋值起始行rowStartIndex
        if (!endRowSequenceNbr) {
            // 为空时默认结束行为-1,即起始行为0，从第0行开始查
            rowStartIndex = 0;
        } else {
            let endRowIndex = itemBillList.findIndex(i => i.sequenceNbr === endRowSequenceNbr);
            if (endRowIndex >= 0) {
                rowStartIndex = endRowIndex + 1;
            } else {
                throw new Error("结尾行的sequenceNbr未找到");
            }
        }

        // 将该单位下的数据分页并过滤收起的元素
        let pageData = this.pageAndFilterCloseElement(rowStartIndex, limit, itemBillList);
        // 转换为树结构返回
        return this.convertToParentChildTree(pageData, upAllFbFxList, kind);
    }

    /**
     * 分页并过滤掉收起的元素
     * @deprecated
     * @param rowStartIndex 起始行
     * @param limit 分页条数
     * @param fbFxList 需要过滤的list
     * @return {*|*[]} pageData
     */
    pageAndFilterCloseElement(rowStartIndex, limit, fbFxList) {
        // 临时分页数据
        let pageData = [];
        //循环判断是否收起，若收起查同级的下个元素
        for (let i = rowStartIndex; i < fbFxList.length; i++) {
            if (limit == pageData.length) {
                //pageData元素够分页条数后跳出
                break;
            }
            // 是否收起
            if (fbFxList[i].closeFlag) {
                // 若该元素收起，将该元素添加到pageData,并将index跳到同级的一下个元素
                pageData.push(fbFxList[i]);
                let siblingNextElementIndex = this.findSiblingNextElementIndex(fbFxList[i], i, fbFxList);
                if (siblingNextElementIndex > 0) {
                    // 有下一个同级元素下标则跳跃
                    i = siblingNextElementIndex;
                    continue;
                } else {
                    // 没有则跳出
                    break;
                }
            }
            pageData.push(fbFxList[i]);
        }
        if (pageData.length === 0) {
            return [];
        }
        return ConvertUtil.deepCopy(pageData);
    }

    /**
     * 查同级的下一个元素下标
     * @deprecated
     * @param currentItemBill
     * @param currentItemBillIndex
     * @param allList
     * @return {number|*}
     */
    findSiblingNextElementIndex(currentItemBill, currentItemBillIndex, allList) {
        currentItemBill.parentId;
        for (let i = currentItemBillIndex + 1; i < allList.length; i++) {
            if (currentItemBill.parentId === allList[i].parentId && currentItemBill.kind === allList[i].kind) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 转换为父子结构的树
     * @deprecated
     * @param convertItemBillList 需要转换的分部分项list
     * @param allItemBillList 单位下所有分部分项list
     * @param treeTopKind 树顶层kind
     * @return {*[]}
     */
    convertToParentChildTree(convertItemBillList, allItemBillList, treeTopKind) {
        if (null == convertItemBillList || convertItemBillList.length === 0) {
            return [];
        }
        // 取list中kind最小的kind,最小kind即最大的类型最大的父级，比如0单位01分部
        let minKind = convertItemBillList[0];
        convertItemBillList.forEach(i => minKind = i.kind < minKind ? i.kind : minKind)
        let topNodeKind = treeTopKind;
        if (minKind !== treeTopKind) {
            // 找上一层的父级作为顶层节点
            topNodeKind = "0" + (minKind - 1);
        }
        // 如果第一个元素kind大于顶层kind，比如03清单大于02子分部，那需找第一个元素的父级直到该父级和顶层kind一致。
        let firstElement = convertItemBillList[0];
        let firstKind = firstElement.kind;
        while (firstKind > topNodeKind) {
            // 取firstElement的父级并重新赋值firstKind
            let parent = allItemBillList.find(i => i.sequenceNbr === firstElement.parentId);
            firstElement = parent;
            firstKind = parent.kind;
        }
        // 深拷贝作为Tree的顶层节点
        let topElement = ConvertUtil.deepCopy(firstElement);
        // 循环转换的list，将每个元素都加到树中。
        convertItemBillList.forEach(i => {
            this.addParentChildTreeNode(i, topElement, allItemBillList);
        });
        return topElement;
    }


    /**
     * 添加父子树的节点
     * @deprecated
     * @param node 节点元素
     * @param tree 树
     * @param allList 全量分部分项list
     */
    addParentChildTreeNode(node, tree, allList) {
        if (node.sequenceNbr === tree.sequenceNbr) {
            return;
        }
        // 找父节点
        let parentNode = this.recursionSelectParentChildTree(node.parentId, tree);
        // 是否找到父节点
        if (parentNode) {
            // 如果树里面有父节点则将node添加到父节点
            if (parentNode.children) {
                parentNode.children.push(node);
            } else {
                parentNode.children = [];
                parentNode.children[0] = node;
            }
        } else {
            // 树中没有父节点，将父节点添加到树中并将node添加到父节点下
            let parent = allList.find(i => i.sequenceNbr === node.parentId);
            parent.children = [];
            parent.children[0] = node;
            this.addParentChildTreeNode(parent, tree, allList);
        }
    }


    /**
     * 递归查父子树的节点
     * @deprecated
     * @param sequenceNbr seq
     * @param tree 树
     */
    recursionSelectParentChildTree(sequenceNbr, tree) {
        if (sequenceNbr === tree.sequenceNbr) {
            return tree;
        } else {
            if (tree.children) {
                for (let i = 0; i < tree.children.length; i++) {
                    let treeNode = this.recursionSelectParentChildTree(sequenceNbr, tree.children[i]);
                    if (treeNode) {
                        // 如果找到则return
                        return treeNode;
                    }
                }
            }
        }
    }

    /**
     * 递归查下级并追加到list
     * @deprecated
     * @param parentId 父id
     * @param itemBillList 要追加的list
     * @param upAllItemBillList 单位下的全量分部分项
     */
    recursionSelectChildren(parentId, itemBillList, upAllItemBillList) {

        let count = upAllItemBillList.some(i => i.parentId === parentId); //this.itemBillProjectDao.countBy({parentId: parentId});
        if (count) {
            // 如果有子集
            let subList = upAllItemBillList.filter(i => i.parentId === parentId); //this.itemBillProjectDao.findBy({parentId: parentId});
            subList.forEach(item => {
                itemBillList.push(item);
                this.recursionSelectChildren(item.sequenceNbr, itemBillList, upAllItemBillList)
            });
        }
    }

    /**
     * 展开或折叠
     * @deprecated
     * @param constructId
     * @param singleId
     * @param unitId
     * @param sequenceNbr 被展开或折叠的数据行sequenceNbr
     */
    unfoldOrFold(constructId, singleId, unitId, sequenceNbr) {
        /*// 自测数据
        global.constructProject = [];
        //global.constructProject[0] = {"biddingType":"0","constructName":"ysb测试项目","fileCode":"1660814140593278976","path":"C:\\Users\\<USER>\\.xilidata\\ysb测试项目.ysf","singleProjects":[{"sequenceNbr":"1","unitProjects":[{"itemBillProjects":[{"bdName":"单位工程","kind":"0","sequenceNbr":"1659383061407444994","unitId":"1658367326819016727"},{"bdName":"分部","kind":"01","parentId":"1659383061407444994","sequenceNbr":"1659384089636773889","unitId":"1658367326819016727"},{"bdName":"子分部","kind":"02","parentId":"1659384089636773889","sequenceNbr":"1659384255106260994","unitId":"1658367326819016727"},{"bdName":"清单1","closeFlag":true,"kind":"03","parentId":"1659384255106260994","sequenceNbr":"1660579763674288128","unitId":"1658367326819016727"},{"bdName":"定额1","kind":"04","parentId":"1660579763674288128","sequenceNbr":"1660579957178503168","unitId":"1658367326819016727"},{"bdName":"清单2","kind":"03","parentId":"1659384255106260994","sequenceNbr":"1660580315070074880","unitId":"1658367326819016727"},{"bdName":"定额1","kind":"04","parentId":"1660580315070074880","sequenceNbr":"1660581320901922816","unitId":"1658367326819016727"},{"bdName":"定额2","kind":"04","parentId":"1660580315070074880","sequenceNbr":"1660581517463785472","unitId":"1658367326819016727"}],"sequenceNbr":"1658367326819016727"}]}]};
        global.constructProject[1] = {"proJectData":{"biddingType":"0","constructName":"ysb测试项目","fileCode":"1660814140593278976","path":"C:\\Users\\<USER>\\.xilidata\\ysb测试项目.ysf","singleProjects":[{"sequenceNbr":"1","unitProjects":[{"itemBillProjects":[{"bdName":"单位工程","kind":"0","sequenceNbr":"1659383061407444994","unitId":"1658367326819016727"},{"bdName":"分部","kind":"01","parentId":"1659383061407444994","sequenceNbr":"1659384089636773889","unitId":"1658367326819016727"},{"bdName":"子分部","kind":"02","parentId":"1659384089636773889","sequenceNbr":"1659384255106260994","unitId":"1658367326819016727"},{"bdName":"清单1","kind":"03","parentId":"1659384255106260994","sequenceNbr":"1660579763674288128","unitId":"1658367326819016727"},{"bdName":"定额1","kind":"04","parentId":"1660579763674288128","sequenceNbr":"1660579957178503168","unitId":"1658367326819016727"},{"bdName":"清单2","kind":"03","parentId":"1659384255106260994","sequenceNbr":"1660580315070074880","unitId":"1658367326819016727"},{"bdName":"定额1","kind":"04","parentId":"1660580315070074880","sequenceNbr":"1660581320901922816","unitId":"1658367326819016727"},{"bdName":"定额2","kind":"04","parentId":"1660580315070074880","sequenceNbr":"1660581517463785472","unitId":"1658367326819016727"}],"sequenceNbr":"1658367326819016727"}]}]}};
        constructId = "1";
        singleId = "1";
        unitId = "1658367326819016727";
        sequenceNbr = "1660579763674288128";*/

        let fbFxList = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        if (fbFxList) {
            let itemBillProject = fbFxList.find(i => i.sequenceNbr === sequenceNbr);
            if (itemBillProject) {
                // 将折叠标识取反,修改内存中物理地址的属性值
                itemBillProject.closeFlag = !Boolean(itemBillProject.closeFlag);
                return true;
            }
        }
        return false;
    }

    /**
     * 获取分部分项单条数据
     * @param sequenceNbr sequenceNbr
     * @param constructId
     * @param singleId
     * @param unitId
     * @return {ItemBillProject|null}
     */
    getOneBySequenceNbr(sequenceNbr, constructId, singleId, unitId) {
        let fbFxList = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        if (ObjectUtils.isEmpty(fbFxList)) {
            return null;
        }
        return fbFxList.find(i => i.sequenceNbr === sequenceNbr);
    }

    /**
     * 获取子集
     * @param parentSequenceNbr 父id
     * @param constructId
     * @param singleId
     * @param unitId
     * @return {ItemBillProject[]}
     */
    getChildrenList(parentSequenceNbr, constructId, singleId, unitId) {
        let fbFxList = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        if (ObjectUtils.isEmpty(fbFxList)) {
            return [];
        }
        return fbFxList.filter(i => i.parentId === parentSequenceNbr);
    }
}

ItemBillProjectService.toString = () => '[class ItemBillProjectService]';
module.exports = ItemBillProjectService;




