const CommonFix = require("./common");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");

/**
 * 全局设置历史版本处理
 */
class GlobalConfigurationFix   extends CommonFix{
    constructor(ctx) {
        super(["1.0.46"],ctx.tarVersion );
        this.ctx = ctx;
    }


    async '1.0.46'(){
        let {project,service} = this.ctx;
        let unitProjects = PricingFileFindUtils.getUnitListByConstructObj(project);
        // try {
            await service.globalConfigurationImplementationService.updateGlobalConfiguration(unitProjects,project.sequenceNbr)
        // }catch (e){
        //     console.log("修补程序出错",e.message)
        // }


    }
}
module.exports = GlobalConfigurationFix