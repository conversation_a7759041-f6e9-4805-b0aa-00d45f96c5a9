<!--
 * @Descripttion: 报表左侧
 * @Author: sunchen
 * @Date: 2023-05-23 14:43:34
 * @LastEditors: renmingming
 * @LastEditTime: 2024-07-12 09:33:47
-->
<template>
  <div class="aside-wrap">
    <div class="list-item" v-for="(v, k) in treeList" :key="k">
      <div class="mode-title" @click="changeItem(k)">
        <down-outlined
          class="icon-down"
          :class="{ 'rotate-icon': useKey !== k }"
        />
        <icon-font class="icon-font" type="icon-xiangmu"></icon-font>
        <span class="label">{{ v.desc }}</span>
      </div>
      <a-tree
        v-if="useKey === k"
        v-model:expandedKeys="expandedKeys"
        treeDefaultExpandAll="true"
        v-model:selectedKeys="selectedKeys"
        :tree-data="v.baoBiaoList"
        :fieldNames="{
          children: 'children',
          title: 'headLine',
          key: 'headLine',
        }"
        class="report-forms-tree"
        show-icon
        @select="clickTree"
      >
        <template #switcherIcon="{ switcherCls }"
          ><down-outlined class="sub-icon-down" :class="switcherCls"
        /></template>

        <template #title="{ headLine }">
          <a-tooltip placement="rightTop">
            <template #title>{{ headLine }}</template>
            <span class="ellipsis"> {{ headLine }}</span>
          </a-tooltip>
        </template>

        <template #icon="{ children }">
          <template v-if="children && children.length">
            <icon-font class="icon-font" type="icon-baobiao"></icon-font>
          </template>
        </template>
      </a-tree>
    </div>
  </div>
</template>

<script setup>
import { DownOutlined } from '@ant-design/icons-vue';
import csProject from '@/api/jiesuanApi.js';
import XEUtils from 'xe-utils';
import { markRaw, ref, watchEffect, watch, toRaw } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { useRoute } from 'vue-router';
import { globalData, removeLocalStorage, setLocalStorage } from './reportFrom';

const emit = defineEmits(['onPreview']);

const route = useRoute();
const store = projectDetailStore();
let expandedKeys = ref([]);
let selectedKeys = ref([]);
const treeList = ref([]);
const useKey = ref(0);
const itemLevelList = markRaw(['project', 'single', 'unit']);

// 获取左侧菜单数据
const getTreeList = () => {
  console.log(
    '🚀 ~ csProject.showExportHeadLine ~ globalData.treeParams:',
    globalData.treeParams
  );
  csProject
    .jieSuanShowExportHeadLineColl(toRaw(globalData.treeParams))
    .then(res => {
      console.log('🚀 ~ csProject.showExportHeadLine ~ res:', res);
      removeLocalStorage();
      treeList.value = res.result;
      setSheetData();
    });
};

// 切换，更新最右侧的表单
const setSheetData = async () => {
  const list = treeList.value[useKey.value];
  if (list) {
    const TreeArrayData = XEUtils.toTreeArray(list.baoBiaoList).filter(i => {
      return i.headLine === selectedKeys.value[0];
    });

    if (TreeArrayData && TreeArrayData[0]) {
      const preview = toRaw(TreeArrayData[0]);
      const data = await getShowSheetStyle(preview);
      emit('onPreview', !!data);
      data ? setLocalStorage(data) : removeLocalStorage();
    } else {
      emit('onPreview', false);
    }
  }
};

const init = XEUtils.debounce(() => {
  const { levelType } = store.currentTreeInfo;
  let params = {
    itemLevel: 'project',
    constructObj: {
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId,
      unitId: levelType === 3 ? store.currentTreeInfo?.id : '',
    },
  };
  params.itemLevel = itemLevelList[levelType - 1];

  globalData.treeParams = params;
  getTreeList();
}, 300);

watch(
  () => store.currentTreeInfo,
  () => {
    init();
  },
  {
    immediate: true,
  }
);

watchEffect(() => {
  const list = {
    ZB: 0,
    TB: 1,
    DW: 2,
  };
  useKey.value = list[store.projectType] || 0;
});

const changeItem = k => {
  if (k === useKey.value) {
    useKey.value = -1;
    return;
  } else {
    useKey.value = k;
    selectedKeys.value = [];
    expandedKeys.value = [];
  }
};

const clickTree = async (keys, { node }) => {
  const nodeKey = node.dataRef.headLine;
  if (expandedKeys.value.includes(nodeKey)) {
    expandedKeys.value = [nodeKey];
    selectedKeys.value = [nodeKey];
  } else {
    if (!node.expanded) {
      expandedKeys.value.push(nodeKey);
    } else {
      expandedKeys.value = expandedKeys.value.filter(key => {
        return key !== nodeKey;
      });
    }
  }

  if (!node?.children) {
    const data = await getShowSheetStyle(node.dataRef);
    emit('onPreview', !!data);
    data ? setLocalStorage(data) : removeLocalStorage();
  }
};

// 获取表单数据
const getShowSheetStyle = sheetInfo => {
  return new Promise((resolve, reject) => {
    let postData = {
      itemLevel: globalData.treeParams.itemLevel,
      lanMuName: treeList.value[useKey.value].desc,
      sheetName: sheetInfo.headLine,
      constructObj: toRaw(globalData.treeParams.constructObj),
    };

    csProject.jieSuanShowSheetStyle(postData).then(res => {
      console.log(postData, res, 'postData');
      if (res.code == 200) {
        resolve(res.result);
      }
    });
  });
};
</script>
<style lang="scss" scoped>
.list-item {
  margin-bottom: 8px;
}

.icon-down {
  color: rgba(51, 51, 51, 1);
  font-size: 12px;
  transition: all 0.2s;
}

.rotate-icon {
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
}
.sub-icon-down {
  color: rgba(178, 177, 177, 1);
  font-size: 13px;
}
.mode-title {
  cursor: pointer;
  display: flex;
  align-items: center;
  user-select: none;
  .icon-font {
    margin: 0 4px 0 6px;
    font-size: 15px;
  }
  .label {
    font-size: 14px;
    font-weight: 400;
    color: #131414;
  }
}
.aside-wrap {
  width: 253px;
  background-color: #f8fbff;
  overflow: hidden;
  padding: 8px 4px;
  &:hover {
    overflow: auto;
  }
  :deep(.report-forms-tree) {
    width: 100%;
    position: relative;
    background-color: #f8fbff;
    .ant-tree-node-selected {
      background-color: #dae7f4;
    }
    .ant-tree-treenode {
      width: 100%;
      overflow: hidden;
    }
    .ant-tree-node-content-wrapper {
      width: 100%;
      display: flex;
      overflow: hidden;
    }
    .ant-tree-title {
      width: 100%;
      display: inline-block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
