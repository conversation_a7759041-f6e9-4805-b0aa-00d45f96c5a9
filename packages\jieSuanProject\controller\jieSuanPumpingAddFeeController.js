const PumpingAddFeeController = require("../../../electron/controller/pumpingAddFeeController");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const JieSuanPumpingAddFeeService = require("../service/jieSuanPumpingAddFeeService");

/**
 * 泵送增加费
 */
class JieSuanPumpingAddFeeController extends PumpingAddFeeController {


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
        this.jieSuanPumpingAddFeeService =
            ObjectUtils.isNotEmpty(this.service.jieSuanPumpingAddFeeService)?this.service.jieSuanPumpingAddFeeService :new JieSuanPumpingAddFeeService(ctx);
    }

    /**
     * 计算泵送增加费
     */
    async calculationPumpingAddFee(args) {
        return ResponseData.success(await this.jieSuanPumpingAddFeeService.calculationPumpingAddFee(args));
    }

}

PumpingAddFeeController.toString = () => '[class JieSuanPumpingAddFeeController]';
module.exports = JieSuanPumpingAddFeeController;