<!--
 * @Author: wangru summaryExpense 费用汇总
 * @Date: 2023-05-23 15:53:42
 * @LastEditors: wangru
 * @LastEditTime: 2024-08-13 14:59:37
-->
<template>
  <div class="table-content table-content-flex-column">
    <split
      horizontal
      ratio="2/1"
      :horizontalBottom="35"
      style="height: 100%"
      mode="vertical"
      :onlyPart="
        Number(projectStore.deStandardReleaseYear) === 12 && Number(projectStore.taxMade) === 1
          ? 'all'
          : 'Top'
      "
    >
      <template #one>
        <summary-header
          class="head"
          @clickAdd="clickAdd"
        ></summary-header>
        <content-up
          class="content"
          ref="contentUp"
          :isCharu="isCharu"
          @getMoveInfo="getMoveInfo"
        ></content-up>
      </template>
      <template #two>
        <jxse-modal @getUpList="getUpList"></jxse-modal>
      </template>
    </split>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import ContentUp from './ContentUp.vue';
import ContentDown from '@/views/projectDetail/customize/summaryExpense/ContentDown.vue';
import JxseModal from '@/views/projectDetail/customize/summaryExpense/JxseModal.vue';
import SummaryHeader from './SummaryHeader.vue';
import { projectDetailStore } from '@/store/projectDetail';
const projectStore = projectDetailStore();
const emits = defineEmits(['getMoveInfo']);

let isCharu = ref(false);
const clickAdd = bol => {
  if (bol) {
    isCharu.value = !isCharu.value;
  }
};

const getMoveInfo = value => {
  emits('getMoveInfo', value);
};

onMounted(() => {
  console.log(
    'projectStore.deType',
    projectStore.deType,
    projectStore.taxMade,
    Number(projectStore.deType) === 12 && Number(projectStore.taxMade) === 1
  );
});
const contentUp = ref(null);
const getUpList = () => {
  contentUp.value.getTableData();
};

defineExpose({
  getTableData: getUpList,
});

projectStore.summaryExpenseGetList = getUpList;

// 费用汇总
</script>
<style lang="scss" scoped>
.table-content {
  height: 100%;
  .head {
    display: flex;
    align-items: center;
  }
  .content {
    height: calc(100%);
  }
}
</style>
