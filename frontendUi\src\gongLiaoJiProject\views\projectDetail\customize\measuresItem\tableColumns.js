/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-07-03 11:24:30
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-05-19 15:49:04
 */
import { ref } from 'vue';
import deMapFun from '../deMap';
import { useSubItem } from '@gongLiaoJi/hooks/useSubItemStable.js';
import { Item } from 'ant-design-vue/lib/menu';
import { projectDetailStore } from '@/store/projectDetail';
const projectStore = projectDetailStore();

const getTableColumns = emits => {
  let { editClosedEvent } = useSubItem({
    emits,
    pageType: 'csxm',
  });
  const tableColumns = ref([
    // {
    //   title: '序号',
    //   field: 'dispNo',
    //   dataIndex: 'dispNo',
    //   width: 50,
    //   autoHeight: true,
    //   fixed: 'left',
    //   align: 'center',
    // },
    {
      title: '编码',
      field: 'deCode',
      dataIndex: 'deCode',
      align: 'left',
      headerAlign: 'center',
      autoHeight: true,
      resizable: true,
      fixed: 'left',
      edit: true,
      width: 140,
      editableTrigger: 'click',
      editable: ({ record: row }) => {
        if (row?.awfType !== 2) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '类别',
      field: 'type',
      dataIndex: 'type',
      width: 65,
      align: 'center',
      edit: true,
      fixed: 'left',
      autoHeight: true,
      editableTrigger: 'click',
      editable: ({ record: row }) => {
        if (
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row?.kind) &&
          (row?.kind !== '07' || row.isCostDe === 4) &&
          (row.deResourceKind === 2 ||
            row.deResourceKind === 5 ||
            row.deResourceKind === 4) &&
          row?.awfType !== 2 &&
          !(row.type == '06' && row.isFyrcj == 0) &&
          Number(row.isDataTaxRate) != 0
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '名称',
      field: 'deName',
      dataIndex: 'deName',
      resizable: true,
      autoHeight: true,
      width: 260,
      align: 'left',
      fixed: 'left',
      edit: true,
      editable: ({ record: row }) => {
        if (
          row.isTempRemove !== 1 &&
          !['00', '07'].includes(row.kind) &&
          row?.awfType !== 2 &&
          !(row.type == '06' && row.isFyrcj == 0) &&
          row.type != -1 &&
          ![11].includes(row.isCostDe)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '单位',
      field: 'unit',
      dataIndex: 'unit',
      width: 70,
      edit: true,
      autoHeight: true,
      editable: ({ record: row }) => {
        if (
          row.isTempRemove !== 1 &&
          row?.awfType !== 2 &&
          (['04', '05', '06', '08', '09'].includes(row.kind) ||
            ([3].includes(row.isCostDe) && ['10'].includes(row.kind))) &&
          !(row.type == '06' && row.isFyrcj == 0) &&
          ![2, 5, 11].includes(row.isCostDe)
        ) {
          if (row.kind === '05') {
            if (
              row.quantity != 0 &&
              !deMapFun.isTz(row.deCode) &&
              !deMapFun.isJxxs(row.deCode) &&
              deMapFun.isPartEdit(row.deCode)
            ) {
              return 'cellEditorSlot';
            } else {
              return false;
            }
          } else {
            return 'cellEditorSlot';
          }
        } else {
          return false;
        }
      },
    },
    {
      title: '调整系数',
      field: 'adjustmentCoefficient',
      slot: true,
      ellipsis: true,
      width: 90,
      editRender: { autofocus: '.vxe-input--inner' },
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row?.awfType !== 2 &&
          (['01', '02'].includes(row.kind) ||
            (row.kind == '03' && row.pricingMethod == 2))
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '工程量表达式',
      field: 'quantityExpression',
      width: 110,
      slot: true,
      ellipsis: true,
      // tooltip: {
      //   placement: 'topLeft',
      //   title: ({ value }) => {
      //     return `测试${value}`;
      //   },
      //   allowEnter: false,
      //   showArrow: false,
      // },
      editRender: { autofocus: '.vxe-input--inner' },
      editable: ({ record: row }) => {
        if (
          ![11, 5].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          (['04', '06', '08', '09'].includes(row.kind) ||
            (['10'].includes(row.kind) && [3].includes(row.isCostDe))) &&
          row.isCostDe !== 1 &&
          row?.awfType !== 2 &&
          !(row.type == '06' && row.isFyrcj == 0)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '工程量',
      field: 'originalQuantity',
      resizable: true,
      dataIndex: 'originalQuantity',
      edit: true,
      ellipsis: true,
      width: 80,
      editable: ({ record: row }) => {
        if (
          ![11, 5].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          row?.awfType !== 2 &&
          (['04', '05', '06', '08', '09'].includes(row.kind) ||
            (['10'].includes(row.kind) && [3].includes(row.isCostDe))) &&
          !(row.type == '06' && row.isFyrcj == 0)
        ) {
          if (row.kind === '05') {
            if (
              deMapFun.isDeHasQuantity(row) &&
              !deMapFun.isTz(row.deCode) &&
              !deMapFun.isJxxs(row.deCode) &&
              deMapFun.isPartEdit(row.deCode)
            ) {
              return 'cellEditorSlot';
            } else {
              return false;
            }
          } else {
            return 'cellEditorSlot';
          }
        } else {
          return false;
        }
      },
    },
    {
      title: '组价方式',
      field: 'pricingMethod',
      resizable: true,
      dataIndex: 'pricingMethod',
      edit: true,
      width: 90,
      ellipsis: true,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row?.awfType !== 2 &&
          ['03'].includes(row.kind)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '计算基数',
      field: 'calculateBase',
      dataIndex: 'calculateBase',
      edit: true,
      width: 90,
      ellipsis: true,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row?.awfType !== 2 &&
          row.kind == '03' &&
          row.pricingMethod == 1
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '基数说明',
      field: 'baseDescription',
      autoHeight: true,
      ellipsis: true,
      width:100,
    },
    {
      title: '费率（%）',
      field: 'rate',
      dataIndex: 'rate',
      width: 100,
      ellipsis: true,
      edit: true,
      resizable: true,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row?.awfType !== 2 &&
          row.kind == '03' &&
          row.pricingMethod == 1
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
  ]);
  let deArr = [
    {
      title: '单价',
      field: 'baseJournalPrice',
      dataIndex: 'baseJournalPrice',
      resizable: true,
      edit: true,
      ellipsis: true,
      width: 70,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row.ifLockStandardPrice != 1 &&
          row?.awfType !== 2 &&
          !['01', '02', '03'].includes(row.kind) &&
          ['04', '05', '06', '09'].includes(row.kind) &&
          row.isCostDe !== 2 &&
          row.isDataTaxRate != 0
        ) {
          if (
            (['05', '06'].includes(row.kind) && row.isFyrcj != 0) ||
            !['05', '06'].includes(row.kind)
          ) {
            return 'cellEditorSlot';
          } else {
            return false;
          }
        } else {
          return false;
        }
      },
    },
    {
      title: '合价',
      field: 'baseJournalTotalNumber',
      dataIndex: 'baseJournalTotalNumber',
      width: 70,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '措施类别',
      field: 'itemCategory',
      dataIndex: 'itemCategory',
      resizable: true,
      edit: true,
      ellipsis: true,
      width: 90,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row?.awfType !== 2 &&
          row.kind == '03'
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '取费专业',
      field: 'costMajorName',
      dataIndex: 'costMajorName',
      resizable: true,
      edit: true,
      ellipsis: true,
      width: 90,
      editable: 'cellEditorSlot',
      editable: ({ record: row }) => {
        // console.log(record)
        if (
          (row.isTempRemove !== 1 &&
            row?.awfType !== 2 &&
            row.type != '-1' &&
            ((row.type == '04' && row.deCode) ||
              (row.type == '03' && row.pricingMethod == 1) ||
              ['04', '06', '08'].includes(row.type))) ||
          (![0, 1].includes(row.isCostDe) && row.isCostDe !== undefined)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    // {
    //   title: '超高过滤类别',
    //   field: 'fitHighType',
    //   ellipsis: true,
    // },
    // {
    //   title: '檐高类别（修缮高度）',
    //   field: 'eaveHeightType',
    //   ellipsis: true,
    // },
    {
      title: '施工组织措施类别',
      field: 'measureType',
      ellipsis: true,
      edit: true,
      resizable: true,
      width: 130,
      editable: ({ record: row }) => {
        if (
          (row?.awfType !== 2 &&
            row.kind == '04' &&
            ([0, 3, 4, 6, 11, 12, 13, 14].includes(row.isCostDe) ||
              row.isCostDe == undefined)) ||
          ['06', '08', '09'].includes(row.kind)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    // {
    //   title: '垂直运输类别',
    //   field: 'verticalTransType',
    //   ellipsis: true,
    // },
    {
      title: '人工费单价',
      field: 'RDSum',
      visible: false,
      ellipsis: true,
      width: 100,
      edit: true,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          row?.awfType !== 2 &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '人工费合价',
      field: 'rdTotalSum',
      ellipsis: true,
      width: 100,
      visible: false,
    },
    {
      title: '材料费单价',
      field: 'CDSum',
      visible: false,
      ellipsis: true,
      width: 100,
      edit: true,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          row?.awfType !== 2 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '材料费合价',
      field: 'cdTotalSum',
      ellipsis: true,
      width: 100,
      visible: false,
    },
    {
      title: '机械费单价',
      field: 'JDSum',
      ellipsis: true,
      width: 100,
      visible: false,
      edit: true,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          row?.awfType !== 2 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '机械费合价',
      field: 'jdTotalSum',
      ellipsis: true,
      width: 100,
      visible: false,
    },
    {
      title: '主材费单价',
      field: 'ZDSum',
      visible: false,
      ellipsis: true,
      width: 100,
      edit: true,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          row?.awfType !== 2 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '主材费合价',
      field: 'zdTotalSum',
      visible: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: '设备费单价',
      field: 'SDSum',
      visible: false,
      ellipsis: true,
      width: 100,
      edit: true,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          row?.awfType !== 2 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '设备费合价',
      field: 'sdTotalSum',
      visible: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: '备注',
      field: 'remark',
      dataIndex: 'remark',
      resizable: true,
      ellipsis: true,
      edit: true,
      editable: ({ record: row }) => {
        return (
          row?.awfType !== 2 &&
          row.isTempRemove !== 1 &&
          !['05'].includes(row.kind)
        );
      },
      valueParser: ({ newValue, oldValue, record: row, column }) => {
        if (newValue === oldValue) return newValue;
        editClosedEvent({ row, column }, newValue, oldValue);
        return newValue;
      },
    },
  ];
  let scjArr = [
    {
      title: '单价',
      field: 'price',
      dataIndex: 'price',
      resizable: true,
      edit: true,
      ellipsis: true,
      width: 70,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row.ifLockStandardPrice != 1 &&
          row?.awfType !== 2 &&
          !['01', '02', '03'].includes(row.kind) &&
          ['04', '05', '06', '09'].includes(row.kind) &&
          row.isCostDe !== 2 &&
          row.isDataTaxRate != 0
        ) {
          if (
            (['05', '06'].includes(row.kind) && row.isFyrcj != 0) ||
            !['05', '06'].includes(row.kind)
          ) {
            return 'cellEditorSlot';
          } else {
            return false;
          }
        } else {
          return false;
        }
      },
    },
    {
      title: '合价',
      field: 'totalNumber',
      dataIndex: 'totalNumber',
      width: 70,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '措施类别',
      field: 'itemCategory',
      dataIndex: 'itemCategory',
      resizable: true,
      edit: true,
      ellipsis: true,
      width: 90,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row?.awfType !== 2 &&
          row.kind == '03'
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '取费专业',
      field: 'costMajorName',
      dataIndex: 'costMajorName',
      resizable: true,
      edit: true,
      ellipsis: true,
      width: 90,
      editable: 'cellEditorSlot',
      editable: ({ record: row }) => {
        // console.log(record)
        if (
          row.isTempRemove !== 1 &&
          row?.awfType !== 2 &&
          row.type != '-1' &&
          ((row.type == '03' && row.pricingMethod == 1) ||
            (row.deCode && ['04', '08'].includes(row.type)))
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    // {
    //   title: '超高过滤类别',
    //   field: 'fitHighType',
    //   ellipsis: true,
    // },
    // {
    //   title: '檐高类别（修缮高度）',
    //   field: 'eaveHeightType',
    //   ellipsis: true,
    // },
    {
      title: '施工组织措施类别',
      field: 'measureType',
      ellipsis: true,
      edit: true,
      resizable: true,
      width: 130,
      editable: ({ record: row }) => {
        if (
          (row?.awfType !== 2 &&
            row.kind == '04' &&
            ([0, 3, 4, 6, 11, 12, 13, 14].includes(row.isCostDe) ||
              row.isCostDe == undefined)) ||
          ['06', '08', '09'].includes(row.kind)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    // {
    //   title: '垂直运输类别',
    //   field: 'verticalTransType',
    //   ellipsis: true,
    // },
    {
      title: '人工费单价',
      field: 'RSum',
      visible: false,
      ellipsis: true,
      edit: true,
      width: 100,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          row?.awfType !== 2 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '人工费合价',
      field: 'rTotalSum',
      ellipsis: true,
      visible: false,
      width: 100,
    },
    {
      title: '材料费单价',
      field: 'CSum',
      visible: false,
      ellipsis: true,
      edit: true,
      width: 100,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          row?.awfType !== 2 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '材料费合价',
      field: 'cTotalSum',
      ellipsis: true,
      visible: false,
      width: 100,
    },
    {
      title: '机械费单价',
      field: 'JSum',
      ellipsis: true,
      visible: false,
      edit: true,
      width: 100,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          row?.awfType !== 2 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '机械费合价',
      field: 'jTotalSum',
      ellipsis: true,
      visible: false,
      width: 100,
    },
    {
      title: '主材费单价',
      field: 'ZSum',
      visible: false,
      ellipsis: true,
      edit: true,
      width: 100,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          row?.awfType !== 2 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '主材费合价',
      field: 'zTotalSum',
      visible: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: '设备费单价',
      field: 'SSum',
      visible: false,
      ellipsis: true,
      edit: true,
      width: 100,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          row?.awfType !== 2 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '设备费合价',
      field: 'sTotalSum',
      visible: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: '备注',
      field: 'remark',
      dataIndex: 'remark',
      resizable: true,
      ellipsis: true,
      edit: true,
      editable: ({ record: row }) => {
        return (
          row?.awfType !== 2 &&
          row.isTempRemove !== 1 &&
          !['05'].includes(row.kind)
        );
      },
      valueParser: ({ newValue, oldValue, record: row, column }) => {
        if (newValue === oldValue) return newValue;
        editClosedEvent({ row, column }, newValue, oldValue);
        return newValue;
      },
    },
  ];
  // 如果是按市场价组价
  if (projectStore.setOption.isScj) {
    tableColumns.value = [...tableColumns.value, ...scjArr];
  } else {
    tableColumns.value = [...tableColumns.value, ...deArr];
  }
  return tableColumns.value;
};
export default getTableColumns;
