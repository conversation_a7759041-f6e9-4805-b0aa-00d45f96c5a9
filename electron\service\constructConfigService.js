'use strict';

const {MathFormulaUtil} = require("../utils/MathFormulaUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const { Service,Log } = require('../../core');
const {SingleProject} = require("../model/SingleProject");
const {Snowflake} = require("../utils/Snowflake");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");

/**
 * 示例服务
 * @class
 */
class ConstructConfigService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /***
     * 获取项目配置信息
     * @param unitId
     * @return {Promise<void>}
     */
    getConstructConfigByConstructId(arg) {
        let constructId = arg.constructId;
        let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
        let result = {
            gfId: projectObj.gfId,
            awfId: projectObj.awfId,
            rgfId: projectObj.rgfId,
            qdStandardId:projectObj.qdStandardId,
            deStandardId:projectObj.deStandardId,
            areaCode:projectObj.ssCity,
            constructId:projectObj.sequenceNbr,
            taxMode:projectObj.projectTaxCalculation.taxCalculationMethod,
            deStandardReleaseYear:projectObj.deStandardReleaseYear
        }
        return result;

    }

    /**
     * 获取工程项目 计算单位
     *
     a)自然单位：用户选择为自然单位后，在计算定额工程量时，不考虑定
     额单位前系数，其工程量值=工程量表达式计算结果；
     b)定额单位：用户选择为定额单位后，在计算定额工程量时，需要考虑
     定额单位前系数，其工程量值=工程量表达式计算结果/定额单位前系
     数；
     * @param constructId
     */
    async getConstructConfigGcldwIsDeUnit(constructId){
        let gcldw = this.service.globalConfigurationService.getProjectConfig(constructId).budget.jiSuan.gcldw;

        if(gcldw == "2"){
            //定额单位
            return true;
        }else {
            //自然单位
            return false;
        }
    }


    /**
     * 切换 定额单位 自然单位 处理
     * @param constructId
     * @returns {Promise<void>}
     */
    async changeConfigGcldw(constructId,gcldw){
        //let gcldw = this.service.globalConfigurationService.getProjectConfig(constructId).budget.jiSuan.gcldw;
        let isDeUnit ;
        if(gcldw == "2"){
            //定额单位
            isDeUnit =  true;
        }else {
            //自然单位
            isDeUnit =  false;
        }

        let unitList = PricingFileFindUtils.getUnitList(constructId);
        if (ObjectUtils.isEmpty(unitList)){
            return ;
        }

        for (let unit of unitList) {
            if (!ObjectUtils.isEmpty(unit.itemBillProjects)){
                let itemBillProjects = unit.itemBillProjects;
                let deList = itemBillProjects.filter(i=>i.kind == "04");
                if (!ObjectUtils.isEmpty(deList)){
                    for (let de of deList) {
                      let unitNum =  await this.getUnitNum(de);
                      if (unitNum != 1){
                          if (isDeUnit){
                              //定额单位
                              de.quantityExpression = MathFormulaUtil.processMultiply(de.quantityExpression,unitNum);
                          }else {
                              //自然单位
                              de.quantityExpression = MathFormulaUtil.processDivide(de.quantityExpression,unitNum);
                          }

                      }
                    }
                }
            }


            if (!ObjectUtils.isEmpty(unit.measureProjectTables)){
                let measureProjectTables = unit.measureProjectTables;
                let deList = measureProjectTables.filter(i=>i.kind == "04");
                if (!ObjectUtils.isEmpty(deList)){
                    for (let de of deList) {
                        let unitNum =  await this.getUnitNum(de);
                        if (unitNum != 1){
                            if (isDeUnit){
                                //定额单位
                                de.quantityExpression = MathFormulaUtil.processMultiply(de.quantityExpression,unitNum);
                            }else {
                                //自然单位
                                de.quantityExpression = MathFormulaUtil.processDivide(de.quantityExpression,unitNum);
                            }

                        }
                    }
                }
            }
        }

    }


    async getUnitNum(currentUpdateLine) {
        let unit = Number.parseFloat(currentUpdateLine.unit);
        if (Number.isNaN(unit) || unit == 0) {
            unit = 1;
        }
        return unit;
    }



}

ConstructConfigService.toString = () => '[class ConstructConfigService]';
module.exports = ConstructConfigService;
