const JieSuanConstantUtil = require("../enum/JieSuanConstantUtil");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {RcjDifferenceInfo} = require("../model/RcjDifferenceInfo");
const {JieSuanDifferencePrice} = require("../model/JieSuanDifferencePrice");
const DePropertyTypeConstant = require("../../../electron/enum/DePropertyTypeConstant");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const JieSuanFeeSetEnum = require("../enum/JieSuanMethodEnum");
const TaxCalculationMethodEnum = require("../../../electron/enum/TaxCalculationMethodEnum");


/**
 * 结算人材机分期计算
 */
class JieSuanRcjStageUtils  {


    /**
     * 计算清单分期工程量
     * @param qd 清单数据
     */
    qdStageCalculate(qd) {
        let stageType = qd.stageType;
        //分期方式 1：按分期比例输入
        if (stageType == JieSuanConstantUtil.STAGE_RATIO){
            let stageQuantitiesList = qd.stageQuantitiesList;
            //按分期比例
            stageQuantitiesList.forEach(k =>{
                k.stageQuantity = NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.divide100(k.stageRatio),qd.quantity));
            });

            let index = null;
            for (let i = stageQuantitiesList.length - 1; i >= 0; i--) {
                if (stageQuantitiesList[i].stageQuantity !== 0) {
                    index = i;
                    break;
                }
            }
            if (ObjectUtil.isNotEmpty(index)){
                let sum = 0;
                stageQuantitiesList.slice(0, index).forEach(function(obj) {
                    sum += Number(obj.stageQuantity);
                });
                //比例不足时做100补齐处理
                stageQuantitiesList[index].stageQuantity = NumberUtil.subtract(qd.quantity,sum);
            }
        }
    }


    /**
     * 项目是否分期
     */
    isStage(unit){

        if (ObjectUtil.isEmpty(unit.rcjStageSet)){
            return false;
        }
        return unit.rcjStageSet.isStage
    }

    /**
     * 项目总期数
     */
    periods(unit){
        if (ObjectUtil.isEmpty(unit.rcjStageSet)){
            return 0;
        }
        return unit.rcjStageSet.periods
    }

    /**
     * 项目分期方式
     */
    stageType(unit){
        return unit.rcjStageSet.stageType
    }

    /**
     * 人材机调整 根据人材机调整类型获取对应的调整法
     */
    rcjAdjustMethod(unit,kind,method,paramkind){
        let rcjType = [2, 4, 5, 6, 8, 9, 10];
        if (rcjType.includes(kind)){
            kind = 2;
        }
        if (paramkind == 8){
            kind = paramkind;
        }
        let rcjAdjustMethodList = this.getRcjAdjustMethodList(unit);
        if (ObjectUtil.isEmpty(rcjAdjustMethodList)){
            return null;
        }
        let rcjDifferenceInfos = rcjAdjustMethodList.find(k =>k.kind == kind && k.adjustMethod == method);
        if (ObjectUtil.isEmpty(rcjDifferenceInfos)){
            return null;
        }
        return rcjDifferenceInfos.frequencyList;
    }


    rcjAdjustMethodType(obj,kind){
        let rcjType = [2, 4, 5, 6, 8, 9, 10];
        if (rcjType.includes(kind)){
            kind = 2;
        }
        let rcjAdjustMethodList = this.getRcjAdjustMethodList(obj);
        if (ObjectUtil.isEmpty(rcjAdjustMethodList)){
            return null;
        }
        let rcjDifferenceInfos = rcjAdjustMethodList.find(k =>k.kind == kind);
        if (ObjectUtil.isEmpty(rcjDifferenceInfos)){
            return null;
        }
        return rcjDifferenceInfos.adjustMethod;
    }


    /**
     * 获取调整法集合
     */
    getRcjAdjustMethodList(unit){
        return unit.adjustMethodList
    }


    /**
     * 获取调整法集合根据材料类型
     */
    getRcjAdjustMethodByKind(unit,kind){
        let rcjAdjustMethodList = this.getRcjAdjustMethodList(unit);
        let rcjType = [2, 4, 5, 6, 8, 9, 10];
        if (rcjType.includes(kind)){
            kind = 2;
        }
        return rcjAdjustMethodList.find(k =>k.kind == kind);
    }



    getCostDeIdList(unitList) {
        //获取当前单位的费用定额ID
        let costDeIdList = [];
        let costDeList = [DePropertyTypeConstant.AWF_DE, DePropertyTypeConstant.ZJCS_DE, DePropertyTypeConstant.CG_DE, DePropertyTypeConstant.AZ_DE];

        unitList.forEach(unit =>{
            if (ObjectUtil.isNotEmpty(unit.itemBillProjects)) {
                let itemBillProjectsIds = unit.itemBillProjects.filter(k => costDeList.includes(k.isCostDe)).map(k => k.sequenceNbr);
                costDeIdList.push(...itemBillProjectsIds);
            }
            if (ObjectUtil.isNotEmpty(unit.measureProjectTables)) {
                let measureProjectTablesIds = unit.measureProjectTables.filter(k => costDeList.includes(k.isCostDe)).map(k => k.sequenceNbr);
                costDeIdList.push(...measureProjectTablesIds);
            }
        })
        return costDeIdList;
    }

    /**
     * 获取某一条人材机的调整法
     */
    aa(rcj){




        for (let i = 0; i < 4; i++) {
            let rcjDifferenceInfo = new RcjDifferenceInfo();
            rcjDifferenceInfo.rcjDifferenceType = i+1;//人材机调整类型
            rcjDifferenceInfo.jieSuanBasePrice = k.marketPrice;//结算基期价默认值
            let jieSuanDifferencePrice = new JieSuanDifferencePrice();
            jieSuanDifferencePrice.jieSuanPrice = k.marketPrice;//结算单价默认值
            jieSuanDifferencePrice.jieSuanPriceSource = k.marketSourcePrice;//结算单价来源
            rcjDifferenceInfo.jieSuanDifferencePriceList = [jieSuanDifferencePrice];//单价信息集合
            adjustmentMethodList.push(rcjDifferenceInfo);
        }



    }

    generateNaturalNumbers(arr) {
        const start = arr[0]; // 第一个元素
        const end = arr[arr.length - 1]; // 最后一个元素
        if (start < end) {
            // 递增
            return Array.from({ length: end - start + 1 }, (_, index) => start + index);
        } else if (start > end) {
            // 递减
            return Array.from({ length: start - end + 1 }, (_, index) => start - index);
        } else {
            // 相等
            return [start];
        }
    }

    getQdStageQuantity(qd,num){
        return  qd.stageQuantitiesList[num].stageQuantity;
    }
    getQdStageRatio(qd,num){
        return  qd.stageQuantitiesList[num].stageRatio;
    }


    /**
     * 分期分次人材机初始化
     * @param rcj
     * @param num
     */
    stageSetRcjInit(rcj,num){
        //基期价
        rcj.jieSuanBasePrice = rcj.jieSuanMarketPrice;
        //合同不含税基期价
        rcj.priceBaseJournal = rcj.jieSuanPriceBaseJournal;
        //合同含税基期价
        rcj.priceBaseJournalTax = rcj.jieSuanPriceBaseJournalTax;
        //基期价来源
        rcj.jieSuanBasePriceSource = rcj.sourcePrice;
        rcj.marketPrice = rcj.jieSuanMarketPrice;//结算单价默认值
        rcj.priceMarketTax = rcj.jieSuanPriceMarketTax;//第n期含税单价
        rcj.priceMarket = rcj.jieSuanPriceMarket;//第n期不含税单价

        rcj.jieSuanRcjDifferenceTypeList = this.jieSuanDifferencePriceListInit(rcj,num);

    }

    /**
     * 初始化人材基期价，结算单价
     */
    jieSuanDifferencePriceListInit(rcj,num){
        let adjustmentMethodList = new Array();
        for (let i = 0; i < num; i++) {
            let jieSuanDifferencePrice = new JieSuanDifferencePrice();
            jieSuanDifferencePrice.jieSuanBasePriceF0 = null;//基本价格指数F0
            jieSuanDifferencePrice.jieSuanCurrentPriceFt = null;//现行价格指数Ft
            jieSuanDifferencePrice.marketPrice = rcj.jieSuanMarketPrice;//结算单价默认值
            jieSuanDifferencePrice.jieSuanPriceSource = rcj.jieSuanPriceSource;//结算单价来源
            jieSuanDifferencePrice.priceMarketTax = rcj.jieSuanPriceMarketTax;//第n期含税单价
            jieSuanDifferencePrice.priceMarket = rcj.jieSuanPriceMarket;//第n期不含税单价
            jieSuanDifferencePrice.taxRemoval = rcj.jieSuanTaxRemoval;//结算除税系数
            jieSuanDifferencePrice.taxRate = rcj.jieSuantaxRate;//税率
            //rcjDifferenceInfo.jieSuanDifferencePriceList = [jieSuanDifferencePrice];//单价信息集合
            adjustmentMethodList.push(jieSuanDifferencePrice);
        }
        return adjustmentMethodList;
    }


    /**
     * 结算导入 数据人材机初始化
     * @param rcjList
     */
    rcjDataHandler(rcjList,flag,unit){

        let constructIs2022 = PricingFileFindUtils.is22Unit(unit);
        rcjList.forEach( k =>{
            //原始数据
            k.originalFlag = flag;
            // if (ObjectUtils.isEmpty(flag)){
            //     k.originalFlag = true;
            // }else {
            //     k.originalFlag = flag;
            // }
            k.deStandardReleaseYear = constructIs2022?"22":"12";
            //风险幅度设置
            k.riskAmplitudeRangeMin = -5;
            k.riskAmplitudeRangeMax = 5;
            //取费
            k.jieSuanFee = JieSuanFeeSetEnum.METHOD3.code;
            //结算除税系数
            k.jieSuanTaxRemoval = k.taxRemoval;
            //税率
            k.jieSuantaxRate = k.taxRate;

            if (!constructIs2022){
                let taxCalculationMethod = unit.projectTaxCalculation.taxCalculationMethod;
                //是否是简易计税
                let isSimple = taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code;
                // if (isSimple){
                //     k.priceMarketTax = k.marketPrice;
                //     k.priceBaseJournalTax = k.marketPrice;
                // }else {
                //     k.priceBaseJournal = k.marketPrice;
                //     k.priceBaseJournalTax = k.marketPrice;
                //     k.priceMarket = k.marketPrice;
                // }
                k.priceBaseJournal = k.marketPrice;
                k.priceBaseJournalTax = k.marketPrice;
                k.priceMarket = k.marketPrice;
                k.priceMarketTax = k.marketPrice;
            }
            //结算合计数量
            k.jieSuanTotalNumber = k.totalNumber;
            //合同不含税基期价
            k.jieSuanPriceBaseJournal = k.priceBaseJournal;
            //合同含税基期价
            k.jieSuanPriceBaseJournalTax = k.priceBaseJournalTax;
            //合同不含税市场价
            k.jieSuanPriceMarket = k.priceMarket;
            //合同含税市场价
            k.jieSuanPriceMarketTax = k.priceMarketTax;
            //结算合价
            k.jieSuanTotal = k.total;
            //结算备份市场价 /合同/确认单价
            k.jieSuanMarketPrice = k.marketPrice;
            //结算备份定额价
            k.jieSuanDePrice = k.dePrice;

            //市场价来源
            k.jieSuanPriceSource = k.sourcePrice;
            //基期价
            k.jieSuanBasePrice = k.marketPrice;
            //基期价来源
            k.jieSuanBasePriceSource = k.sourcePrice;

            //第n期除税系数
            k.jieSuanStagetaxRemovalList = [];
            //结算第n期单位价差
            k.jieSuanUnitPriceDifferencList = [];
            //变值权重B分期数
            k.jieSuanStageValuetWeightB = [];
            if(k.kind==5 || k.kind==4){
                //将合计数量赋值给主材初始值
                k.backQuantity=k.totalNumber;
            }


            if (ObjectUtils.isNotEmpty(k.ifProvisionalEstimate) && k.ifProvisionalEstimate ==1){
                k.isDifference = true;
            }

            //结算单位价差
            k.jieSuanPriceDifferenc = 0;
            //过滤出人材机中的人工，并且编码为10000001、10000002、10000003，即综合用工一类、综合用工二类、综合用工三类设置调差默认值
            let tempMaterialCode = k.materialCode.includes('#')?k.materialCode.split('#')[0]:k.materialCod0e;
            if (["10000001", "10000002", "10000003"].includes(tempMaterialCode) && k.kind === 1){
                k.isDifference = true;
            }
            //人材机四种调整法默认值设置
            k.jieSuanRcjDifferenceTypeList = this.jieSuanDifferencePriceListInit(k,0);

            //批注初始化
            //单位批注
            k.unitPostil= null;
            //人材机单位批注 批注展示状态 0展示,1不展示
            k.unitPostilState= null;
            //单项批注
            k.singlePostil= null;
            //人材机单项批注 批注展示状态 0展示,1不展示
            k.singlePostilState= null;
            //工程项目批注
            k.constructPostil= null;
            //人材机工程项目批注 批注展示状态 0展示,1不展示
            k.constructPostilState= null;


        });


    }
    /**
     * 初始化人材基期价，结算单价
     */
    jieSuanDifferencePriceListInit(rcj,num){
        let adjustmentMethodList = new Array();
        for (let i = 0; i < num; i++) {
            //let rcjDifferenceInfo = new RcjDifferenceInfo();
            //rcjDifferenceInfo.rcjDifferenceType = i+1;//人材机调整类型
            let jieSuanDifferencePrice = new JieSuanDifferencePrice();
            // jieSuanDifferencePrice.jieSuanBasePrice = rcj.marketPrice;//基期价默认值
            // jieSuanDifferencePrice.priceBaseJournal = rcj.priceBaseJournal;// '不含税基期价',
            // jieSuanDifferencePrice.priceBaseJournalTax = rcj.priceBaseJournalTax;// '含税基期价',
            // jieSuanDifferencePrice.jieSuanBasePriceSource = rcj.marketSourcePrice;//基期价来源
            //jieSuanDifferencePrice.basePriceFloatRate = null;//基期价浮动率(%)

            jieSuanDifferencePrice.jieSuanBasePriceF0 = null;//基本价格指数F0
            jieSuanDifferencePrice.jieSuanCurrentPriceFt = null;//现行价格指数Ft
            jieSuanDifferencePrice.marketPrice = rcj.jieSuanMarketPrice;//结算单价默认值
            jieSuanDifferencePrice.jieSuanPriceSource = rcj.jieSuanPriceSource;//结算单价来源
            jieSuanDifferencePrice.priceMarketTax = rcj.jieSuanPriceMarketTax;//第n期含税单价
            jieSuanDifferencePrice.priceMarket = rcj.jieSuanPriceMarket;//第n期不含税单价
            jieSuanDifferencePrice.taxRemoval = rcj.jieSuanTaxRemoval;//结算除税系数
            jieSuanDifferencePrice.taxRate = rcj.jieSuantaxRate;//税率
            //rcjDifferenceInfo.jieSuanDifferencePriceList = [jieSuanDifferencePrice];//单价信息集合
            adjustmentMethodList.push(jieSuanDifferencePrice);
        }
        return adjustmentMethodList;
    }

    //元素拼接
    indexJoint(levelType,rcj,kind,type){
        let tempcol = "";
        //人材机编码去#号
        let tempMaterialCode = rcj.materialCode.includes('#') ? rcj.materialCode.split('#')[0] : rcj.materialCode;
        //项目工程和单位同样
        if (levelType == 1 || levelType == 2) {
            //默认合同外
            tempcol = tempMaterialCode +
                (rcj.kind ?? "")+ (rcj.materialName ?? "")+ (rcj.specification ?? "")+ (rcj.unit ?? "")+ (rcj.dePrice ?? "")+
                (rcj.marketPrice ?? "")+ ((ObjectUtils.isEmpty(rcj.ifDonorMaterial) || rcj.ifDonorMaterial == 0)? 0:rcj.ifDonorMaterial)+ (rcj.jieSuanAdminRate ?? "")+ (rcj.isDifference ?? "")+
                (rcj.markSum ?? "")+(rcj.deStandardReleaseYear ?? "")
            if (type == 1) {
                //合同内
                tempcol = tempMaterialCode +
                    (rcj.kind ?? "")+ (rcj.materialName ?? "")+ (rcj.specification ?? "")+ (rcj.unit ?? "")+ (rcj.jieSuanDePrice  ?? "")+
                    (rcj.jieSuanMarketPrice  ?? "")+ ((ObjectUtils.isEmpty(rcj.ifDonorMaterial) || rcj.ifDonorMaterial == 0)? 0:rcj.ifDonorMaterial)+ (rcj.jieSuanAdminRate ?? "")+ (rcj.isDifference ?? "")+
                    (rcj.markSum ?? "")+(rcj.deStandardReleaseYear ?? "")+((ObjectUtils.isEmpty(rcj.ifProvisionalEstimate) || rcj.ifProvisionalEstimate == 0)? 0:rcj.ifProvisionalEstimate);

            }
            //二级分类
            if(ObjectUtils.isNotEmpty(kind) && kind != 0 &&  kind != 21){
                tempcol = tempMaterialCode +
                    (rcj.kind ?? "")+ (rcj.materialName ?? "")+ (rcj.specification ?? "")+ (rcj.unit ?? "")+
                    (rcj.jieSuanDePrice ?? "")+ (rcj.jieSuanMarketPrice ?? "")+(rcj.deStandardReleaseYear ?? "")
            }
        }
        //单位级别
        if (levelType == 3) {
            tempcol = tempMaterialCode +
                (rcj.kind ?? "")+ (rcj.materialName ?? "")+ (rcj.specification ?? "")+ (rcj.unit ?? "")+
                (rcj.jieSuanDePrice ?? "")
        }
        return tempcol;

    }


    initJieSuanFee(obj){
        obj.jieSuanFee = {
            1:3,//人工：记取安文费，税金
            2:3,//材料：记取安文费，税金
            3:3//机械：记取安文费，税金
        }
    }



}

module.exports = {
    JieSuanRcjStageUtils: new JieSuanRcjStageUtils()
};

