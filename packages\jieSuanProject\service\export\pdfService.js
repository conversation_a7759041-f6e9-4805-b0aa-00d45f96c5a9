const path = require('path');
const {Service} = require("../../../../core");
const util = require('util');
const exec = util.promisify(require('child_process').exec);
const UtilsPs = require('../../../../core/ps');
const {ExcelUtil} = require("../../../../electron/utils/ExcelUtil.js");
const { ObjectUtils } = require('../../../../electron/utils/ObjectUtils');
const { PricingFileFindUtils } = require('../../../../electron/utils/PricingFileFindUtils');
const TaxCalculationMethodEnum = require('../../../../electron/enum/TaxCalculationMethodEnum');
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const ProjectLevelConstant = require('../../../../electron/enum/ProjectLevelConstant');
const fs = require('fs');
class PdfService extends Service {
    constructor(ctx) {
        super(ctx);
    }

    //sheetName 传值的话表示为预览页的导出
    async excelToPdf(lanMuName,params,sheetName,startPage,totalPage) {

        const dialogOptions = {
            title: '保存文件',
            defaultPath: ObjectUtils.isNotEmpty(sheetName)?sheetName:params.headLine,
            filters: [{name: 'pdf', extensions: ['pdf']}]
        };
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        //弹出弹框确定路径以后 走下面
        if (result && !result.canceled) {
            let filePath = result;
            await this.exportPdf(lanMuName,params,filePath,startPage,totalPage);
            return true;
        }else {
            return false;
        }
    }


    async exportPdf(lanMuName,params,pdfPath,startPage,totalPage){

        let construct = PricingFileFindUtils.getProjectObjById(params.id);
        //计税方式
        let taxCalculationMethodPath = "";
        let taxCalculationMethod = construct.projectTaxCalculation.taxCalculationMethod;
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            taxCalculationMethodPath = "简易计税";
        }else {
            taxCalculationMethodPath = "一般计税";
        }
        let is22Str =  PricingFileFindUtils.is22De(params.id)?'22':'12';
        let project = await this.service.jieSuanProject.jieSuanExportQueryService.initWorkBook(ProjectLevelConstant.construct,is22Str,lanMuName,taxCalculationMethodPath,null);

        let fileDir = await this.service.jieSuanProject.jieSuanExportQueryService.getProjectRootPath()+"\\excelTemplate\\export\\"+params.headLine;
        let workBookList = [];
        let args = {};
        args['constructId'] = params.id;
        args["fileType"] = "pdf";
        await this.service.jieSuanProject.jieSuanExportQueryService.parseParams(params, project, null, null, fileDir, args,taxCalculationMethodPath,lanMuName,workBookList);

        //合成一个大excel文件  最后生成一个pdf文件
        //先对workBookList[0]._worksheets 按照 worksheets的顺序进行重排  worksheets的属性orderNo 始终是有序的
        for (let i = 0; i < workBookList[0].sheet.worksheets.length; i++) {
            //按照worksheets的元素排列  确定在_worksheets 中当前的索引  及id相同的对应索引 进行位置交换 使当前索引上的sheet是想要的对应id的sheet
            let indexCur = await this.getIndexIn_worksheets(i+1,workBookList[0].sheet);
            let indexId = await this.getIndexOfSameId(workBookList[0].sheet.worksheets[i].id,workBookList[0].sheet);
            [workBookList[0].sheet._worksheets[indexCur], workBookList[0].sheet._worksheets[indexId]] = [workBookList[0].sheet._worksheets[indexId],workBookList[0].sheet._worksheets[indexCur]];
        }
        for (let i = 1; i < workBookList.length; i++) {
            let bookElement = workBookList[i].sheet;
            if (ObjectUtils.isNotEmpty(bookElement)) {
                for (let j = 0; j < bookElement.worksheets.length; j++) {
                    let worksheet = bookElement.worksheets[j];
                    if (worksheet != null) {
                        workBookList[0].sheet._worksheets.push(worksheet);
                    }
                }
            }
        }
        //excel表格乱序 展示顺序是按照 worksheets数组的顺序来的 而不是  _worksheets
        //如果这里不重置id和orderNo 会导致sheet名称和实际内容对不上  因为会有重复的id和orderNo
        let orderNo = 0;
        for (let i = 0; i < workBookList[0].sheet._worksheets.length; i++) {
            let worksheetSam = workBookList[0].sheet._worksheets[i];
            if (worksheetSam != null) {
                worksheetSam.id = ++orderNo;
                worksheetSam.orderNo = orderNo;
            }
        }

        //生成excel
        let excelFilePath = UtilsPs.getExtraResourcesDir()+"\\excelTemplate\\export\\pdf.xlsx";
        await this.createDirectory(UtilsPs.getExtraResourcesDir()+"\\excelTemplate\\export");
        await workBookList[0].sheet.xlsx.writeFile(excelFilePath);
        //设置环境变量
        let javaCommand = UtilsPs.getExtraResourcesDir()+"\\jre\\bin\\java";
        let javaHomePath = UtilsPs.getExtraResourcesDir()+"\\jre";
        let jarPath = UtilsPs.getExtraResourcesDir()+"\\pdfUtil.jar";
        let parameters = "\""+excelFilePath+"\""
            +"   "+"\""+pdfPath+"\"";

        await this.runCommand(javaCommand+" -Dfile.encoding=UTF-8  -DJAVA_HOME="+javaHomePath+"  -jar "+jarPath+"  "+parameters);
        //删除原来生成的excel文件
        fs.unlink(UtilsPs.getExtraResourcesDir()+"\\excelTemplate\\export\\pdf.xlsx", (err) => {
            if (err) {
                console.error('删除文件时出错:', err);
                return;
            }
            console.log('文件删除成功！');
        });
    }

    async runCommand(command) {
        try {
            const { stdout, stderr } = await exec(command);
            console.log(`命令输出结果: ${stdout}`);
            console.error(`命令错误输出: ${stderr}`);
        } catch (error) {
            console.error(`执行命令时出错: ${error}`);
        }
    }

    async getIndexIn_worksheets(order,workbook) {
        let index = 0;
        for (let i = 0; i < workbook._worksheets.length; i++) {
            if (workbook._worksheets[i]!=null) {
                index++;
                if (index == order) {
                    return i;
                }
            }
        }
    }


    async getIndexOfSameId(idParam,workbook) {
        for (let i = 0; i < workbook._worksheets.length; i++) {
            if (workbook._worksheets[i]!=null && workbook._worksheets[i].id == idParam) {
                return i;
            }
        }
    }

    // 创建目录
    async createDirectory(directoryPath) {
        if (!fs.existsSync(directoryPath)) {
            fs.mkdirSync(directoryPath, { recursive: true });
            console.log('目录已创建');
        } else {
            console.log('目录已存在');
        }
    }


}
PdfService.toString = () => '[class PdfService]';
module.exports = PdfService;
