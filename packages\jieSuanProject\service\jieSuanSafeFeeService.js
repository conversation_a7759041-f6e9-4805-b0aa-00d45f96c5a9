const BranchProjectLevelConstant = require("../../../electron/enum/BranchProjectLevelConstant");
const ConstructionMeasureTypeConstant = require("../../../electron/enum/ConstructionMeasureTypeConstant");
const JieSuanFeeSetEnum = require("../enum/JieSuanFeeSetEnum");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {ArrayUtil} = require("../../../electron/utils/ArrayUtil");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const {SafeFee} = require("../../../electron/model/SafeFee");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {Service} = require('../../../core');
const { RcjHandleFactory } = require('../rcj_handle/RcjHandleFactory');

class JieSuanSafeFeeService extends Service{
    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 获取价差安文费
     *
     * @param args
     * @returns {Promise<*>}
     */
    async getJcSafeFee(args){
        let constructId = args.constructId;
        let singleId = args.singleId;
        let unitId = args.unitId;

        await this.countJcSafeFee(constructId, singleId, unitId);
        let unit =await PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        return unit.jcSafeFees;
    }


    /**
     *  计算价差安文费
     *
     * @param constructId
     * @param singleId
     * @param unitId
     */
    async countJcSafeFee(constructId, singleId, unitId) {
        let unit =await PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        // 获取单位工程下所有的定额
        let deList =await this.getDeList(constructId, singleId, unitId);
        let copyDeList = ConvertUtil.deepCopy(deList);
        // 定额按照取费专业分组
        let costMajorNameGroup = ArrayUtil.group(copyDeList,'costMajorName');

        let rcjListAll = await this.service.rcjProcess.getCountRcjList(constructId, singleId, unitId) === null ? new Array() : this.service.rcjProcess.getCountRcjList(constructId, singleId, unitId);
        let args ={
            constructId:constructId,
            singleId:singleId,
            unitId:unitId,
            rcjList:rcjListAll
        }
        //调用计算价差后的人材机
        let res = await this.service.jieSuanProject.jieSuanRcjStageService.jieSuanRcjListTongYiTiaoCha(args);
        let rcjArgs = {levelType: 3, kind: 0};
        let instance = RcjHandleFactory.getInstance(args.constructId, args.singleId, args.unitId, rcjArgs);
        res = instance.cupmute();

        // 遍历分组后的对象
        let group = new Array();
        for ( let [key, value] of costMajorNameGroup.entries()) {
            let item = {};
            item.costMajorName = key;
            // 该取费专业的所有人材机
            let rcjLists = [];
            for (let j = 0; j < value.length; j++) {
                let de = value[j];
                // 获取该定额的人材机
                let rcjList = res.filter(i => i.deId === de.sequenceNbr)
                let rcjListNew = rcjList.filter(rcj => rcj.jieSuanFee === JieSuanFeeSetEnum.METHOD3.code);
                rcjLists.push(...rcjListNew);
            }

            //定额下记取安文费的价差合计
            let priceDifferencSum_de = rcjLists.reduce((accumulator, constructProjectRcj) => {
                return NumberUtil.add(accumulator , constructProjectRcj.jieSuanPriceDifferencSum);
            }, 0)
            item.formula =priceDifferencSum_de;
            group.push(item);
        }
        let jcSafeFees = new Array();
        let feeFiles = unit.feeFiles;
        if(!ObjectUtils.isObject(group)){

            for (let i = 0; i < group.length; i++) {
                let item = group[i];
                // 当前定额取费专业
                let feeFile = feeFiles.find(obj => obj.feeFileName === item.costMajorName);
                let jcSafeFee = new SafeFee();
                jcSafeFee.sequenceNbr = Snowflake.nextId();
                jcSafeFee.unitId = unit.sequenceNbr;
                jcSafeFee.costMajorName = item.costMajorName;
                jcSafeFee.costFeeBase = ObjectUtils.isEmpty(item.formula)?item.formula:NumberUtil.numberScale2(item.formula) ;
                jcSafeFee.basicRate =NumberUtil.numberScale2(feeFile.anwenRateBase);
                jcSafeFee.addRate =feeFile.anwenRateAdd;
                jcSafeFee.priceDifferencFeeAmount =NumberUtil.numberScale2(NumberUtil.multiply(item.formula,feeFile.anwenRateBase/100)) ;
                jcSafeFees.push(jcSafeFee);
            }

        }

        unit.jcSafeFees = jcSafeFees;
    }


    /**
     * 获取单位工程下所有的定额
     *
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {(ItemBillProject|lineData)[]}
     */
    async getDeList(constructId, singleId, unitId) {
        let unit =await PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //分部分项
        let itemBillProjects = unit.itemBillProjects;
        //措施项目
        let measureProjectTables = unit.measureProjectTables;
        //分部分项定额集合
        let itemBiliDeList = itemBillProjects.filter(item => item.kind === BranchProjectLevelConstant.de);

        //单价措施标题主键
        let djcsBtSequenceNbr = measureProjectTables.filter(item => !ObjectUtils.isEmpty(item.constructionMeasureType) && item.constructionMeasureType === ConstructionMeasureTypeConstant.DJCS).map(item => item.sequenceNbr);
        //单价措施定额
        let djDeMeasureProjectTables = await PricingFileFindUtils.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.de, djcsBtSequenceNbr);

        //总价措施标题主键
        let zjcsBtSequenceNbr = measureProjectTables.filter(item => !ObjectUtils.isEmpty(item.constructionMeasureType) && item.constructionMeasureType === ConstructionMeasureTypeConstant.ZJCS).map(item => item.sequenceNbr);
        //总价措施定额
        let zjDeMeasureProjectTables = await PricingFileFindUtils.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.de, zjcsBtSequenceNbr);

        //获取所有的定额
        let deList = [...itemBiliDeList, ...djDeMeasureProjectTables, ...zjDeMeasureProjectTables];
        return deList;
    }

}
JieSuanSafeFeeService.toString = () => '[class JieSuanSafeFeeService]';
module.exports = JieSuanSafeFeeService;