[{"groupCode": 1, "jsonStr": "", "name": "基本信息", "dispNo": 1}, {"groupCode": 1, "jsonStr": "", "name": "项目编号", "dispNo": 2}, {"groupCode": 1, "jsonStr": "", "name": "工程名称", "dispNo": 3}, {"groupCode": 1, "jsonStr": "", "name": "工程标段", "dispNo": 4}, {"groupCode": 1, "jsonStr": "房屋建筑工程, 市政基础设施工程", "name": "工程类别", "dispNo": 5}, {"groupCode": 1, "jsonStr": "市区, 县城, 镇, 非市区、县城、镇", "name": "地区类别", "dispNo": 6}, {"groupCode": 1, "jsonStr": "", "name": "图纸编号", "dispNo": 7}, {"groupCode": 1, "jsonStr": "", "name": "工程规模", "dispNo": 8}, {"groupCode": 1, "jsonStr": "平方米,立方米,延长米", "name": "工程规模单位", "dispNo": 9}, {"groupCode": 1, "jsonStr": "", "name": "工程所在地", "dispNo": 10}, {"groupCode": 1, "jsonStr": "", "name": "建设单位", "dispNo": 11}, {"groupCode": 1, "jsonStr": "", "name": "建设单位负责人", "dispNo": 12}, {"groupCode": 1, "jsonStr": "", "name": "设计单位", "dispNo": 13}, {"groupCode": 1, "jsonStr": "", "name": "设计单位负责人", "dispNo": 14}, {"groupCode": 1, "jsonStr": "", "name": "监理单位", "dispNo": 15}, {"groupCode": 1, "jsonStr": "", "name": "施工单位", "dispNo": 16}, {"groupCode": 1, "jsonStr": "", "name": "工程地址", "dispNo": 17}, {"groupCode": 1, "jsonStr": "", "name": "质量标准", "dispNo": 18}, {"groupCode": 1, "jsonStr": "", "name": "开工日期", "dispNo": 19}, {"groupCode": 1, "jsonStr": "", "name": "竣工日期", "dispNo": 20}, {"groupCode": 1, "jsonStr": "住宅,办公,商业,道路,桥梁,园林绿化,燃气,给排水", "name": "工程用途", "dispNo": 21}, {"groupCode": 1, "jsonStr": "居住建筑,办公建筑,宾馆酒店,商业建筑,综合体建筑,卫生建筑,教育建筑,交通建筑,文化建筑,观演建筑,工业建筑,道路管道,隧道桥梁,体育建筑,配套设施,餐饮建筑,居民服务建筑,园林绿化", "name": "工程类型", "dispNo": 22}, {"groupCode": 1, "jsonStr": "体育场馆,游泳/跳水馆,操场,健身房", "name": "建筑分类", "dispNo": 23}, {"groupCode": 1, "jsonStr": "", "name": "项目投资方", "dispNo": 24}, {"groupCode": 1, "jsonStr": "估算价,概算价,招标控制价,投标价,中标价,合同价,结算价,其他", "name": "造价类别", "dispNo": 25}, {"groupCode": 1, "jsonStr": "砖混,砖木,框架,框架剪力墙,剪力墙,筒体,排架,钢结构,钢混,网架,其他", "name": "结构类型", "dispNo": 26}, {"groupCode": 1, "jsonStr": "", "name": "基底面积", "dispNo": 27}, {"groupCode": 1, "jsonStr": "", "name": "地上层数", "dispNo": 28}, {"groupCode": 1, "jsonStr": "", "name": "地下层数", "dispNo": 29}, {"groupCode": 1, "jsonStr": "", "name": "抗震设防", "dispNo": 30}, {"groupCode": 1, "jsonStr": "", "name": "防火等级", "dispNo": 31}, {"groupCode": 1, "jsonStr": "", "name": "建筑高度", "dispNo": 32}, {"groupCode": 1, "jsonStr": "", "name": "是否有人防", "dispNo": 33}, {"groupCode": 1, "jsonStr": "", "name": "装修标准", "dispNo": 34}, {"groupCode": 1, "jsonStr": "建筑面积(㎡),建筑长度(m),建筑体积(m³) ", "name": "计算口径", "dispNo": 35}, {"groupCode": 1, "jsonStr": "", "name": "地下面积", "dispNo": 36}, {"groupCode": 1, "jsonStr": "", "name": "地上面积", "dispNo": 37}, {"groupCode": 1, "jsonStr": "", "name": "工期(日历天)", "dispNo": 38}, {"groupCode": 1, "jsonStr": "", "name": "质量承诺", "dispNo": 39}, {"groupCode": 2, "jsonStr": "", "name": "招标信息", "dispNo": 40}, {"groupCode": 2, "jsonStr": "", "name": "编制单位法定代表人", "dispNo": 41}, {"groupCode": 2, "jsonStr": "", "name": "招标人(发包人)", "dispNo": 42}, {"groupCode": 2, "jsonStr": "", "name": "招标人(发包人)法人或其授权人", "dispNo": 43}, {"groupCode": 2, "jsonStr": "", "name": "工程造价咨询人", "dispNo": 44}, {"groupCode": 2, "jsonStr": "", "name": "工程造价咨询人法人或其授权人", "dispNo": 45}, {"groupCode": 2, "jsonStr": "", "name": "中介机构法定代表人", "dispNo": 46}, {"groupCode": 2, "jsonStr": "", "name": "招标代理", "dispNo": 47}, {"groupCode": 2, "jsonStr": "", "name": "造价工程师", "dispNo": 48}, {"groupCode": 2, "jsonStr": "", "name": "注册证号", "dispNo": 49}, {"groupCode": 2, "jsonStr": "", "name": "编制人", "dispNo": 50}, {"groupCode": 2, "jsonStr": "", "name": "编制时间", "dispNo": 51}, {"groupCode": 2, "jsonStr": "", "name": "核对人(复核人)", "dispNo": 52}, {"groupCode": 2, "jsonStr": "", "name": "核对(复核)时间", "dispNo": 53}, {"groupCode": 3, "jsonStr": "", "name": "投标信息", "dispNo": 54}, {"groupCode": 3, "jsonStr": "", "name": "项目经理", "dispNo": 55}, {"groupCode": 3, "jsonStr": "", "name": "投标保证金(万元)", "dispNo": 56}, {"groupCode": 3, "jsonStr": "", "name": "担保类型", "dispNo": 57}, {"groupCode": 3, "jsonStr": "", "name": "投标人(承包人)", "dispNo": 58}, {"groupCode": 3, "jsonStr": "", "name": "投标人(承包人)法人或其授权人", "dispNo": 59}, {"groupCode": 3, "jsonStr": "", "name": "投标单位造价工程师", "dispNo": 60}, {"groupCode": 3, "jsonStr": "", "name": "投标单位造价工程师资质证号", "dispNo": 61}, {"groupCode": 3, "jsonStr": "", "name": "编制人", "dispNo": 62}, {"groupCode": 3, "jsonStr": "", "name": "编制时间", "dispNo": 63}]