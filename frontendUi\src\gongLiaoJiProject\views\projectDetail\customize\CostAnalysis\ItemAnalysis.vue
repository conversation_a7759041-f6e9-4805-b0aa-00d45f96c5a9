<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:00:41
 * @LastEditors: sunchen
 * @LastEditTime: 2025-04-29 14:20:36
-->
<template>
  <vxe-table
    v-if="tableData.length > 0"
    border
    align="center"
    header-align="center"
    auto-resize
    :row-config="{ isHover: true, keyField: 'sequenceNbr', isCurrent: true }"
    :height="stableHeight"
    :tree-config="{
      rowField: 'dispNo',
      children: 'childrenList',
      expandAll: true,
      reserve: true,
    }"
    :data="tableData"
    ref="itemTable"
    keep-source
    @edit-closed="editClosedEvent"
    class="table-edit-common"
    @cell-click="
      cellData => {
        useCellClickEvent(cellData);
      }
    "
    :edit-config="{
      trigger: 'click',
      mode: 'cell',
      beforeEditMethod({ rowIndex }) {
        return true;
      },
    }"
    :cell-class-name="selectedClassName"
    :scroll-y="{ enabled: true, gt: 30 }"
    @current-change="rowChange"
  >
    <vxe-column type="seq"  :width="columnWidth(180)" title="序号" tree-node></vxe-column>
    <vxe-column
      field="projectName"
      :width="columnWidth(180)"
      title="名称"
      :visible="showColumns.some(a => a.unitkKey === 'projectName')"
    ></vxe-column>
    <vxe-column
      field="projectCost"
      :width="columnWidth(80)"
      title="金额（元）"
      :visible="showColumns.some(a => a.unitkKey === 'projectCost')"
    >
      <template #default="{ row }">
        {{ decimalFormat(row.projectCost, 'COST_ANALYSIS_JE_PATH') }}
      </template>
    </vxe-column>
    <vxe-colgroup title="实体项目">
      <vxe-column
        field="budgetzjf"
        :width="columnWidth(90)"
        title="实体项目合计"
        :visible="showColumns.some(a => a.unitkKey === 'budgetzjf')"
      ></vxe-column>
      <vxe-column
        field="ysrgf"
        :width="columnWidth(90)"
        title="实体人工费"
        :visible="showColumns.some(a => a.unitkKey === 'ysrgf')"
      ></vxe-column>
      <vxe-column
        field="ysclf"
        :width="columnWidth(90)"
        title="实体材料费"
        :visible="showColumns.some(a => a.unitkKey === 'ysclf')"
      ></vxe-column>
      <vxe-column
        field="ysjxf"
        :width="columnWidth(90)"
        title="实体机械费"
        :visible="showColumns.some(a => a.unitkKey === 'ysjxf')"
      ></vxe-column>
    </vxe-colgroup>
    <vxe-colgroup title="措施项目">
      <vxe-column
        field="csxmf"
        :width="columnWidth(90)"
        title="措施项目合计"
        :visible="showColumns.some(a => a.unitkKey === 'csxmf')"
      ></vxe-column>
      <vxe-column
        field="csrgf"
        :width="columnWidth(90)"
        title="措施人工费"
        :visible="showColumns.some(a => a.unitkKey === 'csrgf')"
      ></vxe-column>
      <vxe-column
        field="csclf"
        :width="columnWidth(90)"
        title="措施材料费"
        :visible="showColumns.some(a => a.unitkKey === 'csclf')"
      ></vxe-column>
      <vxe-column
        field="csjxf"
        :width="columnWidth(90)"
        title="措施机械费"
        :visible="showColumns.some(a => a.unitkKey === 'csjxf')"
      ></vxe-column>
    </vxe-colgroup>
    <vxe-column
      field="dlf"
      :width="columnWidth(90)"
      title="独立费"
      :visible="showColumns.some(a => a.unitkKey === 'dlf')"
    ></vxe-column>
    <vxe-colgroup title="按专业汇总">
      <vxe-column
        field="jzgcProjectCost"
        :width="columnWidth(90)"
        title="建筑工程"
        :visible="showColumns.some(a => a.unitkKey === 'jzgcProjectCost')"
      ></vxe-column>
      <vxe-column
        field="zsgcProjectCost"
        :width="columnWidth(90)"
        title="装饰装修工程"
        :visible="showColumns.some(a => a.unitkKey === 'zsgcProjectCost')"
      ></vxe-column>
      <vxe-column
        field="azgcProjectCost"
        :width="columnWidth(90)"
        title="安装工程"
        :visible="showColumns.some(a => a.unitkKey === 'azgcProjectCost')"
      ></vxe-column>
      <vxe-column
        field="szgcProjectCost"
        :width="columnWidth(90)"
        title="市政工程"
        :visible="showColumns.some(a => a.unitkKey === 'szgcProjectCost')"
      ></vxe-column>
      <vxe-column
        field="yllhProjectCost"
        :width="columnWidth(90)"
        title="园林绿化工程"
        :visible="showColumns.some(a => a.unitkKey === 'yllhProjectCost')"
      ></vxe-column>
    </vxe-colgroup>
    <vxe-column
      field="qyglf"
      :width="columnWidth(90)"
      title="管理费"
      :visible="showColumns.some(a => a.unitkKey === 'qyglf')"
    ></vxe-column>
    <vxe-column
      field="lr"
      :width="columnWidth(90)"
      title="利润"
      :visible="showColumns.some(a => a.unitkKey === 'lr')"
    ></vxe-column>
    <vxe-column
      field="aqwmsgf"
      :width="columnWidth(140)"
      title="安全生产、文明施工费"
      :visible="showColumns.some(a => a.unitkKey === 'aqwmsgf')"
    ></vxe-column>
    <vxe-column
      field="sj"
      :width="columnWidth(90)"
      title="税金"
      :visible="showColumns.some(a => a.unitkKey === 'sj')"
    ></vxe-column>
    <vxe-column
      field="costProportion"
      :width="columnWidth(110)"
      title="占造价比例（%）"
      :visible="showColumns.some(a => a.unitkKey === 'costProportion')"
    >
      <template #default="{ row }">
        {{
          decimalFormat(row.costProportion, 'COST_ANALYSIS_COSTPROPORTION_PATH')
        }}
      </template>
    </vxe-column>
    <vxe-column
      field="average"
      :width="columnWidth(130)"
      title="工程规模（㎡/m）"
      :edit-render="{ autofocus: '.vxe-input--inner' }"
      fixed="right"
      :visible="showColumns.some(a => a.unitkKey === 'average')"
    >
      <template #default="{ row }">
        {{ decimalFormat(row.average, 'COST_ANALYSIS_JZGM_PATH') }}
      </template>
      <template #edit="{ row }">
        <vxe-input
          :clearable="false"
          v-model="row.average"
          @blur="handleBlur(row)"
          :disabled="row.projectName === '合计' ? true : false"
        ></vxe-input>
      </template>
    </vxe-column>
    <vxe-column
      field="unitcost"
      :width="columnWidth(150)"
      title="单位造价（元/㎡或元/m）"
      fixed="right"
      :visible="showColumns.some(a => a.unitkKey === 'unitcost')"
    >
      <template #default="{ row }">
        {{ Number((+(row?.unitcost || 0)).toFixed(2)) }}
      </template>
    </vxe-column>
  </vxe-table>
</template>
<script setup>
import { watch, ref, onMounted, reactive } from 'vue';
import { message } from 'ant-design-vue';
import feePro from '@/gongLiaoJiProject/api/feePro';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@gongLiaoJi/hooks/useCellClick';
import { useDecimalPoint } from '@gongLiaoJi/hooks/useDecimalPoint.js';
const { decimalFormat } = useDecimalPoint();
import { columnWidth } from '@gongLiaoJi/hooks/useSystemConfig';
const { useCellClickEvent, selectedClassName } = useCellClick({
  rowKey: 'dispNo',
});
import { recordProjectData } from '@gongLiaoJi/hooks/recordProjectOperat.js';
let { updateGljSelrowId } = recordProjectData();
import { pureNumber } from '@/utils/index';
import infoMode from '@/plugins/infoMode.js';
import { cloneDeep } from 'lodash';
const props = defineProps({
  stableHeight: {
    type: Number,
    default: 400,
  },
  showColumns: {
    type: Array,
    default: () => [],
  },
});
const store = projectDetailStore();
const itemTable = ref();
let tableData = ref([]);
let tableDataCopy = ref([]);
let getData = [];
let averageRow = ref();
let average = ref();
let taxMode = ref(); //1-一般计税，0-简易计税
let flag = ref(false); //修改单项建筑面积是否联动修改单位建筑面积
let currentChange = ref(null);

// 递归函数，通过 id 获取节点
function getNodeById(tree, id) {
  for (const node of tree) {
    if (node.dispNo === id) {
      return node;
    }
    if (node.childrenList && node.childrenList.length > 0) {
      const result = getNodeById(node.childrenList, id);
      if (result) {
        return result;
      }
    }
  }
  return null;
}

const getTableData = () => {
  taxMode.value = store.taxMode;
  getData = [];
  let apiData = {
    type: store.currentTreeInfo?.type,
    unitId: '',
  };
  apiData.constructId = store.currentTreeGroupInfo?.constructId;
  if (store.currentTreeInfo?.type === 2) {
    apiData.singleId = store.currentTreeInfo?.id;
  }
  if (!apiData.type) {
    return;
  }
  console.log('getCostAnalysisData', apiData);
  // debugger;
  apiData.type &&
    feePro.getCostAnalysisData(apiData).then(res => {
      if (res.status === 200) {
        console.log('获取造价分析接口获取到的数据', res);
        if (store.currentTreeInfo?.type === 1) {
          getData = res.result.costAnalysisConstructVOList;
          // gczj(getData); //自己实验
        } else if (store.currentTreeInfo?.type === 2) {
          getData = res.result.costAnalysisSingleVOList;
        }
        tableData.value = getData;
        tableDataCopy.value = cloneDeep(getData);
        console.log('获取造价分析返回数据', apiData, tableData.value);
        setTimeout(async () => {
          const $table = itemTable.value;
          if ($table) {
            let gljCheckTab = store.gljCheckTab;
            let selRow = tableData.value[0];
            let upSelRow = gljCheckTab[
              store.currentTreeInfo.sequenceNbr
            ]?.tabList.find(a => a.tabName == '造价分析');
            if (upSelRow && upSelRow.selRowId !== '') {
              let tableList = JSON.parse(JSON.stringify(tableData.value));
              let obj = getNodeById(tableList, upSelRow.selRowId);
              selRow = obj;
            }
            currentChange.value = selRow;
            $table.setAllTreeExpand(true);
            $table.setCurrentRow(selRow);
            setTimeout(async () => {
              $table.scrollToRow(selRow);
            }, 100);
          }
        }, 1);
      }
    });
};
const rowChange = ({ row }) => {
  currentChange.value = row;
  itemTable.value.setCurrentRow(row);
  updateGljSelrowId(row.dispNo, '造价分析', 'selRowId');
};

const findItemBySequenceNbr = (arr, sequenceNbr) => {
  for (let item of arr) {
    if (item.sequenceNbr === sequenceNbr) {
      return item;
    }
    const children = item.children || item.childrenList;
    if (children) {
      const found = findItemBySequenceNbr(children, sequenceNbr);
      if (found) {
        return found;
      }
    }
  }
  return null; // 如果没有找到，返回 null
};

const handleBlur = row => {
  const average = row.average;
  // pureNumber(row.average, 2);
  if (Number.isNaN(Number(average)) || average < 0 || average === '') {
    message.error('请输入大于0的数值');
    const currentRowItem = findItemBySequenceNbr(
      tableDataCopy.value,
      row.sequenceNbr
    );
    row.average = currentRowItem.average;
  } else {
    row.average = average;
  }
  clear();
};

const gczj = data => {
  data.forEach(item => {
    item.gczj = '8888';
    item.childrenList ? gczj(item.childrenList) : '';
  });
};
const getDsposeData = data => {
  if (!data) {
    data = [];
  }
  // data &&
  //   data.map(item => {
  //     item.average =
  //       Number(item.average) >= 0 ? Number(item.average).toFixed(2) : '0.00';
  //     item.averageOld = item.average;
  //     item.unitcost =
  //       Number(item.unitcost) > 0 ? Number(item.unitcost).toFixed(2) : '0.00';
  //     if (item.projectName === '合计' && store.currentTreeInfo?.type === 2) {
  //       item.average = '/';
  //       item.unitcost = '/';
  //     }
  //     if (item.childrenList && item.childrenList.length === 0) return;
  //     item.childrenList && item.childrenList.length > 0
  //       ? getDsposeData(item.childrenList)
  //       : '';
  //   });
  tableData.value = data;
};
// const getEveryUnitCost = data => {
//   data.map(item => {
//     item.average =
//       Number(item.average) >= 0 ? Number(item.average).toFixed(2) : '0.00';
//     item.averageOld = item.average;
//     item.unitcost =
//       Number(item.average) > 0
//         ? Number(item.gczj / item.average).toFixed(2)
//         : '0.00';
//     if (item.projectName === '合计' && store.currentTreeInfo?.type === 2) {
//       item.average = '/';
//       item.unitcost = '/';
//     }
//     if (item.childrenList && item.childrenList.length === 0) return;
//     item.childrenList && item.childrenList.length > 0
//       ? getEveryUnitCost(item.childrenList)
//       : '';
//   });
//   tableData.value = data;
// };

const getTotal = list => {
  //合计数据行
  let totalLast = {
    average: 0,
    unitcost: 0,
    gczj: 0,
    fbfxhj: 0,
    fbfxrgf: 0,
    fbfxclf: 0,
    fbfxjxf: 0,
    fbfxzcf: 0,
    fbfxglf: 0,
    fbfxlr: 0,
    fbfxzgj: 0,
    djcsxhj: 0,
    djcsxrgf: 0,
    djcsxclf: 0,
    djcsxjxf: 0,
    djcsxzcf: 0,
    djcsxglf: 0,
    djcsxlr: 0,
    zjcsxhj: 0,
    zjcsxrgf: 0,
    zjcsxclf: 0,
    zjcsxjxf: 0,
    zjcsxzcf: 0,
    zjcsxglf: 0,
    zjcsxlr: 0,
    qtxmzlje: 0,
    qtxmzygczgj: 0,
    qtxmzcbfwf: 0,
    qtxmjrg: 0,
    csxhj: 0,
    qtxmhj: 0,
    gfee: 0,
    safeFee: 0,
    jxse: 0,
    xxse: 0,
    zzsynse: 0,
    fjse: 0,
    sj: 0,
    sbfsj: 0,
  };
  if (!list) return;
  if (store.currentTreeInfo?.type === 2) {
    for (let key in totalLast) {
      if (key !== 'average' && key !== 'unitcost') totalLast[key] = list[key];
      totalLast[key] = Number(totalLast[key]).toFixed(2);
    }
    totalLast.average = '/';
    totalLast.unitcost = '/';
  } else if (store.currentTreeInfo?.type === 1) {
    for (let key in totalLast) {
      list.forEach(item => {
        if (Object.prototype.hasOwnProperty.call(totalLast, key)) {
          totalLast[key] =
            item[key] && Number(item[key]) >= 0
              ? floatAdd(totalLast[key], item[key])
              : totalLast[key];
          totalLast[key] = Number(totalLast[key]).toFixed(2);
        }
      });
    }
  }
  totalLast.projectName = '合计';
  totalLast.dispNo = '';
  if (list instanceof Array) {
    return [...list, totalLast];
  } else {
    return [list, totalLast];
  }
};
const floatAdd = (arg1, arg2) => {
  var r1, r2, m;
  try {
    r1 = arg1.toString().split('.')[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split('.')[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  return (arg1 * m + arg2 * m) / m;
};
watch(
  () => store.tabSelectName,
  () => {
    if (
      store.tabSelectName === '造价分析' &&
      store.currentTreeInfo?.type !== 3
    ) {
      getTableData();
    }
  }
);
watch(
  () => store.currentTreeInfo,
  () => {
    if (
      store.tabSelectName === '造价分析' &&
      store.currentTreeInfo?.type !== 3
    ) {
      getTableData();
    }
  }
);

onMounted(() => {
  if (store.tabSelectName === '造价分析' && store.currentTreeInfo?.type !== 3) {
    getTableData();
  }
});
const clear = () => {
  //清除编辑状态
  const $table = itemTable.value;
  $table.clearEdit();
};
let parent = reactive(null);
const findParent = (list, tar = null) => {
  list.some(i => {
    if (i.sequenceNbr === averageRow.value.sequenceNbr) {
      parent = tar;
      return true;
    } else if (!parent && i.sequenceNbr !== averageRow.value.sequenceNbr) {
      i.childrenList ? findParent(i.childrenList, i) : '';
    }
    return true;
  });
};
const upDateAverage = () => {
  // getDsposeData(tableData.value);
  // debugger;
  const $table = itemTable.value;
  let apiData = {
    type: averageRow.value.levelType,
    average: average.value ? Number(average.value) : 0.0,
    // unitcost: averageRow.value.unitcost ? averageRow.value.unitcost : 0,
    sequenceNbr: averageRow.value.sequenceNbr,
    constructId: store.currentTreeGroupInfo?.constructId,
    flag: flag.value,
  };
  // if (store.currentTreeInfo?.type === 1) {
  // if (averageRow.value.levelType === 2) {
  //   apiData.singleId = averageRow.value.sequenceNbr;
  // } else if (averageRow.value.levelType === 3) {
  //   findParent(tableData.value);
  //   apiData.singleId = parent?.sequenceNbr;
  //   apiData.unitId = averageRow.value.sequenceNbr;
  //   console.log('修改成功造价分析11', apiData, parent);
  // }
  feePro.updateCostAnalysis(apiData).then(res => {
    if (res.status === 200) {
      console.log('修改成功造价分析', apiData, res, averageRow.value);
      averageRow.value.averageOld = averageRow.value.average;
      getTableData();
    }
  });
};
let expandedList = ref();
const getExpandList = list => {
  list.map(item => {
    expandedList.value.push(item);
    item.childrenList ? getExpandList(item.childrenList) : '';
  });
};
const editClosedEvent = ({ row, column }) => {
  const $table = itemTable.value;
  const field = column.field;
  averageRow.value = row;
  average.value = row[field];
  if (!row[field]) {
    row[field] = 0;
    average.value = 0;
  }
  if (Number(row.averageOld) === Number(row.average)) {
    row.average = Number(row.average).toFixed(2);
  }
  // 判断单元格值是否被修改
  if (
    $table.isUpdateByRow(row, field) &&
    Number(row.averageOld) !== Number(row.average)
  ) {
    flag.value = false;
    const hasChildren =
      row.childrenList && row.childrenList.length > 0 ? true : false;
    console.log(hasChildren);
    if (hasChildren) {
      infoMode.show({
        isSureModal: false,
        iconType: 'icon-querenshanchu',
        infoText: '是否联动修改其下所有层级的工程规模数据？',
        confirm: () => {
          flag.value = true;
          expandedList.value = [row];
          getExpandList(row.childrenList);
          itemTable.value.setTreeExpand(expandedList.value, true);
          // itemTable.value.setTreeExpand(row, true);
          upDateAverage();
          infoMode.hide();
        },
        close: () => {
          upDateAverage();
          infoMode.hide();
        },
      });
    } else {
      upDateAverage();
    }

    // averageRow.value.levelType === 2 && hasChildren
    //   ? Modal.confirm({
    //       title: '是否联动修改其下所有项目层级建筑面积数据？',
    //       zIndex: '99999',
    //       onOk() {
    //         flag.value = true;
    //         expandedList.value = [row];
    //         getExpandList(row.childrenList);
    //         itemTable.value.setTreeExpand(expandedList.value, true);
    //         // itemTable.value.setTreeExpand(row, true);
    //         upDateAverage();
    //       },
    //       onCancel() {
    //         upDateAverage();
    //       },
    //     })
    //   : upDateAverage();
  }
};
</script>
<style lang="scss" scoped>
.table-content {
  max-width: 95%;
  // width: calc(100% - 250px);
  max-height: calc(100% - 40px);
}
// ::v-deep(.vxe-table) {
//   min-height: 400px;
// }
</style>
