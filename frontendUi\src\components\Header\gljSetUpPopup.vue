<!--
 * @Descripttion: 
 * @Author: sunchen
 * @Date: 2023-11-15 16:56:26
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-06-20 10:17:08
-->
<template>
  <common-modal
    className="dialog-comm set-dialog"
    v-model:modelValue="visible"
    title="设置"
    @close="close"
  >
    <div class="content">
      <div class="list-label">
        <div
          :class="{ active: item.key === tabKey, label: true }"
          for=""
          v-for="item in tabList"
          :key="item.key"
          @click="tabChange(item.key)"
        >
          {{ item.name }}
        </div>
      </div>

      <div class="list-content" v-if="tabKey === 'file'">
        <div class="item">
          <span class="title">文件下载路径：</span>
          <div class="path">
            <button class="change" @click="wjSetSetUp('FILE_DOWNLOAD_PATH')">
              更改路径
            </button>
            <span>{{ FILE_DOWNLOAD_PATH }}</span>
          </div>
        </div>
        <div class="item">
          <span class="title">默认数据存储路径：</span>
          <div class="path">
            <button class="change" @click="wjSetSetUp('DEF_SAVE_PATH')">
              更改路径
            </button>
            <span>{{ DEF_SAVE_PATH }}</span>
          </div>
        </div>
      </div>
      <div class="list-content" v-if="tabKey === 'setting'">
        <a-checkbox
          @change="standardConversionShowFlagColl(1)"
          v-model:checked="standardConversionShowFlag"
          style="margin-top: 10px"
          >单位工程中标准换算弹窗展示</a-checkbox
        >
        <a-checkbox
          @change="standardConversionShowFlagColl(2)"
          v-model:checked="pricingMethodShowFlag"
          style="margin-top: 10px"
          >{{
            projectStore.taxMade == 1
              ? '按不含税市场价组价'
              : '按含税市场价组价'
          }}</a-checkbox
        >
        <a-checkbox
          @change="standardConversionShowFlagColl(3)"
          v-model:checked="showZmAssociatePop"
          style="margin-top: 10px"
          >展示定额关联子目弹窗</a-checkbox
        >
        <a-checkbox
          @change="standardConversionShowFlagColl(4)"
          v-model:checked="showUnpricedPop"
          style="margin-top: 10px"
          >展示未计价材料弹窗</a-checkbox
        >
      </div>
      <div class="list-content qiTa" v-if="tabKey === 'qiTa'">
        <div class="save-time">
          <div class="text">文件定时存储时间设置(分钟)：</div>
          <a-input
            v-model:value.number="saveTime"
            @focus="focusSaveTime"
            @blur="setSaveTime"
            @keyup="saveTimeKeyUp"
            type="number"
            class="my-input"
          />
        </div>
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { ref } from 'vue';
import csProject from '@gongLiaoJi/api/csProject';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import api from '@/api/projectDetail.js';
import { getSettingData } from '@gongLiaoJi/hooks/common.js';

const projectStore = projectDetailStore();
let FILE_DOWNLOAD_PATH = ref();
let DEF_SAVE_PATH = ref();
let projectAttrVisible = ref(false);
let rgfInMeasureAndRPriceInMechanicalVisible = ref(false);
const emits = defineEmits(['closePopup']);

let tabList = [
  {
    key: 'file',
    name: '文件管理',
  },
  {
    key: 'setting',
    name: '便捷性设置',
  },
  {
    key: 'qiTa',
    name: '其他设置',
  },
];
let tabKey = ref('file');

const tabChange = key => {
  tabKey.value = key;
};

let settingValue = ref('1');
let rgfInMeasureAndRPriceInMechanicalAction = ref('1');
let standardConversionShowFlag = ref(true); // 设置标准换算弹框
let pricingMethodShowFlag = ref(false); // 设置按含税/不含税市场价组价
const showZmAssociatePop = ref(false); // 设置是否显示定额关联子目弹窗
const showUnpricedPop = ref(false); // 设置是否显示未计价材料弹窗
localStorage.setItem('STANDARD_CONVERSION', standardConversionShowFlag.value);
localStorage.setItem('PRICING_METHOD', pricingMethodShowFlag.value);
const onChange = (e, v) => {
  if (e.target.nodeName.toLowerCase() === 'input') {
    if (v == 'settingValue') {
      settingValue.value = settingValue.value === '1' ? '' : '1';
    } else {
      rgfInMeasureAndRPriceInMechanicalAction.value =
        rgfInMeasureAndRPriceInMechanicalAction.value === '1' ? '' : '1';
    }
    setRelateMergeScheme();
  }
};
const saveTime = ref(5);
const oldSaveTime = ref(5);
const focusSaveTime = () => {
  oldSaveTime.value = saveTime.value;
};
const saveTimeKeyUp = e => {
  const value = e.target.value;
  const integerValue = value.replace(/[^\d]/g, '');
  e.target.value = integerValue;
  saveTime.value = integerValue ? Number(integerValue) : 0;
};
const setSaveTime = () => {
  if (!saveTime.value || saveTime.value < 1) {
    saveTime.value = oldSaveTime.value;
    return;
  }
  const params = [
    {
      paramsTag: 'SAVE_TIME',
      content: saveTime.value,
    },
  ];
  csProject.setSaveTime(params).then(res => {
    if (res.status === 200) {
      projectStore.SET_SAVE_TIME_GLJ(saveTime.value);
      message.success('设置成功');
    } else {
      message.error('设置失败');
    }
  });
};
const setRelateMergeScheme = () => {
  const params = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    projectAttrRelateMergeScheme: !!settingValue.value,
    rgfInMeasureAndRPriceInMechanicalAction:
      !!rgfInMeasureAndRPriceInMechanicalAction.value,
  };
  csProject.projectAttrRelateMergeSchemeSet(params).then(res => {
    if (res.result) {
      message.success('设置成功');
    } else {
      message.error('设置失败');
    }
  });
};
const visible = ref(true);
const close = () => {
  emits('closePopup');
};
// gsGetSetUp
//
const getData = () => {
  csProject
    .getSetUp({ constructId: projectStore.currentTreeGroupInfo?.constructId })
    .then(res => {
      if (res.result) {
        console.log('返回数据', res.result);
        res.result.forEach(e => {
          switch (e.paramsTag) {
            case 'DEF_SAVE_PATH':
              DEF_SAVE_PATH.value = e.content;
              break;
            case 'FILE_DOWNLOAD_PATH':
              FILE_DOWNLOAD_PATH.value = e.content;
              break;
            case 'PROJECTATTR':
              projectAttrVisible.value = true;
              settingValue.value = e.content ? '1' : '';
              break;
            case 'RGFINMEASUREANDRPRICEINMECHANICALACTION':
              rgfInMeasureAndRPriceInMechanicalVisible.value = true;
              rgfInMeasureAndRPriceInMechanicalAction.value = e.content
                ? '1'
                : '';
              break;
            case 'standardConversionShowFlag':
              standardConversionShowFlag.value = e.content;
              break;
            case 'SAVE_TIME':
              saveTime.value = e.content;
              break;
          }
        });
      }
    });
};

const wjSetSetUp = paramsTag => {
  csProject.wjPathSetSetUp({ paramsTag }).then(res => {
    console.log(
      '🚀 ~ file: setUpPopup.vue:69 ~ csProject.setSetUp ~ res:',
      res
    );
    if (res.status == 200) {
      switch (res.result) {
        case 0:
          console.log('点击了取消');
          break;
        case 1:
          getData();
          break;
        default:
          break;
      }
    }
  });
};
const setData = paramsTag => {
  csProject.setSetUp({ paramsTag }).then(res => {
    console.log(
      '🚀 ~ file: setUpPopup.vue:69 ~ csProject.setSetUp ~ res:',
      res
    );
    if (res.status == 200) {
      switch (res.result) {
        case 0:
          console.log('点击了取消');
          break;
        case 1:
          getData();
          break;
        default:
          break;
      }
    }
  });
};
const getGljData = () => {
  csProject
    .getGljSetUp({
      constructId: projectStore.currentTreeGroupInfo?.constructId,
    })
    .then(res => {
      if (res.result) {
        res.result.forEach((item, index) => {
          switch (index) {
            case 'STANDARD_CONVERSION':
              standardConversionShowFlag.value = item;
              break;
            case 'PRICING_METHOD':
              pricingMethodShowFlag.value = item;
              break;
            case 'RELATION_DE':
              showZmAssociatePop.value = item.relationDePop;
              break;
            case 'UNPRICED':
              showUnpricedPop.value = item.unPricedPop;
              break;
            default:
              break;
          }
        });
      }
    });
};

// 设置标准换算 1  按含税/不含税市场价组价 2
const standardConversionShowFlagColl = type => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    gsProjectSettingCode: '',
    setting: '',
  };
  switch (type) {
    case 1:
      apiData.gsProjectSettingCode = 'STANDARD_CONVERSION';
      apiData.setting = standardConversionShowFlag.value;
      break;
    case 2:
      apiData.gsProjectSettingCode = 'PRICING_METHOD';
      apiData.setting = pricingMethodShowFlag.value;
      break;
    case 3:
      apiData.gsProjectSettingCode = 'RELATION_DE';
      apiData.setting = { relationDePop: showZmAssociatePop.value };
      break;
    case 4:
      apiData.gsProjectSettingCode = 'UNPRICED';
      apiData.setting = { unPricedPop: showUnpricedPop.value };
      break;
    default:
      break;
  }
  console.log('设置参数', apiData);
  csProject.setSetUp(apiData).then(res => {
    if (res.status === 200) {
      message.success('设置成功');
      getSettingData();
      if (type == 1) {
        localStorage.setItem(
          'STANDARD_CONVERSION',
          standardConversionShowFlag.value
        );
      } else {
        localStorage.setItem('PRICING_METHOD', pricingMethodShowFlag.value);
        projectStore.SET_OPTION({ isScj: pricingMethodShowFlag.value });
        projectStore.pricingMethod = pricingMethodShowFlag.value ? 1 : 0;
      }
    } else {
      message.error('设置失败');
    }
  });
};
getGljData();
getData();
</script>

<style lang="scss">
.set-dialog {
  width: 60%;
  max-width: 600px;
  min-width: 200px;
  .vxe-modal--content {
    padding-bottom: 0 !important;
  }
  .content {
    display: flex;
    .list-label {
      min-height: 260px;
      padding-bottom: 20px;
      border-right: 1px solid rgba(224, 224, 224, 1);
      .label {
        font-size: 14px;
        font-weight: 400;
        color: #2a2a2a;
        opacity: 1;
        padding: 4px 20px 4px 0;
        margin-bottom: 10px;
        border-right: 1px solid transparent;
        white-space: nowrap;
        cursor: pointer;
        &.active {
          border-right-color: #287cfa;
          color: #287cfa;
        }
      }
    }
    .list-content {
      padding: 0 60px 40px 40px;
      display: flex;
      flex-wrap: wrap;
      .ant-checkbox-wrapper {
        width: 100%;
        & + .ant-checkbox-wrapper {
          margin-left: 0px;
        }
      }
      .item {
        margin-bottom: 23px;
        .title {
          display: block;
          font-size: 14px;
          font-weight: 400;
          color: #2a2a2a;
          margin-bottom: 20px;
        }
        .path {
          display: flex;
          align-items: center;
          .change {
            background: rgba(255, 255, 255, 0.39);
            border: 1px solid #bfbfbf;
            opacity: 1;
            border-radius: 3px;
            font-size: 14px;
            font-weight: 400;
            color: #2a2a2a;
            outline: none;
            padding: 6px 10px;
          }
          span {
            font-size: 14px;
            color: #898989;
            margin-left: 15px;
          }
        }
      }
    }
    .qiTa {
      align-items: flex-start;
      .save-time {
        display: flex;
        align-items: center;
        .text {
          width: 330px;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
