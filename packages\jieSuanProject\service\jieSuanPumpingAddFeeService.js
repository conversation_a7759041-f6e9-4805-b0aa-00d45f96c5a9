const PumpingAddFeeService = require("../../../electron/service/costCalculation/pumpingAddFeeService");
const JieSuanConstructCostMathService = require("./jieSuanConstructCostMathService");


const { ObjectUtil } = require('../../../common/ObjectUtil');
const { NumberUtil } = require('../../../common/NumberUtil');
const { PricingFileFindUtils } = require("../../../electron/utils/PricingFileFindUtils");
const { BaseDeBs2022 } = require('../../../electron/model/BaseDeBs2022');
const { BaseDeBs } = require('../../../electron/model/BaseDeBs');
const { BaseDeRcjBs2022 } = require('../../../electron/model/BaseDeRcjBs2022');
const { BaseDeRcjBs } = require('../../../electron/model//BaseDeRcjBs');
const StepItemCostLevelConstant = require('../../../electron/enum/StepItemCostLevelConstant');
const InsertStrategy = require('../../../electron/main_editor/insert/insertStrategy');
const { ObjectUtils } = require("../../../electron/utils/ObjectUtils");


class JieSuanPumpingAddFeeService extends PumpingAddFeeService {

    constructor(ctx) {
        super(ctx);
        this.jieSuanConstructCostMathService =
            ObjectUtils.isNotEmpty(this.service.jieSuanConstructCostMathService)?this.service.jieSuanConstructCostMathService :new JieSuanConstructCostMathService(ctx);
    }

    /**
     * 计算泵送增加费
     */
    async calculationPumpingAddFee(args) {
        const {
            constructId,
            singleId,
            unitId,
            pumpingType,          // 泵送方式：2 泵车  1 输送泵
            eavesHeight,          // 输送泵地上檐高类别选择的选项的sequenceNbr
            hntCalculationFlag,   // 预拌混凝土是否计算泵送增加费 1：预拌混凝土计算泵送增加费  2：预拌混凝土不计算泵送增加费
            xjhntCalculationFlag, // 现浇混凝土是否计算泵送增加费 1：现浇混凝土计算泵送增加费  2：现浇混凝土不计算泵送增加费
            dsCalculationFlag,    // 地上参与泵送费计算 1：参与  2：不参与
            dxCalculationFlag,    // 地上参与泵送费计算 1：参与  2：不参与
            baseDeList            // 泵送费基数定额列表
        } = args;
        const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        const is22Unit = PricingFileFindUtils.is22Unit(unit);
        const baseDeBs2022s = await this.app.appDataSource.manager.getRepository(is22Unit ? BaseDeBs2022 : BaseDeBs).find();
        // 删除历史的泵送费定额
        await this.clearPumpingAddFeeDe(unit, baseDeBs2022s);
        // 设置缓存
        unit.pumpingAddFeeCache = {
            pumpingType: pumpingType,
            eavesHeight: eavesHeight,
            hntCalculationFlag: hntCalculationFlag,
            xjhntCalculationFlag: xjhntCalculationFlag,
            dsCalculationFlag: dsCalculationFlag,
            dxCalculationFlag: dxCalculationFlag,
            baseDeList: baseDeList
        };
        if (ObjectUtil.isEmpty(baseDeList)) {
            return;
        }

        // 本次记取参与计算的基数人材机code
        let hntRcjCode = [];
        // 取出所有基数人材机的数据
        const baseDeRcjBs = await this.app.appDataSource.manager.getRepository(is22Unit ? BaseDeRcjBs2022 : BaseDeRcjBs).find();
        for (const deRcjBs of baseDeRcjBs) {
            if (deRcjBs.materialName.includes('预拌混凝土')) {
                if (hntCalculationFlag === 1) {
                    // 预拌混凝土参与计算
                    hntRcjCode.push(deRcjBs.materialCode);
                }
                continue;
            }
            if (deRcjBs.materialName.includes('现浇混凝土')) {
                if (xjhntCalculationFlag === 1) {
                    // 现浇混凝土参与计算
                    hntRcjCode.push(deRcjBs.materialCode);
                }
                continue;
            }
            hntRcjCode.push(deRcjBs.materialCode);
        }

        // 筛选出定额
        let baseDeArr = baseDeList.filter(item => item.kind === StepItemCostLevelConstant.de);

        // 泵送费定额记取的对应范围是【对应分部分项清单】  也就是说泵送费定额对应的基数定额只在所属分部分项清单下
        let deGroupByQdId = baseDeArr.reduce((map, de) => {
            if (!map.has(de.parentId)) {
                map.set(de.parentId, []);
            }
            map.get(de.parentId).push(de);
            return map;
        }, new Map());
        // 把定额根据清单id分组后  遍历每一个清单
        for (const qdId of deGroupByQdId.keys()) {
            const qdNode = unit.itemBillProjects.getNodeById(qdId);
            // 获取到这个清单下的基数定额
            let qdDeArr = deGroupByQdId.get(qdId);
            let deMap = new Map();
            // 根据地上地下分组
            for (const item of qdDeArr) {
                const upFloorArr = deMap.get(item.upFloor) || [];
                upFloorArr.push(item);
                deMap.set(item.upFloor, upFloorArr);
            }
            // 根据页面选择的檐高类别 以及当前清单下的基数定额所选择的地上地下 获取到泵送费费用定额的基础定额数据(就是每个对应分部分项清单下需要添加几个泵送费定额)
            let costDeArr = await this.getBaseCosetDeArr(pumpingType, baseDeBs2022s, is22Unit, eavesHeight, deMap);

            if (dsCalculationFlag === 2 && ObjectUtil.isNotEmpty(qdDeArr)) {
                // 【地上】不参与计算  所以排除选择地上的
                qdDeArr = qdDeArr.filter(item => item.upFloor !== 1);
            }
            if (dxCalculationFlag === 2 && ObjectUtil.isNotEmpty(qdDeArr)) {
                // 【地下】不参与计算  所以排除选择地下的
                qdDeArr = qdDeArr.filter(item => item.upFloor !== 2);
            }
            // dsBaseValue、dxBaseValue 为初始化的两个地上泵送费的工程量      就是这个清单下分地上、地下两组的基数定额的人材机合计数量之和
            let dsBaseValue = 0;
            let dxBaseValue = 0;
            if (ObjectUtil.isNotEmpty(qdDeArr)) {
                for (const de of qdDeArr) {
                    if (de.upFloor === 1) {
                        // 如果是地上  那么这个定额下的【基数人材机的合计数量累加值】就是地上对应泵送费定额的工程量
                        dsBaseValue = NumberUtil.add(dsBaseValue, this.sumRcjBaseValue(de, hntRcjCode, unit));
                    } else {
                        // 如果是地下  那么这个定额下的【基数人材机的合计数量累加值】就是地下对应泵送费定额的工程量
                        dxBaseValue = NumberUtil.add(dxBaseValue, this.sumRcjBaseValue(de, hntRcjCode, unit));
                    }
                }
            }

            let newDeArr = [];
            if (costDeArr.length === 1) {
                // 生成对应的泵送费定额
                const newCostDe = this.getCostDeData(costDeArr[0]);
                // 当泵送费定额只有一条的时候 直接使用dsBaseValue+dxBaseValue  原因如下：
                //    只有一条时，有四种情况：
                //      1. 泵送方式选择的【泵车】 不区分地上地下，所以值全部都在dxBaseValue，dsBaseValue为0，相加不会有影响
                //      2. 檐高选择的【40m】  不管是地上地下都算40m的，所以直接使用dsBaseValue+dxBaseValue
                //      3. 基数定额全是【地上】   那么dxBaseValue的值为0，相加不会有影响
                //      4. 基数定额全是【地下】   那么dsBaseValue的值为0，相加不会有影响
                newCostDe.quantityExpressionNbr = NumberUtil.add(dsBaseValue, dxBaseValue);
                if (pumpingType === 2) {
                    // 泵车
                    newCostDe.quantityExpression = 'BSHNTL_BC';
                } else {
                    // 输送泵
                    newCostDe.quantityExpression = 'BSHNTL_' + costDeArr[0].ykHigh;
                }
                // bsfBaseValue 表示泵送费定额的基础工程量值  用于后续工程量表达式计算
                newCostDe.bsfBaseValue = newCostDe.quantityExpressionNbr;
                newDeArr.push(newCostDe);
            } else {
                // 循环本次要生成的泵送费定额
                for (const costDe of costDeArr) {
                    const newCostDe = this.getCostDeData(costDe);
                    if (costDe.upFloor === 2) {
                        // 40m地下
                        newCostDe.quantityExpressionNbr = dxBaseValue;
                    } else {
                        // 地上
                        newCostDe.quantityExpressionNbr = dsBaseValue;
                    }
                    // bsfBaseValue 表示泵送费定额的基础工程量值  用于后续工程量表达式计算
                    newCostDe.bsfBaseValue = newCostDe.quantityExpressionNbr;
                    newCostDe.quantityExpression = 'BSHNTL_' + costDe.ykHigh;
                    newDeArr.push(newCostDe);
                }
            }
            if (ObjectUtil.isNotEmpty(newDeArr)) {
                let insertStrategy = new InsertStrategy({
                    constructId,
                    singleId: singleId,
                    unitId: unitId,
                    pageType: 'fbfx'
                });
                for (const newDe of newDeArr) {
                  let de = await insertStrategy.execute({
                        pointLine: qdNode,
                        newLine: newDe,
                        option: 'insert',
                        indexId: newDe.standardId
                    });
                    await this.jieSuanConstructCostMathService.initRcj(unit, de)
                }
            }

        }
    }
}
JieSuanPumpingAddFeeService.toString = () => '[class JieSuanPumpingAddFeeService]';
module.exports = JieSuanPumpingAddFeeService;