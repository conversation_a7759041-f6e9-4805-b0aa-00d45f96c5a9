<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:00:41
 * @LastEditors: sunchen
 * @LastEditTime: 2024-11-26 16:53:00
-->
<template>
  <div class="table-content" v-if="handlerColumns.length > 0">
    <vxe-table
      align="center"
      :loading="loading"
      :column-config="{ resizable: true }"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      :data="tableData"
      height="auto"
      ref="upTable"
      border="full"
      keep-source
      @edit-closed="editClosedEvent"
      :menu-config="menuConfig"
      @menu-click="contextMenuClickEvent"
      :cell-class-name="cellClassName"
      :header-cell-class-name="headerCellClassName"
      :tree-config="{
        transform: true,
        rowField: 'sequenceNbr',
        parentField: 'parentId',
        line: true,
        showIcon: true,
        expandAll: true,
        iconOpen: 'vxe-icon-square-minus',
        iconClose: 'vxe-icon-square-plus',
      }"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: ({ column, row }) => {
          if (
            ['name'].includes(column.field) &&
            ['建安工程费'].includes(row?.category)
          ) {
            return false;
          }

          return cellBeforeEditMethod();
        },
      }"
      class="table-edit-common"
      @current-change="currentChange"
      @cell-click="useCellClickEvent"
    >
      <!-- <vxe-column field="index" width="35" align="center">
        <template #default="{ row }">
          <div class="multiple-select" >
           {{ row.index + 1 }}
          </div>
        </template>
      </vxe-column> -->
      <vxe-column
        treeNode
        field="sortIndex"
        :width="columnWidth(120)"
        show-overflow
        title="序号"
        :visible="handlerColumns.find(a => a.field === 'sortIndex').visible"
      >
      </vxe-column>
      <vxe-column
        field="dispNo"
        :width="columnWidth(120)"
        title="编码"
        show-overflow
        :visible="handlerColumns.find(a => a.field === 'dispNo').visible"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.dispNo"
            type="text"
            @blur="update(row, dispNo)"
            @keyup="validateAndFormatCode(row)"
          ></vxe-input>
        </template>
      </vxe-column>
      <!-- <vxe-column
        field="dispNos"
        width="80"
        title="概算编码"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.dispNo"
            type="text"
            :disabled="['建安工程费'].includes(row?.category)"
            @blur="update(row,dispNo)"
            @keyup="row.dispNo = row.dispNo.replace(/[^\w.]/g, '')"
          ></vxe-input>
        </template>
      </vxe-column> -->
      <vxe-column
        field="code"
        :width="columnWidth(120)"
        title="费用代号"
        :visible="handlerColumns.find(a => a.field === 'code').visible"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.code"
            type="text"
            @keyup="validateAndFormatCode(row)"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="name"
        :width="columnWidth(220)"
        title="名称"
        :visible="handlerColumns.find(a => a.field === 'name').visible"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row }">
          <span>{{ row.name }}</span>
        </template>
        <template #edit="{ row }">
          <cell-textarea
            v-if="row.whetherTax !== 1"
            :clearable="false"
            v-model.trim="row.name"
            :disabled="['建安工程费'].includes(row?.category)"
            placeholder="请输入名称"
            :textHeight="row.height"
            @keyup="row.name = row.name.replace(/\-|\+|\*|\/|\.|\(|\)/g, '')"
          ></cell-textarea>
          <span v-else>{{ row.name }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="calculateFormula"
        :width="columnWidth(150)"
        title="取费基数"
        :visible="
          handlerColumns.find(a => a.field === 'calculateFormula').visible
        "
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row }">
          {{ row.calculateFormula }}
        </template>
        <template #edit="{ row }">
          <cell-textarea
            v-if="row.ifUpdateRate"
            :clearable="false"
            v-model.trim="row.calculateFormula"
            placeholder="请输入取费基数"
            :textHeight="row.height"
          ></cell-textarea>
          <!-- @keyup="
              row.calculateFormula = row.calculateFormula.replace(
                /[^\w\-\+\*\/]/g,
                ''
              )
            " -->
          <span v-else>{{ row.calculateFormula }}</span>
          <icon-font
            v-if="row.ifUpdateRate"
            type="icon-bianji"
            class="more-icon"
            @click="editCalc(row)"
          ></icon-font>
        </template>
      </vxe-column>
      <vxe-column
        field="instructions"
        :visible="handlerColumns.find(a => a.field === 'instructions').visible"
        :width="columnWidth(200)"
        title="取费基数说明"
      >
        <template #edit="{ row }">
          <cell-textarea
            v-if="row.ifUpdateRate"
            :clearable="false"
            v-model.trim="row.instructions"
            placeholder="请输入取费基数"
            :textHeight="row.height"
          ></cell-textarea>
          <span v-else>{{ row.instructions }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="rate"
        :width="columnWidth(120)"
        title="费率（%）"
        :visible="handlerColumns.find(a => a.field === 'rate').visible"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            v-if="row.ifUpdateRate"
            type="number"
            :clearable="false"
            v-model="row.rate"
          ></vxe-input>
          <span v-else>
            {{ row.rate }}
          </span>
        </template>
      </vxe-column>
      <vxe-colgroup title="概算造价">
        <vxe-column
          field="jzFee"
          :width="columnWidth(120)"
          :visible="handlerColumns.find(a => a.field === 'jzFee').visible"
          title="建筑工程费"
        >
        </vxe-column>
        <vxe-column
          field="azFee"
          :width="columnWidth(120)"
          :visible="handlerColumns.find(a => a.field === 'azFee').visible"
          title="安装工程费"
        >
        </vxe-column>
        <vxe-column
          field="sbgzFee"
          :width="columnWidth(120)"
          :visible="handlerColumns.find(a => a.field === 'sbgzFee').visible"
          title="设备购置费"
        >
        </vxe-column>
        <vxe-column
          field="qtFee"
          :width="columnWidth(120)"
          :visible="handlerColumns.find(a => a.field === 'qtFee').visible"
          title="其他费用"
        >
        </vxe-column>
        <vxe-column
          field="price"
          :width="columnWidth(120)"
          :visible="handlerColumns.find(a => a.field === 'price').visible"
          title="金额"
        >
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="技术经济指标">
        <vxe-column
          field="unit"
          :width="columnWidth(80)"
          :visible="handlerColumns.find(a => a.field === 'unit').visible"
          title="单位"
        >
          <template #edit="{ row }">
            <vxe-input
              v-if="row.category == '建安工程费'"
              :clearable="false"
              v-model.trim="row.unit"
              type="text"
            ></vxe-input>
            <span v-else>{{ row.unit }}</span>
          </template>
        </vxe-column>
        <vxe-column
          field="average"
          :width="columnWidth(100)"
          :visible="handlerColumns.find(a => a.field === 'average').visible"
          title="工程规模"
        >
          <template #edit="{ row }">
            <vxe-input
              v-if="row.category == '建安工程费'"
              :clearable="false"
              v-model.trim="row.average"
              type="text"
            ></vxe-input>
            <span v-else>
              {{ row.average }}
            </span>
          </template>
        </vxe-column>
        <vxe-column
          field="unitCost"
          :width="columnWidth(100)"
          :visible="handlerColumns.find(a => a.field === 'unitCost').visible"
          title="单方造价"
        >
        </vxe-column>
      </vxe-colgroup>
      <vxe-column
        field="proportion"
        :width="columnWidth(120)"
        :visible="handlerColumns.find(a => a.field === 'proportion').visible"
        title="占总投资比例(%)"
      >
      </vxe-column>
      <!-- <vxe-column field="price" width="120" title="金额"> </vxe-column> -->
      <vxe-column
        field="category"
        :width="columnWidth(180)"
        title="费用类别"
        :visible="handlerColumns.find(a => a.field === 'category').visible"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-select
            v-if="
              row.category != '工程费用' &&
              row.category != '建安工程费' &&
              row.category != '设备购置费' &&
              row.category != '建设其他费'
            "
            v-model="row.category"
            placeholder="请输入费用类别"
            @change="categoryChange(row)"
          >
            <vxe-option
              :value="i"
              :label="i"
              v-for="i of categoryOptions"
              :visible="
                ![
                  '建安工程费',
                  '工程费用',
                  '设备购置费',
                  '建设其他费',
                ].includes(i)
              "
            ></vxe-option>
          </vxe-select>
          <span v-else>
            {{ row.category }}
          </span>
        </template>
      </vxe-column>

      <vxe-column
        field="remark"
        title="备注"
        :width="columnWidth(100)"
        :visible="handlerColumns.find(a => a.field === 'remark').visible"
        :edit-render="{ autofocus: '.vxe-textarea--inner' }"
      >
        <template #edit="{ row }">
          <cell-textarea
            :clearable="false"
            v-model.trim="row.remark"
            placeholder="请输入备注"
            :textHeight="row.height"
          ></cell-textarea>
        </template>
      </vxe-column>
      <vxe-column
        field="whetherPrint"
        title="输出"
        :width="columnWidth(80)"
        :visible="handlerColumns.find(a => a.field === 'whetherPrint').visible"
        :cell-render="{}"
      >
        <template #default="{ row }">
          <vxe-checkbox
            v-model="row.whetherPrint"
            size="small"
            content=""
            :checked-value="1"
            :unchecked-value="0"
            @change="update(row, 'whetherPrint')"
          ></vxe-checkbox>
        </template>
      </vxe-column>

      <vxe-column
        field="projectCostBelongs"
        :width="columnWidth(180)"
        title="工程费用归属"
        :visible="
          handlerColumns.find(a => a.field === 'projectCostBelongs').visible
        "
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-select
            v-if="row?.type == 3"
            v-model="row.projectCostBelongs"
            placeholder="工程费用归属"
          >
            <vxe-option
              :value="i"
              :label="i"
              v-for="i of projectCostBelongsOptions"
            ></vxe-option>
          </vxe-select>
          <span v-else>
            {{ row.projectCostBelongs }}
          </span>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
  <common-modal
    className="dialog-comm noMask"
    title="取费基数编辑"
    width="1200"
    height="450"
    v-model:modelValue="comModel"
    @cancel="cancelData"
    @close="comModel = false"
    :mask="false"
    style="position: releative"
  >
    <content-down
      :isTextArea="isTextArea"
      :textValue="textValue"
      ref="comArea"
    ></content-down>
    <span class="btns">
      <a-button @click="cancelData()">取消</a-button>
      <a-button type="primary" @click="sureData()">确定</a-button>
    </span>
  </common-modal>
  <common-modal
    v-model:modelValue="codeNullVisible"
    className="dialog-comm"
    title="提示"
    width="400px"
  >
    <div style="margin-bottom: 20px">置空费用代号将取消引用，是否确定？</div>
    <div class="footer-btn-list">
      <a-button @click="codeNullCancel">取消</a-button>
      <a-button type="primary" @click="codeNullQuery">确定</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import {
  ref,
  onMounted,
  watch,
  reactive,
  nextTick,
  toRaw,
  inject,
  onActivated,
  getCurrentInstance,
} from 'vue';
import csProject from '@gongLiaoJi/api/csProject';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import { getUrl } from '@/utils/index';
import ContentDown from './ContentDown.vue';
import { Modal } from 'ant-design-vue';
import { useCellClick } from '@gongLiaoJi/hooks/useCellClick';
import { pureNumber, isNumericExpression } from '@/utils/index';
import infoMode from '@/plugins/infoMode';
import { insetBus } from '@gongLiaoJi/hooks/insetBus';
const { isMove } = inject('mainData');
const emits = defineEmits(['updateData']);
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const store = projectDetailStore();
const upTable = ref(null);
const codeNullVisible = ref(false);
const currentInfo = ref(null);
let tableData = ref([]);
let totalFee = ref([]);
let taxMode = ref(); //1-一般计税，2-简易计税
let isCurrent = ref(0);
let comModel = ref(false); //计算基数编写弹框
let isTextArea = ref(true);
let textValue = ref('');
// let updateRate = ref(false); //修改费率弹窗
let rowValue = ref('');
// let deleteModel = ref(false);
let deleteInfo = ref();
let loading = ref(false);
let oldValue = ref('');
const comArea = ref();
let selectData = ref(null); //批量选择的数据
import { columnWidth } from '@gongLiaoJi/hooks/useSystemConfig';
onMounted(() => {
  getTableData();
}),
  onActivated(() => {
    currentInfo.value = null;
    isCurrent.value = 0;
    getTableData();
    insetBus(bus, store.componentId, 'estimateExpense', data => {
      if (data.name === 'save-template') executeOperation('saveTemplate'); // 保存模板
      if (data.name === 'load-template') executeOperation('loadingTemplates'); // 载入模板
    });
  });

const props = defineProps({
  isCharu: {
    type: Boolean,
  },
  isCharuDel: {
    type: Boolean,
  },
  handlerColumns: {
    type: Array,
  },
});

const editCalc = row => {
  console.log('---------------------');
  comModel.value = true;
  textValue.value = row;
  oldValue.value = row.calculateFormula;
};
const categoryChange = row => {
  if (row.category === '无') {
    row.category = '';
  }
};

const cancelData = () => {
  comArea.value.value = oldValue.value;
  textValue.value.calculateFormula = oldValue.value;
  // textValue.value.price = oldValue.value;
  comModel.value = false;
};
const sureData = () => {
  //编辑计算基数弹框确认
  const value = comArea.value.value;
  // if (!value) {
  //   //不可以输入空
  //   message.warn(`输入不可为空`);
  //   return;
  // }
  // console.info(comArea.value.matchValue())
  // if (comArea.value.matchValue()&&!isValidExpression(value)) {
  //   message.warn(`表达式有误，请重新编辑！`);
  //   return;
  // } 
  if (comArea.value.matchValue()) {
    return message.warn(`数据引用不合法，请重新编辑！`);
  }
  textValue.value.calculateFormula = value;
  update(textValue.value, 'calculateFormula');
};
function isValidExpression(expression) {
  // 匹配四则运算表达式的正则表达式
  const regex = /^[\d\+\-\*\/\(\)\.]+$/;
  
  // 检查表达式是否匹配正则表达式
  if (!regex.test(expression)) {
    return false;
  }
  
  try {
    // 使用 eval() 函数计算表达式的值
    eval(expression);
    return true;
  } catch (e) {
    // 如果表达式有语法错误，eval() 函数会抛出异常
    return false;
  }
}

const getTableData = () => {
  loading.value = true;
  console.info(store.currentTreeInfo);
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
  };
  console.log('获取费用汇总数据', apiData);
  csProject.getEstimateSummaryList(apiData).then(res => {
    console.log('获取费用汇总数据返回结果', res);
    if (res.status === 200) {
      loading.value = false;
      handleTable(res.result);
    }
  });
};

const handleTable = data => {
  let list = data ? data : [];
  tableData.value = list.map((i, k) => {
    if (k === 0) i.type = 1;
    i.index = k;
    return i;
  });

  // 更新数据
  setMoveRowList(null, tableData.value, true);
  setTimeout(()=>{
    nextTick(() => {
      upTable.value?.setAllTreeExpand(true);
      if (currentInfo.value) {
        upTable.value.setCurrentRow(currentInfo.value);
      } else {
        isCurrent.value
          ? upTable.value.setCurrentRow(tableData.value[isCurrent.value])
          : upTable.value.setCurrentRow(tableData.value[0]);

        setMoveRowList(
          isCurrent.value ? tableData.value[isCurrent.value] : tableData.value[0],
          tableData.value,
          false
        );
      }
    });
  },500)
  
};

watch(
  () => [store.tabSelectName, store.currentTreeInfo],
  ([val, oldVal], [newY, oldy]) => {
    if (store.tabSelectName == '概算汇总' && store.currentTreeInfo.type == 1) {
      isCurrent.value = 0; //切换页面选中行默认选第一行
      getTableData();
    }
  }
);
watch(
  () => currentInfo.value,
  val => {
    if (val) {
      if ([1, 2].includes(val.type)) {
        isMove.value.isFirst = true;
        isMove.value.isLast = true;
      }
    }
  }
);
const getZeroCode = async () => {
  return new Promise((resolve, reject) => {
    const formdata = {
      constructId: store.currentTreeGroupInfo?.constructId,
    };
    let zeroCode = ['0'];
    csProject.getEstimateCodeList(formdata).then(res => {
      if (res.status === 200) {
        let datas=res.result ? res.result : []
        datas?.map((item, index) => (item.sortNo = index + 1));
        datas.map(item => {
          if (item.price === 0) {
            zeroCode.push(item.code);
          }
        });
        resolve({ zeroCode, newTableData: datas });
      }
    });
  });
};
const editClosedEvent = async ({ row, column }) => {
  const $table = upTable.value;
  const field = column.field;
  console.log('概算费用汇总修改', row);
  console.log(
    '概算费用汇总修改',
    row[field],
    field,
    isValidExpression(row[field])
  );
  let value = row[field];
  const reg = /[^\d\.]/g;
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  if (
    field !== 'remark' &&
    field !== 'code' &&
    field !== 'unit' &&
    field !== 'average' &&
    !row[field] &&
    field !== 'category' &&
    field !== 'calculateFormula'
  ) {
    //不可以输入空
    $table.revertData(row, field);
    message.warn(`输入不可为空`);
    return;
  }
  // 判断单元格值是否被修改
  if ((field === 'name' || field === 'remark') && value?.length > 50) {
    console.log('field', field, 'value', value);
    row[field] = value.slice(0, 50);
    message.warn(`输入字符应50个字符范围内`);
    // return;
  }
  if (field === 'name' && value?.length === 0) {
    $table.revertData(row, field);
    message.warn(`输入名称不可为空!`);
    return;
  }
  if (field === 'calculateFormula' && value?.length === 0) {
    message.warn(`计算基数不可为空`);
    $table.revertData(row, field);
    return;
  }
  // if (field === 'calculateFormula' && !isValidExpression(row[field])) {
  //   message.warn(`表达式有误，请重新编辑！`);
  //   $table.revertData(row, field);
  //   return;
  // }
  if (field === 'calculateFormula'){
    // 定义正则表达式，用于匹配字母（a-zA-Z）或汉字（[\u4e00-\u9fa5]）  
    // const regex = /[a-zA-Z\u4e00-\u9fa5]/; 
    let { zeroCode } = await getZeroCode();
    const regex = new RegExp(`/(${zeroCode.join('|')})`);
    const isValid = regex.test(row.calculateFormula);
    console.info(222222222222,isValid)
    if (isValid) {
      $table.revertData(row, field);
      return message.error('数据引用不合法，请重新编辑');
    }
  }
  if (field === 'rate' && value !== '' && reg.test(value)) {
    console.log('----------,不和規則');
    //不可以输入除数字和小数点之外的
    $table.revertData(row, field);
    return;
  }
  if (field === 'rate' && value === '') {
    row[field] = '0';
  } else if ((field === 'rate' && Number(value) > 1000) || Number(value) < 0) {
    message.warn(`费率可输入数值范围：0-1000`);
    $table.revertData(row, field);
    return;
  }
  if (field === 'rate') {
    if (
      (taxMode.value === 1 &&
        (row.type === '附加税费' || row.type === '销项税额')) ||
      (taxMode.value === 0 && row.type === '税金')
    ) {
      if ($table.isUpdateByRow(row, field)) {
        // rowValue.value = { ...row };
        Modal.confirm({
          title: '是否确认修改税率？',
          content: '修改税率将会同步关联取费表中计税设置的费率，是否确认修改?',
          okText: '确定',
          cancelText: '取消',
          onOk() {
            update(row, field);
          },
          onCancel() {},
        });

        return;
      }
    }
  }
  if (field === 'code' && row.code === '') {
    codeNullVisible.value = true;
    currentInfo.value = row;
    return;
  }
  // 判断单元格值是否被修改
  if ($table.isUpdateByRow(row, field)) {
    update(row, field);
  }
};
// 清空费用代号取消
const codeNullCancel = () => {
  const $table = upTable.value;
  codeNullVisible.value = false;
  $table.revertData(currentInfo.value, 'code');
};
// 清空费用代号确认
const codeNullQuery = () => {
  codeNullVisible.value = false;
  update(currentInfo.value, 'code');
};
const update = (row, field) => {
  let { _X_ROW_CHILD, children, permission, ...otherData } = row;
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    estimateSummary: { ...toRaw(otherData), permission: toRaw(permission) },
  };
  delete apiData.estimateSummary.price
  console.log('费用汇总修改', apiData);
  csProject.saveEstimateSummary(apiData).then(res => {
    console.log('费用汇总修改结果', res);
    if (res.status !== 500) {
      message.success('修改成功！');
      tableData.value.map((item, index) => {
        if (item.sequenceNbr === row.sequenceNbr) {
          isCurrent.value = index;
        }
      });
      getTableData();
      comModel.value = false;
    } else if (res.status === 500) {
      message.error(res.message);
      if(field == 'calculateFormula'){
        // 费用汇总判断, 为了解决点击取消，vxetable走了取消编辑，掉了接口，弹出了错误提示，
        getTableData();
        return
      }
      const $table = upTable.value;
      $table.revertData(row, field);

      
    }
  });
};

const menuConfig = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          name: '新增行',
          code: 'add',
          visible: false,
          identification: 0,
        },

        {
          code: 'addChild',
          name: '新增下级',
          visible: false,
          identification: 1,
        },
        {
          code: 'delete',
          name: '删除',
          visible: false,
          identification: 2,
        },
        {
          code: 'saveTemplate',
          name: '保存模版',
          visible: true,
          disabled: false,
          identification: 4,
        },
        {
          code: 'loadingTemplates',
          name: '载入模版',
          visible: true,
          disabled: false,
          identification: 5,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    if (!row || !row?.permission) return;
    upTable.value.setCurrentRow(row);
    console.log('🚀 ~ visibleMethod ~ row:', row.permission);
    menuConfig.body.options[0][0].visible = row?.permission.includes(0);
    menuConfig.body.options[0][1].visible = row?.permission.includes(1);
    menuConfig.body.options[0][2].visible = row?.permission.includes(2);
    for (const item of options[0]) {
      if (!row.permission.includes(item.identification)) {
        item.disabled = true;
      } else {
        item.disabled = false;
      }
    }
    return true;
  },
});
const getCurrentIndex = item => {
  if (item) {
    tableData.value.map((n, index) => {
      if (n.sequenceNbr === item.sequenceNbr) {
        isCurrent.value = index;
      }
    });
  } else {
    isCurrent.value = 0;
  }
};
const operate = (type, row) => {
  let isCurrentRow = upTable.value.getCurrentRecord();
  let lineNumber = 0;
  tableData.value &&
    tableData.value.map((item, index) => {
      if (item.sequenceNbr == isCurrentRow.sequenceNbr) {
        lineNumber = index + 1;
      }
    });
  if (['addChild', 'add'].includes(type)) {
    let addData = {
      constructId: store.currentTreeGroupInfo?.constructId,
      lineNumber,
      estimateSummary: { ...row },
    };
    console.log('费用汇总新增', addData);
    csProject.addEstimateSummary(addData).then(res => {
      console.log('🚀 ~ csProject.addEstimateSummary ~ res:', res);
      if (res.status === 200) {
        message.success('插入成功');
        handleTable(res.result);
      }
    });
  } else if (type === 'delete') {
    let selRow = upTable.value?.getCurrentRecord();
    let deleteData = {
      sequenceNbr: selRow?.sequenceNbr,
      constructId: store.currentTreeGroupInfo?.constructId,
    };
    console.log('费用汇总删除', deleteData);

    csProject.deleteEstimateSummary(deleteData).then(res => {
      console.log('res.status === 200', res.result);

      if (res.result && res.result.status === 500) {
        message.error(res.result.message);
        return;
      } else {
        message.success('删除成功');
        handleTable(res.result);
      }
    });
  }
};
const contextMenuClickEvent = ({ menu, row }) => {
  menu.code === 'delete' ? getCurrentIndex() : getCurrentIndex(row);
  let addData = {
    sequenceNbr: '',
    parentId: row?.sequenceNbr,
  };

  // delete addData.children;
  // delete addData._X_ROW_KEY;
  switch (menu.code) {
    case 'saveTemplate':
      // 保存模板
      executeOperation('saveTemplate');

      break;
    case 'loadingTemplates':
      // 插入子级行
      executeOperation('loadingTemplates');
      break;
    case 'addChild':
      // 插入子级行
      operate('addChild', addData);
      break;
    case 'delete':
      // 删除
      deleteItem(row);
      break;
    case 'add':
      // 插入
      addData = {
        parentId: row?.parentId,
      };
      operate('add', addData);
      break;
  }
};

const executeOperation = type => {
  let apiData = {
    projectId: store.currentTreeGroupInfo?.constructId,
  };
  let url =
    type === 'saveTemplate' ? 'exportEstimateSummary' : 'importEstimateSummary';
  csProject[url](apiData)
    .then(res => {
      if (res.status !== 200) {
        return message.error(res.message);
      }
      message.success(
        type === 'saveTemplate' ? '保存模板成功！' : '载入模版成功！'
      );
      if (type !== 'saveTemplate') {
        emits('updateData');
        getTableData();
      }
    })
    .catch(err => {
      console.log(err, 'err');
    });
};

const deleteItem = item => {
  if (item.whetherTax === 1) {
    message.warning('当前行不可删除');
    return;
  }
  deleteInfo.value = { ...item };
  infoMode.show({
    isDelete: true,
    iconType: 'icon-querenshanchu',
    infoText: '是否确认删除？',
    descText: item.adopted
      ? '删除操作将导致费用代号取消引用，是否确定？'
      : '是否删除当前已选中数据',
    confirm: () => {
      operate('delete', deleteInfo.value);
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};

const validateAndFormatCode = row => {
  const code = row.code.trim();
  const number = /^\d+$/;
  if (number.test(code)) {
    // 如果是纯数字
    row.code = '';
    return;
  }

  const codePattern = /^[a-zA-Z\u4e00-\u9fa5_][a-zA-Z0-9\u4e00-\u9fa5_]*$/;

  if (!codePattern.test(code)) {
    row.code = code.slice(0, -1);
  }
  if (row.code.match(/_/g) && row.code.match(/_/g).length > 3) {
    row.code = code.slice(0, -1);
  }
};

const headerCellClassName = ({ column }) => {
  if (column.field === 'index') {
    return 'index-bg';
  }
  return null;
};
const cellClassName = ({ $columnIndex, row, column }) => {
  const selectName = selectedClassName({ $columnIndex, row, column });
  if (column.field === 'index') {
    return 'index-bg ' + selectName;
  }
  return selectName;
};

const categoryOptions = [
  '无',
  '建安工程费',
  '工程费用',
  '设备购置费',
  '建设其他费',
  '普通费用',
  '总费用',
  '三类费用',
  '建设期贷款利息',
];

const projectCostBelongsOptions = ['建筑工程', '安装工程'];

const currentChange = ({ row, $rowIndex }) => {
  currentInfo.value = row;
  if(row.type==1||row.type==2||row.type==3){
    return
  }
  setMoveRowList(row, tableData.value);
};

/**
 *
 * @param {*} row
 * @param {*} list
 * @param {*} resetList  只更新列表
 */
const setMoveRowList = (row, list, resetList = false) => {
  if (resetList) {
    store.moveRow.tableData = list;
    return;
  }
  store.moveRow = {
    tableData: toRaw(list),
    isTree: false,
    useRowList: [
      {
        ...row,
      },
    ],
  };
  setTimeout(() => {
    if (currentInfo.value?.type === 0 || !currentInfo.value) {
      isMove.value.isLast = true;
    }
  }, 10);
};

defineExpose({
  getTableData,
});
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  // height: 40px;
  padding: 0 10px;
}

.table-content {
  width: 100%;
  // min-height: 450px !important;
  height: calc(100%);
  background: white;
}

.table-content :deep(.vxe-table) {
  .title-bold {
    font-weight: bold;
  }

  .color-red {
    color: #de3f3f;
  }
}

::v-deep(.ant-empty) {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(25%, -25%);
}

.table-content {
  height: calc(100%);
}

.table-content {
  // height: calc(65%);
  height: 100%;
  //user-select: none;

  ::v-deep(.vxe-table .row-unit) {
    background: #f0ecf2;
  }

  ::v-deep(.vxe-table .row-sub) {
    background: #f9f7fa;
  }

  ::v-deep(.vxe-table .row-qd) {
    background: #e9eefa;
  }

  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }

  ::v-deep(.vxe-table .code-color) {
    color: #a73d3d;
  }

  ::v-deep(.vxe-table .index-bg) {
    background-color: #ffffff;
  }

  ::v-deep(
      .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column
    ) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }

  ::v-deep(.vxe-table--render-default.is--tree-line .vxe-header--column) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
}

.table-content ::v-deep(.vxe-table--render-default .vxe-tree--node-btn) {
  color: #87b2f2 !important;
}

.table-content :deep(.vxe-table) {
  .vxe-tree--line {
    /* 修改连接线的颜色 */
    border-left: 1px solid #87b2f2;
    border-bottom: 1px solid #87b2f2;
  }

  .title-bold {
    font-weight: bold;
  }

  .color-red {
    color: #de3f3f;
  }

  .row-lock-color {
    background-color: #bfbfbf;
  }

  .vxe-cell .vxe-cell--label {
    // ::selection {
    user-select: none;
    // }
  }
}

.btns {
  position: absolute;
  width: 200px;
  bottom: 10px;
  right: 40%;
  display: flex;
  justify-content: space-around;
}
</style>
